###
 # @Author: 米亚流年 <EMAIL>
 # @Date: 2024-03-14 10:58:56
 # @LastEditors: liu.yongli
 # @LastEditTime: 2025-04-17 14:51:13
 # @FilePath: /remote-runtime/.env.production
 # @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
###
# 开发环境配置
ENV='production'

# 开发环境打包路径
VUE_APP_BASE='/industry-11153'
VUE_ROUTER_BASE = 'industry-11153'

VUE_APP_REQ_PREFIX_HIGHWAY='biz-service'
VUE_APP_MESSAGE_SDK_DEBUG=false
VUE_APP_REQ_PREFIX_BIZ='railway-biz-service'
VUE_APP_REQ_PREFIX_PLAT='railway-plat-service'
VUE_APP_REQ_PREFIX_DICT='railway-dict-service'
VUE_APP_REQ_PREFIX_SYS=''
VUE_APP_REQ_SDK=true

## 探针的data数据 开发（准生产）与生产环境的data数据不同 BonreeAgent
VUE_APP_REQ_BONREE_AGENT='%7B%22reqHeaderTraceKey%22%3A%5B%22tracestate%22%2C%22traceparent%22%5D%2C%22uploadAddrHttps%22%3A%22https%3A%2F%2Fopmd-apm.chinatowercom.cn%3A58897%2FRUM%2Fupload%22%2C%22mc%22%3A%5B%7B%22n%22%3A%22network%22%2C%22cs%22%3A%7B%22fc%22%3A0%7D%7D%5D%2C%22appId%22%3A%22b9a96fff30424e9594c3e789b1b79588%22%2C%22uploadAddrHttp%22%3A%22http%3A%2F%2Fopmd-apm.chinatowercom.cn%3A58897%2FRUM%2Fupload%22%2C%22respHeaderTraceKey%22%3A%5B%22traceresponse%22%2C%22x-br-response%22%5D%2C%22brss%22%3Afalse%7D'

# 全景展示大屏默认的几个组件图标
VUE_APP_DEVICE_STAT_CAMERA_ICON='https://video-platform.obs.cn-north-4.myhuaweicloud.com/slyy2/prd/1.0/bb08f84d9ddf54459ea7b705612bafa1/dc8318b68c285babc3f6b00029c115ab/f4a02c86b7f64ef997ec6c32c186dc67.svg?AccessKeyId=CRFYWADDOJHXLKROOIXX&Expires=1723804884&Signature=wUG7fXJ9RbCUXaH1%2BKiCRjvDhHk%3D'
VUE_APP_DEVICE_STAT_HORN_ICON='https://video-platform.obs.cn-north-4.myhuaweicloud.com/slyy2/prd/1.0/bb08f84d9ddf54459ea7b705612bafa1/dc8318b68c285babc3f6b00029c115ab/01df65757cc446e78561556967c911a7.svg?AccessKeyId=CRFYWADDOJHXLKROOIXX&Expires=1723804884&Signature=f46XQLCC7YoKLHXQf08oJunjEJE%3D'
VUE_APP_FACILITY_STAT_BRIDGE_ICON='https://video-platform.obs.cn-north-4.myhuaweicloud.com/slyy2/prd/1.0/bb08f84d9ddf54459ea7b705612bafa1/dc8318b68c285babc3f6b00029c115ab/efc104283f3243fabe399366b252a9b3.svg?AccessKeyId=CRFYWADDOJHXLKROOIXX&Expires=1723804786&Signature=Rjp7B1CwlDSkFYHQwo550NcG2C8%3D'
VUE_APP_FACILITY_STAT_TUNNEL_ICON='https://video-platform.obs.cn-north-4.myhuaweicloud.com/slyy2/prd/1.0/bb08f84d9ddf54459ea7b705612bafa1/dc8318b68c285babc3f6b00029c115ab/f26c5dd9a2d545a3b32166dc821aa525.svg?AccessKeyId=CRFYWADDOJHXLKROOIXX&Expires=1723804786&Signature=j3/YnB5fh%2BXFWvjW2eMTUjt4sNU%3D'

# 火车线路图层
# VUE_APP_RAILWAY_LINE_URL= 'http://10.43.82.110:9239/geoserver/railway_space/wms?service=WMS&version=1.1.0&request=GetMap&layers=railway_space%3Arailway-rail-single-dark&bbox=75.96964263916016%2C1.429714322090149%2C134.51109313964844%2C53.00389862060547&width=768&height=676&srs=EPSG%3A4326&styles=&format=application/openlayers'

# 过车抓拍的ws监听协议
VUE_APP_CAPTURE_WS_PREFIX='wss'

# 原traffic-web-component 配置

# 开发环境输出目录
VUE_APP_OUTPUTDIR = 'dist'

# 开发环境后端接口前缀
VUE_APP_BASE_API = '/api'
VUE_APP_PLAT_API = '/plat'

# 后端访问路径
VUE_APP_SERVICE_URL = '/biz-service'
VUE_APP_SERVICE_PLAT_URL = '/plat-service'
VUE_APP_BASE_IMG_URL = 'https://powerexchange-fs.obs.cn-north-4.myhuaweicloud.com:443/'

# 路由懒加载
VUE_CLI_BABEL_TRANSPILE_MODULES = true
VUE_APP_ALLOW_URL = 'http://**************:9091/,http://************:9091/,https://s.chinatowercom.cn:81/,https://sl.chinatowercom.cn:81/,http://s.chinatowercom.cn:81/,https://s.chinatowercom.cn/,https://sl.chinatowercom.cn/,http://*************:9998/,http://127.0.0.1:9999/,http://localhost:9999/,http://************/,http://************:18082/,http://*************:8080/,'
#主工程地址
VUE_APP_IFRAME_PARENT_URL = 'http://**************:9091/'
#林业域名地址
VUE_APP_PLAT_URL = 'http://slwyytest.tower0788.cn/'

VUE_APP_GIS_RESOURCE_API = 'http://**************:9091/api'
