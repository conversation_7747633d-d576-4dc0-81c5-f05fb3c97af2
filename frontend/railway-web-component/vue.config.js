/*
 * @Author: 米亚流年 <EMAIL>
 * @Date: 2024-01-12 13:10:39
 * @LastEditors: liu.yongli
 * @LastEditTime: 2025-04-17 14:42:18
 * @FilePath: /remote-component-template/vue.config.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
let path = require("path");
const TerserPlugin = require('terser-webpack-plugin')
function resolve(dir) {
  return path.join(__dirname, dir);
}

module.exports = {
  publicPath: '/industry-11153',
  lintOnSave: false,
  // 显式指定需要 Babel 转译的依赖
  transpileDependencies: ['@ct/component-gallery-video-player-listener', 'mermaid', '@ctrl/tinycolor', 'marked', '@ct/ais-js-kit'],
  runtimeCompiler: true,
  devServer: {
    port: 8080,
    disableHostCheck: true,
    proxy: {
      // 远程组件proxy
      [process.env.VUE_APP_BASE_API]: {
        target: process.env.VUE_APP_SERVICE_URL,
        changeOrigin: true,
        pathRewrite: {
          ["^" + process.env.VUE_APP_BASE_API]: "",
        },
      },
      [process.env.VUE_APP_PLAT_API]: {
        target: process.env.VUE_APP_SERVICE_PLAT_URL,
        changeOrigin: true,
        pathRewrite: {
          ["^" + process.env.VUE_APP_PLAT_API]: "",
        },
      },
      '/rw-screen-demo/railway-biz-service': {
        target: `http://***********:18080/railway-biz-service`,
        changeOrigin: true,
        pathRewrite: {
          ['^/rw-screen-demo/railway-biz-service']: "",
        },
      },
      '/rw-screen-demo/railway-plat-service': {
        target: `http://***********:18080/railway-plat-service`,
        changeOrigin: true,
        pathRewrite: {
          ['^/rw-screen-demo/railway-plat-service']: "",
        },
      },
      '/rw-screen-demo/railway-dict-service': {
        target: 'http://***********:18080/railway-dict-service',
        changeOrigin: true,
        pathRewrite: {
          ['^/rw-screen-demo/railway-dict-service']: "",
        },
      },
    },
  },
  productionSourceMap: false,
  chainWebpack: (config) => {
    const svgRule = config.module.rule('svg');
    // 清除已有的所有loader
    // 如果你不这样做，接下来的loader会附加在该规则现有的loader之后
    svgRule.uses.clear();
    svgRule
      .test(/\.svg$/)
      .include.add(path.resolve(__dirname, 'src/assets/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]',
      });
    const fileRule = config.module.rule('file');
    fileRule.uses.clear();
    fileRule
      .test(/\.svg$/)
      .exclude.add(path.resolve(__dirname, 'src/assets/icons'))
      .end()
      .use('file-loader')
      .loader('file-loader');
  },
  // chainWebpack: config => {
  //   config.module
  //     .rule('marked')
  //     .test(/marked\.umd\.js$/)
  //     .use('babel-loader')
  //     .loader('babel-loader')
  //     .options({
  //       presets: ['@babel/preset-env']
  //     })
  // },
  configureWebpack: {
    resolve: {
      extensions: [".js", ".json", ".vue"],
      alias: {
        "@": resolve("src"),
        "#": resolve("public"),
        common: resolve("src/common"),
        marked: path.resolve(__dirname, 'node_modules/marked/src/marked.js')
      }
    },
    optimization: {
      minimizer: process.env.ENV === 'production' ? [
        new TerserPlugin({
          terserOptions: {
            ecma: undefined,
            warnings: false,
            parse: {},
            compress: {
              // drop_console: true,
              drop_debugger: true,
              // pure_funcs: ['console.log'] // 移除console
            }
          },
        }),
      ] : [
        new TerserPlugin({
          terserOptions: {
            ecma: undefined,
            warnings: false,
            parse: {},
            compress: {
              drop_debugger: true,
            }
          },
        }),
      ]
    },
    devtool: process.env.ENV !== 'production' ? 'source-map' : undefined,
    module: {
      rules: [
        {
          test: /marked\.umd\.js$/,
          use: {
            loader: 'babel-loader',
            options: {
              presets: ['@babel/preset-env']
            }
          }
        },
      ]
    }
  },
  css: {
    loaderOptions: {
      postcss: {
        plugins: [
          require("postcss-pxtorem")({
            rootValue: 100,
            propList: ["*"],
            exclude(file) {
              return file.indexOf("component-gallery-theme-chalk") !== -1;
            },
          }),
        ],
      },
    },
  },
};
