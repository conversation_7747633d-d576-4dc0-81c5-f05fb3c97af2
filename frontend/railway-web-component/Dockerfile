# 使用Nginx作为基础镜像
FROM nginx:1.27.0

# 清空默认的Nginx静态文件目录
RUN chmod 777 -R /usr/share/nginx/html/
RUN rm -rf /usr/share/nginx/html/*
RUN chmod 777 -R /usr/share/nginx/html/
# 将dist文件夹中的内容复制到Nginx的静态文件目录
COPY dist/ /usr/share/nginx/html/industry-11153
RUN chmod 777 -R /usr/share/nginx/html/
# 将自定义的nginx.conf配置文件复制到Nginx的配置目录
RUN chmod 777 -R /etc/nginx/
COPY nginx.conf /etc/nginx/nginx.conf
RUN chmod 777 -R /etc/nginx/
# 暴露容器端口（如果应用需要特定端口，请修改为对应端口）
EXPOSE 9999

# 启动 Nginx 服务
CMD ["nginx", "-g", "daemon off;"]