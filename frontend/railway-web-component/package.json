{"name": "remote-runtime", "version": "0.0.1", "private": true, "scripts": {"screen": "vue-cli-service serve --mode development --port 18800", "build": "vue-cli-service build --mode production", "build:dev": "vue-cli-service build --mode development", "build:prod": "vue-cli-service build --mode production", "test": "vue-cli-service --mode test build"}, "dependencies": {"@ct/china-tower-tech-ui": "^2.0.5", "@ct/component-gallery-theme-chalk": "^1.0.20", "@ct/component-gallery-video-player-listener": "beta", "@ct/ct_map_ol": "dev_comp_3.0", "@ct/icons-v2": "^1.2.20", "@ct/iframe-connect-sdk": "^1.0.19", "@ct/remote-page-loader": "^1.0.15", "@ct/remote-page-sync-loader": "^0.0.1", "@ct/shout-stream": "1.0.56", "axios": "^1.6.5", "babel-loader": "^10.0.0", "classnames": "^2.5.1", "core-js": "^3.6.5", "dayjs": "^1.11.13", "element-ui": "2.15.14", "file-saver": "^2.0.5", "immer": "^10.0.3", "jquery": "^3.7.1", "lodash": "^4.17.21", "marked": "4.2.12", "moment": "^2.30.1", "ol": "^7.4.0", "ol-ext": "^4.0.10", "qs": "^6.14.0", "swiper": "^5.4.3", "terser-webpack-plugin": "^4.2.3", "uuidjs": "^5.1.0", "vue": "^2.6.11", "vue-fullscreen": "^2.6.1", "vue-router": "3.0.2", "vue-video-player": "^5.0.2", "vuex": "^3.4.0"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@types/sass": "^1.45.0", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-service": "~4.5.0", "babel-eslint": "^10.1.0", "echarts": "5.4.3", "echarts-gl": "^2.0.8", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "less": "^4.1.1", "less-loader": "^7.3.0", "postcss-pxtorem": "^5.1.1", "sass": "^1.70.0", "sass-loader": "^7.3.1", "svg-sprite-loader": "^6.0.11", "vue-template-compiler": "^2.6.11", "worker-loader": "^3.0.8"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}