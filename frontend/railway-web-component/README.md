# remote-runtime

Components load containers remotely

## Getting started

node16+
pnpm

### 外部全局依赖准备

为降低组件打包负荷，组件库内将`Vue`和`CTMapOl地图包`两个包视为外部依赖，不打包在组件库中。
因此，集成工程需要在入口处（如本工程是main.js）主动暴露这两个依赖到window对象上：
```javascript
import Vue from 'vue';
import CTMapOl from '@ct/ct_map_ol';
// 暴露全局引用。组件库里把Vue和地图工具类独立了出去，对应的集成工程需要将这两个引用暴露到window，不然集成组件无法正常工作。
// 请注意：！！！暴露的名称不可更改！！！必须是Vue和@ct/ct_map_ol
window.Vue = Vue;
window['@ct/ct_map_ol'] = CTMapOl;
```

### RemoteSyncLoader 异步加载器使用注意

旧版使用`RemoteComponentLoader`，替换为新的`RemoteComponentSyncLoader`异步加载器时，需要注意以下内容：

1、App.vue内，需要注入新的`mapFlag`字段。这个`mapFlag`的数据由`mapRef`类提供（该类也有更改来适配）
**否则1.6.70开始的组件版本，地图相关功能无法正常工作。**
```javascript
// 
{
  provide() {
    return {
      mapRef: new MapRef({
        onMapSet: this.onMapChange
      }),
      mapFlag: () => this.mapFlag
    }
  },
  data: function() {
    return {
      mapFlag: null // 用于触发更新用的简易对象。这个对象仅仅用于刷新引用，里面的数据不具备任何意义。
    }
  },
  methods: {
    onMapChange() {
      this.mapFlag = {}
    }
  }
}
```
2、`@/utils/index.js`内提供了对标的`getBusinessFilesAsync`方法，各大屏也应使用此方法来获得对应的组件定义数据（原本是`getBusinessFiles`方法）
主要差别是Async版本不再在方法内自己执行scriptLoad操作，转为交给加载器执行。

### Install
 > 运行前置条件

- 1. 打包 theme-chalk
   1. 在`component-gallery`根目录运行`pnpm run build:theme-chalk`,
- 2. 进入`component-gallery/theme-chalk/dist` 执行`npm init` 生成`package.json` 文件,将此包发布到 npm

```json
{
  "name": "component-gallery-theme-chalk",
  "version": "1.0.1", // 变更版本号
  "description": "",
  "main": "index.js",
  "scripts": {
    "test": "echo \"Error: no test specified\" && exit 1"
  },
  "keywords": [],
  "author": "",
  "license": "ISC"
}
```

- 3. 进入`component-remote-page-loader`工程内,执行 build 命令,然后将仓库发布到 npm
- 4. 调用对应的接口 返回对应的数据
