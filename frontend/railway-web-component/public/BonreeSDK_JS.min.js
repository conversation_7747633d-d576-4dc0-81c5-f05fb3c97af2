(function(J,es){typeof exports=="object"&&typeof module!="undefined"?es(exports):typeof define=="function"&&define.amd?define(["exports"],es):es((J=typeof globalThis!="undefined"?globalThis:J||self).BonreeAgent={})})(this,function(exports){var version="2.1.7";function c$5(){return"labels"in HTMLInputElement.prototype}function i$4(J){if(!J.isContentEditable){if("innerText"in J){let es=J.innerText;return A$8()||(ts=>{var is=J.querySelectorAll(ts);for(let ns=0;ns<is.length;ns+=1){let rs=is[ns];if("innerText"in rs){let ss=rs.innerText;ss&&0<ss.trim().length&&(es=es.replace(ss,""))}}})("script, style"),es}return J.textContent}}function f$4(J,es){var ts=0;if(ts<J.length)return es(ts)}function p$9(J,es,ts=""){var is=J.charCodeAt(es-1),is=55296<=is&&is<=56319?es+1:es;return J.length<=is?J:""+J.slice(0,is)+ts}function b$4(J){return 100<J.length?p$9(J,100)+" [...]":J}function g$a(J){return J.replace(/\s+/g," ")}function d$a(J,es){return J.ownerDocument?J.ownerDocument.getElementById(es):null}let m$7=[J=>{var es;if(c$5()){if("labels"in J&&J.labels&&0<J.labels.length)return i$4(J.labels[0])}else if(J.id)return(es=J.ownerDocument&&f$4(J.ownerDocument.querySelectorAll("label"),ts=>ts.htmlFor===J.id))&&i$4(es)},J=>{if(J.nodeName==="INPUT"){var es=J.getAttribute("type");if(es==="button"||es==="submit"||es==="reset")return J.value}},J=>{if(J.nodeName==="BUTTON"||J.nodeName==="LABEL"||J.getAttribute("role")==="button")return i$4(J)},J=>J.getAttribute("aria-label"),J=>{var es=J.getAttribute("aria-labelledby");if(es)return es.split(/\s+/).map(ts=>d$a(J,ts)).filter(ts=>!!ts).map(ts=>i$4(ts)).join(" ")},J=>J.getAttribute("alt"),J=>J.getAttribute("name"),J=>J.getAttribute("title"),J=>J.getAttribute("placeholder"),J=>{if("options"in J&&0<J.options.length)return i$4(J.options[0])}];function A$8(){return!T$5()}function T$5(){return document.documentMode}let y$7=[J=>i$4(J)],x$6=10;function s$5(J,es){let ts=J,is=0;for(;is<=x$6&&ts&&ts.nodeName!=="BODY"&&ts.nodeName!=="HTML"&&ts.nodeName!=="HEAD";){for(let ns of es){let rs=ns(ts);if(typeof rs=="string"){let ss=rs.trim();if(ss)return b$4(g$a(ss))}}if(ts.nodeName==="FORM")break;ts=ts.parentElement,is+=1}}function getActionText(J){return s$5(J,m$7)||s$5(J,y$7)||""}var d$9=8;function hex_md5(J){return p$8(w$5(k$3(J),J.length*d$9))}function w$5(J,es){J[es>>5]|=128<<es%32,J[14+(es+64>>>9<<4)]=es;for(var ts=1732584193,is=-271733879,ns=-1732584194,rs=271733878,ss=0;ss<J.length;ss+=16){var as=ts,os=is,ds=ns,ls=rs,ts=v$7(ts,is,ns,rs,J[ss+0],7,-680876936),rs=v$7(rs,ts,is,ns,J[ss+1],12,-389564586),ns=v$7(ns,rs,ts,is,J[ss+2],17,606105819),is=v$7(is,ns,rs,ts,J[ss+3],22,-1044525330);ts=v$7(ts,is,ns,rs,J[ss+4],7,-176418897),rs=v$7(rs,ts,is,ns,J[ss+5],12,1200080426),ns=v$7(ns,rs,ts,is,J[ss+6],17,-1473231341),is=v$7(is,ns,rs,ts,J[ss+7],22,-45705983),ts=v$7(ts,is,ns,rs,J[ss+8],7,1770035416),rs=v$7(rs,ts,is,ns,J[ss+9],12,-1958414417),ns=v$7(ns,rs,ts,is,J[ss+10],17,-42063),is=v$7(is,ns,rs,ts,J[ss+11],22,-1990404162),ts=v$7(ts,is,ns,rs,J[ss+12],7,1804603682),rs=v$7(rs,ts,is,ns,J[ss+13],12,-40341101),ns=v$7(ns,rs,ts,is,J[ss+14],17,-1502002290),ts=f$3(ts,is=v$7(is,ns,rs,ts,J[ss+15],22,1236535329),ns,rs,J[ss+1],5,-165796510),rs=f$3(rs,ts,is,ns,J[ss+6],9,-1069501632),ns=f$3(ns,rs,ts,is,J[ss+11],14,643717713),is=f$3(is,ns,rs,ts,J[ss+0],20,-373897302),ts=f$3(ts,is,ns,rs,J[ss+5],5,-701558691),rs=f$3(rs,ts,is,ns,J[ss+10],9,38016083),ns=f$3(ns,rs,ts,is,J[ss+15],14,-660478335),is=f$3(is,ns,rs,ts,J[ss+4],20,-405537848),ts=f$3(ts,is,ns,rs,J[ss+9],5,568446438),rs=f$3(rs,ts,is,ns,J[ss+14],9,-1019803690),ns=f$3(ns,rs,ts,is,J[ss+3],14,-187363961),is=f$3(is,ns,rs,ts,J[ss+8],20,1163531501),ts=f$3(ts,is,ns,rs,J[ss+13],5,-1444681467),rs=f$3(rs,ts,is,ns,J[ss+2],9,-51403784),ns=f$3(ns,rs,ts,is,J[ss+7],14,1735328473),ts=o$2(ts,is=f$3(is,ns,rs,ts,J[ss+12],20,-1926607734),ns,rs,J[ss+5],4,-378558),rs=o$2(rs,ts,is,ns,J[ss+8],11,-2022574463),ns=o$2(ns,rs,ts,is,J[ss+11],16,1839030562),is=o$2(is,ns,rs,ts,J[ss+14],23,-35309556),ts=o$2(ts,is,ns,rs,J[ss+1],4,-1530992060),rs=o$2(rs,ts,is,ns,J[ss+4],11,1272893353),ns=o$2(ns,rs,ts,is,J[ss+7],16,-155497632),is=o$2(is,ns,rs,ts,J[ss+10],23,-1094730640),ts=o$2(ts,is,ns,rs,J[ss+13],4,681279174),rs=o$2(rs,ts,is,ns,J[ss+0],11,-358537222),ns=o$2(ns,rs,ts,is,J[ss+3],16,-722521979),is=o$2(is,ns,rs,ts,J[ss+6],23,76029189),ts=o$2(ts,is,ns,rs,J[ss+9],4,-640364487),rs=o$2(rs,ts,is,ns,J[ss+12],11,-421815835),ns=o$2(ns,rs,ts,is,J[ss+15],16,530742520),ts=F$4(ts,is=o$2(is,ns,rs,ts,J[ss+2],23,-995338651),ns,rs,J[ss+0],6,-198630844),rs=F$4(rs,ts,is,ns,J[ss+7],10,1126891415),ns=F$4(ns,rs,ts,is,J[ss+14],15,-1416354905),is=F$4(is,ns,rs,ts,J[ss+5],21,-57434055),ts=F$4(ts,is,ns,rs,J[ss+12],6,1700485571),rs=F$4(rs,ts,is,ns,J[ss+3],10,-1894986606),ns=F$4(ns,rs,ts,is,J[ss+10],15,-1051523),is=F$4(is,ns,rs,ts,J[ss+1],21,-2054922799),ts=F$4(ts,is,ns,rs,J[ss+8],6,1873313359),rs=F$4(rs,ts,is,ns,J[ss+15],10,-30611744),ns=F$4(ns,rs,ts,is,J[ss+6],15,-1560198380),is=F$4(is,ns,rs,ts,J[ss+13],21,1309151649),ts=F$4(ts,is,ns,rs,J[ss+4],6,-145523070),rs=F$4(rs,ts,is,ns,J[ss+11],10,-1120210379),ns=F$4(ns,rs,ts,is,J[ss+2],15,718787259),is=F$4(is,ns,rs,ts,J[ss+9],21,-343485551),ts=h$a(ts,as),is=h$a(is,os),ns=h$a(ns,ds),rs=h$a(rs,ls)}return Array(ts,is,ns,rs)}function _$2(J,es,ts,is,ns,rs){return h$a(C$8(h$a(h$a(es,J),h$a(is,rs)),ns),ts)}function v$7(J,es,ts,is,ns,rs,ss){return _$2(es&ts|~es&is,J,es,ns,rs,ss)}function f$3(J,es,ts,is,ns,rs,ss){return _$2(es&is|ts&~is,J,es,ns,rs,ss)}function o$2(J,es,ts,is,ns,rs,ss){return _$2(es^ts^is,J,es,ns,rs,ss)}function F$4(J,es,ts,is,ns,rs,ss){return _$2(ts^(es|~is),J,es,ns,rs,ss)}function h$a(J,es){var ts=(65535&J)+(65535&es);return(J>>16)+(es>>16)+(ts>>16)<<16|65535&ts}function C$8(J,es){return J<<es|J>>>32-es}function k$3(J){for(var es=Array(),ts=(1<<d$9)-1,is=0;is<J.length*d$9;is+=d$9)es[is>>5]|=(J.charCodeAt(is/d$9)&ts)<<is%32;return es}function p$8(J){for(var es="0123456789abcdef",ts="",is=0;is<4*J.length;is++)ts+=es.charAt(J[is>>2]>>is%4*8+4&15)+es.charAt(J[is>>2]>>is%4*8&15);return ts}let e$5={"3dm":"x-world/x-3dmf","3dmf":"x-world/x-3dmf",a:"application/octet-stream",aab:"application/x-authorware-bin",aam:"application/x-authorware-map",aas:"application/x-authorware-seg",abc:"text/vndabc",acgi:"text/html",afl:"video/animaflex",ai:"application/postscript",aif:"audio/x-aiff",aifc:"audio/x-aiff",aiff:"audio/x-aiff",aim:"application/x-aim",aip:"text/x-audiosoft-intra",ani:"application/x-navi-animation",aos:"application/x-nokia-9000-communicator-add-on-software",aps:"application/mime",arc:"application/octet-stream",arj:"application/octet-stream",art:"image/x-jg",asf:"video/x-ms-asf",asm:"text/x-asm",asp:"text/asp",asx:"application/x-mplayer2",au:"audio/x-au",avi:"video/avi",bcpio:"application/x-bcpio",bin:"application/x-macbinary",bm:"image/bmp",bmp:"image/x-windows-bmp",boo:"application/book",book:"application/book",boz:"application/x-bzip2",bsh:"application/x-bsh",bz:"application/x-bzip",bz2:"application/x-bzip2",c:"text/plain","c++":"text/plain",cat:"application/vndms-pki.seccat",cc:"text/plain",ccad:"application/clariscad",cco:"application/x-cocoa",cdf:"application/x-cdf",cer:"application/pkix-cert",cha:"application/x-chat",chat:"application/x-chat",class:"application/x-java-class",com:"application/octet-stream",conf:"text/plain",cpio:"application/x-cpio",cpp:"text/x-c",cpt:"application/x-cpt",crl:"application/pkcs-crl",crt:"application/x-x509-ca-cert",csh:"application/x-csh",css:"application/x-pointplus",cxx:"text/plain",dcr:"application/x-director",deepv:"application/x-deepv",der:"application/x-x509-ca-cert",dif:"video/x-dv",dir:"application/x-director",dl:"video/dl",doc:"application/msword",dot:"application/msword",dp:"application/commonground",drw:"application/drafting",dump:"application/octet-stream",dv:"video/x-dv",dvi:"application/x-dvi",dwf:"model/vnd.dwf",dwg:"application/acad",dxf:"application/dxf",dxr:"application/x-director",el:"text/x-script.elisp",elc:"application/x-bytecode.elisp compiled elisp)",env:"application/x-envoy",eps:"application/postscript",es:"application/x-esrehber",etx:"text/x-setext",evy:"application/x-envoy",exe:"application/octet-stream",f:"text/x-fortran",f77:"text/x-fortran",f90:"text/x-fortran",fdf:"application/vnd.fdf",fif:"application/fractals",fli:"video/x-fli",flo:"image/florian",flx:"text/vnd.fmi.flexstor",fmf:"video/x-atomic3d-feature",for:"text/x-fortran",fpx:"image/vnd.net-fpx",frl:"application/freeloader",funk:"audio/make",g:"text/plain",g3:"image/g3fax",gif:"image/gif",gl:"video/x-gl",gsd:"audio/x-gsm",gsm:"audio/x-gsm",gsp:"application/x-gsp",gss:"application/x-gss",gtar:"application/x-gtar",gz:"application/x-compressed",gzip:"application/x-gzip",h:"text/x-h",hdf:"application/x-hdf",help:"application/x-helpfile",hgl:"application/vnd.hp-hpgl",hh:"text/x-h",hlb:"text/x-script",hlp:"application/x-winhelp",hpg:"application/vnd.hp-hpgl",hpgl:"application/vnd.hp-hpgl",hqx:"application/binhex",hta:"application/hta",htc:"text/x-component",htm:"text/html",html:"text/html",htmls:"text/html",htt:"text/webviewhtml",htx:"text/html",ice:"x-conference/x-cooltalk",ico:"image/x-icon",idc:"text/plain",ief:"image/ief",iefs:"image/ief",iges:"application/iges",igs:"model/iges",ima:"application/x-ima",imap:"application/x-httpd-imap",inf:"application/inf",ins:"application/x-internett-signup",ip:"application/x-ip2",isu:"video/x-isvideo",it:"audio/it",iv:"application/x-inventor",ivr:"i-world/i-vrml",ivy:"application/x-livescreen",jam:"audio/x-jam",jav:"text/x-java-source",java:"text/x-java-source",jcm:"application/x-java-commerce",jfif:"image/pjpeg","jfif-tbnl":"image/jpeg",jpe:"image/pjpeg",jpeg:"image/pjpeg",jpg:"image/pjpeg",jps:"image/x-jps",js:"application/x-javascript",jut:"image/jutvision",kar:"audio/midi",ksh:"application/x-ksh",la:"audio/x-nspaudio",lam:"audio/x-liveaudio",latex:"application/x-latex",lha:"application/octet-stream",lhx:"application/octet-stream",list:"text/plain",lma:"audio/x-nspaudio",log:"text/plain",lsp:"application/x-lisp",lst:"text/plain",lsx:"text/x-la-asf",ltx:"application/x-latex",lzh:"application/octet-stream",lzx:"application/octet-stream",m:"text/x-m",m1v:"video/mpeg",m2a:"audio/mpeg",m2v:"video/mpeg",m3u:"audio/x-mpequrl",man:"application/x-troff-man",map:"application/x-navimap",mar:"text/plain",mbd:"application/mbedlet",mc$:"application/x-magic-cap-package-1.0",mcd:"application/x-mathcad",mcf:"text/mcf",mcp:"application/netmc",me:"application/x-troff-me",mht:"message/rfc822",mhtml:"message/rfc822",mid:"application/x-midi",midi:"application/x-midi",mif:"application/x-mif",mime:"www/mime",mjf:"audio/x-vnd.audioexplosion.mjuicemediafile",mjpg:"video/x-motion-jpeg",mm:"application/x-meme",mod:"audio/x-mod",moov:"video/quicktime",mov:"video/quicktime",movie:"video/x-sgi-movie",mp2:"audio/x-mpeg",mp3:"audio/x-mpeg-3",mpa:"audio/mpeg",mpc:"application/x-project",mpe:"video/mpeg",mpeg:"video/mpeg",mpg:"video/mpeg",mpga:"audio/mpeg",mpp:"application/vnd.ms-project",mpt:"application/x-project",mpv:"application/x-project",mpx:"application/x-project",mrc:"application/marc",ms:"application/x-troff-ms",mv:"video/x-sgi-movie",my:"audio/make",mzz:"application/x-vnd.audioexplosion.mzz",nap:"image/naplps",naplps:"image/naplps",nc:"application/x-netcdf",ncm:"application/vnd.nokia.configuration-message",nif:"image/x-niff",niff:"image/x-niff",nix:"application/x-mix-transfer",nsc:"application/x-conference",nvd:"application/x-navidoc",o:"application/octet-stream",oda:"application/oda",omc:"application/x-omc",omcd:"application/x-omcdatamaker",omcr:"application/x-omcregerator",p:"text/x-pascal",p10:"application/x-pkcs10",p12:"application/x-pkcs12",p7a:"application/x-pkcs7-signature",p7c:"application/x-pkcs7-mime",p7m:"application/x-pkcs7-mime",p7r:"application/x-pkcs7-certreqresp",p7s:"application/pkcs7-signature",part:"application/pro_eng",pas:"text/pascal",pbm:"image/x-portable-bitmap",pcl:"application/x-pcl",pcx:"image/x-pcx",pdb:"chemical/x-pdb",pdf:"application/pdf",pfunk:"audio/make.my.funk",pgm:"image/x-portable-greymap",pic:"image/pict",pict:"image/pict",pkg:"application/x-newton-compatible-pkg",pko:"application/vnd.ms-pki.pko",pl:"text/x-script.perl",plx:"application/x-pixclscript",pm:"text/x-script.perl-module",pm4:"application/x-pagemaker",pm5:"application/x-pagemaker",png:"image/png",pnm:"application/x-portable-anymap",pot:"application/mspowerpoint",pov:"model/x-pov",ppa:"application/vnd.ms-powerpoint",ppm:"image/x-portable-pixmap",pps:"application/vnd.ms-powerpoint",ppt:"application/powerpoint",ppz:"application/mspowerpoint",pre:"application/x-freelance",prt:"application/pro_eng",ps:"application/postscript",psd:"application/octet-stream",pvu:"paleovu/x-pv",pwz:"application/vnd.ms-powerpoint",py:"text/x-script.phyton",pyc:"application/x-bytecode.python",qcp:"audio/vnd.qcelp",qd3:"x-world/x-3dmf",qd3d:"x-world/x-3dmf",qif:"image/x-quicktime",qt:"video/quicktime",qtc:"video/x-qtc",qti:"image/x-quicktime",qtif:"image/x-quicktime",ra:"audio/x-realaudio",ram:"audio/x-pn-realaudio",ras:"application/x-cmu-raster",rast:"image/cmu-raster",rexx:"text/x-script.rexx",rf:"image/vnd.rn-realflash",rgb:"image/x-rgb",rm:"application/vnd.rn-realmedia",rmm:"audio/x-pn-realaudio",rmp:"audio/x-pn-realaudio-plugin",rng:"application/vnd.nokia.ringing-tone",rnx:"application/vnd.rn-realplayer",roff:"application/x-troff",rp:"image/vnd.rn-realpix",rpm:"audio/x-pn-realaudio-plugin",rt:"text/vnd.rn-realtext",rtf:"application/x-rtf",rtx:"application/rtf",rv:"video/vnd.rn-realvideo",s:"text/x-asm",s3m:"audio/s3m",saveme:"application/octet-stream",sbk:"application/x-tbook",scm:"application/x-lotusscreencam",sdml:"text/plain",sdp:"application/x-sdp",sdr:"application/sounder",sea:"application/x-sea",set:"application/set",sgm:"text/x-sgml",sgml:"text/x-sgml",sh:"application/x-sh",shar:"application/x-shar",shtml:"text/x-server-parsed-html",sid:"audio/x-psid",sit:"application/x-sit",skd:"application/x-koan",skm:"application/x-koan",skp:"application/x-koan",skt:"application/x-koan",sl:"application/x-seelogo",smi:"application/smil",smil:"application/smil",snd:"audio/x-adpcm",sol:"application/solids",spc:"application/x-pkcs7-certificates",spl:"application/futuresplash",spr:"application/x-sprite",sprite:"application/x-sprite",src:"application/x-wais-source",ssi:"text/x-server-parsed-html",ssm:"application/streamingmedia",sst:"application/vnd.ms-pki.certstore",step:"application/step",stl:"application/vnd.ms-pki.stl",stp:"application/step",sv4cpio:"application/x-sv4cpio",sv4crc:"application/x-sv4crc",svf:"image/x-dwg",svr:"application/x-world",swf:"application/x-shockwave-flash",t:"application/x-troff",talk:"text/x-speech",tar:"application/x-tar",tbk:"application/toolbook",tcl:"application/x-tcl",tcsh:"text/x-script.tcsh",tex:"application/x-tex",texi:"application/x-texinfo",texinfo:"application/x-texinfo",text:"application/plain",tgz:"application/gnutar",tif:"image/x-tiff",tiff:"image/x-tiff",tr:"application/x-troff",tsi:"audio/tsp-audio",tsp:"application/dsptype",tsv:"text/tab-separated-values",turbot:"image/florian",txt:"text/plain",uil:"text/x-uil",uni:"text/uri-list",unis:"text/uri-list",unv:"application/i-deas",uri:"text/uri-list",uris:"text/uri-list",ustar:"application/x-ustar",uu:"application/octet-stream",uue:"text/x-uuencode",vcd:"application/x-cdlink",vcs:"text/x-vcalendar",vda:"application/vda",vdo:"video/vdo",vew:"application/groupwise",viv:"video/vivo",vivo:"video/vivo",vmd:"application/vocaltec-media-desc",vmf:"application/vocaltec-media-file",voc:"audio/x-voc",vos:"video/vosaic",vox:"audio/voxware",vqe:"audio/x-twinvq-plugin",vqf:"audio/x-twinvq",vql:"audio/x-twinvq-plugin",vrml:"application/x-vrml",vrt:"x-world/x-vrt",vsd:"application/x-visio",vst:"application/x-visio",vsw:"application/x-visio",w60:"application/wordperfect6.0",w61:"application/wordperfect6.1",w6w:"application/msword",wav:"audio/x-wav",wb1:"application/x-qpro",wbmp:"image/vnd.wap.wbmp",web:"application/vnd.xara",wiz:"application/msword",wk1:"application/x-123",wmf:"windows/metafile",wml:"text/vnd.wap.wml",wmlc:"application/vnd.wap.wmlc",wmls:"text/vnd.wap.wmlscript",wmlsc:"application/vnd.wap.wmlscriptc",word:"application/msword",wp:"application/wordperfect",wp5:"application/wordperfect",wp6:"application/wordperfect",wpd:"application/wordperfect",wq1:"application/x-lotus",wri:"application/x-wri",wrl:"application/x-world",wrz:"model/vrml",wsc:"text/scriplet",wsrc:"application/x-wais-source",wtk:"application/x-wintalk",xbm:"image/x-xbitmap",xdr:"video/x-amt-demorun",xgz:"xgl/drawing",xif:"image/vnd.xiff",xl:"application/excel",xla:"application/x-msexcel",xlb:"application/x-excel",xlc:"application/x-excel",xld:"application/x-excel",xlk:"application/x-excel",xll:"application/x-excel",xlm:"application/x-excel",xls:"application/excel",xlt:"application/excel",xlv:"application/x-excel",xlw:"application/vnd.ms-excel",xm:"audio/xm",xml:"application/xml",xmz:"xgl/movie",xpix:"application/x-vnd.ls-xpix",xpm:"image/x-xpixmap","x-png":"image/png",xsr:"video/x-amt-showrun",xwd:"image/x-xwindowdump",xyz:"chemical/x-pdb",z:"application/x-compressed",zip:"multipart/x-zip",zoo:"application/octet-stream",zsh:"text/x-script.zsh"};function getMineTypeByHeader(J,es="text/html"){return isEmpty(J)?es:(J["Content-Type"]||J["content-type"]||es).split(";")[0].trim()}function getMineTypeByUrl(J){var es="text/html";return isEmpty(J)?es:l$5(c$4(J),es)}function c$4(J){if(isEmpty(J))return"";let es="",ts=J.lastIndexOf(".");if(ts!=-1){let is=J.indexOf("?",ts);is==-1&&(is=J.length),es=J.substring(ts+1,is)}return es}function l$5(J,es){return isEmpty(J)||isEmpty(J=e$5[J])?es:J}function getRandom(J){return J<0?NaN:J<=30?0|Math.random()*(1<<J):J<=53?(0|Math.random()*(1<<30))+(0|Math.random()*(1<<J-30))*(1<<30):NaN}function t(J,es){for(var ts=J.toString(16),is=es-ts.length,ns="0";0<is;is>>>=1,ns+=ns)1&is&&(ts=ns+ts);return ts}function uuid(){return t(getRandom(32),8)+"-"+t(getRandom(16),4)+"-"+t(16384|getRandom(12),4)+"-"+t(32768|getRandom(14),4)+"-"+t(getRandom(48),12)}function uuidWithLength(J){return J==16?t(getRandom(32),8)+t(getRandom(32),8):J==32?t(getRandom(32),8)+t(getRandom(32),8)+t(getRandom(32),8)+t(getRandom(32),8):void 0}function skyUuid(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,J=>{var es=16*Math.random()|0;return(J==="x"?es:3&es|8).toString(16)})}function buildViewID(){return Date.now().toString(36)+Math.ceil(1e3*Math.random()).toString(16)}function enBase64(J){for(var es=[],ts=0,is=(J=new TextEncoder().encode(J)).length;ts<is;ts+=4096)es.push(String.fromCharCode.bind(String).apply(null,J.subarray(ts,ts+4096)));return btoa(es.join(""))}function traceId(){return"00-"+t(getRandom(32),8)+t(getRandom(32),8)+t(getRandom(32),8)+t(getRandom(32),8)+"-"+t(getRandom(32),8)+t(getRandom(32),8)+"-00"}var $global=window||{};function isDefined(J){return J!=null}function log(J,es){(es||isDefined(window.BonreeAgent)&&isDefined(window.BonreeAgent.isDebuge)||isDefined(window.BonreeClient)&&isDefined(window.BonreeClient.isDebuge))&&isDefined(window.console)&&(window.console.$bonree=!0,console.log(J),window.console.$bonree=!1)}function extend(J,es){return J&&es&&forEachOwn(es,function(ts,is){J[is]=ts}),J}function isString(J){return typeof J=="string"}function isJSON(J){if(isString(J))try{var es=JSON.parse(J);return!(typeof es!="object"||!es)}catch(ts){return!1}}function NIL_FN(){}function hasOwnProperty(J,es){return J&&Object.prototype.hasOwnProperty.call(J,es)}function isObject(J){return typeof J=="object"}function overwrite(J,es){return J&&es&&forEachOwn(es,function(ts,is){hasOwnProperty(J,is)&&(J[is]=ts)}),J}function forEachOwn(J,es,ts){if(isDefined(J)){for(var is in J)if(hasOwnProperty(J,is)){var ns=J[is];try{if(es(ns,is)===!1)break}catch(rs){}}ts&&ts()}}function isFunction(J){return typeof J=="function"}function isArray(J){return isDefined(J)&&J instanceof Array}function forEach(J,es,ts){(isArray(J)?forEachArray:forEachOwn)(J,es,ts)}function forEachArray(J,es,ts){if(isArray(J)){for(var is=0,ns=J.length;is<ns&&es(J[is],is)!==!1;is++);ts&&ts()}}function bind(J,es){return function(){return J.apply(es,arguments)}}function isEmpty(J){if(isDefined(J)){if(hasOwnProperty(J,"length"))return!J.length;if(hasOwnProperty(J,"size"))return!J.size;for(var es in J)if(hasOwnProperty(J,es))return}return 1}function setCookie(J,es,ts){try{var is=J+"="+es;isDefined((ts=ts||{}).domain)&&(is+=";domain="+ts.domain),isDefined(ts.path)&&(is+=";path="+ts.path),isDefined(ts.expires)&&(typeof ts.expires=="string"?is+=";expires="+ts.expires:is+=";expires="+ts.expires.toUTCString()),document.cookie=is,window.sessionStorage&&window.sessionStorage.setItem(J,es),window.localStorage&&window.localStorage.setItem(J,es)}catch(ns){log("C107")}}function getCookie(J){try{var es=document.cookie.split(/;\s?/);if(document.cookie)for(var ts=0,is=es.length;ts<is;ts++){var ns=es[ts].split("="),rs=ns[0],ss=ns[1];if(rs===J&&isDefined(ss))return ss}return getSession(J)||""}catch(as){return log("C108"),""}}function getSession(J){let es="";return es=window.sessionStorage&&isDefined(es=window.sessionStorage.getItem(J)||"")&&es!=""?es:getLocaStore(J)||""}function getLocaStore(J){let es="";return es=window.localStorage?window.localStorage.getItem(J)||"":es}function isReadable(J){return isDefined(J)&&!isEmpty(J)}function dateNow(){return Date.now?Date.now():new Date().getTime()}var nowtime=function(){var J=dateNow();return function(es){var ts;return isDefined($global.performance)&&isDefined($global.performance.now)&&isDefined($global.performance.timing)?[1e3*(ts=es===0?0:Math.round($global.performance.now())),1e3*(ts+$global.performance.timing.navigationStart)]:[1e3*((ts=es===0?J:dateNow())-J),1e3*ts]}}();function now(J){return nowtime(J)[1]}function tillNow(J){return nowtime(J)[0]}function getTime(){var J=nowtime();return{startTime:J[0],timestamp:J[1]}}function args(J){return isDefined(J.length)?Array.prototype.slice.call(J):J}function map(J,es){var ts=[];return forEach(J,function(is,ns){is=es&&es(is,ns),isDefined(is)&&ts.push(is)}),ts}var getFixedMetric=function(J,es){return J=J[es],isDefined(J)&&0<J?Math.round(J):0},getContentSize=function(J){return isDefined(J)?window.ArrayBuffer&&J instanceof ArrayBuffer?J.byteLength:window.Blob&&J instanceof Blob?J.size:J.toJSON?stringify(J.toJSON()).length:J.length||0:0},getRequestParam=function(J){if(!isDefined(J))return"";var es="";if(typeof J=="string")es=J;else if(window.FormData&&J instanceof FormData){var ts={};try{forEach(iterate(J.entries()),function(rs){var ns=rs[0],rs=rs[1];window.File&&rs instanceof File?0<rs.size&&(ts[ns]="[File]"+(rs.name?" "+rs.name:"")):ts[ns]=rs})}catch(is){ts={},log("C145")}es="[FormData] "+stringify(ts)}else{if(window.Blob&&J instanceof Blob)return"[Blob]";if(window.ArrayBuffer&&J instanceof ArrayBuffer)return"[ArrayBuffer]";if(J.toJSON)es=stringify(J.toJSON());else{if(!J.toString)return"[unknown]";es=J.toString()}}return withLength(es,2e3)};function getIP(J){return J?(J=J.split("?")[0],/(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\:([0-9]{1,5})/.test(J)?[RegExp.$1+`.${RegExp.$2}.${RegExp.$3}.`+RegExp.$4,Number(RegExp.$5)]:/\:([0-9]{1,5})/.test(J)?["",Number(RegExp.$1)]:-1<J.indexOf("https")?["",443]:["",80]):["",""]}function getUrl(J=""){return window.location.protocol!=="file:"||startWith(J,"http")?isDefined(J)&&J.indexOf("http")===0?J:/^\/{2}/.test(J)?window&&window.location.protocol+J||"http:"+J:/^\?/.test(J)?window&&window.location.origin+window.location.pathname+J||J:/^\/{1}/.test(J)?window&&window.location.origin+J||J:/^\.{1,}/.test(J)?window&&window.location.origin+J.replace(/^(\.{1,}\/){1,}/,"/")||J:isDefined(J)&&window&&window.location.origin+"/"+J||J:J||window&&window.location.href+J}var stringify=isDefined($global.JSON)?$global.JSON.stringify:function(J){var es,ts,is=typeof J;return is=="undefined"?'"undefined"':is=="boolean"?J?"true":"false":is=="number"?J.toString():is=="string"?'"'+(es=J.replace(/(["\\])/g,"\\$1").replace(/\r/g,"\\r").replace(/\n/g,"\\n"))+'"':is=="object"?J?J instanceof Array?(es="[",forEach(J,function(ns,rs){0<rs&&(es+=","),es+=stringify(ns)}),es+="]"):(es="{",ts=0,forEachOwn(J,function(ns,rs){0<ts&&(es+=","),es+=stringify(rs)+":"+stringify(ns),ts++}),es+="}"):"null":"[none]"};function addListener(J,es,ts,is){J&&J.addEventListener&&J.addEventListener(es,ts,is)}function on$2(J,es,ts,is){if(J&&J.addEventListener)return J.addEventListener(es,ts,is);J&&J.attachEvent&&J.attachEvent("on"+es,ts)}function delay(J,es){return J.$$original=!0,setTimeout(J,isDefined(es)?es:0)}function last(J){if(isArray(J)&&!isEmpty(J))return J[J.length-1]}var unicode=function(J){for(var es="",ts=0;ts<J.length;ts++)es+="\\u"+("0000"+J.charCodeAt(ts).toString(16)).slice(-4);return es},hack=function(J,es,ts,is){var ns;return(J=isDefined(J)?J:function(){}).$$original?J:((ns=function(){var rs,ss=args(arguments);try{return isDefined(es)&&es.apply(this,ss),J.apply(this,ss)}catch(as){isDefined(is)?is.apply(this,ss.concat(as)):(rs=as,isDefined(window.console)&&(window.console.$bonree=!0,(isDefined(window.console.error)?window.console.error(rs):NIL_FN)(rs),window.console.$bonree=!1))}finally{if(isDefined(ts)&&ts.apply(this,ss),ss&&ss[0]&&ss[0]instanceof Error&&J&&J.name!=="error")throw ss[0]}}).$$original=J,ns)},xpath=function(J){if(!J)return"";var es=J.id;if(es)return'//*[@id="'+es+'"]';for(var ts=[],is=J;is;){var ns=is.tagName&&is.tagName.toLowerCase&&is.tagName.toLowerCase()||null;if(!ns)break;var rs=is.parentElement&&is.parentElement||null;if(!isDefined(rs)){ts.unshift(ns);break}for(var ss=rs.children,as=ss.length,os=0,ds=0,ls=0;ls<as;ls++){var fs=ss[ls];if(fs.tagName.toLowerCase()===ns&&(ds++,0<os))break;fs===is&&(os=ds)}ts.unshift(ds===1?ns:ns+"["+os+"]"),is=rs}return"/"+ts.join("/")},prop=function(J,es){return isDefined(J)&&hasOwnProperty(J,es)?J[es]:null};function includes(J,es){for(let ts=0,is=J.length;ts<is;++ts)if(J[ts]===es)return!0;return!1}function isObj(J){return typeof J=="object"}function getUserAgent(){try{var J={Chrome:/Chrome/,IE:/MSIE/,Firefox:/Firefox/,Opera:/Presto/,Safari:/Version\/([\d.]+).*Safari/,360:/360SE/,QQBrowswe:/QQ/},es={iPhone:/iPhone/,iPad:/iPad/,Android:/Android/,Windows:/Windows/,Mac:/Macintosh/},ts=navigator&&navigator.userAgent;if(!ts)return null;var is,ns,rs={browserName:"",browserVersion:"",osName:"",osVersion:"",deviceName:""};for(is in J)J[is].test(ts)&&(rs.browserName=is,is==="Chrome"?rs.browserVersion=ts.split("Chrome/")[1].split(" ")[0]:is==="IE"?rs.browserVersion=ts.split("MSIE ")[1].split(" ")[1]:is==="Firefox"?rs.browserVersion=ts.split("Firefox/")[1]:is==="Opera"?rs.browserVersion=ts.split("Version/")[1]:is==="Safari"?rs.browserVersion=ts.split("Version/")[1].split(" ")[0]:is==="360"?rs.browserVersion="":is==="QQBrowswe"&&(rs.browserVersion=ts.split("Version/")[1].split(" ")[0]));for(ns in es)es[ns].test(ts)&&(rs.osName=ns,ns==="Windows"?rs.osVersion=ts.split("Windows NT ")[1].split(";")[0]:ns==="Mac"?rs.osVersion=ts.split("Mac OS X ")[1].split(")")[0]:ns==="iPhone"?rs.osVersion=ts.split("iPhone OS ")[1].split(" ")[0]:ns==="iPad"?rs.osVersion=ts.split("iPad; CPU OS ")[1].split(" ")[0]:ns==="Android"&&(rs.osVersion=ts.split("Android ")[1].split(";")[0],rs.deviceName=ts.split("(Linux; Android ")[1].split("; ")[1].split(" Build")[0]));return rs}catch(ss){log("C123")}}function getElementForNum(J,es){var ts;return isObj(J)?(ts={},Object.keys(J).map((is,ns)=>{ns<=es&&(ts[is]=J[is])}),ts):{}}function iterate(J){var es=[];if(isDefined(J)&&isDefined(J.next))for(var ts=J.next();!ts.done;)es.push(ts.value),ts=J.next();return es}function readBytesFromStream(J,es,ts){if(J.getReader){let as=function(){var os;ns.cancel().catch(noop);{let ds;if(rs.length===1)ds=rs[0];else{ds=new Uint8Array(ss);let ls=0;rs.forEach(fs=>{ds.set(fs,ls),ls+=fs.length})}return os=ds.slice(0,es.bytesLimit),ds.length,es.bytesLimit,os.length}};var is=as;let ns=J.getReader(),rs=[],ss=0;(function os(){ns.read().then(ds=>{var ls;ds.done?(ls=as(),ts(ls)):(rs.push(ds.value),(ss+=ds.value.length)>es.bytesLimit?(ls=as(),ts(ls)):os())})})()}}function noop(){}function tryToClone(J){try{return J.clone()}catch(es){}}function set_64_keys(data,emit){var reData={},keys;if(isDefined(data)){if(typeof data=="string"&&data!==""){if(data=eval("("+data+")"),!isJSON(data))return data;data=JSON.parse(data)}return typeof data!="object"?data:(keys=Object.keys(data),keys.length>emit?(keys=keys.slice(0,emit),keys.forEach(function(J){reData[J]=data[J]}),reData):data)}return reData}function filter(J,es){var ts=[];return forEach(J,function(is,ns){es&&es(is,ns)===!0&&ts.push(is)}),ts}function setDeviceForLog(J){window.bonreeRUM&&J&&(window.bonreeRUM.fingerId=J)}function size(J){if(J){if(typeof J=="string")return strSize(J);if(J instanceof ArrayBuffer)return J.byteLength;if(isObject(J)){let es="";try{es=JSON.stringify(J)}catch(ts){}return strSize(es)}}return 0}function strSize(J){let es=0;for(let is=0,ns=J.length;is<ns;is++){var ts=J.charCodeAt(is);es+=ts<=127?1:ts<=2047?2:ts<=65535?3:4}return es}function trim(J){return J?String.prototype.trim?String.prototype.trim.call(J):J.toString().replace(/(^\s+)|(\s+$)/g,""):""}function startWith(J,es){return J&&J.indexOf&&J.indexOf(es)===0}function endWith(J,es){return!!areUrlsEqual(J,es)||!!(J&&es&&J.length>=es.length)&&(J.slice(-1)==="/"&&(es+="/"),J.substring(J.length-es.length)===es)}function areUrlsEqual(J,es){try{var ts=new URL(J),is=new URL(es);return ts.protocol===is.protocol&&ts.host===is.host&&ts.pathname===is.pathname&&ts.search===is.search&&ts.hash===is.hash}catch(ns){}}function withLength(J,es){try{return J?J.length<=es?J:J.substr(0,es-3)+"...":""}catch(ts){return"The data cannot be parsed normally, please handle it manually"}}function toUpper(J,es){return isDefined(es)||(es=""),J&&J.toUpperCase?J.toUpperCase():es}function toLower(J,es){return isDefined(es)||(es=""),J&&J.toLowerCase?J.toLowerCase():es}var stringify=isDefined(window.JSON)?window.JSON.stringify:function(J){var es,ts,is=typeof J;return is=="undefined"?'"undefined"':is=="boolean"?J?"true":"false":is=="number"?J.toString():is=="string"?'"'+(es=J.replace(/(["\\])/g,"\\$1").replace(/\r/g,"\\r").replace(/\n/g,"\\n"))+'"':is=="object"?J?J instanceof Array?(es="[",forEach(J,function(ns,rs){0<rs&&(es+=","),es+=stringify(ns)}),es+="]"):(es="{",ts=0,forEachOwn(J,function(ns,rs){0<ts&&(es+=","),es+=stringify(rs)+":"+stringify(ns),ts++}),es+="}"):"null":"[none]"};function toString(J){return typeof J=="string"?J:JSON.stringify(J)}function set_length(J,es){return J&&(J.length>es?J.slice(0,es):J)}function getDataSize(J,es){return typeof J!="string"&&(J=JSON.stringify(J)),J=new Blob([J]).size/1048576,isDefined(es)?J<es:J}let GC={BROWSER:0,ANDROID:1,IOS:2,WINDOWS:3,HOS:4,ZERO:0,THOUSAND:1e3,TYPEARR:["","android","ios","pc"],FLUSH_DATA:"data/flush",PAGE_READY:"page/ready",PAGE_LOAD:"page/load",PAGE_DATA:"data/page",RESOURCE_DATA:"data/resource",CONSOLE_DATA:"data/console",TITLE:"title",DURATION:"duration",NAVIGATION_START:"navigationStart",FETCH_START:"fetchStart",START_TIME:"startTime",UNLOAD_EVENT_START:"unloadEventStart",UNLOAD_EVENT_END:"unloadEventEnd",REDIRECT_START:"redirectStart",REDIRECT_END:"redirectEnd",DOMAIN_LOOKUP_START:"domainLookupStart",DOMAIN_LOOKUP_END:"domainLookupEnd",CONNECT_START:"connectStart",CONNECT_END:"connectEnd",SECURE_CONNECTION_START:"secureConnectionStart",REQUEST_START:"requestStart",RESPONSE_START:"responseStart",RESPONSE_END:"responseEnd",RESPONSE_STATUS:"responseStatus",DOM_LOADING:"domLoading",DOM_INTERACTIVE:"domInteractive",DOM_CONTENT_LOADED_EVENT_START:"domContentLoadedEventStart",DOM_CONTENT_LOADED_EVENT_END:"domContentLoadedEventEnd",DOM_COMPLETE:"domComplete",LOAD_EVENT_START:"loadEventStart",LOAD_EVENT_END:"loadEventEnd",FIRST_PAINT:"fp",FIRST_CONTENTFUL_PAINT:"fcp",LARGEST_CONTENTFUL_PAINT:"lcp",REDIRECT_COUNT:"redirectCount",WORKER_START:"workerStart",NEXT_HOP_PROTOCOL:"nextHopProtocol",TRANSFER_SIZE:"transferSize",ENCODED_BODY_SIZE:"encodedBodySize",DECODED_BODY_SIZE:"decodedBodySize",UPLOAD_BODY_SIZE:"uploadBodySize",REQUEST_HEADER:"requestHeader",RESPONSE_HEADER:"responseHeader",CALLBACK_START:"callbackStart",CALLBACK_END:"callbackEnd",CALLBACK_TIME:"callbackTime",USER_TIME:"userTime",TIMESTAMP:"timestamp",INITIATOR_TYPE:"initiatorType",XML_HTTP_REQUEST:"xmlhttprequest",FETCH:"fetch",TAG_NAME:"tagName",TYPE:"type",MODEL:"model",EMBED:"embed",IS_MAIN_DOCUMENT:"isMainDocument",PAGE_ID:"pageViewId",PAGE_URL:"pageUrl",PAGE_TITLE:"pageTitle",MESSAGE:"message",NAME:"name",ERROR_LINE:"line",ERROR_FILE:"file",ERROR_COLUMN:"column",ERROR_STACK:"stack",JS_ERROR:"jsError",IS_SLOW:"isSlow",IS_CUSTOM:"isCustome",URL:"url",NET_METHOD:"method",NET_PORT:"port",NET_IP:"ip",NET_DT:"domainLookupTime",NET_CT:"connectTime",NET_SSLT:"ssl",NET_DTI:"downloadTime",NET_CNA:"cname",NET_PT:"protocalType",NET_TID:"guid",NET_XBR:"xBrResponse",NET_TRACE:"traceResponse",NET_TPAR:"tpar",NET_EOP:"errorOccurrentProcess",NET_EC:"code",NET_TYPE:"requestType",NET_RET:"resourceType",CUSTOM_IC:"customIc",NET_CBBQ:"requestBodyByKey",REQUEST_BODY:"requestBody",NET_CBHQ:"requestHeaderByKey",NET_CBQ:"reqUrlDta",REQUEST_HEADER_ALL:"requestHeaderAll",E_TYPE:"eType",UA:"userAgent",SOURCE_OF_ACTION:"sourceOfAction",FRAME_TYPE:"frameType",STAY_TIME:"stayTime",CORRELATION_ID:"correlationId",IS_EXIT:"isExit",PAGE_CREATE_TIME:"pageCreateTime",REFERRER:"referrer",STATUS:"status",STATUS_TEXT:"statusText",ALIAS:"alias",PATH:"path",ROOT:"root",FULL_URL:"fullUrl",FRAME_WORK:"framework",CLIENT_TYPE:"clientType",VALUE:"value",FULL_RESOURCE_LOAD_TIME:"fullResourceLoadTime",PAGE_INVISIBLE:"page/invisible",PAGE_VISIBLE:"page/visible",RESOURCE_DUMP:"resource/dump",ERROR_DATA:"data/error",REQUEST_INIT:"request/init",REQUEST_DATA:"data/request",ROUTE_DATA:"data/route",ACTION_DATA:"data/event",TRACE_ACTION_DATA:"data/traceAction",SESSION_CHANGE:"session/change",USER_SET:"user/set",PARAM:"param",SPAN_DATA:"data/span",FULLY_COLLECT:1e3,NET:"network",VIEW:"view",ERROR:"jserror",ACTION:"action",ROUTE:"routechange",WEBVIEWDATA:"h5",PAGE:"page",RESOURCE:"resource",CUSTOM_EVENT:"customevent",CUSTOM_METRIC:"custommetric",CUSTOM_LOG:"customlog",CRASH:"crash",SPEED_TEST:"speedtest",CONSOLE:"console",SPAN:"span",GUID_KEY:"br-resp-key",X_BR_RESPONSE:"x-br-response",TRACE_RESPONSE:"traceresponse",LIFESTYLE_XHR_START:"XMLHttpRequest_Start",LIFESTYLE_REQUEST_END:"Request_Completed",LIFESTYLE_FETCH_START:"FETCH_Satrt",CUSTOM_ACTION_END:"customActionEnd",INIT_SESSION_START:"initSessionStart",LIFESTYLE_CALLBACK_REQUEST_START:"CallbackRequest_Start",ROUTE_CHANGE_START:"ROUTE_CHANGE_START",ROUTE_CHANGE_END:"ROUTE_CHANGE_END",SPAN_CALLBACK_DATA:"SPAN_CALLBACK_DATA",WEBSOCKET_CONNECTION_START:"WEBSOCKET_CONNECTION_START",WEBSOCKET_CONNECTION_END:"WEBSOCKET_CONNECTION_END",XHR_CALLBACK_IDENTIFY:"XHR_CALLBACK_IDENTIFY"};function u$2(J){return isDefined(J)&&!(J instanceof Object)&&(isString(J)||(J=""+J),/^[\u4E00-\u9FFFa-zA-Z0-9:_\-@.\s/]+$/.test(J))&&J.length<=256}let p$7=class{constructor(){this.useAPI=!1,this.configeManage=[],this.originManageData=null,this.cookieArr=[],this.requestArr=[],this.responseArr=[],this.isStopGetValue=50}checkOriginManageData(J){this.useAPI||isDefined(J=J&&isString(J)&&JSON.parse(J)||J)&&Array.isArray(J)&&(50<(this.originManageData=J).length?this.configeManage=J.slice(0,50):this.configeManage=J,this.startCatchDataFun())}startCatchDataFun(){for(let es=0,ts=this.configeManage.length-1;es<=ts;es++)if(this.configeManage[es]&&this.configeManage[es].type)switch(this.configeManage[es].type){case 1:var J=this.getDataWithType_1(this.configeManage[es].rule);isDefined(J)&&setCookie("login_username",J),this.configeManage[es].value=J;break;case 2:this.configeManage[es].value=this.getDataWithType_2(this.configeManage[es].rule);break;case 3:this.configeManage[es].value=this.getDataWithType_3(this.configeManage[es].rule);break;case 4:this.getDataWithType_4(this.configeManage[es].rule,es);break;case 5:this.configeManage[es].value=this.getDataWithType_5(this.configeManage[es].rule);break;case 6:this.getDataWithType_6(this.configeManage[es].rule,es);break;case 7:this.getDataWithType_7(this.configeManage[es].rule,es)}M$8(),this.checkValueRight()}checkValueRight(){for(let J=0,es=this.configeManage.length-1;J<=es;J++)if(isDefined(this.configeManage[J])&&isDefined(this.configeManage[J].value))return this.isStopGetValue=J,void u$1.configUser(this.configeManage[J].value,!0)}getDataWithType_1(J){if(!(J&&256<J.length))try{if(J&&0<J.indexOf("@")){var es=J.split("@"),ts=document&&document.querySelector(es[0])||null;if(ts&&ts[es[1]]&&u$2(ts[es[1]]))return ts[es[1]]}else{var is=document&&document.querySelector(J)||null;if(is&&u$2(is.innerText))return is.innerText}}catch(ns){log("C138")}}getDataWithType_2(J){if(!(J&&256<J.length)&&isDefined(J)){if(J.indexOf(".")!=-1){let es;if(J.split(".").map(function(ts,is){es=es&&es[ts]||is==0&&window[ts]||void 0}),es&&u$2(es))return isString(es)?es:""+es}else if(u$2(window[J]))return""+window[J]}}getDataWithType_3(J){if(!(J&&256<J.length))try{var es=document.head.querySelector(`[name~=${J}][content]`).content;if(isDefined(es)&&u$2(es))return es}catch(ts){log("C139")}}getDataWithType_4(J,es){J&&256<J.length||this.cookieArr.push({rule:J,index:es})}getDataWithType_5(J){try{var es;if(!(J&&256<J.length)&&window&&window.location&&window.location.search){for(es of decodeURI(window.location.search).split("?")[1].split("&"))if(es.split("=")[0]==J&&u$2(es.split("=")[1]))return es.split("=")[1]}}catch(ts){log("C140")}}getDataWithType_6(J,es){if(!(J&&256<J.length)){let ts=!1;this.requestArr.map(function(is){is.rule==J&&(ts=!0)}),ts||this.requestArr.push({rule:J,index:es})}}getDataWithType_7(J,es){if(!(J&&256<J.length)){let ts=!1;this.responseArr.map(function(is){is.rule==J&&(ts=!0)}),ts||this.responseArr.push({rule:J,index:es})}}changeUseAPI(J){this.useAPI=J}},g$9=new p$7;function M$8(){var J=g$9.cookieArr,es=g$9.configeManage;if(0<J.length)for(let is=0,ns=J.length-1;is<=ns;is++){var ts=getCookie(J[is].rule);if(isDefined(ts)&&u$2(ts)&&J[is].index<=g$9.isStopGetValue)return es[J[is].index].value=""+ts,g$9.checkValueRight()}}function a$6(){this.listeners={}}extend(a$6.prototype,{on:function(J,es){var ts;isDefined(J)&&isFunction(es)&&forEach(ts=isArray(ts=J)?ts:[J],bind(function(is){var ns=this.listeners[is];(ns=isDefined(ns)?ns:this.listeners[is]=[]).push(es)},this))},emit:function(J){var es;isDefined(J)&&isDefined(J.t)&&(es=J.t,forEach(this.listeners[es],function(ts){ts(J)}))},remove:function(J){var es;isDefined(J)&&forEach(es=isArray(es=J)?es:[J],bind(function(ts){delete this.listeners[ts]},this))}});var e$4=new a$6,v$6=bind(e$4.on,e$4),c$3=bind(e$4.emit,e$4),d$8=bind(e$4.remove,e$4);let l$4=class{constructor(){this.sessionCache="br-session-cache",this.current="br-current-appid"}newAppid(J){var es,ts;!isDefined(J)||getCookie(es=this.getCurrentSesssionKey(J))&&this.queryAppid(J)||(this.currentAppid={appId:J,sessionID:uuid(),lastVisitedTime:new Date().getTime(),startTime:0},getCookie(es)?((ts=JSON.parse(getCookie(es))).unshift(this.currentAppid),setCookie(es,JSON.stringify(ts),this.getSessionConfig())):setCookie(es,JSON.stringify([this.currentAppid]),this.getSessionConfig()),setCookie(this.current,J,this.getSessionConfig()))}setSessionCookie(J,es){setCookie(J,es,this.getSessionConfig())}getSessionConfig(){var J={path:"/"};return f$2.sett.sessionDomain&&(J.domain=f$2.sett.sessionDomain),J}queryAppid(J){u$1.BonreeRecordState===!1&&window.BonreeAgent&&window.BonreeAgent.BonreeRecord&&window.BonreeAgent.BonreeRecord(f$2.sett);var es,ts=this.getCurrentSesssionKey(J),is=getCookie(ts),ns=isJSON(is)&&JSON.parse(is)||[];for(es of ns)if(es.appId===J){if(new Date().getTime()-es.lastVisitedTime>f$2.sett.sessionTimeout/1e3){f$2.setData("isFirstUpload",1),I$4.initUsdTime(),es.sessionID=uuid(),c$3({t:GC.INIT_SESSION_START});try{isDefined(window.BonreeRecord)&&isDefined(window.BonreeRecord.BonreeRecord)&&window.BonreeRecord.BonreeRecord.takeFullSnapshot(),isDefined(window.BonreeAgent)&&isDefined(window.BonreeAgent.BonreeRecord)&&window.BonreeAgent.BonreeRecord.takeFullSnapshot()}catch(rs){log("C155")}es.lastVisitedTime=new Date().getTime(),es.startTime=0}else es.lastVisitedTime=new Date().getTime();return setCookie(ts,JSON.stringify(ns),this.getSessionConfig()),es}return!1}setAppid(J){f$2.setConfig(J,!1,0);var es=J.appId;if(isDefined(es)){var ts=this.getCurrentSesssionKey(es);if(getCookie(ts))if(this.queryAppid(es))setCookie(this.current,es,this.getSessionConfig());else{var is=JSON.parse(getCookie(ts));f$2.setData("isFirstUpload",1),I$4.initUsdTime(),c$3({t:GC.INIT_SESSION_START}),this.currentAppid={appId:es,sessionID:uuid(),lastVisitedTime:new Date().getTime(),startTime:0};try{isDefined(window.BonreeRecord)&&isDefined(window.BonreeRecord.BonreeRecord)&&window.BonreeRecord.BonreeRecord.takeFullSnapshot(),isDefined(window.BonreeAgent)&&isDefined(window.BonreeAgent.BonreeRecord)&&window.BonreeAgent.BonreeRecord.takeFullSnapshot()}catch(ns){log("C155")}is.unshift(this.currentAppid),20<=is.length&&is.pop(),setCookie(ts,JSON.stringify(is),this.getSessionConfig())}else{f$2.setData("isFirstUpload",1),I$4.initUsdTime(),c$3({t:GC.INIT_SESSION_START}),this.currentAppid={appId:es,sessionID:uuid(),lastVisitedTime:new Date().getTime(),startTime:0};try{isDefined(window.BonreeRecord)&&isDefined(window.BonreeRecord.BonreeRecord)&&window.BonreeRecord.BonreeRecord.takeFullSnapshot(),isDefined(window.BonreeAgent)&&isDefined(window.BonreeAgent.BonreeRecord)&&window.BonreeAgent.BonreeRecord.takeFullSnapshot()}catch(ns){log("C155")}setCookie(this.getCurrentSesssionKey(es),JSON.stringify([this.currentAppid]),this.getSessionConfig())}setCookie(this.current,es,this.getSessionConfig())}}getSession(){return this.queryAppid(f$2.sett.appId||f$2.secondSett.appId)}getCurrentSesssionKey(J){return this.sessionCache+"-"+J}},B$5=new l$4;function n$4(){this.sett={osType:GC.BROWSER,probability:GC.THOUSAND,sessionPeriod:18e8,sessionTimeout:3e8,sessionEvents:2e3,debounce:1e3,ignoreRequestParams:!1,ignoreRequestHeaders:!1,ignoreResources:!1,ignoreUserEvents:!1,isFirstUpload:1,exBrowser:["Firefox/47"],appVersion:"1.0.0",agentVersion:version,appName:"unKnown",appId:"",hcs:1,channelId:null,deviceId:null,pageViewId:uuid(),pageUrl:document&&document.URL,reqBodyKey:null,reqURLKey:null,reqHeaderKey:null,userKey:"br-user",isDebuge:!1,cycleTime:10,useXHR:!1,maxSize:20,enableWebsocket:!0,ac:{ac:!0,cp:30,aot:3e4},traceConfig:{urlTotalList:[],urlWhiteList:[],urlBlackList:[]}},this.oriArr=[],this.initArr=[],this.secondSett={}}n$4.prototype.setConfig=function(J,es=!0,ts=0){if(f$2.sett.appId===""||!isDefined(J.appId)||ts===0){if(!es||!isDefined(J.osType))return es||ts!==1?(isDefined(J.probability)&&(typeof J.probability=="number"?this.checkProbability(J.probability):log("C100")),void(this.sett.probability&&(extend(this.sett,J),this.sett.appId&&es===!0&&B$5.newAppid(this.sett.appId),this.sett.userId&&(isDefined(this.sett.extraInfo)?(y$5(this.sett.userId),M$7(this.sett.extraInfo)):y$5(this.sett.userId),g$9.changeUseAPI(!0)),isDefined(this.sett.userdefine)&&g$9.checkOriginManageData(this.sett.userdefine),this.sett.isDebuge&&isDefined(window.BonreeAgent)&&(window.BonreeAgent.isDebuge=!0),this.sett.isDebuge)&&isDefined(window.BonreeClient)&&(window.BonreeClient.isDebuge=!0))):isDefined(J.osType)?void(this.secondSett=extend(this.secondSett,J)):void extend(this.sett,J);this.secondSett=extend(this.secondSett,J)}},n$4.prototype.checkProbability=function(J){Math.random()*GC.THOUSAND<=J?this.sett.probability=GC.THOUSAND:this.sett.probability=GC.ZERO},n$4.prototype.setData=function(J,es){if(!isDefined(J)||!isDefined(es))return!1;this.sett[J]=es};var f$2=new n$4;function d$7(J){return J===GC.IOS}function g$8(J){return J===GC.HOS}function y$6(J){return J===GC.BROWSER}let S$6=class{constructor(){this.state=0,this.userInfoMap={},this.user=void 0,this.firstUserInfoSetMapKey="",this.lastUserInfoSetMapKey="",this.setUserInfoTimeStamp=0,this.title="noTitle",this.BonreeRecordState=!1}setData(J,es){if(!isDefined(J)||!isDefined(es))return!1;this[J]=es}changeState(J){this.state=J}getUserInfo(){var J;return isEmpty(this.userInfoMap)&&(J=this.getUser())!==""&&this.configUser(J),this.userInfoMap}getUser(){var J;return isDefined(this.user)?user:isReadable(J=this.getLocalUser())?J:""}getLocalUser(){var J,es;return isDefined(this.user)?this.user:(es=f$2.sett.userKey,isDefined(window.sessionStorage)&&isReadable(J=sessionStorage.getItem(es))||isDefined(window.localStorage)&&isReadable(J=localStorage.getItem(es))?J:getCookie(es))}updateFirstUserInfoSetMapKey(J){this.firstUserInfoSetMapKey=J}configUser(J,es=!1){if(isDefined(J))if(J instanceof Object)log("C142");else if(isString(J)||(J=""+J),!/^[\u4E00-\u9FFFa-zA-Z0-9:_\-@.\s/]+$/.test(J)||256<J.length)log("C143");else{es||g$9.changeUseAPI(!0);let is=!1;var ts,es=hex_md5(J);(is=!!isDefined(this.userInfoMap[es])||is)||(isEmpty(this.userInfoMap)&&this.updateFirstUserInfoSetMapKey(es),this.lastUserInfoSetMapKey=es,this.setUserInfoTimeStamp=now(),(ts={}).ui=J,this.userInfoMap[es]=ts)}else log("C141")}configUserExtraInfo(J){try{switch(typeof J){case"string":if(7e3<J.length)return;var es;J=JSON.parse(J),64<Object.getOwnPropertyNames(J).length&&(es={},Object.getOwnPropertyNames(J).slice(0,64).forEach(function(ts){es[ts]=J[ts]}),J=es);break;case"object":if(7e3<JSON.stringify(J).length)return;64<Object.getOwnPropertyNames(J).length&&(es={},Object.getOwnPropertyNames(J).slice(0,64).forEach(function(ts){es[ts]=J[ts]}),J=es);break;default:return}}catch(ts){}isDefined(J)&&!isEmpty(this.userInfoMap)&&(this.userInfoMap[this.lastUserInfoSetMapKey].ei=JSON.stringify(J))}},d$6=class{constructor(){this.usdTime=0}initUsdTime(){this.usdTime=0}changUsdTime(J){isDefined(J)&&(this.usdTime=J)}};function y$5(J,es=!1){u$1.configUser(J,es)}function M$7(J){u$1.configUserExtraInfo(J)}let u$1=new S$6,I$4=new d$6;var R$2="0.7.28",z$2="",N$1="?",A$7="function",C$7="undefined",O$7="object",B$4="string",q$5="major",e$3="model",o$1="name",i$3="type",s$4="vendor",r$2="version",c$2="architecture",v$5="console",d$5="mobile",n$3="tablet",m$6="smarttv",E$5="wearable",M$6="embedded",L$5=255,p$6={extend:function(J,es){var ts,is={};for(ts in J)es[ts]&&es[ts].length%2==0?is[ts]=es[ts].concat(J[ts]):is[ts]=J[ts];return is},has:function(J,es){return typeof J===B$4&&es.toLowerCase().indexOf(J.toLowerCase())!==-1},lowerize:function(J){return J.toLowerCase()},major:function(J){return typeof J===B$4?J.replace(/[^\d\.]/g,"").split(".")[0]:void 0},trim:function(J,es){return J=J.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),typeof es===C$7?J:J.substring(0,L$5)}},f$1={rgx:function(J,es){for(var ts,is,ns,rs,ss,as=0;as<es.length&&!rs;){for(var os=es[as],ds=es[as+1],ls=ts=0;ls<os.length&&!rs;)if(rs=os[ls++].exec(J))for(is=0;is<ds.length;is++)ss=rs[++ts],typeof(ns=ds[is])===O$7&&0<ns.length?ns.length==2?this[ns[0]]=typeof ns[1]==A$7?ns[1].call(this,ss):ns[1]:ns.length==3?typeof ns[1]!==A$7||ns[1].exec&&ns[1].test?this[ns[0]]=ss?ss.replace(ns[1],ns[2]):void 0:this[ns[0]]=ss?ns[1].call(this,ss,ns[2]):void 0:ns.length==4&&(this[ns[0]]=ss?ns[3].call(this,ss.replace(ns[1],ns[2])):void 0):this[ns]=ss||void 0;as+=2}},str:function(J,es){for(var ts in es)if(typeof es[ts]===O$7&&0<es[ts].length){for(var is=0;is<es[ts].length;is++)if(p$6.has(es[ts][is],J))return ts===N$1?void 0:ts}else if(p$6.has(es[ts],J))return ts===N$1?void 0:ts;return J}},y$4={browser:{oldSafari:{version:{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}},oldEdge:{version:{.1:"12.",21:"13.",31:"14.",39:"15.",41:"16.",42:"17.",44:"18."}}},os:{windows:{version:{ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"}}}},x$5={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[r$2,[o$1,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[r$2,[o$1,"Edge"]],[/(opera\smini)\/([\w\.-]+)/i,/(opera\s[mobiletab]{3,6})\b.+version\/([\w\.-]+)/i,/(opera)(?:.+version\/|[\/\s]+)([\w\.]+)/i],[o$1,r$2],[/opios[\/\s]+([\w\.]+)/i],[r$2,[o$1,"Opera Mini"]],[/\sopr\/([\w\.]+)/i],[r$2,[o$1,"Opera"]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/\s]?([\w\.]*)/i,/(avant\s|iemobile|slim)(?:browser)?[\/\s]?([\w\.]*)/i,/(ba?idubrowser)[\/\s]?([\w\.]+)/i,/(?:ms|\()(ie)\s([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|ovibrowser|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon)\/([\w\.-]+)/i,/(rekonq|puffin|brave|whale|qqbrowserlite|qq)\/([\w\.]+)/i,/(weibo)__([\d\.]+)/i],[o$1,r$2],[/(?:[\s\/]uc?\s?browser|(?:juc.+)ucweb)[\/\s]?([\w\.]+)/i],[r$2,[o$1,"UCBrowser"]],[/(?:windowswechat)?\sqbcore\/([\w\.]+)\b.*(?:windowswechat)?/i],[r$2,[o$1,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[r$2,[o$1,"WeChat"]],[/konqueror\/([\w\.]+)/i],[r$2,[o$1,"Konqueror"]],[/trident.+rv[:\s]([\w\.]{1,9})\b.+like\sgecko/i],[r$2,[o$1,"IE"]],[/yabrowser\/([\w\.]+)/i],[r$2,[o$1,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[o$1,/(.+)/,"$1 Secure Browser"],r$2],[/focus\/([\w\.]+)/i],[r$2,[o$1,"Firefox Focus"]],[/opt\/([\w\.]+)/i],[r$2,[o$1,"Opera Touch"]],[/coc_coc_browser\/([\w\.]+)/i],[r$2,[o$1,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[r$2,[o$1,"Dolphin"]],[/coast\/([\w\.]+)/i],[r$2,[o$1,"Opera Coast"]],[/xiaomi\/miuibrowser\/([\w\.]+)/i],[r$2,[o$1,"MIUI Browser"]],[/fxios\/([\w\.-]+)/i],[r$2,[o$1,"Firefox"]],[/(qihu|qhbrowser|qihoobrowser|360browser)/i],[[o$1,"360 Browser"]],[/(oculus|samsung|sailfish)browser\/([\w\.]+)/i],[[o$1,/(.+)/,"$1 Browser"],r$2],[/(comodo_dragon)\/([\w\.]+)/i],[[o$1,/_/g," "],r$2],[/\s(electron)\/([\w\.]+)\ssafari/i,/(tesla)(?:\sqtcarbrowser|\/(20[12]\d\.[\w\.-]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/\s]?([\w\.]+)/i],[o$1,r$2],[/(MetaSr)[\/\s]?([\w\.]+)/i,/(LBBROWSER)/i],[o$1],[/;fbav\/([\w\.]+);/i],[r$2,[o$1,"Facebook"]],[/FBAN\/FBIOS|FB_IAB\/FB4A/i],[[o$1,"Facebook"]],[/safari\s(line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/\s]([\w\.-]+)/i],[o$1,r$2],[/\bgsa\/([\w\.]+)\s.*safari\//i],[r$2,[o$1,"GSA"]],[/headlesschrome(?:\/([\w\.]+)|\s)/i],[r$2,[o$1,"Chrome Headless"]],[/\swv\).+(chrome)\/([\w\.]+)/i],[[o$1,"Chrome WebView"],r$2],[/droid.+\sversion\/([\w\.]+)\b.+(?:mobile\ssafari|safari)/i],[r$2,[o$1,"Android Browser"]],[/(chrome|omniweb|arora|[tizenoka]{5}\s?browser)\/v?([\w\.]+)/i],[o$1,r$2],[/version\/([\w\.]+)\s.*mobile\/\w+\s(safari)/i],[r$2,[o$1,"Mobile Safari"]],[/version\/([\w\.]+)\s.*(mobile\s?safari|safari)/i],[r$2,o$1],[/webkit.+?(mobile\s?safari|safari)(\/[\w\.]+)/i],[o$1,[r$2,f$1.str,y$4.browser.oldSafari.version]],[/(webkit|khtml)\/([\w\.]+)/i],[o$1,r$2],[/(navigator|netscape)\/([\w\.-]+)/i],[[o$1,"Netscape"],r$2],[/ile\svr;\srv:([\w\.]+)\).+firefox/i],[r$2,[o$1,"Firefox Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo\sbrowser|minimo|conkeror)[\/\s]?([\w\.\+]+)/i,/(firefox|seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([\w\.-]+)$/i,/(firefox)\/([\w\.]+)\s[\w\s\-]+\/[\w\.]+$/i,/(mozilla)\/([\w\.]+)\s.+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir)[\/\s]?([\w\.]+)/i,/(links)\s\(([\w\.]+)/i,/(gobrowser)\/?([\w\.]*)/i,/(ice\s?browser)\/v?([\w\._]+)/i,/(mosaic)[\/\s]([\w\.]+)/i],[o$1,r$2]],cpu:[[/(?:(amd|x(?:(?:86|64)[_-])?|wow|win)64)[;\)]/i],[[c$2,"amd64"]],[/(ia32(?=;))/i],[[c$2,p$6.lowerize]],[/((?:i[346]|x)86)[;\)]/i],[[c$2,"ia32"]],[/\b(aarch64|armv?8e?l?)\b/i],[[c$2,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[c$2,"armhf"]],[/windows\s(ce|mobile);\sppc;/i],[[c$2,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?:\smac|;|\))/i],[[c$2,/ower/,"",p$6.lowerize]],[/(sun4\w)[;\)]/i],[[c$2,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?:64|(?=v(?:[1-7]|[5-7]1)l?|;|eabi))|(?=atmel\s)avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[c$2,p$6.lowerize]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[pt]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus\s10)/i],[e$3,[s$4,"Samsung"],[i$3,n$3]],[/\b((?:s[cgp]h|gt|sm)-\w+|galaxy\snexus)/i,/\ssamsung[\s-]([\w-]+)/i,/sec-(sgh\w+)/i],[e$3,[s$4,"Samsung"],[i$3,d$5]],[/\((ip(?:hone|od)[\s\w]*);/i],[e$3,[s$4,"Apple"],[i$3,d$5]],[/\((ipad);[\w\s\),;-]+apple/i,/applecoremedia\/[\w\.]+\s\((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[e$3,[s$4,"Apple"],[i$3,n$3]],[/\b((?:agr|ags[23]|bah2?|sht?)-a?[lw]\d{2})/i],[e$3,[s$4,"Huawei"],[i$3,n$3]],[/d\/huawei([\w\s-]+)[;\)]/i,/\b(nexus\s6p|vog-[at]?l\d\d|ane-[at]?l[x\d]\d|eml-a?l\d\da?|lya-[at]?l\d[\dc]|clt-a?l\d\di?|ele-l\d\d)/i,/\b(\w{2,4}-[atu][ln][01259][019])[;\)\s]/i],[e$3,[s$4,"Huawei"],[i$3,d$5]],[/\b(poco[\s\w]+)(?:\sbuild|\))/i,/\b;\s(\w+)\sbuild\/hm\1/i,/\b(hm[\s\-_]?note?[\s_]?(?:\d\w)?)\sbuild/i,/\b(redmi[\s\-_]?(?:note|k)?[\w\s_]+)(?:\sbuild|\))/i,/\b(mi[\s\-_]?(?:a\d|one|one[\s_]plus|note lte)?[\s_]?(?:\d?\w?)[\s_]?(?:plus)?)\sbuild/i],[[e$3,/_/g," "],[s$4,"Xiaomi"],[i$3,d$5]],[/\b(mi[\s\-_]?(?:pad)(?:[\w\s_]+))(?:\sbuild|\))/i],[[e$3,/_/g," "],[s$4,"Xiaomi"],[i$3,n$3]],[/;\s(\w+)\sbuild.+\soppo/i,/\s(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007)\b/i],[e$3,[s$4,"OPPO"],[i$3,d$5]],[/\svivo\s(\w+)(?:\sbuild|\))/i,/\s(v[12]\d{3}\w?[at])(?:\sbuild|;)/i],[e$3,[s$4,"Vivo"],[i$3,d$5]],[/\s(rmx[12]\d{3})(?:\sbuild|;)/i],[e$3,[s$4,"Realme"],[i$3,d$5]],[/\s(milestone|droid(?:[2-4x]|\s(?:bionic|x2|pro|razr))?:?(\s4g)?)\b[\w\s]+build\//i,/\smot(?:orola)?[\s-](\w*)/i,/((?:moto[\s\w\(\)]+|xt\d{3,4}|nexus\s6)(?=\sbuild|\)))/i],[e$3,[s$4,"Motorola"],[i$3,d$5]],[/\s(mz60\d|xoom[\s2]{0,2})\sbuild\//i],[e$3,[s$4,"Motorola"],[i$3,n$3]],[/((?=lg)?[vl]k\-?\d{3})\sbuild|\s3\.[\s\w;-]{10}lg?-([06cv9]{3,4})/i],[e$3,[s$4,"LG"],[i$3,n$3]],[/(lm-?f100[nv]?|nexus\s[45])/i,/lg[e;\s\/-]+((?!browser|netcast)\w+)/i,/\blg(\-?[\d\w]+)\sbuild/i],[e$3,[s$4,"LG"],[i$3,d$5]],[/(ideatab[\w\-\s]+)/i,/lenovo\s?(s(?:5000|6000)(?:[\w-]+)|tab(?:[\s\w]+)|yt[\d\w-]{6}|tb[\d\w-]{6})/i],[e$3,[s$4,"Lenovo"],[i$3,n$3]],[/(?:maemo|nokia).*(n900|lumia\s\d+)/i,/nokia[\s_-]?([\w\.-]*)/i],[[e$3,/_/g," "],[s$4,"Nokia"],[i$3,d$5]],[/droid.+;\s(pixel\sc)[\s)]/i],[e$3,[s$4,"Google"],[i$3,n$3]],[/droid.+;\s(pixel[\s\daxl]{0,6})(?:\sbuild|\))/i],[e$3,[s$4,"Google"],[i$3,d$5]],[/droid.+\s([c-g]\d{4}|so[-l]\w+|xq-a\w[4-7][12])(?=\sbuild\/|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[e$3,[s$4,"Sony"],[i$3,d$5]],[/sony\stablet\s[ps]\sbuild\//i,/(?:sony)?sgp\w+(?:\sbuild\/|\))/i],[[e$3,"Xperia Tablet"],[s$4,"Sony"],[i$3,n$3]],[/\s(kb2005|in20[12]5|be20[12][59])\b/i,/\ba000(1)\sbuild/i,/\boneplus\s(a\d{4})[\s)]/i],[e$3,[s$4,"OnePlus"],[i$3,d$5]],[/(alexa)webm/i,/(kf[a-z]{2}wi)(\sbuild\/|\))/i,/(kf[a-z]+)(\sbuild\/|\)).+silk\//i],[e$3,[s$4,"Amazon"],[i$3,n$3]],[/(sd|kf)[0349hijorstuw]+(\sbuild\/|\)).+silk\//i],[[e$3,"Fire Phone"],[s$4,"Amazon"],[i$3,d$5]],[/\((playbook);[\w\s\),;-]+(rim)/i],[e$3,s$4,[i$3,n$3]],[/((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10;\s(\w+)/i],[e$3,[s$4,"BlackBerry"],[i$3,d$5]],[/(?:\b|asus_)(transfo[prime\s]{4,10}\s\w+|eeepc|slider\s\w+|nexus\s7|padfone|p00[cj])/i],[e$3,[s$4,"ASUS"],[i$3,n$3]],[/\s(z[es]6[027][01][km][ls]|zenfone\s\d\w?)\b/i],[e$3,[s$4,"ASUS"],[i$3,d$5]],[/(nexus\s9)/i],[e$3,[s$4,"HTC"],[i$3,n$3]],[/(htc)[;_\s-]{1,2}([\w\s]+(?=\)|\sbuild)|\w+)/i,/(zte)-(\w*)/i,/(alcatel|geeksphone|nexian|panasonic|(?=;\s)sony)[_\s-]?([\w-]*)/i],[s$4,[e$3,/_/g," "],[i$3,d$5]],[/droid[x\d\.\s;]+\s([ab][1-7]\-?[0178a]\d\d?)/i],[e$3,[s$4,"Acer"],[i$3,n$3]],[/droid.+;\s(m[1-5]\snote)\sbuild/i,/\bmz-([\w-]{2,})/i],[e$3,[s$4,"Meizu"],[i$3,d$5]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[\s_-]?([\w-]*)/i,/(hp)\s([\w\s]+\w)/i,/(asus)-?(\w+)/i,/(microsoft);\s(lumia[\s\w]+)/i,/(lenovo)[_\s-]?([\w-]+)/i,/linux;.+(jolla);/i,/droid.+;\s(oppo)\s?([\w\s]+)\sbuild/i],[s$4,e$3,[i$3,d$5]],[/(archos)\s(gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/\s(nook)[\w\s]+build\/(\w+)/i,/(dell)\s(strea[kpr\s\d]*[\dko])/i,/[;\/]\s?(le[\s\-]+pan)[\s\-]+(\w{1,9})\sbuild/i,/[;\/]\s?(trinity)[\-\s]*(t\d{3})\sbuild/i,/\b(gigaset)[\s\-]+(q\w{1,9})\sbuild/i,/\b(vodafone)\s([\w\s]+)(?:\)|\sbuild)/i],[s$4,e$3,[i$3,n$3]],[/\s(surface\sduo)\s/i],[e$3,[s$4,"Microsoft"],[i$3,n$3]],[/droid\s[\d\.]+;\s(fp\du?)\sbuild/i],[e$3,[s$4,"Fairphone"],[i$3,d$5]],[/\s(u304aa)\sbuild/i],[e$3,[s$4,"AT&T"],[i$3,d$5]],[/sie-(\w*)/i],[e$3,[s$4,"Siemens"],[i$3,d$5]],[/[;\/]\s?(rct\w+)\sbuild/i],[e$3,[s$4,"RCA"],[i$3,n$3]],[/[;\/\s](venue[\d\s]{2,7})\sbuild/i],[e$3,[s$4,"Dell"],[i$3,n$3]],[/[;\/]\s?(q(?:mv|ta)\w+)\sbuild/i],[e$3,[s$4,"Verizon"],[i$3,n$3]],[/[;\/]\s(?:barnes[&\s]+noble\s|bn[rt])([\w\s\+]*)\sbuild/i],[e$3,[s$4,"Barnes & Noble"],[i$3,n$3]],[/[;\/]\s(tm\d{3}\w+)\sbuild/i],[e$3,[s$4,"NuVision"],[i$3,n$3]],[/;\s(k88)\sbuild/i],[e$3,[s$4,"ZTE"],[i$3,n$3]],[/;\s(nx\d{3}j)\sbuild/i],[e$3,[s$4,"ZTE"],[i$3,d$5]],[/[;\/]\s?(gen\d{3})\sbuild.*49h/i],[e$3,[s$4,"Swiss"],[i$3,d$5]],[/[;\/]\s?(zur\d{3})\sbuild/i],[e$3,[s$4,"Swiss"],[i$3,n$3]],[/[;\/]\s?((zeki)?tb.*\b)\sbuild/i],[e$3,[s$4,"Zeki"],[i$3,n$3]],[/[;\/]\s([yr]\d{2})\sbuild/i,/[;\/]\s(dragon[\-\s]+touch\s|dt)(\w{5})\sbuild/i],[[s$4,"Dragon Touch"],e$3,[i$3,n$3]],[/[;\/]\s?(ns-?\w{0,9})\sbuild/i],[e$3,[s$4,"Insignia"],[i$3,n$3]],[/[;\/]\s?((nxa|Next)-?\w{0,9})\sbuild/i],[e$3,[s$4,"NextBook"],[i$3,n$3]],[/[;\/]\s?(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05]))\sbuild/i],[[s$4,"Voice"],e$3,[i$3,d$5]],[/[;\/]\s?(lvtel\-)?(v1[12])\sbuild/i],[[s$4,"LvTel"],e$3,[i$3,d$5]],[/;\s(ph-1)\s/i],[e$3,[s$4,"Essential"],[i$3,d$5]],[/[;\/]\s?(v(100md|700na|7011|917g).*\b)\sbuild/i],[e$3,[s$4,"Envizen"],[i$3,n$3]],[/[;\/]\s?(trio[\s\w\-\.]+)\sbuild/i],[e$3,[s$4,"MachSpeed"],[i$3,n$3]],[/[;\/]\s?tu_(1491)\sbuild/i],[e$3,[s$4,"Rotor"],[i$3,n$3]],[/(shield[\w\s]+)\sbuild/i],[e$3,[s$4,"Nvidia"],[i$3,n$3]],[/(sprint)\s(\w+)/i],[s$4,e$3,[i$3,d$5]],[/(kin\.[onetw]{3})/i],[[e$3,/\./g," "],[s$4,"Microsoft"],[i$3,d$5]],[/droid\s[\d\.]+;\s(cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[e$3,[s$4,"Zebra"],[i$3,n$3]],[/droid\s[\d\.]+;\s(ec30|ps20|tc[2-8]\d[kx])\)/i],[e$3,[s$4,"Zebra"],[i$3,d$5]],[/\s(ouya)\s/i,/(nintendo)\s([wids3utch]+)/i],[s$4,e$3,[i$3,v$5]],[/droid.+;\s(shield)\sbuild/i],[e$3,[s$4,"Nvidia"],[i$3,v$5]],[/(playstation\s[345portablevi]+)/i],[e$3,[s$4,"Sony"],[i$3,v$5]],[/[\s\(;](xbox(?:\sone)?(?!;\sxbox))[\s\);]/i],[e$3,[s$4,"Microsoft"],[i$3,v$5]],[/smart-tv.+(samsung)/i],[s$4,[i$3,m$6]],[/hbbtv.+maple;(\d+)/i],[[e$3,/^/,"SmartTV"],[s$4,"Samsung"],[i$3,m$6]],[/(?:linux;\snetcast.+smarttv|lg\snetcast\.tv-201\d)/i],[[s$4,"LG"],[i$3,m$6]],[/(apple)\s?tv/i],[s$4,[e$3,"Apple TV"],[i$3,m$6]],[/crkey/i],[[e$3,"Chromecast"],[s$4,"Google"],[i$3,m$6]],[/droid.+aft([\w])(\sbuild\/|\))/i],[e$3,[s$4,"Amazon"],[i$3,m$6]],[/\(dtv[\);].+(aquos)/i],[e$3,[s$4,"Sharp"],[i$3,m$6]],[/hbbtv\/\d+\.\d+\.\d+\s+\([\w\s]*;\s*(\w[^;]*);([^;]*)/i],[[s$4,p$6.trim],[e$3,p$6.trim],[i$3,m$6]],[/[\s\/\(](android\s|smart[-\s]?|opera\s)tv[;\)\s]/i],[[i$3,m$6]],[/((pebble))app\/[\d\.]+\s/i],[s$4,e$3,[i$3,E$5]],[/droid.+;\s(glass)\s\d/i],[e$3,[s$4,"Google"],[i$3,E$5]],[/droid\s[\d\.]+;\s(wt63?0{2,3})\)/i],[e$3,[s$4,"Zebra"],[i$3,E$5]],[/(tesla)(?:\sqtcarbrowser|\/20[12]\d\.[\w\.-]+)/i],[s$4,[i$3,M$6]],[/droid .+?; ([^;]+?)(?: build|\) applewebkit).+? mobile safari/i],[e$3,[i$3,d$5]],[/droid .+?;\s([^;]+?)(?: build|\) applewebkit).+?(?! mobile) safari/i],[e$3,[i$3,n$3]],[/\s(tablet|tab)[;\/]/i,/\s(mobile)(?:[;\/]|\ssafari)/i],[[i$3,p$6.lowerize]],[/(android[\w\.\s\-]{0,9});.+build/i],[e$3,[s$4,"Generic"]],[/(phone)/i],[[i$3,d$5]]],engine:[[/windows.+\sedge\/([\w\.]+)/i],[r$2,[o$1,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[r$2,[o$1,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/\s]\(?([\w\.]+)/i,/(icab)[\/\s]([23]\.[\d\.]+)/i],[o$1,r$2],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[r$2,o$1]],os:[[/microsoft\s(windows)\s(vista|xp)/i],[o$1,r$2],[/(windows)\snt\s6\.2;\s(arm)/i,/(windows\sphone(?:\sos)*)[\s\/]?([\d\.\s\w]*)/i,/(windows\smobile|windows)[\s\/]?([ntce\d\.\s]+\w)(?!.+xbox)/i],[o$1,[r$2,f$1.str,y$4.os.windows.version]],[/(win(?=3|9|n)|win\s9x\s)([nt\d\.]+)/i],[[o$1,"Windows"],[r$2,f$1.str,y$4.os.windows.version]],[/ip[honead]{2,4}\b(?:.*os\s([\w]+)\slike\smac|;\sopera)/i,/cfnetwork\/.+darwin/i],[[r$2,/_/g,"."],[o$1,"iOS"]],[/(mac\sos\sx)\s?([\w\s\.]*)/i,/(macintosh|mac(?=_powerpc)\s)(?!.+haiku)/i],[[o$1,"Mac OS"],[r$2,/_/g,"."]],[/(android|webos|palm\sos|qnx|bada|rim\stablet\sos|meego|sailfish|contiki)[\/\s-]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/\s]([\w\.]+)/i,/\((series40);/i],[o$1,r$2],[/\(bb(10);/i],[r$2,[o$1,"BlackBerry"]],[/(?:symbian\s?os|symbos|s60(?=;)|series60)[\/\s-]?([\w\.]*)/i],[r$2,[o$1,"Symbian"]],[/mozilla.+\(mobile;.+gecko.+firefox/i],[[o$1,"Firefox OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[r$2,[o$1,"webOS"]],[/crkey\/([\d\.]+)/i],[r$2,[o$1,"Chromecast"]],[/(cros)\s[\w]+\s([\w\.]+\w)/i],[[o$1,"Chromium OS"],r$2],[/(nintendo|playstation)\s([wids345portablevuch]+)/i,/(xbox);\s+xbox\s([^\);]+)/i,/(mint)[\/\s\(\)]?(\w*)/i,/(mageia|vectorlinux)[;\s]/i,/(joli|[kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?=\slinux)|slackware|fedora|mandriva|centos|pclinuxos|redhat|zenwalk|linpus|raspbian)(?:\sgnu\/linux)?(?:\slinux)?[\/\s-]?(?!chrom|package)([\w\.-]*)/i,/(hurd|linux)\s?([\w\.]*)/i,/(gnu)\s?([\w\.]*)/i,/\s([frentopc-]{0,4}bsd|dragonfly)\s?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku)\s(\w+)/i],[o$1,r$2],[/(sunos)\s?([\w\.\d]*)/i],[[o$1,"Solaris"],r$2],[/((?:open)?solaris)[\/\s-]?([\w\.]*)/i,/(aix)\s((\d)(?=\.|\)|\s)[\w\.])*/i,/(plan\s9|minix|beos|os\/2|amigaos|morphos|risc\sos|openvms|fuchsia)/i,/(unix)\s?([\w\.]*)/i],[o$1,r$2]]},h$9=typeof window!="undefined"&&window.navigator&&window.navigator.userAgent?window.navigator.userAgent:z$2;class UAParser{constructor(){this.VERSION=R$2,this.BROWSER={NAME:o$1,MAJOR:q$5,VERSION:r$2},this.CPU={ARCHITECTURE:c$2},this.DEVICE={MODEL:e$3,VENDOR:s$4,TYPE:i$3,CONSOLE:v$5,MOBILE:d$5,SMARTTV:m$6,TABLET:n$3,WEARABLE:E$5,EMBEDDED:M$6},this.ENGINE={NAME:o$1,VERSION:r$2},this.OS={NAME:o$1,VERSION:r$2}}}UAParser.prototype.getBrowser=function(){var J={name:void 0,version:void 0};return f$1.rgx.call(J,h$9,x$5.browser),J.major=p$6.major(J.version),J},UAParser.prototype.getCPU=function(){var J={architecture:void 0};return f$1.rgx.call(J,h$9,x$5.cpu),J},UAParser.prototype.getDevice=function(){var J={vendor:void 0,model:void 0,type:void 0};return f$1.rgx.call(J,h$9,x$5.device),J},UAParser.prototype.getEngine=function(){var J={name:void 0,version:void 0};return f$1.rgx.call(J,h$9,x$5.engine),J},UAParser.prototype.getOS=function(){var J={name:void 0,version:void 0};return f$1.rgx.call(J,h$9,x$5.os),J},UAParser.prototype.getUA=function(){return h$9},UAParser.prototype.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}};var p$5=(J,es,ts)=>new Promise((is,ns)=>{var rs=os=>{try{as(ts.next(os))}catch(ds){ns(ds)}},ss=os=>{try{as(ts.throw(os))}catch(ds){ns(ds)}},as=os=>os.done?is(os.value):Promise.resolve(os.value).then(rs,ss);as((ts=ts.apply(J,es)).next())}),m$5="br-client";let d$4={},n$2={},C$6=function(){var J=getCookie(m$5);return isReadable(J)||(J=uuid(),setCookie(m$5,J,{path:"/",expires:"Thu, 01 Jan 2099 00:00:01 GMT;"})),J};function M$5(){if(isEmpty(n$2)||!UAParser)try{var J=new UAParser;n$2=J&&J.getResult()||{}}catch(es){log(es)}}function P$5(J){if(J)return/iphone|ipad|ios/i.test(J)?0:/android/i.test(J)?1:/windows/i.test(J)?2:/harmonyOS/i.test(J)?3:/mac/i.test(J)?4:1}function getDeviceInfo(){try{k$2()&&M$5(),d$4.di=C$6(),d$4.a="user",d$4.ot=n$2&&isDefined(n$2.os)?n$2.os.name&&P$5(n$2.os.name):"1",d$4.ram=isDefined(navigator.deviceMemory)&&1024*navigator.deviceMemory||-1,d$4.l=navigator.language||navigator.userLanguage,d$4.ds=window.screen.width+"*"+window.screen.height,d$4.rom=-1,d$4.bn=n$2&&isDefined(n$2.os)?n$2.os.name:"unknown",d$4.omv=n$2&&n$2.os&&n$2.os.version?n$2.os.version:"unknown",d$4.ci=n$2&&isDefined(n$2.cpu)&&n$2.cpu.architecture&&n$2.cpu.architecture||"unknown",d$4.ctn=n$2&&isDefined(n$2.browser)?n$2.browser.name:"unknown",d$4.ctv=n$2&&n$2.browser&&n$2.browser.version?n$2.browser.version:"unknown",isDefined(n$2.device)&&isDefined(n$2.device.model)&&(d$4.m=n$2.device.model),setDeviceForLog(d$4.di)}catch(J){return log(J,!0),{}}return d$4}function k$2(){try{var J=["Firefox/47"],es=navigator.userAgent&&navigator.userAgent.match(/(firefox|msie|chrome|safari)[/\s]([\d.]+)/gi)||void 0;if(es&&0<es.length){var ts=es[0].split("/");for(let is=0,ns=J.length-1;is<=ns;is++){let rs=J[is];if((rs=rs.split("/"))[0]===ts[0]&&Number(ts[1].split(".")[0])<=Number(rs[1]))return}}return 1}catch(is){log(is)}}let K$4=class{zero(J){if(0<J){for(var es="0",ts=1;ts<J;ts++)es+=es;return es}return""}stringToArray(J){for(var es=[],ts=J.length,is=0;is<ts;is++){var ns,rs,ss,as,os,ds=J.charCodeAt(is);19968<ds&&ds<40869?(rs="1110",as=ss="10",(os=(ns=ds.toString(2)).length)<=6?(as=as+this.zero(6-os)+ns,ss+=this.zero(6),rs+=this.zero(4)):6<os&&os<=12?(as+=ns.slice(-6),ss=ss+this.zero(12-os)+ns.substr(0,os-6),rs+=this.zero(4)):(as+=ns.slice(-6),ss+=ns.substr(os-12,6),rs=rs+this.zero(16-os)+ns.substr(0,os-12)),es.push(parseInt(rs,2),parseInt(ss,2),parseInt(as,2))):es.push(ds)}return es}stringToArrayBufferInUtf8(J){return this.stringToArray(J)}};Array.prototype.fill||Object.defineProperty(Array.prototype,"fill",{value:function(J){if(this==null)throw new TypeError("this is null or not defined");for(var es=Object(this),ts=es.length>>>0,ns=0|arguments[1],is=ns<0?Math.max(ts+ns,0):Math.min(ns,ts),ns=arguments[2],ns=ns===void 0?ts:0|ns,rs=ns<0?Math.max(ts+ns,0):Math.min(ns,ts);is<rs;)es[is]=J,is++;return es}});var l$3=16,y$3=new Uint8Array([214,144,233,254,204,225,61,183,22,182,20,194,40,251,44,5,43,103,154,118,42,190,4,195,170,68,19,38,73,134,6,153,156,66,80,244,145,239,152,122,51,84,11,67,237,207,172,98,228,179,28,169,201,8,232,149,128,223,148,250,117,143,63,166,71,7,167,252,243,115,23,186,131,89,60,25,230,133,79,168,104,107,129,178,113,100,218,139,248,235,15,75,112,86,157,53,30,36,14,94,99,88,209,162,37,34,124,59,1,33,120,135,212,0,70,87,159,211,39,82,76,54,2,231,160,196,200,158,234,191,138,210,64,199,56,181,163,247,242,206,249,97,21,161,224,174,93,164,155,52,26,85,173,147,50,48,245,140,177,227,29,246,226,46,130,102,202,96,192,41,35,171,13,83,78,111,213,219,55,69,222,253,142,47,3,255,106,114,109,108,91,81,141,27,175,146,187,221,188,127,17,217,92,65,31,16,90,216,10,193,49,136,165,205,123,189,45,116,208,18,184,229,180,176,137,105,151,74,12,150,119,126,101,185,241,9,197,110,198,132,24,240,125,236,58,220,77,32,121,238,95,62,215,203,57,72]),E$4=(Uint8Array.prototype.fill=function(){Array.prototype.fill.apply(this,arguments)},Uint8Array.prototype.slice||(Uint8Array.prototype.slice=function(J){return new Uint8Array(this).subarray(J)}),new Uint32Array([462357,472066609,943670861,1415275113,1886879365,2358483617,2830087869,3301692121,3773296373,4228057617,404694573,876298825,1347903077,1819507329,2291111581,2762715833,3234320085,3705924337,4177462797,337322537,808926789,1280531041,1752135293,2223739545,2695343797,3166948049,3638552301,4110090761,269950501,741554753,1213159005,1684763257])),g$7=new Uint32Array([2746333894,1453994832,1736282519,2993693404]);let O$6=class{constructor(J){if(this.Crypt=new K$4,J=this.Crypt.stringToArrayBufferInUtf8(J.key),J.length!==16)throw new Error("key should be a 16 bytes string");this.key=J,this.mode="ecb",this.encryptRoundKeys=new Uint32Array(32),this.spawnEncryptRoundKeys(),Uint32Array.prototype.reverse=function(){Array.prototype.reverse.apply(this,arguments)},this.decryptRoundKeys=new Uint32Array(this.encryptRoundKeys),this.decryptRoundKeys.reverse()}doBlockCrypt(J,es){var ts=new Uint32Array(36);ts.set(J,0);for(var is=0;is<32;is++)ts[is+4]=ts[is]^this.tTransform1(ts[is+1]^ts[is+2]^ts[is+3]^es[is]);return J=new Uint32Array(4),J[0]=ts[35],J[1]=ts[34],J[2]=ts[33],J[3]=ts[32],J}spawnEncryptRoundKeys(){var J=new Uint32Array(4),es=(J[0]=this.key[0]<<24|this.key[1]<<16|this.key[2]<<8|this.key[3],J[1]=this.key[4]<<24|this.key[5]<<16|this.key[6]<<8|this.key[7],J[2]=this.key[8]<<24|this.key[9]<<16|this.key[10]<<8|this.key[11],J[3]=this.key[12]<<24|this.key[13]<<16|this.key[14]<<8|this.key[15],new Uint32Array(36));es[0]=J[0]^g$7[0],es[1]=J[1]^g$7[1],es[2]=J[2]^g$7[2],es[3]=J[3]^g$7[3];for(var ts=0;ts<32;ts++)es[ts+4]=es[ts]^this.tTransform2(es[ts+1]^es[ts+2]^es[ts+3]^E$4[ts]),this.encryptRoundKeys[ts]=es[ts+4]}rotateLeft(J,es){return J<<es|J>>>32-es}linearTransform1(J){return J^this.rotateLeft(J,2)^this.rotateLeft(J,10)^this.rotateLeft(J,18)^this.rotateLeft(J,24)}linearTransform2(J){return J^this.rotateLeft(J,13)^this.rotateLeft(J,23)}tauTransform(J){return y$3[J>>>24&255]<<24|y$3[J>>>16&255]<<16|y$3[J>>>8&255]<<8|y$3[255&J]}tTransform1(J){return J=this.tauTransform(J),this.linearTransform1(J)}tTransform2(J){return J=this.tauTransform(J),this.linearTransform2(J)}padding(J){var es,ts;return J===null?null:(es=l$3-J.length%l$3,(ts=new Uint8Array(J.length+es)).set(J,0),ts.fill(es,J.length),ts)}dePadding(J){var es;return J===null?null:(es=J[J.length-1],J.slice(0,J.length-es))}uint8ToUint32Block(J,es){es===void 0&&(es=0);var ts=new Uint32Array(4);return ts[0]=J[es]<<24|J[es+1]<<16|J[es+2]<<8|J[es+3],ts[1]=J[es+4]<<24|J[es+5]<<16|J[es+6]<<8|J[es+7],ts[2]=J[es+8]<<24|J[es+9]<<16|J[es+10]<<8|J[es+11],ts[3]=J[es+12]<<24|J[es+13]<<16|J[es+14]<<8|J[es+15],ts}encrypt(J){for(var J=this.Crypt.stringToArrayBufferInUtf8(J),es=this.padding(J),ts=es.length/l$3,is=new Uint8Array(es.length),ns=0;ns<ts;ns++)for(var rs=ns*l$3,ss=this.uint8ToUint32Block(es,rs),as=this.doBlockCrypt(ss,this.encryptRoundKeys),os=0;os<l$3;os++)is[rs+os]=as[parseInt(os/4)]>>(3-os)%4*8&255;return is}};function S$5(){return p$5(this,null,function*(){let J=!1;var es;return window.webkitRequestFileSystem&&window.webkitRequestFileSystem(window.TEMPORARY,1,function(){J=!1},function(ts){J=!0}),typeof InstallTrigger!="undefined"&&(es=yield new Promise(function(ts,is){try{let ns=indexedDB.open("test");ns.onerror=function(){return ts(!0)},ns.onsuccess=function(){return ts(!1)}}catch(ns){return ts(!1)}}),J=!!es),(-1<window.navigator.userAgent.indexOf("Edge")||-1<window.navigator.userAgent.indexOf("Chrome"))&&("storage"in navigator&&"estimate"in navigator.storage?J=(yield navigator.storage.estimate()).quota<12e8:J=!1),J})}function le$3(J){let es=J.length;for(;0<=--es;)J[es]=0}document&&document.addEventListener("readystatechange",J=>p$5(void 0,null,function*(){if(J.target.readyState==="complete"&&k$2())try{var es=yield S$5();isDefined(window.bonreeRUM)&&(bonreeRUM.isPrivate=es)}catch(ts){log(ts)}}));let Bi=0,yt$1=1,Ki=2,Pi=3,Xi=258,Ye$1=29,ue$3=256,we$1=ue$3+1+Ye$1,fe=30,Ge$1=19,mt$1=2*we$1+1,Q$2=15,je$1=16,Yi=7,We$1=256,zt=16,St$1=17,At$1=18,Ve=new Uint8Array([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0]),Ne$1=new Uint8Array([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13]),Gi=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7]),Rt$1=new Uint8Array([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),ji=512,K$3=new Array(2*(we$1+2)),be$1=(le$3(K$3),new Array(2*fe)),ge$1=(le$3(be$1),new Array(ji)),pe$3=(le$3(ge$1),new Array(Xi-Pi+1)),Je$1=(le$3(pe$3),new Array(Ye$1)),Le$1=(le$3(Je$1),new Array(fe));function Qe$1(J,es,ts,is,ns){this.static_tree=J,this.extra_bits=es,this.extra_base=ts,this.elems=is,this.max_length=ns,this.has_stree=J&&J.length}let Tt$1,Dt$1,Zt$1;function qe$1(J,es){this.dyn_tree=J,this.max_code=0,this.stat_desc=es}le$3(Le$1);let It$1=J=>J<256?ge$1[J]:ge$1[256+(J>>>7)],xe$1=(J,es)=>{J.pending_buf[J.pending++]=255&es,J.pending_buf[J.pending++]=es>>>8&255},N=(J,es,ts)=>{J.bi_valid>je$1-ts?(J.bi_buf|=es<<J.bi_valid&65535,xe$1(J,J.bi_buf),J.bi_buf=es>>je$1-J.bi_valid,J.bi_valid+=ts-je$1):(J.bi_buf|=es<<J.bi_valid&65535,J.bi_valid+=ts)},F$3=(J,es,ts)=>{N(J,ts[2*es],ts[2*es+1])},Ot$1=(J,es)=>{let ts=0;for(;ts|=1&J,J>>>=1,ts<<=1,0<--es;);return ts>>>1},Wi=J=>{J.bi_valid===16?(xe$1(J,J.bi_buf),J.bi_buf=0,J.bi_valid=0):8<=J.bi_valid&&(J.pending_buf[J.pending++]=255&J.bi_buf,J.bi_buf>>=8,J.bi_valid-=8)},Vi=(J,es)=>{var ts=es.dyn_tree,is=es.max_code,ns=es.stat_desc.static_tree,rs=es.stat_desc.has_stree,ss=es.stat_desc.extra_bits,as=es.stat_desc.extra_base,os=es.stat_desc.max_length;let ds,ls,fs,us,cs,hs,ms=0;for(us=0;us<=Q$2;us++)J.bl_count[us]=0;for(ts[2*J.heap[J.heap_max]+1]=0,ds=J.heap_max+1;ds<mt$1;ds++)ls=J.heap[ds],(us=ts[2*ts[2*ls+1]+1]+1)>os&&(us=os,ms++),ts[2*ls+1]=us,ls>is||(J.bl_count[us]++,cs=0,ls>=as&&(cs=ss[ls-as]),hs=ts[2*ls],J.opt_len+=hs*(us+cs),!rs)||(J.static_len+=hs*(ns[2*ls+1]+cs));if(ms!==0){do for(us=os-1;J.bl_count[us]===0;)us--;while(J.bl_count[us]--,J.bl_count[us+1]+=2,J.bl_count[os]--,0<(ms-=2));for(us=os;us!==0;us--)for(ls=J.bl_count[us];ls!==0;)is<(fs=J.heap[--ds])||(ts[2*fs+1]!==us&&(J.opt_len+=(us-ts[2*fs+1])*ts[2*fs],ts[2*fs+1]=us),ls--)}},Nt$1=(J,es,ts)=>{var is=new Array(Q$2+1);let ns,rs,ss=0;for(ns=1;ns<=Q$2;ns++)ss=ss+ts[ns-1]<<1,is[ns]=ss;for(rs=0;rs<=es;rs++){var as=J[2*rs+1];as!==0&&(J[2*rs]=Ot$1(is[as]++,as))}},Ji=()=>{let J,es,ts,is,ns;var rs=new Array(Q$2+1);for(ts=0,is=0;is<Ye$1-1;is++)for(Je$1[is]=ts,J=0;J<1<<Ve[is];J++)pe$3[ts++]=is;for(pe$3[ts-1]=is,ns=0,is=0;is<16;is++)for(Le$1[is]=ns,J=0;J<1<<Ne$1[is];J++)ge$1[ns++]=is;for(ns>>=7;is<fe;is++)for(Le$1[is]=ns<<7,J=0;J<1<<Ne$1[is]-7;J++)ge$1[256+ns++]=is;for(es=0;es<=Q$2;es++)rs[es]=0;for(J=0;J<=143;)K$3[2*J+1]=8,J++,rs[8]++;for(;J<=255;)K$3[2*J+1]=9,J++,rs[9]++;for(;J<=279;)K$3[2*J+1]=7,J++,rs[7]++;for(;J<=287;)K$3[2*J+1]=8,J++,rs[8]++;for(Nt$1(K$3,we$1+1,rs),J=0;J<fe;J++)be$1[2*J+1]=5,be$1[2*J]=Ot$1(J,5);Tt$1=new Qe$1(K$3,Ve,ue$3+1,we$1,Q$2),Dt$1=new Qe$1(be$1,Ne$1,0,fe,Q$2),Zt$1=new Qe$1(new Array(0),Gi,0,Ge$1,Yi)},Lt$1=J=>{let es;for(es=0;es<we$1;es++)J.dyn_ltree[2*es]=0;for(es=0;es<fe;es++)J.dyn_dtree[2*es]=0;for(es=0;es<Ge$1;es++)J.bl_tree[2*es]=0;J.dyn_ltree[2*We$1]=1,J.opt_len=J.static_len=0,J.sym_next=J.matches=0},Ut=J=>{8<J.bi_valid?xe$1(J,J.bi_buf):0<J.bi_valid&&(J.pending_buf[J.pending++]=J.bi_buf),J.bi_buf=0,J.bi_valid=0},Ct$1=(J,es,ts,is)=>{var ns=2*es,rs=2*ts;return J[ns]<J[rs]||J[ns]===J[rs]&&is[es]<=is[ts]},et$1=(J,es,ts)=>{var is=J.heap[ts];let ns=ts<<1;for(;ns<=J.heap_len&&(ns<J.heap_len&&Ct$1(es,J.heap[ns+1],J.heap[ns],J.depth)&&ns++,!Ct$1(es,is,J.heap[ns],J.depth));)J.heap[ts]=J.heap[ns],ts=ns,ns<<=1;J.heap[ts]=is},$t$1=(J,es,ts)=>{let is,ns,rs,ss,as=0;if(J.sym_next!==0)for(;is=255&J.pending_buf[J.sym_buf+as++],is+=(255&J.pending_buf[J.sym_buf+as++])<<8,ns=J.pending_buf[J.sym_buf+as++],is==0?F$3(J,ns,es):(rs=pe$3[ns],F$3(J,rs+ue$3+1,es),(ss=Ve[rs])!==0&&(ns-=Je$1[rs],N(J,ns,ss)),is--,rs=It$1(is),F$3(J,rs,ts),(ss=Ne$1[rs])!==0&&(is-=Le$1[rs],N(J,is,ss))),as<J.sym_next;);F$3(J,We$1,es)},tt$1=(J,es)=>{var ts=es.dyn_tree,is=es.stat_desc.static_tree,ns=es.stat_desc.has_stree,rs=es.stat_desc.elems;let ss,as,os,ds=-1;for(J.heap_len=0,J.heap_max=mt$1,ss=0;ss<rs;ss++)ts[2*ss]!==0?(J.heap[++J.heap_len]=ds=ss,J.depth[ss]=0):ts[2*ss+1]=0;for(;J.heap_len<2;)ts[2*(os=J.heap[++J.heap_len]=ds<2?++ds:0)]=1,J.depth[os]=0,J.opt_len--,ns&&(J.static_len-=is[2*os+1]);for(es.max_code=ds,ss=J.heap_len>>1;1<=ss;ss--)et$1(J,ts,ss);for(os=rs;ss=J.heap[1],J.heap[1]=J.heap[J.heap_len--],et$1(J,ts,1),as=J.heap[1],J.heap[--J.heap_max]=ss,J.heap[--J.heap_max]=as,ts[2*os]=ts[2*ss]+ts[2*as],J.depth[os]=(J.depth[ss]>=J.depth[as]?J.depth[ss]:J.depth[as])+1,ts[2*ss+1]=ts[2*as+1]=os,J.heap[1]=os++,et$1(J,ts,1),2<=J.heap_len;);J.heap[--J.heap_max]=J.heap[1],Vi(J,es),Nt$1(ts,ds,J.bl_count)},Ft$1=(J,es,ts)=>{let is,ns,rs=-1,ss=es[1],as=0,os=7,ds=4;for(ss===0&&(os=138,ds=3),es[2*(ts+1)+1]=65535,is=0;is<=ts;is++)ns=ss,ss=es[2*(is+1)+1],++as<os&&ns===ss||(as<ds?J.bl_tree[2*ns]+=as:ns!==0?(ns!==rs&&J.bl_tree[2*ns]++,J.bl_tree[2*zt]++):as<=10?J.bl_tree[2*St$1]++:J.bl_tree[2*At$1]++,as=0,rs=ns,ds=ss===0?(os=138,3):ns===ss?(os=6,3):(os=7,4))},Mt$1=(J,es,ts)=>{let is,ns,rs=-1,ss=es[1],as=0,os=7,ds=4;for(ss===0&&(os=138,ds=3),is=0;is<=ts;is++)if(ns=ss,ss=es[2*(is+1)+1],!(++as<os&&ns===ss)){if(as<ds)for(;F$3(J,ns,J.bl_tree),--as!=0;);else ns!==0?(ns!==rs&&(F$3(J,ns,J.bl_tree),as--),F$3(J,zt,J.bl_tree),N(J,as-3,2)):as<=10?(F$3(J,St$1,J.bl_tree),N(J,as-3,3)):(F$3(J,At$1,J.bl_tree),N(J,as-11,7));as=0,rs=ns,ds=ss===0?(os=138,3):ns===ss?(os=6,3):(os=7,4)}},Qi=J=>{let es;for(Ft$1(J,J.dyn_ltree,J.l_desc.max_code),Ft$1(J,J.dyn_dtree,J.d_desc.max_code),tt$1(J,J.bl_desc),es=Ge$1-1;3<=es&&J.bl_tree[2*Rt$1[es]+1]===0;es--);return J.opt_len+=3*(es+1)+5+5+4,es},qi=(J,es,ts,is)=>{let ns;for(N(J,es-257,5),N(J,ts-1,5),N(J,is-4,4),ns=0;ns<is;ns++)N(J,J.bl_tree[2*Rt$1[ns]+1],3);Mt$1(J,J.dyn_ltree,es-1),Mt$1(J,J.dyn_dtree,ts-1)},en$1=J=>{let es,ts=4093624447;for(es=0;es<=31;es++,ts>>>=1)if(1&ts&&J.dyn_ltree[2*es]!==0)return 0;if(J.dyn_ltree[18]!==0||J.dyn_ltree[20]!==0||J.dyn_ltree[26]!==0)return 1;for(es=32;es<ue$3;es++)if(J.dyn_ltree[2*es]!==0)return 1;return 0},Ht=!1,tn$1=J=>{Ht||(Ji(),Ht=!0),J.l_desc=new qe$1(J.dyn_ltree,Tt$1),J.d_desc=new qe$1(J.dyn_dtree,Dt$1),J.bl_desc=new qe$1(J.bl_tree,Zt$1),J.bi_buf=0,J.bi_valid=0,Lt$1(J)},Bt$1=(J,es,ts,is)=>{N(J,(Bi<<1)+(is?1:0),3),Ut(J),xe$1(J,ts),xe$1(J,~ts),ts&&J.pending_buf.set(J.window.subarray(es,es+ts),J.pending),J.pending+=ts},nn$1=J=>{N(J,yt$1<<1,3),F$3(J,We$1,K$3),Wi(J)},an$1=(J,es,ts,is)=>{let ns,rs,ss=0;0<J.level?(J.strm.data_type===2&&(J.strm.data_type=en$1(J)),tt$1(J,J.l_desc),tt$1(J,J.d_desc),ss=Qi(J),ns=J.opt_len+3+7>>>3,(rs=J.static_len+3+7>>>3)<=ns&&(ns=rs)):ns=rs=ts+5,ts+4<=ns&&es!==-1?Bt$1(J,es,ts,is):J.strategy===4||rs===ns?(N(J,(yt$1<<1)+(is?1:0),3),$t$1(J,K$3,be$1)):(N(J,(Ki<<1)+(is?1:0),3),qi(J,J.l_desc.max_code+1,J.d_desc.max_code+1,ss+1),$t$1(J,J.dyn_ltree,J.dyn_dtree)),Lt$1(J),is&&Ut(J)},rn$1=(J,es,ts)=>(J.pending_buf[J.sym_buf+J.sym_next++]=es,J.pending_buf[J.sym_buf+J.sym_next++]=es>>8,J.pending_buf[J.sym_buf+J.sym_next++]=ts,es===0?J.dyn_ltree[2*ts]++:(J.matches++,es--,J.dyn_ltree[2*(pe$3[ts]+ue$3+1)]++,J.dyn_dtree[2*It$1(es)]++),J.sym_next===J.sym_end);var ln$1=tn$1,fn$1=Bt$1,on$1=an$1,_n=rn$1,hn$1=nn$1,dn$1={_tr_init:ln$1,_tr_stored_block:fn$1,_tr_flush_block:on$1,_tr_tally:_n,_tr_align:hn$1};let sn$1=(J,es,ts,is)=>{let ns=65535&J,rs=J>>>16&65535,ss=0;for(;ts!==0;){for(ts-=ss=2e3<ts?2e3:ts;ns=ns+es[is++]|0,rs=rs+ns|0,--ss;);ns%=65521,rs%=65521}return ns|rs<<16};var ve$1=sn$1;let cn$1=()=>{let J,es=[];for(var ts=0;ts<256;ts++){J=ts;for(var is=0;is<8;is++)J=1&J?3988292384^J>>>1:J>>>1;es[ts]=J}return es},un$1=new Uint32Array(cn$1()),wn$1=(J,es,ts,is)=>{var ns=un$1,rs=is+ts;J^=-1;for(let ss=is;ss<rs;ss++)J=J>>>8^ns[255&(J^es[ss])];return~J};var Z$1=wn$1,q$4={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"},ee$2={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_MEM_ERROR:-4,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8};let{_tr_init:bn$1,_tr_stored_block:it$1,_tr_flush_block:gn$1,_tr_tally:Y,_tr_align:pn$1}=dn$1,{Z_NO_FLUSH:G$1,Z_PARTIAL_FLUSH:xn,Z_FULL_FLUSH:vn$1,Z_FINISH:C$5,Z_BLOCK:Kt$1,Z_OK:I$3,Z_STREAM_END:Pt$1,Z_STREAM_ERROR:M$4,Z_DATA_ERROR:kn,Z_BUF_ERROR:nt$1,Z_DEFAULT_COMPRESSION:En,Z_FILTERED:yn$1,Z_HUFFMAN_ONLY:Ue$1,Z_RLE:mn$1,Z_FIXED:zn,Z_DEFAULT_STRATEGY:Sn$1,Z_UNKNOWN:An$1,Z_DEFLATED:Ce$1}=ee$2,Rn$1=9,Tn=15,Dn=8,Zn=29,In$1=256,at$1=In$1+1+Zn,On$1=30,Nn=19,Ln=2*at$1+1,Un=15,v$4=3,j$2=258,H$3=j$2+v$4+1,Cn$1=32,oe=42,rt$1=57,lt$1=69,ft$1=73,ot$1=91,_t$1=103,te$1=113,ke$1=666,O$5=1,_e$1=2,ie$1=3,he$2=4,$n=3,ne$1=(J,es)=>(J.msg=q$4[es],es),Xt=J=>2*J-(4<J?9:0),W$2=J=>{let es=J.length;for(;0<=--es;)J[es]=0},Fn=J=>{let es,ts,is,ns=J.w_size;for(es=J.hash_size,is=es;ts=J.head[--is],J.head[is]=ts>=ns?ts-ns:0,--es;);for(es=ns,is=es;ts=J.prev[--is],J.prev[is]=ts>=ns?ts-ns:0,--es;);},Mn$1=(J,es,ts)=>(es<<J.hash_shift^ts)&J.hash_mask,V=Mn$1,L$4=J=>{var es=J.state;let ts=es.pending;(ts=ts>J.avail_out?J.avail_out:ts)!==0&&(J.output.set(es.pending_buf.subarray(es.pending_out,es.pending_out+ts),J.next_out),J.next_out+=ts,es.pending_out+=ts,J.total_out+=ts,J.avail_out-=ts,es.pending-=ts,es.pending===0)&&(es.pending_out=0)},U$7=(J,es)=>{gn$1(J,0<=J.block_start?J.block_start:-1,J.strstart-J.block_start,es),J.block_start=J.strstart,L$4(J.strm)},z$1=(J,es)=>{J.pending_buf[J.pending++]=es},Ee$3=(J,es)=>{J.pending_buf[J.pending++]=es>>>8&255,J.pending_buf[J.pending++]=255&es},ht$1=(J,es,ts,is)=>{let ns=J.avail_in;return(ns=ns>is?is:ns)===0?0:(J.avail_in-=ns,es.set(J.input.subarray(J.next_in,J.next_in+ns),ts),J.state.wrap===1?J.adler=ve$1(J.adler,es,ns,ts):J.state.wrap===2&&(J.adler=Z$1(J.adler,es,ns,ts)),J.next_in+=ns,J.total_in+=ns,ns)},Yt=(J,es)=>{let ts,is,ns=J.max_chain_length,rs=J.strstart,ss=J.prev_length,as=J.nice_match;var os=J.strstart>J.w_size-H$3?J.strstart-(J.w_size-H$3):0,ds=J.window,ls=J.w_mask,fs=J.prev,us=J.strstart+j$2;let cs=ds[rs+ss-1],hs=ds[rs+ss];J.good_match<=J.prev_length&&(ns>>=2),as>J.lookahead&&(as=J.lookahead);do if(ds[(ts=es)+ss]===hs&&ds[ts+ss-1]===cs&&ds[ts]===ds[rs]&&ds[++ts]===ds[rs+1]){for(rs+=2,ts++;ds[++rs]===ds[++ts]&&ds[++rs]===ds[++ts]&&ds[++rs]===ds[++ts]&&ds[++rs]===ds[++ts]&&ds[++rs]===ds[++ts]&&ds[++rs]===ds[++ts]&&ds[++rs]===ds[++ts]&&ds[++rs]===ds[++ts]&&rs<us;);if(is=j$2-(us-rs),rs=us-j$2,is>ss){if(J.match_start=es,(ss=is)>=as)break;cs=ds[rs+ss-1],hs=ds[rs+ss]}}while((es=fs[es&ls])>os&&--ns!=0);return ss<=J.lookahead?ss:J.lookahead},de$1=J=>{var es=J.w_size;let ts,is,ns;do{if(is=J.window_size-J.lookahead-J.strstart,J.strstart>=es+(es-H$3)&&(J.window.set(J.window.subarray(es,es+es-is),0),J.match_start-=es,J.strstart-=es,J.block_start-=es,J.strstart<J.insert&&(J.insert=J.strstart),Fn(J),is+=es),J.strm.avail_in===0)break;if(ts=ht$1(J.strm,J.window,J.strstart+J.lookahead,is),J.lookahead+=ts,J.lookahead+J.insert>=v$4)for(ns=J.strstart-J.insert,J.ins_h=J.window[ns],J.ins_h=V(J,J.ins_h,J.window[ns+1]);J.insert&&(J.ins_h=V(J,J.ins_h,J.window[ns+v$4-1]),J.prev[ns&J.w_mask]=J.head[J.ins_h],J.head[J.ins_h]=ns,ns++,J.insert--,!(J.lookahead+J.insert<v$4)););}while(J.lookahead<H$3&&J.strm.avail_in!==0)},Gt$1=(J,es)=>{let ts,is,ns,rs=J.pending_buf_size-5>J.w_size?J.w_size:J.pending_buf_size-5,ss=0,as=J.strm.avail_in;for(;ts=65535,ns=J.bi_valid+42>>3,!(J.strm.avail_out<ns||(ns=J.strm.avail_out-ns,is=J.strstart-J.block_start,(ts=(ts=ts>is+J.strm.avail_in?is+J.strm.avail_in:ts)>ns?ns:ts)<rs&&(ts===0&&es!==C$5||es===G$1||ts!==is+J.strm.avail_in)))&&(ss=es===C$5&&ts===is+J.strm.avail_in?1:0,it$1(J,0,0,ss),J.pending_buf[J.pending-4]=ts,J.pending_buf[J.pending-3]=ts>>8,J.pending_buf[J.pending-2]=~ts,J.pending_buf[J.pending-1]=~ts>>8,L$4(J.strm),is&&(is>ts&&(is=ts),J.strm.output.set(J.window.subarray(J.block_start,J.block_start+is),J.strm.next_out),J.strm.next_out+=is,J.strm.avail_out-=is,J.strm.total_out+=is,J.block_start+=is,ts-=is),ts&&(ht$1(J.strm,J.strm.output,J.strm.next_out,ts),J.strm.next_out+=ts,J.strm.avail_out-=ts,J.strm.total_out+=ts),ss===0););return(as-=J.strm.avail_in)&&(J.w_size<=as?(J.matches=2,J.window.set(J.strm.input.subarray(J.strm.next_in-J.w_size,J.strm.next_in),0),J.strstart=J.w_size,J.insert=J.strstart):(J.window_size-J.strstart<=as&&(J.strstart-=J.w_size,J.window.set(J.window.subarray(J.w_size,J.w_size+J.strstart),0),J.matches<2&&J.matches++,J.strstart<J.insert)&&(J.insert=J.strstart),J.window.set(J.strm.input.subarray(J.strm.next_in-as,J.strm.next_in),J.strstart),J.strstart+=as,J.insert+=J.w_size-J.insert<as?J.w_size-J.insert:as),J.block_start=J.strstart),J.high_water<J.strstart&&(J.high_water=J.strstart),ss?he$2:es!==G$1&&es!==C$5&&J.strm.avail_in===0&&J.strstart===J.block_start?_e$1:(ns=J.window_size-J.strstart,J.strm.avail_in>ns&&J.w_size<=J.block_start&&(J.block_start-=J.w_size,J.strstart-=J.w_size,J.window.set(J.window.subarray(J.w_size,J.w_size+J.strstart),0),J.matches<2&&J.matches++,ns+=J.w_size,J.strstart<J.insert)&&(J.insert=J.strstart),(ns=ns>J.strm.avail_in?J.strm.avail_in:ns)&&(ht$1(J.strm,J.window,J.strstart,ns),J.strstart+=ns,J.insert+=ns>J.w_size-J.insert?J.w_size-J.insert:ns),J.high_water<J.strstart&&(J.high_water=J.strstart),ns=J.bi_valid+42>>3,ns=65535<J.pending_buf_size-ns?65535:J.pending_buf_size-ns,rs=ns>J.w_size?J.w_size:ns,((is=J.strstart-J.block_start)>=rs||(is||es===C$5)&&es!==G$1&&J.strm.avail_in===0&&is<=ns)&&(ts=is>ns?ns:is,ss=es===C$5&&J.strm.avail_in===0&&ts===is?1:0,it$1(J,J.block_start,ts,ss),J.block_start+=ts,L$4(J.strm)),ss?ie$1:O$5)},dt$1=(J,es)=>{let ts,is;for(;;){if(J.lookahead<H$3){if(de$1(J),J.lookahead<H$3&&es===G$1)return O$5;if(J.lookahead===0)break}if(ts=0,J.lookahead>=v$4&&(J.ins_h=V(J,J.ins_h,J.window[J.strstart+v$4-1]),ts=J.prev[J.strstart&J.w_mask]=J.head[J.ins_h],J.head[J.ins_h]=J.strstart),ts!==0&&J.strstart-ts<=J.w_size-H$3&&(J.match_length=Yt(J,ts)),J.match_length>=v$4)if(is=Y(J,J.strstart-J.match_start,J.match_length-v$4),J.lookahead-=J.match_length,J.match_length<=J.max_lazy_match&&J.lookahead>=v$4){for(J.match_length--;J.strstart++,J.ins_h=V(J,J.ins_h,J.window[J.strstart+v$4-1]),ts=J.prev[J.strstart&J.w_mask]=J.head[J.ins_h],J.head[J.ins_h]=J.strstart,--J.match_length!=0;);J.strstart++}else J.strstart+=J.match_length,J.match_length=0,J.ins_h=J.window[J.strstart],J.ins_h=V(J,J.ins_h,J.window[J.strstart+1]);else is=Y(J,0,J.window[J.strstart]),J.lookahead--,J.strstart++;if(is&&(U$7(J,!1),J.strm.avail_out===0))return O$5}return J.insert=J.strstart<v$4-1?J.strstart:v$4-1,es===C$5?(U$7(J,!0),J.strm.avail_out===0?ie$1:he$2):J.sym_next&&(U$7(J,!1),J.strm.avail_out===0)?O$5:_e$1},se$1=(J,es)=>{let ts,is,ns;for(;;){if(J.lookahead<H$3){if(de$1(J),J.lookahead<H$3&&es===G$1)return O$5;if(J.lookahead===0)break}if(ts=0,J.lookahead>=v$4&&(J.ins_h=V(J,J.ins_h,J.window[J.strstart+v$4-1]),ts=J.prev[J.strstart&J.w_mask]=J.head[J.ins_h],J.head[J.ins_h]=J.strstart),J.prev_length=J.match_length,J.prev_match=J.match_start,J.match_length=v$4-1,ts!==0&&J.prev_length<J.max_lazy_match&&J.strstart-ts<=J.w_size-H$3&&(J.match_length=Yt(J,ts),J.match_length<=5)&&(J.strategy===yn$1||J.match_length===v$4&&4096<J.strstart-J.match_start)&&(J.match_length=v$4-1),J.prev_length>=v$4&&J.match_length<=J.prev_length){for(ns=J.strstart+J.lookahead-v$4,is=Y(J,J.strstart-1-J.prev_match,J.prev_length-v$4),J.lookahead-=J.prev_length-1,J.prev_length-=2;++J.strstart<=ns&&(J.ins_h=V(J,J.ins_h,J.window[J.strstart+v$4-1]),ts=J.prev[J.strstart&J.w_mask]=J.head[J.ins_h],J.head[J.ins_h]=J.strstart),--J.prev_length!=0;);if(J.match_available=0,J.match_length=v$4-1,J.strstart++,is&&(U$7(J,!1),J.strm.avail_out===0))return O$5}else if(J.match_available){if((is=Y(J,0,J.window[J.strstart-1]))&&U$7(J,!1),J.strstart++,J.lookahead--,J.strm.avail_out===0)return O$5}else J.match_available=1,J.strstart++,J.lookahead--}return J.match_available&&(is=Y(J,0,J.window[J.strstart-1]),J.match_available=0),J.insert=J.strstart<v$4-1?J.strstart:v$4-1,es===C$5?(U$7(J,!0),J.strm.avail_out===0?ie$1:he$2):J.sym_next&&(U$7(J,!1),J.strm.avail_out===0)?O$5:_e$1},Hn=(J,es)=>{let ts,is,ns,rs;for(var ss=J.window;;){if(J.lookahead<=j$2){if(de$1(J),J.lookahead<=j$2&&es===G$1)return O$5;if(J.lookahead===0)break}if(J.match_length=0,J.lookahead>=v$4&&0<J.strstart&&(ns=J.strstart-1,(is=ss[ns])===ss[++ns])&&is===ss[++ns]&&is===ss[++ns]){for(rs=J.strstart+j$2;is===ss[++ns]&&is===ss[++ns]&&is===ss[++ns]&&is===ss[++ns]&&is===ss[++ns]&&is===ss[++ns]&&is===ss[++ns]&&is===ss[++ns]&&ns<rs;);J.match_length=j$2-(rs-ns),J.lookahead<J.match_length&&(J.match_length=J.lookahead)}if(J.match_length>=v$4?(ts=Y(J,1,J.match_length-v$4),J.lookahead-=J.match_length,J.strstart+=J.match_length,J.match_length=0):(ts=Y(J,0,J.window[J.strstart]),J.lookahead--,J.strstart++),ts&&(U$7(J,!1),J.strm.avail_out===0))return O$5}return J.insert=0,es===C$5?(U$7(J,!0),J.strm.avail_out===0?ie$1:he$2):J.sym_next&&(U$7(J,!1),J.strm.avail_out===0)?O$5:_e$1},Bn=(J,es)=>{for(var ts;;){if(J.lookahead===0&&(de$1(J),J.lookahead===0)){if(es===G$1)return O$5;break}if(J.match_length=0,ts=Y(J,0,J.window[J.strstart]),J.lookahead--,J.strstart++,ts&&(U$7(J,!1),J.strm.avail_out===0))return O$5}return J.insert=0,es===C$5?(U$7(J,!0),J.strm.avail_out===0?ie$1:he$2):J.sym_next&&(U$7(J,!1),J.strm.avail_out===0)?O$5:_e$1};function B$3(J,es,ts,is,ns){this.good_length=J,this.max_lazy=es,this.nice_length=ts,this.max_chain=is,this.func=ns}let ye$1=[new B$3(0,0,0,0,Gt$1),new B$3(4,4,8,4,dt$1),new B$3(4,5,16,8,dt$1),new B$3(4,6,32,32,dt$1),new B$3(4,4,16,16,se$1),new B$3(8,16,32,32,se$1),new B$3(8,16,128,128,se$1),new B$3(8,32,128,256,se$1),new B$3(32,128,258,1024,se$1),new B$3(32,258,258,4096,se$1)],Kn=J=>{J.window_size=2*J.w_size,W$2(J.head),J.max_lazy_match=ye$1[J.level].max_lazy,J.good_match=ye$1[J.level].good_length,J.nice_match=ye$1[J.level].nice_length,J.max_chain_length=ye$1[J.level].max_chain,J.strstart=0,J.block_start=0,J.lookahead=0,J.insert=0,J.match_length=J.prev_length=v$4-1,J.match_available=0,J.ins_h=0};function Pn(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=Ce$1,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new Uint16Array(2*Ln),this.dyn_dtree=new Uint16Array(2*(2*On$1+1)),this.bl_tree=new Uint16Array(2*(2*Nn+1)),W$2(this.dyn_ltree),W$2(this.dyn_dtree),W$2(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new Uint16Array(Un+1),this.heap=new Uint16Array(2*at$1+1),W$2(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new Uint16Array(2*at$1+1),W$2(this.depth),this.sym_buf=0,this.lit_bufsize=0,this.sym_next=0,this.sym_end=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}let me$1=J=>{var es;return!J||!(es=J.state)||es.strm!==J||es.status!==oe&&es.status!==rt$1&&es.status!==lt$1&&es.status!==ft$1&&es.status!==ot$1&&es.status!==_t$1&&es.status!==te$1&&es.status!==ke$1?1:0},jt=J=>{if(me$1(J))return ne$1(J,M$4);J.total_in=J.total_out=0,J.data_type=An$1;var es=J.state;return es.pending=0,es.pending_out=0,es.wrap<0&&(es.wrap=-es.wrap),es.status=es.wrap===2?rt$1:es.wrap?oe:te$1,J.adler=es.wrap===2?0:1,es.last_flush=-2,bn$1(es),I$3},Wt$1=J=>{var es=jt(J);return es===I$3&&Kn(J.state),es},Xn=(J,es)=>me$1(J)||J.state.wrap!==2?M$4:(J.state.gzhead=es,I$3),Vt$1=(J,es,ts,is,ns,rs)=>{if(!J)return M$4;let ss=1;if(es===En&&(es=6),is<0?(ss=0,is=-is):15<is&&(ss=2,is-=16),ns<1||ns>Rn$1||ts!==Ce$1||is<8||15<is||es<0||9<es||rs<0||rs>zn||is===8&&ss!==1)return ne$1(J,M$4);is===8&&(is=9);var as=new Pn;return(J.state=as).strm=J,as.status=oe,as.wrap=ss,as.gzhead=null,as.w_bits=is,as.w_size=1<<as.w_bits,as.w_mask=as.w_size-1,as.hash_bits=ns+7,as.hash_size=1<<as.hash_bits,as.hash_mask=as.hash_size-1,as.hash_shift=~~((as.hash_bits+v$4-1)/v$4),as.window=new Uint8Array(2*as.w_size),as.head=new Uint16Array(as.hash_size),as.prev=new Uint16Array(as.w_size),as.lit_bufsize=1<<ns+6,as.pending_buf_size=4*as.lit_bufsize,as.pending_buf=new Uint8Array(as.pending_buf_size),as.sym_buf=as.lit_bufsize,as.sym_end=3*(as.lit_bufsize-1),as.level=es,as.strategy=rs,as.method=ts,Wt$1(J)},Yn=(J,es)=>Vt$1(J,es,Ce$1,Tn,Dn,Sn$1),Gn=(J,es)=>{if(me$1(J)||es>Kt$1||es<0)return J?ne$1(J,M$4):M$4;var ts=J.state;if(!J.output||J.avail_in!==0&&!J.input||ts.status===ke$1&&es!==C$5)return ne$1(J,J.avail_out===0?nt$1:M$4);var is=ts.last_flush;if(ts.last_flush=es,ts.pending!==0){if(L$4(J),J.avail_out===0)return ts.last_flush=-1,I$3}else if(J.avail_in===0&&Xt(es)<=Xt(is)&&es!==C$5)return ne$1(J,nt$1);if(ts.status===ke$1&&J.avail_in!==0)return ne$1(J,nt$1);if(ts.status===oe&&ts.wrap===0&&(ts.status=te$1),ts.status===oe){let rs=Ce$1+(ts.w_bits-8<<4)<<8,ss;if(ss=ts.strategy>=Ue$1||ts.level<2?0:ts.level<6?1:ts.level===6?2:3,rs|=ss<<6,ts.strstart!==0&&(rs|=Cn$1),rs+=31-rs%31,Ee$3(ts,rs),ts.strstart!==0&&(Ee$3(ts,J.adler>>>16),Ee$3(ts,65535&J.adler)),J.adler=1,ts.status=te$1,L$4(J),ts.pending!==0)return ts.last_flush=-1,I$3}if(ts.status===rt$1){if(J.adler=0,z$1(ts,31),z$1(ts,139),z$1(ts,8),ts.gzhead)z$1(ts,(ts.gzhead.text?1:0)+(ts.gzhead.hcrc?2:0)+(ts.gzhead.extra?4:0)+(ts.gzhead.name?8:0)+(ts.gzhead.comment?16:0)),z$1(ts,255&ts.gzhead.time),z$1(ts,ts.gzhead.time>>8&255),z$1(ts,ts.gzhead.time>>16&255),z$1(ts,ts.gzhead.time>>24&255),z$1(ts,ts.level===9?2:ts.strategy>=Ue$1||ts.level<2?4:0),z$1(ts,255&ts.gzhead.os),ts.gzhead.extra&&ts.gzhead.extra.length&&(z$1(ts,255&ts.gzhead.extra.length),z$1(ts,ts.gzhead.extra.length>>8&255)),ts.gzhead.hcrc&&(J.adler=Z$1(J.adler,ts.pending_buf,ts.pending,0)),ts.gzindex=0,ts.status=lt$1;else if(z$1(ts,0),z$1(ts,0),z$1(ts,0),z$1(ts,0),z$1(ts,0),z$1(ts,ts.level===9?2:ts.strategy>=Ue$1||ts.level<2?4:0),z$1(ts,$n),ts.status=te$1,L$4(J),ts.pending!==0)return ts.last_flush=-1,I$3}if(ts.status===lt$1){if(ts.gzhead.extra){let rs=ts.pending,ss=(65535&ts.gzhead.extra.length)-ts.gzindex;for(;ts.pending+ss>ts.pending_buf_size;){var ns=ts.pending_buf_size-ts.pending;if(ts.pending_buf.set(ts.gzhead.extra.subarray(ts.gzindex,ts.gzindex+ns),ts.pending),ts.pending=ts.pending_buf_size,ts.gzhead.hcrc&&ts.pending>rs&&(J.adler=Z$1(J.adler,ts.pending_buf,ts.pending-rs,rs)),ts.gzindex+=ns,L$4(J),ts.pending!==0)return ts.last_flush=-1,I$3;rs=0,ss-=ns}is=new Uint8Array(ts.gzhead.extra),ts.pending_buf.set(is.subarray(ts.gzindex,ts.gzindex+ss),ts.pending),ts.pending+=ss,ts.gzhead.hcrc&&ts.pending>rs&&(J.adler=Z$1(J.adler,ts.pending_buf,ts.pending-rs,rs)),ts.gzindex=0}ts.status=ft$1}if(ts.status===ft$1){if(ts.gzhead.name){let rs,ss=ts.pending;do if(ts.pending===ts.pending_buf_size){if(ts.gzhead.hcrc&&ts.pending>ss&&(J.adler=Z$1(J.adler,ts.pending_buf,ts.pending-ss,ss)),L$4(J),ts.pending!==0)return ts.last_flush=-1,I$3;ss=0}while(rs=ts.gzindex<ts.gzhead.name.length?255&ts.gzhead.name.charCodeAt(ts.gzindex++):0,z$1(ts,rs),rs!=0);ts.gzhead.hcrc&&ts.pending>ss&&(J.adler=Z$1(J.adler,ts.pending_buf,ts.pending-ss,ss)),ts.gzindex=0}ts.status=ot$1}if(ts.status===ot$1){if(ts.gzhead.comment){let rs,ss=ts.pending;do if(ts.pending===ts.pending_buf_size){if(ts.gzhead.hcrc&&ts.pending>ss&&(J.adler=Z$1(J.adler,ts.pending_buf,ts.pending-ss,ss)),L$4(J),ts.pending!==0)return ts.last_flush=-1,I$3;ss=0}while(rs=ts.gzindex<ts.gzhead.comment.length?255&ts.gzhead.comment.charCodeAt(ts.gzindex++):0,z$1(ts,rs),rs!=0);ts.gzhead.hcrc&&ts.pending>ss&&(J.adler=Z$1(J.adler,ts.pending_buf,ts.pending-ss,ss))}ts.status=_t$1}if(ts.status===_t$1){if(ts.gzhead.hcrc){if(ts.pending_buf_size<ts.pending+2&&(L$4(J),ts.pending!==0))return ts.last_flush=-1,I$3;z$1(ts,255&J.adler),z$1(ts,J.adler>>8&255),J.adler=0}if(ts.status=te$1,L$4(J),ts.pending!==0)return ts.last_flush=-1,I$3}if(J.avail_in!==0||ts.lookahead!==0||es!==G$1&&ts.status!==ke$1){if(is=ts.level===0?Gt$1(ts,es):ts.strategy===Ue$1?Bn(ts,es):ts.strategy===mn$1?Hn(ts,es):ye$1[ts.level].func(ts,es),is!==ie$1&&is!==he$2||(ts.status=ke$1),is===O$5||is===ie$1)return J.avail_out===0&&(ts.last_flush=-1),I$3;if(is===_e$1&&(es===xn?pn$1(ts):es!==Kt$1&&(it$1(ts,0,0,!1),es===vn$1)&&(W$2(ts.head),ts.lookahead===0)&&(ts.strstart=0,ts.block_start=0,ts.insert=0),L$4(J),J.avail_out===0))return ts.last_flush=-1,I$3}return es!==C$5||!(ts.wrap<=0)&&(ts.wrap===2?(z$1(ts,255&J.adler),z$1(ts,J.adler>>8&255),z$1(ts,J.adler>>16&255),z$1(ts,J.adler>>24&255),z$1(ts,255&J.total_in),z$1(ts,J.total_in>>8&255),z$1(ts,J.total_in>>16&255),z$1(ts,J.total_in>>24&255)):(Ee$3(ts,J.adler>>>16),Ee$3(ts,65535&J.adler)),L$4(J),0<ts.wrap&&(ts.wrap=-ts.wrap),ts.pending!==0)?I$3:Pt$1},jn=J=>{var es;return me$1(J)?M$4:(es=J.state.status,J.state=null,es===te$1?ne$1(J,kn):I$3)},Wn=(J,es)=>{let ts=es.length;if(me$1(J))return M$4;var is=J.state,ns=is.wrap;if(ns===2||ns===1&&is.status!==oe||is.lookahead)return M$4;ns===1&&(J.adler=ve$1(J.adler,es,ts,0)),is.wrap=0,ts>=is.w_size&&(ns===0&&(W$2(is.head),is.strstart=0,is.block_start=0,is.insert=0),(rs=new Uint8Array(is.w_size)).set(es.subarray(ts-is.w_size,ts),0),es=rs,ts=is.w_size);var rs=J.avail_in,ss=J.next_in,as=J.input;for(J.avail_in=ts,J.next_in=0,J.input=es,de$1(is);is.lookahead>=v$4;){let os=is.strstart,ds=is.lookahead-(v$4-1);for(;is.ins_h=V(is,is.ins_h,is.window[os+v$4-1]),is.prev[os&is.w_mask]=is.head[is.ins_h],is.head[is.ins_h]=os,os++,--ds;);is.strstart=os,is.lookahead=v$4-1,de$1(is)}return is.strstart+=is.lookahead,is.block_start=is.strstart,is.insert=is.lookahead,is.lookahead=0,is.match_length=is.prev_length=v$4-1,is.match_available=0,J.next_in=ss,J.input=as,J.avail_in=rs,is.wrap=ns,I$3};var Vn=Yn,Jn=Vt$1,Qn=Wt$1,qn=jt,ea=Xn,ta=Gn,ia=jn,na=Wn,aa="pako deflate (from Nodeca project)",ze$1={deflateInit:Vn,deflateInit2:Jn,deflateReset:Qn,deflateResetKeep:qn,deflateSetHeader:ea,deflate:ta,deflateEnd:ia,deflateSetDictionary:na,deflateInfo:aa};let ra=(J,es)=>Object.prototype.hasOwnProperty.call(J,es);var la=function(J){let es=Array.prototype.slice.call(arguments,1);for(;es.length;){var ts=es.shift();if(ts){if(typeof ts!="object")throw new TypeError(ts+"must be non-object");for(let is in ts)ra(ts,is)&&(J[is]=ts[is])}}return J},fa=J=>{let es=0;for(let ns=0,rs=J.length;ns<rs;ns++)es+=J[ns].length;var ts=new Uint8Array(es);for(let ns=0,rs=0,ss=J.length;ns<ss;ns++){var is=J[ns];ts.set(is,rs),rs+=is.length}return ts},$e$1={assign:la,flattenChunks:fa};let Jt$1=!0;try{String.fromCharCode.apply(null,new Uint8Array(1))}catch(J){Jt$1=!1}let Se$1=new Uint8Array(256);for(let J=0;J<256;J++)Se$1[J]=252<=J?6:248<=J?5:240<=J?4:224<=J?3:192<=J?2:1;Se$1[254]=Se$1[254]=1;var oa=J=>{if(typeof TextEncoder=="function"&&TextEncoder.prototype.encode)return new TextEncoder().encode(J);let es,ts,is,ns,rs,ss=J.length,as=0;for(ns=0;ns<ss;ns++)(64512&(ts=J.charCodeAt(ns)))==55296&&ns+1<ss&&(64512&(is=J.charCodeAt(ns+1)))==56320&&(ts=65536+(ts-55296<<10)+(is-56320),ns++),as+=ts<128?1:ts<2048?2:ts<65536?3:4;for(es=new Uint8Array(as),rs=0,ns=0;rs<as;ns++)(64512&(ts=J.charCodeAt(ns)))==55296&&ns+1<ss&&(64512&(is=J.charCodeAt(ns+1)))==56320&&(ts=65536+(ts-55296<<10)+(is-56320),ns++),ts<128?es[rs++]=ts:(ts<2048?es[rs++]=192|ts>>>6:(ts<65536?es[rs++]=224|ts>>>12:(es[rs++]=240|ts>>>18,es[rs++]=128|ts>>>12&63),es[rs++]=128|ts>>>6&63),es[rs++]=128|63&ts);return es};let _a=(J,es)=>{if(es<65534&&J.subarray&&Jt$1)return String.fromCharCode.apply(null,J.length===es?J:J.subarray(0,es));let ts="";for(let is=0;is<es;is++)ts+=String.fromCharCode(J[is]);return ts};var ha=(J,es)=>{var ts=es||J.length;if(typeof TextDecoder=="function"&&TextDecoder.prototype.decode)return new TextDecoder().decode(J.subarray(0,es));let is,ns;var rs=new Array(2*ts);for(ns=0,is=0;is<ts;){let ss=J[is++];if(ss<128)rs[ns++]=ss;else{let as=Se$1[ss];if(4<as)rs[ns++]=65533,is+=as-1;else{for(ss&=as===2?31:as===3?15:7;1<as&&is<ts;)ss=ss<<6|63&J[is++],as--;1<as?rs[ns++]=65533:ss<65536?rs[ns++]=ss:(ss-=65536,rs[ns++]=55296|ss>>10&1023,rs[ns++]=56320|1023&ss)}}}return _a(rs,ns)},da=(J,es)=>{let ts=(es=(es=es||J.length)>J.length?J.length:es)-1;for(;0<=ts&&(192&J[ts])==128;)ts--;return!(ts<0||ts===0)&&ts+Se$1[J[ts]]>es?ts:es},Ae$1={string2buf:oa,buf2string:ha,utf8border:da};function sa(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0}var Qt$1=sa;let qt$1=Object.prototype.toString,{Z_NO_FLUSH:ca,Z_SYNC_FLUSH:ua,Z_FULL_FLUSH:wa,Z_FINISH:ba,Z_OK:Fe$1,Z_STREAM_END:ga,Z_DEFAULT_COMPRESSION:pa,Z_DEFAULT_STRATEGY:xa,Z_DEFLATED:va}=ee$2;function Re$1(J){if(this.options=$e$1.assign({level:pa,method:va,chunkSize:16384,windowBits:15,memLevel:8,strategy:xa},J||{}),J=this.options,J.raw&&0<J.windowBits?J.windowBits=-J.windowBits:J.gzip&&0<J.windowBits&&J.windowBits<16&&(J.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new Qt$1,this.strm.avail_out=0,(es=ze$1.deflateInit2(this.strm,J.level,J.method,J.windowBits,J.memLevel,J.strategy))!==Fe$1)throw new Error(q$4[es]);if(J.header&&ze$1.deflateSetHeader(this.strm,J.header),J.dictionary){var es,J=typeof J.dictionary=="string"?Ae$1.string2buf(J.dictionary):qt$1.call(J.dictionary)==="[object ArrayBuffer]"?new Uint8Array(J.dictionary):J.dictionary;if((es=ze$1.deflateSetDictionary(this.strm,J))!==Fe$1)throw new Error(q$4[es]);this._dict_set=!0}}function st$1(J,es){if(es=new Re$1(es),es.push(J,!0),es.err)throw es.msg||q$4[es.err];return es.result}function ka(J,es){return(es=es||{}).raw=!0,st$1(J,es)}function Ea(J,es){return(es=es||{}).gzip=!0,st$1(J,es)}Re$1.prototype.push=function(J,es){var ts,is,ns=this.strm,rs=this.options.chunkSize;if(this.ended)return!1;for(is=es===~~es?es:es===!0?ba:ca,typeof J=="string"?ns.input=Ae$1.string2buf(J):qt$1.call(J)==="[object ArrayBuffer]"?ns.input=new Uint8Array(J):ns.input=J,ns.next_in=0,ns.avail_in=ns.input.length;;)if(ns.avail_out===0&&(ns.output=new Uint8Array(rs),ns.next_out=0,ns.avail_out=rs),(is===ua||is===wa)&&ns.avail_out<=6)this.onData(ns.output.subarray(0,ns.next_out)),ns.avail_out=0;else{if(ze$1.deflate(ns,is)===ga)return 0<ns.next_out&&this.onData(ns.output.subarray(0,ns.next_out)),ts=ze$1.deflateEnd(this.strm),this.onEnd(ts),this.ended=!0,ts===Fe$1;if(ns.avail_out!==0){if(0<is&&0<ns.next_out)this.onData(ns.output.subarray(0,ns.next_out)),ns.avail_out=0;else if(ns.avail_in===0)break}else this.onData(ns.output)}return!0},Re$1.prototype.onData=function(J){this.chunks.push(J)},Re$1.prototype.onEnd=function(J){J===Fe$1&&(this.result=$e$1.flattenChunks(this.chunks)),this.chunks=[],this.err=J,this.msg=this.strm.msg};var ya=Re$1,ma=st$1,za=ka,Sa=Ea,Aa=ee$2,Ra={Deflate:ya,deflate:ma,deflateRaw:za,gzip:Sa,constants:Aa};let Me$1=16209,Ta=16191;var Da=function(J,es){let ts,is,ns,rs,ss,as,os,ds,ls,fs,us,cs,hs,ms,ps,$s,gs,ws,Ss,_s,ys,Ts,Es,vs;var Cs=J.state;ts=J.next_in,Es=J.input,is=ts+(J.avail_in-5),ns=J.next_out,vs=J.output,rs=ns-(es-J.avail_out),ss=ns+(J.avail_out-257),as=Cs.dmax,os=Cs.wsize,ds=Cs.whave,ls=Cs.wnext,fs=Cs.window,us=Cs.hold,cs=Cs.bits,hs=Cs.lencode,ms=Cs.distcode,ps=(1<<Cs.lenbits)-1,$s=(1<<Cs.distbits)-1;e:do for(cs<15&&(us+=Es[ts++]<<cs,cs+=8,us+=Es[ts++]<<cs,cs+=8),gs=hs[us&ps];;){if(ws=gs>>>24,us>>>=ws,cs-=ws,(ws=gs>>>16&255)===0)vs[ns++]=65535&gs;else{if(!(16&ws)){if(64&ws){if(32&ws){Cs.mode=Ta;break e}J.msg="invalid literal/length code",Cs.mode=Me$1;break e}gs=hs[(65535&gs)+(us&(1<<ws)-1)];continue}for(Ss=65535&gs,(ws&=15)&&(cs<ws&&(us+=Es[ts++]<<cs,cs+=8),Ss+=us&(1<<ws)-1,us>>>=ws,cs-=ws),cs<15&&(us+=Es[ts++]<<cs,cs+=8,us+=Es[ts++]<<cs,cs+=8),gs=ms[us&$s];;){if(ws=gs>>>24,us>>>=ws,cs-=ws,16&(ws=gs>>>16&255)){if(_s=65535&gs,ws&=15,cs<ws&&(us+=Es[ts++]<<cs,(cs+=8)<ws)&&(us+=Es[ts++]<<cs,cs+=8),as<(_s+=us&(1<<ws)-1)){J.msg="invalid distance too far back",Cs.mode=Me$1;break e}if(us>>>=ws,cs-=ws,_s>(ws=ns-rs)){if((ws=_s-ws)>ds&&Cs.sane){J.msg="invalid distance too far back",Cs.mode=Me$1;break e}if(ys=0,Ts=fs,ls===0){if(ys+=os-ws,ws<Ss){for(Ss-=ws;vs[ns++]=fs[ys++],--ws;);ys=ns-_s,Ts=vs}}else if(ls<ws){if(ys+=os+ls-ws,(ws-=ls)<Ss){for(Ss-=ws;vs[ns++]=fs[ys++],--ws;);if(ys=0,ls<Ss){for(ws=ls,Ss-=ws;vs[ns++]=fs[ys++],--ws;);ys=ns-_s,Ts=vs}}}else if(ys+=ls-ws,ws<Ss){for(Ss-=ws;vs[ns++]=fs[ys++],--ws;);ys=ns-_s,Ts=vs}for(;2<Ss;)vs[ns++]=Ts[ys++],vs[ns++]=Ts[ys++],vs[ns++]=Ts[ys++],Ss-=3;Ss&&(vs[ns++]=Ts[ys++],1<Ss)&&(vs[ns++]=Ts[ys++])}else{for(ys=ns-_s;vs[ns++]=vs[ys++],vs[ns++]=vs[ys++],vs[ns++]=vs[ys++],2<(Ss-=3););Ss&&(vs[ns++]=vs[ys++],1<Ss)&&(vs[ns++]=vs[ys++])}break}if(64&ws){J.msg="invalid distance code",Cs.mode=Me$1;break e}gs=ms[(65535&gs)+(us&(1<<ws)-1)]}}break}while(ts<is&&ns<ss);Ss=cs>>3,ts-=Ss,cs-=Ss<<3,us&=(1<<cs)-1,J.next_in=ts,J.next_out=ns,J.avail_in=ts<is?is-ts+5:5-(ts-is),J.avail_out=ns<ss?ss-ns+257:257-(ns-ss),Cs.hold=us,Cs.bits=cs};let ce$1=15,ei=852,ti=592,ii=0,ct$1=1,ni=2,Za=new Uint16Array([3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0]),Ia=new Uint8Array([16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78]),Oa=new Uint16Array([1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0]),Na=new Uint8Array([16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64]),La=(J,es,ts,is,ns,rs,ss,as)=>{var os=as.bits;let ds,ls,fs,us,cs,hs,ms=0,ps=0,$s=0,gs=0,ws=0,Ss=0,_s=0,ys=0,Ts=0,Es=0,vs=null;var Cs=new Uint16Array(ce$1+1),As=new Uint16Array(ce$1+1);let Ns,Ms,Ls,xs=null;for(ms=0;ms<=ce$1;ms++)Cs[ms]=0;for(ps=0;ps<is;ps++)Cs[es[ts+ps]]++;for(ws=os,gs=ce$1;1<=gs&&Cs[gs]===0;gs--);if(ws>gs&&(ws=gs),gs===0)ns[rs++]=20971520,ns[rs++]=20971520,as.bits=1;else{for($s=1;$s<gs&&Cs[$s]===0;$s++);for(ws<$s&&(ws=$s),ys=1,ms=1;ms<=ce$1;ms++)if((ys=(ys<<=1)-Cs[ms])<0)return-1;if(0<ys&&(J===ii||gs!==1))return-1;for(As[1]=0,ms=1;ms<ce$1;ms++)As[ms+1]=As[ms]+Cs[ms];for(ps=0;ps<is;ps++)es[ts+ps]!==0&&(ss[As[es[ts+ps]]++]=ps);if(hs=J===ii?(vs=xs=ss,20):J===ct$1?(vs=Za,xs=Ia,257):(vs=Oa,xs=Na,0),Es=0,ps=0,ms=$s,cs=rs,Ss=ws,_s=0,fs=-1,us=(Ts=1<<ws)-1,J===ct$1&&Ts>ei||J===ni&&Ts>ti)return 1;for(;;){for(Ns=ms-_s,Ls=ss[ps]+1<hs?(Ms=0,ss[ps]):ss[ps]>=hs?(Ms=xs[ss[ps]-hs],vs[ss[ps]-hs]):(Ms=96,0),ds=1<<ms-_s,ls=1<<Ss,$s=ls;ls-=ds,ns[cs+(Es>>_s)+ls]=Ns<<24|Ms<<16|Ls,ls!==0;);for(ds=1<<ms-1;Es&ds;)ds>>=1;if(ds!==0?Es=(Es&=ds-1)+ds:Es=0,ps++,--Cs[ms]==0){if(ms===gs)break;ms=es[ts+ss[ps]]}if(ms>ws&&(Es&us)!==fs){for(_s===0&&(_s=ws),cs+=$s,Ss=ms-_s,ys=1<<Ss;Ss+_s<gs&&!((ys-=Cs[Ss+_s])<=0);)Ss++,ys<<=1;if(Ts+=1<<Ss,J===ct$1&&Ts>ei||J===ni&&Ts>ti)return 1;ns[fs=Es&us]=ws<<24|Ss<<16|cs-rs}}Es!==0&&(ns[cs+Es]=ms-_s<<24|64<<16),as.bits=ws}return 0};var Te$1=La;let Ua=0,ai=1,ri=2,{Z_FINISH:li,Z_BLOCK:Ca,Z_TREES:He$1,Z_OK:ae,Z_STREAM_END:$a,Z_NEED_DICT:Fa,Z_STREAM_ERROR:$$2,Z_DATA_ERROR:fi,Z_MEM_ERROR:oi,Z_BUF_ERROR:Ma,Z_DEFLATED:_i}=ee$2,Be=16180,hi=16181,di=16182,si=16183,ci=16184,ui=16185,wi=16186,bi=16187,gi=16188,pi=16189,Ke$1=16190,P$4=16191,ut$1=16192,xi=16193,wt$1=16194,vi=16195,ki=16196,Ei=16197,yi=16198,Pe$1=16199,Xe$1=16200,mi=16201,zi=16202,Si=16203,Ai=16204,Ri=16205,bt$1=16206,Ti=16207,Di=16208,R$1=16209,Zi=16210,Ii=16211,Ha=852,Ba=592,Ka=15,Pa=Ka,Oi=J=>(J>>>24&255)+(J>>>8&65280)+((65280&J)<<8)+((255&J)<<24);function Xa(){this.strm=null,this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new Uint16Array(320),this.work=new Uint16Array(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}let re$1=J=>{var es;return!J||!(es=J.state)||es.strm!==J||es.mode<Be||es.mode>Ii?1:0},Ni=J=>{var es;return re$1(J)?$$2:(es=J.state,J.total_in=J.total_out=es.total=0,J.msg="",es.wrap&&(J.adler=1&es.wrap),es.mode=Be,es.last=0,es.havedict=0,es.flags=-1,es.dmax=32768,es.head=null,es.hold=0,es.bits=0,es.lencode=es.lendyn=new Int32Array(Ha),es.distcode=es.distdyn=new Int32Array(Ba),es.sane=1,es.back=-1,ae)},Li=J=>{var es;return re$1(J)?$$2:((es=J.state).wsize=0,es.whave=0,es.wnext=0,Ni(J))},Ui=(J,es)=>{let ts;var is;return re$1(J)||(is=J.state,es<0?(ts=0,es=-es):(ts=5+(es>>4),es<48&&(es&=15)),es&&(es<8||15<es))?$$2:(is.window!==null&&is.wbits!==es&&(is.window=null),is.wrap=ts,is.wbits=es,Li(J))},Ci=(J,es)=>{var ts;return J?(ts=new Xa,(J.state=ts).strm=J,ts.window=null,ts.mode=Be,(ts=Ui(J,es))!==ae&&(J.state=null),ts):$$2},Ya=J=>Ci(J,Pa),$i=!0,gt$1,pt$1,Ga=J=>{if($i){gt$1=new Int32Array(512),pt$1=new Int32Array(32);let es=0;for(;es<144;)J.lens[es++]=8;for(;es<256;)J.lens[es++]=9;for(;es<280;)J.lens[es++]=7;for(;es<288;)J.lens[es++]=8;for(Te$1(ai,J.lens,0,288,gt$1,0,J.work,{bits:9}),es=0;es<32;)J.lens[es++]=5;Te$1(ri,J.lens,0,32,pt$1,0,J.work,{bits:5}),$i=!1}J.lencode=gt$1,J.lenbits=9,J.distcode=pt$1,J.distbits=5},Fi=(J,es,ts,is)=>{let ns;return J=J.state,J.window===null&&(J.wsize=1<<J.wbits,J.wnext=0,J.whave=0,J.window=new Uint8Array(J.wsize)),J.wsize<=is?(J.window.set(es.subarray(ts-J.wsize,ts),0),J.wnext=0,J.whave=J.wsize):((ns=J.wsize-J.wnext)>is&&(ns=is),J.window.set(es.subarray(ts-is,ts-is+ns),J.wnext),(is-=ns)?(J.window.set(es.subarray(ts-is,ts),0),J.wnext=is,J.whave=J.wsize):(J.wnext+=ns,J.wnext===J.wsize&&(J.wnext=0),J.whave<J.wsize&&(J.whave+=ns))),0},ja=(J,es)=>{let ts,is,ns,rs,ss,as,os,ds,ls,fs,us,cs,hs,ms,ps,$s,gs,ws,Ss,_s,ys,Ts,Es=0;var vs=new Uint8Array(4);let Cs,As;var Ns=new Uint8Array([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]);if(re$1(J)||!J.output||!J.input&&J.avail_in!==0)return $$2;(ts=J.state).mode===P$4&&(ts.mode=ut$1),ss=J.next_out,ns=J.output,os=J.avail_out,rs=J.next_in,is=J.input,as=J.avail_in,ds=ts.hold,ls=ts.bits,fs=as,us=os,Ts=ae;e:for(;;)switch(ts.mode){case Be:if(ts.wrap===0)ts.mode=ut$1;else{for(;ls<16;){if(as===0)break e;as--,ds+=is[rs++]<<ls,ls+=8}2&ts.wrap&&ds===35615?(ts.wbits===0&&(ts.wbits=15),vs[ts.check=0]=255&ds,vs[1]=ds>>>8&255,ts.check=Z$1(ts.check,vs,2,0),ds=0,ls=0,ts.mode=hi):(ts.head&&(ts.head.done=!1),!(1&ts.wrap)||(((255&ds)<<8)+(ds>>8))%31?(J.msg="incorrect header check",ts.mode=R$1):(15&ds)!==_i?(J.msg="unknown compression method",ts.mode=R$1):(ds>>>=4,ls-=4,ys=8+(15&ds),ts.wbits===0&&(ts.wbits=ys),15<ys||ys>ts.wbits?(J.msg="invalid window size",ts.mode=R$1):(ts.dmax=1<<ts.wbits,ts.flags=0,J.adler=ts.check=1,ts.mode=512&ds?pi:P$4,ds=0,ls=0)))}break;case hi:for(;ls<16;){if(as===0)break e;as--,ds+=is[rs++]<<ls,ls+=8}if(ts.flags=ds,(255&ts.flags)!==_i){J.msg="unknown compression method",ts.mode=R$1;break}if(57344&ts.flags){J.msg="unknown header flags set",ts.mode=R$1;break}ts.head&&(ts.head.text=ds>>8&1),512&ts.flags&&4&ts.wrap&&(vs[0]=255&ds,vs[1]=ds>>>8&255,ts.check=Z$1(ts.check,vs,2,0)),ds=0,ls=0,ts.mode=di;case di:for(;ls<32;){if(as===0)break e;as--,ds+=is[rs++]<<ls,ls+=8}ts.head&&(ts.head.time=ds),512&ts.flags&&4&ts.wrap&&(vs[0]=255&ds,vs[1]=ds>>>8&255,vs[2]=ds>>>16&255,vs[3]=ds>>>24&255,ts.check=Z$1(ts.check,vs,4,0)),ds=0,ls=0,ts.mode=si;case si:for(;ls<16;){if(as===0)break e;as--,ds+=is[rs++]<<ls,ls+=8}ts.head&&(ts.head.xflags=255&ds,ts.head.os=ds>>8),512&ts.flags&&4&ts.wrap&&(vs[0]=255&ds,vs[1]=ds>>>8&255,ts.check=Z$1(ts.check,vs,2,0)),ds=0,ls=0,ts.mode=ci;case ci:if(1024&ts.flags){for(;ls<16;){if(as===0)break e;as--,ds+=is[rs++]<<ls,ls+=8}ts.length=ds,ts.head&&(ts.head.extra_len=ds),512&ts.flags&&4&ts.wrap&&(vs[0]=255&ds,vs[1]=ds>>>8&255,ts.check=Z$1(ts.check,vs,2,0)),ds=0,ls=0}else ts.head&&(ts.head.extra=null);ts.mode=ui;case ui:if(1024&ts.flags&&((cs=(cs=ts.length)>as?as:cs)&&(ts.head&&(ys=ts.head.extra_len-ts.length,ts.head.extra||(ts.head.extra=new Uint8Array(ts.head.extra_len)),ts.head.extra.set(is.subarray(rs,rs+cs),ys)),512&ts.flags&&4&ts.wrap&&(ts.check=Z$1(ts.check,is,cs,rs)),as-=cs,rs+=cs,ts.length-=cs),ts.length))break e;ts.length=0,ts.mode=wi;case wi:if(2048&ts.flags){if(as===0)break e;for(cs=0;ys=is[rs+cs++],ts.head&&ys&&ts.length<65536&&(ts.head.name+=String.fromCharCode(ys)),ys&&cs<as;);if(512&ts.flags&&4&ts.wrap&&(ts.check=Z$1(ts.check,is,cs,rs)),as-=cs,rs+=cs,ys)break e}else ts.head&&(ts.head.name=null);ts.length=0,ts.mode=bi;case bi:if(4096&ts.flags){if(as===0)break e;for(cs=0;ys=is[rs+cs++],ts.head&&ys&&ts.length<65536&&(ts.head.comment+=String.fromCharCode(ys)),ys&&cs<as;);if(512&ts.flags&&4&ts.wrap&&(ts.check=Z$1(ts.check,is,cs,rs)),as-=cs,rs+=cs,ys)break e}else ts.head&&(ts.head.comment=null);ts.mode=gi;case gi:if(512&ts.flags){for(;ls<16;){if(as===0)break e;as--,ds+=is[rs++]<<ls,ls+=8}if(4&ts.wrap&&ds!==(65535&ts.check)){J.msg="header crc mismatch",ts.mode=R$1;break}ds=0,ls=0}ts.head&&(ts.head.hcrc=ts.flags>>9&1,ts.head.done=!0),J.adler=ts.check=0,ts.mode=P$4;break;case pi:for(;ls<32;){if(as===0)break e;as--,ds+=is[rs++]<<ls,ls+=8}J.adler=ts.check=Oi(ds),ds=0,ls=0,ts.mode=Ke$1;case Ke$1:if(ts.havedict===0)return J.next_out=ss,J.avail_out=os,J.next_in=rs,J.avail_in=as,ts.hold=ds,ts.bits=ls,Fa;J.adler=ts.check=1,ts.mode=P$4;case P$4:if(es===Ca||es===He$1)break e;case ut$1:if(ts.last)ds>>>=7&ls,ls-=7&ls,ts.mode=bt$1;else{for(;ls<3;){if(as===0)break e;as--,ds+=is[rs++]<<ls,ls+=8}switch(ts.last=1&ds,ds>>>=1,--ls,3&ds){case 0:ts.mode=xi;break;case 1:if(Ga(ts),ts.mode=Pe$1,es!==He$1)break;ds>>>=2,ls-=2;break e;case 2:ts.mode=ki;break;case 3:J.msg="invalid block type",ts.mode=R$1}ds>>>=2,ls-=2}break;case xi:for(ds>>>=7&ls,ls-=7&ls;ls<32;){if(as===0)break e;as--,ds+=is[rs++]<<ls,ls+=8}if((65535&ds)!=(ds>>>16^65535)){J.msg="invalid stored block lengths",ts.mode=R$1;break}if(ts.length=65535&ds,ds=0,ls=0,ts.mode=wt$1,es===He$1)break e;case wt$1:ts.mode=vi;case vi:if(cs=ts.length){if((cs=(cs=cs>as?as:cs)>os?os:cs)===0)break e;ns.set(is.subarray(rs,rs+cs),ss),as-=cs,rs+=cs,os-=cs,ss+=cs,ts.length-=cs}else ts.mode=P$4;break;case ki:for(;ls<14;){if(as===0)break e;as--,ds+=is[rs++]<<ls,ls+=8}if(ts.nlen=257+(31&ds),ds>>>=5,ls-=5,ts.ndist=1+(31&ds),ds>>>=5,ls-=5,ts.ncode=4+(15&ds),ds>>>=4,ls-=4,286<ts.nlen||30<ts.ndist){J.msg="too many length or distance symbols",ts.mode=R$1;break}ts.have=0,ts.mode=Ei;case Ei:for(;ts.have<ts.ncode;){for(;ls<3;){if(as===0)break e;as--,ds+=is[rs++]<<ls,ls+=8}ts.lens[Ns[ts.have++]]=7&ds,ds>>>=3,ls-=3}for(;ts.have<19;)ts.lens[Ns[ts.have++]]=0;if(ts.lencode=ts.lendyn,ts.lenbits=7,Cs={bits:ts.lenbits},Ts=Te$1(Ua,ts.lens,0,19,ts.lencode,0,ts.work,Cs),ts.lenbits=Cs.bits,Ts){J.msg="invalid code lengths set",ts.mode=R$1;break}ts.have=0,ts.mode=yi;case yi:for(;ts.have<ts.nlen+ts.ndist;){for(;Es=ts.lencode[ds&(1<<ts.lenbits)-1],ps=Es>>>24,$s=Es>>>16&255,gs=65535&Es,!(ps<=ls);){if(as===0)break e;as--,ds+=is[rs++]<<ls,ls+=8}if(gs<16)ds>>>=ps,ls-=ps,ts.lens[ts.have++]=gs;else{if(gs===16){for(As=ps+2;ls<As;){if(as===0)break e;as--,ds+=is[rs++]<<ls,ls+=8}if(ds>>>=ps,ls-=ps,ts.have===0){J.msg="invalid bit length repeat",ts.mode=R$1;break}ys=ts.lens[ts.have-1],cs=3+(3&ds),ds>>>=2,ls-=2}else if(gs===17){for(As=ps+3;ls<As;){if(as===0)break e;as--,ds+=is[rs++]<<ls,ls+=8}ds>>>=ps,ls-=ps,ys=0,cs=3+(7&ds),ds>>>=3,ls-=3}else{for(As=ps+7;ls<As;){if(as===0)break e;as--,ds+=is[rs++]<<ls,ls+=8}ds>>>=ps,ls-=ps,ys=0,cs=11+(127&ds),ds>>>=7,ls-=7}if(ts.have+cs>ts.nlen+ts.ndist){J.msg="invalid bit length repeat",ts.mode=R$1;break}for(;cs--;)ts.lens[ts.have++]=ys}}if(ts.mode===R$1)break;if(ts.lens[256]===0){J.msg="invalid code -- missing end-of-block",ts.mode=R$1;break}if(ts.lenbits=9,Cs={bits:ts.lenbits},Ts=Te$1(ai,ts.lens,0,ts.nlen,ts.lencode,0,ts.work,Cs),ts.lenbits=Cs.bits,Ts){J.msg="invalid literal/lengths set",ts.mode=R$1;break}if(ts.distbits=6,ts.distcode=ts.distdyn,Cs={bits:ts.distbits},Ts=Te$1(ri,ts.lens,ts.nlen,ts.ndist,ts.distcode,0,ts.work,Cs),ts.distbits=Cs.bits,Ts){J.msg="invalid distances set",ts.mode=R$1;break}if(ts.mode=Pe$1,es===He$1)break e;case Pe$1:ts.mode=Xe$1;case Xe$1:if(6<=as&&258<=os){J.next_out=ss,J.avail_out=os,J.next_in=rs,J.avail_in=as,ts.hold=ds,ts.bits=ls,Da(J,us),ss=J.next_out,ns=J.output,os=J.avail_out,rs=J.next_in,is=J.input,as=J.avail_in,ds=ts.hold,ls=ts.bits,ts.mode===P$4&&(ts.back=-1);break}for(ts.back=0;Es=ts.lencode[ds&(1<<ts.lenbits)-1],ps=Es>>>24,$s=Es>>>16&255,gs=65535&Es,!(ps<=ls);){if(as===0)break e;as--,ds+=is[rs++]<<ls,ls+=8}if($s&&!(240&$s)){for(ws=ps,Ss=$s,_s=gs;Es=ts.lencode[_s+((ds&(1<<ws+Ss)-1)>>ws)],ps=Es>>>24,$s=Es>>>16&255,gs=65535&Es,!(ws+ps<=ls);){if(as===0)break e;as--,ds+=is[rs++]<<ls,ls+=8}ds>>>=ws,ls-=ws,ts.back+=ws}if(ds>>>=ps,ls-=ps,ts.back+=ps,ts.length=gs,$s===0){ts.mode=Ri;break}if(32&$s){ts.back=-1,ts.mode=P$4;break}if(64&$s){J.msg="invalid literal/length code",ts.mode=R$1;break}ts.extra=15&$s,ts.mode=mi;case mi:if(ts.extra){for(As=ts.extra;ls<As;){if(as===0)break e;as--,ds+=is[rs++]<<ls,ls+=8}ts.length+=ds&(1<<ts.extra)-1,ds>>>=ts.extra,ls-=ts.extra,ts.back+=ts.extra}ts.was=ts.length,ts.mode=zi;case zi:for(;Es=ts.distcode[ds&(1<<ts.distbits)-1],ps=Es>>>24,$s=Es>>>16&255,gs=65535&Es,!(ps<=ls);){if(as===0)break e;as--,ds+=is[rs++]<<ls,ls+=8}if(!(240&$s)){for(ws=ps,Ss=$s,_s=gs;Es=ts.distcode[_s+((ds&(1<<ws+Ss)-1)>>ws)],ps=Es>>>24,$s=Es>>>16&255,gs=65535&Es,!(ws+ps<=ls);){if(as===0)break e;as--,ds+=is[rs++]<<ls,ls+=8}ds>>>=ws,ls-=ws,ts.back+=ws}if(ds>>>=ps,ls-=ps,ts.back+=ps,64&$s){J.msg="invalid distance code",ts.mode=R$1;break}ts.offset=gs,ts.extra=15&$s,ts.mode=Si;case Si:if(ts.extra){for(As=ts.extra;ls<As;){if(as===0)break e;as--,ds+=is[rs++]<<ls,ls+=8}ts.offset+=ds&(1<<ts.extra)-1,ds>>>=ts.extra,ls-=ts.extra,ts.back+=ts.extra}if(ts.offset>ts.dmax){J.msg="invalid distance too far back",ts.mode=R$1;break}ts.mode=Ai;case Ai:if(os===0)break e;if(cs=us-os,ts.offset>cs){if((cs=ts.offset-cs)>ts.whave&&ts.sane){J.msg="invalid distance too far back",ts.mode=R$1;break}hs=cs>ts.wnext?(cs-=ts.wnext,ts.wsize-cs):ts.wnext-cs,cs>ts.length&&(cs=ts.length),ms=ts.window}else ms=ns,hs=ss-ts.offset,cs=ts.length;for(cs>os&&(cs=os),os-=cs,ts.length-=cs;ns[ss++]=ms[hs++],--cs;);ts.length===0&&(ts.mode=Xe$1);break;case Ri:if(os===0)break e;ns[ss++]=ts.length,os--,ts.mode=Xe$1;break;case bt$1:if(ts.wrap){for(;ls<32;){if(as===0)break e;as--,ds|=is[rs++]<<ls,ls+=8}if(us-=os,J.total_out+=us,ts.total+=us,4&ts.wrap&&us&&(J.adler=ts.check=(ts.flags?Z$1:ve$1)(ts.check,ns,us,ss-us)),us=os,4&ts.wrap&&(ts.flags?ds:Oi(ds))!==ts.check){J.msg="incorrect data check",ts.mode=R$1;break}ds=0,ls=0}ts.mode=Ti;case Ti:if(ts.wrap&&ts.flags){for(;ls<32;){if(as===0)break e;as--,ds+=is[rs++]<<ls,ls+=8}if(4&ts.wrap&&ds!==(4294967295&ts.total)){J.msg="incorrect length check",ts.mode=R$1;break}ds=0,ls=0}ts.mode=Di;case Di:Ts=$a;break e;case R$1:Ts=fi;break e;case Zi:return oi;default:return $$2}return J.next_out=ss,J.avail_out=os,J.next_in=rs,J.avail_in=as,ts.hold=ds,ts.bits=ls,(ts.wsize||us!==J.avail_out&&ts.mode<R$1&&(ts.mode<bt$1||es!==li))&&Fi(J,J.output,J.next_out,us-J.avail_out),fs-=J.avail_in,us-=J.avail_out,J.total_in+=fs,J.total_out+=us,ts.total+=us,4&ts.wrap&&us&&(J.adler=ts.check=(ts.flags?Z$1:ve$1)(ts.check,ns,us,J.next_out-us)),J.data_type=ts.bits+(ts.last?64:0)+(ts.mode===P$4?128:0)+(ts.mode===Pe$1||ts.mode===wt$1?256:0),Ts=(fs==0&&us===0||es===li)&&Ts===ae?Ma:Ts},Wa=J=>{var es;return re$1(J)?$$2:((es=J.state).window&&(es.window=null),J.state=null,ae)},Va=(J,es)=>!re$1(J)&&2&(J=J.state).wrap?((J.head=es).done=!1,ae):$$2,Ja=(J,es)=>{var ts=es.length;let is;return re$1(J)||(is=J.state).wrap!==0&&is.mode!==Ke$1?$$2:is.mode===Ke$1&&ve$1(1,es,ts,0)!==is.check?fi:Fi(J,es,ts,ts)?(is.mode=Zi,oi):(is.havedict=1,ae)};var Qa=Li,qa=Ui,er$1=Ni,tr$1=Ya,ir$1=Ci,nr$1=ja,ar$1=Wa,rr$1=Va,lr$1=Ja,fr$1="pako inflate (from Nodeca project)",X$1={inflateReset:Qa,inflateReset2:qa,inflateResetKeep:er$1,inflateInit:tr$1,inflateInit2:ir$1,inflate:nr$1,inflateEnd:ar$1,inflateGetHeader:rr$1,inflateSetDictionary:lr$1,inflateInfo:fr$1};function or$1(){this.text=0,this.time=0,this.xflags=0,this.os=0,this.extra=null,this.extra_len=0,this.name="",this.comment="",this.hcrc=0,this.done=!1}var _r$1=or$1;let Mi=Object.prototype.toString,{Z_NO_FLUSH:hr$1,Z_FINISH:dr$1,Z_OK:De$1,Z_STREAM_END:xt$1,Z_NEED_DICT:vt$1,Z_STREAM_ERROR:sr$1,Z_DATA_ERROR:Hi,Z_MEM_ERROR:cr$1}=ee$2;function Ze$1(J){this.options=$e$1.assign({chunkSize:65536,windowBits:15,to:""},J||{});var es=this.options;es.raw&&0<=es.windowBits&&es.windowBits<16&&(es.windowBits=-es.windowBits,es.windowBits===0)&&(es.windowBits=-15),!(0<=es.windowBits&&es.windowBits<16)||J&&J.windowBits||(es.windowBits+=32),15<es.windowBits&&es.windowBits<48&&(15&es.windowBits||(es.windowBits|=15)),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new Qt$1,this.strm.avail_out=0;let ts=X$1.inflateInit2(this.strm,es.windowBits);if(ts!==De$1)throw new Error(q$4[ts]);if(this.header=new _r$1,X$1.inflateGetHeader(this.strm,this.header),es.dictionary&&(typeof es.dictionary=="string"?es.dictionary=Ae$1.string2buf(es.dictionary):Mi.call(es.dictionary)==="[object ArrayBuffer]"&&(es.dictionary=new Uint8Array(es.dictionary)),es.raw)&&(ts=X$1.inflateSetDictionary(this.strm,es.dictionary))!==De$1)throw new Error(q$4[ts])}function kt$1(J,es){if(es=new Ze$1(es),es.push(J),es.err)throw es.msg||q$4[es.err];return es.result}function ur$1(J,es){return(es=es||{}).raw=!0,kt$1(J,es)}Ze$1.prototype.push=function(J,es){var ts,is,ns,rs=this.strm,ss=this.options.chunkSize,as=this.options.dictionary;let os,ds,ls;if(this.ended)return!1;for(ds=es===~~es?es:es===!0?dr$1:hr$1,Mi.call(J)==="[object ArrayBuffer]"?rs.input=new Uint8Array(J):rs.input=J,rs.next_in=0,rs.avail_in=rs.input.length;;){for(rs.avail_out===0&&(rs.output=new Uint8Array(ss),rs.next_out=0,rs.avail_out=ss),(os=X$1.inflate(rs,ds))===vt$1&&as&&((os=X$1.inflateSetDictionary(rs,as))===De$1?os=X$1.inflate(rs,ds):os===Hi&&(os=vt$1));0<rs.avail_in&&os===xt$1&&0<rs.state.wrap&&J[rs.next_in]!==0;)X$1.inflateReset(rs),os=X$1.inflate(rs,ds);switch(os){case sr$1:case Hi:case vt$1:case cr$1:return this.onEnd(os),!(this.ended=!0)}if(ls=rs.avail_out,!rs.next_out||rs.avail_out!==0&&os!==xt$1||(this.options.to==="string"?(ts=Ae$1.utf8border(rs.output,rs.next_out),is=rs.next_out-ts,ns=Ae$1.buf2string(rs.output,ts),rs.next_out=is,rs.avail_out=ss-is,is&&rs.output.set(rs.output.subarray(ts,ts+is),0),this.onData(ns)):this.onData(rs.output.length===rs.next_out?rs.output:rs.output.subarray(0,rs.next_out))),os!==De$1||ls!==0){if(os===xt$1)return os=X$1.inflateEnd(this.strm),this.onEnd(os),this.ended=!0;if(rs.avail_in===0)break}}return!0},Ze$1.prototype.onData=function(J){this.chunks.push(J)},Ze$1.prototype.onEnd=function(J){J===De$1&&(this.options.to==="string"?this.result=this.chunks.join(""):this.result=$e$1.flattenChunks(this.chunks)),this.chunks=[],this.err=J,this.msg=this.strm.msg};var wr$1=Ze$1,br$1=kt$1,gr$1=ur$1,pr$1=kt$1,xr$1=ee$2,vr$1={Inflate:wr$1,inflate:br$1,inflateRaw:gr$1,ungzip:pr$1,constants:xr$1};let{Deflate:kr$1,deflate:Er$1,deflateRaw:yr$1,gzip:mr$1}=Ra,{Inflate:zr$1,inflate:Sr$1,inflateRaw:Ar$1,ungzip:Rr$1}=vr$1;var Tr$1=kr$1,Dr$1=Er$1,Zr$1=yr$1,Ir$1=mr$1,Or$1=zr$1,Nr$1=Sr$1,Lr$1=Ar$1,Ur$1=Rr$1,Cr$1=ee$2,$r$1={Deflate:Tr$1,deflate:Dr$1,deflateRaw:Zr$1,gzip:Ir$1,Inflate:Or$1,inflate:Nr$1,inflateRaw:Lr$1,ungzip:Ur$1,constants:Cr$1},q$3=[],se=0,be="",x$4;function Kt(J){return J.nodeType===J.ELEMENT_NODE}function we(J){var es=J==null?void 0:J.host;return(es==null?void 0:es.shadowRoot)===J}function Me(J){return Object.prototype.toString.call(J)==="[object ShadowRoot]"}function Jt(J){return J=J.includes(" background-clip: text;")&&!J.includes(" -webkit-background-clip: text;")?J.replace(" background-clip: text;"," -webkit-background-clip: text; background-clip: text;"):J}function He(J){try{var es=J.rules||J.cssRules;return es?Jt(Array.from(es).map(et).join("")):null}catch(ts){return null}}function et(J){var es=J.cssText;if(Qt(J))try{es=He(J.styleSheet)||es}catch(ts){}return es}function Qt(J){return"styleSheet"in J}(function(J){J[J.Document=0]="Document",J[J.DocumentType=1]="DocumentType",J[J.Element=2]="Element",J[J.Text=3]="Text",J[J.CDATA=4]="CDATA",J[J.Comment=5]="Comment"})(x$4=x$4||{});var tt=function(){function J(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap}return J.prototype.getId=function(es){return es&&(es=(es=this.getMeta(es))==null?void 0:es.id)!=null?es:-1},J.prototype.getNode=function(es){return this.idNodeMap.get(es)||null},J.prototype.getIds=function(){return Array.from(this.idNodeMap.keys())},J.prototype.getMeta=function(es){return this.nodeMetaMap.get(es)||null},J.prototype.removeNodeFromMap=function(es){var ts=this,is=this.getId(es);this.idNodeMap.delete(is),es.childNodes&&es.childNodes.forEach(function(ns){return ts.removeNodeFromMap(ns)})},J.prototype.has=function(es){return this.idNodeMap.has(es)},J.prototype.hasNode=function(es){return this.nodeMetaMap.has(es)},J.prototype.add=function(es,ts){var is=ts.id;this.idNodeMap.set(is,es),this.nodeMetaMap.set(es,ts)},J.prototype.replace=function(es,ts){var is=this.getNode(es);is&&(is=this.nodeMetaMap.get(is))&&this.nodeMetaMap.set(ts,is),this.idNodeMap.set(es,ts)},J.prototype.reset=function(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap},J}();function $t(){return new tt}function Ue(rs){var es=rs.element,ts=rs.maskInputOptions,is=rs.tagName,ss=rs.type,ns=rs.maskInputFn,rs=rs.value||"",ss=ss&&ss.toLowerCase();return rs=ts[is.toLowerCase()]||ss&&ts[ss]?ns?ns(rs,es):"*".repeat(rs.length):rs}var rt="__rrweb_original__";function qt(J){var es=J.getContext("2d");if(es)for(var ts=0;ts<J.width;ts+=50)for(var is=0;is<J.height;is+=50){var ns=es.getImageData,ns=rt in ns?ns[rt]:ns;if(new Uint32Array(ns.call(es,ts,is,Math.min(50,J.width-ts),Math.min(50,J.height-is)).data.buffer).some(function(rs){return rs!==0}))return}return 1}function Ye(J){var es=J.type;return J.hasAttribute("data-rr-is-password")?"password":es?es.toLowerCase():null}var er=1,tr=new RegExp("[^a-z0-9-_:]"),Ae=-2;function nt(){return er++}function rr(J){return J instanceof HTMLFormElement?"form":(J=J.tagName.toLowerCase().trim(),tr.test(J)?"div":J)}function nr(J){return J.cssRules?Array.from(J.cssRules).map(function(es){return es.cssText||""}).join(""):""}function or(J){return(-1<J.indexOf("//")?J.split("/").slice(0,3).join("/"):J.split("/")[0]).split("?")[0]}var me,ot,ar=/url\((?:(')([^']*)'|(")(.*?)"|([^)]*))\)/gm,ir=/^(?:[a-z+]+:)?\/\//i,sr=/^www\..*/i,lr=/^(data:)([^,]*),(.*)/i;function _e(J,es){return(J||"").replace(ar,function(ts,is,ns,rs,ss,ds){if(ns=ns||ss||ds,ss=is||rs||"",!ns)return ts;if(ir.test(ns)||sr.test(ns)||lr.test(ns))return"url(".concat(ss).concat(ns).concat(ss,")");if(ns[0]==="/")return"url(".concat(ss).concat(or(es)+ns).concat(ss,")");var os=es.split("/"),ds=ns.split("/");os.pop();for(var ls=0,fs=ds;ls<fs.length;ls++){var us=fs[ls];us!=="."&&(us===".."?os.pop():os.push(us))}return"url(".concat(ss).concat(os.join("/")).concat(ss,")")})}var cr=/^[^ \t\n\r\u000c]+/,dr=/^[, \t\n\r\u000c]+/;function ur(J,es){if(es.trim()==="")return es;var ts=0;function is(ls){var ls=ls.exec(es.substring(ts));return ls?(ls=ls[0],ts+=ls.length,ls):""}for(var ns=[];is(dr),!(ts>=es.length);)if((ss=is(cr)).slice(-1)===",")ss=ge(J,ss.substring(0,ss.length-1)),ns.push(ss);else for(var rs="",ss=ge(J,ss),as=!1;;){var os=es.charAt(ts);if(os===""){ns.push((ss+rs).trim());break}if(as)os===")"&&(as=!1);else{if(os===","){ts+=1,ns.push((ss+rs).trim());break}os==="("&&(as=!0)}rs+=os,ts+=1}return ns.join(", ")}function ge(J,es){return es&&es.trim()!==""?((J=J.createElement("a")).href=es,J.href):es}function pr(J){return!(J.tagName!=="svg"&&!J.ownerSVGElement)}function ze(){var J=document.createElement("a");return J.href="",J.href}function at(J,es,ts,is){return is&&(ts==="src"||ts==="href"&&(es!=="use"||is[0]!=="#")||ts==="xlink:href"&&is[0]!=="#"||ts==="background"&&(es==="table"||es==="td"||es==="th")?ge(J,is):ts==="srcset"?ur(J,is):ts==="style"?_e(is,ze()):es==="object"&&ts==="data"?ge(J,is):is)}function it(J,es,ts){return(J==="video"||J==="audio")&&es==="autoplay"}function hr(J,es,ts){try{if(typeof es=="string"){if(J.classList.contains(es))return!0}else for(var is=J.classList.length;is--;){var ns=J.classList[is];if(es.test(ns))return!0}if(ts)return J.matches(ts)}catch(rs){}return!1}function Le(J,es,ts){if(!J)return!1;if(J.nodeType===J.ELEMENT_NODE)for(var is=J.classList.length;is--;){var ns=J.classList[is];if(es.test(ns))return!0}return!!ts&&Le(J.parentNode,es,ts)}function st(J,es,ts){try{var is=J.nodeType===J.ELEMENT_NODE?J:J.parentElement;if(is===null)return;if(typeof es=="string"){if(is.classList.contains(es)||is.closest(".".concat(es)))return 1}else if(Le(is,es,!0))return 1;if(ts&&(is.matches(ts)||is.closest(ts)))return 1}catch(ns){}}function fr(J,es,ts){var is=J.contentWindow;if(is){var ns,rs,ss=!1;try{rs=is.document.readyState}catch(as){return}return rs!=="complete"?(ns=setTimeout(function(){ss||(es(),ss=!0)},ts),J.addEventListener("load",function(){clearTimeout(ns),ss=!0,es()})):is.location.href!==(rs="about:blank")||J.src===rs||J.src===""?(setTimeout(es,0),J.addEventListener("load",es)):void J.addEventListener("load",es)}}function mr(J,es,ts){var is,ns,rs=!1;try{is=J.sheet}catch(ss){return}is||(ns=setTimeout(function(){rs||(es(),rs=!0)},ts),J.addEventListener("load",function(){clearTimeout(ns),rs=!0,es()}))}function gr(J,es){var ts=es.doc,is=es.blockClass,ns=es.blockSelector,rs=es.maskTextClass,ss=es.maskTextSelector,as=es.inlineStylesheet,ms=es.maskInputOptions,os=ms===void 0?{}:ms,ds=es.maskTextFn,ls=es.maskInputFn,ms=es.dataURLOptions,fs=ms===void 0?{}:ms,us=es.inlineImages,cs=es.recordCanvas,hs=es.keepIframeSrcFn,ms=es.newlyAddedElement,ps=ms!==void 0&&ms,$s=yr(ts,es.mirror);switch(J.nodeType){case J.DOCUMENT_NODE:return J.compatMode!=="CSS1Compat"?{type:x$4.Document,childNodes:[],compatMode:J.compatMode}:{type:x$4.Document,childNodes:[]};case J.DOCUMENT_TYPE_NODE:return{type:x$4.DocumentType,name:J.name,publicId:J.publicId,systemId:J.systemId,rootId:$s};case J.ELEMENT_NODE:return Cr(J,{doc:ts,blockClass:is,blockSelector:ns,inlineStylesheet:as,maskInputOptions:os,maskInputFn:ls,dataURLOptions:fs,inlineImages:us,recordCanvas:cs,keepIframeSrcFn:hs,newlyAddedElement:ps,rootId:$s});case J.TEXT_NODE:return Ir(J,{maskTextClass:rs,maskTextSelector:ss,maskTextFn:ds,rootId:$s});case J.CDATA_SECTION_NODE:return{type:x$4.CDATA,textContent:"",rootId:$s};case J.COMMENT_NODE:return{type:x$4.Comment,textContent:J.textContent||"",rootId:$s};default:return!1}}function yr(J,es){return!es.hasNode(J)||(es=es.getId(J))===1?void 0:es}function Ir(J,es){var ts,is=es.maskTextClass,ns=es.maskTextSelector,rs=es.maskTextFn,ss=es.rootId,ds=J.parentNode&&J.parentNode.tagName,as=J.textContent,os=ds==="STYLE"||void 0,ds=ds==="SCRIPT"||void 0;if(os&&as){try{J.nextSibling||J.previousSibling||(ts=J.parentNode.sheet)!=null&&ts.cssRules&&(as=nr(J.parentNode.sheet))}catch(ls){console.warn("Cannot get CSS styles from text's parentNode. Error: ".concat(ls),J)}as=_e(as,ze())}return ds&&(as="SCRIPT_PLACEHOLDER"),!os&&!ds&&as&&st(J,is,ns)&&(as=rs?rs(as):as.replace(/[\S]/g,"*")),{type:x$4.Text,textContent:as||"",isStyle:os,rootId:ss}}function Cr(J,es){for(var ts,is,ns,rs,ss,as=es.doc,os=es.inlineStylesheet,ds=es.maskInputOptions,ds=ds===void 0?{}:ds,ls=es.maskInputFn,us=es.dataURLOptions,fs=us===void 0?{}:us,us=es.inlineImages,cs=es.recordCanvas,hs=es.keepIframeSrcFn,ms=es.newlyAddedElement,ms=ms!==void 0&&ms,ps=es.rootId,es=hr(J,es.blockClass,es.blockSelector),$s=rr(J),gs={},ws=J.attributes.length,Ss=0;Ss<ws;Ss++){var _s=J.attributes[Ss];it($s,_s.name,_s.value)||(gs[_s.name]=at(as,$s,_s.name,_s.value))}return $s==="link"&&os&&(ss=null,ss=(os=Array.from(as.styleSheets).find(function(ys){return ys.href===J.href}))?He(os):ss)&&(delete gs.rel,delete gs.href,gs._cssText=_e(ss,os.href)),$s==="style"&&J.sheet&&!(J.innerText||J.textContent||"").trim().length&&(ss=He(J.sheet))&&(gs._cssText=_e(ss,ze())),$s!=="input"&&$s!=="textarea"&&$s!=="select"||(os=J.value,ss=J.checked,gs.type!=="radio"&&gs.type!=="checkbox"&&gs.type!=="submit"&&gs.type!=="button"&&os?(ts=Ye(J),gs.value=Ue({element:J,type:ts,tagName:$s,value:os,maskInputOptions:ds,maskInputFn:ls})):ss&&(gs.checked=ss)),$s==="option"&&(J.selected&&!ds.select?gs.selected=!0:delete gs.selected),$s==="canvas"&&cs&&(J.__context==="2d"?qt(J)||(gs.rr_dataURL=J.toDataURL(fs.type,fs.quality)):"__context"in J||(ts=J.toDataURL(fs.type,fs.quality),(os=document.createElement("canvas")).width=J.width,os.height=J.height,ts!==os.toDataURL(fs.type,fs.quality)&&(gs.rr_dataURL=ts))),$s==="img"&&us&&(me||(me=as.createElement("canvas"),ot=me.getContext("2d")),ns=(is=J).crossOrigin,is.crossOrigin="anonymous",rs=function(){is.removeEventListener("load",rs);try{me.width=is.naturalWidth,me.height=is.naturalHeight,ot.drawImage(is,0,0),gs.rr_dataURL=me.toDataURL(fs.type,fs.quality)}catch(ys){console.warn("Cannot inline img src=".concat(is.currentSrc,"! Error: ").concat(ys))}ns?gs.crossOrigin=ns:is.removeAttribute("crossorigin")},is.complete&&is.naturalWidth!==0?rs():is.addEventListener("load",rs)),$s!=="audio"&&$s!=="video"||(gs.rr_mediaState=J.paused?"paused":"played",gs.rr_mediaCurrentTime=J.currentTime),ms||(J.scrollLeft&&(gs.rr_scrollLeft=J.scrollLeft),J.scrollTop&&(gs.rr_scrollTop=J.scrollTop)),es&&(ss=(ls=J.getBoundingClientRect()).width,ds=ls.height,gs={class:gs.class,rr_width:"".concat(ss,"px"),rr_height:"".concat(ds,"px")}),$s!=="iframe"||hs(gs.src)||(J.contentDocument||(gs.rr_src=gs.src),delete gs.src),{type:x$4.Element,tagName:$s,attributes:gs,childNodes:[],isSVG:pr(J)||void 0,needBlock:es,rootId:ps}}function A$6(J){return J==null?"":J.toLowerCase()}function vr(J,es){if(es.comment&&J.type===x$4.Comment||J.type===x$4.Element&&(es.script&&(J.tagName==="script"||J.tagName==="link"&&(J.attributes.rel==="preload"||J.attributes.rel==="modulepreload")&&J.attributes.as==="script"||J.tagName==="link"&&J.attributes.rel==="prefetch"&&typeof J.attributes.href=="string"&&J.attributes.href.endsWith(".js"))||es.headFavicon&&(J.tagName==="link"&&J.attributes.rel==="shortcut icon"||J.tagName==="meta"&&(A$6(J.attributes.name).match(/^msapplication-tile(image|color)$/)||A$6(J.attributes.name)==="application-name"||A$6(J.attributes.rel)==="icon"||A$6(J.attributes.rel)==="apple-touch-icon"||A$6(J.attributes.rel)==="shortcut icon"))||J.tagName==="meta"&&(es.headMetaDescKeywords&&A$6(J.attributes.name).match(/^description|keywords$/)||es.headMetaSocial&&(A$6(J.attributes.property).match(/^(og|twitter|fb):/)||A$6(J.attributes.name).match(/^(og|twitter):/)||A$6(J.attributes.name)==="pinterest")||es.headMetaRobots&&(A$6(J.attributes.name)==="robots"||A$6(J.attributes.name)==="googlebot"||A$6(J.attributes.name)==="bingbot")||es.headMetaHttpEquiv&&J.attributes["http-equiv"]!==void 0||es.headMetaAuthorship&&(A$6(J.attributes.name)==="author"||A$6(J.attributes.name)==="generator"||A$6(J.attributes.name)==="framework"||A$6(J.attributes.name)==="publisher"||A$6(J.attributes.name)==="progid"||A$6(J.attributes.property).match(/^article:/)||A$6(J.attributes.property).match(/^product:/))||es.headMetaVerification&&(A$6(J.attributes.name)==="google-site-verification"||A$6(J.attributes.name)==="yandex-verification"||A$6(J.attributes.name)==="csrf-token"||A$6(J.attributes.name)==="p:domain_verify"||A$6(J.attributes.name)==="verify-v1"||A$6(J.attributes.name)==="verification"||A$6(J.attributes.name)==="shopify-checkout-api-token"))))return 1}function ye(J,Es){var ts=Es.doc,is=Es.mirror,ns=Es.blockClass,rs=Es.blockSelector,ss=Es.maskTextClass,as=Es.maskTextSelector,os=Es.skipChild,os=os!==void 0&&os,vs=Es.inlineStylesheet,ds=vs===void 0||vs,vs=Es.maskInputOptions,ls=vs===void 0?{}:vs,fs=Es.maskTextFn,us=Es.maskInputFn,cs=Es.slimDOMOptions,vs=Es.dataURLOptions,hs=vs===void 0?{}:vs,vs=Es.inlineImages,ms=vs!==void 0&&vs,vs=Es.recordCanvas,ps=vs!==void 0&&vs,$s=Es.onSerialize,gs=Es.onIframeLoad,vs=Es.iframeLoadTimeout,ws=vs===void 0?5e3:vs,Ss=Es.onStylesheetLoad,vs=Es.stylesheetLoadTimeout,_s=vs===void 0?5e3:vs,vs=Es.keepIframeSrcFn,ys=vs===void 0?function(){return!1}:vs,vs=Es.newlyAddedElement,Es=Es.preserveWhiteSpace,Ts=Es===void 0||Es,Es=gr(J,{doc:ts,mirror:is,blockClass:ns,blockSelector:rs,maskTextClass:ss,maskTextSelector:as,inlineStylesheet:ds,maskInputOptions:ls,maskTextFn:fs,maskInputFn:us,dataURLOptions:hs,inlineImages:ms,recordCanvas:ps,keepIframeSrcFn:ys,newlyAddedElement:vs!==void 0&&vs});if(!Es)return console.warn(J,"not serialized"),null;var vs=is.hasNode(J)?is.getId(J):vr(Es,cs)||!Ts&&Es.type===x$4.Text&&!Es.isStyle&&!Es.textContent.replace(/^\s+|\s+$/gm,"").length?Ae:nt(),Cs=Object.assign(Es,{id:vs});if(is.add(J,Cs),vs===Ae)return null;if($s&&$s(J),Es=!os,Cs.type===x$4.Element&&(Es=Es&&!Cs.needBlock,delete Cs.needBlock,vs=J.shadowRoot)&&Me(vs)&&(Cs.isShadowHost=!0),(Cs.type===x$4.Document||Cs.type===x$4.Element)&&Es){cs.headWhitespace&&Cs.type===x$4.Element&&Cs.tagName==="head"&&(Ts=!1);for(var As,Ns={doc:ts,mirror:is,blockClass:ns,blockSelector:rs,maskTextClass:ss,maskTextSelector:as,skipChild:os,inlineStylesheet:ds,maskInputOptions:ls,maskTextFn:fs,maskInputFn:us,slimDOMOptions:cs,dataURLOptions:hs,inlineImages:ms,recordCanvas:ps,preserveWhiteSpace:Ts,onSerialize:$s,onIframeLoad:gs,iframeLoadTimeout:ws,onStylesheetLoad:Ss,stylesheetLoadTimeout:_s,keepIframeSrcFn:ys},Ms=0,Ls=Array.from(J.childNodes);Ms<Ls.length;Ms++)(As=ye(Ls[Ms],Ns))&&Cs.childNodes.push(As);if(Kt(J)&&J.shadowRoot)for(var xs=0,Bs=Array.from(J.shadowRoot.childNodes);xs<Bs.length;xs++)(As=ye(Bs[xs],Ns))&&(Me(J.shadowRoot)&&(As.isShadow=!0),Cs.childNodes.push(As))}return J.parentNode&&we(J.parentNode)&&Me(J.parentNode)&&(Cs.isShadow=!0),Cs.type===x$4.Element&&Cs.tagName==="iframe"&&fr(J,function(){var Os=J.contentDocument;Os&&gs&&(Os=ye(Os,{doc:Os,mirror:is,blockClass:ns,blockSelector:rs,maskTextClass:ss,maskTextSelector:as,skipChild:!1,inlineStylesheet:ds,maskInputOptions:ls,maskTextFn:fs,maskInputFn:us,slimDOMOptions:cs,dataURLOptions:hs,inlineImages:ms,recordCanvas:ps,preserveWhiteSpace:Ts,onSerialize:$s,onIframeLoad:gs,iframeLoadTimeout:ws,onStylesheetLoad:Ss,stylesheetLoadTimeout:_s,keepIframeSrcFn:ys}))&&gs(J,Os)},ws),Cs.type===x$4.Element&&Cs.tagName==="link"&&Cs.attributes.rel==="stylesheet"&&mr(J,function(){var Os;Ss&&(Os=ye(J,{doc:ts,mirror:is,blockClass:ns,blockSelector:rs,maskTextClass:ss,maskTextSelector:as,skipChild:!1,inlineStylesheet:ds,maskInputOptions:ls,maskTextFn:fs,maskInputFn:us,slimDOMOptions:cs,dataURLOptions:hs,inlineImages:ms,recordCanvas:ps,preserveWhiteSpace:Ts,onSerialize:$s,onIframeLoad:gs,iframeLoadTimeout:ws,onStylesheetLoad:Ss,stylesheetLoadTimeout:_s,keepIframeSrcFn:ys}))&&Ss(J,Os)},_s),Cs}function Sr(J,ts){var ts=ts||{},is=ts.mirror,is=is===void 0?new tt:is,ns=ts.blockClass,rs=ts.blockSelector,ss=ts.maskTextClass,as=ts.maskTextSelector,os=ts.inlineStylesheet,ds=ts.inlineImages,ls=ts.recordCanvas,fs=ts.maskAllInputs,fs=fs!==void 0&&fs,us=ts.slimDOM,us=us!==void 0&&us,cs=ts.keepIframeSrcFn;return ye(J,{doc:J,mirror:is,blockClass:ns===void 0?"rr-block":ns,blockSelector:rs===void 0?null:rs,maskTextClass:ss===void 0?"rr-mask":ss,maskTextSelector:as===void 0?null:as,skipChild:!1,inlineStylesheet:os===void 0||os,maskInputOptions:fs===!0?{color:!0,date:!0,"datetime-local":!0,email:!0,month:!0,number:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0,textarea:!0,select:!0,password:!0}:fs===!1?{password:!0}:fs,maskTextFn:ts.maskTextFn,maskInputFn:ts.maskInputFn,slimDOMOptions:us===!0||us==="all"?{script:!0,comment:!0,headFavicon:!0,headWhitespace:!0,headMetaDescKeywords:us==="all",headMetaSocial:!0,headMetaRobots:!0,headMetaHttpEquiv:!0,headMetaAuthorship:!0,headMetaVerification:!0}:us===!1?{}:us,dataURLOptions:ts.dataURLOptions,inlineImages:ds!==void 0&&ds,recordCanvas:ls!==void 0&&ls,preserveWhiteSpace:ts.preserveWhiteSpace,onSerialize:ts.onSerialize,onIframeLoad:ts.onIframeLoad,iframeLoadTimeout:ts.iframeLoadTimeout,onStylesheetLoad:ts.onStylesheetLoad,stylesheetLoadTimeout:ts.stylesheetLoadTimeout,keepIframeSrcFn:cs===void 0?function(){return!1}:cs,newlyAddedElement:!1})}function Z(J,es,ts=document){let is={capture:!0,passive:!0};return ts.addEventListener(J,es,is),()=>ts.removeEventListener(J,es,is)}let Ie=`Please stop import mirror directly. Instead of that,\r
now you can use replayer.getMirror() to access the mirror instance of a replayer,\r
or you can use record.mirror to access the mirror instance during recording.`,lt={map:{},getId:()=>(console.error(Ie),-1),getNode:()=>(console.error(Ie),null),removeNodeFromMap(){console.error(Ie)},has:()=>(console.error(Ie),!1),reset(){console.error(Ie)}};function Oe(J,es,ts={}){let is=null,ns=0;return function(...rs){var ss=Date.now();ns||ts.leading!==!1||(ns=ss);let as=es-(ss-ns),os=this;as<=0||es<as?(is&&(clearTimeout(is),is=null),ns=ss,J.apply(os,rs)):is||ts.trailing===!1||(is=setTimeout(()=>{ns=ts.leading===!1?0:Date.now(),is=null,J.apply(os,rs)},as))}}function Ee$2(J,es,ts,is,ns=window){let rs=ns.Object.getOwnPropertyDescriptor(J,es);return ns.Object.defineProperty(J,es,is?ts:{set(ss){setTimeout(()=>{ts.set.call(this,ss)},0),rs&&rs.set&&rs.set.call(this,ss)}}),()=>Ee$2(J,es,rs||{},!0)}function Re(J,es,ts){try{if(!(es in J))return()=>{};let is=J[es],ns=ts(is);return typeof ns=="function"&&(ns.prototype=ns.prototype||{},Object.defineProperties(ns,{__rrweb_original__:{enumerable:!1,value:is}})),J[es]=ns,()=>{J[es]=is}}catch(is){return()=>{}}}function ct(J){var es,ts=J.document;return{left:ts.scrollingElement?ts.scrollingElement.scrollLeft:J.pageXOffset!==void 0?J.pageXOffset:(ts==null?void 0:ts.documentElement.scrollLeft)||((es=(es=ts==null?void 0:ts.body)==null?void 0:es.parentElement)==null?void 0:es.scrollLeft)||((es=ts==null?void 0:ts.body)==null?void 0:es.scrollLeft)||0,top:ts.scrollingElement?ts.scrollingElement.scrollTop:J.pageYOffset!==void 0?J.pageYOffset:(ts==null?void 0:ts.documentElement.scrollTop)||((J=(es=ts==null?void 0:ts.body)==null?void 0:es.parentElement)==null?void 0:J.scrollTop)||((es=ts==null?void 0:ts.body)==null?void 0:es.scrollTop)||0}}function dt(){return window.innerHeight||document.documentElement&&document.documentElement.clientHeight||document.body&&document.body.clientHeight}function ut(){return window.innerWidth||document.documentElement&&document.documentElement.clientWidth||document.body&&document.body.clientWidth}function B$2(J,es,ts,is){if(J){var ns=J.nodeType===J.ELEMENT_NODE?J:J.parentElement;if(ns){try{if(typeof es=="string"){if(ns.classList.contains(es)||is&&ns.closest("."+es)!==null)return 1}else if(Le(ns,es,is))return 1}catch(rs){}return ts&&(ns.matches(ts)||is&&ns.closest(ts)!==null)}}}function br(J,es){return es.getId(J)!==-1}function Xe(J,es){return es.getId(J)===Ae}function pt(J,es){var ts;return!we(J)&&(ts=es.getId(J),!es.has(ts)||(!J.parentNode||J.parentNode.nodeType!==J.DOCUMENT_NODE)&&(!J.parentNode||pt(J.parentNode,es)))}function je(J){return J.changedTouches}function wr(J=window){"NodeList"in J&&!J.NodeList.prototype.forEach&&(J.NodeList.prototype.forEach=Array.prototype.forEach),"DOMTokenList"in J&&!J.DOMTokenList.prototype.forEach&&(J.DOMTokenList.prototype.forEach=Array.prototype.forEach),Node.prototype.contains||(Node.prototype.contains=(...es)=>{let ts=es[0];if(!(0 in es))throw new TypeError("1 argument is required");do if(this===ts)return!0;while(ts=ts&&ts.parentNode);return!1})}function ht(J,es){return J.nodeName==="IFRAME"&&es.getMeta(J)}function ft(J,es){return J.nodeName==="LINK"&&J.nodeType===J.ELEMENT_NODE&&J.getAttribute&&J.getAttribute("rel")==="stylesheet"&&es.getMeta(J)}function Ke(J){return J!=null&&J.shadowRoot}typeof window!="undefined"&&window.Proxy&&window.Reflect&&(lt=new Proxy(lt,{get:(J,es,ts)=>(es==="map"&&console.error(Ie),Reflect.get(J,es,ts))}));class Mr{constructor(){this.id=1,this.styleIDMap=new WeakMap,this.idStyleMap=new Map}getId(es){return(es=this.styleIDMap.get(es))!=null?es:-1}has(es){return this.styleIDMap.has(es)}add(es,ts){return this.has(es)?this.getId(es):(ts=ts===void 0?this.id++:ts,this.styleIDMap.set(es,ts),this.idStyleMap.set(ts,es),ts)}getStyle(es){return this.idStyleMap.get(es)||null}reset(){this.styleIDMap=new WeakMap,this.idStyleMap=new Map,this.id=1}generateId(){return this.id++}}function mt(J){let es,ts,is=null;return is=((ts=(es=J.getRootNode)==null?void 0:es.call(J))==null?void 0:ts.nodeType)===Node.DOCUMENT_FRAGMENT_NODE&&J.getRootNode().host?J.getRootNode().host:is}function Ar(J){let es,ts=J;for(;es=mt(ts);)ts=es;return ts}function Or(J){var es=J.ownerDocument;return!!es&&(J=Ar(J),es.contains(J))}function gt(J){var es=J.ownerDocument;return es&&(es.contains(J)||Or(J))}var w$4=(J=>(J[J.DomContentLoaded=0]="DomContentLoaded",J[J.Load=1]="Load",J[J.FullSnapshot=2]="FullSnapshot",J[J.IncrementalSnapshot=3]="IncrementalSnapshot",J[J.Meta=4]="Meta",J[J.Custom=5]="Custom",J[J.Plugin=6]="Plugin",J))(w$4||{}),b$3=(J=>(J[J.Mutation=0]="Mutation",J[J.MouseMove=1]="MouseMove",J[J.MouseInteraction=2]="MouseInteraction",J[J.Scroll=3]="Scroll",J[J.ViewportResize=4]="ViewportResize",J[J.Input=5]="Input",J[J.TouchMove=6]="TouchMove",J[J.MediaInteraction=7]="MediaInteraction",J[J.StyleSheetRule=8]="StyleSheetRule",J[J.CanvasMutation=9]="CanvasMutation",J[J.Font=10]="Font",J[J.Log=11]="Log",J[J.Drag=12]="Drag",J[J.StyleDeclaration=13]="StyleDeclaration",J[J.Selection=14]="Selection",J[J.AdoptedStyleSheet=15]="AdoptedStyleSheet",J))(b$3||{}),U$6=(J=>(J[J.MouseUp=0]="MouseUp",J[J.MouseDown=1]="MouseDown",J[J.Click=2]="Click",J[J.ContextMenu=3]="ContextMenu",J[J.DblClick=4]="DblClick",J[J.Focus=5]="Focus",J[J.Blur=6]="Blur",J[J.TouchStart=7]="TouchStart",J[J.TouchMove_Departed=8]="TouchMove_Departed",J[J.TouchEnd=9]="TouchEnd",J[J.TouchCancel=10]="TouchCancel",J))(U$6||{}),le$2=(J=>(J[J.Mouse=0]="Mouse",J[J.Pen=1]="Pen",J[J.Touch=2]="Touch",J))(le$2||{}),Ce=(J=>(J[J["2D"]=0]="2D",J[J.WebGL=1]="WebGL",J[J.WebGL2=2]="WebGL2",J))(Ce||{}),ve=(J=>(J[J.Play=0]="Play",J[J.Pause=1]="Pause",J[J.Seeked=2]="Seeked",J[J.VolumeChange=3]="VolumeChange",J[J.RateChange=4]="RateChange",J))(ve||{});function yt(J){return"__ln"in J}class Rr{constructor(){this.length=0,this.head=null}get(es){if(es>=this.length)throw new Error("Position outside of list range");let ts=this.head;for(let is=0;is<es;is++)ts=(ts==null?void 0:ts.next)||null;return ts}addNode(es){var ts,is={value:es,previous:null,next:null};es.__ln=is,es.previousSibling&&yt(es.previousSibling)?(ts=es.previousSibling.__ln.next,is.next=ts,is.previous=es.previousSibling.__ln,es.previousSibling.__ln.next=is,ts&&(ts.previous=is)):es.nextSibling&&yt(es.nextSibling)&&es.nextSibling.__ln.previous?(ts=es.nextSibling.__ln.previous,is.previous=ts,is.next=es.nextSibling.__ln,es.nextSibling.__ln.previous=is,ts&&(ts.next=is)):(this.head&&(this.head.previous=is),is.next=this.head,this.head=is),this.length++}removeNode(es){var ts=es.__ln;this.head&&(ts.previous?(ts.previous.next=ts.next,ts.next&&(ts.next.previous=ts.previous)):(this.head=ts.next,this.head&&(this.head.previous=null)),es.__ln&&delete es.__ln,this.length--)}}let It=(J,es)=>J+"@"+es;class Nr{constructor(){this.frozen=!1,this.locked=!1,this.texts=[],this.attributes=[],this.removes=[],this.mapRemoves=[],this.movedMap={},this.addedSet=new Set,this.movedSet=new Set,this.droppedSet=new Set,this.processMutations=es=>{es.forEach(this.processMutation),this.emit()},this.emit=()=>{if(!this.frozen&&!this.locked){let ts=[],is=new Rr,ns=as=>{let os=as,ds=Ae;for(;ds===Ae;)os=os&&os.nextSibling,ds=os&&this.mirror.getId(os);return ds},rs=as=>{var os,ds,ls;if(as.parentNode&&gt(as))return os=we(as.parentNode)?this.mirror.getId(mt(as)):this.mirror.getId(as.parentNode),ds=ns(as),os===-1||ds===-1?is.addNode(as):void((ls=ye(as,{doc:this.doc,mirror:this.mirror,blockClass:this.blockClass,blockSelector:this.blockSelector,maskTextClass:this.maskTextClass,maskTextSelector:this.maskTextSelector,skipChild:!0,newlyAddedElement:!0,inlineStylesheet:this.inlineStylesheet,maskInputOptions:this.maskInputOptions,maskTextFn:this.maskTextFn,maskInputFn:this.maskInputFn,slimDOMOptions:this.slimDOMOptions,dataURLOptions:this.dataURLOptions,recordCanvas:this.recordCanvas,inlineImages:this.inlineImages,onSerialize:fs=>{ht(fs,this.mirror)&&this.iframeManager.addIframe(fs),ft(fs,this.mirror)&&this.stylesheetManager.trackLinkElement(fs),Ke(as)&&this.shadowDomManager.addShadowRoot(as.shadowRoot,this.doc)},onIframeLoad:(fs,us)=>{this.iframeManager.attachIframe(fs,us),this.shadowDomManager.observeAttachShadow(fs)},onStylesheetLoad:(fs,us)=>{this.stylesheetManager.attachLinkElement(fs,us)}}))&&ts.push({parentId:os,nextId:ds,node:ls}))};for(;this.mapRemoves.length;)this.mirror.removeNodeFromMap(this.mapRemoves.shift());for(let as of this.movedSet)Ct(this.removes,as,this.mirror)&&!this.movedSet.has(as.parentNode)||rs(as);for(let as of this.addedSet)!St(this.droppedSet,as)&&!Ct(this.removes,as,this.mirror)||St(this.movedSet,as)?rs(as):this.droppedSet.add(as);let ss=null;for(;is.length;){let as=null;if(ss){let os=this.mirror.getId(ss.value.parentNode),ds=ns(ss.value);os!==-1&&ds!==-1&&(as=ss)}if(!as)for(let os=is.length-1;0<=os;os--){let ds=is.get(os);if(ds){let ls=this.mirror.getId(ds.value.parentNode);if(ns(ds.value)!==-1){if(ls!==-1){as=ds;break}{let fs=ds.value;if(fs.parentNode&&fs.parentNode.nodeType===Node.DOCUMENT_FRAGMENT_NODE){let us=fs.parentNode.host;if(this.mirror.getId(us)!==-1){as=ds;break}}}}}}if(!as){for(;is.head;)is.removeNode(is.head.value);break}ss=as.previous,is.removeNode(as.value),rs(as.value)}var es={texts:this.texts.map(as=>({id:this.mirror.getId(as.node),value:as.value})).filter(as=>this.mirror.has(as.id)),attributes:this.attributes.map(as=>({id:this.mirror.getId(as.node),attributes:as.attributes})).filter(as=>this.mirror.has(as.id)),removes:this.removes,adds:ts};(es.texts.length||es.attributes.length||es.removes.length||es.adds.length)&&(this.texts=[],this.attributes=[],this.removes=[],this.addedSet=new Set,this.movedSet=new Set,this.droppedSet=new Set,this.movedMap={},this.mutationCb(es))}},this.processMutation=es=>{if(!Xe(es.target,this.mirror))switch(es.type){case"characterData":var ts=es.target.textContent;B$2(es.target,this.blockClass,this.blockSelector,!1)||ts===es.oldValue||this.texts.push({value:st(es.target,this.maskTextClass,this.maskTextSelector)&&ts?this.maskTextFn?this.maskTextFn(ts):ts.replace(/[\S]/g,"*"):ts,node:es.target});break;case"attributes":{var is=es.target;let os=es.attributeName,ds=es.target.getAttribute(os);if(os==="value"){let fs=Ye(is);ds=Ue({element:is,maskInputOptions:this.maskInputOptions,tagName:is.tagName,type:fs,value:ds,maskInputFn:this.maskInputFn})}if(B$2(es.target,this.blockClass,this.blockSelector,!1)||ds===es.oldValue)return;let ls=this.attributes.find(fs=>fs.node===es.target);if(is.tagName==="IFRAME"&&os==="src"&&!this.keepIframeSrcFn(ds)){if(is.contentDocument)return;os="rr_src"}if(ls||(ls={node:es.target,attributes:{}},this.attributes.push(ls)),os==="type"&&is.tagName==="INPUT"&&(es.oldValue||"").toLowerCase()==="password"&&is.setAttribute("data-rr-is-password","true"),os==="style"){var ns=this.doc.createElement("span"),rs=(es.oldValue&&ns.setAttribute("style",es.oldValue),ls.attributes.style!==void 0&&ls.attributes.style!==null||(ls.attributes.style={}),ls.attributes.style);for(let fs of Array.from(is.style)){var ss=is.style.getPropertyValue(fs),as=is.style.getPropertyPriority(fs);ss===ns.style.getPropertyValue(fs)&&as===ns.style.getPropertyPriority(fs)||(rs[fs]=as===""?ss:[ss,as])}for(let fs of Array.from(ns.style))is.style.getPropertyValue(fs)===""&&(rs[fs]=!1)}else it(is.tagName,os)||(ls.attributes[os]=at(this.doc,is.tagName,os,ds));break}case"childList":if(B$2(es.target,this.blockClass,this.blockSelector,!0))return;es.addedNodes.forEach(os=>this.genAdds(os,es.target)),es.removedNodes.forEach(os=>{var ds=this.mirror.getId(os),ls=we(es.target)?this.mirror.getId(es.target.host):this.mirror.getId(es.target);B$2(es.target,this.blockClass,this.blockSelector,!1)||Xe(os,this.mirror)||!br(os,this.mirror)||(this.addedSet.has(os)?(Je(this.addedSet,os),this.droppedSet.add(os)):this.addedSet.has(es.target)&&ds===-1||pt(es.target,this.mirror)||(this.movedSet.has(os)&&this.movedMap[It(ds,ls)]?Je(this.movedSet,os):this.removes.push({parentId:ls,id:ds,isShadow:!(!we(es.target)||!Me(es.target))||void 0})),this.mapRemoves.push(os))})}},this.genAdds=(es,ts)=>{if(!this.processedNodeManager.inOtherBuffer(es,this)){if(this.mirror.hasNode(es)){if(Xe(es,this.mirror))return;this.movedSet.add(es);let is=null;(is=ts&&this.mirror.hasNode(ts)?this.mirror.getId(ts):is)&&is!==-1&&(this.movedMap[It(this.mirror.getId(es),is)]=!0)}else this.addedSet.add(es),this.droppedSet.delete(es);B$2(es,this.blockClass,this.blockSelector,!1)||(es.childNodes.forEach(is=>this.genAdds(is)),Ke(es)&&es.shadowRoot.childNodes.forEach(is=>{this.processedNodeManager.add(is,this),this.genAdds(is,es)}))}}}init(es){["mutationCb","blockClass","blockSelector","maskTextClass","maskTextSelector","inlineStylesheet","maskInputOptions","maskTextFn","maskInputFn","keepIframeSrcFn","recordCanvas","inlineImages","slimDOMOptions","dataURLOptions","doc","mirror","iframeManager","stylesheetManager","shadowDomManager","canvasManager","processedNodeManager"].forEach(ts=>{this[ts]=es[ts]})}freeze(){this.frozen=!0,this.canvasManager.freeze()}unfreeze(){this.frozen=!1,this.canvasManager.unfreeze(),this.emit()}isFrozen(){return this.frozen}lock(){this.locked=!0,this.canvasManager.lock()}unlock(){this.locked=!1,this.canvasManager.unlock(),this.emit()}reset(){this.shadowDomManager.reset(),this.canvasManager.reset()}}function Je(J,es){J.delete(es),es.childNodes.forEach(ts=>Je(J,ts))}function Ct(J,es,ts){return J.length!==0&&vt(J,es,ts)}function vt(J,es,ts){if(es=es.parentNode,!es)return!1;let is=ts.getId(es);return!!J.some(ns=>ns.id===is)||vt(J,es,ts)}function St(J,es){return J.size!==0&&bt(J,es)}function bt(J,es){return es=es.parentNode,!!es&&(!!J.has(es)||bt(J,es))}let Ne;function kr(J){Ne=J}function Tr(){Ne=void 0}let S$4=J=>Ne?(...es)=>{try{return J(...es)}catch(ts){if(!Ne||Ne(ts)!==!0)throw ts}}:J;var _r=Object.defineProperty,Lr=Object.defineProperties,Er=Object.getOwnPropertyDescriptors,wt=Object.getOwnPropertySymbols,Dr=Object.prototype.hasOwnProperty,xr=Object.prototype.propertyIsEnumerable,Mt=(J,es,ts)=>es in J?_r(J,es,{enumerable:!0,configurable:!0,writable:!0,value:ts}):J[es]=ts,Qe=(J,es)=>{for(var ts in es=es||{})Dr.call(es,ts)&&Mt(J,ts,es[ts]);if(wt)for(var ts of wt(es))xr.call(es,ts)&&Mt(J,ts,es[ts]);return J},Fr=(J,es)=>Lr(J,Er(es));let pe$2=[];function ke(J){try{if("composedPath"in J){var es=J.composedPath();if(es.length)return es[0]}else if("path"in J&&J.path.length)return J.path[0];return J.target}catch(ts){return J.target}}function At(rs,es){var ts=new Nr;pe$2.push(ts),ts.init(rs);let is=window.MutationObserver||window.__rrMutationObserver;var ns=(ns=(rs=window==null?void 0:window.Zone)==null?void 0:rs.__symbol__)==null?void 0:ns.call(rs,"MutationObserver"),rs=new(is=ns&&window[ns]?window[ns]:is)(S$4(ts.processMutations.bind(ts)));return rs.observe(es,{attributes:!0,attributeOldValue:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0}),rs}function Wr({mousemoveCb:J,sampling:rs,doc:ts,mirror:is}){if(rs.mousemove===!1)return()=>{};var ns=typeof rs.mousemove=="number"?rs.mousemove:50,rs=typeof rs.mousemoveCallback=="number"?rs.mousemoveCallback:500;let ss,as=[],os=Oe(S$4(fs=>{let us=Date.now()-ss;J(as.map(cs=>(cs.timeOffset-=us,cs)),fs),as=[],ss=null}),rs),ds=S$4(Oe(S$4(fs=>{var us=ke(fs),{clientX:cs,clientY:hs}=je(fs)?fs.changedTouches[0]:fs;ss=ss||Date.now(),as.push({x:cs,y:hs,id:is.getId(us),timeOffset:Date.now()-ss}),os(typeof DragEvent!="undefined"&&fs instanceof DragEvent?b$3.Drag:fs instanceof MouseEvent?b$3.MouseMove:b$3.TouchMove)}),ns,{trailing:!1})),ls=[Z("mousemove",ds,ts),Z("touchmove",ds,ts),Z("drag",ds,ts)];return S$4(()=>{ls.forEach(fs=>fs())})}function Pr({mouseInteractionCb:J,doc:es,mirror:ts,blockClass:is,blockSelector:ns,sampling:rs}){if(rs.mouseInteraction===!1)return()=>{};let ss=rs.mouseInteraction===!0||rs.mouseInteraction===void 0?{}:rs.mouseInteraction,as=[],os=null;return Object.keys(U$6).filter(ds=>Number.isNaN(Number(ds))&&!ds.endsWith("_Departed")&&ss[ds]!==!1).forEach(ds=>{let ls=ds.toLowerCase();fs=ds;var fs,us=cs=>{var hs=ke(cs);if(!B$2(hs,is,ns,!0)){let ps=null,$s=fs;if("pointerType"in cs){switch(cs.pointerType){case"mouse":ps=le$2.Mouse;break;case"touch":ps=le$2.Touch;break;case"pen":ps=le$2.Pen}ps===le$2.Touch?U$6[fs]===U$6.MouseDown?$s="TouchStart":U$6[fs]===U$6.MouseUp&&($s="TouchEnd"):le$2.Pen}else je(cs)&&(ps=le$2.Touch);ps!==null?(os=ps,($s.startsWith("Touch")&&ps===le$2.Touch||$s.startsWith("Mouse")&&ps===le$2.Mouse)&&(ps=null)):U$6[fs]===U$6.Click&&(ps=os,os=null);var ms,cs=je(cs)?cs.changedTouches[0]:cs;cs&&(hs=ts.getId(hs),{clientX:cs,clientY:ms}=cs,S$4(J)(Qe({type:U$6[$s],id:hs,x:cs,y:ms},ps!==null&&{pointerType:ps})))}};if(window.PointerEvent)switch(U$6[ds]){case U$6.MouseDown:case U$6.MouseUp:ls=ls.replace("mouse","pointer");break;case U$6.TouchStart:case U$6.TouchEnd:return}as.push(Z(ls,us,es))}),S$4(()=>{as.forEach(ds=>ds())})}function Ot({scrollCb:J,doc:es,mirror:ts,blockClass:is,blockSelector:ns,sampling:rs}){return Z("scroll",S$4(Oe(S$4(ss=>{if(ss=ke(ss),ss&&!B$2(ss,is,ns,!0)){var as=ts.getId(ss);if(ss===es&&es.defaultView){let os=ct(es.defaultView);J({id:as,x:os.left,y:os.top})}else J({id:as,x:ss.scrollLeft,y:ss.scrollTop})}}),rs.scroll||100)),es)}function Gr({viewportResizeCb:J}){let es=-1,ts=-1;return Z("resize",S$4(Oe(S$4(()=>{var is=dt(),ns=ut();es===is&&ts===ns||(J({width:Number(ns),height:Number(is)}),es=is,ts=ns)}),200)),window)}function Rt(J,es){return J=Qe({},J),es||delete J.userTriggered,J}let Zr=["INPUT","TEXTAREA","SELECT"],$e=window.WeakMap!==void 0?new WeakMap:null;function Br({inputCb:J,doc:es,mirror:ts,blockClass:is,blockSelector:ns,ignoreClass:rs,maskInputOptions:ss,maskInputFn:as,sampling:os,userTriggeredOnInput:ds}){function ls($s){let ps=ke($s);var $s=$s.isTrusted,gs=ps&&ps.tagName;if(!(!(ps=ps&&gs==="OPTION"?ps.parentElement:ps)||!gs||Zr.indexOf(gs)<0||B$2(ps,is,ns,!0)||ps.classList.contains(rs))){let Ss=ps.value,_s=!1;var ws=Ye(ps)||"",gs=(ws==="radio"||ws==="checkbox"?_s=ps.checked:(ss[gs.toLowerCase()]||ss[ws])&&(Ss=Ue({element:ps,maskInputOptions:ss,tagName:gs,type:ws,value:Ss,maskInputFn:as})),fs(ps,S$4(Rt)({text:Ss,isChecked:_s,userTriggered:$s},ds)),ps.name);ws==="radio"&&gs&&_s&&es.querySelectorAll(`input[type="radio"][name="${gs}"]`).forEach(Ts=>{Ts!==ps&&fs(Ts,S$4(Rt)({text:Ts.value,isChecked:!_s,userTriggered:!1},ds))})}}function fs(ms,ps){if($e!==null){let $s=$e.get(ms);if(!$s||$s.text!==ps.text||$s.isChecked!==ps.isChecked){$e.set(ms,ps);let gs=ts.getId(ms);S$4(J)(Fr(Qe({},ps),{id:gs}))}}}let us=(os.input==="last"?["change"]:["input","change"]).map(ms=>Z(ms,S$4(ls),es)),cs=es.defaultView;var hs;return cs?(os=cs.Object.getOwnPropertyDescriptor(cs.HTMLInputElement.prototype,"value"),hs=[[cs.HTMLInputElement.prototype,"value"],[cs.HTMLInputElement.prototype,"checked"],[cs.HTMLSelectElement.prototype,"value"],[cs.HTMLTextAreaElement.prototype,"value"],[cs.HTMLSelectElement.prototype,"selectedIndex"],[cs.HTMLOptionElement.prototype,"selected"]],os&&os.set&&us.push(...hs.map(ms=>Ee$2(ms[0],ms[1],{set(){S$4(ls)({target:this,isTrusted:!1})}},!1,cs))),S$4(()=>{us.forEach(ms=>ms())})):()=>{us.forEach(ms=>ms())}}function De(J){return J=J,es=[],xe("CSSGroupingRule")&&J.parentRule instanceof CSSGroupingRule||xe("CSSMediaRule")&&J.parentRule instanceof CSSMediaRule||xe("CSSSupportsRule")&&J.parentRule instanceof CSSSupportsRule||xe("CSSConditionRule")&&J.parentRule instanceof CSSConditionRule?(ts=Array.from(J.parentRule.cssRules).indexOf(J),es.unshift(ts)):J.parentStyleSheet&&(ts=Array.from(J.parentStyleSheet.cssRules).indexOf(J),es.unshift(ts)),es;var es,ts}function ue$2(J,es,ts){let is,ns;return J?(J.ownerNode?is=es.getId(J.ownerNode):ns=ts.getId(J),{styleId:ns,id:is}):{}}function Vr({styleSheetRuleCb:J,mirror:es,stylesheetManager:ts},{win:is}){if(!is.CSSStyleSheet||!is.CSSStyleSheet.prototype)return()=>{};let ns=is.CSSStyleSheet.prototype.insertRule,rs=(is.CSSStyleSheet.prototype.insertRule=new Proxy(ns,{apply:S$4((ls,fs,us)=>{var[cs,hs]=us,{id:ms,styleId:ps}=ue$2(fs,es,ts.styleMirror);return(ms&&ms!==-1||ps&&ps!==-1)&&J({id:ms,styleId:ps,adds:[{rule:cs,index:hs}]}),ls.apply(fs,us)})}),is.CSSStyleSheet.prototype.deleteRule),ss,as,os=(is.CSSStyleSheet.prototype.deleteRule=new Proxy(rs,{apply:S$4((ls,fs,us)=>{var[cs]=us,{id:hs,styleId:ms}=ue$2(fs,es,ts.styleMirror);return(hs&&hs!==-1||ms&&ms!==-1)&&J({id:hs,styleId:ms,removes:[{index:cs}]}),ls.apply(fs,us)})}),is.CSSStyleSheet.prototype.replace&&(ss=is.CSSStyleSheet.prototype.replace,is.CSSStyleSheet.prototype.replace=new Proxy(ss,{apply:S$4((ls,fs,us)=>{var[cs]=us,{id:hs,styleId:ms}=ue$2(fs,es,ts.styleMirror);return(hs&&hs!==-1||ms&&ms!==-1)&&J({id:hs,styleId:ms,replace:cs}),ls.apply(fs,us)})})),is.CSSStyleSheet.prototype.replaceSync&&(as=is.CSSStyleSheet.prototype.replaceSync,is.CSSStyleSheet.prototype.replaceSync=new Proxy(as,{apply:S$4((ls,fs,us)=>{var[cs]=us,{id:hs,styleId:ms}=ue$2(fs,es,ts.styleMirror);return(hs&&hs!==-1||ms&&ms!==-1)&&J({id:hs,styleId:ms,replaceSync:cs}),ls.apply(fs,us)})})),{}),ds=(Fe("CSSGroupingRule")?os.CSSGroupingRule=is.CSSGroupingRule:(Fe("CSSMediaRule")&&(os.CSSMediaRule=is.CSSMediaRule),Fe("CSSConditionRule")&&(os.CSSConditionRule=is.CSSConditionRule),Fe("CSSSupportsRule")&&(os.CSSSupportsRule=is.CSSSupportsRule)),{});return Object.entries(os).forEach(([ls,fs])=>{ds[ls]={insertRule:fs.prototype.insertRule,deleteRule:fs.prototype.deleteRule},fs.prototype.insertRule=new Proxy(ds[ls].insertRule,{apply:S$4((us,cs,hs)=>{var[ms,ps]=hs,{id:$s,styleId:gs}=ue$2(cs.parentStyleSheet,es,ts.styleMirror);return($s&&$s!==-1||gs&&gs!==-1)&&J({id:$s,styleId:gs,adds:[{rule:ms,index:[...De(cs),ps||0]}]}),us.apply(cs,hs)})}),fs.prototype.deleteRule=new Proxy(ds[ls].deleteRule,{apply:S$4((us,cs,hs)=>{var[ms]=hs,{id:ps,styleId:$s}=ue$2(cs.parentStyleSheet,es,ts.styleMirror);return(ps&&ps!==-1||$s&&$s!==-1)&&J({id:ps,styleId:$s,removes:[{index:[...De(cs),ms]}]}),us.apply(cs,hs)})})}),S$4(()=>{is.CSSStyleSheet.prototype.insertRule=ns,is.CSSStyleSheet.prototype.deleteRule=rs,ss&&(is.CSSStyleSheet.prototype.replace=ss),as&&(is.CSSStyleSheet.prototype.replaceSync=as),Object.entries(os).forEach(([ls,fs])=>{fs.prototype.insertRule=ds[ls].insertRule,fs.prototype.deleteRule=ds[ls].deleteRule})})}function Nt({mirror:J,stylesheetManager:es},ts){let is,ns,rs,ss,as=(ss=ts.nodeName==="#document"?J.getId(ts):J.getId(ts.host),ts.nodeName==="#document"?(is=ts.defaultView)==null?void 0:is.Document:(rs=(ns=ts.ownerDocument)==null?void 0:ns.defaultView)==null?void 0:rs.ShadowRoot),os=Object.getOwnPropertyDescriptor(as==null?void 0:as.prototype,"adoptedStyleSheets");return ss!==null&&ss!==-1&&as&&os?(Object.defineProperty(ts,"adoptedStyleSheets",{configurable:os.configurable,enumerable:os.enumerable,get(){var ds;return(ds=os.get)==null?void 0:ds.call(this)},set(ds){var ls=(ls=os.set)==null?void 0:ls.call(this,ds);if(ss!==null&&ss!==-1)try{es.adoptStyleSheets(ds,ss)}catch(fs){}return ls}}),S$4(()=>{Object.defineProperty(ts,"adoptedStyleSheets",{configurable:os.configurable,enumerable:os.enumerable,get:os.get,set:os.set})})):()=>{}}function Hr({styleDeclarationCb:J,mirror:es,ignoreCSSAttributes:ts,stylesheetManager:is},{win:ns}){let rs=ns.CSSStyleDeclaration.prototype.setProperty,ss=(ns.CSSStyleDeclaration.prototype.setProperty=new Proxy(rs,{apply:S$4((as,os,ds)=>{var ls,fs,[us,cs,hs]=ds;return ts.has(us)?rs.apply(os,[us,cs,hs]):({id:ls,styleId:fs}=ue$2((ls=os.parentRule)==null?void 0:ls.parentStyleSheet,es,is.styleMirror),(ls&&ls!==-1||fs&&fs!==-1)&&J({id:ls,styleId:fs,set:{property:us,value:cs,priority:hs},index:De(os.parentRule)}),as.apply(os,ds))})}),ns.CSSStyleDeclaration.prototype.removeProperty);return ns.CSSStyleDeclaration.prototype.removeProperty=new Proxy(ss,{apply:S$4((as,os,ds)=>{var ls,fs,[us]=ds;return ts.has(us)?ss.apply(os,[us]):({id:ls,styleId:fs}=ue$2((ls=os.parentRule)==null?void 0:ls.parentStyleSheet,es,is.styleMirror),(ls&&ls!==-1||fs&&fs!==-1)&&J({id:ls,styleId:fs,remove:{property:us},index:De(os.parentRule)}),as.apply(os,ds))})}),S$4(()=>{ns.CSSStyleDeclaration.prototype.setProperty=rs,ns.CSSStyleDeclaration.prototype.removeProperty=ss})}function Ur({mediaInteractionCb:J,blockClass:es,blockSelector:ts,mirror:is,sampling:ns}){let rs=S$4(as=>Oe(S$4(cs=>{var ds,ls,fs,us,cs=ke(cs);cs&&!B$2(cs,es,ts,!0)&&({currentTime:ds,volume:ls,muted:fs,playbackRate:us}=cs,J({type:as,id:is.getId(cs),currentTime:ds,volume:0,muted:!0,playbackRate:us}))}),ns.media||500)),ss=[Z("play",rs(ve.Play)),Z("pause",rs(ve.Pause)),Z("seeked",rs(ve.Seeked)),Z("volumechange",rs(ve.VolumeChange)),Z("ratechange",rs(ve.RateChange))];return S$4(()=>{ss.forEach(as=>as())})}function Yr({fontCb:J,doc:es}){let ts=es.defaultView;if(!ts)return()=>{};let is=[],ns=new WeakMap,rs=ts.FontFace;return ts.FontFace=function(ss,as,os){var ds=new rs(ss,as,os);return ns.set(ds,{family:ss,buffer:typeof as!="string",descriptors:os,fontSource:typeof as=="string"?as:JSON.stringify(Array.from(new Uint8Array(as)))}),ds},es=Re(es.fonts,"add",function(ss){return function(as){return setTimeout(S$4(()=>{var os=ns.get(as);os&&(J(os),ns.delete(as))}),0),ss.apply(this,[as])}}),is.push(()=>{ts.FontFace=rs}),is.push(es),S$4(()=>{is.forEach(ss=>ss())})}function zr(J){let{doc:es,mirror:ts,blockClass:is,blockSelector:ns,selectionCb:rs}=J,ss=!0;return J=S$4(()=>{var as=es.getSelection();if(!(!as||ss&&as!=null&&as.isCollapsed)){ss=as.isCollapsed||!1;let os=[],ds=as.rangeCount||0;for(let ls=0;ls<ds;ls++){let fs=as.getRangeAt(ls),{startContainer:us,startOffset:cs,endContainer:hs,endOffset:ms}=fs;B$2(us,is,ns,!0)||B$2(hs,is,ns,!0)||os.push({start:ts.getId(us),startOffset:cs,end:ts.getId(hs),endOffset:ms})}rs({ranges:os})}}),J(),Z("selectionchange",J)}function Xr(J,es){let{mutationCb:ts,mousemoveCb:is,mouseInteractionCb:ns,scrollCb:rs,viewportResizeCb:ss,inputCb:as,mediaInteractionCb:os,styleSheetRuleCb:ds,styleDeclarationCb:ls,canvasMutationCb:fs,fontCb:us,selectionCb:cs}=J;J.mutationCb=(...hs)=>{es.mutation&&es.mutation(...hs),ts(...hs)},J.mousemoveCb=(...hs)=>{es.mousemove&&es.mousemove(...hs),is(...hs)},J.mouseInteractionCb=(...hs)=>{es.mouseInteraction&&es.mouseInteraction(...hs),ns(...hs)},J.scrollCb=(...hs)=>{es.scroll&&es.scroll(...hs),rs(...hs)},J.viewportResizeCb=(...hs)=>{es.viewportResize&&es.viewportResize(...hs),ss(...hs)},J.inputCb=(...hs)=>{es.input&&es.input(...hs),as(...hs)},J.mediaInteractionCb=(...hs)=>{es.mediaInteaction&&es.mediaInteaction(...hs),os(...hs)},J.styleSheetRuleCb=(...hs)=>{es.styleSheetRule&&es.styleSheetRule(...hs),ds(...hs)},J.styleDeclarationCb=(...hs)=>{es.styleDeclaration&&es.styleDeclaration(...hs),ls(...hs)},J.canvasMutationCb=(...hs)=>{es.canvasMutation&&es.canvasMutation(...hs),fs(...hs)},J.fontCb=(...hs)=>{es.font&&es.font(...hs),us(...hs)},J.selectionCb=(...hs)=>{es.selection&&es.selection(...hs),cs(...hs)}}function jr(J,es={}){var ts=J.doc.defaultView;if(!ts)return()=>{};Xr(J,es);let is=At(J,J.doc),ns=Wr(J),rs=Pr(J),ss=Ot(J),as=Gr(J),os=Br(J),ds=Ur(J),ls=Vr(J,{win:ts}),fs=Nt(J,J.doc),us=Hr(J,{win:ts}),cs=J.collectFonts?Yr(J):()=>{},hs=zr(J),ms=[];for(let ps of J.plugins)ms.push(ps.observer(ps.callback,ts,ps.options));return S$4(()=>{pe$2.forEach(ps=>ps.reset()),is.disconnect(),ns(),rs(),ss(),as(),os(),ds(),ls(),fs(),us(),cs(),hs(),ms.forEach(ps=>ps())})}function xe(J){return window[J]!==void 0}function Fe(J){return window[J]!==void 0&&window[J].prototype&&"insertRule"in window[J].prototype&&"deleteRule"in window[J].prototype}class kt{constructor(es){this.generateIdFn=es,this.iframeIdToRemoteIdMap=new WeakMap,this.iframeRemoteIdToIdMap=new WeakMap}getId(es,ts,is,ns){is=is||this.getIdToRemoteIdMap(es),ns=ns||this.getRemoteIdToIdMap(es);let rs=is.get(ts);return rs||(rs=this.generateIdFn(),is.set(ts,rs),ns.set(rs,ts)),rs}getIds(es,ts){let is=this.getIdToRemoteIdMap(es),ns=this.getRemoteIdToIdMap(es);return ts.map(rs=>this.getId(es,rs,is,ns))}getRemoteId(es,ts,is){return is=is||this.getRemoteIdToIdMap(es),typeof ts!="number"?ts:is.get(ts)||-1}getRemoteIds(es,ts){let is=this.getRemoteIdToIdMap(es);return ts.map(ns=>this.getRemoteId(es,ns,is))}reset(es){es?(this.iframeIdToRemoteIdMap.delete(es),this.iframeRemoteIdToIdMap.delete(es)):(this.iframeIdToRemoteIdMap=new WeakMap,this.iframeRemoteIdToIdMap=new WeakMap)}getIdToRemoteIdMap(es){let ts=this.iframeIdToRemoteIdMap.get(es);return ts||(ts=new Map,this.iframeIdToRemoteIdMap.set(es,ts)),ts}getRemoteIdToIdMap(es){let ts=this.iframeRemoteIdToIdMap.get(es);return ts||(ts=new Map,this.iframeRemoteIdToIdMap.set(es,ts)),ts}}class Kr{constructor(es){this.iframes=new WeakMap,this.crossOriginIframeMap=new WeakMap,this.crossOriginIframeMirror=new kt(nt),this.crossOriginIframeRootIdMap=new WeakMap,this.mutationCb=es.mutationCb,this.wrappedEmit=es.wrappedEmit,this.stylesheetManager=es.stylesheetManager,this.recordCrossOriginIframes=es.recordCrossOriginIframes,this.crossOriginIframeStyleMirror=new kt(this.stylesheetManager.styleMirror.generateId.bind(this.stylesheetManager.styleMirror)),this.mirror=es.mirror,this.recordCrossOriginIframes&&window.addEventListener("message",this.handleMessage.bind(this))}addIframe(es){this.iframes.set(es,!0),es.contentWindow&&this.crossOriginIframeMap.set(es.contentWindow,es)}addLoadListener(es){this.loadListener=es}attachIframe(es,ts){this.mutationCb({adds:[{parentId:this.mirror.getId(es),nextId:null,node:ts}],removes:[],texts:[],attributes:[],isAttachIframe:!0}),(ts=this.loadListener)!=null&&ts.call(this,es),es.contentDocument&&es.contentDocument.adoptedStyleSheets&&0<es.contentDocument.adoptedStyleSheets.length&&this.stylesheetManager.adoptStyleSheets(es.contentDocument.adoptedStyleSheets,this.mirror.getId(es.contentDocument))}handleMessage(es){var ts=es;ts.data.type==="rrweb"&&ts.origin===ts.data.origin&&es.source&&(es=this.crossOriginIframeMap.get(es.source))&&(es=this.transformCrossOriginEvent(es,ts.data.event))&&this.wrappedEmit(es,ts.data.isCheckout)}transformCrossOriginEvent(es,ts){var is;switch(ts.type){case w$4.FullSnapshot:{this.crossOriginIframeMirror.reset(es),this.crossOriginIframeStyleMirror.reset(es),this.replaceIdOnNode(ts.data.node,es);let ns=ts.data.node.id;return this.crossOriginIframeRootIdMap.set(es,ns),this.patchRootIdOnNode(ts.data.node,ns),{timestamp:ts.timestamp,type:w$4.IncrementalSnapshot,data:{source:b$3.Mutation,adds:[{parentId:this.mirror.getId(es),nextId:null,node:ts.data.node}],removes:[],texts:[],attributes:[],isAttachIframe:!0}}}case w$4.Meta:case w$4.Load:case w$4.DomContentLoaded:return!1;case w$4.Plugin:return ts;case w$4.Custom:return this.replaceIds(ts.data.payload,es,["id","parentId","previousId","nextId"]),ts;case w$4.IncrementalSnapshot:switch(ts.data.source){case b$3.Mutation:return ts.data.adds.forEach(ns=>{this.replaceIds(ns,es,["parentId","nextId","previousId"]),this.replaceIdOnNode(ns.node,es);var rs=this.crossOriginIframeRootIdMap.get(es);rs&&this.patchRootIdOnNode(ns.node,rs)}),ts.data.removes.forEach(ns=>{this.replaceIds(ns,es,["parentId","id"])}),ts.data.attributes.forEach(ns=>{this.replaceIds(ns,es,["id"])}),ts.data.texts.forEach(ns=>{this.replaceIds(ns,es,["id"])}),ts;case b$3.Drag:case b$3.TouchMove:case b$3.MouseMove:return ts.data.positions.forEach(ns=>{this.replaceIds(ns,es,["id"])}),ts;case b$3.ViewportResize:return!1;case b$3.MediaInteraction:case b$3.MouseInteraction:case b$3.Scroll:case b$3.CanvasMutation:case b$3.Input:return this.replaceIds(ts.data,es,["id"]),ts;case b$3.StyleSheetRule:case b$3.StyleDeclaration:return this.replaceIds(ts.data,es,["id"]),this.replaceStyleIds(ts.data,es,["styleId"]),ts;case b$3.Font:return ts;case b$3.Selection:return ts.data.ranges.forEach(ns=>{this.replaceIds(ns,es,["start","end"])}),ts;case b$3.AdoptedStyleSheet:return this.replaceIds(ts.data,es,["id"]),this.replaceStyleIds(ts.data,es,["styleIds"]),(is=ts.data.styles)!=null&&is.forEach(ns=>{this.replaceStyleIds(ns,es,["styleId"])}),ts}}}replace(es,ts,is,ns){for(var rs of ns)!Array.isArray(ts[rs])&&typeof ts[rs]!="number"||(Array.isArray(ts[rs])?ts[rs]=es.getIds(is,ts[rs]):ts[rs]=es.getId(is,ts[rs]));return ts}replaceIds(es,ts,is){return this.replace(this.crossOriginIframeMirror,es,ts,is)}replaceStyleIds(es,ts,is){return this.replace(this.crossOriginIframeStyleMirror,es,ts,is)}replaceIdOnNode(es,ts){this.replaceIds(es,ts,["id","rootId"]),"childNodes"in es&&es.childNodes.forEach(is=>{this.replaceIdOnNode(is,ts)})}patchRootIdOnNode(es,ts){es.type===x$4.Document||es.rootId||(es.rootId=ts),"childNodes"in es&&es.childNodes.forEach(is=>{this.patchRootIdOnNode(is,ts)})}}var Jr=Object.defineProperty,Qr=Object.defineProperties,$r=Object.getOwnPropertyDescriptors,Tt=Object.getOwnPropertySymbols,qr=Object.prototype.hasOwnProperty,en=Object.prototype.propertyIsEnumerable,_t=(J,es,ts)=>es in J?Jr(J,es,{enumerable:!0,configurable:!0,writable:!0,value:ts}):J[es]=ts,Lt=(J,es)=>{for(var ts in es=es||{})qr.call(es,ts)&&_t(J,ts,es[ts]);if(Tt)for(var ts of Tt(es))en.call(es,ts)&&_t(J,ts,es[ts]);return J},Et=(J,es)=>Qr(J,$r(es));class tn{constructor(es){this.shadowDoms=new WeakSet,this.restoreHandlers=[],this.mutationCb=es.mutationCb,this.scrollCb=es.scrollCb,this.bypassOptions=es.bypassOptions,this.mirror=es.mirror,this.init()}init(){this.reset(),this.patchAttachShadow(Element,document)}addShadowRoot(es,ts){if(Me(es)&&!this.shadowDoms.has(es)){this.shadowDoms.add(es);let is=At(Et(Lt({},this.bypassOptions),{doc:ts,mutationCb:this.mutationCb,mirror:this.mirror,shadowDomManager:this}),es);this.restoreHandlers.push(()=>is.disconnect()),this.restoreHandlers.push(Ot(Et(Lt({},this.bypassOptions),{scrollCb:this.scrollCb,doc:es,mirror:this.mirror}))),setTimeout(()=>{es.adoptedStyleSheets&&0<es.adoptedStyleSheets.length&&this.bypassOptions.stylesheetManager.adoptStyleSheets(es.adoptedStyleSheets,this.mirror.getId(es.host)),this.restoreHandlers.push(Nt({mirror:this.mirror,stylesheetManager:this.bypassOptions.stylesheetManager},es))},0)}}observeAttachShadow(es){es.contentWindow&&es.contentDocument&&this.patchAttachShadow(es.contentWindow.Element,es.contentDocument)}patchAttachShadow(es,ts){let is=this;this.restoreHandlers.push(Re(es.prototype,"attachShadow",function(ns){return function(rs){return rs=ns.call(this,rs),this.shadowRoot&&gt(this)&&is.addShadowRoot(this.shadowRoot,ts),rs}}))}reset(){this.restoreHandlers.forEach(es=>{try{es()}catch(ts){}}),this.restoreHandlers=[],this.shadowDoms=new WeakSet}}for(var Se="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",rn=typeof Uint8Array=="undefined"?[]:new Uint8Array(256),We=0;We<Se.length;We++)rn[Se.charCodeAt(We)]=We;var nn=function(J){for(var es=new Uint8Array(J),ts=es.length,is="",ns=0;ns<ts;ns+=3)is=(is=(is=(is+=Se[es[ns]>>2])+Se[(3&es[ns])<<4|es[ns+1]>>4])+Se[(15&es[ns+1])<<2|es[ns+2]>>6])+Se[63&es[ns+2]];return ts%3==2?is=is.substring(0,is.length-1)+"=":ts%3==1&&(is=is.substring(0,is.length-2)+"=="),is};let Dt=new Map;function on(J,es){let ts=Dt.get(J);return ts||(ts=new Map,Dt.set(J,ts)),ts.has(es)||ts.set(es,[]),ts.get(es)}let xt=(J,es,ts)=>{if(J&&(Wt(J,es)||typeof J=="object")){es=on(ts,J.constructor.name);let is=es.indexOf(J);return is===-1&&(is=es.length,es.push(J)),is}};function Pe(J,es,ts){if(J instanceof Array)return J.map(is=>Pe(is,es,ts));if(J===null)return J;if(J instanceof Float32Array||J instanceof Float64Array||J instanceof Int32Array||J instanceof Uint32Array||J instanceof Uint8Array||J instanceof Uint16Array||J instanceof Int16Array||J instanceof Int8Array||J instanceof Uint8ClampedArray)return{rr_type:J.constructor.name,args:[Object.values(J)]};if(J instanceof ArrayBuffer)return{rr_type:J.constructor.name,base64:nn(J)};if(J instanceof DataView)return{rr_type:J.constructor.name,args:[Pe(J.buffer,es,ts),J.byteOffset,J.byteLength]};if(J instanceof HTMLImageElement){let is=J.constructor.name,ns=J.src;return{rr_type:is,src:ns}}return J instanceof HTMLCanvasElement?{rr_type:"HTMLImageElement",src:J.toDataURL()}:J instanceof ImageData?{rr_type:J.constructor.name,args:[Pe(J.data,es,ts),J.width,J.height]}:Wt(J,es)||typeof J=="object"?{rr_type:J.constructor.name,index:xt(J,es,ts)}:J}let Ft=(J,es,ts)=>[...J].map(is=>Pe(is,es,ts)),Wt=(J,es)=>!!["WebGLActiveInfo","WebGLBuffer","WebGLFramebuffer","WebGLProgram","WebGLRenderbuffer","WebGLShader","WebGLShaderPrecisionFormat","WebGLTexture","WebGLUniformLocation","WebGLVertexArrayObject","WebGLVertexArrayObjectOES"].filter(ts=>typeof es[ts]=="function").find(ts=>J instanceof es[ts]);function an(J,es,ts,is){let ns=[],rs=Object.getOwnPropertyNames(es.CanvasRenderingContext2D.prototype);for(let ss of rs)try{if(typeof es.CanvasRenderingContext2D.prototype[ss]!="function")continue;let as=Re(es.CanvasRenderingContext2D.prototype,ss,function(os){return function(...ds){return B$2(this.canvas,ts,is,!0)||setTimeout(()=>{var ls=Ft([...ds],es,this);J(this.canvas,{type:Ce["2D"],property:ss,args:ls})},0),os.apply(this,ds)}});ns.push(as)}catch(as){let os=Ee$2(es.CanvasRenderingContext2D.prototype,ss,{set(ds){J(this.canvas,{type:Ce["2D"],property:ss,args:[ds],setter:!0})}});ns.push(os)}return()=>{ns.forEach(ss=>ss())}}function Pt(J,es,ts){let is=[];try{var ns=Re(J.HTMLCanvasElement.prototype,"getContext",function(rs){return function(ss,...as){return B$2(this,es,ts,!0)||"__context"in this||(this.__context=ss),rs.apply(this,[ss,...as])}});is.push(ns)}catch(rs){console.error("failed to patch HTMLCanvasElement.prototype.getContext")}return()=>{is.forEach(rs=>rs())}}function Gt(J,es,ts,is,ns,rs,ss){let as=[],os=Object.getOwnPropertyNames(J);for(let ds of os)if(!["isContextLost","canvas","drawingBufferWidth","drawingBufferHeight"].includes(ds))try{if(typeof J[ds]!="function")continue;let ls=Re(J,ds,function(fs){return function(...us){var cs=fs.apply(this,us);if(xt(cs,ss,this),!B$2(this.canvas,is,ns,!0)){let hs=Ft([...us],ss,this),ms={type:es,property:ds,args:hs};ts(this.canvas,ms)}return cs}});as.push(ls)}catch(ls){let fs=Ee$2(J,ds,{set(us){ts(this.canvas,{type:es,property:ds,args:[us],setter:!0})}});as.push(fs)}return as}function sn(J,es,ts,is,ns){let rs=[];return rs.push(...Gt(es.WebGLRenderingContext.prototype,Ce.WebGL,J,ts,is,ns,es)),es.WebGL2RenderingContext!==void 0&&rs.push(...Gt(es.WebGL2RenderingContext.prototype,Ce.WebGL2,J,ts,is,ns,es)),()=>{rs.forEach(ss=>ss())}}function ln(J,es){return atob(J)}function cn(is,es,ts){var is=ln(is),ns=is.indexOf(`
`,10)+1,is=is.substring(ns)+"",ns=new Blob([is],{type:"application/javascript"});return URL.createObjectURL(ns)}function dn(J,es,ts){var is;return function(ns){return is=is||cn(J),new Worker(is,ns)}}var un=dn("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"),Zt=Object.getOwnPropertySymbols,pn=Object.prototype.hasOwnProperty,hn=Object.prototype.propertyIsEnumerable,fn=(J,es)=>{var ts={};for(is in J)pn.call(J,is)&&es.indexOf(is)<0&&(ts[is]=J[is]);if(J!=null&&Zt)for(var is of Zt(J))es.indexOf(is)<0&&hn.call(J,is)&&(ts[is]=J[is]);return ts},mn=(J,es,ts)=>new Promise((is,ns)=>{var rs=os=>{try{as(ts.next(os))}catch(ds){ns(ds)}},ss=os=>{try{as(ts.throw(os))}catch(ds){ns(ds)}},as=os=>os.done?is(os.value):Promise.resolve(os.value).then(rs,ss);as((ts=ts.apply(J,es)).next())});class gn{constructor(es){this.pendingCanvasMutations=new Map,this.rafStamps={latestId:0,invokeId:null},this.frozen=!1,this.locked=!1,this.processMutation=(os,ds)=>{(this.rafStamps.invokeId&&this.rafStamps.latestId!==this.rafStamps.invokeId||!this.rafStamps.invokeId)&&(this.rafStamps.invokeId=this.rafStamps.latestId),this.pendingCanvasMutations.has(os)||this.pendingCanvasMutations.set(os,[]),this.pendingCanvasMutations.get(os).push(ds)};var{sampling:ts="all",win:is,blockClass:ns,blockSelector:rs,recordCanvas:ss,dataURLOptions:as}=es;this.mutationCb=es.mutationCb,this.mirror=es.mirror,ss&&ts==="all"&&this.initCanvasMutationObserver(is,ns,rs),ss&&typeof ts=="number"&&this.initCanvasFPSObserver(ts,is,ns,rs,{dataURLOptions:as})}reset(){this.pendingCanvasMutations.clear(),this.resetObservers&&this.resetObservers()}freeze(){this.frozen=!0}unfreeze(){this.frozen=!1}lock(){this.locked=!0}unlock(){this.locked=!1}initCanvasFPSObserver(es,ts,is,ns,rs){let ss=Pt(ts,is,ns),as=new Map,os=new un,ds=(os.onmessage=cs=>{var hs,ms,ps,$s=cs.data.id;as.set($s,!1),"base64"in cs.data&&({base64:cs,type:hs,width:ms,height:ps}=cs.data,this.mutationCb({id:$s,type:Ce["2D"],commands:[{property:"clearRect",args:[0,0,ms,ps]},{property:"drawImage",args:[{rr_type:"ImageBitmap",args:[{rr_type:"Blob",data:[{rr_type:"ArrayBuffer",base64:cs}],type:hs}]},0,0]}]}))},1e3/es),ls,fs=0,us=cs=>{fs&&cs-fs<ds||(fs=cs,(()=>{let hs=[];return ts.document.querySelectorAll("canvas").forEach(ms=>{B$2(ms,is,ns,!0)||hs.push(ms)}),hs})().forEach(hs=>mn(this,null,function*(){let ms,ps=this.mirror.getId(hs);if(!as.get(ps)){if(as.set(ps,!0),["webgl","webgl2"].includes(hs.__context)){let gs=hs.getContext(hs.__context);((ms=gs==null?void 0:gs.getContextAttributes())==null?void 0:ms.preserveDrawingBuffer)!==!1||gs!=null&&gs.clear(gs.COLOR_BUFFER_BIT)}var $s=yield createImageBitmap(hs);os.postMessage({id:ps,bitmap:$s,width:hs.width,height:hs.height,dataURLOptions:rs.dataURLOptions},[$s])}}))),ls=requestAnimationFrame(us)};ls=requestAnimationFrame(us),this.resetObservers=()=>{ss(),cancelAnimationFrame(ls)}}initCanvasMutationObserver(es,ts,is){this.startRAFTimestamping(),this.startPendingCanvasMutationFlusher();let ns=Pt(es,ts,is),rs=an(this.processMutation.bind(this),es,ts,is),ss=sn(this.processMutation.bind(this),es,ts,is,this.mirror);this.resetObservers=()=>{ns(),rs(),ss()}}startPendingCanvasMutationFlusher(){requestAnimationFrame(()=>this.flushPendingCanvasMutations())}startRAFTimestamping(){let es=ts=>{this.rafStamps.latestId=ts,requestAnimationFrame(es)};requestAnimationFrame(es)}flushPendingCanvasMutations(){this.pendingCanvasMutations.forEach((es,ts)=>{var is=this.mirror.getId(ts);this.flushPendingCanvasMutationFor(ts,is)}),requestAnimationFrame(()=>this.flushPendingCanvasMutations())}flushPendingCanvasMutationFor(es,ts){var is,ns;this.frozen||this.locked||(ns=this.pendingCanvasMutations.get(es))&&ts!==-1&&(is=ns.map(rs=>fn(rs,["type"])),ns=ns[0].type,this.mutationCb({id:ts,type:ns,commands:is}),this.pendingCanvasMutations.delete(es))}}class yn{constructor(es){this.trackedLinkElements=new WeakSet,this.styleMirror=new Mr,this.mutationCb=es.mutationCb,this.adoptedStyleSheetCb=es.adoptedStyleSheetCb}attachLinkElement(es,ts){"_cssText"in ts.attributes&&this.mutationCb({adds:[],removes:[],texts:[],attributes:[{id:ts.id,attributes:ts.attributes}]}),this.trackLinkElement(es)}trackLinkElement(es){this.trackedLinkElements.has(es)||(this.trackedLinkElements.add(es),this.trackStylesheetInLinkElement(es))}adoptStyleSheets(es,ts){if(es.length!==0){let is={id:ts,styleIds:[]},ns=[];for(let rs of es){let ss;if(this.styleMirror.has(rs))ss=this.styleMirror.getId(rs);else{ss=this.styleMirror.add(rs);let as=Array.from(rs.rules||CSSRule);ns.push({styleId:ss,rules:as.map((os,ds)=>({rule:et(os),index:ds}))})}is.styleIds.push(ss)}0<ns.length&&(is.styles=ns),this.adoptedStyleSheetCb(is)}}reset(){this.styleMirror.reset(),this.trackedLinkElements=new WeakSet}trackStylesheetInLinkElement(es){}}class In{constructor(){this.nodeMap=new WeakMap,this.loop=!0,this.periodicallyClear()}periodicallyClear(){requestAnimationFrame(()=>{this.clear(),this.loop&&this.periodicallyClear()})}inOtherBuffer(es,ts){return es=this.nodeMap.get(es),es&&Array.from(es).some(is=>is!==ts)}add(es,ts){this.nodeMap.set(es,(this.nodeMap.get(es)||new Set).add(ts))}clear(){this.nodeMap=new WeakMap}destroy(){this.loop=!1}}var Cn=Object.defineProperty,vn=Object.defineProperties,Sn=Object.getOwnPropertyDescriptors,Bt=Object.getOwnPropertySymbols,bn=Object.prototype.hasOwnProperty,wn=Object.prototype.propertyIsEnumerable,Vt=(J,es,ts)=>es in J?Cn(J,es,{enumerable:!0,configurable:!0,writable:!0,value:ts}):J[es]=ts,X=(J,es)=>{for(var ts in es=es||{})bn.call(es,ts)&&Vt(J,ts,es[ts]);if(Bt)for(var ts of Bt(es))wn.call(es,ts)&&Vt(J,ts,es[ts]);return J},Mn=(J,es)=>vn(J,Sn(es));function F$2(J){return Mn(X({},J),{timestamp:Date.now()})}let L$3,Ge,qe,Ze=!1,ee$1=$t();function ce(J={}){let{emit:es,checkoutEveryNms:ts,checkoutEveryNth:is,blockClass:ns="rr-block",blockSelector:rs=null,ignoreClass:ss="rr-ignore",maskTextClass:as="rr-mask",maskTextSelector:os=null,inlineStylesheet:ds=!0,maskAllInputs:ls,maskInputOptions:fs,slimDOMOptions:us,maskInputFn:cs,maskTextFn:hs,hooks:ms,packFn:ps,sampling:$s={},dataURLOptions:gs={},mousemoveWait:ws,recordCanvas:Ss=!1,recordCrossOriginIframes:_s=!0,recordAfter:ys=J.recordAfter==="DOMContentLoaded"?J.recordAfter:"load",userTriggeredOnInput:Ts=!1,collectFonts:Es=!1,inlineImages:vs=!1,plugins:Cs,keepIframeSrcFn:As=()=>!1,ignoreCSSAttributes:Ns=new Set([]),errorHandler:Ms}=J,Ls=(kr(Ms),!_s||window.parent===window),xs=!1;if(!Ls)try{window.parent.document&&(xs=!1)}catch(bs){xs=!0}if(Ls&&!es)throw new Error("emit function is required");ws!==void 0&&$s.mousemove===void 0&&($s.mousemove=ws),ee$1.reset();let Bs=ls===!0?{color:!0,date:!0,"datetime-local":!0,email:!0,month:!0,number:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0,textarea:!0,select:!0,password:!0}:fs!==void 0?fs:{password:!0},Os=us===!0||us==="all"?{script:!0,comment:!0,headFavicon:!0,headWhitespace:!0,headMetaSocial:!0,headMetaRobots:!0,headMetaHttpEquiv:!0,headMetaVerification:!0,headMetaAuthorship:us==="all",headMetaDescKeywords:us==="all"}:us||{};wr();let Zs,Hs=0,Ws=bs=>{for(var ks of Cs||[])ks.eventProcessor&&(bs=ks.eventProcessor(bs));return bs=ps&&!xs?ps(bs):bs},Ps=(L$3=(bs,ks)=>{var Is;if((Is=pe$2[0])==null||!Is.isFrozen()||bs.type===w$4.FullSnapshot||bs.type===w$4.IncrementalSnapshot&&bs.data.source===b$3.Mutation||pe$2.forEach(Rs=>Rs.unfreeze()),Ls)es!=null&&es(Ws(bs),ks);else if(xs){let Rs={type:"rrweb",event:Ws(bs),origin:window.location.origin,isCheckout:ks};window.parent.postMessage(Rs,"*")}if(bs.type===w$4.FullSnapshot)Zs=bs,Hs=0;else if(bs.type===w$4.IncrementalSnapshot&&(bs.data.source!==b$3.Mutation||!bs.data.isAttachIframe)){Hs++;let Rs=is&&Hs>=is,Ds=ts&&bs.timestamp-Zs.timestamp>ts;(Rs||Ds)&&Ge(!0)}},bs=>{L$3(F$2({type:w$4.IncrementalSnapshot,data:X({source:b$3.Mutation},bs)}))}),Vs=bs=>L$3(F$2({type:w$4.IncrementalSnapshot,data:X({source:b$3.Scroll},bs)})),Ys=bs=>L$3(F$2({type:w$4.IncrementalSnapshot,data:X({source:b$3.CanvasMutation},bs)})),Gs=new yn({mutationCb:Ps,adoptedStyleSheetCb:bs=>L$3(F$2({type:w$4.IncrementalSnapshot,data:X({source:b$3.AdoptedStyleSheet},bs)}))}),Us=new Kr({mirror:ee$1,mutationCb:Ps,stylesheetManager:Gs,recordCrossOriginIframes:_s,wrappedEmit:L$3});for(let bs of Cs||[])bs.getMirror&&bs.getMirror({nodeMirror:ee$1,crossOriginIframeMirror:Us.crossOriginIframeMirror,crossOriginIframeStyleMirror:Us.crossOriginIframeStyleMirror});let zs=new In,Fs=(qe=new gn({recordCanvas:Ss,mutationCb:Ys,win:window,blockClass:ns,blockSelector:rs,mirror:ee$1,sampling:$s.canvas,dataURLOptions:gs}),new tn({mutationCb:Ps,scrollCb:Vs,bypassOptions:{blockClass:ns,blockSelector:rs,maskTextClass:as,maskTextSelector:os,inlineStylesheet:ds,maskInputOptions:Bs,dataURLOptions:gs,maskTextFn:hs,maskInputFn:cs,recordCanvas:Ss,inlineImages:vs,sampling:$s,slimDOMOptions:Os,iframeManager:Us,stylesheetManager:Gs,canvasManager:qe,keepIframeSrcFn:As,processedNodeManager:zs},mirror:ee$1}));Ge=(bs=!1)=>{L$3(F$2({type:w$4.Meta,data:{href:window.location.href,width:ut(),height:dt()}}),bs),Gs.reset(),Fs.init(),pe$2.forEach(Is=>Is.lock());var ks=Sr(document,{mirror:ee$1,blockClass:ns,blockSelector:rs,maskTextClass:as,maskTextSelector:os,inlineStylesheet:ds,maskAllInputs:Bs,maskTextFn:hs,slimDOM:Os,dataURLOptions:gs,recordCanvas:Ss,inlineImages:vs,onSerialize:Is=>{ht(Is,ee$1)&&Us.addIframe(Is),ft(Is,ee$1)&&Gs.trackLinkElement(Is),Ke(Is)&&Fs.addShadowRoot(Is.shadowRoot,document)},onIframeLoad:(Is,Rs)=>{Us.attachIframe(Is,Rs),Fs.observeAttachShadow(Is)},onStylesheetLoad:(Is,Rs)=>{Gs.attachLinkElement(Is,Rs)},keepIframeSrcFn:As});if(!ks)return console.warn("Failed to snapshot the document");L$3(F$2({type:w$4.FullSnapshot,data:{node:ks,initialOffset:ct(window)}}),bs),pe$2.forEach(Is=>Is.unlock()),document.adoptedStyleSheets&&0<document.adoptedStyleSheets.length&&Gs.adoptStyleSheets(document.adoptedStyleSheets,ee$1.getId(document))};try{let bs=[],ks=Rs=>S$4(jr)({mutationCb:Ps,mousemoveCb:(Ds,Ks)=>L$3(F$2({type:w$4.IncrementalSnapshot,data:{source:Ks,positions:Ds}})),mouseInteractionCb:Ds=>L$3(F$2({type:w$4.IncrementalSnapshot,data:X({source:b$3.MouseInteraction},Ds)})),scrollCb:Vs,viewportResizeCb:Ds=>L$3(F$2({type:w$4.IncrementalSnapshot,data:X({source:b$3.ViewportResize},Ds)})),inputCb:Ds=>L$3(F$2({type:w$4.IncrementalSnapshot,data:X({source:b$3.Input},Ds)})),mediaInteractionCb:Ds=>L$3(F$2({type:w$4.IncrementalSnapshot,data:X({source:b$3.MediaInteraction},Ds)})),styleSheetRuleCb:Ds=>L$3(F$2({type:w$4.IncrementalSnapshot,data:X({source:b$3.StyleSheetRule},Ds)})),styleDeclarationCb:Ds=>L$3(F$2({type:w$4.IncrementalSnapshot,data:X({source:b$3.StyleDeclaration},Ds)})),canvasMutationCb:Ys,fontCb:Ds=>L$3(F$2({type:w$4.IncrementalSnapshot,data:X({source:b$3.Font},Ds)})),selectionCb:Ds=>{L$3(F$2({type:w$4.IncrementalSnapshot,data:X({source:b$3.Selection},Ds)}))},blockClass:ns,ignoreClass:ss,maskTextClass:as,maskTextSelector:os,maskInputOptions:Bs,inlineStylesheet:ds,sampling:$s,recordCanvas:Ss,inlineImages:vs,userTriggeredOnInput:Ts,collectFonts:Es,doc:Rs,maskInputFn:cs,maskTextFn:hs,keepIframeSrcFn:As,blockSelector:rs,slimDOMOptions:Os,dataURLOptions:gs,mirror:ee$1,iframeManager:Us,stylesheetManager:Gs,shadowDomManager:Fs,processedNodeManager:zs,canvasManager:qe,ignoreCSSAttributes:Ns,plugins:((Rs=Cs==null?void 0:Cs.filter(Ds=>Ds.observer))==null?void 0:Rs.map(Ds=>({observer:Ds.observer,options:Ds.options,callback:Ks=>L$3(F$2({type:w$4.Plugin,data:{plugin:Ds.name,payload:Ks}}))})))||[]},ms),Is=(Us.addLoadListener(Rs=>{try{bs.push(ks(Rs.contentDocument))}catch(Ds){console.warn(Ds)}}),()=>{Ge(),bs.push(ks(document)),Ze=!0});return document.readyState==="interactive"||document.readyState==="complete"?Is():(bs.push(Z("DOMContentLoaded",()=>{L$3(F$2({type:w$4.DomContentLoaded,data:{}})),ys==="DOMContentLoaded"&&Is()})),bs.push(Z("load",()=>{L$3(F$2({type:w$4.Load,data:{}})),ys==="load"&&Is()},window))),()=>{bs.forEach(Rs=>Rs()),zs.destroy(),Ze=!1,Tr()}}catch(bs){console.warn(bs)}}function An(J){return J=JSON.stringify(J),J=$r$1.gzip(J,{level:9}),btoa(new Uint8Array(J).reduce(function(es,ts){return es+String.fromCharCode(ts)},""))}function On(J,es){var ts,is;isDefined(window.stopBonreeRecordUpload)||q$3.length===0||(se===0&&(se=1e3*q$3[0].timestamp),(ts=new FormData).append("s",bonreeRUM.getSession()),ts.append("v",bonreeRUM&&bonreeRUM.version||"1.0.0"),ts.append("mt",String(now())),ts.append("cmt",String(es.initTime)),ts.append("di",JSON.stringify(getDeviceInfo())),ts.append("ai",JSON.stringify({ai:es.appId,av:es.appVersion||"unknown",an:es.appName||"unknown",at:4})),es.dataFusionInfo=="net"&&ts.append("dfi",JSON.stringify({net:"net"})),se===0&&(se=1e3*q$3[0].timestamp),ts.append("st",se),!isDefined(window.bonreeRUM)&&isDefined(window.bonreeRUM.getUserInfo)?log("C154"):(Object.getOwnPropertyNames(bonreeRUM.getUserInfo()).length!==0&&ts.append("ui",JSON.stringify(Object.entries(bonreeRUM.getUserInfo())[0][1])),is=1e3*q$3[q$3.length-1].timestamp-se,ts.append("ud",is),se=1e3*q$3[q$3.length-1].timestamp,ts.append("wev","2.0.0-alpha.8"),is=new Blob([JSON.stringify({wsd:An(q$3)})],{type:"application/json"}),ts.append("event",is),Rn(ts,J+"?a="+es.appId+"&brkey="+uuid()+"&d="+getDeviceInfo().di+"&v=2023061901")))}function Rn(J,es){window.fetch&&fetch(es,{method:"POST",body:J}).then(function(ts){if(ts!==void 0)return q$3=[],ts.json()},function(ts){console.warn("fetch reject:",ts)}).catch(function(ts){console.warn("fetch reject:",ts)})}function BonreeRecord(J){u$1.BonreeRecordState=!0,(ts=J.RecordConfiguration||J.mc)instanceof Array&&ts.forEach(function(ns){ns.n==="sessionreplay"&&(es=ns)});var es,ts=es||ts;if(isDefined(ts)){var is=function(ns){return!(ns<Math.floor(100*Math.random()))};if(isDefined(ts)&&isDefined(ts.Rate)&&is(ts.Rate)||isDefined(ts)&&isDefined(ts.c)&&is(ts.c))return be=window.location.protocol==="https:"?ts.UploadHttps||ts.src&&ts.src.uas:ts.UploadHttp||ts.src&&ts.src.ua,isDefined(window.$bonreeReplayUrl=be)&&setInterval(On,1e3*(ts.UploadCycle||ts.src&&ts.src.uc),be,J),is={emit:function(ns){q$3.push(ns)},maskAllInputs:!0,recordCanvas:!0,recordCrossOriginIframes:!0,maskTextSelector:"body",maskTextFn:function(ns){var rs;return ns.indexOf(`
`)===-1?"*".repeat(ns.length):(rs=ns.trim(),ns.replace(rs,"*".repeat(rs.length)))}},J={emit:function(ns){q$3.push(ns)},maskAllInputs:!0,recordCanvas:!0,recordCrossOriginIframes:!0},ts.src&&ts.src.sl!==void 0?ts.src.sl===1?ce(is):ce(J):ts.Sensitivity!==void 0?ts.Sensitivity===1?ce(is):ce(J):void 0}}ce.addCustomEvent=(J,es)=>{if(!Ze)throw new Error("please add custom event after start recording");L$3(F$2({type:w$4.Custom,data:{tag:J,payload:es}}))},ce.freezePage=()=>{pe$2.forEach(J=>J.freeze())},ce.takeFullSnapshot=J=>{se=0,Ze&&Ge(J)},ce.mirror=ee$1,BonreeRecord.takeFullSnapshot=ce.takeFullSnapshot;class Protocol{constructor(){this.json={v:bonreeRUM&&bonreeRUM.version||"1.0.0",e:[]}}}Protocol.prototype.setMonitorTime=function(J){return this.json.mt=J,this},Protocol.prototype.setConfigTime=function(J){return this.json.cmt=J,this},Protocol.prototype.setSessionId=function(J){return this.json.s=J,this},Protocol.prototype.setDeviceInfo=function(J){return this.json.di=J,this},Protocol.prototype.setAppInfo=function(J){return this.json.ai=J,this},Protocol.prototype.setFirstUserInfoIndex=function(){var J=u$1.firstUserInfoSetMapKey;return J!==""&&(this.json.fui=J,u$1.updateFirstUserInfoSetMapKey("")),this},Protocol.prototype.setUserInfo=function(J){return this.json.ui=J,this},Protocol.prototype.setSessionDuration=function(J){return this.json.usd=J,this},Protocol.prototype.pushEventData=function(J){var es={k:J.type,ent:J.ent,sin:J.sin,v:J.data};return isDefined(J.revts)&&(es.revts=J.revts),this.json.e.push(es),this},Protocol.prototype.build=function(J){return this.json},Protocol.prototype.setDataFusionInfo=function(J){return this.json.dfi={net:J},this},Protocol.prototype.setSessionStartTime=function(J){return this.json.sst=J,this};let c$1=3e4,a$5=[],p$4=!0;function A$5(){return f$2.sett.isFirstUpload?(extend(f$2.sett,{isFirstUpload:0}),1):0}function S$3(J,es){let ts="";return window.location.protocol==="https:"?(isDefined(f$2.sett.uploadAddrHttps)&&(ts=f$2.sett.uploadAddrHttps),isDefined(f$2.secondSett.uploadAddrHttps)&&(ts=f$2.secondSett.uploadAddrHttps)):(isDefined(f$2.sett.uploadAddrHttp)&&(ts=f$2.sett.uploadAddrHttp),isDefined(f$2.secondSett.uploadAddrHttp)&&(ts=f$2.secondSett.uploadAddrHttp)),ts=(ts=(ts=(ts=(ts=(ts=(ts+="?v=2024041001")+("&a="+J.ai.ai))+("&d="+J.di.di))+("&mt="+J.mt))+("&cmt="+J.cmt))+("&s="+J.s)+("&brkey="+es))+("&if="+A$5()),f$2.sett.sm4Config!==void 0&&/^[A-Za-z0-9_-]{1,256}$/.test(f$2.sett.sm4Config.identify||f$2.sett.sm4Config.identifier)&&(ts+="&BR-Encryption-Method=sm4:"+(f$2.sett.sm4Config.identify||f$2.sett.sm4Config.identifier)),isDefined(window.bonreeRUM)&&window.bonreeRUM.isPrivate?ts+="&isp=1":ts+="&isp=0",ts}function C$4(J,es,ts,is,ns){var rs;if(window.XDomainRequest)return(rs=new window.XDomainRequest).open("POST",J),rs.timeout=c$1,rs.onload=function(){ts&&ts(!0)},rs.onerror=rs.ontimeout=function(){ts&&ts(!1)},delay(function(){rs.send(es)});if(window.XMLHttpRequest){rs=new window.XMLHttpRequest;try{rs.$$inner=!0,rs.overrideMimeType("text/plain"),rs.open("POST",J,!0),rs.setRequestHeader("brkey",ns),rs.timeout=c$1,rs.onreadystatechange=function(){rs.readyState===4&&(ts&&ts(rs.status===200),h$8(rs))}}catch(ss){}rs.send(es)}}function H$2(){return/\bQQ\b/i.test(navigator.userAgent)}function h$8(J){p$4=!0,w$3()}function w$3(J,es,ts,is,ns){if(J&&es&&a$5.length<100&&a$5.push({data:es,uid:ns,url:J}),p$4===!0&&0<a$5.length){let rs=a$5.shift();p$4=!1,C$4(rs.url,rs.data,ts,!0,rs.uid)}}function sendUpload(J,es,ts){var is=uuid(),ns=S$3(J,is);if(startWith(ns,"http")){var rs,ss=f$2.sett.useXHR||f$2.secondSett.useXHR||!1;if(window.navigator&&navigator.sendBeacon&&!H$2()&&ss===!1){if(f$2.sett.sm4Config!==void 0&&/^[A-Za-z0-9_-]{1,256}$/.test(f$2.sett.sm4Config.identify||f$2.sett.sm4Config.identifier)){try{isDefined(window.BonreeRecord)&&window.BonreeRecord.Sm4&&(rs=new window.BonreeRecord.Sm4(f$2.sett.sm4Config)),isDefined(window.BonreeAgent)&&window.BonreeAgent.Sm4&&(rs=new window.BonreeAgent.Sm4(f$2.sett.sm4Config))}catch(as){return log("C156"),ss=f$2.sett.sm4Config.identify||f$2.sett.sm4Config.identifier,ts(navigator.sendBeacon(ns.replace("&BR-Encryption-Method=sm4:"+ss,""),stringify(J)))}return ts(navigator.sendBeacon(ns,rs.encrypt(stringify(J))))}return ts(navigator.sendBeacon(ns,stringify(J)))}if(f$2.sett.sm4Config!==void 0){if(isDefined(window.BonreeRecord)&&window.BonreeRecord.Sm4)return w$3(ns,(rs=new window.BonreeRecord.Sm4(f$2.sett.sm4Config)).encrypt(stringify(J)),ts,es,is);if(isDefined(window.BonreeAgent)&&window.BonreeAgent.Sm4)return w$3(ns,(rs=new window.BonreeAgent.Sm4(f$2.sett.sm4Config)).encrypt(stringify(J)),ts,es,is)}w$3(ns,stringify(J),ts,es,is)}}let T$4=0,S$2=[],n$1={WORKER:null,IDLE:0,CAPACITY:0,RETRY:3},D$2=0,O$4="br-session-cache";function setTotalSize(J){return D$2+=J}function B$1(){var J;0<n$1.IDLE||(J=Number(f$2.sett.cycleTime)||Number(f$2.secondSett.cycleTime)||10,n$1.IDLE=J&&0<J&&J<=60?1e3*J:1e4)}function getMaxSize(){var J;0<n$1.CAPACITY||(J=Number(f$2.sett.maxSize)||Number(f$2.secondSett.maxSize)||20,n$1.CAPACITY=J&&0<J&&J<=60?1024*J:20480)}function F$1(J){var es=0;if(J.length!==0)return forEach(J,function(ts){(es===0||es>ts.ent)&&(es=ts.ent)}),es}function _$1(){v$6(GC.FLUSH_DATA,function(J){var es;B$1(),f$2.sett.osType!==1||isDefined(n$1.WORKER)||(es=setInterval(g$6,n$1.IDLE),n$1.changeData(es)),1<u$1.state&&!isDefined(n$1.WORKER)&&(es=setInterval(g$6,n$1.IDLE),n$1.changeData(es)),g$6(J.p)}),v$6(GC.INIT_SESSION_START,function(J){T$4=0})}function K$2(J,es,ts){let is=new Protocol;var ns={ai:f$2.sett.appId||f$2.secondSett.appId,av:f$2.sett.appVersion||f$2.secondSett.appVersion||"unknown",an:f$2.sett.appName||f$2.secondSett.appName||"unknown",at:4};isDefined(f$2.sett.channelId)&&(ns.ci=f$2.sett.channelId),is.setMonitorTime(now()).setConfigTime(f$2.sett.initTime).setSessionId(B$5.getSession().sessionID).setAppInfo(ns).setUserInfo(u$1.getUserInfo()).setFirstUserInfoIndex(),isDefined(window.BonreeAgent)&&isDefined(window.BonreeAgent.getDeviceInfo)?is.setDeviceInfo(window.BonreeAgent.getDeviceInfo()):log("C153"),isDefined(window.BonreeRecord)&&isDefined(window.BonreeRecord.getDeviceInfo)?is.setDeviceInfo(window.BonreeRecord.getDeviceInfo()):log("C153"),isDefined(f$2.sett.dataFusionInfo)&&f$2.sett.dataFusionInfo=="net"&&is.setDataFusionInfo("net"),T$4===0&&(T$4=F$1(S$2)),is.setSessionStartTime(T$4);let rs=0,ss=0;forEach(J,function(as){var os=as.ent;if((os<rs||rs===0)&&(rs=os),(os>ss||ss===0)&&(ss=os),isDefined(as.revts))for(var ds of as.revts)ds.ent<rs&&(rs=ds.ent),ds.ent>ss&&(ss=ds.ent);is.pushEventData(as)},function(){var as,os=getCookie(O$4),ds=isJSON(os)&&JSON.parse(os)||[];for(as of ds)isDefined(as.startTime)&&as.startTime==0&&(as.startTime=rs,B$5.setSessionCookie(O$4,JSON.stringify(ds)))}),ns=I$4.usdTime&&ss-I$4.usdTime||ss-rs,ns=Math.abs(ns),I$4.changUsdTime(ss),is.setSessionDuration(ns),sendUpload(is.build(),es,ts)}function W$1(J,es,ts){if(isDefined(J)&&!isEmpty(J))try{K$2(J,es,ts),ts&&ts(!0)}catch(is){log(is),ts&&ts(!1)}}function k$1(J,es){let ts,is=[],ns=0,rs=0;if(ns>=n$1.RETRY)return log("C117");function ss(as,os){!isEmpty(is)&&(getMaxSize(),as||rs>n$1.CAPACITY)&&(W$1(is,es,function(ds){ds?0<ns&&(ns=0):(ns+=1,es||delay(function(){var ls=size(J);rs+=ls,is.push(J)},n$1.IDLE/10))}),is=[],rs=0,ts=as===1?os:null)}forEach(J,as=>{var ds=as.data&&as.data.e;if(!isDefined(ds))return rs+=as.size,is.push(as.data),ss.call(this);var os=ds[0],ds=ds[1],ls=as.data&&as.data.p.info;ls.userTime=Math.max(0,ls.timestamp-ds),(ts=isDefined(ts)?ts:os)!==os&&ss.call(this,1,os),rs+=as.size,is.push(as.data),ss.call(this)}),ss.call(this,2)}function g$6(J){isEmpty(S$2)||(g$9.useAPI||g$9.startCatchDataFun(),k$1(S$2,J),S$2=[],D$2=0)}n$1.changeData=function(J){this.WORKER=J};var h$7={};let a$4=class qs{constructor(es,ts,is=!1,ns,rs=-1,ss=!1,as){this.isChild=is,isDefined(ns)&&(this.startTime=ns),isDefined(es)&&isDefined(ts)&&typeof es=="string"&&typeof ts=="string"&&/^[a-zA-Z0-9:_-\s@./]+$/.test(ts)&&ts.trim()!==""&&/^[a-zA-Z0-9:_-\s@./]+$/.test(es)&&es.trim()!==""?(this.spanEvent={n:es.slice(0,256),t:ts.slice(0,256),da:[],tag:[],m:[],ic:!0,st:isDefined(ns)?1e3*new Date().getTime()-ns:0},this.special=ss):this.special=!0,is||this.special||(this.spanEvent.timestamp=1e3*new Date().getTime(),this.startTime=this.spanEvent.timestamp),is?(this.identify=as,h$7[this.identify]=h$7[this.identify]+1):(this.identify=uuid(),h$7[this.identify]=0),this.dataSave=[],this.tagSave=[],this.metricSave=[],this.childSave=[],this.hasFinished=!1,this.deep=rs+1}startChild(es,ts){return this.hasFinished||!(isDefined(es)&&isDefined(ts)&&typeof es=="string"&&typeof ts=="string"&&/^[a-zA-Z0-9:_-\s@./]+$/.test(ts)&&ts.trim()!==""&&/^[a-zA-Z0-9:_-\s@./]+$/.test(es)&&es.trim()!=="")||this.special||10<=this.deep||isDefined(this.childSave)&&50<=this.childSave.length||isDefined(h$7)&&isDefined(h$7[this.identify])&&200<=h$7[this.identify]?new qs("name","type",!0,this.startTime,this.deep,!0,this.identify):isDefined(this.spanEvent)?(es=new qs(es,ts,!0,this.startTime,this.deep,!1,this.identify),isDefined(this.spanEvent.sub)?this.spanEvent.sub.push(es.spanEvent):this.spanEvent.sub=[es.spanEvent],this.childSave.push(es),es):void 0}setData(es,ts){if(!this.special&&!this.hasFinished&&isDefined(es)&&isDefined(ts)&&typeof es=="string"&&typeof ts=="string"&&!(200<es.length)&&/^[a-zA-Z][a-zA-Z\.\_-]*[a-zA-Z]$|^[a-zA-Z]$/.test(es)&&es.trim()!==""&&ts.trim()!==""&&!(isDefined(this.spanEvent.da)&&64<=this.spanEvent.da.length)&&isDefined(this.spanEvent.da))if(0<this.spanEvent.da.length)if(isDefined(this.dataSave)&&-1<this.dataSave.indexOf(es))for(let is=0;is<this.spanEvent.da.length;is++)isDefined(this.spanEvent.da[is].k)&&this.spanEvent.da[is].k===es&&(this.spanEvent.da[is].k=es,this.spanEvent.da[is].v=ts.slice(0,7e3));else this.spanEvent.da.push({k:es,v:ts.slice(0,7e3)}),this.dataSave.push(es);else this.spanEvent.da.push({k:es,v:ts.slice(0,7e3)}),this.dataSave.push(es)}removeData(es){if(!this.special&&!this.hasFinished&&isDefined(es)&&typeof es=="string"&&!(200<es.length)&&/^[a-zA-Z][a-zA-Z\.\_-]*[a-zA-Z]$|^[a-zA-Z]$/.test(es)&&es.trim()!==""&&isDefined(this.dataSave)&&-1<this.dataSave.indexOf(es))for(let is=0;is<this.spanEvent.da.length;is++){var ts;isDefined(this.spanEvent.da[is].k)&&this.spanEvent.da[is].k===es&&(this.spanEvent.da.splice(is,1),ts=this.dataSave.indexOf(es),this.dataSave.splice(ts,1))}}setTag(es,ts){if(!this.special&&!this.hasFinished&&isDefined(es)&&isDefined(ts)&&typeof es=="string"&&typeof ts=="string"&&!(200<es.length)&&/^[a-zA-Z][a-zA-Z\.\_-]*[a-zA-Z]$|^[a-zA-Z]$/.test(es)&&es.trim()!==""&&ts.trim()!==""&&!(isDefined(this.spanEvent.tag)&&64<=this.spanEvent.tag.length)&&isDefined(this.spanEvent.tag))if(0<this.spanEvent.tag.length)if(isDefined(this.tagSave)&&-1<this.tagSave.indexOf(es))for(let is=0;is<this.spanEvent.tag.length;is++)isDefined(this.spanEvent.tag[is].k)&&this.spanEvent.tag[is].k===es&&(this.spanEvent.tag[is].k=es,this.spanEvent.tag[is].v=ts.slice(0,7e3));else this.spanEvent.tag.push({k:es,v:ts.slice(0,7e3)}),this.tagSave.push(es);else this.spanEvent.tag.push({k:es,v:ts.slice(0,7e3)}),this.tagSave.push(es)}removeTag(es){if(!this.special&&!this.hasFinished&&isDefined(es)&&typeof es=="string"&&!(200<es.length)&&/^[a-zA-Z][a-zA-Z\.\_-]*[a-zA-Z]$|^[a-zA-Z]$/.test(es)&&es.trim()!==""&&isDefined(this.tagSave)&&-1<this.tagSave.indexOf(es))for(let is=0;is<this.spanEvent.tag.length;is++){var ts;isDefined(this.spanEvent.tag[is].k)&&this.spanEvent.tag[is].k===es&&(this.spanEvent.tag.splice(is,1),ts=this.tagSave.indexOf(es),this.tagSave.splice(ts,1))}}setMetric(es,ts,is){if(!this.special&&!this.hasFinished&&isDefined(es)&&isDefined(ts)&&typeof ts=="number"&&typeof es=="string"&&!(200<es.length)&&/^[a-zA-Z][a-zA-Z\.\_-]*[a-zA-Z]$|^[a-zA-Z]$/.test(es)&&es.trim()!==""&&!(isDefined(this.spanEvent.m)&&64<=this.spanEvent.m.length)&&isDefined(this.spanEvent.m))if(0<this.spanEvent.m.length)if(isDefined(this.metricSave)&&-1<this.metricSave.indexOf(es))for(let ns=0;ns<this.spanEvent.m.length;ns++)isDefined(this.spanEvent.m[ns].k)&&this.spanEvent.m[ns].k===es&&(this.spanEvent.m[ns].k=es,this.spanEvent.m[ns].v=Math.round(ts),isDefined(is)&&typeof is=="string"&&is.length<=256&&/^[a-zA-Z0-9:_-\s@./]+$/.test(is)&&is.trim()!==""?this.spanEvent.m[ns].u=is:delete this.spanEvent.m[ns].u);else isDefined(is)&&typeof is=="string"&&is.length<=256&&/^[a-zA-Z0-9:_-\s@./]+$/.test(is)&&is.trim()!==""?this.spanEvent.m.push({k:es,v:Math.round(ts),u:is}):this.spanEvent.m.push({k:es,v:Math.round(ts)}),this.metricSave.push(es);else isDefined(is)&&typeof is=="string"&&is.length<=256&&/^[a-zA-Z0-9:_-\s@./]+$/.test(is)&&is.trim()!==""?this.spanEvent.m.push({k:es,v:Math.round(ts),u:is}):this.spanEvent.m.push({k:es,v:Math.round(ts)}),this.metricSave.push(es)}removeMetric(es){if(!this.special&&!this.hasFinished&&isDefined(es)&&typeof es=="string"&&!(200<es.length)&&/^[a-zA-Z][a-zA-Z\.\_-]*[a-zA-Z]$|^[a-zA-Z]$/.test(es)&&es.trim()!==""&&isDefined(this.metricSave)&&-1<this.metricSave.indexOf(es))for(let is=0;is<this.spanEvent.m.length;is++){var ts;isDefined(this.spanEvent.m[is].k)&&this.spanEvent.m[is].k===es&&(this.spanEvent.m.splice(is,1),ts=this.metricSave.indexOf(es),this.metricSave.splice(ts,1))}}setDuration(es){this.special||this.hasFinished||!isDefined(es)||typeof es!="number"||(this.spanEvent.du=Math.round(es))}setStatus(es){this.special||this.hasFinished||!isDefined(es)||typeof es!="number"||es!==0&&es!==1&&es!==2||(this.spanEvent.sta=es)}setStatusCode(es){this.special||this.hasFinished||!isDefined(es)||typeof es!="string"||7e3<es.length||es.trim()===""||(this.spanEvent.stac=es)}finish(es){if(!this.special&&!this.hasFinished&&(!isDefined(es)&&typeof es!="number"||es!==0&&es!==1&&es!==2||(this.spanEvent.sta=es),this.hasFinished=!0,isDefined(this.spanEvent.da)&&this.spanEvent.da.length===0&&delete this.spanEvent.da,isDefined(this.spanEvent.tag)&&this.spanEvent.tag.length===0&&delete this.spanEvent.tag,isDefined(this.spanEvent.m)&&this.spanEvent.m.length===0&&delete this.spanEvent.m,isDefined(this.spanEvent.du)||(this.spanEvent.du=1e3*new Date().getTime()-this.startTime-this.spanEvent.st),isDefined(this.spanEvent.sta)||(this.spanEvent.sta=1),isDefined(this.spanEvent.st))&&isDefined(this.spanEvent.n)&&isDefined(this.spanEvent.t)&&isDefined(this.spanEvent.ic)){if(isDefined(this.childSave)&&0<this.childSave.length)for(let ts=0;ts<this.childSave.length;ts++)this.childSave[ts].finish();this.isChild||(isDefined(h$7)&&isDefined(h$7[this.identify])&&delete h$7[this.identify],c$3({t:GC.SPAN_DATA,p:{info:this.spanEvent}}))}}};function i$2(J,es){return J=JSON.parse(J),J.k=es,J}function checkMoudle(J){return isDefined(f$2.secondSett)&&isDefined(f$2.secondSett.mn)&&f$2.secondSett.mn.indexOf(J)!==-1}let a$3={log:function(J,es){if(!getDataSize(J,5))return!1;try{d$7(es.osType)?window.webkit.messageHandlers.brsWKLog.postMessage(J):g$8(es.osType)?window.bonreePrivateInterface.call(stringify({log:J})):window.bonreePrivateInterface.log(J)}catch(ts){}},webviewPerformanceTimingEvent:function(J,es,ts){if(!getDataSize(J,5))return!1;if(checkMoudle("h5")){this.log("webviewPerformanceTimingEvent=>"+J,es);try{d$7(es.osType)?window.webkit.messageHandlers.brsWKWebviewPerformanceTimingEvent.postMessage(J):g$8(es.osType)?window.bonreePrivateInterface.call(stringify({h5:[i$2(J,"h5")],imd:ts})):window.bonreePrivateInterface.webViewEventBus(stringify({h5:[i$2(J,"h5")],imd:ts}),es.webviewID)}catch(is){}}},webviewJSErrorEvent:function(J,es){if(!getDataSize(J,5))return!1;if(checkMoudle("jserror")){this.log("webviewJSErrorEvent=>"+J,es);try{d$7(es.osType)?window.webkit.messageHandlers.brsWKWebviewJSErrorEvent.postMessage(J):g$8(es.osType)?window.bonreePrivateInterface.call(stringify({jserror:[i$2(J,"jserror")]})):window.bonreePrivateInterface.webViewEventBus(stringify({jserror:[i$2(J,"jserror")]}),es.webviewID)}catch(ts){}}},NetworkEvent:function(J,es){if(!getDataSize(J,5))return!1;if(checkMoudle("network")){this.log("NetworkEvent=>"+J);try{d$7(es.osType)?window.webkit.messageHandlers.brsWKAjaxPerformanceTimingEvent.postMessage(J):g$8(es.osType)?window.bonreePrivateInterface.call(stringify({network:[i$2(J,"network")]})):window.bonreePrivateInterface.webViewEventBus(stringify({network:[i$2(J,"network")],imd:2}),es.webviewID)}catch(ts){}}},webviewActionEvent:function(J,es){if(!getDataSize(J,5))return!1;if(checkMoudle("action")){this.log("webviewActionEvent=>"+J,es);var ts=JSON.parse(J);try{d$7(es.osType)?(isDefined(ts.v.ice)?ts.v.isa=!0:ts.v.isa=!1,window.webkit.messageHandlers.brsWKWebviewActionEvent.postMessage(stringify(ts))):g$8(es.osType)?window.bonreePrivateInterface.call(stringify({action:[i$2(J,"action")]})):window.bonreePrivateInterface.webViewEventBus(stringify({action:[i$2(J,"action")]}),es.webviewID)}catch(is){}}},webviewPageEvent:function(J,es){if(!getDataSize(J,5))return!1;this.log("webviewPageEvent=>"+J);try{d$7(es.osType)?window.webkit.messageHandlers.brsWKWebviewPageEvent.postMessage(J):g$8(es.osType)?window.bonreePrivateInterface.call(stringify({view:[i$2(J,"view")]})):window.bonreePrivateInterface.webViewEventBus(stringify({view:[i$2(J,"view")]}),es.webviewID)}catch(ts){}},routeChangeData:function(J,es){if(!getDataSize(J,5))return!1;if(checkMoudle("routechange")){this.log("routeChangeData=>"+J,es);try{d$7(es.osType)?window.webkit.messageHandlers.brsWKRouteChangeEvent.postMessage(J):g$8(es.osType)?window.bonreePrivateInterface.call(stringify({routechange:[i$2(J,"routechange")]})):window.bonreePrivateInterface.webViewEventBus(stringify({routechange:[i$2(J,"routechange")]}),es.webviewID)}catch(ts){}}},consoleEvent:function(J,es){if(!getDataSize(J,5))return!1;if(checkMoudle("console")){this.log("consoleData=>",J,es);try{d$7(es.osType)?window.webkit.messageHandlers.brsWKConsoleEvent.postMessage(J):g$8(es.osType)?window.bonreePrivateInterface.call(stringify({console:[i$2(J,"console")]})):window.bonreePrivateInterface.webViewEventBus(stringify({console:[i$2(J,"console")]}),es.webviewID)}catch(ts){}}},spanEvent:function(J,es){if(!getDataSize(J,5))return!1;if(checkMoudle("span")){this.log("spanData=>"+J,es);try{d$7(es.osType)?window.webkit.messageHandlers.brsWKSpanEvent.postMessage(J):g$8(es.osType)?window.bonreePrivateInterface.call(stringify({span:[i$2(J,"span")]})):window.bonreePrivateInterface.webViewEventBus(stringify({span:[i$2(J,"span")]}),es.webviewID)}catch(ts){}}},customLogEvent:function(J,es){if(!getDataSize(J,5))return!1;this.log("setCustomLog=>"+J);try{d$7(es.osType)?window.webkit.messageHandlers.brsWKSetCustomLog.postMessage(J):g$8(es.osType)?window.bonreePrivateInterface.call(stringify({customlog:[i$2(J,"customlog")]})):window.bonreePrivateInterface.webViewEventBus(stringify({customlog:[i$2(J,"customlog")]}),es.webviewID)}catch(ts){}},customMetricEvent:function(J,es){if(!getDataSize(J,5))return!1;this.log("setCustomMetric=>"+J);try{d$7(es.osType)?window.webkit.messageHandlers.brsWKSetCustomMetric.postMessage(J):g$8(es.osType)?window.bonreePrivateInterface.call(stringify({custommetric:[i$2(J,"custommetric")]})):window.bonreePrivateInterface.webViewEventBus(stringify({custommetric:[i$2(J,"custommetric")]}),es.webviewID)}catch(ts){}},customExceptionEvent:function(J,es){if(!getDataSize(J,5))return!1;this.log("setCustomException=>"+J);try{d$7(es.osType)?window.webkit.messageHandlers.brsWKSetCustomException.postMessage(J):g$8(es.osType)?window.bonreePrivateInterface.call(stringify({crash:[i$2(J,"crash")]})):window.bonreePrivateInterface.webViewEventBus(stringify({crash:[i$2(J,"crash")]}),es.webviewID)}catch(ts){}},customSpeedTestEvent:function(J,es){if(!getDataSize(J,5))return!1;this.log("speedTest=>"+J);try{d$7(es.osType)?window.webkit.messageHandlers.brsWKsetCustomSpeedTest.postMessage(J):g$8(es.osType)?window.bonreePrivateInterface.call(stringify({speedtest:[i$2(J,"speedtest")]})):window.bonreePrivateInterface.webViewEventBus(stringify({speedtest:[i$2(J,"speedtest")]}),es.webviewID)}catch(ts){}},customEvent:function(J,es){if(!getDataSize(J,5))return!1;this.log("setCustomEvent=>"+J);try{d$7(es.osType)?window.webkit.messageHandlers.brsWKsetCustomEvent.postMessage(J):g$8(es.osType)?window.bonreePrivateInterface.call(stringify({customevent:[i$2(J,"customevent")]})):window.bonreePrivateInterface.webViewEventBus(stringify({customevent:[i$2(J,"customevent")]}),es.webviewID)}catch(ts){}},MainDocumentNetworkEvent:function(J,es){if(!getDataSize(J,5))return!1;this.log("MainDocumentNetworkEvent=>"+J);try{var ts;d$7(es.osType)?((ts=JSON.parse(J)).imd=1,window.webkit.messageHandlers.brsWKMainDocumentNetworkEvent.postMessage(stringify(ts))):g$8(es.osType)?window.bonreePrivateInterface.call(stringify({network:[i$2(J,"network")],imd:1})):window.bonreePrivateInterface.webViewEventBus(stringify({network:[i$2(J,"network")],imd:1}),es.webviewID)}catch(is){}},ResourceNetworkEvent:function(J,es){if(!getDataSize(J,5))return!1;this.log("ResourceNetworkEvent=>"+J);try{d$7(es.osType)?window.webkit.messageHandlers.brsWKResourceNetworkEvent.postMessage(J):g$8(es.osType)?window.bonreePrivateInterface.call(J):window.bonreePrivateInterface.webViewEventBus(J,es.webviewID)}catch(ts){}}};function a$2(J,es){let ts=null;return ts=(ts=J.Zone&&typeof J.Zone.__symbol__=="function"?J[J.Zone.__symbol__(es)]:ts)||J[es]}function d$3(){let J=null;var es;return window.Zone&&(J=a$2(window,"MutationObserver")),window.MutationObserver&&J===window.MutationObserver&&(es=a$2(new window.MutationObserver(function(){}),"originalInstance"),J=es&&es.constructor),J=J||window.MutationObserver}class PageActivityManager{constructor(es){this.mutationRecord=[],this.performanceRecord=[],this.record=!1,this.unlocked=!0,window.PerformanceObserver&&(this.PerformanceObserver=new PerformanceObserver(ns=>{var ns=ns.getEntries(),rs=isDefined(window.$bonreeReplayUrl)?window.$bonreeReplayUrl:null;ns.some(ss=>ss.entryType==="resource")&&this.record&&(this.performanceRecord.push(now()),isDefined(es))&&es(ns.filter(function(ss){return ss.entryType==="resource"&&!l$2([f$2.sett.uploadAddrHttp,f$2.sett.uploadAddrHttps,rs],ss.name.split("?")[0])}))}));var ts=d$3();isDefined(ts)&&(this.mutationObserver=new ts(is=>{0<this.mutationRecord.lengths&&this.stopPageActivityObserver(1),this.record&&this.mutationRecord.push(now())}))}startPageActivityObserver(){window.PerformanceObserver&&this.PerformanceObserver.observe({entryTypes:["resource"]}),this.mutationObserver.observe(window.document,{attributes:!0,attributeOldValue:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0})}turnOnRecord(){this.record=!0}turnOffRecord(){this.record=!1,this.mutationRecord=[],this.performanceRecord=[]}stopPageActivityObserver(es){(isDefined(es)||(window.PerformanceObserver&&this.PerformanceObserver.disconnect(),isDefined(this.mutationObserver)))&&this.mutationObserver.disconnect()}}function l$2(J,es){return-1<J.indexOf(es)}function checkRecord(J,es){if(!(J.length===0||J[J.length-1]<es)){for(var ts=0;ts<=J.length;ts++)if(0<=(J[ts]-es)/1e3)return!0}return!1}var e$2=new PageActivityManager;class Metric{constructor(es,ts){this.d_type=ts,this.data=es||{},this.$metric={},ts=es&&es.info&&es.info.timestamp&&es.info.timestamp||now(),this.ent=ts,u$1.setUserInfoTimeStamp!=0&&(this.userMapIndex=u$1.lastUserInfoSetMapKey)}}function d$2(J,es){this.$metric[J]=es}function pushQueueData(J,es){if(isDefined(J)&&isDefined(S$2)){let ts=size(J);S$2.push({data:J,size:ts}),J=setTotalSize(ts),getMaxSize(),J>n$1.CAPACITY&&g$6()}}function k(J,is){var ts=is.fullCollect||is.fc,is=is.whiteList||is.wl;if(!isDefined(ts)||ts)return 1;if(isDefined(is)){try{var ns=J,rs="";if(ns.indexOf("http")!==0&&ns.indexOf("file:")!==0&&ns.indexOf("ws")!==0)return;rs=(isDefined(ns.match(/:\/\/\[([^\]]+)\]/))?ns.match(/:\/\/\[([^\]]+)\]/):ns.match(/:\/\/(.[^/:]+)/))[1]}catch(fs){return}let ss=isDefined(is.hostRule||is.hr)?(is.hostRule||is.hr).slice(0,20):[],as=isDefined(is.pathRule||is.pr)?(is.pathRule||is.pr).slice(0,20):[],os=function(fs,us){var cs=us.split("*");return!(2<cs.length)&&(us.indexOf("*")===-1?us===fs:cs[0]!==""&&cs[1]!==""?new RegExp("("+cs[0]+").+("+cs[1]+")").test(fs):cs[0]==""&&cs[1]!==""?!!endWith(fs,cs[1]):cs[0]!==""&&cs[1]==""?!!startWith(fs,cs[0]):void 0)},ds=function(fs,us){return fs=fs.split("?")[0],os(fs,us)},ls=function(fs,us){var cs,hs=us===1?os:ds,ms=us===1?rs:ns;for(cs of fs)if(hs(ms,cs))return!0;return!1};return ls(ss,1)||ls(as,2)}}function O$3(J){var es=void 0;return isDefined(J.collectionStrategy)?es=J.collectionStrategy:isDefined(J.mc)&&J.mc instanceof Array&&J.mc.forEach(function(ts){ts.n==="network"&&isDefined(ts.cs)&&(es=ts.cs)}),es}function A$4(J,es,ts){{var is=J.mk||J.maskKeys,ns=J.mt||J.maskType,J=isDefined(is.qka||is.urlKeys)&&(is.qka||is.urlKeys);if(is.aqk||is.allQueryKeys)switch(ns){case 1:es.p.info.url=es.p.info.url.split("?")[0],isDefined(ts.reqURLKey)&&(es.p.info.reqUrlDta=null);break;case 2:es.p.info.url=es.p.info.url.replace(/(\?|&)([^=&]+)=([^&]+)/g,"$1$2=**"),isDefined(ts.reqURLKey)&&(es.p.info.reqUrlDta=es.p.info.url.split("?")[1]);break;default:return}else if(isDefined(J)&&0<J.length&&es.p.info&&es.p.info.url&&-1<es.p.info.url.indexOf("?")){var rs=es.p.info.url.split("?")[1].split("&"),ss="";for(let $s=0;$s<rs.length;$s++){var as=rs[$s].split("=");switch(ns){case 1:includes(is.qka||is.urlKeys,as[0])||(ss+=rs[$s]+"&");break;case 2:includes(is.qka||is.urlKeys,as[0])?ss+=rs[$s].split("=")[0]+"=**&":ss+=rs[$s]+"&"}}isDefined(ts.reqURLKey)&&(es.p.info.reqUrlDta=ss.slice(0,-1)),ss.slice(0,-1)!==""?es.p.info.url=es.p.info.url.split("?")[0]+"?"+ss.slice(0,-1):es.p.info.url=es.p.info.url.split("?")[0]}let ms=isDefined(is.reqhka||is.requestHeadersKeys)?is.reqhka||is.requestHeadersKeys:[];if(ms=ms.map(function($s){return $s.toLowerCase()}),is.areqh||is.allRequestHeaders)switch(ns){case 1:es.p.info[GC.REQUEST_HEADER]=void 0,es.p.info.reqhtData={};break;case 2:isDefined(es.p.info[GC.REQUEST_HEADER])&&(es.p.info[GC.REQUEST_HEADER]=es.p.info[GC.REQUEST_HEADER].replace(/:(.*?)($|\r\n)/g,":**$2")),isDefined(ts.reqHeaderTraceKey)&&0<ts.reqHeaderTraceKey.length&&(isEmpty(es.p.info.reqhtData)||forEach(es.p.info.reqhtData,function($s,gs){es.p.info.reqhtData[gs]="**"}));break;default:return}else switch(ns){case 1:if(isDefined(es.p.info[GC.REQUEST_HEADER])&&isDefined(ms)&&0<ms.length)for(var os of ms)os=new RegExp(os+`:(.*?)(\r
|$)`,"i"),os.test(es.p.info[GC.REQUEST_HEADER])&&(es.p.info[GC.REQUEST_HEADER]=es.p.info[GC.REQUEST_HEADER].replace(os,""));if(isDefined(ts.reqHeaderTraceKey)&&0<ts.reqHeaderTraceKey.length&&!isEmpty(es.p.info.reqhtData)){for(var ds in es.p.info.reqhtData)if(includes(ms,ds.toLowerCase())){delete es.p.info.reqhtData[ds];continue}}break;case 2:if(isDefined(es.p.info[GC.REQUEST_HEADER])&&isDefined(ms)&&0<ms.length)for(var ls of ms)ls=new RegExp(ls+`:(.*?)(\r
|$)`,"i"),ls.test(es.p.info[GC.REQUEST_HEADER])&&(es.p.info[GC.REQUEST_HEADER]=es.p.info[GC.REQUEST_HEADER].replace(ls,function($s,gs){return $s.replace(gs,"**")}));if(isDefined(ts.reqHeaderTraceKey)&&0<ts.reqHeaderTraceKey.length&&!isEmpty(es.p.info.reqhtData)){for(var fs in es.p.info.reqhtData)if(includes(ms,fs.toLowerCase())){es.p.info.reqhtData[fs]="**";continue}}break;default:return}let ps=isDefined(is.reshka||is.responseHeadersKeys)?is.reshka||is.responseHeadersKeys:[];if(ps=ps.map(function($s){return $s.toLowerCase()}),is.aresh||is.allResponseHeaders)switch(ns){case 1:isDefined(es.p.info.responseHeader)&&delete es.p.info.responseHeader,isEmpty(es.p.info.reshtData)||delete es.p.info.reshtData,isDefined(es.p.info.guid)&&delete es.p.info.guid,isDefined(es.p.info.xBrResponse)&&delete es.p.info.xBrResponse,isDefined(es.p.info.traceResponse)&&delete es.p.info.traceResponse;break;case 2:isDefined(es.p.info.responseHeader)&&(es.p.info.responseHeader=es.p.info.responseHeader.replace(/:(.*?)($|\r\n)/g,":**$2")),isEmpty(es.p.info.reshtData)||forEach(es.p.info.reshtData,function($s,gs){es.p.info.reshtData[gs]="**"}),isDefined(es.p.info.guid)&&es.p.info.guid!==""&&(es.p.info.guid="**"),isDefined(es.p.info.xBrResponse)&&es.p.info.xBrResponse!=""&&(es.p.info.xBrResponse="**"),isDefined(es.p.info.traceResponse)&&es.p.info.traceResponse!==""&&(es.p.info.traceResponse="**")}else switch(ns){case 1:if(isDefined(es.p.info.responseHeader))for(var us of ps)us=new RegExp(us+`:(.*?)(\r
|$)`,"i"),us.test(es.p.info.responseHeader)&&(es.p.info.responseHeader=es.p.info.responseHeader.replace(us,""));isEmpty(es.p.info.reshtData)||forEach(es.p.info.reshtData,function($s,gs){includes(ps,gs.toLowerCase())&&delete es.p.info.reshtData[gs]}),isDefined(es.p.info.guid)&&includes(ps,"br-resp-key")&&delete es.p.info.guid,isDefined(es.p.info.xBrResponse)&&includes(ps,"x-br-response")&&delete es.p.info.xBrResponse,isDefined(es.p.info.traceResponse)&&includes(ps,"traceresponse")&&delete es.p.info.traceResponse;break;case 2:if(isDefined(es.p.info.responseHeader))for(var cs of ps)cs=new RegExp(cs+`:(.*?)(\r
|$)`,"i"),cs.test(es.p.info.responseHeader)&&(es.p.info.responseHeader=es.p.info.responseHeader.replace(cs,function($s,gs){return $s.replace(gs,"**")}));isEmpty(es.p.info.reshtData)||forEach(es.p.info.reshtData,function($s,gs){includes(ps,gs.toLowerCase())&&(es.p.info.reshtData[gs]="**")}),isDefined(es.p.info.guid)&&includes(ps,"br-resp-key")&&(es.p.info.guid="**"),isDefined(es.p.info.xBrResponse)&&includes(ps,"x-br-response")&&(es.p.info.xBrResponse="**"),isDefined(es.p.info.traceResponse)&&includes(ps,"traceresponse")&&(es.p.info.traceResponse="**")}}}function d$1(J,es){if(isDefined(es)&&(isDefined(es.sensitiveNetworkRule)||isDefined(es.snr))){try{var ts,is=J.p.info.url;if(is.indexOf("http")!==0&&is.indexOf("file:")!==0&&is.indexOf("ws")!==0)return J;ts=is.match(/:\/\/(.[^/:?]+)/)[1]}catch(os){return J}var ns,rs=es.sensitiveNetworkRule||es.snr,ss=function(os){if(os.type===1||os.t===1){if(!isDefined(os.c||os.content))return!1;switch(os.r||os.rule){case 1:if(ts.toLowerCase()===(os.c||os.content).toLowerCase())return!0;break;case 2:if(-1<ts.toLowerCase().indexOf((os.c||os.content).toLowerCase()))return!0;break;case 3:if(startWith(ts.toLowerCase(),(os.c||os.content).toLowerCase()))return!0;break;case 4:if(endWith(ts.toLowerCase(),(os.c||os.content).toLowerCase()))return!0;default:return!1}}if(os.type===2||os.t===2)switch(os.r||os.rule){case 1:if(is===(os.c||os.content))return!0;break;case 2:if(-1<is.indexOf(os.c||os.content))return!0;break;case 3:if(startWith(is,os.c||os.content))return!0;break;case 4:if(endWith(is,os.c||os.content))return!0;default:return!1}return!1},as=function(os,ds){switch(ds){case 1:for(var ls of os)if(ss(ls))return!0;return!1;case 2:for(var fs of os)if(!ss(fs))return!1;return!0;default:return!1}};for(ns of rs)ns.t!==1&&ns.type!==1?ns.t!==2&&ns.type!==2||as(ns.sa||ns.scopes,2)&&A$4(ns,J,es):as(ns.sa||ns.scopes,1)&&A$4(ns,J,es)}return J}function x$3(J){let es=0,ts=0,is=0,ns=0,rs=0,ss=0,{domainLookupStart:as,domainLookupEnd:os,connectStart:ds,connectEnd:ls,responseStart:fs,responseEnd:us,secureConnectionStart:cs,fetchStart:hs,requestStart:ms}=J.p.metric;isDefined(as)&&isDefined(os)&&0<as&&(ps=os-as||-1,es=0<ps?ps:0),isDefined(ds)&&isDefined(ls)&&0<ds&&(ps=ls-ds||-1,ts=0<ps?ps:0),isDefined(ls)&&isDefined(cs)&&0<cs&&(ps=ls-cs||-1,is=0<ps?ps:0),isDefined(fs)&&0<fs?(isDefined(ms)&&0<ms&&(ps=fs-ms||-1,rs=0<=ps?ps:999),isDefined(ls)&&0<ls&&(ps=ms-ls||-1,ns=0<=ps?ps:999),isDefined(us)&&(ps=us-fs||-1,ss=0<=ps?ps:999)):isDefined(hs)&&0<hs&&(ps=us-hs||-1,ss=0<=ps?ps:999);var ps=isDefined(J.p.metric)?J.p.metric:{};ps.domianTime=es,ps.connetcTime=ts,ps.sslt=is,ps.rti=rs,ps.dti=ss,ps.rt=ns}function g$5(J,es){if(isDefined(es.reqBodyKey)){let ts="";if(isJSON(J.p.info.requestBody))try{let is=new Function("","return "+J.p.info.requestBody)();forEach(es.reqBodyKey,function(ns,rs){isDefined(is[ns])&&(ts+=`${ns}=${escape(is[ns])}&`)}),J.p.info[GC.NET_CBBQ]=ts.slice(0,-1)}catch(is){}else if(isString(J.p.info.requestBody))try{forEach(J.p.info.requestBody.split("&"),function(is){forEach(es.reqBodyKey,function(ns,rs){ns&&is.indexOf(ns+"=")!==-1&&(ts+=is.split("=")[0]+"="+escape(is.split("=")[1])+"&")})}),J.p.info[GC.NET_CBBQ]=ts.slice(0,-1)}catch(is){}}if(isDefined(es.reqURLKey)&&isString(J.p.info.reqUrlDta))try{let ts="";forEach(J.p.info.reqUrlDta.split("&"),function(is){forEach(es.reqURLKey,function(ns,rs){ns&&is.split("=")[0]===ns&&(ts+=is.split("=")[0]+"="+escape(is.split("=")[1])+"&")})}),J.p.info[GC.NET_CBQ]=ts.slice(0,-1)}catch(ts){}else delete J.p.info[GC.NET_CBQ];if(isDefined(J.p.metric)&&isDefined(J.p.metric[GC.CALLBACK_END])&&isDefined(J.p.metric[GC.CALLBACK_START])&&(J.p.metric[GC.CALLBACK_TIME]=J.p.metric[GC.CALLBACK_END]-J.p.metric[GC.CALLBACK_START],J.p.metric[GC.CALLBACK_TIME]=0<J.p.metric[GC.CALLBACK_TIME]&&1e3*J.p.metric[GC.CALLBACK_TIME]||0),isDefined(es.reqHeaderKey)&&isDefined(J.p.info)&&isDefined(J.p.info[GC.REQUEST_HEADER]))try{let ts="";forEach(J.p.info[GC.REQUEST_HEADER].split(`\r
`),function(is){forEach(es.reqHeaderKey,function(ns){isDefined(ns)&&ns!=""&&is.split(":")[0].toLowerCase()===ns.toLowerCase()&&(ts+=is.split(":")[0]+"="+escape(is.split(":")[1])+"&")})}),J.p.info[GC.NET_CBHQ]=ts.slice(0,-1)}catch(ts){}return J}function B(J){var es=J.data;return isDefined(es.tid)&&es.tid===""&&delete es.tid,isDefined(es.xbr)&&es.xbr===""&&delete es.xbr,isDefined(es.trsp)&&es.trsp===""&&delete es.trsp,isDefined(es.tpar)&&es.tpar===""&&delete es.tpar,isDefined(es.cbhq)&&es.cbhq===""&&delete es.cbhq,isDefined(es.cbbq)&&es.cbbq===""&&delete es.cbbq,isDefined(es.cbq)&&es.cbq===""&&delete es.cbq,isDefined(es.ti)&&es.ti===""&&delete es.ti,isDefined(es.ec)&&200<=es.ec&&es.ec<400&&(isDefined(es.ep)&&delete es.ep,isDefined(es.eop))&&delete es.eop,isDefined(es.ec)&&400<es.ec&&es.ec<=602&&isDefined(es.eop)&&(es.eop=4),isDefined(es.cbhq)&&(es.cbhq=es.cbhq.replace("/","%2F")),isDefined(es.ec)&&es.ec===602&&isDefined(es.ru)&&es.ru.indexOf("file:")===0&&(es.ec=200,delete es.eop,delete es.ep,delete es.em),J}function U$5(J,es){if(isDefined(es.reqHeaderTraceKey)&&0<es.reqHeaderTraceKey.length){let ts={};isDefined(J.p.info.reqhtData&&Object.getOwnPropertyNames(J.p.info.reqhtData).length<=64)?ts=J.p.info.reqhtData:Object.getOwnPropertyNames(J.p.info.reqhtData).slice(0,64).forEach(function(is){ts[is]=J.p.info.reqhtData[is]}),isEmpty(ts)||(J.p.info.reqht=ts)}if(isDefined(es.respHeaderTraceKey)&&0<es.respHeaderTraceKey.length){let ts={},is=0;for(let ns=0,rs=es.respHeaderTraceKey.length-1;ns<=rs;ns++)is<64&&isDefined(J.p.info.reshtData)&&isDefined(J.p.info.reshtData[es.respHeaderTraceKey[ns]])&&(is+=1,ts[es.respHeaderTraceKey[ns]]=J.p.info.reshtData[es.respHeaderTraceKey[ns]]);isEmpty(ts)||(J.p.info.resht=ts)}}function Q$1(J,es,ts=!1){var is={};if(J){var is=JSON.parse(JSON.stringify(J)),ns=O$3(es);if(!ts&&isDefined(ns)&&!k(is.p.info.url||"",ns))return null;try{if(isDefined(window.$bonreeReplayUrl)&&-1<is.p.info.url.indexOf(window.$bonreeReplayUrl))return null}catch(ss){}return isDefined(is.p.metric)&&x$3.call(this,is),isDefined(is.p.metric)&&x$3.call(this,is),isDefined(es.sensitiveNetworkRule||es.snr)&&d$1.call(this,is,es),g$5.call(this,is,es),U$5.call(this,is,es),ts=is.p.info,ns=es.hcs,es=(isDefined(ns)&&(isDefined(ts.requestHeader)||isDefined(ts.responseHeader))&&(ns===0||ns===1&&ts.status<400||ns===1&&ts.code<400)&&(delete is.p.info.requestHeader,delete is.p.info.responseHeader),is&&is.p.info.url&&-1<is.p.info.url.indexOf("https")?2:1),ns=getMineTypeByHeader(is.p.info[GC.RESPONSE_HEADER])||getMineTypeByHeader(is.p.info[GC.REQUEST_HEADER])||getMineTypeByUrl(is.p.info[GC.URL])||"text/html",ts=new Metric(is.p,GC.NET),(isDefined(ts.data.info[GC.NET_EC])&&400<ts.data.info[GC.NET_EC]||!isDefined(ts.data.info[GC.NET_EC]))&&isDefined(window.BRLog)&&window.BRLog.__sendNetInfo(ts.data.info),ts.info("id").info(GC.URL,"","ru").info(GC.NET_METHOD,"","m").info(GC.NET_IP,"","ti").info(GC.NET_PORT,0,"tp").info(GC.REQUEST_HEADER,"","rh",!0).info(GC.RESPONSE_HEADER,"","rhe",!0).info(GC.NET_TID,"","tid").info(GC.NET_XBR,"","xbr").info(GC.NET_TRACE,"","trsp").info("reqht","","reqht",!0).info("resht","","resht",!0).info(GC.E_TYPE,"http","ep",!0).info(GC.NET_EOP,"","eop",!0).info(GC.NET_TYPE,3,"art").info(GC.PAGE_ID,"","pvid").info(GC.CUSTOM_IC,!1,"ic").info(GC.NET_CBBQ,"","cbbq").info(GC.NET_CBHQ,"","cbhq").info(GC.NET_CBQ,"","cbq").info("ret",ns,"ret").metric("domianTime",0,"dt").metric("connetcTime",0,"ct").metric("sslt",0,"sslt").metric("rt",0,"rt").metric("rti",0,"rti").metric("dti",0,"dti").metric(GC.DECODED_BODY_SIZE,0,"ds").metric(GC.UPLOAD_BODY_SIZE,0,"rds").metric(GC.NET_PT,es,"pt"),is.p&&is.p.info.ai&&ts.info("ai","","ai"),(is.p&&is.p.info[GC.NET_EC]==200||is.p&&is.p[GC.STATUS]==200)&&ts.info(GC.NET_EOP,0,"eop"),is.p&&isDefined(is.p.info[GC.NET_EC])&&ts.info(GC.NET_EC,200,"ec"),is.p&&isDefined(is.p.info[GC.STATUS])&&ts.info(GC.STATUS,200,"ec"),is.p&&is.p.info[GC.E_TYPE]==="XHR"&&ts.info(GC.MESSAGE,"","em",!0),is.p&&is.p.info[GC.E_TYPE]==="FETCH"&&ts.info(GC.STATUS_TEXT,"","em",!0),B(ts.build())}}function P$3(ts){var es=new Metric(ts,GC.NET),ts=getMineTypeByUrl(ts[GC.URL])||"text/html";return es.info("id").info("ru").info("m").info("ti").info("tp").info("art").info("pvid","","pvid").info("ic").info("ret",ts,"ret").info("dt").info("ct").info("sslt").info("rt").info("rti").info("dti").info("ds",0,"ds").info("rds",0,"rds").info("pt").info("ec",200,"ec"),B(es.build())}Metric.prototype.info=function(J,es="",ts,is=!1){var ns=this.data.info;if(isDefined(ns)){let rs=ns[J];isDefined(rs)||(rs=es),isDefined(ts)&&(J=ts),is&&rs===""||(this.$metric[J]=rs)}return this},Metric.prototype.metric=function(J,es="",ts){var is=this.data.metric;if(isDefined(is)){let ns=is[J];isDefined(ns)||(ns=isDefined(es)?es:-1),isDefined(ts)&&(J=ts),d$2.call(this,J,ns)}return this},Metric.prototype.build=function(es){var es={type:this.d_type,ent:(es&&es.ent?es:this).ent,data:{},sin:[isDefined(this.userMapIndex)?this.userMapIndex:"","",""]},ts=this.$metric;if(isDefined(this.data.ext))switch(this.d_type){case GC.ACTION:case GC.NET:case GC.ERROR:case GC.ROUTE:break;case GC.PAGE:case GC.RESOURCE:case GC.WEBVIEWDATA:default:ts.ext=this.data.ext}return es.data=ts,es};let m$4=function(J,es,ts,is,ns,rs){return!(J===void 0||(es==null||es===""?(a$3.log(ts+" Parameter has a value or key ["+J+"] that does not exist!"),rs):is!==null&&es.length>is?(a$3.log(ts+" Parameter has a value or key ["+J+"] that the value length exceeds "+es.length+"!"),0):!ns||!isNaN(es)||(a$3.log(ts+" Parameter has a value or key ["+J+"] value is not a valid number!"),0)))},E$3=2083,p$3=256,R=function(J){var es={};if(J!==void 0&&Object.prototype.toString.call(J)==="[object Object]"){if(64<Object.getOwnPropertyNames(J).length){var ts,is=Object.getOwnPropertyNames(J).slice(0,64);for(ts in is)typeof is[ts]!="string"||256<is[ts].length||is[ts]===""||(es[is[ts]]=J[is[ts]])}else for(var ns in J)typeof ns!="string"||256<ns.length||ns===""||(es[ns]=J[ns]);return Object.getOwnPropertyNames(es).length===0?void 0:es}},y$2={},M$3={},x$2={},P$2={},T$3=[],O$2=[];function w$2(J,es){!isDefined(f$2.secondSett)||isEmpty(f$2.secondSett)||isEmpty(J)||(isDefined(J.data.url)&&(J.data.url=d$1({p:{info:{url:J.data.url}}},f$2.secondSett).p.info.url),isDefined(f$2.secondSett.osType)&&f$2.secondSett.osType!==GC.BROWSER?es.call(a$3,stringify({ent:J.ent,v:J.data}),f$2.secondSett):pushQueueData(J)),!isDefined(f$2.sett)||isEmpty(f$2.sett)||isEmpty(J)||(isDefined(f$2.sett.osType)&&f$2.sett.osType!==GC.BROWSER?es.call(a$3,stringify({ent:J.ent,v:J.data}),f$2.sett):pushQueueData(J))}let q$2={setCustomSpeedTest:function(J,es){var ts;arguments.length<1||2<arguments.length||J&&256<J.length||(Array.isArray(es)&&0<es.length&&es.length<=1e3?(ts={},isDefined(J)&&J!==""&&(ts.oip=J,ts.sti=es,(J=new Metric({info:ts},"speedtest")).info("oip").info("sti"),w$2(J.build(),a$3.customSpeedTestEvent))):log("C156"))},setExtraInfo:function(J){a$3.log("setExtraInfo=>"+J);var es=J&&set_64_keys(J,64)||J;try{M$7(es),d$7(f$2.sett.osType)||d$7(f$2.secondSett.osType)?window.webkit.messageHandlers.brsWKSetExtraInfo.postMessage(stringify(es)):g$8(f$2.sett.osType)||g$8(f$2.secondSett.osType)?window.bonreePrivateInterface.call(stringify({setExtraInfo:es})):window.bonreePrivateInterface.setExtraInfo(stringify(es))}catch(ts){}},setUserID:function(J){if(a$3.log("setUserID=>"+J),!m$4("userId",J,"setUserID",p$3,!1)&&/^[\u4E00-\u9FFFa-zA-Z0-9:_\-@.\s/]+$/.test(J))try{y$5(J),d$7(f$2.sett.osType)||d$7(f$2.secondSett.osType)?window.webkit.messageHandlers.brsWKSetUserId.postMessage(J):g$8(f$2.sett.osType)||g$8(f$2.secondSett.osType)?window.bonreePrivateInterface.call(stringify({setUserID:J})):window.bonreePrivateInterface.setUserID(J)}catch(es){}},setCustomEventWithLabel:function(J,es,ts,is,ns){var rs;arguments.length<1||5<arguments.length||m$4("eventId",J,"setCustomEvent",p$3,!1)||isDefined(ns)&&7e3<JSON.stringify(ns).length||(rs={},es=set_length(es,p$3),is=set_length(is,7e3),rs.eventId=isDefined(J)?J:"",rs.eventName=es,rs.eventParam=is,rs.eventLabel=set_length(ts,256),rs.info=R(ns),(J=new Metric({info:rs},GC.CUSTOM_EVENT)).info("eventId","","i").info("t",0).info("d",0),rs.eventName!==void 0&&rs.eventName!==""&&rs.eventName!==null&&J.info("eventName","","n"),rs.eventLabel!==void 0&&rs.eventLabel!=="unuse"&&rs.eventLabel!==""&&rs.eventLabel!==null&&J.info("eventLabel","","l"),rs.eventParam!==void 0&&rs.eventParam!==""&&rs.eventParam!==null&&J.info("eventParam","","p"),rs.info!==void 0&&rs.info instanceof Object&&J.info("info","","info"),w$2(J.build(),a$3.customEvent))},setCustomEvent:function(J,es,ts,is){this.setCustomEventWithLabel(J,es,"unuse",ts,is)},setCustomLog:function(J,es){var ts;arguments.length<1||2<arguments.length?a$3.log("setcustomlog custom log parameter number error"):m$4("logInfo",J,"setCustomLog",null,!1)||(ts={},J=isDefined(J)&&set_length(J,1e4)||"",es=isDefined(es)?set_length(es,1e4):es,ts.logInfo=J,ts.logParam=es,(J=new Metric({info:ts},GC.CUSTOM_LOG)).info("logInfo","","i"),ts.logParam!==void 0&&J.info("logParam","","p"),w$2(J.build(),a$3.customLogEvent))},setCustomMetric:function(J,es,ts){var is;arguments.length<1||3<arguments.length?a$3.log("setCustomMetric\u81EA\u5B9A\u4E49\u6307\u6807\u53C2\u6570\u4E2A\u6570\u9519\u8BEF"):m$4("metricName",J,"setCustomMetric",p$3,!1)||m$4("metricValue",es,"setCustomMetric",null,!1)||((is={}).metricName=isDefined(J)?J:"",is.metricValue=isDefined(es)?Math.round(es):"",isDefined(ts)&&(is.metricParam=1e4<ts.length?ts.slice(0,1e4):ts),(J=new Metric({info:is},GC.CUSTOM_METRIC)).info("metricName","","n").info("metricValue",0,"v"),ts!==void 0&&J.info("metricParam","","p"),w$2(J.build(),a$3.customMetricEvent))},setCustomException:function(J,es,ts){var is;arguments.length<1||3<arguments.length?a$3.log("onEvent\u81EA\u5B9A\u4E49\u4E8B\u4EF6\u53C2\u6570\u4E2A\u6570\u9519\u8BEF"):m$4("exceptionType",J,"setCustomException",p$3,!1)||(is={},es=isDefined(es)&&set_length(es,512)||"",ts=isDefined(ts)&&set_length(ts,1e4)||"",is.exceptionType=isDefined(J)?J:"",is.causedBy=es,is.errorDump=ts,(J=new Metric({info:is},GC.CRASH)).info("causedBy","","cab").info("exceptionType","","t").info("errorDump","","p").info("ic",!0).info("id",uuid()),w$2(J.build(),a$3.customExceptionEvent))},setCustomPageStart:function(J,es){var ts;arguments.length<1||2<arguments.length||m$4("pageName",J,"setCustomPageStart",p$3,!1)||includes(T$3,J)||(T$3.push(J),y$2[J]=dateNow(),M$3[J]=buildViewID(),!J)||(ts=set_length(es,p$3),(ts=new Metric({info:{pageName:J,param:ts}},GC.VIEW)).info("pageName","","n").info("ci",M$3[J]).info("lt",0).info("m",1).info("t",1).info("ic",!0),es!==void 0&&ts.info("param","","p"),w$2(ts.build(),a$3.webviewPageEvent))},setCustomPageEnd:function(J,es){if(!(arguments.length<1||2<arguments.length||m$4("pageName",J,"setCustomPageEnd",p$3,!1))&&includes(T$3,J)){let is=0;var ts;isDefined(dateNow)&&y$2&&isDefined(y$2[J])&&(is=dateNow()-y$2[J],is*=1e3,J)&&(ts=set_length(es,p$3),ts={pageName:J,param:ts},T$3=filter(T$3,function(ns){return ns!==J}),delete y$2[J],(ts=new Metric({info:ts},GC.VIEW)).info("pageName","","n").info("ci",M$3[J]).info("st",is).info("lt",0).info("m",2).info("t",1).info("ic",!0),es!==void 0&&ts.info("param","","p"),w$2(ts.build(),a$3.webviewPageEvent),delete M$3[J])}},setCustomH5performanceData:function(J){let es={};try{es=JSON.parse(J)}catch(rs){return void a$3.log("setCustomH5performanceData has error! ==>"+rs)}let ts=!0;if(ts&&forEach(["url"],function(rs){m$4(rs,es[rs],"setCustomH5performanceData",E$3,!1)&&(es[rs]=set_length(es[rs],E$3))}),ts&&forEach(["imd","ns","ues","uee","rds","rde","fs","dls","dle","cs","scs","ce","reqs","rsps","rspe","dl","di","dcles","dclee","dc","les","lee","fp","fcp","lcp"],function(rs){if(m$4(rs,es[rs],"setCustomH5performanceData",null,!0))return ts=!1}),ts&&((es.pvid===void 0||es.pvid===null||es.pvid===""||es.pvid.length>p$3)&&(es.pvid=uuid()),es={url:es.url,pvid:es.pvid,ic:!0,wpi:{ns:isDefined(es.ns)&&0<es.ns?es.ns:0,ues:isDefined(es.ues)&&0<es.ues?es.ues:-1,uee:isDefined(es.uee)&&0<es.uee?es.uee:-1,rds:isDefined(es.rds)&&0<es.rds?es.rds:-1,rde:isDefined(es.rde)&&0<es.rde?es.rde:-1,fs:isDefined(es.fs)&&0<es.fs?es.fs:0,dls:isDefined(es.dls)&&0<es.dls?es.dls:-1,dle:isDefined(es.dle)&&0<es.dle?es.dle:-1,cs:isDefined(es.cs)&&0<es.cs?es.cs:-1,scs:isDefined(es.scs)&&0<es.scs?es.scs:-1,ce:isDefined(es.ce)&&0<es.ce?es.ce:-1,reqs:isDefined(es.reqs)&&0<es.reqs?es.reqs:0,rsps:isDefined(es.rsps)&&0<es.rsps?es.rsps:0,rspe:isDefined(es.rspe)&&0<es.rspe?es.rspe:0,dl:isDefined(es.dl)&&0<es.dl?es.dl:0,di:isDefined(es.di)&&0<es.di?es.di:0,dcles:isDefined(es.dcles)&&0<es.dcles?es.dcles:0,dclee:isDefined(es.dclee)&&0<es.dclee?es.dclee:0,dc:isDefined(es.dc)&&0<es.dc?es.dc:0,les:isDefined(es.les)&&0<es.les?es.les:0,lee:isDefined(es.lee)&&0<es.lee?es.lee:-1,fp:isDefined(es.fp)&&0<es.fp?es.fp:-1,fcp:isDefined(es.fcp)&&0<es.fcp?es.fcp:-1,lcp:isDefined(es.lcp)&&0<es.lcp?es.lcp:-1}},isDefined(es.url))&&es.url.trim("")!==""){if(y$6(f$2.sett.osType)||y$6(f$2.secondSett.osType)){let rs=new Metric({info:es},GC.WEBVIEWDATA);rs.info("url","").info("ic",!0).info("pvid").info("wpi"),pushQueueData(rs.build())}var is=stringify({h5:[{ent:now(),v:es,k:"h5"}],imd:es.imd}),ns=stringify({ent:now(),v:es});a$3.log("setCustomH5performanceData=> "+ns);try{d$7(f$2.sett.osType)||d$7(f$2.secondSett.osType)?window.webkit.messageHandlers.brsWKSetCustomH5performanceData.postMessage(ns):g$8(f$2.sett.osType)||g$8(f$2.secondSett.osType)?window.bonreePrivateInterface.call(is):window.bonreePrivateInterface.webViewEventBus(is,f$2.sett.webviewID||f$2.secondSett.webviewID)}catch(rs){}}},setCustomRouteChangeData:function(J){let es={};try{(es=JSON.parse(J)).d=1e3*Math.round(es.d)}catch(ns){return void a$3.log("setCustomRouteChangeData has error! ==>"+ns)}let ts=!0;var is;ts&&forEach(["tu","fu","rt","pu"],function(ns){(!isDefined(es[ns])||es[ns]===""||es[ns].length>E$3)&&(ts=!1)}),ts&&forEach(["pt","fw"],function(ns){(!isDefined(es[ns])||es[ns]===""||es[ns].length>p$3)&&(ts=!1)}),ts&&forEach(["d","sta"],function(ns){isDefined(es[ns])&&typeof es[ns]=="number"||(ts=!1)}),ts&&(es.fu===void 0&&es.fu===null?a$3.log("setCustomRouteChangeData Parameter has a value or key [fu] that does not exist!"):(es.al!==void 0&&es.al!==null&&es.al.length>p$3&&(a$3.log("setCustomRouteChangeData Parameter has a value or key [al] that the value length exceeds "+p$3+"!"),es.al=""),es.ent=1e3*new Date().getTime(),(is=new Metric({info:es},GC.ROUTE)).info("tu","").info("fu","").info("d",0).info("sta",0).info("pt","").info("rt","").info("pu","").info("fw","").info("ic",!0).info("ctp",2).info("pvid",isDefined(f$2.sett.pageViewId)?f$2.sett.pageViewId:""),isDefined(es.al)&&es.al!==""&&is.info("al",""),isDefined(window.performance)&&isDefined(window.performance.timing)&&is.info("ns",window.performance.timing.navigationStart),w$2(is.build(),a$3.routeChangeData)))},setCustomNetworkData:function(J){let es={};try{es=JSON.parse(J)}catch(ns){return void a$3.log("setCustomNetworkData has error! ==>"+ns)}let ts=!0;var is;forEach(["tp","dt","ct","sslt","rt","rti","dti","ds","pt","rds"],function(ns){if(m$4(ns,es[ns],"setCustomNetworkData",null,!0)||es[ns]<0)return ts=!1}),m$4("ti",es.ti,"setCustomNetworkData",null,!1)&&(ts=!1),isDefined(es.ru)&&es.ru!==""&&(m$4("ru",es.ru,"setCustomNetworkData",E$3,!1)&&(es.ru=set_length(es.ru,E$3)),m$4("m",es.m,"setCustomNetworkData",E$3,!1)&&(es.m=set_length(es.m,E$3)),ts)&&(es.ent=1e3*new Date().getTime(),(is=new Metric({info:es},GC.NET)).info("ru","").info("m","").info("ti","").info("tp",1e3).info("dt",1e3).info("ct",1e3).info("sslt",1e3).info("rt",1e3).info("rti",1e3).info("dti",1e3).info("ds",0).info("pt",0).info("rh","").info("rhe","").info("ret","").info("rds",0).info("id",uuid()).info("ic",!0).info("art",0).info("ec",200),w$2(is.build(),a$3.NetworkEvent))},setCustomEventStart:function(J,es,ts,is,ns){var rs;arguments.length<1||5<arguments.length||m$4("eventId",J,"setCustomEventStart",null,!1)||isDefined(ns)&&7e3<JSON.stringify(ns).length||includes(O$2,J)||(O$2.push(J),!J)||(P$2[J]=dateNow(),x$2[J]=buildViewID(),rs={},es=set_length(es,p$3),is=set_length(is,7e3),rs.eventId=isDefined(J)?J:"",256<J.length||J.length===0||(rs.eventName=es,rs.eventParam=is,rs.eventLabel=set_length(ts,256),rs.info=R(ns),(es=new Metric({info:rs},GC.CUSTOM_EVENT)).info("eventId","","i").info("t",1).info("d",0).info("ci",x$2[J]),rs.eventName!==void 0&&rs.eventName!==""&&es.info("eventName","","n"),rs.eventLabel!==void 0&&rs.eventLabel!==""&&es.info("eventLabel","","l"),rs.eventParam!==void 0&&rs.eventParam!==""&&es.info("eventParam","","p"),rs.info!==void 0&&rs.info instanceof Object&&es.info("info","","info"),w$2(es.build(),a$3.customEvent)))},setCustomEventEnd:function(J,es,ts,is,ns){if(!(arguments.length<1||5<arguments.length)&&!m$4("eventId",J,"setCustomEventEnd",null,!1)&&!(isDefined(ns)&&7e3<JSON.stringify(ns).length)&&includes(O$2,J)){if(J){let ss=0;isDefined(dateNow)&&P$2&&isDefined(P$2[J])&&(ss=dateNow()-P$2[J],ss*=1e3);var rs={},es=set_length(es,p$3),is=set_length(is,7e3);if(rs.eventId=isDefined(J)?J:"",256<J.length||J.length===0)return;rs.eventName=es,rs.eventParam=is,rs.eventLabel=set_length(ts,256),rs.info=R(ns),es=new Metric({info:rs},GC.CUSTOM_EVENT),es.info("eventId","","i").info("t",2).info("d",ss).info("ci",x$2[J]),rs.eventName!==void 0&&rs.eventName!==""&&es.info("eventName","","n"),rs.eventLabel!==void 0&&rs.eventLabel!==""&&es.info("eventLabel","","l"),rs.eventParam!==void 0&&rs.eventParam!==""&&es.info("eventParam","","p"),rs.info!==void 0&&rs.info instanceof Object&&es.info("info","","info"),w$2(es.build(),a$3.customEvent)}O$2=filter(O$2,function(ss){return ss!==J}),delete P$2[J],delete x$2[J]}},startSpan:function(J,es){return new a$4(J,es)}};var h$6=Object.defineProperty,o=(J,es,ts)=>es in J?h$6(J,es,{enumerable:!0,configurable:!0,writable:!0,value:ts}):J[es]=ts,e$1=(J,es,ts)=>o(J,typeof es!="symbol"?es+"":es,ts);class u{constructor(es){e$1(this,"funActionModify",function(ts,is){return isDefined(ts)&&isDefined(is)&&(this[ts]=is),this}),e$1(this,"funActionBuild",function(){var ts={n:this.n,t:this.t,to:this.to,ice:this.ice,st:this.st,et:this.et||now(),ti:this.ti,im:this.im,ne:this.ne};return isEmpty(ts.ne)&&delete ts.ne,ts}),this.n=es&&typeof es=="string"?es:es.name,this.t=12,this.to=!1,this.ice=!1,this.st=1,this.et=0,this.ti=1225,this.im=!0,this.ne={}}}function Q(J){var es;return isDefined(J)?(J.$xpath||(200<(es=xpath(J)).length?J.$xpath="[Error: Overflow with more than 200 characters.]":J.$xpath=es),J.$xpath):""}function A$3(J,es=!1){var ts;isDefined(J)&&(isDefined(J.$timeoutTimer)&&clearTimeout(J.$timeoutTimer),d$8(GC.LIFESTYLE_REQUEST_END+J.identify),d$8(GC.CUSTOM_ACTION_END),d$8(GC.LIFESTYLE_CALLBACK_REQUEST_START+J.identify),d$8(GC.ROUTE_CHANGE_END+J.identify),d$8(GC.WEBSOCKET_CONNECTION_END),d$8(GC.SPAN_CALLBACK_DATA),(ts=new u("anonymous")).funActionModify("st",J.startTime),ts.funActionModify("et",J.endTime),es&&(ts.funActionModify("ice",!0),J.startEvent.lt=J.endTime-J.startTime,J.startEvent.ice=!0,isDefined(J.hasEmitStart))&&!J.hasEmitStart&&(c$3({t:GC.TRACE_ACTION_DATA,p:{info:J.startEvent},type:1}),isEmpty(f$2.secondSett)||c$3({t:GC.TRACE_ACTION_DATA,p:{info:J.startEvent},type:2}),J.hasEmitStart=!0),(es=extend({},J.startEvent)).m=2,es.me=ts.funActionBuild(),c$3({t:GC.TRACE_ACTION_DATA,p:{info:es},revts:J.handledRequestStruct,type:1}),isEmpty(f$2.secondSett)||c$3({t:GC.TRACE_ACTION_DATA,p:{info:es},revts:J.handledRequestStructSecond,type:2}))}function $$1(J){try{d$8(GC.CUSTOM_ACTION_END);let as=now();J.startTime=as,J.requestRecord=[],J.handledRequestStruct=[],J.handledRequestStructSecond=[],J.requestCount=0,J.routeCount=0,J.wsCount=0,J.identify=uuid(),J.handledRouteRequest=[],J.handledRouteRequestSecond=[],J.handledSpan=[],v$6(GC.ROUTE_CHANGE_START,function(os){J.routeCount=J.routeCount+1,J.relatedRoute=!0,os.info.id=J.identify}),v$6(GC.ROUTE_CHANGE_END+J.identify,function(os){var ds;J.routeCount=J.routeCount-1,isDefined(os.routeData)&&J.relatedRoute&&0<J.handledRouteRequest.length&&J.routeCount===0?((ds=JSON.parse(JSON.stringify(os.routeData))).revts=J.handledRouteRequest,J.handledRequestStruct.push(ds)):J.handledRequestStruct.push(os.routeData),!isEmpty(f$2.secondSett)&&checkMoudle("routechange")&&(isDefined(os.routeData)&&J.relatedRoute&&0<J.handledRouteRequestSecond.length&&J.routeCount===0?((ds=JSON.parse(JSON.stringify(os.routeData))).revts=J.handledRouteRequestSecond,J.handledRequestStructSecond.push(ds)):J.handledRequestStructSecond.push(os.routeData)),J.hasEmitStart&&J.requestCount===0&&J.wsCount===0&&(d$8(GC.LIFESTYLE_REQUEST_END+J.identify),d$8(GC.LIFESTYLE_CALLBACK_REQUEST_START+J.identify),d$8(GC.ROUTE_CHANGE_END+J.identify),d$8(GC.WEBSOCKET_CONNECTION_END),A$3(J))}),v$6(GC.LIFESTYLE_FETCH_START,function(os){isDefined(os.info)&&(os.info.ai=J.identify),J.requestRecord.push(now()),J.requestCount=J.requestCount+1}),v$6(GC.LIFESTYLE_XHR_START,function(os){isDefined(os.info)&&(os.info.ai=J.identify),J.requestRecord.push(now()),J.requestCount=J.requestCount+1}),v$6(GC.LIFESTYLE_CALLBACK_REQUEST_START+J.identify,function(os){J.requestCount=J.requestCount+1}),v$6(GC.LIFESTYLE_REQUEST_END+J.identify,function(os){var ds;J.requestCount=J.requestCount-1,isDefined(os)&&isDefined(os.requestData)&&isDefined(os.endTime)&&!J.relatedRoute&&(ds=Q$1(os.requestData,f$2.sett,!0),J.handledRequestStruct.push({k:ds.type,ent:ds.ent,sin:ds.sin,v:ds.data}),!isEmpty(f$2.secondSett))&&checkMoudle("network")&&(ds=Q$1(os.requestData,f$2.secondSett,!0),J.handledRequestStructSecond.push({k:ds.type,ent:ds.ent,sin:ds.sin,v:ds.data})),J.relatedRoute&&(ds=Q$1(os.requestData,f$2.sett,!0),J.handledRouteRequest.push({k:ds.type,ent:ds.ent,sin:ds.sin,v:ds.data}),!isEmpty(f$2.secondSett))&&checkMoudle("network")&&(ds=Q$1(os.requestData,f$2.secondSett,!0),J.handledRouteRequestSecond.push({k:ds.type,ent:ds.ent,sin:ds.sin,v:ds.data})),J.requestCount===0&&J.routeCount===0&&J.wsCount===0&&(d$8(GC.LIFESTYLE_REQUEST_END+J.identify),d$8(GC.LIFESTYLE_CALLBACK_REQUEST_START+J.identify),d$8(GC.ROUTE_CHANGE_END+J.identify),d$8(GC.WEBSOCKET_CONNECTION_END),isDefined(J.loadTime)&&A$3(J),e$2.turnOffRecord(),e$2.stopPageActivityObserver())}),v$6(GC.CUSTOM_ACTION_END,function(os){J.endTime=now(),A$3(J,!0)}),v$6(GC.SPAN_CALLBACK_DATA,function(os){var ds,ls,fs,us;isDefined(os.websocketData)&&({type:ds,ent:ls,sin:fs,data:us}=os.websocketData,os.websocketData.isRelated=!0,J.handledRequestStruct.push({k:ds,ent:ls,sin:fs,v:us}),checkMoudle("span")&&J.handledRequestStructSecond.push({k:ds,ent:ls,sin:fs,v:us}),J.handledSpan.push(os.websocketData))}),v$6(GC.WEBSOCKET_CONNECTION_START,function(){J.wsCount=J.wsCount+1}),v$6(GC.WEBSOCKET_CONNECTION_END,function(){J.wsCount=J.wsCount-1,J.requestCount===0&&J.routeCount===0&&J.wsCount===0&&(d$8(GC.LIFESTYLE_REQUEST_END+J.identify),d$8(GC.LIFESTYLE_CALLBACK_REQUEST_START+J.identify),d$8(GC.ROUTE_CHANGE_END+J.identify),d$8(GC.WEBSOCKET_CONNECTION_END),A$3(J))}),f$2.sett.pageUrl=d$1({p:{info:{url:isDefined(window.location)?window.location.href:""}}},f$2.sett).p.info.url,isDefined(e$2)&&(e$2.startPageActivityObserver(),e$2.turnOnRecord());var es,ts,is,ns,rs,ss=J.target;(isString(ss.tagName)||ss.name||"")!=="BODY"&&(es="xpath"+unicode("=")+Q(ss)+",outerHTML"+unicode("=")+ss.outerHTML,ts=J&&J.type==="click"?1:4,is=J&&J.target&&J.target.tagName||"",ns={t:getActionText(ss)||ss.innerText||"",c:String(ss.className||""),tag:ss.nodeName||ss.localName||"",id:isDefined(ss.id)?ss.id:""},rs=f$2.sett.pageUrl,J.startEvent={t:ts,n:is,sa:1,i:es,vn:rs,ic:!1,lt:0,is:!1,id:J.identify,m:1,ice:!1,ci:ns,timestamp:as,vci:f$2.sett.pageViewId},J.$startTimer=setTimeout(function(){isDefined(e$2)&&isDefined(checkRecord)&&(checkRecord(e$2.mutationRecord,as)||checkRecord(e$2.performanceRecord,as)||checkRecord(J.requestRecord,as)?(c$3({t:GC.TRACE_ACTION_DATA,p:{info:J.startEvent},type:1}),isEmpty(f$2.secondSett)||c$3({t:GC.TRACE_ACTION_DATA,p:{info:J.startEvent},type:2}),d$8(GC.LIFESTYLE_REQUEST_END+J.identify),d$8(GC.LIFESTYLE_FETCH_START),d$8(GC.LIFESTYLE_XHR_START),d$8(GC.CUSTOM_ACTION_END),d$8(GC.LIFESTYLE_CALLBACK_REQUEST_START+J.identify),d$8(GC.ROUTE_CHANGE_START),d$8(GC.ROUTE_CHANGE_END+J.identify),d$8(GC.SPAN_CALLBACK_DATA),d$8(GC.WEBSOCKET_CONNECTION_START),d$8(GC.WEBSOCKET_CONNECTION_END),e$2.turnOffRecord(),e$2.stopPageActivityObserver(),J.hasEmitStart=!0):(d$8(GC.LIFESTYLE_REQUEST_END+J.identify),d$8(GC.LIFESTYLE_FETCH_START),d$8(GC.LIFESTYLE_XHR_START),d$8(GC.CUSTOM_ACTION_END),d$8(GC.LIFESTYLE_CALLBACK_REQUEST_START+J.identify),d$8(GC.ROUTE_CHANGE_START),d$8(GC.ROUTE_CHANGE_END+J.identify),d$8(GC.SPAN_CALLBACK_DATA),d$8(GC.WEBSOCKET_CONNECTION_START),d$8(GC.WEBSOCKET_CONNECTION_END),e$2.turnOffRecord(),e$2.stopPageActivityObserver()))},1e3))}catch(as){log("C151")}}function M$2(J){try{var es=now(),ts=(isDefined(J.$startTimer)&&clearTimeout(J.$startTimer),d$8(GC.LIFESTYLE_FETCH_START),d$8(GC.LIFESTYLE_XHR_START),d$8(GC.ROUTE_CHANGE_START),d$8(GC.WEBSOCKET_CONNECTION_START),es-J.startTime);if(J.loadTime=ts,J.endTime=es,isDefined(e$2))if(!J.hasEmitStart&&(0<J.requestRecord.length||checkRecord(e$2.mutationRecord,J.startTime)||checkRecord(e$2.performanceRecord,J.startTime)))J.startEvent.lt=ts,c$3({t:GC.TRACE_ACTION_DATA,p:{info:J.startEvent},type:1}),isEmpty(f$2.secondSett)||c$3({t:GC.TRACE_ACTION_DATA,p:{info:J.startEvent},type:2}),J.hasEmitStart=!0,J.$timeoutTimer=setTimeout(function(){A$3(J)},12e4),J.requestCount===0&&J.routeCount===0&&J.wsCount===0&&A$3(J),e$2.turnOffRecord(),e$2.stopPageActivityObserver();else{if(0<J.handledSpan.length)for(var is of J.handledSpan)w$2(is,a$3.spanEvent);d$8(GC.LIFESTYLE_REQUEST_END+J.identify),d$8(GC.CUSTOM_ACTION_END),d$8(GC.LIFESTYLE_CALLBACK_REQUEST_START+J.identify),d$8(GC.ROUTE_CHANGE_END+J.identify),d$8(GC.WEBSOCKET_CONNECTION_END),d$8(GC.SPAN_CALLBACK_DATA)}}catch(ns){log("C152")}}function G(){c$3({t:GC.CUSTOM_ACTION_END})}function b$2(){on$2(window,"click",$$1,!0),on$2(window,"click",J=>setTimeout(()=>M$2(J),50),!1)}function e(J,es){this.lastTimer=null,this.lastEvent=null,this.period=es||1e3,this.compare=function(){return!1},this.submit=J||NIL_FN}function P$1(J,es,ts){if(!isReadable(J.file)&&es)try{for(var is,ns=/[@(\s]+?([\S]+):(\d+):(\d+)\)?/,rs=/[@(\s]+?([\S]+):(\d+)\)?/,ss=/([^@(\s)]+):(\d+):(\d+)/,as=/([^@(\s)]+):(\d+)/,os=es.split(`
`),ds=!0,ls=function(hs){return hs=Number(hs),isNaN(hs)?0:hs},fs=0;fs<os.length;fs++)if(is=os[fs],ns.test(is)||rs.test(is)){if(!ds||!ts){var us=last(is.split(/\s+/)),cs=ss.exec(us)||as.exec(us);J.file=cs[1].replace(/^\(/,""),J.line=ls(cs[2]),J.column=ls(cs[3]);break}ds=!1,os[fs]=""}ts&&(J.stack=os.join(`
`).replace(/\n+/g,`
`).replace(/^\n/,""))}catch(hs){log("C146")}}function v$3(J,es,ts){var is;isDefined(es)&&(is=es.name||es.constructor&&es.constructor.name,J.name=is&&toString(is)||"",isReadable(J.message)||(J.message=(isReadable(is)?is+": ":"")+es.message),isReadable(J.file)||(J.file=es.fileName),isDefined(J.line)||(J.line=es.lineNumber,J.column=es.columnNumber),J.stack=es.stack,P$1(J,J.stack,ts))}extend(e.prototype,{$schedule:function(J){isDefined(this.lastTimer)&&clearTimeout(this.lastTimer);var es=this;this.lastEvent=J,this.lastTimer=delay(function(){es.submit(es.lastEvent),es.lastEvent=null},this.period)},event:function(J){isDefined(this.lastEvent)&&(this.compare(this.lastEvent,J)?(J.info[GC.DURATION]=J.info[GC.START_TIME]-this.lastEvent.info[GC.START_TIME],J.info[GC.TIMESTAMP]=this.lastEvent.info[GC.TIMESTAMP],J.info[GC.START_TIME]=this.lastEvent.info[GC.START_TIME],J.info.count=this.lastEvent.info.count+1):this.submit(this.lastEvent)),this.$schedule(J)},flush:function(){isDefined(this.lastTimer)&&(clearTimeout(this.lastTimer),this.lastTimer=null),isDefined(this.lastEvent)&&(this.submit(this.lastEvent),this.lastEvent=null)},equalsWith:function(J){"apply"in J&&(this.compare=J)}});var E$2=new e(function(J){c$3({t:GC.ERROR_DATA,p:J})},f$2.sett.debounce);function y$1(J,es){J={info:extend(J,{message:withLength(J.message,200),duration:0,type:isDefined(J.type)?J.type:0,count:1,file:withLength(J.file,200)||"<anonymous>",stack:withLength(J.stack,5e3)})},isDefined(es)&&(J.ext=es),E$2.event(J)}function T$2(){window.onerror=hack(window.onerror,function(J,es,ts,is,ns){J=extend({message:J,line:ts,column:is,file:es,type:3},getTime()),v$3(J,ns),y$1(J)})}function U$4(){T$2(),v$6(GC.PAGE_READY,function(){T$2()}),addListener(window,"error",function(J){var es=J.target||J.srcElement;isDefined(es)&&(es instanceof HTMLScriptElement||es instanceof HTMLLinkElement||es instanceof HTMLImageElement)&&c$3({t:GC.ERROR_DATA,p:{info:extend({message:xpath(es),duration:0,type:1,count:1,file:withLength(es.src||J.filename,200),line:0,column:0,stack:""},getTime())}})},!0),addListener(window,"unhandledrejection",function(J){var es;isDefined(J)&&(isString(J.reason)?(es=extend({message:J.reason,line:0,column:0},getTime()),isDefined(J.type)&&(es.type=2),y$1(es)):h$5(J.reason))},!0)}function h$5(J){if(!isDefined(J))return log("C122");var es,ts=getTime();typeof J=="string"?(ts.message=J,v$3(ts,J=new Error(J),!0)):window.ErrorEvent&&J instanceof ErrorEvent?(extend(ts,{message:J.message,line:J.lineno,column:J.colno,file:J.filename}),v$3(ts,J.error),isReadable(ts.file)||J.target&&(ts.file=J.target.baseURI)):(v$3(ts,J),typeof J.type=="number"?ts.type=J.type:ts.type=3,es=J.ext),y$1(ts,es)}function g$4(){console&&console.error&&(console.error=h$4(console.error,null,d))}function h$4(J,es,ts){var is;return J.$original?J:((is=function(){var ns=args(arguments);try{return isDefined(es)&&es.apply(this,ns),ns[0]instanceof Error&&h$5(ns[0]),J.apply(this,ns)}catch(rs){throw rs}finally{ts.apply(this,ns)}}).$original=J,is)}function d(){var J;console.$bonree||(J={lv:"error",msg:E$1(args(arguments))},c$3({t:GC.CONSOLE_DATA,p:{info:J}}))}function E$1(J){return J&&isArray(J)?map(J,function(es){return S$1(es=es===void 0?String(es):es)}).join(" "):J}function S$1(J){let es;if(isString(J))return J;if(J instanceof Error||J instanceof Function)return String(J);try{es=stringify(J)}catch(ts){es=String(J)}return es}E$2.equalsWith(function(J,es){function ts(is){return is.message+is.line+is.column}return ts(J.info)===ts(es.info)}),v$6(GC.FLUSH_DATA,function(){E$2.flush()});var C$3=!1,h$3=[],l$1=[];let A$2,c,M$1;var m$3=[],I$2=[],K$1=!1,s$3=0;function W(){if(!isDefined(window.PerformanceObserver))return log("C113");var J=new PerformanceObserver(function(es){forEach(es.getEntries(),function(ts){ts.name==="first-paint "&&(A$2=ts[GC.START_TIME]&&ts[GC.START_TIME]||""),ts.name==="first-contentful-paint"&&(c=ts[GC.START_TIME]&&ts[GC.START_TIME]||""),ts.entryType==="largest-contentful-paint"&&(M$1=ts.renderTime||ts.loadTime||"")})});try{J.observe({entryTypes:["paint","largest-contentful-paint"]})}catch(es){log("C114")}}function j$1(J){var es,ts;return u$1.state?(isDefined(A$2)&&isDefined(c)||!window.performance.getEntriesByType||forEach(window.performance.getEntriesByType("paint"),function(is){is.name==="first-paint"&&(A$2=is[GC.START_TIME]&&is[GC.START_TIME]||0),is.name==="first-contentful-paint"&&(c=is[GC.START_TIME]&&is[GC.START_TIME]||0)}),isDefined(A$2)||(es=(ts=window.performance.timing)[GC.NAVIGATION_START],ts.msFirstPaint&&(log("C111"),A$2=ts.msFirstPaint-es),window.chrome&&window.chrome.loadTimes&&(ts=window.chrome.loadTimes())&&ts.firstPaintTime&&(log("C112"),A$2=ts.firstPaintTime-es)),{fp:A$2?Math.round(A$2):0,fcp:c?Math.round(c):0,lcp:M$1?Math.round(M$1):0}):log("C110")}function a$1(J,es){return 0<J?J-es:0}function z(){var J=window.performance||window.msPerformance||window.webkitPerformance,es=J.getEntriesByType("resource")||[];return es.length===0?isDefined(J.timing)?0<J.timing[GC.LOAD_EVENT_END]-J.timing[GC.NAVIGATION_START]?J.timing[GC.LOAD_EVENT_END]-J.timing[GC.NAVIGATION_START]:0:10:(J=map(es,function(ts){return ts.responseEnd}).sort((ts,is)=>is-ts),Math.round(J[0]))}function x$1(){f$2.setData("pageUrl",d$1({p:{info:{url:f$2.sett.pageUrl}}},f$2.sett).p.info.url),c$3({t:GC.PAGE_LOAD}),ne(),delay(ee,1e3)}function ee(J){C$3||(C$3=!0,c$3({t:GC.RESOURCE_DATA,p:Ee$1(),type:1}),c$3({t:GC.RESOURCE_DATA,p:te(),type:2,special:K$1}),s$3===0&&(d$8(GC.LIFESTYLE_XHR_START),d$8(GC.LIFESTYLE_FETCH_START),d$8(GC.LIFESTYLE_REQUEST_END+"H5"),c$3({t:GC.RESOURCE_DATA,p:{},type:3,revts:m$3}),isEmpty(f$2.secondSett)||c$3({t:GC.RESOURCE_DATA,p:{},type:4,revts:I$2}))),c$3({t:GC.FLUSH_DATA,p:!!J})}function te(){var J;return window.performance&&window.performance.getEntriesByType?(J=l$1.concat(window.performance.getEntriesByType("resource")),l$1=[],map(J,function(es){var ts,is,ns=es[GC.INITIATOR_TYPE],rs=es.entryType,ss=getMineTypeByUrl(es.name);if(isDefined(es.name)&&es.name.indexOf(f$2.sett.uploadAddrHttps)===-1||isDefined(es.name)&&es.name.indexOf(f$2.sett.uploadAddrHttp)===-1)return is={},(ts={}).name=d$1({p:{info:{url:es.name}}},f$2.sett).p.info.url,ts[GC.START_TIME]=getFixedMetric(es,GC.START_TIME),ts[GC.INITIATOR_TYPE]=ns,ts.entryType=rs,ts.ret=ss,is[GC.WORKER_START]=getFixedMetric(es,GC.WORKER_START),is[GC.REDIRECT_START]=getFixedMetric(es,GC.REDIRECT_START),is[GC.REDIRECT_END]=getFixedMetric(es,GC.REDIRECT_END),is[GC.FETCH_START]=getFixedMetric(es,GC.FETCH_START),is[GC.DOMAIN_LOOKUP_START]=getFixedMetric(es,GC.DOMAIN_LOOKUP_START),is[GC.DOMAIN_LOOKUP_END]=getFixedMetric(es,GC.DOMAIN_LOOKUP_END),is[GC.CONNECT_START]=getFixedMetric(es,GC.CONNECT_START),is[GC.CONNECT_END]=getFixedMetric(es,GC.CONNECT_END),is[GC.SECURE_CONNECTION_START]=getFixedMetric(es,GC.SECURE_CONNECTION_START),is[GC.REQUEST_START]=getFixedMetric(es,GC.REQUEST_START),is[GC.RESPONSE_START]=getFixedMetric(es,GC.RESPONSE_START),is[GC.RESPONSE_END]=getFixedMetric(es,GC.RESPONSE_END),is[GC.RESPONSE_STATUS]=es[GC.RESPONSE_STATUS],ts[GC.DURATION]=isDefined(es[GC.DURATION])?getFixedMetric(es,GC.DURATION):is[GC.RESPONSE_END]-is[GC.START_TIME],isDefined(es[GC.DECODED_BODY_SIZE])&&(ts[GC.NEXT_HOP_PROTOCOL]=es[GC.NEXT_HOP_PROTOCOL],is[GC.TRANSFER_SIZE]=es[GC.TRANSFER_SIZE],is[GC.ENCODED_BODY_SIZE]=es[GC.ENCODED_BODY_SIZE]>=Math.pow(2,64)?Math.pow(2,60):es[GC.ENCODED_BODY_SIZE],is[GC.DECODED_BODY_SIZE]=es[GC.DECODED_BODY_SIZE]),{info:ts,metric:is}})):(log("C116"),[])}function Te(J,es){var ts=performance.timing.domContentLoadedEventEnd-performance.timing.domContentLoadedEventStart;{var is=J,ns=es,rs=performance.getEntriesByType("resource"),ss=ts;let os,ds=is;for(;is+5e3<=5e4;){ds=is;var as=ns.filter(ls=>ls.startTime<is+5e3&&is<ls.startTime+ls.duration);if(as.length){let ls=as[as.length-1];is=ls.startTime+ls.duration}else{if(!(2<(os=rs.filter(ls=>!(ls.startTime>=is+5e3||ls.startTime+ls.duration<=is))).length))return Math.max(ds,ss);{let ls=os[0];is=ls.startTime+ls.duration}}}return Math.max(ds,ss)}}function Ee$1(){var J,es,ts=window.screen||{},ts={timestamp:now(0),title:u$1.title,referrer:document.referrer,charset:document.characterSet||document.charset||"",embed:window.top&&window.self&&window.top!==window.self?1:0,width:ts.width||0,height:ts.height||0,completed:u$1.state,param:location&&location.search||""},is=window.performance&&window.performance.timing;return ts.supported=isDefined(is)?1:0,is?(is={},J=(es=window.performance.timing)[GC.NAVIGATION_START]||es[GC.FETCH_START],is[GC.NAVIGATION_START]=J||"",is[GC.UNLOAD_EVENT_START]=a$1(es[GC.UNLOAD_EVENT_START],J),is[GC.UNLOAD_EVENT_END]=a$1(es[GC.UNLOAD_EVENT_END],J),is[GC.REDIRECT_START]=a$1(es[GC.REDIRECT_START],J),is[GC.REDIRECT_END]=a$1(es[GC.REDIRECT_END],J),is[GC.FETCH_START]=a$1(es[GC.FETCH_START],J),is[GC.DOMAIN_LOOKUP_START]=a$1(es[GC.DOMAIN_LOOKUP_START],J),is[GC.DOMAIN_LOOKUP_END]=a$1(es[GC.DOMAIN_LOOKUP_END],J),is[GC.CONNECT_START]=a$1(es[GC.CONNECT_START],J),is[GC.CONNECT_END]=a$1(es[GC.CONNECT_END],J),is[GC.SECURE_CONNECTION_START]=a$1(es[GC.SECURE_CONNECTION_START],J),is[GC.REQUEST_START]=a$1(es[GC.REQUEST_START],J),is[GC.RESPONSE_START]=a$1(es[GC.RESPONSE_START],J),is[GC.RESPONSE_END]=a$1(es[GC.RESPONSE_END],J),is[GC.DOM_LOADING]=a$1(es[GC.DOM_LOADING],J),is[GC.DOM_INTERACTIVE]=a$1(es[GC.DOM_INTERACTIVE],J),is[GC.DOM_CONTENT_LOADED_EVENT_START]=a$1(es[GC.DOM_CONTENT_LOADED_EVENT_START],J),is[GC.DOM_CONTENT_LOADED_EVENT_END]=a$1(es[GC.DOM_CONTENT_LOADED_EVENT_END],J),is[GC.DOM_COMPLETE]=a$1(es[GC.DOM_COMPLETE],J),is[GC.LOAD_EVENT_START]=a$1(es[GC.LOAD_EVENT_START],J),is[GC.LOAD_EVENT_END]=a$1(es[GC.LOAD_EVENT_END],J),is[GC.FULL_RESOURCE_LOAD_TIME]=z(),ts[GC.DURATION]=u$1.state?Math.max(0,is[GC.LOAD_EVENT_END],is[GC.LOAD_EVENT_START]):Math.round(tillNow()/1e3),window.performance.getEntriesByType&&isDefined(es=last(window.performance.getEntriesByType("navigation")))&&(ts.type=es.type,ts[GC.NEXT_HOP_PROTOCOL]=es[GC.NEXT_HOP_PROTOCOL],is[GC.REDIRECT_COUNT]=es[GC.REDIRECT_COUNT],is[GC.TRANSFER_SIZE]=es[GC.TRANSFER_SIZE],is[GC.ENCODED_BODY_SIZE]=es[GC.ENCODED_BODY_SIZE],is[GC.DECODED_BODY_SIZE]=es[GC.DECODED_BODY_SIZE],is[GC.RESPONSE_STATUS]=es[GC.RESPONSE_STATUS]),extend(is,J=j$1()),J&&J.fcp&&(es=Te(J.fcp,h$3)||0,extend(is,{tti:Math.round(es)})),{info:ts,metric:is}):(log("C109"),ts[GC.DURATION]=tillNow(),{info:ts})}function ne(){try{new PerformanceObserver(function(J){h$3=J.getEntries()}).observe({entryTypes:["longtask"]})}catch(J){log("C115")}}function re(){var J;window.performance&&window.performance.getEntriesByType&&(addListener(window.performance,"resourcetimingbufferfull",(J=function(es){return function(){log("Dump when resource buffer is full");var ts=window.performance.getEntriesByType("resource"),ts=(c$3({t:GC.RESOURCE_DUMP,p:ts}),C$3||(l$1=l$1.concat(ts)),es+"learResourceTimings");ts in window.performance&&window.performance[ts]()}})("c"),!1),addListener(window.performance,"webkitresourcetimingbufferfull",J("webkitC"),!1))}function ie(){v$6(GC.LIFESTYLE_XHR_START,function(J){isDefined(J.info)&&(J.info.isRelatedH5=!0),s$3+=1}),v$6(GC.LIFESTYLE_FETCH_START,function(J){isDefined(J.info)&&(J.info.isRelatedH5=!0),s$3+=1}),v$6(GC.LIFESTYLE_REQUEST_END+"H5",function(J){--s$3;var es=Q$1(JSON.parse(JSON.stringify(J.requestData)),f$2.sett,!0);m$3.push({k:es.type,ent:es.ent,sin:es.sin,v:es.data}),!isEmpty(f$2.secondSett)&&checkMoudle("network")&&(es=Q$1(JSON.parse(JSON.stringify(J.requestData)),f$2.secondSett,!0),I$2.push({k:es.type,ent:es.ent,sin:es.sin,v:es.data})),s$3===0&&(d$8(GC.LIFESTYLE_XHR_START),d$8(GC.LIFESTYLE_FETCH_START),d$8(GC.LIFESTYLE_REQUEST_END+"H5"),c$3({t:GC.RESOURCE_DATA,p:{},type:3,revts:m$3}),isEmpty(f$2.secondSett)||c$3({t:GC.RESOURCE_DATA,p:{},type:4,revts:I$2}))}),addListener(document,"DOMContentLoaded",function(){c$3({t:GC.PAGE_READY})}),document.readyState!=="complete"||C$3?on$2(window,"load",x$1):startWith(document.location.href,"about:")&&startWith(document.URL,"about:")||(x$1.call(),K$1=!0),on$2(window,"pagehide",function(){g$6(!0)},!1),W(),re()}var O$1=[],A$1,D$1=[],I$1=[GC.XML_HTTP_REQUEST,GC.FETCH],b$1=function(J,es){return J[GC.START_TIME]-es[GC.START_TIME]};function getCookieUserID(){var J=g$9.cookieArr,es=g$9.configeManage;if(0<J.length)for(let is=0,ns=J.length-1;is<=ns;is++){var ts=getCookie(J[is].rule);if(isDefined(ts)&&u$2(ts)&&J[is].index<=g$9.isStopGetValue)return es[J[is].index].value=""+ts,g$9.checkValueRight()}}function j(rs){var es,ts=rs.url,is=rs[GC.TIMESTAMP],ns=I$1[rs.type],rs=map(D$1,function(as){if(as.url===ts&&I$1[as.type]===ns)return as}),ss=(rs.sort(b$1),-1);if(forEach(rs,function(as,os){if(as[GC.TIMESTAMP]===is)return ss=os,!1}),!(ss<0))return rs=map(O$1,function(as,os){if(v$2(as,ts,ns))return as}),ss>=rs.length?(log("C131"),es=[],window.performance.getEntriesByType&&(es=map(window.performance.getEntriesByType("resource"),function(as){if(v$2(as,ts,ns))return as})),rs=rs.concat(es)):log("C132"),rs.sort(b$1),rs.length<1||ss>=rs.length?void log("C133"):rs[ss];log("C130")}function m$2(J){return(J.initiatorType===GC.XML_HTTP_REQUEST||J.initiatorType===GC.FETCH)&&J[GC.START_TIME]>A$1}function v$2(J,es,ts){return m$2(J)&&endWith(J.name,es)&&J.initiatorType===ts}function L$2(J){return 0<J&&J<600}var extendMetrics=function(J){var es,ts,is;return window.performance&&window.performance.now?L$2((es=J.info).status||es.code)?isDefined(ts=j(es))?(is=J.metric,es[GC.START_TIME]=1e3*getFixedMetric(ts,GC.START_TIME),es[GC.TIMESTAMP]=1e3*(es[GC.START_TIME]/1e3+window.performance.timing.navigationStart),es[GC.DURATION]=1e3*getFixedMetric(ts,GC.DURATION),is[GC.REDIRECT_START]=1e3*getFixedMetric(ts,GC.REDIRECT_START),is[GC.REDIRECT_END]=1e3*getFixedMetric(ts,GC.REDIRECT_END),is[GC.FETCH_START]=1e3*getFixedMetric(ts,GC.FETCH_START),is[GC.DOMAIN_LOOKUP_START]=1e3*getFixedMetric(ts,GC.DOMAIN_LOOKUP_START),is[GC.DOMAIN_LOOKUP_END]=1e3*getFixedMetric(ts,GC.DOMAIN_LOOKUP_END),is[GC.CONNECT_START]=1e3*getFixedMetric(ts,GC.CONNECT_START),is[GC.CONNECT_END]=1e3*getFixedMetric(ts,GC.CONNECT_END),is[GC.SECURE_CONNECTION_START]=1e3*getFixedMetric(ts,GC.SECURE_CONNECTION_START),is[GC.RESPONSE_START]=1e3*getFixedMetric(ts,GC.RESPONSE_START),is[GC.REQUEST_START]=1e3*getFixedMetric(ts,GC.REQUEST_START),is[GC.RESPONSE_END]=1e3*getFixedMetric(ts,GC.RESPONSE_END),isDefined(ts[GC.DECODED_BODY_SIZE])&&(es[GC.NEXT_HOP_PROTOCOL]=ts[GC.NEXT_HOP_PROTOCOL],is[GC.WORKER_START]=1e3*getFixedMetric(ts,GC.WORKER_START),is[GC.TRANSFER_SIZE]=getFixedMetric(ts,GC.TRANSFER_SIZE),is[GC.ENCODED_BODY_SIZE]=getFixedMetric(ts,GC.ENCODED_BODY_SIZE),is[GC.DECODED_BODY_SIZE]=getFixedMetric(ts,GC.DECODED_BODY_SIZE))):log("C137"):log("C135"):log("C134"),J},polyfillMetric=function(J){var es=J.info,ts=J.metric;return isDefined(es[GC.DURATION])||(es[GC.DURATION]=ts[GC.RESPONSE_END]-es[GC.START_TIME]),isDefined(es.callbackError)||(es.callbackError=0),J},queueRequest=function(J){var es=J.info;L$2(es.status||es.code)&&D$1.push(J.info)};function H$1(){A$1=tillNow()/1e3||0,v$6(GC.RESOURCE_DUMP,function(J){J=map(J.p,function(es){if(m$2(es))return es}),O$1=O$1.concat(J)})}class F{constructor(){this.list={},this.isOpen=!1,this.blackKey=[]}checkIsOpen(){isDefined(f$2.sett.brss)&&!f$2.sett.brss||isDefined(f$2.secondSett.brss)&&!f$2.secondSett.brss?this.isOpen=!1:this.isOpen=!0}getItem(es,ts,is){var ns=es+"+"+ts;return isDefined(es)&&this.list[ns]?this.list[ns]:this.setItem(es,ts,is)}setItem(es,ts,is){let{urlTotalList:ns,urlWhiteList:rs,urlBlackList:ss}=isDefined(f$2.sett.traceConfig)?f$2.sett.traceConfig:{urlTotalList:[],urlBlackList:[],urlWhiteList:[]},as=(isDefined(f$2.secondSett.traceConfig)&&(isDefined(f$2.secondSett.traceConfig.urlTotalList)&&(ns=isDefined(ns)?ns.concat(f$2.secondSett.traceConfig.urlTotalList):f$2.secondSett.traceConfig.urlTotalList),isDefined(f$2.secondSett.traceConfig.urlWhiteList)&&(rs=isDefined(rs)?rs.concat(f$2.secondSett.traceConfig.urlWhiteList):f$2.secondSett.traceConfig.urlWhiteList),isDefined(f$2.secondSett.traceConfig.urlBlackList))&&(ss=isDefined(ss)?ss.concat(f$2.secondSett.traceConfig.urlBlackList):f$2.secondSett.traceConfig.urlBlackList),{});if(isDefined(ns)&&0<ns.length)for(let os=0,ds=(ns=200<ns.length?ns.slice(0,200):ns).length-1;os<=ds;os++)extend(as,this.totalHead(ns[os],es,ts,is));if(isDefined(rs)&&0<rs.length)for(let os=0,ds=(rs=200<rs.length?rs.slice(0,200):rs).length-1;os<=ds;os++)this.whiteCheck(es,rs[os])&&extend(as,this.whiteHead(rs[os],es,ts,is));if(isDefined(ss)&&0<ss.length)for(let os=0,ds=(ss=200<ss.length?ss.slice(0,200):ss).length-1;os<=ds;os++)this.black(es,ss[os])?extend(as,this.blackHead(ss[os],es,ts,is)):this.getBlackKey(ss[os]);return 200<Object.keys(as).length&&(as=getElementForNum(as,200)),as=this.delBlackKey(as),this.list[es+"+"+ts]=as}delBlackKey(es){if(0<this.blackKey.length)for(let ts=0,is=this.blackKey.length-1;ts<=is;ts++)isDefined(es[this.blackKey[ts]])&&delete es[this.blackKey[ts]];return this.blackKey=[],es}getBlackKey(es){var ts=es&&es.reqHeaderRules;if(isDefined(ts)&&0<ts.length&&this.blackKey)for(let is=0,ns=ts.length-1;is<=ns;is++)ts[is]&&ts[is].key&&this.blackKey.push(ts[is].key)}totalHead(es,ts,is,ns){var rs={};return extend(rs,this.constructData(es,ts,is,ns)),rs}whiteCheck(es,ts){if(ts.reqHeaderRules&&0<ts.reqHeaderRules.length){if(includes([0,1,2,3,4],ts.type)===!1)return!1;switch(ts.type){case 0:if(es==ts.rule)return!0;break;case 1:if(startWith(es,ts.rule))return!0;break;case 2:if(endWith(es,ts.rule))return!0;break;case 3:var is=new RegExp(ts.rule);if(is instanceof RegExp&&is.test(es))return!0;break;case 4:if(-1<es.indexOf(ts.rule))return!0;break;default:return!1}}return!1}whiteHead(es,ts,is,ns){var rs=es.reqHeaderRules,ss={};if(isDefined(rs)&&0<rs.length)for(let as=0,os=rs.length-1;as<=os;as++)extend(ss,this.constructData(rs[as],ts,is,ns));return ss}black(es,ts){if(ts.reqHeaderRules&&0<ts.reqHeaderRules.length){if(includes([0,1,2,3,4],ts.type)===!1)return!1;switch(ts.type){case 0:if(es==ts.rule)return!1;break;case 1:if(startWith(es,ts.rule))return!1;break;case 2:if(endWith(es,ts.rule))return!1;break;case 3:var is=new RegExp(ts.rule);if(is instanceof RegExp&&is.test(es))return!1;break;case 4:if(-1<es.indexOf(ts.rule))return!1;break;default:return!0}}return!0}blackHead(es,ts,is,ns){var rs=es.reqHeaderRules,ss={};if(isDefined(rs)&&0<rs.length)for(let as=0,os=rs.length-1;as<=os;as++)extend(ss,this.constructData(rs[as],ts,is,ns));return ss}checkKey(es,ts,is){return!(!is||is===1)||!(!isDefined(es)||es.length>ts)&&(ts===256?!!/^[0-9a-zA-Z_-]{1,256}$/.test(es):trim(es)!="")}constructData(es,ts,is,ns){var rs={},ss=getUserAgent(),as=ss&&ss.osName+"/"+ss.osVersion||f$2.sett.osType&&f$2.sett.typeArr[f$2.sett.osType]||f$2.sett.typeArr[1];if(isDefined(es)&&isDefined(es.type)&&this.checkKey(es.key,256)){if(isDefined(es.value)&&this.checkKey(es.value,512)===!1)return;switch(es.type){case 1:isDefined(es.value)&&(rs[es.key]=es.value);break;case 2:rs[es.key]={fun:uuidWithLength,len:16,sky:!1};break;case 3:rs[es.key]={fun:uuidWithLength,len:32,sky:!1};break;case 4:rs[es.key]={fun:skyData,len:32,sky:!0,url:ts,pathname:is};break;case 5:rs[es.key]={fun:traceId,len:32,sky:!1};break;case 6:rs[es.key]="bnro="+as+"_js/"+f$2.sett.agentVersion}}return rs}}let BWexample=new F;function checkBlackAndWhite(J,es){if(isDefined(J.url)&&(J=(J=J.url).indexOf("http")===0?(ts=J.match(/^(https?\:)\/\/(([^:\/?#]*)(?:\:([0-9]+))?)([\/]{0,1}[^?#]*)(\?[^#]*|)(#.*|)$/))&&{href:J,protocol:ts[1],host:ts[2],hostname:ts[3],port:ts[4],pathname:ts[5],search:ts[6],hash:ts[7]}:(ts=J.match(/^(file?\:)\/\/(([^:\/?#]*)(?:\:([0-9]+))?)([\/]{0,1}[^?#]*)(\?[^#]*|)(#.*|)$/))&&{href:J,protocol:ts[1],host:ts[2],hostname:ts[3],port:ts[4],pathname:ts[5],search:ts[6],hash:ts[7]},isDefined(J)))return ts=isDefined(J.hostname)?J.hostname:"",J=isDefined(J.pathname)?J.pathname:"",isDefined(ts)?BWexample.getItem(ts,J,es):void 0;var ts}function skyData(J,es){return`1-${String(enBase64(skyUuid()))}-${String(enBase64(skyUuid()))}-0-${String(enBase64(f$2.sett.appName))}-${String(enBase64(f$2.sett.appVersion))}-${es&&String(enBase64(es))||String(enBase64("/"))}-`+String(enBase64(J))}var p$2={isPeriod:!1,actionID:"",networkID:""};function H(J,es,ts){!J.$metric||isDefined((J=J.$metric.metric)[es])&&!ts||(J[es]=tillNow())}function he$1(J,es,ts){J.$metric&&(J.$metric.info[es]=ts)}function safetySetErrorInfo(J,es,ts,is,ns){J.$metric&&((J=J.$metric.info)[es]=ts,J[GC.MESSAGE]=ns,J[GC.E_TYPE]=is)}function K(J,es){let ts=es;es=getIP(ts=getUrl(ts=typeof es!="string"?es.href||"":ts)),this.$$inner||(this.$metric={info:{id:uuid(),url:ts.slice(0,4096),ip:es[0]||"",port:es[1]||"",method:toUpper(J),type:0,protocalType:-1<ts.indexOf("https")?2:1,reqUrlDta:isDefined(f$2.sett.reqURLKey||f$2.secondSett.reqURLKey)&&getParams(ts)||"",reqhtData:{},reshtData:{}}},isDefined(f$2.sett)&&isDefined(f$2.sett.pageViewId)&&(this.$metric.info.pageViewId=f$2.sett.pageViewId))}function getParams(J){return isDefined(J)?String.prototype.split.call(J,"?")[1]:""}function ue$1(J){if(this.$metric&&!this.$$done){this.$$done=!0;var es,ts=this.$metric,is=ts.info,ns=ts.metric;extendMetrics(ts),polyfillMetric(ts);try{if((!isDefined(ns[GC.DECODED_BODY_SIZE])||ns[GC.DECODED_BODY_SIZE]<=0)&&(es=this.responseType===""||this.responseType==="text"?getContentSize(this.responseText):getContentSize(this.response),ns[GC.DECODED_BODY_SIZE]=es),this.getAllResponseHeaders){var rs=this.getAllResponseHeaders(),ss=rs,as=toLower(ss),os=ss.trim().split(/[\r\n]+/),ds={},ls=(os.forEach(function(cs){var cs=cs.split(": "),us=cs.shift(),cs=cs.join(": ");ds[us]=cs}),is.guid=isDefined(ds[GC.GUID_KEY])&&ds[GC.GUID_KEY].split(",")[0]||"",is.xBrResponse=isDefined(ds[GC.X_BR_RESPONSE])&&ds[GC.X_BR_RESPONSE].split(",")[0]||"",is.traceResponse=isDefined(ds[GC.TRACE_RESPONSE])&&ds[GC.TRACE_RESPONSE].split(",")[0]||"",is[GC.RESPONSE_HEADER]=withLength(ss,2e3),[]);if(isDefined(f$2.sett.respHeaderTraceKey)&&0<f$2.sett.respHeaderTraceKey.length||isDefined(f$2.secondSett.respHeaderTraceKey)&&0<f$2.secondSett.respHeaderTraceKey.length){isDefined(f$2.secondSett.respHeaderTraceKey)&&0<f$2.secondSett.respHeaderTraceKey.length?ls=isDefined(f$2.sett.respHeaderTraceKey)?f$2.sett.respHeaderTraceKey.concat(f$2.secondSett.respHeaderTraceKey):f$2.secondSett.respHeaderTraceKey:isDefined(f$2.sett.respHeaderTraceKey)&&(ls=f$2.sett.respHeaderTraceKey);for(let fs=0,us=ls.length-1;fs<=us;fs++)-1<as.indexOf(toLower(ls[fs]))&&(is.reshtData[ls[fs]]=this.getResponseHeader(ls[fs]))}getResponseHead(rs)}}catch(fs){log("C123")}c$3({t:GC.REQUEST_DATA,p:ts,isRelatedH5:!!isDefined(ts.info.isRelatedH5)}),setTimeout(function(){getCookieUserID()},50)}}function getResponseHead(J){if(0<(J=J.trim()).length){let ns=J.split(/[\r\n]+/),rs={};ns.forEach(function(os){var os=os.split(": "),as=os.shift(),os=os.join(": ");rs[as]=os});var es=g$9.responseArr,ts=g$9.configeManage;if(0<es.length)for(let ss=0,as=es.length-1;ss<=as;ss++){var is=es[ss].rule.toLowerCase();if(rs.hasOwnProperty(is)&&es[ss].index<=g$9.isStopGetValue&&u$2(rs[is]))return ts[es[ss].index].value=""+rs[is],g$9.checkValueRight()}}}function U$3(J){return isFunction(prop(J.onreadystatechange,"$$original"))}function $(J){return J.readyState===0||J.readyState===4}function pe$1(){D.call(this),U$3(this)||($(this)?T$1.call(this):this.onreadystatechange=hack(this.onreadystatechange,D,T$1,q$1))}function D(){isDefined(this.$metric)&&isDefined(this.$metric.info)&&isDefined(this.$metric.info.ai)&&!p$2.isPeriod&&p$2.networkID!==this.$metric.info.id&&(p$2.isPeriod=!0,p$2.actionID=this.$metric.info.ai,p$2.networkID=this.$metric.info.id,c$3({t:GC.XHR_CALLBACK_IDENTIFY,info:p$2})),this.readyState===2&&H(this,GC.RESPONSE_START),$(this)&&(H(this,GC.RESPONSE_START),H(this,GC.RESPONSE_END),H(this,GC.CALLBACK_START))}function T$1(){if($(this)){p$2.isPeriod=!1,c$3({t:GC.XHR_CALLBACK_IDENTIFY,info:p$2});let ts=now();H(this,GC.CALLBACK_END);var J=this;if(this.$metric){var es=0;try{es=this.status,this.$metric.info[GC.E_TYPE]="http"}catch(is){log("C124")}es===0&&(this.$metric.info[GC.E_TYPE]="XHR",this.$metric.info[GC.MESSAGE]="XHR internal services not available or Cross domain request"),this.$metric.info.code=es,queueRequest(this.$metric),delay(function(){log("C125"),ue$1.call(J,ts)})}}}function q$1(){log("C126"),h$5(last(args(arguments))),he$1(this,"callbackError",1)}function L$1(J,es,ts){var is;(isDefined(f$2.sett.reqHeaderTraceKey)||isDefined(f$2.secondSett.reqHeaderTraceKey))&&(is=[],isDefined(f$2.secondSett.reqHeaderTraceKey)&&0<f$2.secondSett.reqHeaderTraceKey.length?is=isDefined(f$2.sett.reqHeaderTraceKey)?f$2.sett.reqHeaderTraceKey.concat(f$2.secondSett.reqHeaderTraceKey):f$2.secondSett.reqHeaderTraceKey:isDefined(f$2.sett.reqHeaderTraceKey)&&(is=f$2.sett.reqHeaderTraceKey),isDefined(is))&&includes(is.map(function(ns){return ns.toLowerCase()}),J.toLowerCase())&&(ts.info.reqhtData[J]=es)}function x(J){if(!this.$$inner&&isDefined(this.$metric)){p$2.isPeriod&&(this.$metric.info.ai=p$2.actionID,this.$metric.info.parentNetworkID=p$2.networkID,c$3({t:GC.LIFESTYLE_CALLBACK_REQUEST_START+this.$metric.info.ai})),c$3({t:GC.LIFESTYLE_XHR_START,info:isDefined(this.$metric.info)?this.$metric.info:null});var es,ts,is=this,ns=(BWexample.checkIsOpen(),isDefined(this.$metric.info)&&BWexample.isOpen&&(this.$metric.info.timestamp=now(),isDefined(ns=checkBlackAndWhite(this.$metric.info,"_xhr")))&&isObj(ns)&&forEachOwn(ns,function(ss,as){var os;isObj(ss)?(os=ss.sky?ss.fun(ss.url,ss.pathname):ss.fun(ss.len),is.setRequestHeader(as,os),L$1(as,os,is.$metric)):(startWith(ss,"bnro=")&&(ss+="_xhr_XMLHttpRequest"),is.setRequestHeader(as,ss),L$1(as,ss,is.$metric))}),isFunction(this.addEventListener)?(forEach(["abort","timeout","error"],function(ss,as){var os=["User terminates XHR request","XHR request timeout","XHR request exception"];addListener(is,ss,function(){safetySetErrorInfo(is,"code",600+Number(as),"XHR",os[as])},!1)}),addListener(this,"readystatechange",pe$1,!1)):(es=3,(ts=function(){delay(function(){if($(is)&&!U$3(is))return T$1.call(is);$(is)||(is.onreadystatechange=hack(is.onreadystatechange,D,T$1,q$1),0<=--es&&ts())})})()),this.onreadystatechange=hack(this.onreadystatechange,D,T$1,q$1),this.$metric.info),rs=(extend(ns,getTime()),this.$metric.metric={});try{ns[GC.REQUEST_BODY]=getRequestParam(J),rs[GC.UPLOAD_BODY_SIZE]=getContentSize(J)}catch(ss){log("C127")}}}function de(J,es){if(u$2(es)){var ts=g$9.requestArr,is=g$9.configeManage;if(0<ts.length){for(let ns=0,rs=ts.length-1;ns<=rs;ns++)if(ts[ns].rule==J&&ts[ns].index<=g$9.isStopGetValue)return is[ts[ns].index].value=""+es,g$9.checkValueRight()}}}function M(J,es){var ts;de(J,es),isDefined(this.$metric)&&(L$1(J,es,this.$metric),isDefined((ts=this.$metric.info)[GC.REQUEST_HEADER])||(ts[GC.REQUEST_HEADER]=""),ts[GC.REQUEST_HEADER]=ts[GC.REQUEST_HEADER]&&ts[GC.REQUEST_HEADER]+`\r
`+J+":"+es||J+":"+es)}function le$1(){if(!window.XMLHttpRequest)return log("C128");var J;XMLHttpRequest.prototype?(XMLHttpRequest.prototype.open=hack(XMLHttpRequest.prototype.open,K),XMLHttpRequest.prototype.send=hack(XMLHttpRequest.prototype.send,x),XMLHttpRequest.prototype.setRequestHeader=hack(XMLHttpRequest.prototype.setRequestHeader,M)):(log("C129"),J=window.XMLHttpRequest,window.XMLHttpRequest=function(){var es=new J;return es.open=hack(es.open,K),es.send=hack(es.send,x),es.setRequestHeader=hack(es.setRequestHeader,M),es})}var A={isPeriod:!1,actionID:"",networkID:""};function le(J){return getRequestParam(J)}function ue(J){return getContentSize(J)}function v$1(J){if(!isDefined(J))return"";var es="";try{var ts,es=(window.Headers&&J instanceof Headers?map(iterate(J.entries()),function(ns){return ns.join(":")}):(ts=[],forEachOwn(J,function(ns,rs){ts.push(rs+":"+ns)}),ts)).join(`\r
`)}catch(is){es="",log("C150")}return es}function _(J){return isDefined(J)&&typeof J!="string"}function q(J,es,ts){return ts==="headers"&&es?_(J)&&J[ts]?extend(es[ts],J[ts]):es[ts]:_(J)?J[ts]:es?es[ts]:null}function pe(J,es){return{requestBody:le(q(J,es,"body")),requestHeader:v$1(q(J,es,"headers"))}}function he(is,ns){var ts=getUrl(ts=_(is)?is.url:is),is=toUpper(q(is,ns,"method"))||"GET",ns=getIP(ts);return{id:uuid(),url:ts.slice(0,4096),method:is,ip:ns[0]||"",port:ns[1]||"",protocalType:-1<ts.indexOf("https")?2:1,reqUrlDta:isDefined(f$2.sett.reqURLKey||f$2.secondSett.reqURLKey)&&getParams(ts)||"",requestType:4}}function I(J,es){var ts,is;J&&(extendMetrics(J),polyfillMetric(J),(!isDefined((ts=J.metric)[GC.DECODED_BODY_SIZE])||ts[GC.DECODED_BODY_SIZE]<=0)&&(is=J.ext||{},ts[GC.DECODED_BODY_SIZE]=is.originalResponseSize||0),c$3({t:GC.REQUEST_DATA,p:J,isRelatedH5:!!isDefined(J.info.isRelatedH5)}),setTimeout(function(){getCookieUserID()},50))}function Ee(){var J=window.fetch;window.fetch=function(es,ts){let is=typeof es=="object"?JSON.parse(JSON.stringify(es)):es;typeof es!="string"&&es&&(es.href||es.url)&&(is=es.href||es.url||"");var as=isDefined(window.$bonreeReplayUrl)?window.$bonreeReplayUrl:null,ns=he(is,ts);if((!_(is)&&is.indexOf(as)===-1||_(is))&&c$3({t:GC.LIFESTYLE_FETCH_START,info:ns}),A.isPeriod&&(ns.ai=A.actionID,ns.parentNetworkID=A.networkID,c$3({t:GC.LIFESTYLE_CALLBACK_REQUEST_START+ns.ai})),(ns=extend(ns,{reqhtData:{},reshtData:{}})).timestamp=now(),isDefined(f$2.sett)&&isDefined(f$2.sett.pageViewId)&&(ns.pageViewId=f$2.sett.pageViewId),BWexample.checkIsOpen(),BWexample.isOpen&&(extend(ns,{reqhtData:{},reshtData:{}}),isDefined(as=checkBlackAndWhite(ns,"_fetch")))&&isObj(as)&&forEachOwn(as,function(ls,fs){let us=ls;if(isObj(ls)&&(us=ls.sky?ls.fun(ls.url,ls.pathname):ls.fun(ls.len)),startWith(ls,"bnro=")&&(us+="_fetch_fetch"),isDefined(ts))if(isDefined(ts.headers)){if(ts.headers instanceof Headers){var cs,hs={};for(cs of ts.headers.entries())hs[cs[0]]=cs[1];ts.headers=hs}extend(ts.headers,{[fs]:us})}else extend(ts,{headers:{[fs]:us}});else ts={headers:{[fs]:us}}}),ts&&ts.headers){if(ts.headers instanceof Headers){var rs,ss={};for(rs of ts.headers.entries())ss[rs[0]]=rs[1];ts.headers=ss}U$2(ts.headers,ns)}ns.type=1,extend(ns,getTime());var as=J(es,ts),os=(extend(ns,pe(is,ts)),{info:ns}),ds=os.metric={};return ds[GC.UPLOAD_BODY_SIZE]=ue(q(is,ts,"body")),as.then(function(ls){ns.status=ls.status,ns.statusText=ls.statusText;var fs=ds[GC.RESPONSE_END]=tillNow(),fs=(ds[GC.RESPONSE_START]=ds[GC.CALLBACK_START]=fs,queueRequest(os),v$1(ls.headers)),us=fs,cs=toLower(us),hs=us.trim().split(/[\r\n]+/),ms={};hs.forEach(function(ws){var ws=ws.split(":"),gs=ws.shift(),ws=ws.join(": ");ms[gs]=ws}),ns.guid=isDefined(ms[GC.GUID_KEY])&&ms[GC.GUID_KEY].split(",")[0]||"",ns.xBrResponse=isDefined(ms[GC.X_BR_RESPONSE])&&ms[GC.X_BR_RESPONSE].split(",")[0]||"",ns.traceResponse=isDefined(ms[GC.TRACE_RESPONSE])&&ms[GC.TRACE_RESPONSE].split(",")[0]||"",ns[GC.RESPONSE_HEADER]=withLength(us,2e3);try{var ps=[];if(isDefined(f$2.sett.respHeaderTraceKey)&&0<f$2.sett.respHeaderTraceKey.length||isDefined(f$2.secondSett.respHeaderTraceKey)&&0<f$2.secondSett.respHeaderTraceKey.length){isDefined(f$2.secondSett.respHeaderTraceKey)&&0<f$2.secondSett.respHeaderTraceKey.length?ps=isDefined(f$2.sett.respHeaderTraceKey)?f$2.sett.respHeaderTraceKey.concat(f$2.secondSett.respHeaderTraceKey):f$2.secondSett.respHeaderTraceKey:isDefined(f$2.sett.respHeaderTraceKey)&&(ps=f$2.sett.respHeaderTraceKey);for(let $s=0,gs=ps.length-1;$s<=gs;$s++)-1<cs.indexOf(toLower(ps[$s]))&&(ns.reshtData[ps[$s]]=ls.headers.get(ps[$s]))}}catch($s){log("C148")}if(getResponseHead(fs),ls.$metric=os,ds[GC.CALLBACK_END]=tillNow(),hs=tryToClone(ls),hs)try{let $s={bytesLimit:Number.POSITIVE_INFINITY,collectStreamBody:!0};readBytesFromStream(hs.body,$s,function(gs){os.ext={originalResponseSize:gs},I(os,ls)})}catch($s){log("C149")}else I(os);return ls},function(ls){var fs=ds[GC.RESPONSE_END]=tillNow();throw ds[GC.RESPONSE_START]=ds[GC.CALLBACK_START]=ds[GC.CALLBACK_END]=fs,ns.status=window.TypeError&&ls instanceof TypeError?602:600,ns.eType="FETCH",ns[GC.STATUS_TEXT]=ns.status==602?"FETCH request exception":"User terminates FETCH request",I(os),ls}).catch(ls=>{throw ls.type=2,ls})}}function U$2(J,es){if(isDefined(J)){var ts,is=g$9.requestArr,ns=g$9.configeManage;if(0<is.length){for(let rs=0,ss=is.length-1;rs<=ss;rs++)if(J.hasOwnProperty(is[rs].rule)&&is[rs].index<=g$9.isStopGetValue&&u$2(J[is[rs].rule]))return ns[is[rs].index].value=""+J[is[rs].rule],g$9.checkValueRight()}(isDefined(f$2.sett.reqHeaderTraceKey)&&0<f$2.sett.reqHeaderTraceKey.length||isDefined(f$2.secondSett.reqHeaderTraceKey)&&0<f$2.secondSett.reqHeaderTraceKey.length)&&(ts=[],isDefined(f$2.secondSett.reqHeaderTraceKey)&&0<f$2.secondSett.reqHeaderTraceKey.length?ts=isDefined(f$2.sett.reqHeaderTraceKey)?f$2.sett.reqHeaderTraceKey.concat(f$2.secondSett.reqHeaderTraceKey):f$2.secondSett.reqHeaderTraceKey:isDefined(f$2.sett.reqHeaderTraceKey)&&(ts=f$2.sett.reqHeaderTraceKey),Object.keys(J).map(function(rs){includes(ts.map(function(ss){return ss.toLowerCase()}),rs.toLowerCase())&&(es.reqhtData[rs]=J[rs])}))}}function i$1(){if(!isFunction(window.fetch))return log("C147");v$6(GC.XHR_CALLBACK_IDENTIFY,function(J){A=J.info}),Ee()}function r$1(){le$1(),i$1(),window.performance&&window.performance.now&&H$1()}function p$1(J){var es=J.data;return isDefined(es.al)&&es.al===""&&delete es.al,J}function s$2(){try{return navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i)?2:1}catch(J){log("C119")}}function routeService(J){var es=new Metric(J.p,GC.ROUTE),ts=s$2(),ts=(es.info(GC.URL,"","tu").info(GC.REFERRER,"","fu").info(GC.DURATION,0,"d").info(GC.STATUS,0,"sta").info(GC.ALIAS,"","al").info(GC.PATH,"","pt").info(GC.ROOT,"","rt").info(GC.FULL_URL,"","pu").info(GC.FRAME_WORK,"","fw").info(GC.CUSTOM_IC,!1,"ic").info(GC.CLIENT_TYPE,ts,"ctp").info("pvid","","pvid").info("ns",0,"ns"),isDefined(J.p.info.wri)&&es.info("wri",[],"wri"),es.build());return isDefined(J.revts)&&0<J.revts.length&&(ts.revts=J.revts),p$1(ts)}var m$1=isDefined(window.location)?window.location:"";function C$2(){return window?window.Vue!==void 0||window.__VUE__||window.$nuxt&&window.$nuxt._isVue?"Vue":window.React?"React":window.ng!==void 0||isDefined(window.Zone)||isDefined(window.angular)?"Angular":"Other":"Other"}var h$2=[],n={};function O(){if(m$1.href.split("?")[0]!==(isDefined(location)?location.href:"").split("?")[0]){let rs={id:""};c$3({t:GC.ROUTE_CHANGE_START,info:rs}),isDefined(n)&&0<Object.getOwnPropertyNames(n).length&&Object.keys(n).map(ss=>{typeof n[ss]=="function"&&(n[ss](),delete n[ss])}),f$2.sett.isSinglePage=!0,f$2.sett.pageViewId=uuid();var J=extend({},location),es={info:{uuid:uuid(),timestamp:now(),url:J.href,referrer:extend({},m$1).href,duration:1e3,status:0,path:J.hash===""?J.pathname:J.hash,root:J.hash===""?J.origin+J.pathname:J.origin+J.pathname+J.hash.replace("#/",""),fullUrl:J.hash!==""?J.origin+J.pathname:J.origin+"/",framework:C$2(),pvid:f$2.sett.pageViewId,ns:isDefined(performance)&&isDefined(performance.timing)?performance.timing.navigationStart:0}},ts=(-1<es.info.url.indexOf("?")&&(es.info.url=es.info.url.split("?")[0]),-1<es.info.referrer.indexOf("?")&&(es.info.referrer=es.info.referrer.split("?")[0]),-1<es.info.path.indexOf("?")&&(es.info.path=es.info.path.split("?")[0]),-1<es.info.root.indexOf("?")&&(es.info.root=es.info.root.split("?")[0]),-1<es.info.fullUrl.indexOf("?")&&(es.info.fullUrl=es.info.fullUrl.split("?")[0]),function(){var ss,as,os,ds;isDefined(n)&&isDefined(n[es.info.uuid])&&(isDefined(n)&&isDefined(n[es.info.uuid])&&delete n[es.info.uuid],{type:ss,ent:as,sin:os,data:ds}=routeService({p:es}),c$3({t:GC.ROUTE_CHANGE_END+rs.id,routeData:{k:ss,ent:as,sin:os,v:ds}}))}),is=(isDefined(n)&&(n[es.info.uuid]=ts),setTimeout(ts,1e3)),ns=new PageActivityManager(function(ss){ss.forEach(as=>{var os={st:getFixedMetric(as,GC.START_TIME),name:d$1({p:{info:{url:as[GC.NAME]}}},f$2.sett).p.info.url,dura:isDefined(as[GC.DURATION])?getFixedMetric(as,GC.DURATION):as[GC.RESPONSE_END]-as[GC.START_TIME],rt:getMineTypeByUrl(as[GC.NAME]),fs:getFixedMetric(as,GC.FETCH_START),dls:getFixedMetric(as,GC.DOMAIN_LOOKUP_START),dle:getFixedMetric(as,GC.DOMAIN_LOOKUP_END),cs:getFixedMetric(as,GC.CONNECT_START),ce:getFixedMetric(as,GC.CONNECT_END),scs:getFixedMetric(as,GC.SECURE_CONNECTION_START),reqs:getFixedMetric(as,GC.REQUEST_START),rsps:getFixedMetric(as,GC.RESPONSE_START),rspe:getFixedMetric(as,GC.RESPONSE_END),rs:isDefined(as[GC.RESPONSE_STATUS])?as[GC.RESPONSE_STATUS]:-1};isDefined(as[GC.INITIATOR_TYPE])&&(os.it=as[GC.INITIATOR_TYPE]),isDefined(as[GC.DECODED_BODY_SIZE])?(os.ts=as[GC.TRANSFER_SIZE],os.ebs=as[GC.ENCODED_BODY_SIZE]>=Math.pow(2,64)?Math.pow(2,60):as[GC.ENCODED_BODY_SIZE],os.dbs=as[GC.DECODED_BODY_SIZE]):(os.ts=0,os.ebs=0,os.dbs=0),isDefined(os.it)&&(os.it==="xmlhttprequest"||os.it==="fetch")||h$2.push(os)}),isDefined(is)&&(clearTimeout(is),is=setTimeout(ts,1e3))});ns.turnOnRecord(),ns.startPageActivityObserver(),ts=function(){var ss,as,os,ds;isDefined(n)&&isDefined(n[es.info.uuid])?(isDefined(n)&&isDefined(n[es.info.uuid])&&delete n[es.info.uuid],0<ns.mutationRecord.length?0<ns.performanceRecord.length?(ss=ns.performanceRecord.pop(),as=ns.mutationRecord.pop(),es.info.duration=0<ss-as?ss-es.info.timestamp:as-es.info.timestamp):es.info.duration=ns.mutationRecord.pop()-es.info.timestamp:0<ns.performanceRecord.length&&(es.info.duration=ns.performanceRecord.pop()-es.info.timestamp),ns.turnOffRecord(),ns.stopPageActivityObserver(),isDefined(es)&&isDefined(h$2)&&0<h$2.length&&(es.info.wri=h$2),{type:ss,ent:as,sin:os,data:ds}=routeService({p:es}),c$3({t:GC.ROUTE_CHANGE_END+rs.id,routeData:{k:ss,ent:as,sin:os,v:ds}}),isDefined(h$2)&&(h$2=[])):(ns.turnOffRecord(),ns.stopPageActivityObserver())},isDefined(n)&&(n[es.info.uuid]=ts),m$1=J}}let U$1=class{constructor(J){this.currentLocation=extend({},J),m$1=this.currentLocation}trackHistory(){let J=history.pushState,es=history.replaceState;isDefined(J)&&!isDefined(J.$hasHooked)&&(history.pushState=function(ts,is,ns){J.apply(this,[ts,is,ns]),O()},history.pushState.$hasHooked=!0),isDefined(es)&&!isDefined(history.replaceState.$hasHooked)&&(history.replaceState=function(ts,is,ns){es.apply(this,[ts,is,ns]),O()},history.replaceState.$hasHooked=!0),window.addEventListener("popstate",O)}trackHashchage(){window.addEventListener("hashchange",O)}init(){this.trackHistory(),this.trackHashchage()}};function P(){var J=new U$1(isDefined(window.location)?window.location:"");J.init()}var w$1=0;function E(J){w$1=1e3*new Date().getTime()}function g$3(J){if(isDefined(J)){-1<J.constructor.toString().indexOf("String")&&(es=(isDefined(window.TextEncoder)?new TextEncoder().encode(J):J).length),-1<J.constructor.toString().indexOf("ArrayBuffer")&&(es=J.byteLength),-1<J.constructor.toString().indexOf("Blob")&&(es=J.size),-1<J.constructor.toString().indexOf("Array")&&(es=J.byteLength);var es,ts,is,ns=isDefined(this.name)?this.name:this.url.split("?")[0];try{/^[a-zA-Z0-9:_-\s@./]+$/.test(ns)!==!1&&((ts=new a$4(ns,"websocket")).setStatus(1),isDefined(ts.spanEvent)&&isDefined(ts.spanEvent.ic)&&(ts.spanEvent.ic=!1),ts.setTag("process","send"),ts.setTag("connectID",isDefined(this.identify)?this.identify:uuid()),ts.setData("url",isDefined(this.handledUrl)?this.handledUrl:this.url),(is=1e3*new Date().getTime()-w$1)===0&&(is=999),!isDefined(this.readyState)||this.readyState!==2&&this.readyState!==3||(ts.setStatus(2),ts.setStatusCode("WebSocket is already in CLOSING or CLOSED state.")),ts.setMetric("send",is,"us"),ts.setDuration(is),ts.setMetric("size",es,"byte"),ts.finish())}catch(rs){}}}function y(){var J=window.WebSocket;window.WebSocket.prototype.send=hack(window.WebSocket.prototype.send,E,g$3),window.WebSocket=function(){c$3({t:GC.WEBSOCKET_CONNECTION_START});let es=1e3*new Date().getTime();var ts=arguments[1]===void 0?new J(arguments[0]):new J(arguments[0],arguments[1]),is=ts&&ts.url&&-1<ts.url.indexOf("?")?ts.url.split("?")[0]:ts.url,ns=d$1({p:{info:{url:ts.url}}},f$2.sett).p.info.url;return ts.handledUrl=isDefined(ns)?ns:"",ts.identify=uuid(),ts.name=isDefined(is)?is:"",ts.hasHandleConnect=!1,ts.addEventListener("open",function(){var rs;/^[a-zA-Z0-9:_-\s@./]+$/.test(is)!==!1&&((rs=new a$4(is,"websocket")).spanEvent.st=0,rs.spanEvent.timestamp=es,rs.setStatus(1),isDefined(rs.spanEvent)&&isDefined(rs.spanEvent.ic)&&(rs.spanEvent.ic=!1),rs.setTag("process","connect"),rs.setTag("connectID",isDefined(ts.identify)?ts.identify:uuid()),rs.setData("url",ts.handledUrl),rs.setDuration(1e3*new Date().getTime()-es),rs.finish(),ts.hasHandleConnect=!0,c$3({t:GC.WEBSOCKET_CONNECTION_END}))}),ts.addEventListener("error",function(rs){var ss;ts.hasHandleConnect||/^[a-zA-Z0-9:_-\s@./]+$/.test(is)!==!1&&((ss=new a$4(is,"websocket")).spanEvent.st=0,ss.spanEvent.timestamp=es,ss.setStatus(2),isDefined(ss.spanEvent)&&isDefined(ss.spanEvent.ic)&&(ss.spanEvent.ic=!1),ss.setTag("process","connect"),ss.setTag("connectID",isDefined(ts.identify)?ts.identify:uuid()),ss.setData("url",ts.handledUrl),ss.setDuration(1e3*new Date().getTime()-es),ss.setStatusCode("Websocket connection failed"),ss.finish(),ts.hasHandleConnect=!0,c$3({t:GC.WEBSOCKET_CONNECTION_END}))}),ts},window.WebSocket=extend(window.WebSocket,J),window.WebSocket.prototype=J.prototype}function T(){if(f$2.sett.enableWebsocket!==!1)return window.WebSocket===void 0?log("C157"):window.WebSocket.prototype===void 0?log("C158"):void y()}var f={config:NIL_FN,error:NIL_FN,recordCustomActionEnd:NIL_FN};function S(){if(typeof document=="undefined"||!f$2.sett.probability||window.bonreeRUM)return log("C105"),window.bonreeRUM;P(),U$4(),ie(),r$1(),g$4(),b$2(),T(),isDefined(f$2.sett.RecordConfiguration||f$2.sett.mc)&&(isDefined(window.BonreeRecord)&&isDefined(window.BonreeRecord.BonreeRecord)?window.stopBonreeRecord=window.BonreeRecord.BonreeRecord(f$2.sett):isDefined(window.BonreeAgent)&&window.BonreeAgent.BonreeRecord?window.stopBonreeRecord=window.BonreeAgent.BonreeRecord(f$2.sett):log("C155"));var J=overwrite(f,{config:function(es,ts=!0,is=0){isDefined(es.osType)&&es.osType===GC.ANDROID&&f$2.sett.osType===GC.ANDROID||(f$2.setConfig.call(f$2,es,ts,is),isDefined(es.appId)&&B$5.setAppid(es))},error:h$5,recordCustomActionEnd:G});isDefined(window)&&(window.bonreeRUM=J),window.bonreeRUM.version=version,window.bonreeRUM.startSpan=function(es,ts){return new a$4(es,ts)},window.bonreeRUM.getSession=function(){return B$5.getSession().sessionID},window.bonreeRUM.getUserInfo=function(){return u$1.getUserInfo()},window.bonreeRUM.updateConfig=function(es){isDefined(window.stopBonreeRecord)&&(window.stopBonreeRecord(),window.stopBonreeRecordUpload=!0),g$6(),B$5.setAppid(es)},window.bonreeJsBridge=q$2,window.bonreeRUM=extend(window.bonreeRUM,q$2)}function r(J){var es=new Metric(J.p,GC.ACTION),es=(es.info("t").info("n").info("sa").info("i").info("vn").info("ic").info("p","","p",!0).info("lt").info("is").info("id").info("m").info("ice").info("ci").info("vt",1,"vt").info("vci","","vci"),isDefined(J.p.info.me)&&es.info("me"),es.build());return isDefined(J.revts)&&0<J.revts.length&&(es.revts=J.revts),es}function i(J){return J=new Metric(J.p,GC.CONSOLE),J.info("lv").info("msg","","msg",!0),J.build()}function a(J){var es=typeof window!="undefined"&&window.navigator&&window.navigator.userAgent?window.navigator.userAgent:"",ts=new Metric(J.p,GC.ERROR),is=isDefined(window.performance)&&isDefined(window.performance.timing)&&performance.timing.navigationStart||1;return isDefined(window.BRLog)&&window.BRLog.__sendErrorInfo(ts.data.info),ts.info(GC.PAGE_ID,"","pvid").info(GC.PAGE_URL,"","url").info(GC.ERROR_FILE,"","n").info(GC.MESSAGE,"","m").info(GC.ERROR_LINE,0,"l").info(GC.ERROR_COLUMN,0,"col").info(GC.PAGE_CREATE_TIME,1e3*is,"pct").info(GC.PAGE_TITLE,document.title||"noTitle","t").info("type",3,"ect").info("ua",es,"ua"),isDefined(J.p.info[GC.NAME]&&J.p.info[GC.NAME]!=="")&&ts.info(GC.NAME,"","et"),isDefined(J.p.info[GC.ERROR_STACK])&&J.p.info[GC.ERROR_STACK]!==""?ts.info(GC.ERROR_STACK,"","sta"):isDefined(ts.data.info.file)&&(ts.data.info[GC.ERROR_STACK]="undefined at @"+ts.data.info.file,ts.info(GC.ERROR_STACK,"at undefined @ "+ts.data.info.file,"sta")),isDefined(f$2.sett.isSinglePage)?ts.info("pt",2,"pt"):ts.info("pt",1,"pt"),ts.build()}let m={pvid:"",url:"",wpi:{},wri:[],ic:!1,timestamp:isDefined(performance.timing)&&performance.timing.navigationStart?1e3*performance.timing.navigationStart:now()};function b(es){isDefined(es.p.info[GC.PAGE_ID])&&(m.pvid=es.p.info[GC.PAGE_ID]),isDefined(es.p.info[GC.PAGE_URL])&&(m.url=es.p.info[GC.PAGE_URL]);var es=new Metric(es.p,GC.WEBVIEWDATA),{type:es,ent:ts,sin:is,data:ns}=(es.metric(GC.NAVIGATION_START,"","ns").metric(GC.UNLOAD_EVENT_START,0,"ues").metric(GC.FULL_RESOURCE_LOAD_TIME,0,"frlt").metric(GC.UNLOAD_EVENT_END,0,"uee").metric(GC.REDIRECT_START,0,"rds").metric(GC.REDIRECT_END,0,"rde").metric(GC.FETCH_START,0,"fs").metric(GC.DOMAIN_LOOKUP_START,0,"dls").metric(GC.DOMAIN_LOOKUP_END,0,"dle").metric(GC.CONNECT_START,0,"cs").metric(GC.SECURE_CONNECTION_START,0,"scs").metric(GC.CONNECT_END,0,"ce").metric(GC.REQUEST_START,0,"reqs").metric(GC.RESPONSE_START,0,"rsps").metric(GC.RESPONSE_END,0,"rspe").metric(GC.DOM_LOADING,0,"dl").metric(GC.DOM_INTERACTIVE,0,"di").metric(GC.DOM_CONTENT_LOADED_EVENT_START,0,"dcles").metric(GC.DOM_CONTENT_LOADED_EVENT_END,0,"dclee").metric(GC.DOM_COMPLETE,0,"dc").metric(GC.LOAD_EVENT_START,0,"les").metric(GC.LOAD_EVENT_END,0,"lee").metric(GC.FIRST_PAINT,0,"fp").metric(GC.FIRST_CONTENTFUL_PAINT,0,"fcp").metric(GC.LARGEST_CONTENTFUL_PAINT,0,"lcp").metric(GC.TRANSFER_SIZE,0,"ts").metric(GC.ENCODED_BODY_SIZE,0,"ebs").metric(GC.DECODED_BODY_SIZE,0,"dbs").metric("tti",0,"tti").metric(GC.RESPONSE_STATUS,-1,"rs"),m.wpi=es.build().data,P$3(g$2(m.wpi,m.url,m.pvid)));m$3.push(JSON.parse(JSON.stringify({k:es,ent:ts,sin:is,v:ns}))),ns.ru=d$1({p:{info:{url:ns.ru}}},f$2.secondSett).p.info.url,I$2.push({k:es,ent:ts,sin:is,v:ns})}function h$1(J){forEach(J.p||[],function(es){let ts=new Metric(es,GC.RESOURCE);ts.info(GC.START_TIME,0,"st").info(GC.NAME,"","name").info(GC.DURATION,0,"dura").info("ret","","rt").metric(GC.FETCH_START,0,"fs").metric(GC.DOMAIN_LOOKUP_START,0,"dls").metric(GC.DOMAIN_LOOKUP_END,0,"dle").metric(GC.CONNECT_START,0,"cs").metric(GC.CONNECT_END,0,"ce").metric(GC.SECURE_CONNECTION_START,0,"scs").metric(GC.REQUEST_START,0,"reqs").metric(GC.RESPONSE_START,0,"rsps").metric(GC.RESPONSE_END,0,"rspe").metric(GC.TRANSFER_SIZE,0,"ts").metric(GC.ENCODED_BODY_SIZE,0,"ebs").metric(GC.DECODED_BODY_SIZE,0,"dbs").metric(GC.RESPONSE_STATUS,-1,"rs"),isDefined(es.info)&&isDefined(es.info[GC.INITIATOR_TYPE])&&ts.info(GC.INITIATOR_TYPE,"","it");var is=ts.build().data;if(isDefined(is.it)&&!J.special){if(is.it!=="xmlhttprequest"&&is.it!=="fetch"){m.wri.push(is);let{type:ns,ent:rs,sin:ss,data:as}=P$3(C$1(is));m$3.push(JSON.parse(JSON.stringify({k:ns,ent:rs,sin:ss,v:as}))),as.ru=d$1({p:{info:{url:as.ru}}},f$2.secondSett).p.info.url,I$2.push({k:ns,ent:rs,sin:ss,v:as})}}else m.wri.push(is)})}function U(J){var es,ts;return isEmpty(m.wpi)?null:((es=new Metric({info:m},GC.WEBVIEWDATA)).info("pvid","","pvid").info("url","","url").info("wpi",{},"wpi").info("ic",!1,"ic"),0<m.wri.length&&es.info("wri",[],"wri"),isDefined(J.revts)&&0<J.revts.length?((ts=es.build()).revts=J.revts,ts):es.build())}function g$2(J,es,ts){if(es!=="about:blank"){let is=J,ns=getIP(es),rs=getMineTypeByUrl(es)||"text/html",ss=1e3*(is.dle-is.dls),as=(ss<0&&(ss=0),1e3*(is.ce-is.scs)||-1),os=((as<0||is.scs===0)&&(as=0),1e3*(is.reqs-is.ce)||-1),ds=(os<-1?os=999:os===-1&&(os=0),1e3*(is.rsps-is.reqs)||-1),ls=(ds<-1?ds=999:ds===-1&&(ds=0),0);return(ls=is.rsps===0?1e3*(is.rspe-is.fs)||-1:1e3*(is.rspe-is.rsps)||-1)<-1?ls=999:ls===-1&&(ls=0),J={info:{id:uuid(),ru:es,m:"GET",ti:ns[0]||"",tp:ns[1]||"",dt:ss,sslt:as,rt:os,rti:ds,dti:ls,ds:0,rds:0,pt:-1<es.indexOf("https")?2:1,ec:200,art:1,ret:rs,pvid:ts,ic:!1}},J.info.ct=0<is.ce-is.cs?1e3*(is.ce-is.cs):0,J.info.ti.length<1&&delete J.info.ti,J}}function C$1(fs){let es=fs,ts=es.name.slice(0,4096),is=3;if(isDefined(es.it))switch(es.it){case"xmlhttprequest":is=3;break;case"beacon":is=5;break;case"fetch":is=4;break;default:is=2}let ns=getIP(ts),rs=getMineTypeByUrl(ts)||"text/html",ss=1e3*(es.dle-es.dls),as=((ss<0||es.dura<=10)&&(ss=0),1e3*(es.ce-es.scs)||-1),os=((as<0||es.dura<=10||es.scs===0)&&(as=0),1e3*(es.reqs-es.ce)||-1),ds=(os<-1||es.dura<=10?os=999:os===-1&&(os=0),1e3*(es.rsps-es.reqs)||-1),ls=(ds<-1||es.dura<=10?ds=999:ds===-1&&(ds=0),0);(ls=es.rsps===0?1e3*(es.rspe-es.fs)||-1:1e3*(es.rspe-es.rsps)||-1)<-1||es.dura<=10?ls=999:ls===-1&&(ls=0);var fs={info:{timestamp:isDefined(es.st)&&isDefined(window.performance)&&isDefined(window.performance.timing)?1e3*(window.performance.timing.navigationStart+es.st):now(),id:uuid(),ru:ts,m:"GET",ti:ns[0]||"",tp:ns[1]||"",dt:ss,sslt:as,rt:os,rti:ds,dti:ls,ds:0,rds:0,pt:-1<ts.indexOf("https")?2:1,ec:200,art:is,ret:rs,ic:!1,pvid:isDefined(f$2.sett.pageViewId)?f$2.sett.pageViewId:""}},us=1e3*(es.ce-es.cs);return fs.info.ct=0<us?us:0,fs}function s$1(J){var es=new Metric(J.p,GC.SPAN);return es.info("st").info("n","","n",!0).info("t","","t").info("du",0,"du").info("sta",0,"sta").info("ic",!1,"ic"),isDefined(J.p.info)&&isDefined(J.p.info.da)&&es.info("da",[],"da"),isDefined(J.p.info)&&isDefined(J.p.info.m)&&es.info("m",[],"m"),isDefined(J.p.info)&&isDefined(J.p.info.tag)&&es.info("tag",[],"tag"),isDefined(J.p.info)&&isDefined(J.p.info.stac)&&es.info("stac","","stac"),isDefined(J.p.info)&&isDefined(J.p.info.sub)&&es.info("sub",{},"sub"),es.build()}function C(J){v$6(J,function(es){if(!isDefined(es.p))return log("C144");var ts=es.t;isDefined(es.p)&&isDefined(es.p.info)&&!isDefined(es.p.info[GC.PAGE_ID])&&(es.p.info[GC.PAGE_ID]=f$2.sett.pageViewId,es.p.info[GC.PAGE_URL]=d$1({p:{info:{url:isDefined(window.location)?window.location.href:""}}},f$2.sett).p.info.url);let is={};switch(ts){case GC.ERROR_DATA:w$2(is=a(es),a$3.webviewJSErrorEvent);break;case GC.CONSOLE_DATA:w$2(is=i(es),a$3.consoleEvent);break;case GC.REQUEST_DATA:isDefined(es)&&isDefined(es.p)&&isDefined(es.p.info.ai)||isDefined(es.isRelatedH5)&&es.isRelatedH5||(isEmpty(f$2.secondSett)||isEmpty(is=Q$1(JSON.parse(stringify(es)),f$2.secondSett))||(isDefined(f$2.secondSett.osType)&&f$2.secondSett.osType!==GC.BROWSER?a$3.NetworkEvent(stringify({ent:is.ent,v:is.data}),f$2.secondSett):pushQueueData(is)),isEmpty(is=Q$1(JSON.parse(stringify(es)),f$2.sett)))||(isDefined(f$2.sett.osType)&&f$2.sett.osType!==GC.BROWSER?a$3.NetworkEvent(stringify({ent:is.ent,v:is.data}),f$2.sett):pushQueueData(is)),isDefined(es)&&isDefined(es.p.info)&&isDefined(es.p.info.ai)&&c$3({t:GC.LIFESTYLE_REQUEST_END+es.p.info.ai,requestData:JSON.parse(stringify(es)),endTime:now()}),isDefined(es.isRelatedH5)&&es.isRelatedH5&&c$3({t:GC.LIFESTYLE_REQUEST_END+"H5",requestData:JSON.parse(stringify(es)),endTime:now()}),c$3({t:GC.LIFESTYLE_REQUEST_END,requestData:JSON.parse(stringify(es)),endTime:now()});break;case GC.SPAN_DATA:isDefined((is=s$1(es)).data)&&c$3({t:GC.SPAN_CALLBACK_DATA,websocketData:is}),is.isRelated||w$2(is,a$3.spanEvent);break;case GC.RESOURCE_DATA:if(es.type===1&&b(es),es.type===2&&h$1(es),es.type===3&&isDefined(is=U(es)))if(isDefined(f$2.sett.osType)&&f$2.sett.osType!==GC.BROWSER){let rs=window.top&&window.self&&window.top===window.self?1:0;is.imd=rs;var ns={ent:is.ent,v:is.data};isDefined(is.revts)&&0<is.revts.length&&(is.revts.map(function(ss){return ss.v.ru.split("?")[0]==window.location.href.split("?")[0]&&(ss.v.imd=rs),ss}),ns.revts=is.revts),checkMoudle("network")||delete ns.revts,a$3.webviewPerformanceTimingEvent(stringify(ns),f$2.sett,rs)}else pushQueueData(is);if(es.type===4&&isDefined(is=U(es)))if(isDefined(f$2.secondSett.osType)&&f$2.secondSett.osType!==GC.BROWSER){let rs=window.top&&window.self&&window.top===window.self?1:0;is.imd=rs,is.data.url=d$1({p:{info:{url:is.data.url}}},f$2.secondSett).p.info.url;var ns={ent:is.ent,v:is.data};isDefined(is.revts)&&0<is.revts.length&&(is.revts.map(function(as){return as.v.ru.split("?")[0]==window.location.href.split("?")[0]&&(as.v.imd=rs),as}),ns.revts=is.revts),checkMoudle("network")||delete ns.revts,a$3.webviewPerformanceTimingEvent(stringify(ns),f$2.secondSett,rs)}else pushQueueData(is);break;case GC.TRACE_ACTION_DATA:is=r(es),isDefined(es.type)&&es.type===1&&(isDefined(f$2.sett.osType)&&f$2.sett.osType!==GC.BROWSER?(ns={ent:is.ent,v:is.data},isDefined(is.revts)&&0<is.revts.length&&(ns.revts=is.revts),a$3.webviewActionEvent(stringify(ns),f$2.sett)):pushQueueData(is)),isDefined(es.type)&&es.type===2&&(isDefined(f$2.secondSett.osType)&&f$2.secondSett.osType!==GC.BROWSER?(is.data.vn=d$1({p:{info:{url:is.data.vn}}},f$2.secondSett).p.info.url,ns={ent:is.ent,v:is.data},isDefined(is.revts)&&0<is.revts.length&&(ns.revts=is.revts),a$3.webviewActionEvent(stringify(ns),f$2.secondSett)):pushQueueData(is));break;default:log("C118")}})}function L(){C([GC.ROUTE_DATA,GC.ERROR_DATA,GC.CONSOLE_DATA,GC.REQUEST_DATA,GC.TRACE_ACTION_DATA,GC.SPAN_DATA,GC.RESOURCE_DATA,GC.PAGE_DATA])}function g$1(){function J(){for(var rs=0;rs<is.length;rs++)is[rs]()}var es=!(!window.attachEvent||window.opera),ts=/webkit\/(\d+)/i.test(navigator.userAgent)&&RegExp.$1<525,is=[],ns=document;ns.ready=function(rs){if(!es&&!ts&&ns.addEventListener)return ns.addEventListener("DOMContentLoaded",rs,!1);var ss;1<is.push(rs)||(es?function(){try{ns.documentElement.doScroll("left"),J()}catch(as){setTimeout(arguments.callee,0)}}():ts&&(ss=setInterval(function(){/^(loaded|complete)$/.test(ns.readyState)&&(clearInterval(ss),J())},0)))}}function p(){return!/BonreeRUM=0/.test(window.location.search)||(f$2.checkProbability(0),0)}function v(){v$6([GC.PAGE_READY,GC.PAGE_LOAD],J=>{J.t===GC.PAGE_READY?u$1.changeState(1):u$1.changeState(2)})}function h(){if(!p())return log("C106",!0),!1;g$1(),v(),document.ready(function(){document.title&&u$1.setData(GC.TITLE,document.title)})&&isDefined(window)&&(window.setJsBridge=function(ts){window.bonreePrivateInterface=ts});try{var J=document.querySelector("#BonreeAgent");if(!isDefined(J))return!1;var es=J.getAttribute("data");if(!isJSON(es=window.decodeURIComponent&&window.decodeURIComponent(es)))return log("C102",!0),!1;if(isDefined((es=JSON.parse(es)).appId)||log("C101",!0),isDefined(window.getIsRunWithNetClient)&&(es.dataFusionInfo="net"),isDefined(window.bonreeRUM)?window.bonreeRUM.config(es,!1,1):f$2.setConfig(es),f$2.sett.initTime=now(),f$2.sett.probability)return!0;log("C103",!0)}catch(ts){return log("C102",!0),!1}return!0}let g=h();function w(){window.bonreeRUM?log("C104"):(window.BonreeRecord={BonreeRecord},S(),L(),_$1())}function s(J){if(isDefined(window.getIsRunWithNetClient)&&(J.dataFusionInfo="net"),isDefined(window.bonreeRUM)){if(isDefined(window.bonreeRUM.config))return void window.bonreeRUM.config(J,!1,1)}else f$2.setConfig(J);f$2.sett.initTime=now(),w()}g&&w();let l={BonreeRecord,BonreeStart:s,Sm4:O$6,getDeviceInfo,setExtraInfo:M$7,setUserID:y$5};window.BonreeAgent=l,exports.BonreeRecord=BonreeRecord,exports.BonreeStart=s,exports.Sm4=O$6,exports.getDeviceInfo=getDeviceInfo,exports.setExtraInfo=M$7,exports.setUserID=y$5});
