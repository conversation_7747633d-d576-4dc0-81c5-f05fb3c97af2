<!--
 * @Author: 逗逗飞
 * @Date: 2024-03-11 10:20:57
 * @LastEditors: 逗逗飞
 * @LastEditTime: 2024-05-07 17:26:52
 * @FilePath: /Component-Gallery/playground/public/index.html
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<!DOCTYPE html>
<html lang="">
<head>
  <meta charset="utf-8" />
  <meta content="IE=edge" http-equiv="X-UA-Compatible" />
  <meta content="width=device-width,initial-scale=1.0" name="viewport" />
  <link href="<%= BASE_URL %>favicon.ico" rel="icon" />
  <!-- 测试环境探针 -->
  <script type="text/javascript" src="./BonreeSDK_JS.min.js" data="<%= VUE_APP_REQ_BONREE_AGENT %>" id="BonreeAgent" ></script>
  <title>中国铁塔</title>
  <!--    <title><%= htmlWebpackPlugin.options.title %></title>-->
  <!-- <script src="<%= BASE_URL %>EasyPlayer-lib.min.js"></script> -->
</head>
<body class="mainbody">
<noscript>
  <strong
  >We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work
    properly without JavaScript enabled. Please enable it to
    continue.</strong
  >
</noscript>
<div id="app"></div>
<!-- built files will be auto injected -->
</body>
</html>
