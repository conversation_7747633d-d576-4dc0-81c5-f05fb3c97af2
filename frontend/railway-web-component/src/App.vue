<template>
  <div id="app">
    <router-view />
    <base-video-listener @click="videoListenerClick" @new="newPosition" @append="appendPosition" />
  </div>
</template>

<script>
import { requestSDK } from '@ct/iframe-connect-sdk' // 导入SDK，用于与外部系统通信
import MapRef from './common/MapRef'
export default {
  name: 'App',
  components: {},
  provide() {
    return {
      mapRef: new MapRef({
        onMapSet: this.onMapChange,
      }),
      mapFlag: () => this.mapFlag,
    }
  },
  data: function () {
    return {
      mapFlag: null, // 用于触发更新用的简易对象。这个对象仅仅用于刷新引用，里面的数据不具备任何意义。
    }
  },
  mounted() {
    this.initBonreeUserId() // 组件挂载后初始化Bonree用户ID
  },
  methods: {
    onMapChange() {
      this.mapFlag = {}
    },
    videoListenerClick() {
      this.$globalEventBus.$emit(`videoListener__click`, {})
    },
    newPosition(index) {
      this.$globalEventBus.$emit(`newPosition__click`, { index })
    },
    appendPosition(index) {
      this.$globalEventBus.$emit(`appendPosition__click`, { index })
    },
    // 初始化Bonree用户ID的方法
    async initBonreeUserId() {
      // 调用requestSDK获取用户信息
      const res = await requestSDK('getInfo')
      // 如果bonreeJsBridge存在，则设置用户ID为获取到的用户名
      window.bonreeJsBridge &&
        window.bonreeJsBridge.setUserID(res.user.userName)
    },
  },
}
</script>
<style lang="scss" scoped>
* {
  margin: 0;
  padding: 0;
}
html,
body,
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
  height: 100vh;
  width: 100vw;
  margin: 0px;
  padding: 0px;
  background-color: #090f18;
  /* overflow: hidden; */
  .big-screen + .video-item-body.video-dialog-drag {
    position: unset;
  }
  ::v-deep .video-item.newPositionClass {
    z-index: 985 !important;
  }
}
img {
  vertical-align: top;
}
</style>
<style>
@import './assets/css/main.css'; /* 导入全局样式 */
</style>
