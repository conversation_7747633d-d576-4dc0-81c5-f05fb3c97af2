import axios from 'axios';
import { MessageBox, Notification } from 'element-ui';

// const baseUrl = process.env.VUE_APP_BASE_API + '/api/';

// const baseUrl = process.env.VUE_APP_BASE_API;

let baseUrl = '';
if (process.env.NODE_ENV === 'development') {
  baseUrl = process.env.VUE_APP_BASE_API;
} else {
  // http://**************:9091/component-gallery/api/business?xxxx
  // baseUrl = process.env.VUE_APP_SERVICE_URL + 'component-gallery/api/';
  baseUrl = '/component-gallery/api/';
}
// const prefix = process.env.NODE_ENV === 'development' ? '' : '/component-gallery';
// const baseUrl = window.location.protocol + '//' + window.location.host + prefix + '/api/';

console.log(baseUrl, '>>>>>>', process.env.NODE_ENV);
let outLoginCount = 0;
axios.defaults.withCredentials = true;
axios.defaults.headers = { 'Content-Type': 'application/json;charset=UTF-8' };
const request = axios.create({
  baseURL: baseUrl, // 基础url。
  timeout: 60000, // 请求超时时间
  withCredentials: true,
  validateStatus: function () {
    return true;
  },
});

// request interceptor(请求拦截器)
request.interceptors.request.use(
  config => {
    //自动传cookie
    config.credentials = true;
    config.headers['clienttype'] = 'pc';
    config.headers['Authorization'] = 'Bearer ' + sessionStorage.getItem('Admin-Token');
    return config;
  },
  error => {
    let respError = {
      status: error.response.status,
      statusText: error.response.Unauthorized,
    };
    return Promise.reject(respError);
  },
);

// response interceptor（接收拦截器）
request.interceptors.response.use(
  response => {
    const res = response;
    res.url = response.request.responseURL;
    res.time = response.headers.date;
    const msg = res.data.msg || res.data.message; // 网关响应参数可能为msg或message
    // 暂时 . 工具箱看这里获取视频设备code400
    if (res.data.code === 200 || res.data.code === 400) {
      return res.data;
    } else if (res.data.code === 401) {
      // 401:token失效
      if (outLoginCount === 0) {
        MessageBox.alert(msg + '，请重新登录', '系统提示', {
          confirmButtonText: '确定',
          type: 'warning',
          callback: () => {
            // 为了重新实例化vue-router对象 避免bug
            location.reload();
          },
        });
      }
      outLoginCount++;
    } else {
      // if (outLoginCount === 0) {
      //   if (msg === '未知异常，请联系管理员') {
      //     Notification.error({
      //       title: '错误',
      //       message: msg,
      //     });
      //   } else {
      //     Notification.warning({
      //       title: '警告',
      //       message: msg || '系统接口请求异常',
      //     });
      //   }
      // }
      outLoginCount++;

      return Promise.reject({
        title: '警告',
        message: msg || '系统接口请求异常',
      });
    }
  },
  error => {
    let { message } = error;
    // let timer;
    if (outLoginCount === 0) {
      if (message === 'Network Error') {
        message = '系统接口请求异常';
      } else if (message.includes('timeout')) {
        message = '系统接口请求超时';
      } else if (message.includes('Request failed with status code')) {
        const code = message.substr(message.length - 3);
        if (code === '503') {
          Notification.warning({
            title: '警告',
            message: '服务尚未注册成功,请确认已启动服务后稍等!',
          });
        } else if (code === '401') {
          // 401:token失效
          MessageBox.alert('暂未登录或token已经过期，请重新联系管理员或重新登录!', '系统提示', {
            confirmButtonText: '确定',
            type: 'warning'
          });
        } else {
          message = '系统接口' + message.substr(message.length - 3) + '异常';
          Notification.error({
            title: '错误',
            message: message,
          });
        }
      } else {
        Notification.error({
          title: '错误',
          message: message,
        });
      }
    }
    outLoginCount++;

    return Promise.reject(error);
  },
);

export default request;
