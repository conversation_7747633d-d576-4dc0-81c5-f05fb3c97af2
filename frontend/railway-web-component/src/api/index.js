/**
 * 导入axios库，用于发起HTTP请求
 */
import axios from 'axios';
/**
 * 导入FileSaver库，用于保存文件
 */
import FileSaver from 'file-saver';
/**
 * 导入moment库，用于处理日期和时间
 */
import moment from 'moment';
/**
 * 导入qs库，用于序列化查询参数
 */
import qs from 'qs';
/**
 * 导入Vue实例
 */
import vue from '../main';
/**
 * 导入请求工具方法
 */
import _request from '../utils/request';
import { requestSDK } from "@ct/iframe-connect-sdk";

const _header = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'POST, GET, PUT, OPTIONS, DELETE',
  'authorization': `Bearer ${sessionStorage.getItem('Admin-Token') || ''}`
}

/**
 * 用户登录接口
 * @param {Object} query 查询参数，通常包含用户名和密码
 * @returns {Promise} 返回登录结果的Promise对象
 */
export const login = (query) => {
  return _request({
    url: '/api/login',
    method: 'get',
    params: query,
  });
};

/**
 * 发起GET请求
 * @param {string} url 请求的URL
 * @param {Object} param GET请求的查询参数
 * @param {Object} header 请求的头部信息
 * @returns {Promise} 返回请求结果的Promise对象
 */
const get = async (url, param = {}, header = {}) => {
  if (vue.$env.VUE_APP_REQ_SDK === 'false') {
    try {
      const response = await axios({
        url: `${url}`,
        method: 'get',
        params: param,
        requestId: url,
        timeout: 30000,
        headers: _header,
      });
      return response.data;
    } catch (err) {
      return {
        isError: true,
        statusCode: -10001,
        message: '接口异常',
        data: null,
      };
    }
  } else {
    const response = await vue.$requestSDK(url, param, header, 'get', 'json');
    return response;
  }
};

/**
 * 发起POST请求
 * @param {string} url 请求的URL
 * @param {Object} param POST请求的参数
 * @param {Object} header 请求的头部信息
 * @returns {Promise} 返回请求结果的Promise对象
 */
const post = async (url, param = {}, header = {}) => {
  if (vue.$env.VUE_APP_REQ_SDK === 'false') {
    try {
      const response = await axios({
        url: `${url}`,
        method: 'post',
        data: param,
        requestId: url,
        timeout: 30000,
        headers: _header,
      });
      return response.data;
    } catch (err) {
      return {
        isError: true,
        statusCode: -10001,
        message: '接口异常',
        data: null,
      };
    }
  } else {
    const response = await vue.$requestSDK(url, param, header, 'post', 'json');
    return response;
  }
};

/**
 * 发起POST请求，使用表单格式提交数据
 * @param {string} url 请求的URL
 * @param {Object} param POST请求的参数
 * @param {Object} header 请求的头部信息
 * @returns {Promise} 返回请求结果的Promise对象
 */
const form = async (url, param = {}, header = {}) => {
  const headerParam = Object.assign(
    { 'Content-Type': 'application/x-www-form-urlencoded' },
    header
  );
  const params = qs.stringify(param);
  if (vue.$env.VUE_APP_REQ_SDK === 'false') {
    try {
      const response = await axios({
        url: `${url}`,
        method: 'post',
        data: params,
        requestId: url,
        timeout: 30000,
        headers: headerParam,
      });
      return response.data;
    } catch (err) {
      return {
        isError: true,
        statusCode: -10001,
        message: '接口异常',
        data: null,
      };
    }
  } else {
    const response = await vue.$requestSDK(url, params, headerParam, 'post');
    return response;
  }
};

/**
 * 通过POST请求下载Excel文件
 * @param {string} url 请求的URL
 * @param {Object} param POST请求的参数
 * @param {Function} error 请求失败时的回调函数
 * @param {Function} success 请求成功时的回调函数
 * @returns {Promise} 返回请求结果的Promise对象
 */
const downloadExcelPost = async (
  url,
  param = {},
  error = () => {
    console.log('downloadExcelPost error');
  },
  success = () => {
    console.log('downloadExcelPost success');
  }
) => {
  const params = Object.assign(
    { fileName: `${moment().format('YYYYMMDDHHmmss')}.xlsx` },
    param
  );
  const { code, data } = await post(url, params, {});
  if (code === 200) {
    success();
    const blobData = base64ToBlob(data);
    FileSaver.saveAs(blobData, param.fileName);
  } else {
    error();
  }
};

/**
 * 将base64编码转换为Blob对象
 * @param {string} base64 base64编码字符串
 * @returns {Blob} 转换后的Blob对象
 */
const base64ToBlob = (base64) => {
  let str = atob(base64);
  let n = str.length;
  let u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = str.charCodeAt(n);
  }
  return new Blob([u8arr]);
};

/**
 * 发起HTTP请求
 * @param {Object} config 请求的配置对象，包含URL、方法、参数、数据和头部信息
 * @returns {Promise} 返回请求结果的Promise对象
 */
const request = async ({
  url,
  method,
  params = {},
  data = {},
  header = {},
}) => {
  let response = {};
  try {
    if (vue.$env.VUE_APP_REQ_SDK === 'false') {
      const _response = await axios({
        url,
        method,
        params,
        data,
        headers: _header // header,
      });
      if (_response.status === 200) {
        response = _response.data;
      } else {
        response = {
          isError: true,
          statusCode: -10001,
          message: '接口异常',
          data: null,
        };
      }
    } else {
      response = await vue.$requestSDK(
        url,
        method === 'get' ? params : data,
        header,
        method,
        'json'
      );
    }
  } catch (e) {
    response = {
      isError: true,
      statusCode: -10001,
      message: '接口异常',
      data: null,
    };
    console.error(e);
  }
  return response;
};

const getStream = async (url, param = {}, header = {}) => {
  if (vue.$env.VUE_APP_REQ_SDK === 'true') {
    try {
      const response = await axios({
        url: `${url}`,
        method: 'get',
        params: param,
        requestId: url,
        timeout: 30000,
        responseType: 'blob',
        headers: header,
      });
      return response.data;
    } catch (err) {
      return {
        isError: true,
        statusCode: -10001,
        message: '接口异常',
        data: null,
      };
    }
  } else {
    const response = await vue.$requestSDK(url, param, header, 'get', 'json');
    if (response.status == 200) {
      return response.data;
    } else {
      return;
    }
  }
};

// 下载文件方法
const downloadFile = async (
  url,
  param = {},
  error = () => {
    console.info('------error default----------');
  },
  success = () => {
    console.info('------success default----------');
  }
) => {
  console.log('downloadFile url', url)
  // 参数拼接
  const params = Object.assign(
    { fileName: `${moment().format('YYYYMMDDHHmmss')}.xlsx` },
    param
  );
  // 增加token
  const token = sessionStorage.getItem('Admin-Token');
  // 返回对象
  const res = await getStream(url, params, { responseType: 'blob', authorization: `Bearer ${token}` });
  if (res) {
    success();
    let type = 'text/plain;charset=utf-8';
    if (param.fileName.endsWith('.docx')) {
      type =
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document;charset=utf-8';
    }
    if (param.fileName.endsWith('.doc')) {
      type = 'application/msword;charset=utf-8';
    }
    if (param.fileName.endsWith('.pdf')) {
      type = 'application/pdf;charset=utf-8';
    }
    if (param.fileName.endsWith('.xls')) {
      type = 'application/vnd.ms-excel;charset=utf-8';
    }
    if (param.fileName.endsWith('.xlsx')) {
      type =
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8';
    }

    const blob = new Blob([res], { type: type });
    let url2 = window.URL.createObjectURL(blob);
    let link = document.createElement('a');
    link.style.display = 'none';
    link.download = param.fileName;
    link.href = url2;
    document.body.appendChild(link);
    link.click();
    window.URL.revokeObjectURL(link.href);
  } else {
    console.info('----------------res--nothing---');
    error();
  }
};

/**
 * beforeCreate之前发起POST请求
 * @param {string} url 请求的URL
 * @param {Object} param POST请求的参数
 * @param {Object} header 请求的头部信息
 * @returns {Promise} 返回请求结果的Promise对象
 */
const postBeforeCreate = async (url, envReqSdk = 'false', param = {}, header = {}) => {
  if (envReqSdk === 'false') {
    try {
      const response = await axios({
        url: `${url}`,
        method: 'post',
        data: param,
        requestId: url,
        timeout: 30000,
        headers: header,
      });
      return response.data;
    } catch (err) {
      return {
        isError: true,
        statusCode: -10001,
        message: '接口异常',
        data: null,
      };
    }
  } else {
    const response = await requestSDK(url, param, header, 'post', 'json');
    return response;
  }
};

export default {
  get,
  post,
  form,
  request,
  downloadExcelPost,
  getStream,
  downloadFile,
  postBeforeCreate,
};
