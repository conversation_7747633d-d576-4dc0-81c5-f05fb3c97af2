/**
 * 广播素材库相关接口
 */
import api from '@/api'; // 导入API模块
import vue from '@/main'; // 导入Vue实例

const {
  request,
} = api; // 从api模块中解构出请求函数

/**
 * 素材管理服务类
 * 提供与素材库相关的各种操作接口
 */
export default {
  // 语音文件过期问题，获取新的url  fileUrls:[urlString]
  qryNewVoiceUrl: (data) =>
    request({
      url: `${vue.$env.VUE_APP_REQ_PREFIX_PLAT}/system/getObsEncryptUrls`,
      method: "post",
      data,
    }),
  //文件过期问题，获取新的url  fileUrls:[urlString]
  qryNewFileUrl: (data) =>
    request({
      url: `${vue.$env.VUE_APP_REQ_PREFIX_PLAT}/system/getObsEncryptUrls`,
      method: 'post',
      data
    }),
}
