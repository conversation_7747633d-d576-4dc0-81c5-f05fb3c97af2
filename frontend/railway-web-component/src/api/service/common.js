/**
 * 导入api模块，包含请求配置
 */
import api from '@/api';
/**
 * 导入全量视图服务，用于获取主题组件配置
 */
// import { getThemeCompByCode } from '@/api/service/fullviewService';
import ConstEnum from '@/components/common/ConstEnum';
/**
 * 导入配置枚举，用于字典和配置项的键值参考
 */
// import {
//   CfgEnum
// } from '@/components/page/ScreenPages/IntegratedMonitor/enum/CfgEnum';
/**
 * 导入vue实例，用于访问全局配置和方法
 */
import vue from '@/main';
import { Loading, Message, MessageBox } from 'element-ui';
import FileSaver from 'file-saver';

const { request } = api;

const getDictListByCatCode = (catCode) =>
  request({
    url: `${vue.$env.VUE_APP_REQ_PREFIX_DICT}/dictValue/qryByCatCode`,
    method: 'get',
    params: {
      catCode
    }
  })

/**
 * 定义导出的默认对象，包含各种字典和数据请求方法
 */
export default {
  /**
   * 根据字典分类代码查询字典项列表
   * @param {string} catCode 字典分类代码
   * @returns {Promise} 返回字典项列表的请求Promise
   */
  // 根据catCode查询字典数据
  getDictListByCatCode,

  /**
   * 查询通用平台的字典数据，根据字典类型
   * @param {object} params 请求参数对象，包含字典类型
   * @returns {Promise} 返回字典数据的请求Promise
   */
  // 通用平台字典接口 入参dictType
  getCommonPlatDictByDictType: (params) =>
    request({
      url: `${vue.$env.VUE_APP_REQ_PREFIX_DICT}/platDict/getDictDataByType`,
      method: 'get',
      params
    }),

  /**
   * 查询事件类型
   * @param {object} params 请求参数对象，包含字典类型
   * @returns {Promise} 返回字典数据的请求Promise
 */
  // 通用平台字典接口 入参dictType
  getEventTypeDictByDictType: (params) =>
    request({
      url: `${vue.$env.VUE_APP_REQ_PREFIX_PLAT}/event/selectMultiRelateTagInfoByHighwayAlarmType`,
      method: 'post',
      params
    }),

  /**
   * 查询通用平台的用户权限区域树
   * @param {object} params 请求参数对象，用于过滤区域树
   * @returns {Promise} 返回区域树的请求Promise
   */
  // 通用平台，查询用户权限区域树
  getCommonPlatUserAreaTree: (params) =>
    request({
      url: `${vue.$env.VUE_APP_REQ_PREFIX_PLAT}/system/getRegionTree`,
      method: 'post',
      params
    }),
  /**
   *
   * @param {*} params
   * @returns
   */
  getIndustryConfigInfo: (params) =>
    request({
      url: `${vue.$env.VUE_APP_REQ_PREFIX_PLAT}/system/queryIndustryConfigInfo`,
      method: 'post',
      params
    }),
};

/**
 * 异步下载文件的方法
 * @param {string} src 文件下载的URL
 * @param {string} name 文件的保存名称
 */
export const downloadFile = async (src, name) => {
  const param = {
    fileId: '',
    fileName: name
  };
  await api.downloadFile(src, param, () => {
    vue.$message.error('导出失败');
  });
};

/**
 * 异步获取用户信息的方法
 * @returns {Promise<object>} 返回包含用户信息的Promise
 */
export const getUserInfo = async () => {
  return await vue.$requestSDK('getInfo');
};

const {
  get,
  post,
  postBeforeCreate
} = api;
const httpCode = {
  ok: 200
};
/**
 * 发起GET请求并处理响应的方法
 * @param {object} config 请求配置，包含URL和参数
 * @returns {Promise<[boolean, any]>} 返回一个包含请求成功状态和数据的Promise
 */
export const getProxy = async ({
  url,
  params
}) => {
  const {
    code,
    data
  } = await get(url, params);
  if (code === httpCode.ok) {
    return [true, data];
  }
  return [false, data];
};

/**
 * 获取主题布局配置的方法
 * @param {object} params 请求参数，可能包含主题代码
 * @returns {Promise<any>} 返回主题布局配置的Promise
 */
export const getComponentLayout = async (params) => {
  return await getProxy({
    url: `${vue.$env.VUE_APP_REQ_PREFIX_BIZ}/adsLayoutTheme/getThemeByCode`,
    params
  });
};

/**
 * 获取铁塔线上地图配置
 * @param {Object} params 请求参数，包含通道代码、设备代码等
 * @returns {Promise} 返回Promise对象
 */
export const getTenantMapConfig = async () => {
  return await postProxy({
    url: `${vue.$env.VUE_APP_REQ_PREFIX_SYS}/admin/custom/manage/queryTenantMapConfigInfo4Tenant`,
    params: {}
  });
};

/**
 * 获取区域树数据
 * @param {object} params 请求参数，可能包含主题代码
 * @returns {Promise<any>} 返回主题布局配置的Promise
 */
export const getRegionTreeByUser = async (params) => {
  return await getProxy({
    url: `${vue.$env.VUE_APP_REQ_PREFIX_PLAT}/system/getRegionTreeByUser`,
    params
  });
};

/**
 * 获取默认的地图配置
 * @returns
 */
const getDefaultMapConfig = async () => {
  const { code, data } = await getDictListByCatCode("MAP_ADDR");
  // 处理系统配置数据
  if (code !== 200 || !data || data.length < 1) {
    return null;
  }
  return (data[0].comments || null);
}

/**
 * 获取地图瓦片配置的方法
 * @returns {void} 无返回值，但会全局配置hjCfg用于地图瓦片的访问
 */
export const getMapTiles = async () => {
  //   const [success, data] = await getThemeCompByCode({
  //     compCode: CfgEnum.MAP_CFG.Key
  //   });
  const success = true;
  const data = {
    "compId": 10,
    "compName": "地图组件",
    "compCode": "MAP_ADDR",
    "compIconUrl": null,
    "compImgUrl": null,
    "defaultWidth": null,
    "defaultHeight": null,
    "minWidth": null,
    "minHeight": null,
    "maxWidth": null,
    "maxHeight": null,
    "compUrl": null,
    "showTech": "NATIVE",
    "compParam": "",
    "isSystem": "Y",
    "modelCodes": "",
    "state": "A",
    "tenantId": "121062346",
    "industryCode": "800001",
    "creatorId": "",
    "creatorName": null,
    "gmtCreate": "2025-02-21T11:28:48",
    "gmtModify": null,
    "modifierId": null,
    "modifierName": null,
    "compParamList": [
      {
        "paramId": 8,
        "compId": 10,
        "paramName": "瓦片地址",
        "paramCode": "tiles",
        "iconUrl": "",
        "seq": 1,
        "paramJson": "{\"MAP_CFG\":{\"maxZoom\":17,\"minZoom\":6,\"defCenter\":[116.38813,39.89446],\"defZoom\":12,\"tiles\":{\"defTile\":{\"sourceOpt\":{\"url\":\"https://slwyytest.tower0788.cn/geoserver/railway_space/wms\",\"params\":{\"SERVICE\":\"WMS\",\"VERSION\":\"1.1.1\",\"REQUEST\":\"GetMap\",\"FORMAT\":\"image/png\",\"TRANSPARENT\":\"true\",\"STYLES\":\"\",\"LAYERS\":\"railway-project\",\"exceptions\":\"application/vnd.ogc.se_inimage\",\"SRS\":\"EPSG:4326\",\"WIDTH\":\"768\",\"HEIGHT\":\"651\",\"BBOX\":\"99.03076171875,31.7449951171875,107.46826171875,38.8970947265625\"},\"serverType\":\"geoserver\",\"transition\":0},\"sourceType\":\"TileWMS\"},\"satelliteTile\":[{\"sourceOpt\":{\"url\":\"http://webst01.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}\"},\"sourceType\":\"XYZ\"},{\"sourceOpt\":{\"url\":\"http://webst01.is.autonavi.com/appmaptile?style=8&x={x}&y={y}&z={z}\"},\"sourceType\":\"XYZ\"}],\"railwayLayer\":{\"sourceOpt\":{\"url\":\"https://slwyytest.tower0788.cn/geoserver/railway_space/wms\",\"params\":{\"SERVICE\":\"WMS\",\"VERSION\":\"1.1.1\",\"REQUEST\":\"GetMap\",\"FORMAT\":\"image/png\",\"TRANSPARENT\":\"true\",\"STYLES\":\"\",\"LAYERS\":\"railway-rail-single\",\"exceptions\":\"application/vnd.ogc.se_inimage\",\"SRS\":\"EPSG:4326\",\"WIDTH\":\"768\",\"HEIGHT\":\"651\",\"BBOX\":\"99.03076171875,31.7449951171875,107.46826171875,38.8970947265625\"},\"serverType\":\"geoserver\"},\"sourceType\":\"TileWMS\"}}}}",
        "isDefault": "Y",
        "state": "A",
        "creatorId": "",
        "creatorName": "",
        "gmtCreate": "2025-02-21T11:42:22",
        "gmtModify": "2025-02-21T11:42:22",
        "modifierId": "",
        "modifierName": "",
        "tenantId": "121062346",
        "industryCode": null,
        "defaultValue": ""
      }
    ]
  }
  // 是否需要补充查询地图配置
  let needSupplement = false;
  if (!success || !data || data.length < 1) {
    needSupplement = true;
  }
  const {
    compParamList
  } = (data || {});
  const temp = (compParamList || []).find(item => item.paramCode === 'tiles');
  let { defaultValue: compParam } = (temp || {});

  if (!compParam) {
    // console.error('!!!!瓦片参数未配置');
    needSupplement = true;
  }
  // 补充查询字典表
  if (needSupplement) {
    compParam = await getDefaultMapConfig();
    if (!compParam) {
      console.error('!!!!补充查询瓦片参数，仍未配置');
      return;
    }
  }
  // 补充获取铁塔线上地图配置
  //   const [tenantMapConfigSuccess, tenantMapConfigData] = await getTenantMapConfig();
  const tenantMapConfigSuccess = true;
  const tenantMapConfigData = {
    "satellite_2D": [
      {
        "maxZoom": 17,
        "minZoom": 1,
        "url": "https://t{0-7}.tianditu.gov.cn/img_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=img&style=default&tileMatrixSet=w&format=tiles&TileMatrix={z}&TileRow={y}&TileCol={x}&tk=86db5390161c4da2abe96c3fca00f403"
      },
      {
        "maxZoom": 17,
        "minZoom": 1,
        "url": "https://t{0-7}.tianditu.gov.cn/cia_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=cia&style=default&tileMatrixSet=w&format=tiles&TileMatrix={z}&TileRow={y}&TileCol={x}&tk=86db5390161c4da2abe96c3fca00f403"
      }
    ]
  }
  window.hjCfg = JSON.parse(compParam);
  window.tenantMapConfig = tenantMapConfigSuccess ? tenantMapConfigData : {};
  console.log('地图配置--', window.hjCfg)
};
/**
 * 发起POST请求并处理响应的方法
 * @param {object} config 请求配置，包含URL和参数
 * @returns {Promise<[boolean, any]>} 返回一个包含请求成功状态和数据的Promise
 */
export const postProxy = async ({
  url,
  params
}) => {
  const {
    code,
    data,
    msg
  } = await post(url, params);
  if (code === httpCode.ok) {
    return [true, data];
  }
  return [false, data, msg];
};

/**
 * 额外处理地区树数据
 * @param {*} user
 */
export const supplyRegionTree = async (user) => {
  if (!user.areaTree || user.areaTree.length < 1) {
    // 重新通过接口获取
    const [, data] = await getRegionTreeByUser();
    user.areaTree = data || [];
  }
}

/**
 * 获取铁塔大屏配置
 * @param {Object} params 请求参数，包含通道代码、设备代码等
 * @returns {Promise} 返回Promise对象
 */
export const getAllViewTemplate = async () => {
  return await postProxy({
    url: `${vue.$env.VUE_APP_REQ_PREFIX_SYS}/manage/industry/config/queryAllViewTemplate`,
    params: {}
  });
};

export const initUserProvinceCityData = async () => {
  const { user } = await getUserInfo();
  await supplyRegionTree(user);
  window.userInfo = user;
  // 调用接口查询省市数据
  if (!user.areaTree || user.areaTree.length < 1) {
    return [false];
  }
  if (user.areaTree[0].id === '100000') {
    user.areaTree = user.areaTree[0].children;
  }
  let regionLevel = ConstEnum.REGION_LEVEL.province;
  let regionCode = '';
  if (user.areaTree[0].children[0].children.length < 2) {
    regionLevel = ConstEnum.REGION_LEVEL.district;
    regionCode = user.areaTree[0].children[0].children[0].id;
    window.userInfo.defArea = [
      user.areaTree[0].id,
      user.areaTree[0].children[0].id,
      user.areaTree[0].children[0].children[0].id
    ];
    window.userInfo.defAreaName = user.areaTree[0].children[0].children[0].label;
  } else if (user.areaTree[0].children.length < 2) {
    regionLevel = ConstEnum.REGION_LEVEL.city;
    regionCode = user.areaTree[0].children[0].id;
    window.userInfo.defArea = [
      user.areaTree[0].id,
      user.areaTree[0].children[0].id
    ];
    window.userInfo.defAreaName = user.areaTree[0].children[0].label;
  } else {
    regionLevel = ConstEnum.REGION_LEVEL.province;
    regionCode = user.areaTree[0].id;
    window.userInfo.defArea = [
      user.areaTree[0].id
    ];
    window.userInfo.defAreaName = user.areaTree[0].label;
  }

  let addressTemp = [];
  user.areaTree.forEach((item) => {
    item.value = item.id;
    item.regionLevel = ConstEnum.REGION_LEVEL.province;
    if (item.children && item.children.length > 0) {
      let sonLevelList = [];
      item.children.forEach((secondChild) => {
        secondChild.value = secondChild.id;
        secondChild.regionLevel = ConstEnum.REGION_LEVEL.city;
        sonLevelList.push(secondChild);
        if (secondChild.children && secondChild.children.length > 0) {
          let thirdLevelList = [];
          secondChild.children.forEach((thirdChild) => {
            thirdChild.value = thirdChild.id;
            delete thirdChild.children;
            thirdChild.regionLevel = ConstEnum.REGION_LEVEL.district;
            thirdLevelList.push(thirdChild);
          });
          secondChild.children = thirdLevelList;
          secondChild.disabled = thirdLevelList.length < 2;
        }
      });
      item.children = sonLevelList;
      item.disabled = sonLevelList.length < 2;
    }
    addressTemp.push(item);
  });
  window.userInfo.roleAreaTree = addressTemp;
  return [
    true, {
      regionLevel,
      regionCode
    }
  ];
};

export function exportExcel({
  url,
  query,
  fileName,
  message = '是否确认导出所有的数据项?',
  title = '警告',
  customClass = ''
}) {
  MessageBox.confirm(message, title, {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
    customClass
  }).then(async () => {
    const loadingInstance = Loading.service({
      lock: true,
      text: '数据正在准备中，请稍等...',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    const [success, data, msg] = await postProxy({
      url,
      params: query
    });
    loadingInstance.close();
    if (!success) {
      Message.warning(msg || '导出失败，请检查网络');
      return;
    }
    Message.success(msg || '导出成功');
    const base64 = data.split('base64,')[1];
    const blobData = base64ToBlob(base64);
    FileSaver.saveAs(blobData, (fileName || '导出文件') + '.xlsx');
  });
}

/**
 * base64数据转成blob格式
 * @param {*} base64
 * @returns
 */
export function base64ToBlob(base64) {
  const str = atob(base64);
  let n = str.length;
  const u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = str.charCodeAt(n);
  }
  return new Blob([u8arr]);
}

export const getRegionInfo = async (params) => {
  const {
    code,
    data,
    msg
  } = await request({
    url: `admin/video/getRegion`,
    method: 'post',
    data: params
  });
  if (code === httpCode.ok) {
    return [true, data];
  }
  return [false, data, msg];
};

/**
 * 用户登陆缓存token缓存
 */
export const cacheAuthorization = (envReqSdk, urlPrefix) => {
  postBeforeCreate(`${urlPrefix}/authorization/cache`, envReqSdk, {});
}
