/**
 * 导入服务层的请求函数，用于封装API请求。
 */
import { getProxy, postProxy } from '@/api/service/common';
/**
 * 导入Vue实例，用于访问全局配置和方法。
 */
import vue from '@/main';

/**
 * 异步获取线路状态统计摘要。
 * @param {Object} params 请求参数对象。
 * @returns {Promise} 返回包含统计信息的Promise对象。
 */
export const getLineStatSummary = async (params) => {
  return await getProxy({
    url: `${vue.$env.VUE_APP_REQ_PREFIX_BIZ}/adsNetStat/getLineStatSummary`,
    params
  });
};

/**
 * 异步获取线路级别数量统计。
 * @param {Object} params 请求参数对象。
 * @returns {Promise} 返回包含统计信息的Promise对象。
 */
export const getLineLevelNumStat = async (params) => {
  return await getProxy({
    url: `${vue.$env.VUE_APP_REQ_PREFIX_BIZ}/adsNetStat/getLineLevelNumStat`,
    params
  });
};

/**
 * 异步获取线路级别长度统计。
 * @param {Object} params 请求参数对象。
 * @returns {Promise} 返回包含统计信息的Promise对象。
 */
export const getLineLevelLengthStat = async (params) => {
  return await getProxy({
    url: `${vue.$env.VUE_APP_REQ_PREFIX_BIZ}/adsNetStat/getLineLevelLengthStat`,
    params
  });
};

/**
 * 异步获取线路使用长度统计。
 * @param {Object} params 请求参数对象。
 * @returns {Promise} 返回包含统计信息的Promise对象。
 */
export const getLineUsageLengthStat = async (params) => {
  return await getProxy({
    url: `${vue.$env.VUE_APP_REQ_PREFIX_BIZ}/adsNetStat/getLineUsageLengthStat`,
    params
  });
};

/**
 * 异步获取线路站点列表。
 * @param {Object} params 请求参数对象。
 * @returns {Promise} 返回包含站点列表的Promise对象。
 */
export const getLineStationList = async (params) => {
  return await getProxy({
    url: `${vue.$env.VUE_APP_REQ_PREFIX_BIZ}/adsNetStation/list`,
    params
  });
};

/**
 * 异步获取预警统计信息。
 * @param {Object} params 请求参数对象。
 * @returns {Promise} 返回包含预警统计信息的Promise对象。
 */
export const getWarningStatistics = async (params) => {
  return await getProxy({
    url: `${vue.$env.VUE_APP_REQ_PREFIX_PLAT}/analysis/getWarningStatistics`,
    params
  });
};

/**
 * 事件统计接口
 * @param {Object} params 请求参数对象。
 * @returns {Promise} 返回包含预警统计信息的Promise对象。 
 */
export const getEvtStatistics = async (params) => {
  return await postProxy({
    url: `${vue.$env.VUE_APP_REQ_PREFIX_BIZ}/fusionEvent/statisticalEventByCondition`,
    params
  });
};

/**
 * 异步获取预警类型统计信息。
 * @param {Object} params 请求参数对象。
 * @returns {Promise} 返回包含预警类型统计信息的Promise对象。
 */
export const getWarnTypeStatistics = async (params) => {
  return await getProxy({
    url: `${vue.$env.VUE_APP_REQ_PREFIX_PLAT}/analysis/getWarnTypeStatistics`,
    params
  });
};

/**
 * 异步获取预警来源统计信息。
 * @param {Object} params 请求参数对象。
 * @returns {Promise} 返回包含预警来源统计信息的Promise对象。
 */
export const getWarnSourceStatistics = async (params) => {
  return await getProxy({
    url: `${vue.$env.VUE_APP_REQ_PREFIX_PLAT}/analysis/getWarnSourceStatistics`,
    params
  });
};

/**
 * 事件类型统计接口
 * @param {Object} params 请求参数对象。
 * @returns {Promise} 返回包含预警来源统计信息的Promise对象。
 */
export const getEvtTypesStatistics = async (params) => {
  return await postProxy({
    url: `${vue.$env.VUE_APP_REQ_PREFIX_BIZ}/fusionEvent/statisEventBySourceType`,
    params
  });
};

/**
 * 事件告发地TOP5
 * @param {Object} params 请求参数对象。
 * @returns {Promise} 返回包含区域事件数量统计信息的Promise对象。
 */
export const getStatisEventByArea = async (params) => {
  return await postProxy({
    url: `${vue.$env.VUE_APP_REQ_PREFIX_BIZ}/fusionEvent/statisEventByArea`,
    params
  });
}

/**
 * 异步获取区域事件数量统计。
 * @param {Object} params 请求参数对象。
 * @returns {Promise} 返回包含区域事件数量统计信息的Promise对象。
 */
export const getEventNumByArea = async (params) => {
  return await getProxy({
    url: `${vue.$env.VUE_APP_REQ_PREFIX_PLAT}/analysis/getEventNumByArea`,
    params
  });
}

/**
 * 异步获取自动化处理事件统计信息。
 * @param {Object} params 请求参数对象。
 * @returns {Promise} 返回包含自动化处理事件统计信息的Promise对象。
 */
export const getAutoHandleEvtStatistics = async (params) => {
  return await getProxy({
    url: `${vue.$env.VUE_APP_REQ_PREFIX_PLAT}/analysis/getAutoHandleEvtStatistics`,
    params
  });
};

/**
 * 自动化处置分析
 * @param {Object} params 请求参数对象。
 * @returns {Promise} 返回包含自动化处理事件统计信息的Promise对象。
 */
export const getStatisAnalysis = async (params) => {
  return await postProxy({
    url: `${vue.$env.VUE_APP_REQ_PREFIX_BIZ}/fusionEvent/statisAnalysis`,
    params
  });
};

/**
 * 异步查询订单预警信息。
 * @param {Object} params 请求参数对象。
 * @returns {Promise} 返回包含订单预警信息的Promise对象。
 */
export const findOrderAlarm = async (params) => {
  return await postProxy({
    url: `${vue.$env.VUE_APP_REQ_PREFIX_PLAT}/event/findOrderAlarm`,
    params
  });
};

/**
 * 异步根据代码获取主题组件配置。
 * @param {Object} params 请求参数对象，包含组件代码等信息。
 * @returns {Promise} 返回包含主题组件配置的Promise对象。
 */
export const getThemeCompByCode = async (params) => {
  return await getProxy({
    url: `${vue.$env.VUE_APP_REQ_PREFIX_BIZ}/adsLayoutThemeComp/getThemeCompByCode`,
    params
  });
};

/**
 * 异步根据代码获取首页配置。
 * @param {Object} params 请求参数对象，包含首页代码等信息。
 * @returns {Promise} 返回包含首页配置的Promise对象。
 */
export const getIndexByCode = async (params) => {
  return await postProxy({
    url: `${vue.$env.VUE_APP_REQ_PREFIX_BIZ}/adsLayoutThemeComp/getIndexByCode`,
    params
  });
};

