import vue from '@/main';
import { getProxy, postProxy, getAllViewTemplate } from './common';

// 大屏参数
let viewInfos = [];

// 解析url
const setViewInfos = (viewId, compoPath) => {
  if (compoPath && compoPath.split("//").length > 1) {
    const path = compoPath.split("//")[1];
    const pathArr = path.split('/');
    let url = pathArr[pathArr.length - 1];
    if (!url && pathArr.length > 1) {
      url = pathArr[pathArr.length - 2];
    }
    if (url) {
      viewInfos.push(`${viewId}:${url}`);
    }
  }
}
// 获取大屏参数
const getViewInfos = async () => {
  if (viewInfos.length < 1) {
    const [success, data] = await getAllViewTemplate();
    if (success) {
      data.forEach(item => {
        if (item.viewJson) {
          const viewObj = JSON.parse(item.viewJson)[0];
          const { compInfo: { compoPath } } = viewObj;
          setViewInfos(item.viewId, compoPath);
        }
      })
    }
  }
}

/**
 * 根据参数查询广告布局图层配置的分页列表
 * @param {Object} params 查询参数
 * @returns {Promise} 返回查询结果的Promise对象
 */
// 查询素材库分页列表
export const getMapLegend = async (params) => {
  return await getProxy({
    url: `${vue.$env.VUE_APP_REQ_PREFIX_BIZ}/adsLayoutLayerConfig/getLayerList`,
    params
  });
};

/**
 * 根据模型代码查询广告布局图层配置
 * @param {Object} params 查询参数，包含模型代码
 * @returns {Promise} 返回查询结果的Promise对象
 */
export const getLayerCfgByModelCode = async (params) => {
  return await postProxy({
    url: `${vue.$env.VUE_APP_REQ_PREFIX_BIZ}/adsLayoutLayerConfig/list`,
    params
  });
};

/**
 * 根据图层代码查询元数据
 * @param {Object} params 查询参数，包含图层代码
 * @returns {Promise} 返回查询结果的Promise对象
 */
export const getMetaByDicCode = async (params) => {
  return await postProxy({
    url: `${vue.$env.VUE_APP_REQ_PREFIX_BIZ}/adsLayoutLayerConfig/listByLayerCode`,
    params
  });
};

/**
 * 获取地图事件配置
 * @param {Object} params 查询参数，配置开启状态和焦点状态
 * @returns {Promise} 返回查询结果的Promise对象
 */
export const getMapEvts = async (params) => {
  // 原接口参数
  // params.isOpen = 'Y';
  // params.isFocus = 'Y';
  return await postProxy({
    // adsEvtTypeConfig/getWarningOrderLayer 老的事件接口
    url: `${vue.$env.VUE_APP_REQ_PREFIX_BIZ}/fusionEvent/list`,
    params
  });

};

/**
 * 根据设备ID查询设备详情
 * @param {Object} params 查询参数，包含设备ID
 * @returns {Promise} 返回查询结果的Promise对象
 */
export const getDeviceDetail = async (params) => {
  await getViewInfos();
  return await postProxy({
    url: `${vue.$env.VUE_APP_REQ_PREFIX_BIZ}/deviceFacilities/detail`,
    params: {
      ...params,
      viewInfos
    }
  });
};

/**
 * 根据设施ID查询设施详情
 * @param {Object} params 查询参数，包含设施ID
 * @returns {Promise} 返回查询结果的Promise对象
 */
export const getFacilityDetail = async (params) => {
  return await getProxy({
    url: `${vue.$env.VUE_APP_REQ_PREFIX_BIZ}/adsNetFacility/detail`,
    params
  });
};

/**
 * 根据设施ID查询畜牧设备详情
 * @param {Object} params 查询参数，包含设施ID
 * @returns {Promise} 返回查询结果的Promise对象
 */
export const getAnimalDetail = async (params) => {
  return await getProxy({
    url: `${vue.$env.VUE_APP_REQ_PREFIX_BIZ}/adsHusPositionRt/detail`,
    params
  });
};

/**
 * 分页查询广播列表
 * @param {Object} params 查询参数
 * @returns {Promise} 返回查询结果的Promise对象
 */
export const getBroadcastList = async (params) => {
  return await getProxy({
    url: `${vue.$env.VUE_APP_REQ_PREFIX_BIZ}/deviceFacilities/broadPlayRecordByPage
`,
    params
  });
};

/**
 * 分页查询多媒体素材列表
 * @param {Object} params 查询参数
 * @returns {Promise} 返回查询结果的Promise对象
 */
export const geBroadcastMetaList = async (params) => {
  return await getProxy({
    url: `${vue.$env.VUE_APP_REQ_PREFIX_BIZ}/material/qryMaterialListOfPage`,
    params
  });
};


/**
 * 发送对讲请求
 * @param {Object} params 请求参数
 * @returns {Promise} 返回请求结果的Promise对象
 */
export const playMeta = async (params) => {
  return await postProxy({
    url: `${vue.$env.VUE_APP_REQ_PREFIX_BIZ}/deviceFacilities/startTalkSend`,
    params
  });
};

/**
 * 取消订阅
 * @param {Object} params 取消订阅的参数，包含预订ID和版本号
 * @returns {Promise} 返回取消订阅结果的Promise对象
 */
export const cancelSub = async (params) => {
  const {
    bookingId,
    version
  } = params;
  return await postProxy({
    url: `${vue.$env.VUE_APP_REQ_PREFIX_BIZ}/broadcast/sub/cancel?bookingId=${bookingId}&version=${version}`,
    params
  });
};

/**
 * 获取视频流地址
 * @param {Object} params 请求参数，包含通道代码、设备代码等
 * @returns {Promise} 返回视频流地址的Promise对象
 */
export const getVideoStream = async ({
  channelCode,
  deviceCode
}) => {
  return await postProxy({
    url: `${vue.$env.VUE_APP_REQ_PREFIX_SYS}/video-video/video/player/sdk/getVideoRealtimeUrl`,
    params: {
      channelCode,
      deviceCode,
      streamType: 2,
      protocolType: location.protocol === 'https:' ? '9' : '5',
      netType: 2
    }
  });
};

/**
 * 根据参数查询事件设备捕获信息
 * @param {Object} params 查询参数
 * @returns {Promise} 返回查询结果的Promise对象
 */
export const getPassRoute = async (params) => {
  return await postProxy({
    url: `${vue.$env.VUE_APP_REQ_PREFIX_BIZ}/evtDevcCapture/getEvtDevcCapture`,
    params
  });
};

/**
 * 分页查询广播记录
 * @param {Object} params 查询参数
 * @returns {Promise} 返回查询结果的Promise对象
 */
export const getBroadCastHistoryGroup = async (params) => {
  return await getProxy({
    url: `${vue.$env.VUE_APP_REQ_PREFIX_BIZ}/deviceFacilities/broadPlayRecordByPageByGroup`,
    params
  });
};

/**
 * 根据参数查询流程链接信息
 * @param {Object} params 查询参数
 * @returns {Promise} 返回查询结果的Promise对象
 */
export const queryFlowLink = async (params) => {
  return await postProxy({
    url: `${vue.$env.VUE_APP_REQ_PREFIX_PLAT}/event/queryFlowLink`,
    params
  });
};

/**
 * 根据参数查询布局主题配置详情
 * @param {Object} params 查询参数
 * @returns {Promise} 返回查询结果的Promise对象
 */
export const getDetailCfg = async (params) => {
  return await getProxy({
    url: `${vue.$env.VUE_APP_REQ_PREFIX_BIZ}/adsLayoutThemeConfig/queryLayoutThemeConfigDetail`,
    params
  }
  );
};

/**
 * 根据参数查询审批列表
 * @param {Object} params 查询参数
 * @returns {Promise} 返回查询结果的Promise对象
 */
export const getApprovalList = async (params) => {
  return await getProxy({
    url: `${vue.$env.VUE_APP_REQ_PREFIX_BIZ}/dict/approval/list`,
    params
  }
  );
};

/**
 * 根据参数保存或更新审批详情
 * @param {Object} params 保存或更新参数
 * @returns {Promise} 返回保存或更新结果的Promise对象
 */
export const postApprovalDetail = async (params) => {
  return await postProxy({
    url: `${vue.$env.VUE_APP_REQ_PREFIX_BIZ}/dict/approval/batchSaveOrUpdate`,
    params
  });
};

/**
 * 根据参数查询事件详情
 * @param {Object} params 查询参数
 * @returns {Promise} 返回查询结果的Promise对象
 */
export const queryEvtDetail = async (params) => {
  // 原接口
  // return await postProxy({
  //   url: `${vue.$env.VUE_APP_REQ_PREFIX_BIZ}/adsEvtInfo/detail`,
  //   params
  // });
  return await getProxy({
    url: `${vue.$env.VUE_APP_REQ_PREFIX_BIZ}/fusionEvent/detail`,
    params
  });
};

/**
 * 根据参数查询告警详情
 * @param {Object} params 查询参数
 * @returns {Promise} 返回查询结果的Promise对象
 */
export const queryOrderInfo = async (params) => {
  // event vue.$env.VUE_APP_REQ_PREFIX_PLAT
  return await postProxy({
    url: `${vue.$env.VUE_APP_REQ_PREFIX_BIZ}/fusionEvent/getOrderInfo`,
    params
  });
};

/**
 * 根据参数查询当前事件控制角色
 * @param {Object} params 查询参数
 * @returns {Promise} 返回查询结果的Promise对象
 */
export const queryEvtCtlRole = async (params) => {
  return await postProxy({
    url: `${vue.$env.VUE_APP_REQ_PREFIX_PLAT}/event/getCurrentByOrderIdAndLinkId`,
    params
  });
};

/**
 * 同时执行多个Promise任务
 * @param {Array} promises Promise数组
 * @returns {Promise} 返回包含所有Promise结果的数组的Promise对象
 */
export const promiseAll = async (promises) => {
  return await Promise.all(promises);
};

/**
 * 发起"看这里"指令
 * @param {Object} params 指令参数
 * @returns {Promise} 返回执行结果的Promise对象
 */
export const lookHere = async (params) => {
  return await postProxy({
    url: `${vue.$env.VUE_APP_REQ_PREFIX_PLAT}/device/lookHere`,
    params
  });
};

/**
 * 根据参数查询布局配置列表
 * @param {Object} params 查询参数
 * @returns {Promise} 返回查询结果的Promise对象
 */
export const qryLayoutCfg = async (params) => {
  return await getProxy({
    url: `${vue.$env.VUE_APP_REQ_PREFIX_BIZ}/dimLayoutTheme/list`,
    params
  });
};

/**
 * 根据参数查询资源详情
 * @param {Object} params 查询参数
 * @returns {Promise} 返回查询结果的Promise对象
 */
// 桥梁/隧道详情
export const getResourceDetail = async (params) => {
  return await postProxy({
    url: `${vue.$env.VUE_APP_REQ_PREFIX_BIZ}/resource/detail`,
    params,
  });
};

/**
 * 素材喊话接口
 * @param {Object} params 指令参数
 * @returns {Promise} 返回执行结果的Promise对象
 */
export const metaSpeaker = async (params) => {
  return await postProxy({
    url: `${vue.$env.VUE_APP_REQ_PREFIX_BIZ}/deviceFacilities/matrialHornTalk`,
    params
  });
};

/**
 * 获取摄像机树
 * @param {Object} params 查询参数，包含模型代码
 * @returns {Promise} 返回查询结果的Promise对象
 */
export const getMonitorLabelTree = async (params) => {
  return await postProxy({
    url: `${vue.$env.VUE_APP_REQ_PREFIX_PLAT}/device/getMonitorTree`,
    params
  });
};

/**
 * websocket推送认证
 * @param {Object} params 查询参数，包含模型代码
 * @returns {Promise} 返回查询结果的Promise对象
 */
export const wssAuth = async (params) => {
  return await postProxy({
    url: `${vue.$env.VUE_APP_REQ_PREFIX_BIZ}/wss/auth`,
    params
  });
};


/**
 * 过车数据分页接口
 * @param {Object} params 查询参数
 * @returns {Promise} 返回查询结果的Promise对象
 */
export const getEvtDevcCaptureByPage = async (params) => {
  return await postProxy({
    url: `${vue.$env.VUE_APP_REQ_PREFIX_BIZ}/evtDevcCapture/getEvtDevcCaptureByPage`,
    params,
  });
};

/**
 * 过车数据详情查询
 * @param {Object} params 查询参数
 * @returns {Promise} 返回查询结果的Promise对象
 */
export const getEvtDevcCaptureDetail = async (params) => {
  return await getProxy({
    url: `${vue.$env.VUE_APP_REQ_PREFIX_BIZ}/evtDevcCapture/getEvtDevcCaptureDetail`,
    params,
  });
};

export default {
  promiseAll
}
