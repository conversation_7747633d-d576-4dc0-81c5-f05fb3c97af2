/*
 * @Description:
 * @Author: liu.yongli
 * @Date: 2023-12-13 11:09:43
 * @LastEditTime: 2024-07-16 11:01:14
 * @LastEditors: liu.yongli
 */
import axios from "axios";

const service = axios.create({
    timeout: 5000,
});
// 请求配置
service.interceptors.request.use(
    (config) => {
        // 请求头添加一些公共信息

        return config;
    },
    (error) => {
        console.log(error);
        return Promise.reject();
    }
);
// 返回配置
service.interceptors.response.use(
    (response) => {
        if (response.status === 200) {
            //公共接口返回

            return response.data;
        } else {
            Promise.reject();
        }
    },
    (error) => {
        console.log(error);
        return Promise.reject();
    }
);

export default service;
