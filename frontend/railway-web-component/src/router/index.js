/*
 * @Description: 路由文件
 * @Author: liu.yongli
 * @Date: 2025-04-02 18:56:59
 * @LastEditTime: 2025-04-17 16:28:35
 * @LastEditors: liu.yongli
 */
import Vue from 'vue'
import VueRouter from 'vue-router'
import { setRootFontSize } from '@/utils/index.js'

Vue.use(VueRouter)

const constantRoutes = [
  // 大屏调试菜单路径及其对应的组件
  // 大屏调试菜单 ../components/page/ScreenPages/IntegratedMonitor/index.vue
  {
    path: '/rw-screen-demo',
    component: async () => await import('../components/page/ScreenPages/demoIntegratedMonitor/index.vue'),
  },
  // 综合监测大屏路径及其对应的组件
  {
    path: '/monitorScreen', // 综合监测大屏
    component: async () => await import('../components/page/ScreenPages/IntegratedMonitor/index.vue'),
  },
  // 演示系统-综合监测大屏路径及其对应的组件
  {
    path: '/monitorScreenDemo', // 综合监测大屏
    component: async () => await import('../components/page/ScreenPages/demoIntegratedMonitor/index.vue'),
  },
  // 全景展示大屏路径及其对应的组件
  {
    path: '/fullViewMonitor',
    component: async () => await import('../components/page/ScreenPages/FullViewMonitor/index.vue'),
  },
  // 事件归档路径及其对应的组件
  {
    path: '/eventFile', // 事件归档
    component: async () => await import('../components/page/ScreenPages/eventFile/index.vue'),
  },
  // 演示系统-事件归档大屏路径及其对应的组件
  {

    path: '/eventFileDemo', // 事件归档
    component: async () => await import('../components/page/ScreenPages/demoEventFile/index.vue'),
  },
]
// 创建路由
const router = new VueRouter({
  base: 'industry-11153', // 部署应用包时的基本 URL
  mode: 'history', // history 模式
  scrollBehavior: () => ({ y: 0 }), // 返回顶部
  routes: constantRoutes // 路由列表
})
// 路由守卫
router.beforeEach((to, from, next) => {
  // console.log('router.beforeEach', to, from)
  // 设置根字体大小
  setRootFontSize('v')
  next()
})

export default router
