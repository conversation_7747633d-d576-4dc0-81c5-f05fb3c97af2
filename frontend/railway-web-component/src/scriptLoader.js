/*
 * @Author: 米亚流年 <EMAIL>
 * @Date: 2024-03-29 09:19:33
 * @LastEditors: 米亚流年 <EMAIL>
 * @LastEditTime: 2024-03-29 13:18:47
 * @FilePath: /component-remote-page-loader/lib/script-loader/src/scriptLoader.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { logVersion, extractVersion } from './log';
const removeElNode = el => {
  if (el.parentNode) el.parentNode.removeChild(el);
  el.onload = null;
  el.onerror = null;
  el = null;
};

export const scriptLoad = options => {
  const {
    config: { js, css, name },
  } = options;
  return new Promise((resolve, reject) => {
    const target = document.getElementsByTagName('script')[0] || document.head;
    const script = document.createElement('script');
    const link = document.createElement('link');
    script.src = js;
    script.async = true;
    link.href = css;
    link.rel = 'stylesheet';
    script.onload = () => {
      logVersion(`${name}`, `${extractVersion(name)}`, '#606060', '#1475B2');
      resolve();
      removeElNode(script);
    };
    script.onerror = () => {
      reject(new Error(`load ${name} failed`));
      removeElNode(script);
    };
    document.head.appendChild(link);
    target.parentNode.insertBefore(script, target);
  });
};

export const scriptLoadAsync = async options => {
  const { data } = options;
  try {
    await Promise.all(data.map(scriptLoad));
    return data.map(component => ({
      name: 'RemoteComponentsLoader',
      config: {
        name: component.config.name,
      },
    }));
  } catch (error) {
    console.error('Error loading scripts:', error);
    throw error;
  }
};

export const loadSpaceShardAsync = async options => {
  const { data } = options;
  const chunkSize = 4; // 分片大小
  const chunkedData = [];

  // 分片数据
  for (let i = 0; i < data.length; i += chunkSize) {
    chunkedData.push(data.slice(i, i + chunkSize));
  }
  console.log('分片的数据----->', chunkedData);
  try {
    await Promise.all(chunkedData.map(chunk => Promise.all(chunk.map(scriptLoad))));
    return data.map(component => ({
      name: 'RemoteComponentsLoader',
      config: {
        name: component.config.name,
      },
    }));
  } catch (error) {
    console.error('Error loading scripts:', error);
    throw error;
  }
};

export const loader = sfc => {
  let vueRef = {};
  if (Object.prototype.toString.call(window[sfc]) === '[object Module]') {
    Object.keys(window[sfc]).forEach(key => {
      vueRef = window[sfc][key];
    });
  }
  return vueRef;
};
