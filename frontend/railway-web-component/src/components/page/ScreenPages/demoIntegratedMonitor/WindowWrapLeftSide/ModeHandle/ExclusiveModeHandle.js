import ComponentHandle from '@/components/common/ComponentHandle'; // 导入组件处理模块
import LayerConst from '@/components/page/ScreenPages/IntegratedMonitor/Map/LayerConst'; // 导入图层常量
import { maplayer } from '../../Map/maplayer'; // 导入地图图层模块
/**
 * 左侧详情面板独占模式
 *
 */
const ExclusiveModeHandle = {
  contentWidows: {}, // 存储内容窗口的对象
  evtContent: null, // 存储事件内容的对象
  setState($this) {
    this.$this = $this; // 设置当前对象的状态
  },
  addContent({ component, props, key, group = 'default', onclose }) {
    // 添加内容窗口
    const comp = ComponentHandle.createComponent({
      component, // 组件
      props, // 组件属性
      key, // 组件唯一标识
      group, // 组件分组，默认为 'default'
      onclose: (params) => {
        // 关闭时的回调函数
        if (props.markerType === LayerConst.ALARM_EVENT) {
          this.closeEvt(); // 如果是警报事件，关闭事件窗口
        } else {
          this.closeContent(key, group); // 否则关闭内容窗口
        }
        onclose && onclose(key, params); // 执行传入的关闭回调
      }
    });
    this.closeAllContentWindows(); // 关闭所有内容窗口
    maplayer.getKeys().map(markerType => {
      // 事件模式是独立于其他的图例的模式
      if (markerType !== props.markerType && markerType !== LayerConst.ALARM_EVENT) {
        maplayer.getLayer(markerType).cancelSelected(markerType); // 取消选中状态
      }
    });
    if (props.markerType === LayerConst.ALARM_EVENT) {
      this.closeEvt(); // 关闭事件窗口
      this.evtContent = comp; // 设置事件内容
      return;
    }
    this.contentWidows[group] = {}; // 初始化分组
    this.contentWidows[group][key] = comp; // 存储组件
    if (this.evtContent) {
      this.evtContent.$el.style.display = 'none'; // 隐藏事件内容
    }
  },
  closeContent(key, group = 'default') {
    // 关闭指定内容窗口
    const comp = this.contentWidows[group]?.[key];
    if (comp) {
      comp.$destroy(); // 销毁组件
      const div = document.getElementById('left-side-window-ins');
      this.contentWidows[group][key] && div.hasChildNodes() && div.removeChild(comp.$el); // 从DOM中移除组件
      delete this.contentWidows[group][key]; // 删除组件引用
    }
    this.showEvt(); // 显示事件窗口
  },
  closeGroupContent(group) {
    // 关闭指定分组的所有内容窗口
    if (this.contentWidows[group] && Object.keys(this.contentWidows[group]).length > 0) {
      Object.keys(this.contentWidows[group]).forEach(key => {
        this.closeContent(key, group); // 逐个关闭内容窗口
      });
    }
  },
  closeByIdPrefix(prefix, group) {
    // 关闭指定前缀的内容窗口
    const groupWindow = this.contentWidows[group];
    groupWindow && Object.keys(groupWindow).map(key => {
      const comp = this.contentWidows[group][key];
      if (comp && key.indexOf(prefix) === 0) {
        comp.$destroy(); // 销毁组件
        const div = document.getElementById('left-side-window-ins');
        this.contentWidows[group][key] && div.hasChildNodes() && div.removeChild(comp.$el); // 从DOM中移除组件
        delete this.contentWidows[group][key]; // 删除组件引用
      }
      return null;
    });
    this.showEvt(); // 显示事件窗口
  },
  showEvt() {
    // 显示事件窗口
    const keys = Object.keys(this.contentWidows);
    const otherWin = keys.find(key => (this.contentWidows[key] && Object.keys(this.contentWidows[key]).length > 0));
    if (!otherWin && this.evtContent) {
      this.evtContent.$el.style.display = 'flex'; // 显示事件内容
    }
  },
  closeAllContent() {
    // 关闭所有内容窗口
    for (const key in this.contentWidows) {
      this.closeGroupContent(key); // 逐个关闭分组内容
    }
    this.closeEvt(); // 关闭事件窗口
  },
  closeEvt() {
    // 关闭事件窗口
    const comp = this.evtContent;
    if (comp) {
      comp.$destroy(); // 销毁组件
      delete this.evtContent; // 删除事件内容引用
      const div = document.getElementById('left-side-window-ins');
      div.hasChildNodes() && div.removeChild(comp.$el); // 从DOM中移除组件
    }
  },
  closeAllContentWindows() {
    // 关闭除了事件以外的所有窗口
    for (const key in this.contentWidows) {
      this.closeGroupContent(key); // 逐个关闭分组内容
    }
  }
};
export default ExclusiveModeHandle; // 导出
