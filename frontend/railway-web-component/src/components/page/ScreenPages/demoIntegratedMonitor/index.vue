<template>
  <div v-loading="sysLoading" class="screen-container">
    <div class="preload" />
    <!-- 
      showMouseLocation 右下角显示当前鼠标经纬度位置，默认不开启 
      showMapControl 初始化完成后，是否在右下角显示地图切换、比例尺工具。默认开启
      tileModes 定义可供切换的地图类型。0、1、2、3 分别代表 常规地图、卫星地图、三维地形图、二维地形图。默认启用全部
    -->
    <!-- chooseMapMemoryKey="integratedMonitorDemo" -->
    <RemoteComponentSyncLoader
      v-if="components['common-comp-map']"
      showMapControl
      mapToolElement=".screen-container .maptooltele"
      :class="['main-map', $store.state.event.evtListOpen && 'fix-map-tool']"
      :config="components['common-comp-map']"
      :mapId="mapId"
      chooseMapMemoryKey
      :defaultTileMode="5"
      :tileModes="tileModes"
    />
    <!-- 切换到3D地形图时使用 -->
    <!-- <div :class="['maptooltele', 'fix-map-tool']" /> -->
    <!-- 内容 -->
    <template v-if="mapComLoaded && loaded && !sysLoading">
      <!-- 左侧窗口包装组件，只有在系统加载完成后才显示 -->
      <WindowWrapLeftSide :mapId="mapId" ref="leftWrap" />
      <!-- 底部窗口包装组件 -->
      <WindowWrapBottomSide ref="bottomWrap" />
      <!-- 地图组件，加载完成后触发 onMapLoad 方法 -->
      <Map :mapId="mapId" :onMapLoad="onMapLoad" />
      <template v-if="mapLoaded">
        <!-- 侧边控制面板组件 -->
        <SideCtlPanel :mapId="mapId" />
        <div
          :class="`right-background  ${
          $store.state.event.evtListOpen ? 'open' : 'close'
        }`"
        >
          <!-- 区域选择组件，区域变化时触发 onRegionChange 方法 -->
          <RegionSelect :mapId="mapId" :onRegionChange="onRegionChange" />
          <!-- 工具箱组件 -->
          <ToolBox :mapId="mapId" />
          <!-- 右侧事件列表容器 -->
          <div class="right-container">
            <AlarmList
              ref="rightAlarm"
              :refreshEvtList="initEventListData"
              :provinceId="provinceId"
              :cityId="cityId"
            />
          </div>
          <!-- 地图控制组件 $store.state.event.evtListOpen && 'fix-map-tool' -->
          <!-- 通过 common-comp-map 组件自动生成 -->
          <div v-if="$store.state.map.mapToolSwitch" :class="['maptooltele', 'fix-map-tool']" />
          <ChangeRailwayType :mapId="mapId" :tileModes="tileModes" />
          <!-- 图例 -->
          <Legend screenType="monitor" />
        </div>
        <!-- 事件推送 -->
        <CaptureInfoWS v-if="$store.state.map.passRouteSwitch" />
        <PassRoutePanel
          v-if="openPassRouteWin"
          :propsComponents="components"
          :tileModes="tileModes"
        />
      </template>
      <CaptureInfoFullScreenWin v-if="openCaptureInfoWin" :curPlayData="curCaptureInfo" />
    </template>
    <!-- 地图工具 configItemMemoryNameMenu="paranomaSettingMenu"
    configItemMemoryNameMap="paranomaSettingMap"-->
    <RemoteComponentSyncLoader
      v-if="components['common-comp-tool-box']"
      :class="['tool-box', !rightHide && 'fix-map-tool']"
      :toolsList="toolsList"
      :overlayOptions="{
        use3DHeight: false
      }"
      :defaultCheckedIds="[4]"
      :config="components['common-comp-tool-box']"
      :mapId="mapId"
      :customLayers="customLayers"
      alarmFilteTeleport=".screen-container"
    />
    <!-- 周边分析 -->
    <RemoteComponentSyncLoader
      v-if="components['common-comp-around-analysis']"
      :class="['aroundanalysis', rightHide && 'hide']"
      :config="components['common-comp-around-analysis']"
      :mapId="mapId"
    />
    <!-- 指点飞行组件 -->
    <RemoteComponentSyncLoader
      v-if="flycomponents['common-comp-guide-flight']"
      :config="flycomponents['common-comp-guide-flight']"
      :mapId="mapId"
      :right="460"
      :top="180"
    />
    <RemoteComponentSyncLoader
      v-if="components['common-comp-track-popup']"
      :config="components['common-comp-track-popup']"
      :mapId="mapId"
    />
    <RemoteComponentSyncLoader
      v-if="components['common-comp-dialog-tool']"
      :config="components['common-comp-dialog-tool']"
      :mapId="mapId"
    />
    <RemoteComponentSyncLoader
      v-if="components['common-comp-layers-tool']"
      :config="components['common-comp-layers-tool']"
      :mapId="mapId"
    />
    <RemoteComponentSyncLoader
      v-if="components['common-comp-uav-check-auth']"
      :config="components['common-comp-uav-check-auth']"
      :mapId="mapId"
    />
    <div class="tree-wrapper">
      <RemoteComponentSyncLoader
        v-if="components['common-comp-uav-tree']"
        :class="['dtreebox']"
        :config="components['common-comp-uav-tree']"
        :mapId="mapId"
      />
      <RemoteComponentSyncLoader
        v-if="components['common-comp-tree-recorder']"
        :class="['dtreebox']"
        :config="components['common-comp-tree-recorder']"
        :mapId="mapId"
      />
    </div>
  </div>
</template>
<script>
import RemoteComponentSyncLoader from '@ct/remote-page-sync-loader/remote-page-sync-loader.umd.js' // 远程组件同步加载器
import { parseQueryString } from '@/common/parse-qs.js' // 解析url参数
import { getInfo, getFisUrl, getBusinessFilesAsync } from '@/utils/index.js' // 获取url参数
import commonService, {
  getComponentLayout,
  initUserProvinceCityData,
} from '@/api/service/common' // 公共服务
import { promiseAll } from '@/api/service/imScreenService' // 地图组件
import { getUserLocalStorage } from '@/components/common/utils'
import { CfgEnum } from '@/components/page/ScreenPages/IntegratedMonitor/enum/CfgEnum' // 配置枚举
import { Events } from '@/components/page/ScreenPages/IntegratedMonitor/enum/EventsEnum.js' // 事件枚举
import AlarmList from './EventList' // 事件列表
import Map from './Map' // 地图
import MapTool from '@/components/page/ScreenPages/IntegratedMonitor/Map/MapTool/index.vue' // 地图工具
import RegionSelect from '@/components/page/ScreenPages/IntegratedMonitor/Map/RegionSelect' // 区域选择
import SideCtlPanel from './SideCtlPanel' // 侧边控制面板
import ToolBox from '@/components/page/ScreenPages/IntegratedMonitor/ToolBox' // 工具箱
import WindowWrapBottomSide from '@/components/page/ScreenPages/IntegratedMonitor/WindowWrapBottomSide' // 底部窗口包装组件
import WindowWrapLeftSide from './WindowWrapLeftSide' // 左侧窗口包装组件
import Legend from '@/components/common/legend.vue' // 图例
import { mapMutations, mapActions } from 'vuex' // vuex
import PassRoutePanel from './PassRoutePanel' // 巡航路线
import CaptureInfoWS from './captureInfoWS.vue' // 事件推送
import CaptureInfoFullScreenWin from '@/components/page/ScreenPages/IntegratedMonitor/InfoWindow/CaptureInfoFullScreenWin.vue' // 事件详情全屏弹窗
import ClearVideo from '@/utils/mixins/clearVideo' // 清除视频
import { getEvtParams } from '@/components/common/utils' // 获取事件参数
import api from '@/api' // 接口
import ss from '@/components/page/ScreenPages/IntegratedMonitor/Map/MapTool/img/2D-dark-map.png'
import cg from '@/components/page/ScreenPages/IntegratedMonitor/Map/MapTool/img/2D-map.jpeg'
import wx from '@/components/page/ScreenPages/IntegratedMonitor/Map/MapTool/img/satellite-map.jpeg'
import ChangeRailwayType from '@/components/common/Map/ChangeRailwayType'
import { getMapTiles } from '@/api/service/common' // 导入获取地图瓦片的服务
import { setMapService } from '@/utils/common'

const { EVT_LIST } = Events // 事件枚举

// 使用到的远程组件定义
// key是组件内部名称，应当与uniqueId指代的具体组件完全对应，用于维护者自己区分和填写在RemoteComponentSyncLoader内
const remoteComp = {
  'common-comp-map': { uniqueId: '2024042465120', version: '1.7.65' },
  'common-comp-tool-box': { uniqueId: '2024042422835', version: '1.7.65' },
  'common-comp-dialog-tool': { uniqueId: '2024082000537', version: '1.7.00' },
  'common-comp-layers-tool': { uniqueId: '2024082008622', version: '1.7.00' },
  'common-comp-around-analysis': {
    uniqueId: '2024042467840',
    version: '1.6.90',
  },
  'common-comp-footer': { uniqueId: '2024042461157', version: '1.6.80' },
  'common-comp-alarm-detail': { uniqueId: '2024042421530', version: '1.7.65' },
  'common-comp-alarm-detail-large': {
    uniqueId: '2024072609683',
    version: '1.7.65',
  },
  'common-comp-tree': { uniqueId: '2024042483471', version: '1.7.20' },
  'common-comp-iot-tree': { uniqueId: '2024042457412', version: '1.6.90' },
  'common-comp-radar-tree': { uniqueId: '2024042467191', version: '1.6.90' },
  'common-comp-horn-tree': { uniqueId: '2024042488249', version: '1.6.90' },
  'common-comp-uav-tree': { uniqueId: '2024042495052', version: '1.7.50' },
  'common-comp-source-tree': { uniqueId: '2024042494149', version: '1.6.90' },
  'common-comp-grid-tree': { uniqueId: '2024042446567', version: '1.6.90' },
  'common-comp-grid-operator-tree': {
    uniqueId: '2024042477521',
    version: '1.6.90',
  },
  'common-comp-track-popup': { uniqueId: '2024042449828', version: '1.7.10' },
  'common-comp-tree-recorder': { uniqueId: '2024052417174', version: '1.7.20' },
  'common-comp-tool-spot': { uniqueId: '2024042413387', version: '1.6.80' },
  'common-comp-tool-space': { uniqueId: '2024042408209', version: '1.6.80' },
  'common-comp-tool-compound': { uniqueId: '2024042436593', version: '1.6.80' },
  'common-comp-tool-complex': { uniqueId: '2024042424703', version: '1.6.80' },
  'common-comp-tool-swiper': { uniqueId: '2024042410687', version: '1.6.70' },
  'common-comp-uav-check-auth': {
    uniqueId: '2025052195256',
    version: '1.7.50',
  },
}
// 指点飞行
const flyComp = {
  'common-comp-guide-flight': {
    uniqueId: '2025012119577',
    version: '1.7.50',
  },
}
// 监听footer设备树出现事件中的对象
// const footerStatus = {}

export default {
  name: 'RemoteComponentLayout', // 组件名称
  inject: ['mapFlag', 'mapRef'], // 注入的属性
  mixins: [ClearVideo], // 混入mixin
  components: {
    RemoteComponentSyncLoader, // 远程组件同步加载器
    RegionSelect, // 区域选择
    MapTool, // 地图工具
    SideCtlPanel, // 侧边控制面板
    ToolBox, // 工具箱
    Map, // 地图
    WindowWrapLeftSide, // 左侧窗口包装组件
    WindowWrapBottomSide, // 底部窗口包装组件
    AlarmList, // 事件列表
    PassRoutePanel, // 巡航路线
    Legend, // 图例
    CaptureInfoWS, // 事件推送
    CaptureInfoFullScreenWin, // 事件详情全屏弹窗
    ChangeRailwayType, // 切换铁路线路
  },
  data() {
    return {
      // 地图瓦片
      tileModes: [
        {
          modeId: 5,
          name: '深色地图',
          mapType: '2D',
          imgUrl: ss,
          tileType: 'vector',
          layerUrls: [
            {
              url: '',
              // url: 'http://10.43.82.110:9239/geoserver/railway_space/gwc/service/wmts?layer=railway_space:railway-project-dark&style=&tilematrixset=WebMercatorQuad&Service=WMTS&Request=GetTile&Version=1.0.0&Format=image/png&TileMatrix={z}&TileCol={x}&TileRow={y}',
              // url: 'http://webst01.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}',
            },
          ],
        },
        {
          modeId: 1,
          name: '卫星地图',
          imgUrl: wx,
          tileType: 'satellite',
          mapType: '2D',
        },
        {
          modeId: 6,
          name: '常规地图',
          imgUrl: cg,
          tileType: 'vector',
          mapType: '2D',
          layerUrls: [
            {
              url: '',
              // url: 'http://10.43.82.110:9239/geoserver/railway_space/gwc/service/wmts?layer=railway_space:railway-project&style=&tilematrixset=WebMercatorQuad&Service=WMTS&Request=GetTile&Version=1.0.0&Format=image/png&TileMatrix={z}&TileCol={x}&TileRow={y}',
            },
          ],
        },
      ],
      rightHide: false, // 右侧隐藏
      // 地图是否加载完成
      mapComLoaded: false,
      mapLoaded: false, // 地图是否加载完成
      mapId: 'mainMap', // 地图id
      // ctlEnter: true,
      provinceId: '', // 110000
      cityId: '', // 110100
      cityName: '',
      //是否展示告警详情
      // showAlarmInfo: false,
      //告警详情id
      // alarmInfoId: '',
      sysLoading: false, // 系统加载状态
      notifyIns: null, // 通知实例
      // 告警信息全屏弹窗相关
      openCaptureInfoWin: false,
      curCaptureInfo: null,
      // 是否打开过车记录弹窗
      openPassRouteWin: false,
      // mapControl: true,
      // userInfo: null,
      // 默认图层 1摄像机和4无人机
      customLayers: [
        {
          id: 4,
          footerBtns: [
            {
              key: 'fxkz', // 飞行控制
            },
            {
              key: 'zdfx', // 指点飞行
            },
            {
              key: 'sssp', // 实时视频
            },
          ],
        },
      ],
      initedFlyComp: false, // 指点飞行
      flycomponents: {}, // 指点飞行配置项
      mapInitFlag: this.mapFlag(), // 初始化函数返回值
      loaded: false,
      components: {}, // 远程组件使用的配置项们
      // naviRightPosition: false, // 监听footer设备树
      // alarmDetailOpen: false, // 告警详情是否打开
      // tableLeft: '', // 地块查询弹窗左偏移
      // 远程组件使用的配置项们
      filterState: '', // 工具箱事件筛选的状态
      // 工具箱开启的功能列表
      toolsList: [
        {
          key: 'mapControl',
        },
        {
          key: 'smallTools',
        },
        {
          key: 'lookHere',
        },
        {
          key: 'layersControl',
        },
      ],
      // evtDataShow: false, // 事件加载
    }
  },
  computed: {
    // 获取当前事件的数据
    curAddEventData() {
      return this.$store.state.mockDataStore.curAddEventData
    },
    // 获取巡航路线开关
    passRouteSwitch() {
      return this.$store.state.map.passRouteSwitch
    },
    // 获取区域筛选状态
    selectedRegion() {
      return this.$store.state.map.selectedRegion //需要监听的属性
    },
  },
  watch: {
    // 地图加载后的处理函数。如果地图加载完成，则执行初始化操作
    mapInitFlag: {
      handler(val) {
        // 这里要判断 this.mapComLoaded ，不然整个页面会重新加载
        if (val && !this.mapComLoaded) {
          this.setMapStoreRef(this.mapRef.getMapRef(this.mapId))
          this.mapComLoaded = true
          // 指点飞行
          if (!this.initedFlyComp) {
            this.getFlyComp()
          }
          // 初始化页面上悬浮面板方法
          this.initcom()
        }
      },
      immediate: true,
    },
    // 监听 获取当前告警的事件序号的数据
    curAddEventData: {
      handler: function (newVal, oldVal) {
        if (!oldVal || newVal.id !== oldVal.id) {
          // 弹窗提示有新事件
          this.openNewEventNotify(newVal)
        }
      },
      deep: true,
    },
    // 监听区域筛选状态
    selectedRegion: {
      handler: function (newVal, oldVal) {
        if (newVal) {
          this.initEventListData()
        }
      },
    },
  },
  created() {
    // 监听基本类型（通过函数）
    this.$watch(
      () => this.mapFlag(), // 监听函数返回值
      newVal => {
        // 初始化地图
        this.mapInitFlag = newVal
      }
    )

    // 解析url参数，获取viewId
    const qsObj = parseQueryString(window.location.search)
    sessionStorage.setItem('VIEW_ID', qsObj.viewId) // '1001693'

    // 视频初期偏移量
    window._remoteMetadata = window._remoteMetadata || {}
    const videoPositionRight = Math.ceil((window.innerHeight / 1032) * 100 * 5)
    window._remoteMetadata.videoPositionRight = videoPositionRight
  },
  async mounted() {
    try {
      // 导入获取地图瓦片的服务
      await getMapTiles()
      // 设置地图瓦片图层
      this.tileModes = setMapService(this.tileModes)
    } catch {
      console.log('地图瓦片服务加载失败')
    }
    // 获取用户信息
    await getInfo()
    // const _info = await getInfo()
    // this.userInfo = _info?.user || {}
    // 获取配置项
    const { data } = await getFisUrl()
    const { configValue } = data || {}
    console.log('当前配置远程域名为---：', configValue)
    // 获取远程组件
    const { components, loaded } = await this.getRemoteComps(
      configValue,
      getBusinessFilesAsync
    )
    // 赋值
    this.components = components
    this.loaded = loaded

    // 执行下面的操作
    this.changeTheme()
    this.afterRemoteLoad()
  },
  methods: {
    // 地图相关
    ...mapMutations('map', [
      'setSelectionRange',
      'setPassRouteSwitch',
      'setMapStoreRef',
    ]),
    // 事件相关
    ...mapMutations('event', [
      'setEvtListOpen',
      'setEvtTypeList',
      'setEvtSourceList',
      'setEvtOrderStatusList',
      'setEvtListFilterState',
    ]),
    ...mapMutations('mockDataStore', ['setAllEventData']), // 设置模拟所有事件数据
    ...mapMutations('captureInfo', ['setCurSelectCamera']),
    ...mapActions('event', [
      'fetchEventSourceData',
      'fetchEventData',
      'fetchOrderStatusData',
    ]),
    /**
     * 初始化页面上悬浮面板组件需要的方法
     */
    async initcom() {
      // 事件类型
      this.fetchEventData()
      // 事件来源
      this.fetchEventSourceData()
      // 查询处置状态
      this.fetchOrderStatusData()
      // 初始化系统配置
      await this.iniSysConfig()
      this.$nextTick(() => {
        window.leftWrap = this.$refs.leftWrap
        window.bottomWrap = this.$refs.bottomWrap
      })
    },
    /**
     * 初始化事件列表数据
     */
    async initEventListData() {
      // this.evtDataShow = true
      const param = {
        pageNum: 1,
        pageSize: 999,
        // isOpen: 'Y',
        isRequest: true,
        // “是否重点关注”：Y-是，N-否,
        // 默认Y
        isFocus: 'Y',
        // 默认时间
        startDate: '2025-07-14 00:00:00',
        endDate: '2025-08-15 23:59:59',
        fusionStatusList: undefined, // 查所有 // ['1', '2'], // 未消散
        // 默认区域值
        // provinceId: '',
        // cityId: '',
        // countyId: '140122', // 阳曲县
        ...getEvtParams(this.filterState),
      }
      // 接口
      const resData = await api.post(
        `${this.$env.VUE_APP_REQ_PREFIX_BIZ}/fusionEvent/page`,
        param
      )
      // this.evtDataShow = false

      // 测试数据
      const allEventData = resData?.data?.list || []
      this.setEvtListFilterState(this.filterState)
      // 需要处理下经纬度字段
      this.setAllEventData(
        allEventData.map(v => ({
          ...v,
          latitude: v.startLat,
          longitude: v.startLng,
        }))
      )
      // 初始化事件的mock数据
      this.$store.dispatch('mockDataStore/initializeCurLoadedEventData')
      // 开始定时刷新任务
      this.$store.dispatch('mockDataStore/startLoadEventData')
    },
    /**
     * 打开新事件提示框
     */
    openNewEventNotify(newEvent) {
      // 获取当前的数据
      const { title, eventAddress } = newEvent
      console.log('newEvent--', newEvent)
      // 通知框
      this.notifyIns = this.$notify({
        dangerouslyUseHTMLString: true,
        customClass: 'new-event-notify-wrap',
        message: `<div class="content-wrap">
                <div class="bell-icon"></div>
                <div class="text-info">
                    <div class="line1">${title}</div>
                    <div class="line2">${eventAddress}</div>
                </div>
                <div class="status">待研判</div>
            </div>`,
        onClick: () => {
          // 触发事件的点击事件
          this.$EventBus.$emit(EVT_LIST.ON_CLICK, newEvent)
          this.notifyIns && this.notifyIns.close()
        },
        onClose: () => {
          this.notifyIns = null
        },
      })
      // 判断左侧是否打开了任何东西，如果没有，那么触发新事件的点击事件
      console.log('告警通知提示--', window.leftWrap)
      const leftElement = document.getElementById('left-side-window-ins')
      let openDetail = false
      // 过滤出元素节点
      const eleChildNodes = []
      for (const node of leftElement.childNodes) {
        // 判断节点类型：1 为元素节点
        if (node.nodeType === 1) {
          eleChildNodes.push(node)
        }
      }
      // 如果没有子元素，或者子元素长度为1并且子元素是事件详情，并且这个详情是自动打开的isAutoOpen='true'
      if (eleChildNodes.length === 0) {
        openDetail = true
      }
      if (eleChildNodes.length === 1) {
        const childNode = eleChildNodes[0]
        // 判断子标签上是否有自定义属性data-custom-open-mode的值
        const isAutoOpen = childNode.getAttribute('data-custom-open-mode')
        if (isAutoOpen === 'true') {
          openDetail = true
        }
      }
      // 过车信息是否打开
      const passRouteSwitch = this.$store.state.map.passRouteSwitch
      if (openDetail && !passRouteSwitch) {
        // 触发事件的点击事件
        this.$EventBus.$emit(EVT_LIST.ON_CLICK, {
          ...newEvent,
          // 右上角提示时，打开事件详情时，节点上添加一个标记
          isAutoOpen: true,
        })
      }
    },
    /**
     * 事件筛选过滤条件回调处理
     * @param {*} e
     */
    onEventFilterChange(e) {
      this.filterState = e.filterState || ''
      this.initEventListData()
    },
    afterRemoteLoad() {
      // 监听footer设备树出现事件，以此为契机处理导航弹窗的偏移量
      // this.$globalEventBus.$on(`common-comp-footer__click`, options => {
      //   const { currentSelectData } = options
      //   const { id, isChecked } = currentSelectData
      //   footerStatus[id] = !!isChecked
      //   // isChecked表示启用还是关闭
      //   this.naviRightPosition = Object.values(footerStatus).find(o => o)
      // })

      // 监听告警详情是否打开，以此为契机处理导航弹窗的偏移量
      // this.$globalEventBus.$on(
      //   `common-comp-alarm-detail__alarm-detail-open-status`,
      //   ({ opened }) => {
      //     this.alarmDetailOpen = opened
      //   }
      // )

      // 切换地图模式 onMapTypeChange
      // this.$globalEventBus.$on('common-comp-map__init-map-resolve', e => {
      //   console.log('切换地图模式', e)
      //   // if (e.status) {
      //   //   this.mapComLoaded = false
      //   // }
      // })

      // 事件筛选过滤条件回调处理
      this.$EventBus.$on('onEventFilterChange', this.onEventFilterChange)

      // 地图控制按钮选中状态改变
      // this.$globalEventBus.$on('common-comp-tool-box__map-control-show', e => {
      //   this.mapControl = e.status
      // })
      // 过车记录
      this.$EventBus.$on(
        Events.PassRouteEvt.CHANGE_PASS_ROUTE_OPEN_STATUS,
        this.changePassRouteWin
      )
      // 首页事件推送
      this.$EventBus.$on(
        Events.PassRouteEvt.CHANGE_CAPTURE_FULL_SCREEN_OPEN_STATUS,
        this.changeCaptureInfoWin
      )
    },
    /**
     * 地图加载完成后执行的初始化操作
     */
    async onMapLoad() {
      // 初始化用户省市数据
      await this.initUserProvinceCityData()
      this.mapLoaded = true
    },
    /**
     * 过车记录
     * @param {*} param0
     */
    changePassRouteWin({ open, deviceInfo = null }) {
      this.setCurSelectCamera(deviceInfo)
      this.openPassRouteWin = open
    },
    /**
     * 首页事件推送
     * @param {*} param0
     */
    changeCaptureInfoWin({ open, captureInfo = null }) {
      this.curCaptureInfo = captureInfo
      this.openCaptureInfoWin = open
    },
    /**
     * 首页事件推送
     * @returns {void}
     */
    async initUserProvinceCityData() {
      // 查询用户权限城市
      const [success, data] = await initUserProvinceCityData()
      console.log(
        'performance initUserProvinceCityData result',
        new Date().getTime()
      )
      if (!success) {
        this.$message.error('查询用户权限城市失败！')
        return
      }
      // 从本地存储中获取用户特定数据
      const userHandleCache = getUserLocalStorage(
        CfgEnum.STORAGE_KEY.SELECT_REGION
      )
      // 获取区域数据
      let { regionCode, regionLevel } = data
      // 用户区域数据
      if (userHandleCache) {
        const {
          nodes,
          cityName,
          regionLevel: cacheRegionLevel,
          regionCode: cacheRegionCode,
        } = userHandleCache
        window.userInfo.defArea = nodes
        window.userInfo.defAreaName = cityName
        regionCode = cacheRegionCode
        regionLevel = cacheRegionLevel
      }
      // 设置
      this.setSelectionRange({
        regionCode,
        regionLevel,
      })
    },
    /**
     * 初始化系统配置和布局配置
     * @returns {void}
     */
    async iniSysConfig() {
      this.sysLoading = true
      const res = await promiseAll([
        await commonService.getDictListByCatCode(CfgEnum.SYS_CFG.Key),
        await getComponentLayout(CfgEnum.MODEL_CODE),
      ])
      if (res.length < 2) {
        console.error('!!!!获取系统配置失败')
        return
      }
      const { code: cfgCode, data: cfgData } = res[0]
      if (cfgCode !== 200 || !cfgData || cfgData.length < 1) {
        console.error('!!!!获取系统配置失败', CfgEnum.SYS_CFG.Key)
        return
      }
      // 测试数据
      // {
      //   dicValue:
      //     '{"EVENT_RES_RADIUS":5000,"IS_SHOW_CAPTURE_TIME":[1,5],"CAPTURE_IMG_SHOW_TIME":3,"CAPTURE_VIDEO_SHOW_TIME":10,"MAP_CAPTURE_INFO_SHOW_TIME":10,"HUS_LAYER_UPDATE":5,"HUS_HIS_TRA":[1,168]}',
      // }
      const { dicValue } = cfgData[0]
      // detailMode 需要一个默认值，用于打开地图点位详情时
      window.sysConfig = { detailMode: 'exclusive', ...JSON.parse(dicValue) }
      const [success, layoutCfg] = res[1]
      if (!success || !layoutCfg || !layoutCfg.layoutJsonObj) {
        console.error('!!!!获取布局配置失败，启动默认配置')
        window.sysConfig.detailMode = 'exclusive'
      } else {
        Object.keys(layoutCfg.layoutJsonObj).forEach(key => {
          layoutCfg.layoutJsonObj[key].map(item => {
            // exclusive: 互斥模式，只显示一个详情,tab 页签模式，多个详情面板，pending 模式，多个详情面板
            if (item.compCode === 'COMMON_LAYER_DETAIL') {
              window.sysConfig.detailMode = item.param.showMode || 'exclusive'
            }
          })
        })
      }
      this.sysLoading = false
    },
    /**
     * 区域选择变化时的处理函数
     * @param {Object} region 选中的区域对象
     */
    onRegionChange(region) {
      this.setSelectionRange(region)
    },
    /**
     * 指点飞行组件初始化
     * 获取配置
     */
    async getFlyComp() {
      const { data } = await getFisUrl()
      const { configValue } = data || {}
      const param = {
        components: Object.values(flyComp),
      }
      const { components, loaded } = await getBusinessFilesAsync(
        configValue,
        param
      )
      if (loaded) {
        this.flycomponents = components
        this.initedFlyComp = true
      }
    },
    /**
     * 原页面右侧面板显隐时的操作
     * 视频初期偏移量修改
     */
    onRightClick() {
      this.rightHide = !this.rightHide

      // 视频初期偏移量修改
      window._remoteMetadata = window._remoteMetadata || {}
      const alarmListWidth = !this.rightHide ? 5 : 1.1
      const videoPositionRight = Math.ceil(
        (window.innerHeight / 1032) * 100 * alarmListWidth
      )
      window._remoteMetadata.videoPositionRight = videoPositionRight
      this.$nextTick(() => {
        this.$globalEventBus.$emit(`screenview__updateVideoOffset`)
      })
    },
    /**
     * 获取远程组件
     * 配置项
     */
    async getRemoteComps(configValue, callback) {
      const param = {
        components: Object.values(remoteComp),
      }
      const { components, loaded } = await callback(configValue, param)
      return { components, loaded }
    },
    /**
     * 切换主题
     * 行业主题
     */
    changeTheme() {
      document.documentElement.setAttribute('data-theme', `theme-wiseblue`)
      this.$globalEventBus.$emit('data-theme', 'theme-wiseblue')
      console.log('通用', 2)
    },
  },
  beforeDestroy() {
    this.$EventBus.$off('onEventFilterChange', this.onEventFilterChange)
    this.$globalEventBus.$off('common-comp-tool-box__map-control-show')
    this.$EventBus.$off(
      Events.PassRouteEvt.CHANGE_PASS_ROUTE_OPEN_STATUS,
      this.changePassRouteWin
    )
    this.$EventBus.$off(
      Events.PassRouteEvt.CHANGE_CAPTURE_FULL_SCREEN_OPEN_STATUS,
      this.changeCaptureInfoWin
    )
  },
}
</script>
<style lang="scss" scoped>
@import '~@/components/page/ScreenPages/IntegratedMonitor/index.scss';
.right-container {
  overflow: visible !important;
}
</style>
<style lang="less" >
// 右上角提示
.new-event-notify-wrap {
  width: 360px;
  height: 80px;
  background: url(./img/bg_notify_360.gif) no-repeat 100% 100%;
  background-size: 100% 100%;
  border: unset;
  overflow: unset;
  padding: 18px 24px;
  .el-notification__group {
    margin: 0;
    width: 100%;
    .el-notification__closeBtn {
      top: 0px;
      right: 0px;
    }
    .el-icon-close {
      &::before {
        position: absolute;
        content: '';
        width: 18px;
        height: 18px;
        top: -9px;
        right: -9px;
        background: url(./img/icon_close_18.svg) no-repeat 100% 100%;
        background-size: 100% 100%;
        cursor: pointer;
      }
    }
    .el-notification__content {
      height: 100%;
      width: 100%;
      overflow: hidden;
      margin: 0;
      .content-wrap {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        cursor: pointer;
        color: #fff;
        @keyframes blink {
          0%,
          100% {
            opacity: 1; /* 完全不透明 */
          }
          50% {
            opacity: 0; /* 完全透明 */
          }
        }
        .bell-icon {
          width: 24px;
          height: 28px;
          background: url(./img/icon_bell_24.svg) no-repeat 100% 100%;
          background-size: 100% 100%;
          animation: blink 2s infinite;
          margin-right: 24px;
        }
        .text-info {
          width: calc(100% - 24px - 24px - 60px - 24px);
          height: 44px;
          .line1 {
            width: 100%;
            height: 20px;
            line-height: 20px;
            font-family: PingFangSC-Medium;
            font-size: 14px;
            color: #e8f3ff;
            letter-spacing: 0;
            font-weight: 500;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .line2 {
            width: 100%;
            height: 18px;
            line-height: 18px;
            margin-top: 6px;
            font-family: PingFangSC-Regular;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
            letter-spacing: 0;
            font-weight: 400;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
        .status {
          margin-left: 24px;
          width: 60px;
          height: 24px;
          line-height: 24px;
          text-align: center;
          background-image: linear-gradient(90deg, #519eff 8%, #1273e5 100%);
          border-radius: 4px;
          font-family: PingFangSC-Regular;
          font-size: 12px;
          color: #e8f3ff;
          letter-spacing: 0;
          font-weight: 400;
        }
      }
    }
  }
}
</style>
