<!--
 * @Description  : 告警事件
 * <AUTHOR> wnj
 * @Date         : 2023-12-08 09:52:58
 * @LastEditors: liu.yongli
 * @LastEditTime: 2025-02-26 11:02:11
 * @FilePath     :  / src / components / page / alarmEvent / components / alarmRight / alarmList.vue
-->
<template>
  <div :class="`alarm-list-outter`">
    <i
      :class="`btn-handle ${$store.state.event.evtListOpen ? 'open' : 'close'}`"
      @click="extendHandle"
    />
    <div class="topContent">
      <div class="titleWrap">
        <div class="title">
          <div class="titlName">今日重点关注事件</div>
          <div class="unit">（起）</div>
        </div>
      </div>
      <div class="eventIndex">
        <div class="eventIndexItem todayItem">
          <img
            alt="今日新增"
            class="indexIcon"
            :src="require(`@/assets/images/alarmEvent/alarm/todayAdd_event.svg`)"
          />
          <span class="indexKey">新增</span>
          <FlipCounter class="indexValue" :value="eventCount.today" />
        </div>
        <div class="eventIndexItem pendingItem">
          <img
            alt="未消散"
            class="indexIcon"
            :src="require(`@/assets/images/alarmEvent/alarm/icon_未消散_14_n.svg`)"
          />
          <span class="indexKey">未消散</span>
          <FlipCounter class="indexValue" :value="eventCount.pending" />
        </div>
        <div class="eventIndexItem finishedItem">
          <img
            alt="已消散"
            class="indexIcon"
            :src="require(`@/assets/images/alarmEvent/alarm/icon_已消散_14_n.svg`)"
          />
          <span class="indexKey">已消散</span>
          <FlipCounter class="indexValue" :value="eventCount.finished" />
        </div>
      </div>
    </div>
    <div class="rt-alarm-list" v-loading="rightTableLoading">
      <div class="list-tab">
        <div
          class="tab-item"
          :class="{ 'tab-item-click': tabIndex === '1' }"
          @click="handleChangeTab('1')"
        >
          <span>
            重点关注事件
            <i class="total-num">{{ keyAlarmListTotal }}</i>
          </span>
        </div>
        <div
          class="tab-item"
          :class="{ 'tab-item-click': tabIndex === '2' }"
          @click="handleChangeTab('2')"
        >
          <span>我的收藏</span>
        </div>
      </div>
      <div class="list-table">
        <div class="table-body" v-if="showPagination">
          <div v-for="item in rightTableParam.tableDatas" :key="item.id">
            <EventListItem
              :item="item"
              :power="power"
              :orderId="alarmInfoId"
              @alarmInfoFn="getAlarmInfo"
              @mouseoverFile="mouseoverFile"
              :mouseoutFile="touchClose"
              :flowLinkCache="flowLinkCache"
            />
          </div>
        </div>
        <el-empty v-show="!showPagination" :image-size="100" description="暂无数据"></el-empty>
      </div>
      <!--   底部分页   -->
      <div class="list-pagination" v-if="showPagination">
        <div
          class="page-number"
          :title="'第' +  rightQueryParam.pageNum + '页/共' + rightTableParam.total +'条'"
        >第{{ rightQueryParam.pageNum }}页/共{{ rightTableParam.total }}条</div>
        <!-- 展示模块表格分页-->
        <el-pagination
          @current-change="rightHandleCurrentChange"
          :current-page="rightQueryParam.pageNum"
          :page-size="rightQueryParam.pageSize"
          layout="prev, pager, next"
          small
          :total="rightTableParam.total"
          :pager-count="5"
        ></el-pagination>
        <i class="alarm-icon el-icon-refresh" title="刷新" @click="refreshList"></i>
        <!-- 暂时屏蔽 -->
        <!-- <img
          alt="更多"
          class="alarm-icon"
          :src="require(`@/assets/images/alarmEvent/alarm/xq_icon.svg`)"
          title="更多"
          @click="goEventManage"
        />-->
      </div>
    </div>
    <!--  放大查看 非全屏  -->
    <div class="alarm-max-file-box" v-if="showBigDialog">
      <img alt="放大" :src="bigImgUrl" fit="contain" class="carousel-img-big" />
    </div>
  </div>
</template>

<script>
import api from '@/api'
import { queryFlowLink } from '@/api/service/imScreenService'
import { Events } from '@/components/page/ScreenPages/IntegratedMonitor/enum/EventsEnum'
import EventDetailInfo from '@/components/page/ScreenPages/demoIntegratedMonitor/EventDetailInfo'
import LayerConst from '@/components/page/ScreenPages/IntegratedMonitor/Map/LayerConst'
import { postMsgUtil } from '@ct/iframe-connect-sdk'
import $ from 'jquery'
import dayjs from 'dayjs'
import { mapGetters, mapMutations } from 'vuex'
import EventListItem from '@/components/page/ScreenPages/IntegratedMonitor/EventList/EventListItem/index.vue'
import FlipCounter from '@/components/common/FlipCounter/index.vue'
import { getEvtParams } from '@/components/common/utils'
import { alarmStatusListDataDefault } from '@/components/page/ScreenPages/IntegratedMonitor/EventFilter/typeDatas'

const { EVT_LIST } = Events

export default {
  name: 'AlarmList',
  inject: ['mapRef'],
  props: {
    provinceId: {
      // 省份编码
      type: String,
      default: '110000',
    },
    cityId: {
      // 地市编码
      type: String,
      default: '',
    },
    refreshEvtList: {
      type: Function,
    },
  },
  components: {
    EventListItem,
    FlipCounter,
  },
  computed: {
    dataParam() {
      // 使用计算属性承载参数。省市区编码任意一个更新才处理
      const { provinceId, cityId } = this
      return {
        provinceId,
        cityId,
      }
    },
    selectedRegion() {
      return this.$store.state.map.selectedRegion //需要监听的属性
    },
    curLoadedEventData() {
      return this.$store.state.mockDataStore.curLoadedEventData
    },
    curCollectEventData() {
      return this.$store.state.mockDataStore.curCollectEventData
    },
    getFusionStatus() {
      return key => {
        const obj = alarmStatusListDataDefault.find(v =>
          v.dictValue.includes(key)
        )
        return obj || {}
      }
    },
    ...mapGetters('map', ['formatSelectedRegion']),
  },
  watch: {
    dataParam: {
      handler(val) {
        // 没有就不处理
        if (val?.provinceId || val?.cityId) {
          this.alarmInfoId = '' // 重置选中详情卡片
          this.refreshList(true)
        }
      },
      immediate: true,
    },
    selectedRegion: {
      handler: function (newVal, oldVal) {
        if (newVal) {
          this.alarmInfoId = '' // 重置选中详情卡片
          this.refreshList(true)
          leftWrap?.closeAllContent?.()
        }
      },
    },
    /**
     * 监听当前加载的事件数据变化
     */
    curLoadedEventData: {
      handler: function (newVal, oldVal) {
        if (newVal) {
          // 如果当前在收藏页面，不刷新
          if (this.tabIndex !== '2') {
            this.getFocusList()
          }
          // 重新计算统计结果
          this.getAlarmCount(newVal)
        }
      },
      deep: true,
      immediate: true,
    },
  },
  data: function () {
    return {
      filterState: '', // 工具箱事件筛选的状态
      eventCount: {
        today: 0,
        pending: 0,
        finished: 0,
      },
      flowLinkCache: {},
      alarmInfoId: '', //告警详情id
      showBigDialog: false, //是否展示大窗口
      bigImgUrl: '', //放大非全屏的图片地址
      tabIndex: '1',
      rightQueryParam: {
        //告警列表入参
        pageNum: 1, //页码 Y Int
        pageSize: 10, //每页显示数量
        // dayNum: "1", //查询天数
        warningTypeId: null, //告警类型
        orderStatus: null, //告警状态
        deviceCodeList: [], //摄像机列表
        warningSourceList: [], //告警来源列表
        warningTypeIdList: [], //告警类型列表
        orderStatusList: [], //告警状态列表
        alarmDate: [], //告警时间
        isCollection: '',
      },
      keywordValue: '', //搜索关键字
      clickedItem: null,
      rightTableLoading: false, //右侧告警是否加载中
      rightTableParam: {
        //表格展示参数
        tableDatas: [], // 表格显示数据
        total: 0, //数据总数
        isMap: false, //点地图告警查询的
      },
      chooseAlarmOrderId: -1, //选中的告警列表 id // 原接口orderId
      detailObj: {}, //告警详情数据
      dialogVisible: false, //详情状态
      showPagination: false, //是否右侧告警显示页码
      defaultAlarmTime: [], //默认七天
      keyAlarmListTotal: 0, // 重点关注事件总数
      power: null, //"event:alarm:judge;event:alarm:sch;event:alarm:dis;event:alarm:ver",//用户按钮权限
      // timer: null, // 监听新事件的定时器
    }
  },
  created() {
    // this.getDefaultTime()
    //初始化监听页面点击click事件 用于点击空白区域关闭放大图片
    $('body').on('click', event => {
      if (!$(event.target).closest('.alarm-max-file-box').length) {
        // 在这里编写操作代码
        this.touchClose()
      }
    })
  },
  mounted() {
    // this.getAlarmCount()
    // this.getRightCollectList ()
    this.$EventBus.$on('refreshAlarmList', this.refreshList)
    this.$EventBus.$on(EVT_LIST.ON_CLICK, this.getAlarmInfo)
    this.$EventBus.$on(EVT_LIST.ON_DETAIL_CLOSE, this.closeInfo)
    // this.$EventBus.$on('onEventFilterChange', this.onEventFilterChange)

    // 监听新事件提示
    // this.timer = setInterval(() => {
    //   try {
    //     const targetNodeNew =
    //       window.parent.document.querySelector('.c-socket-message')
    //     if (targetNodeNew) {
    //       this.refreshList()
    //     }
    //   } catch (e) {}
    // }, 4000)
  },
  beforeDestroy() {
    $('body').unbind('click', this.touchClose)
    this.$EventBus.$off('refreshAlarmList', this.refreshList)
    this.$EventBus.$off(EVT_LIST.ON_CLICK, this.getAlarmInfo)
    this.$EventBus.$off(EVT_LIST.ON_DETAIL_CLOSE, this.closeInfo)
    // this.$EventBus.$off('onEventFilterChange', this.onEventFilterChange)
    // this.timer && clearInterval(this.timer)
  },
  methods: {
    // 使用mapMutations将store中的mutation映射到组件方法中
    ...mapMutations('event', [
      'setEvtListOpen', // 设置事件列表打开状态的mutation
    ]),
    ...mapMutations('map', [
      'setPassRouteSwitch', // 设置导航路线切换状态的mutation
    ]),
    /**
     * 事件筛选过滤条件回调处理
     */
    onEventFilterChange(e) {
      this.filterState = e.filterState || ''
      this.getAlarmCount()
      this.rightQueryParam = {
        ...this.rightQueryParam,
        pageNum: 1,
      }
      this.getRightCollectList()
    },
    /**
     * 扩展操作处理
     * @returns {void}
     */
    extendHandle() {
      // 切换事件列表的打开状态
      const isOpen = !this.$store.state.event.evtListOpen
      this.setEvtListOpen(isOpen)
      // 过车信息切换的开关不要关联了右侧展示
      // 如果打开事件列表，则关闭导航路线切换
      // if (isOpen) {
      //   this.setPassRouteSwitch(false)
      // }
    },
    /**
     * 点击空白区域关闭大图显示
     * @returns {void}
     */
    touchClose() {
      this.showBigDialog = false
    },
    /**
     * 鼠标移入文件时显示大图
     * @param {string} data - 图片文件路径
     * @returns {void}
     */
    mouseoverFile(data) {
      this.bigImgUrl = data
      this.showBigDialog = true
    },
    /**
     * 切换Tab选项
     * @param {string} tabIndex - 当前选项索引
     * @returns {void}
     */
    handleChangeTab(tabIndex) {
      this.tabIndex = tabIndex
      this.refreshList(false)
    },
    /**
     * 获取默认的告警查询时间范围
     * @returns {void}
     */
    getDefaultTime() {
      this.defaultAlarmTime = [
        dayjs()
          .subtract(7, 'days')
          .startOf('days')
          .format('YYYY-MM-DD HH:mm:ss'),
        dayjs().endOf('days').format('YYYY-MM-DD HH:mm:ss'),
      ]
    },
    /**
     * 加载右侧收藏列表
     * @returns {void}
     */
    getRightCollectList() {
      if (this.tabIndex === '2') {
        this.getCollectList()
      }
    },
    /**
     * 更新重点关注列表
     * @returns {void}
     */
    getRightListFcuse() {
      if (this.tabIndex === '1') {
        // 更新重点关注列表
        this.refreshEvtList()
      }
    },
    /**
     * 获取告警数量,使用接口
     * @returns {void}
     */
    async getAlarmCount2() {
      const url = `${this.$env.VUE_APP_REQ_PREFIX_BIZ}/fusionEvent/count`
      const params = {
        // ...getEvtParams(this.filterState),
        // .subtract(1, 'day')
        //   .startOf('days')
        // 指定默认时间
        startDate: dayjs().format('YYYY-MM-DD 00:00:00'),
        endDate: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        isFocus: 'Y',
      }
      // 今日新增
      const res1 = await api.post(url, {
        ...params,
      })
      // 未消散
      const res2 = await api.post(url, {
        ...params,
        fusionStatusList: ['1', '2'],
      })
      // 已消散
      const res3 = await api.post(url, {
        ...params,
        fusionStatusList: ['3'],
      })
      console.log('统计--', res1, res2, res3)
      this.eventCount = {
        today: res1.data || 0,
        pending: res2.data || 0,
        finished: res3.data || 0,
      }
    },
    /**
     * 查询重点关注列表
     * @returns {void}
     */
    async getFocusList() {
      this.showPagination = true
      this.rightTableLoading = true
      // store 数据
      const data = [...this.curLoadedEventData]
      // 加载mock数据
      this.rightTableParam.total = data.length
      this.keyAlarmListTotal = data.length
      // 前台分页
      const pageNum = this.rightQueryParam.pageNum
      const pageSize = this.rightQueryParam.pageSize
      const rows = data.slice((pageNum - 1) * pageSize, pageNum * pageSize)
      this.rightTableParam.tableDatas = rows.map(v => ({
        ...v,
        longitude: v.startLng,
        latitude: v.startLat,
      }))
      this.showPagination = data.length !== 0
      setTimeout(() => {
        // 出现 loading 效果
        this.rightTableLoading = false
      }, 500)

      let flows = rows.map(item => ({
        flowId: item.flowId,
        templateId: item.flowTemplate,
      }))
      // 使用lodash去重
      flows = _.uniqBy(flows, item => item.flowId + item.templateId)
      // 异步获取链路
      this.getLinkIdEnum(flows)
    },
    /**
     * 获取告警数量
     * @returns {void}
     */
    getAlarmCount(eventData = this.curLoadedEventData) {
      const pendingCount = eventData.filter(
        i => this.getFusionStatus(i.fusionStatus).dictLabel === '未消散'
      ).length
      const finishedCount = eventData.filter(
        i => this.getFusionStatus(i.fusionStatus).dictLabel !== '未消散'
      ).length
      // 重新计算统计结果
      this.eventCount = {
        today: eventData.length,
        pending: pendingCount,
        finished: finishedCount,
      }
    },
    /**
     * 获取收藏事件列表
     * @returns {void}
     */
    async getCollectList() {
      // 加载
      this.showPagination = true
      this.rightTableLoading = true
      let param = {
        pageNum: this.rightQueryParam?.pageNum || 1,
        pageSize: this.rightQueryParam?.pageSize || 10,
        ...getEvtParams(this.filterState),
      }
      // 是否收藏列表
      // 原重点关注事件列表接口参数
      const resData = await api.post(
        `${this.$env.VUE_APP_REQ_PREFIX_BIZ}/fusionEvent/pageFavorite`,
        param
      )
      // 测试数据
      const res = resData?.data ? resData : { data: { list: [] } }

      if (res.data) {
        this.rightTableParam.total = res.data.total
        if (this.tabIndex === '1') {
          this.keyAlarmListTotal = res.data.total
        }
        this.rightTableParam.tableDatas = res.data?.list?.map(v => ({
          ...v,
          longitude: v.startLng,
          latitude: v.startLat,
        }))
        this.showPagination = res.data.total !== 0
        this.rightTableLoading = false
        let flows = res.data.list.map(item => ({
          flowId: item.flowId,
          templateId: item.flowTemplate,
        }))
        // 使用lodash去重
        flows = _.uniqBy(flows, item => item.flowId + item.templateId)
        // 异步获取链路
        this.getLinkIdEnum(flows)
      } else {
        this.showPagination = false
        this.rightTableLoading = false
      }
    },
    /**
     * 右侧告警列表页码改变时的处理
     * @param {number} num - 新的页码
     * @returns {void}
     */
    rightHandleCurrentChange(num) {
      this.rightQueryParam.pageNum = num
      if (this.tabIndex === '1') this.getFocusList()
      else this.getRightCollectList()
    },
    /**
     * 跳转到事件管理页面
     * @returns {void}
     */
    goEventManage() {
      postMsgUtil.trigger(null, 'redirectTo', {
        isOpener: true,
        url: '/eventManagement',
      })
    },
    /**
     * 打开告警详情
     * @param {object} item - 告警项数据
     * @returns {void}
     */
    getAlarmInfo(item) {
      if (item) {
        const { longitude, latitude } = item
        // 如果是当前正在查看的告警，则关闭详情
        if (this.alarmInfoId === item.id) {
          this.closeInfo({ resetLayer: true })
          return
        }
        // 处理告警详情回调
        this.alarmInfoCallbackFn(item)
        // 移动地图到告警位置
        this.mapRef
          .getMapRef('mainMap')
          .mapInstance.mapToCenter([Number(longitude), Number(latitude)])
        this.$EventBus.$emit(Events.EVT_LIST.EVENT_AROUND, item)
      } else {
        this.$message({
          type: 'error',
          message: '告警id不存在!',
        })
      }
    },
    /**
     * 处理告警信息回调函数。
     * 当存在告警信息ID时，先关闭当前告警信息窗口；然后在左侧面板添加告警事件详情组件，
     * 并传递订单ID、详细信息、可关闭性及标记类型；最后更新告警信息ID为当前订单ID。
     * @param {Object} item - 告警事件项，包含订单ID等信息。
     */
    alarmInfoCallbackFn(item) {
      // 如果已存在告警信息ID，则先关闭当前告警信息
      if (this.alarmInfoId) {
        this.closeInfo({ resetLayer: false })
      }
      // 在下一个tick后执行，确保DOM更新后进行后续操作
      this.$nextTick(() => {
        // 在左侧面板添加告警事件详情组件
        // 事件弹窗
        console.log('事件弹窗--', window.leftWrap)
        window.leftWrap?.addContent({
          component: EventDetailInfo,
          props: {
            orderId: item.id,
            detail: item,
            closeable: true,
            markerType: LayerConst.ALARM_EVENT,
            // 右上角提示时，打开事件详情时，节点上添加一个标记
            isAutoOpen: true,
          },
          key: `eventDetailId_${item.id}`,
          group: 'eventDetailGroup',
          onclose: this.closeInfo,
        })
        // 更新告警信息ID为当前订单ID
        this.alarmInfoId = item.id
      })
    },

    /**
     * 关闭告警详情窗口，并重置或清除相关状态。
     * @param {Object} options - 选项对象，包含resetLayer标志。
     */
    /**
     * 关闭告警详情
     */
    closeInfo({ resetLayer }) {
      // 关闭左侧面板的所有内容
      window.leftWrap?.closeAllContent?.()
      // 发送事件，清除周围信息，可选地重置层
      this.$EventBus.$emit(Events.EVT_LIST.EVENT_DETAIL_CLEAR_AROUND, {
        resetLayer,
      })
      // 重置告警信息ID
      this.alarmInfoId = ''
    },

    /**
     * 刷新告警列表。
     * 重置关键字值、页码，并重新获取告警计数和列表。
     */
    /**
     * 刷新列表
     */
    refreshList(showCount = true) {
      // 重新获取告警计数
      showCount && this.getAlarmCount()
      // 重置关键字值
      this.keywordValue = ''
      // 重置页码为第1页
      this.rightQueryParam.pageNum = 1
      this.rightTableParam.total = 0
      // 重新获取右侧收藏列表
      this.getRightCollectList()
      this.getRightListFcuse()
    },

    /**
     * 异步获取链路ID枚举。
     * 根据传入的流程列表，查询每个流程的链路信息，并存储为字典形式的缓存。
     * @param {Array} flows - 流程列表，每个元素包含流程ID。
     */
    async getLinkIdEnum(flows) {
      // 对每个流程发起查询链路信息的请求
      const fetches = flows.map(item => {
        return queryFlowLink(item)
      })
      // 如果没有请求，则直接返回
      if (fetches.length < 1) {
        return
      }
      // 等待所有请求完成
      const res = await Promise.all(fetches)
      // 初始化链路ID字典
      const linkIdDict = {}
      // 处理每个请求的结果
      res.forEach(item => {
        // 解析请求结果的成功与否及数据
        const [success, data] = item
        // 如果请求不成功，则跳过
        if (!success) {
          return
        }
        // 遍历数据中的每个链路
        data.forEach(link => {
          const { linkId, flowId, linkName, flowName } = link
          // 以<流程ID>_<链路ID>为键，存储链路信息
          linkIdDict[`${flowId}_${linkId}`] = {
            linkName,
            flowName,
            flowId,
            linkId,
          }
        })
      })
      // 更新链路ID缓存
      this.flowLinkCache = linkIdDict
    },
  },
}
</script>

<style lang='less' src='../../../ScreenPages/IntegratedMonitor/EventList/index.less' />
