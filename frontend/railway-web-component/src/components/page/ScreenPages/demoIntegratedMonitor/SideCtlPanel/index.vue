<!--
 * @Description:
 * @Autor: he.chao
 * @Date: 2022-10-19 15:07:55
 * @LastEditors: liu.yongli
 * @LastEditTime: 2025-06-12 21:39:01
 * @LastEditTime: 2024-06-04 18:29:10
-->
<template>
  <!-- 控制面板的外层容器，根据 ctlEnter 的值动态添加 'active' 类 -->
  <div :class="`ctl-panel-wrap ${ctlEnter ? 'active' : ''}`">
    <!-- 展开控制图标 -->
    <i class="expand-ctl"></i>
    <div class="ctl-panel-container">
      <!-- 循环渲染配置的控制项 -->
      <div
        v-for="(item, index) in cfgCtl"
        :class="`ctl-panel-item ${
            selectedCtl[item.dicValue] ? 'selected' : ''
          }`"
        :data-key="item.dicValue"
        @click="itemClick(item)"
        :key="`${index}_${item.dicValue}`"
      >
        <!-- 图标显示 -->
        <span class="item-icon">
          <i
            :class="`iconfont`"
            :style="`background: url(${item.layerIconUrl}) 100% 100% no-repeat;background-size: 100% 100%;`"
          />
        </span>
        <!-- 显示控制项名称 -->
        <span class="item-text">{{ item.dicName }}</span>
      </div>
    </div>
  </div>
</template>
<script>
import api from '@/api'
import { getLayerCfgByModelCode } from '@/api/service/imScreenService'
// import MapOptions from '@/components/common/Map/CommonMap'
import {
  getUserLocalStorage,
  setUserLocalStorage,
} from '@/components/common/utils'
import { $getUrlParam } from '@/utils/common.js'
import { CfgEnum } from '@/components/page/ScreenPages/IntegratedMonitor/enum/CfgEnum'
import LayerConst from '@/components/page/ScreenPages/IntegratedMonitor/Map/LayerConst'
//  drawCircle,
import { drawCircle30, isExistCircle } from '@/utils/map3.0' // 绘制圆
import { mapGetters, mapMutations } from 'vuex'
import { getAllViewTemplate } from '@/api/service/common'
import { Events } from '@/components/page/ScreenPages/IntegratedMonitor/enum/EventsEnum' // 导入事件枚举
import {
  // leftData,
  orderResourceData,
} from '@/components/page/ScreenPages/IntegratedMonitor/mockData'
import AnimalLayerData from '../mockData/AnimalLayerData' // 导入动物图层数据
import { transformPointToMercator } from '@/utils'

const MapEvent = Events.MapEvent
const layerCache = {}
let timer
export default {
  inject: ['mapRef'],
  props: {
    mapId: {
      type: String,
    },
  },
  data: () => {
    return {
      // 控制面板是否显示
      ctlEnter: true,
      // 选中的控制项
      selectedCtl: {},
      // 控制项配置
      cfgCtl: [],
      // 当前事件
      currentEvent: null,
      // 大屏参数
      viewInfos: [],
      // 监听新事件的定时器
      // newEventListentimer: null,
    }
  },
  computed: {
    // 选中的区域
    selectedRegion() {
      return this.$store.state.map.selectedRegion // 需要监听的属性
    },
    // allEventData() {
    //   return this.$store.state.mockDataStore.allEventData
    // },
    ...mapGetters('map', ['formatSelectedRegion']), // 获取地图相关的 Vuex 状态
    ...mapGetters('mockDataStore', ['getAllLoudSpeakerData']), // 获取模拟数据相关的 Vuex 状态
    ...mapGetters('captureInfo', ['getOriVideLayerOpenStatus']),
  },
  watch: {
    // 监听 selectedRegion 的变化
    selectedRegion: {
      handler: function (newVal, oldVal) {
        if (newVal) {
          this.eventDetailClose({
            resetLayer: true,
            clearEvt: true,
          })
        }
      },
    },
  },
  beforeDestroy() {
    // 清除定时器和事件监听
    timer && clearTimeout(timer)
    this.$EventBus.$off(Events.EVT_LIST.EVENT_AROUND) // 取消事件监听
    this.$EventBus.$off(Events.EVT_LIST.EVENT_DETAIL_CLEAR_AROUND) // 取消事件监听
    // this.newEventListentimer && clearInterval(this.newEventListentimer)
    this.$EventBus.$off(Events.PassRouteEvt.OPEN, this.changeOpenPassRoute) // 取消事件监听
  },
  mounted() {
    // 监听事件
    this.$EventBus.$on(Events.EVT_LIST.EVENT_AROUND, this.onAddEventArround) // 监听事件
    this.$EventBus.$on(
      Events.EVT_LIST.EVENT_DETAIL_CLEAR_AROUND,
      this.eventDetailClose
    ) // 监听事件
    this.addTimer() // 添加定时器
    this.getCfgCtl() // 获取图层配置
    // 监听新事件提示
    // this.newEventListentimer = setInterval(() => {
    //   try {
    //     const targetNodeNew =
    //       window.parent.document.querySelector('.c-socket-message')
    //     if (targetNodeNew) {
    //       if (this.selectedCtl['ALARM_EVENT']) {
    //         const eventItem = this.cfgCtl.find(
    //           item => item.dicValue === 'ALARM_EVENT'
    //         )
    //         this.$EventBus.$emit(MapEvent.REMOVE_MARKER, eventItem)
    //         setTimeout(
    //           () => this.$EventBus.$emit(MapEvent.ADD_MARKER, eventItem),
    //           500
    //         )
    //       }
    //     }
    //   } catch (e) {
    //     // console.log(e)
    //   }
    // }, 4000)
    // 监听过车信息的开关状态切换
    this.$EventBus.$on(Events.PassRouteEvt.OPEN, this.changeOpenPassRoute) // 监听事件
    // this.$EventBus.$on(Events.PassRouteEvt.CLOSE, this.changeClosePassRoute) // 监听事件
  },
  methods: {
    ...mapMutations('captureInfo', ['setOriVideLayerOpenStatus']),
    // 解析url
    setViewInfos(viewId, compoPath) {
      if (compoPath && compoPath.split('//').length > 1) {
        const path = compoPath.split('//')[1]
        const pathArr = path.split('/')
        let url = pathArr[pathArr.length - 1]
        if (!url && pathArr.length > 1) {
          url = pathArr[pathArr.length - 2]
        }
        if (url) {
          this.viewInfos.push(`${viewId}:${url}`)
        }
      }
    },
    // 获取大屏参数
    async getViewInfos() {
      if (this.viewInfos.length < 1) {
        const [success, data] = await getAllViewTemplate()
        if (success) {
          data.forEach(item => {
            if (item.viewJson) {
              const viewObj = JSON.parse(item.viewJson)[0]
              const {
                compInfo: { compoPath },
              } = viewObj
              this.setViewInfos(item.viewId, compoPath)
            }
          })
        }
      }
    },
    /**
     * 异步获取图层配置，并根据配置初始化图层控制。
     * 此方法初始化地图的图层配置，根据配置决定哪些图层是默认选中的，并将配置存储在全局变量中以供其他地方使用。
     */
    async getCfgCtl() {
      const viewId = sessionStorage.getItem('VIEW_ID') // $getUrlParam('viewId') // 获取 URL 参数中的 viewId
      let result = []
      await this.getViewInfos() // 获取大屏参数

      // 获取事件筛选后的参数
      // console.log(
      //   'test---',
      //   getUserLocalStorage(CfgEnum.STORAGE_KEY.SELECT_REGION)
      // )
      // 异步获取指定模型代码的图层配置
      const [, resData] = await getLayerCfgByModelCode({
        modelCode: viewId,
        viewInfos: this.viewInfos,
      })
      const poiIconCfg = {}
      // 使用测试数据 resData?.length ? resData : leftData.data
      const data = resData
      if (data?.length) {
        // 处理图层配置数据，构建 poiIconCfg 和 result
        result = data.map(item => {
          poiIconCfg[item.layerCode] = {
            ...item.poiIconJson,
            layerIconUrl: item.layerIconUrl,
            layerName: item.layerName,
          }
          return {
            dicValue: item.layerCode,
            dicName: item.layerName,
            isDefault: item.isDefault,
            layerIconUrl: item.layerIconUrl,
            layerIconUrlS: item.layerIconUrlS,
          }
        })
      }
      // 将 poi 图标配置存储在全局变量中
      window.sysConfig.poiIconCfg = poiIconCfg
      // 从用户本地存储中获取侧边栏配置
      const usrCachedCfg = getUserLocalStorage(CfgEnum.STORAGE_KEY.SIDEBAR_KEY)
      // 根据用户配置和默认配置初始化 selectedCtl
      result.map(item => {
        if (usrCachedCfg && item.dicValue in usrCachedCfg) {
          this.selectedCtl[item.dicValue] = usrCachedCfg[item.dicValue]
          // 触发地图icon加载事件
          this.selectedCtl[item.dicValue] &&
            this.$EventBus.$emit(MapEvent.ADD_MARKER, item)
          return
        }
        if (item.isDefault === 'Y') {
          this.selectedCtl[item.dicValue] = true
          this.selectedCtl[item.dicValue] &&
            this.$EventBus.$emit(MapEvent.ADD_MARKER, item)
          return
        }
        this.selectedCtl[item.dicValue] = false
      })
      // 更新用户本地存储的图层配置
      setUserLocalStorage(
        `${CfgEnum.STORAGE_KEY.SIDEBAR_KEY}`,
        this.selectedCtl
      )
      this.cfgCtl = result
    },
    /**
     * 处理图层项的点击事件。
     * 根据当前事件和图层配置，切换图层的选中状态，并相应地添加或移除地图标记。
     * @param {Object} item - 被点击的图层项配置。
     */
    itemClick(item) {
      console.log('itemClick--', item, this.selectedCtl)
      const key = item.dicValue

      // 处理无人机操作
      if (key === 'wurenji') {
        this.selectedCtl[key] = !this.selectedCtl[key]
        this.selectedCtl = { ...this.selectedCtl }
        this.$globalEventBus.$emit(
          `common-comp-layers-control__change-layers-checked`,
          { idCheckMap: { 4: this.selectedCtl[key] }, changeMemory: false }
        )
        return
      }

      // 处理特定的当前事件逻辑
      if (this.currentEvent && key !== LayerConst.RAILWAY_LINE) {
        this.eventDetailClose({
          resetLayer: false,
          clearEvt: false,
        })
        this.selectedCtl[key] = !this.selectedCtl[key]
        this.selectedCtl = { ...this.selectedCtl }
        this.addEventArroundMarker()
        return
      }

      // 默认的图层点击逻辑
      this.selectedCtl[key] = !this.selectedCtl[key]
      this.selectedCtl = { ...this.selectedCtl }
      if (this.selectedCtl[key]) {
        this.$EventBus.$emit(MapEvent.ADD_MARKER, item)
      } else {
        this.$EventBus.$emit(MapEvent.REMOVE_MARKER, item)
      }
      // 更新用户本地存储的图层配置
      setUserLocalStorage(
        `${CfgEnum.STORAGE_KEY.SIDEBAR_KEY}`,
        this.selectedCtl
      )
    },
    /**
     * 清除计时器。
     * 用于清理添加的计时器，防止内存泄漏。
     */
    clearTimer() {
      timer && clearInterval(timer)
    },
    /**
     * 添加计时器。
     * 设置一个计时器，在5秒后将 ctlEnter 标志设置为 false。
     */
    addTimer() {
      this.clearTimer()
      timer = setTimeout(() => {
        this.ctlEnter = false
      }, 5 * 1000)
    },
    /**
     * 处理添加事件周边图层的逻辑。
     * 根据当前事件，清除地图上的所有标记，然后添加事件图层和选中的其他图层。
     * @param {Object} param - 当前事件的参数。
     */
    onAddEventArround(param) {
      this.currentEvent = param
      // 首先清空地图上所有数据
      this.clearMapMarker([LayerConst.RAILWAY_LINE])
      // 渲染事件
      this.$EventBus.$emit(MapEvent.ADD_MARKER, {
        dicValue: LayerConst.ALARM_EVENT,
        initData: [{ ...param }],
      })
      // 查询事件周边已经选中的图层数据渲染
      this.addEventArroundMarker()
    },
    /**
     * 清除地图上指定图层的标记。
     * 遍历 selectedCtl，根据选中状态和排除列表决定是否移除标记。
     * @param {Array} exclude - 需要排除的图层列表，默认为空数组。
     */
    clearMapMarker(exclude = []) {
      Object.keys(this.selectedCtl).forEach(key => {
        if (this.selectedCtl[key] && !exclude.includes(key)) {
          this.$EventBus.$emit(MapEvent.REMOVE_MARKER, { dicValue: key })
        }
      })
    },
    /**
     * 在地图上的圆形区域周围添加模拟事件标记
     * @param {string} dicValue - 包含图层类型的字符串
     * @param {Object} circle - 表示圆形区域的对象，具有intersectsCoordinate方法
     */
    addMockEventArroundMarker(dicValue, circle) {
      // 检查dicValue是否包含LayerConst.LOUDSPEAKER
      if (dicValue.includes(LayerConst.LOUDSPEAKER)) {
        // 获取所有扬声器数据，并过滤出与圆形区域相交的数据
        const loudSpeakerData = this.getAllLoudSpeakerData.filter(item => {
          const _point = transformPointToMercator([
            item.longitude * 1,
            item.latitude * 1,
          ])
          return circle.intersectsCoordinate(_point)
        })
        // 通过事件总线发送添加标记的事件，传递扬声器数据
        this.$EventBus.$emit(MapEvent.ADD_MARKER, {
          dicValue: LayerConst.LOUDSPEAKER,
          initData: loudSpeakerData,
        })
      }
      // 检查dicValue是否包含LayerConst.ANIMAL
      if (dicValue.includes(LayerConst.ANIMAL)) {
        // 获取所有动物数据，并过滤出与圆形区域相交的数据
        const animalData = AnimalLayerData.data.filter(item => {
          const _point = transformPointToMercator([item.lon * 1, item.lat * 1])
          return circle.intersectsCoordinate(_point)
        })
        // 通过事件总线发送添加标记的事件，传递动物数据
        this.$EventBus.$emit(MapEvent.ADD_MARKER, {
          dicValue: LayerConst.ANIMAL,
          initData: animalData,
        })
      }
    },
    /**
     * 清除地图上指定图层的圆形标记。
     */
    removeEvtAroundCircle() {
      if (layerCache.evtAroundCircle) {
        // const _map = this.mapRef.getMapRef(this.mapId).mapInstance
        // MapOptions.removeLayer(_map, layerCache.evtAroundCircle)
        layerCache.evtAroundCircle.destroy()
        layerCache.evtAroundCircle = null
      }
    },
    // 初始化字典值
    initDicValue() {
      const dicValue = []
      Object.keys(this.selectedCtl).forEach(key => {
        // 获取选中的图层，排除事件图层
        if (this.selectedCtl[key] && ![LayerConst.ALARM_EVENT].includes(key)) {
          dicValue.push(key)
        }
      })
      return dicValue
    },
    /**
     * 添加事件周边的图层数据到地图。
     * 根据当前事件和选中的图层，查询并添加事件周边的资源标记到地图。
     */
    async addEventArroundMarker() {
      const { latitude, longitude } = this.currentEvent
      const dicValue = this.initDicValue()
      const param = {
        lat: latitude,
        lon: longitude,
        radius: sysConfig.EVENT_RES_RADIUS,
        ...this.formatSelectedRegion,
      }
      // 清除圆形
      this.removeEvtAroundCircle()
      // 绘制圆形
      const center = [Number(longitude), Number(latitude)]
      layerCache.evtAroundCircle = drawCircle30(
        this.mapRef.getMapRef(this.mapId).mapInstance,
        center,
        sysConfig.EVENT_RES_RADIUS
      )
      // 云广播和牲畜图层数据前台进行匹配过滤
      // const circleLayer = layerCache.evtAroundCircle
      // const cirlceSource = circleLayer.getSource()
      // 获取第一个面要素的几何对象
      // const circle = cirlceSource.getFeatures()[0].getGeometry()
      // 获取一个圆的几何对象
      const circle = isExistCircle(center, sysConfig.EVENT_RES_RADIUS)
      this.addMockEventArroundMarker(dicValue, circle)
      const url = `${this.$env.VUE_APP_REQ_PREFIX_BIZ}/adsEvtTypeConfig/qryWarningOrderResource`
      if (dicValue.length > 0) {
        param.dicValue = dicValue
      }
      const { data: resData } = await api.post(url, param)
      // 测试数据
      const data = resData ? resData : orderResourceData.data
      console.log('/adsEvtTypeConfig/qryWarningOrderResource--', data)
      if (Object.keys(data || {})?.length) {
        const { hornDeviceVoList, industryDeviceTreesList, resourceConfsList } =
          data
        // this.$EventBus.$emit(MapEvent.ADD_MARKER, {
        //   dicValue: LayerConst.LOUDSPEAKER,
        //   initData: hornDeviceVoList || [],
        // })
        this.$EventBus.$emit(MapEvent.ADD_MARKER, {
          dicValue: LayerConst.CAMERA,
          initData: industryDeviceTreesList || [],
        })
        if (resourceConfsList) {
          Object.keys(resourceConfsList).forEach(key => {
            this.$EventBus.$emit(MapEvent.ADD_MARKER, {
              dicValue: key,
              initData: resourceConfsList[key] || [],
            })
          })
        }
      } else {
        this.clearMapMarker()
      }
    },
    // 显示事件周边点击图例开关周边资源 ，clearEvt = false
    eventDetailClose({ resetLayer = true, clearEvt = true }) {
      // 关闭事件详情时调用，根据参数决定是否重置图层和清除事件标记
      // 如果 clearEvt 为 true，则清除报警事件标记；否则，仅清除铁路线标记
      this.clearMapMarker(
        clearEvt
          ? [LayerConst.RAILWAY_LINE]
          : [LayerConst.ALARM_EVENT, LayerConst.RAILWAY_LINE]
      )
      // 如果需要清除事件标记，则执行以下操作
      if (clearEvt) {
        // 将当前事件设置为 null，表示没有选中的事件
        this.currentEvent = null
        // 通过事件总线通知地图移除报警事件标记
        this.$EventBus.$emit(MapEvent.REMOVE_MARKER, {
          dicValue: LayerConst.ALARM_EVENT,
        })
        // 从地图中移除 evtAroundCircle 图层
        // MapOptions.removeLayer(
        //   this.mapRef.getMapRef(this.mapId).mapInstance,
        //   layerCache.evtAroundCircle
        // )
        this.removeEvtAroundCircle()
      }
      // 如果不需要重置图层，则直接返回
      if (!resetLayer) {
        return
      }
      // 初始化图层数据
      const dicValue = []
      // 遍历 selectedCtl 对象，将值为 true 的键添加到 dicValue 数组中
      Object.keys(this.selectedCtl).forEach(key => {
        if (this.selectedCtl[key]) {
          dicValue.push(key)
        }
      })
      // 遍历 dicValue 数组，通过事件总线通知地图为每个键（图层）添加标记
      dicValue.forEach(key => {
        this.$EventBus.$emit(MapEvent.ADD_MARKER, {
          dicValue: key,
        })
      })
    },
    /**
     * 过车线路
     * @param {*} openStatus
     */
    changeOpenPassRoute(openStatus) {
      let changeCameraLayer = false
      // 如果是打开过车
      if (openStatus) {
        // 记住原来的图层开关状态
        this.setOriVideLayerOpenStatus(
          this.selectedCtl[LayerConst.CAMERA] ? true : false
        )
        // 如果没打开视频，那就打开
        if (!this.selectedCtl[LayerConst.CAMERA]) {
          changeCameraLayer = true
        }
      } else {
        // 如果是关闭过车，需要判断原来的图层是打开的还是关闭的
        if (
          !this.getOriVideLayerOpenStatus &&
          this.selectedCtl[LayerConst.CAMERA]
        ) {
          changeCameraLayer = true
        }
      }
      if (changeCameraLayer) {
        const cameraItem = this.cfgCtl.find(
          item => item.dicValue === LayerConst.CAMERA
        )
        this.itemClick(cameraItem)
      }
    },
  },
}
</script>
<!-- <style scoped lang="less" src="../../../ScreenPages/IntegratedMonitor/SideCtlPanel/index.less" /> -->
<style scoped lang="less">
@import '~@/components/page/ScreenPages/IntegratedMonitor/SideCtlPanel/index.less';
.ctl-panel-wrap {
  &:hover,
  &.active {
    .expand-ctl {
      background-image: url('../../../ScreenPages/IntegratedMonitor/SideCtlPanel/img/celan_shouqi.png') !important;
    }
  }
  .expand-ctl {
    background-image: url('../../../ScreenPages/IntegratedMonitor/SideCtlPanel/img/celan_zhankai.png') !important;
    right: -15px !important;
    width: 29px !important;
    height: 402px !important;
    top: 50%;
    transform: translate(0, -50%);
  }
}
</style>