<template></template>

<script>
import { mapGetters, mapActions } from 'vuex'
import { getMetaByDicCode } from '@/api/service/imScreenService'
import LayerConst from '@/components/page/ScreenPages/IntegratedMonitor/Map/LayerConst'
import { enums } from '@/components/page/ScreenPages/IntegratedMonitor/DetailInfo/enums'

export default {
  data() {
    return {
      timer: null,
      wsInstance: null,
      messages: [],
      cameraData: [],
      index: 0,
    }
  },
  computed: {
    // 选中的区域
    selectedRegion() {
      return this.$store.state.map.selectedRegion // 需要监听的属性
    },
    // 摄像机图层是否准备好了
    cameraLayerReady() {
      return this.$store.state.captureInfo.cameraLayerReady // 需要监听的属性
    },
    ...mapGetters('map', ['formatSelectedRegion']),
  },
  watch: {
    // 监听 selectedRegion 的变化
    selectedRegion: {
      handler: function (newVal, oldVal) {
        if (newVal) {
          this.changeRegionHandle()
        }
      },
      deep: true,
    },
  },
  async created() {
    this.initWs()
  },
  beforeDestroy() {
    this.timer && clearInterval(this.timer)
    this.initCaptureInfo()
  },
  methods: {
    ...mapActions('captureInfo', ['addCaptureTask', 'initCaptureInfo']),
    /**
     * sonar 对webapi随机数报错，所以使用随机数代替
     */
    getSecureRandom() {
      const array = new Uint32Array(1)
      window.crypto.getRandomValues(array) // 浏览器安全API
      return array[0] / (0xffffffff + 1) // 转换为[0,1)范围
    },
    /**
     *  生成随机数
     * @param {*} min
     * @param {*} max
     */
    getRandomInt(min, max) {
      // 生成一个[min, max]的随机整数
      const random = this.getSecureRandom()
      return Math.floor(random * (max - min + 1)) + min
    },
    async initWs() {
      const [, resData] = await getMetaByDicCode({
        layerCode: LayerConst.CAMERA,
        checkMode: '1',
        ...this.formatSelectedRegion,
      })
      const data = resData
      if (data) {
        this.cameraData = data
          .filter(
            item =>
              item.list &&
              item.list.length > 0 &&
              [enums.CAMERA.deviceStatusEnum.onLine.code].includes(
                item.list[0].deviceStatus
              )
          )
          .map(item => {
            return {
              ...item,
              ...(item?.list?.[0]?.channelInfoVOList?.[0] || {}),
              ...(item?.list?.[0] || {}),
            }
          })
        this.startMockRtCaptureInfo()
      }
    },
    startMockRtCaptureInfo() {
      this.timer = setInterval(() => {
        // 每次随机1-3个摄像机
        let num = this.getRandomInt(1, 3)
        if (this.index + num > this.cameraData.length) {
          num = this.cameraData.length - this.index
        }
        console.log('过车信息推送数据开始--', this.index, this.index + num)
        this.cameraData.slice(this.index, this.index + num).map(item => {
          this.onNewCaptureInfos({
            ...item,
            videoUrl: require(`@/components/page/ScreenPages/demoIntegratedMonitor/mockData/captureVideo/captureVideo_${this.getRandomInt(
              1,
              5
            )}.mp4`),
          })
        })
        if (this.index + num >= this.cameraData.length) {
          this.index = 0
        } else {
          this.index += num
        }
      }, this.getRandomInt(15, 20) * 1000)
    },
    /**
     * 执行
     * @param {*} data
     */
    onNewCaptureInfos(data) {
      this.addCaptureTask({
        deviceCode: data.deviceCode,
        captureInfo: data,
      })
    },
    // 区域切换以后，重新设置当前区域的抓拍数据
    changeRegionHandle() {
      this.timer && clearInterval(this.timer)
      this.cameraData = []
      this.index = 0
      this.initCaptureInfo()
      this.initWs()
    },
  },
}
</script>

<style scoped></style>
