/**
 * 该模块定义了用于处理和显示警报事件地图标记的函数和属性。
 * 它包括绘制标记、处理点击事件、选择和取消选择标记等功能。
 */
import { getMapEvts } from '@/api/service/imScreenService';
// import ConstEnum from '@/components/common/ConstEnum';
import CommonMap from '@/components/common/Map/CommonMap';
import { isNotEmpty } from '@/components/common/utils';
// import {
//   enums
// } from '@/components/page/ScreenPages/IntegratedMonitor/DetailInfo/enums';
import {
  Events
} from '@/components/page/ScreenPages/IntegratedMonitor/enum/EventsEnum';
import LayerConst
  from '@/components/page/ScreenPages/IntegratedMonitor/Map/LayerConst';
import {
  baseMarker, calMakerStyle, closeLeftSideWindowByIdPrefix, clusterTip
} from '@/components/page/ScreenPages/IntegratedMonitor/Map/MapLayerHandle/CommonHandle';
import {
  evtIcon,
  evtTypesIcon
} from '@/components/page/ScreenPages/IntegratedMonitor/Map/marker';
import { createSource, useClusterLayer } from '@/utils/map3.0';
// import moment from 'moment';
import { getEvtParams } from '@/components/common/utils';
import { transformPointToMercator } from '@/utils'
// import {
//   queryOrderInfo,
// } from '@/api/service/imScreenService'
import vue from '@/main'; // 导入Vue实例

// import { postProxy } from '@/api/service/common';
// import api from '@/api';
// import { 
//   dealStateEnm,
// } from '@/components/page/ScreenPages/IntegratedMonitor/EventFilter/typeDatas'

const {
  EVT_LIST,
  // MapEvent
} = Events; // 解构事件列表和地图事件

const ALARM_EVENT = {
  largeAnchor: [40, 80], // 大图标锚点位置
  smallAnchor: [15, 80], // [50, 100], // 小图标锚点位置
  cfg: {
    markers: []
  },
  /**
   * 在地图上绘制警报事件标记。
   * @param {Object} options - 绘制选项。
   * @param {Function} options.onclick - 点击标记时的回调函数。
   * @param {Object|null} options.initData - 初始化数据，用于直接渲染标记。
   * @param {boolean} options.stopClick - 是否阻止标记的点击事件。
   * @param {Object} options.region - 地域筛选条件。
   * @returns {Object|null} 返回包含标记图层和标记数组的对象，如果没有数据则返回null。
   * @returns {string} 筛选组件的操作状态
   */
  async draw({
    onclick = () => {
      console.log("default implement")
    },
    initData = null,
    stopClick = false,
    // region = {},
    filterState = '',
    refresh = false,
    mapInstance
  }) {
    let layerData = initData;
    if (!layerData) {
      // 接口参数
      let params = {
        isFocus: 'Y',
        // 默认时间
        startDate: '2025-07-14 00:00:00',
        endDate: '2025-08-15 23:59:59',
        fusionStatusList: undefined,
        ...getEvtParams(filterState)
      }
      const [, resData] = await getMapEvts(params);
      // 从Vuex中获取当前加载的事件数据
      // layerData =vue.$store.state.mockDataStore.curLoadedEventData;
      const data = resData
      if (data?.length) {
        layerData = data.map(v => ({
          ...v,
          latitude: v.startLat,
          longitude: v.startLng,
        }));
      }
    }
    if (!layerData || layerData.length < 1) {
      return;
    }
    const points = layerData.map((item) => {
      // 3.0 地图转成墨卡托
      const lngLat = transformPointToMercator([Number(item.longitude), Number(item.latitude)])
      return {
        ...item,
        lng: lngLat[0], // longitude
        lat: lngLat[1], // latitude
        orderId: item.warningOrderId,
        location: item.eventAddress, // address
      };
    });
    const markers = [];
    points.map((item) => {
      const [small, large] = this.getTypesIcon(item.eventTypeName);// 获取图标
      const marker = baseMarker({
        point: [item.lng, item.lat],// 标记点的经纬度
        markerAttr: {
          markerType: LayerConst.ALARM_EVENT,// 标记类型
          ds: item, // 数据源
          selected: !!initData // 是否选中
        },
        initIcon: initData ? large : small, // 初始图标
        selectedIcon: large, // 选中图标
        anchorCfg: this, // 锚点配置
        onclick: (attr, markerFeature) => {
          if (stopClick) {
            return; // 如果阻止点击，直接返回
          }
          onclick(attr, markerFeature);// 执行点击回调
          const {
            selected,
          } = attr;
          if (selected) {
            this.onSelected(marker, item);// 如果选中，调用选中处理函数
          } else {
            // 否则关闭详细信息
            vue.$EventBus.$emit(EVT_LIST.ON_DETAIL_CLOSE, { resetLayer: true });
          }
        }
      });
      markers.push(marker); // 将标记添加到数组中
    });
    this.cfg.markers = markers; // 更新配置中的标记数组
    if (!refresh) {
      this.cfg.markersLayer = useClusterLayer({
        markers: markers, // 标记数组
        map: mapInstance, // 地图对象
        type: LayerConst.ALARM_EVENT, // 图层类型
        onclick: this.onClusterClick // 聚合点击事件处理函数
      });
    }
    return {
      markersLayer: this.cfg.markersLayer, // 返回标记图层
      markers
    };
    // 初始化marker点完成
  },
  /**
   * 初始化标记符号。
   * 此函数为给定的标记数组中的每个标记设置初始状态和样式。
   * @param {Array} markers - 包含标记信息的数组。
   */
  initMarkers(markers) {
    // 遍历标记数组
    markers.forEach(marker => {
      // 根据标记的属性获取图标
      const [small] = this.getTypesIcon(marker.values_.attributes?.ds?.eventTypeName);
      // 设置标记的选中状态为未选中
      marker.values_.attributes.selected = false;
      // 根据小图标和锚点位置计算并设置标记的样式
      marker.style_ = calMakerStyle(small, this.smallAnchor);
    });
  },
  /**
   * 当标记被选中时触发的事件处理函数。
   * @param {Object} marker - 被选中的标记对象。
   * @param {Object} item - 与标记相关联的数据项。
   */
  onSelected(marker, item) {
    // 触发点击事件
    vue.$EventBus.$emit(EVT_LIST.ON_CLICK, item);
  },

  /**
   * 取消当前选中的标记。
   */
  cancelSelected() {
    const markers = this.getMarker(); // 获取当前标记
    if (!isNotEmpty(markers)) {
      return; // 如果标记为空，直接返回
    }
    this.initMarkers(markers); // 初始化标记
    this.cfg.markersLayer.setSource(createSource({
      markers,
      type: LayerConst.ALARM_EVENT, // 图层类型
      onclick: this.onClusterClick // 聚合点击事件处理函数
    }));
  },

  /**
   * 获取配置中的标记数组。
   * @returns {Array} 标记数组。
   */
  getMarker() {
    const {
      markers
    } = this.cfg;
    return markers;
  },

  /**
   * 清除地图上的所有标记。
   */
  clear() {
    closeLeftSideWindowByIdPrefix(`${LayerConst.ALARM_EVENT}-`,
      LayerConst.ALARM_EVENT);
  },

  /**
   * 根据数据项生成标记图标。
   * @param {Object} data - 数据项。
   * @returns {Array} 图标URL数组。
   */
  getIcon(data) {
    // 返回小图标和大图标
    return [evtIcon.iconEvtAlarmS, evtIcon.iconEvtAlarmL];
  },

  /**
   * 获取不同类型的事件图标
   * @param {String} type - 类型。
   * @returns {Array} 图标URL数组。
   */
  getTypesIcon(type) {
    return evtTypesIcon[type];
  },

  /**
   * 打开详细信息窗口。
   * @param {Object} selectedData - 被选中的数据项。
   */
  openDetail(selectedData) {
    CommonMap.closeTip(vue.$store.state.map.mapStoreRef.mapInstance); // 关闭提示
    vue.$EventBus.$emit(EVT_LIST.ON_CLICK, selectedData); // 触发点击事件
  },

  /**
   * 当标记聚合成簇时触发的事件处理函数。
   * @param {Object} event - 包含聚合成的簇的信息的对象。
   */
  async onClusterClick({
    features,
    geometry
  }) {
    if (!features || !Array.isArray(features) || features.length < 1) {
      return; // 如果没有聚合特征，直接返回
    }
    const getStatus = (_key) => {
      if (_key === '3') return '已消散'
      else if (['1', '2'].includes(_key)) return '未消散'
      else ''
    }
    // let res = []
    // 等待所有请求完成
    // promise.all 发到线上环境获取不到返回值，所以使用for循环
    // for (const feature of features) {
    //   try {
    //     const data = feature.values_.attributes.ds;
    //     /// 获取详情
    //     const response = await queryOrderInfo({ warningOrderId: data.lastEventId });
    //     res.push(response);
    //   } catch (error) {
    //     console.error('请求失败:', error);
    //   }
    // }
    // console.log('fetches-请求-', res)
    // 查询到对应的数据
    const ds = features.map(feature => {
      const data = feature.values_.attributes.ds;
      // const orderList = res.find(v => {
      //   const [, fetchData] = v
      //   return fetchData?.order?.warningOrderId === data.lastEventId
      // })
      // const orderData = orderList?.[1]?.order

      return {
        key: data.warningOrderId ?? data.id,
        type: ['其他', '其它'].includes(data.eventTypeName) ? data.warningTypeName : data.eventTypeName, // 事件类型,
        name: data.title ?? data.alarmBody,
        status: data.fusionStatus, // orderData?.orderStatus, // 直接展示事件状态,不展示告警状态了 
        statusName: getStatus(data.fusionStatus), // orderData?.orderStatusName,
        // enums.ALARM_EVENT.orderStatus.color(data.orderStatus)
        color: '#4f9fff', // 展示默认颜色,
        ...data
      };
    });
    const [lng, lat] = geometry.getCoordinates();
    clusterTip({
      ds,
      markerType: LayerConst.ALARM_EVENT,
      lng,
      lat
    });
  }
};
export default ALARM_EVENT;

