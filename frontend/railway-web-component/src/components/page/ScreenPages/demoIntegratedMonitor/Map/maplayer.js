/**
 * 导入不同的地图图层处理对象，用于管理地图上不同类型的图层，如警报事件、摄像头、扬声器等。
 */
import CAMERA from '@/components/page/ScreenPages/IntegratedMonitor/Map/MapLayerHandle/CAMERA';
import COMM_LAYER from '@/components/page/ScreenPages/IntegratedMonitor/Map/MapLayerHandle/COMM_LAYER';
import RAILWAY_LINE from '@/components/page/ScreenPages/IntegratedMonitor/Map/MapLayerHandle/RAILWAY_LINE';
import LOUDSPEAKER from './MapLayerHandle/LOUDSPEAKER';
import ANIMAL from './MapLayerHandle/ANIMAL';
import ALARM_EVENT from './MapLayerHandle/ALARM_EVENT';

/**
 * 地图图层管理对象，包含各种图层的处理方法和配置。
 *
 * @type {{TUNNEL: {openDetail(*, *): void, getMarker(): [], cfg: {markers: *[]}, onClusterClick({features: *, geometry: *}): void, clear(): void, onSelected(*): void, smallAnchor: number[], draw({onclick?: function(), initData?: null, region?: {}}): {markersLayer: CTMapOl.layer.Vector, markers: *[]}, cancelSelected(): void, initMarkers(*): void, largeAnchor: number[], getIcon(*): [*,*]}, RAILWAY_LINE: {clear(*): void, draw({markersLayer: *, onclick?: function(), initData?: null, stopClick?: boolean}): {markersLayer: CTMapOl.layer.Vector, markers: *[]}, cancelSelected(*)}, LOUDSPEAKER: {openDetail(*, *): void, filterFault({layer: *, show: *}): void, cfg: {showFault: boolean}, onClusterClick({features: *, geometry: *}): void, clear(): void, smallAnchor: number[], draw({onclick?: function(), initData?: null, region?: {}}): {markersLayer: CTMapOl.layer.Vector, markers: *[]}, largeAnchor: number[], getIcon(*): ([*,*]|[*,*]|[*,*]|[*,*]), getMarker(): (*), onSelected(*): void, cancelSelected(): void, initMarkers(*): void}, CAMERA: {openDetail(*, *): void, openTip(*, *, *): void, cfg: {markers: *[], tipIdSuffix: string}, onClusterClick({features: *, geometry: *}): void, clear(): void, smallAnchor: number[], draw({onclick?: function(), initData?: null, region?: {}}): {markersLayer: CTMapOl.layer.Vector, markers: *[]}, largeAnchor: number[], getIcon(*): [*,*], getMarker(): *, onSelected(*): void, cancelSelected(): void, initMarkers(*): void}, ALARM_EVENT: {openDetail(*): void, getMarker(): [], cfg: {markers: *[]}, onClusterClick({features: *, geometry: *}): void, clear(): void, onSelected(*, *): void, smallAnchor: number[], draw({onclick?: function(), initData?: null, region?: {}}): {markersLayer: CTMapOl.layer.Vector, markers: *[]}, cancelSelected(): void, initMarkers(*): void, largeAnchor: number[], getIcon(*): [*,*]}, BRIDGE: {openDetail(*, *): void, getMarker(): *, cfg: {markers: *[]}, onClusterClick({features: *, geometry: *}): void, clear(): void, onSelected(*): void, smallAnchor: number[], draw({onclick?: Function, initData?: Object, region?: Object}): Promise<undefined|{markersLayer: BRIDGE.cfg.markersLayer, markers: []}>, cancelSelected(): void, initMarkers(*): void, largeAnchor: number[], getIcon(*): [*,*]}}}
 */
export const maplayer = {
  LOUDSPEAKER,
  CAMERA,
  ALARM_EVENT,
  RAILWAY_LINE,
  COMM_LAYER,
  ANIMAL,
  /**
   * 根据图层类型获取对应的图层处理对象。
   * 如果给定的图层类型不存在，则返回默认的COMM_LAYER。
   *
   * @param {string} layer 图层类型。
   * @returns {Object} 对应的图层处理对象。
   */
  getLayer(layer) {
    if (maplayer[layer]) {
      return maplayer[layer];
    }
    return COMM_LAYER;
  },
  /**
   * 获取所有图层管理对象的键名，过滤掉非图层对象的键。
   *
   * @returns {Array} 图层管理对象的键名数组。
   */
  getKeys() {
    return Object.keys(maplayer)
      .filter(key => typeof maplayer[key] !== 'function');
  }
};
