import { isNotEmpty } from '@/components/common/utils'; // 导入工具函数，用于判断是否非空
import vue from '@/main'; // 导入Vue实例
// import CommonMap from '@/components/common/Map/CommonMap'; // 导入通用地图组件
import LayerConst from '@/components/page/ScreenPages/IntegratedMonitor/Map/LayerConst'; // 导入地图层常量
import {
  baseMarker, calMakerStyle, closeLeftSideWindow, closeLeftSideWindowByIdPrefix,
  clusterTip, openLeftSideWidow
} from '@/components/page/ScreenPages/IntegratedMonitor/Map/MapLayerHandle/CommonHandle'; // 导入地图层处理相关函数
import {
  broadcastIcon
} from '@/components/page/ScreenPages/IntegratedMonitor/Map/marker'; // 导入广播图标
import { createSource, useClusterLayer } from '@/utils/map3.0'; // 导入地图相关工具函数
import { enums } from '@/components/page/ScreenPages/IntegratedMonitor/DetailInfo/enums'; // 导入枚举值
// import LoudSpeakerPlayingBg from '@/components/page/ScreenPages/IntegratedMonitor/InfoWindow/LoudSpeakerPlayingBg.vue'; // 导入扬声器播放背景组件
import { transformPointToMercator } from '@/utils'

const LOUDSPEAKER = {
  largeAnchor: [30, 70], // 大图标的锚点位置
  smallAnchor: [25, 55], // 小图标的锚点位置
  cfg: {
    showFault: false, // 是否显示故障标记
    showLoudSpeakerPlayingBg: true, // 是否显示扬声器播放背景
    loudSpeakerPlayingBgIds: [], // 播放背景的ID集合
    loudSpeakerPlayingBgOverlays: {}, // 播放背景的覆盖物集合
  },
  /**
   * 绘制地图图标
   * @param markersLayer
   * @param onclick
   * @param initData
   * @returns {{markersLayer: CTMapOl.layer.Vector, markers: any[]}}
   */
  async draw({
    onclick = () => {
      console.log('default implement'); // 默认点击事件处理
    },
    initData = null, // 初始化数据
    region = {}, // 区域信息
    mapInstance
  }) {
    const layerData = await this.getData(initData, region); // 获取图层数据
    // 筛选正在播放中的设备
    const playingData = layerData.filter(device => device.deviceStatus === enums.LOUDSPEAKER.deviceStatus.playing.code);
    vue.$store.commit('mockDataStore/setCurPlayingLoudSpeakerData', playingData); // 更新Vuex中的播放数据
    const markers = []; // 存储正常状态的标记
    const faultMarkers = []; // 存储故障状态的标记
    layerData.forEach((item, index) => {
      // 3.0 地图转成墨卡托
      const lngLat = transformPointToMercator([Number(item.longitude), Number(item.latitude)])
      // 格式化设备属性
      const attrs = {
        lng: lngLat[0], // longitude
        lat: lngLat[1], // latitude
        deviceCode: item.deviceCode,
        deviceName: item.deviceName,
        location: item.location,
        deviceStatus: item.deviceStatus,
        height: item.height,
        ...item
      };
      const [small, large] = this.getIcon(attrs); // 获取设备图标
      const marker = baseMarker({
        point: [attrs.lng, attrs.lat], // 标记位置
        markerAttr: {
          markerType: LayerConst.LOUDSPEAKER, // 标记类型
          ds: attrs // 标记属性
        },
        initIcon: small, // 初始图标
        selectedIcon: large, // 选中图标
        anchorCfg: this, // 锚点配置
        onclick: (attr, markerFeature, feature) => {
          onclick(attr, markerFeature); // 执行点击事件
          const { ds, selected } = attr;
          const markerId = `${LayerConst.LOUDSPEAKER}-${item.deviceStatus}-${ds.deviceCode}`; // 构造标记ID
          if (selected) {
            this.onSelected(marker); // 处理选中状态
            this.openDetail(item, (key, param) => {
              if (param && param.destroy) {
                return;
              }
              this.cancelSelected(); // 取消选中状态
            });
            return;
          }
          closeLeftSideWindow(markerId, LayerConst.LOUDSPEAKER); // 关闭左侧窗口
        }
      });
      // 根据设备状态分组标记
      if ([
        enums.LOUDSPEAKER.deviceStatus.fault.code,
        enums.LOUDSPEAKER.deviceStatus.offline.code
      ].includes(item.deviceStatus)) {
        faultMarkers.push(marker); // 故障标记
        return;
      }
      markers.push(marker); // 正常标记
    });
    this.cfg.faultMarkers = faultMarkers; // 更新故障标记配置
    this.cfg.otherMarkers = markers; // 更新其他标记配置
    const showMarkers = this.getMarker(); // 获取显示的标记
    this.cfg.markersLayer = useClusterLayer({
      markers: showMarkers, // 标记集合
      map: mapInstance, // 地图实例
      type: LayerConst.LOUDSPEAKER, // 标记类型
      onclick: this.onClusterClick // 集群点击事件处理
    });
    this.renderPlayingLoudSpeaker(playingData); // 渲染播放中的动效
    return {
      markersLayer: this.cfg.markersLayer, // 返回标记图层
      markers // 返回标记集合
    };
  },
  async getData(initData, region) {
    let layerData = initData; // 初始化图层数据
    if (!layerData) {
      const data = vue.$store.state.mockDataStore.allLoudSpeakerData; // 从Vuex中获取数据
      layerData = data;
    }
    if (!layerData || layerData.length < 1) {
      return []; // 如果数据为空，返回空数组
    }
    return layerData; // 返回图层数据
  },
  /**
   * 广播播放中/结束播放图标切换
   */
  afterPlayingChangeIcon(playingDeviceCodes = [], endPlayingDeviceCodes = []) {
    const markers = this.getMarker(); // 获取标记集合
    playingDeviceCodes.forEach(deviceCode => {
      const marker = markers.find(item => item.values_.attributes.ds.deviceCode === deviceCode); // 查找播放中的设备
      const selected = marker.values_.attributes.selected; // 获取选中状态
      const icon = selected ? broadcastIcon.iconBroadcastPlayingL : broadcastIcon.iconBroadcastPlayingS; // 根据选中状态选择图标
      const anchor = selected ? this.largeAnchor : this.smallAnchor; // 根据选中状态选择锚点
      marker.style_ = calMakerStyle(icon, anchor); // 更新标记样式
    });
    endPlayingDeviceCodes.forEach(deviceCode => {
      const marker = markers.find(item => item.values_.attributes.ds.deviceCode === deviceCode); // 查找结束播放的设备
      const selected = marker.values_.attributes.selected; // 获取选中状态
      const icon = selected ? broadcastIcon.iconBroadcastOnL : broadcastIcon.iconBroadcastOnS; // 根据选中状态选择图标
      const anchor = selected ? this.largeAnchor : this.smallAnchor; // 根据选中状态选择锚点
      marker.style_ = calMakerStyle(icon, anchor); // 更新标记样式
    });
    this.cfg.markersLayer.setSource(createSource({
      markers, // 更新标记集合
      type: LayerConst.LOUDSPEAKER, // 标记类型
      onclick: this.onClusterClick // 集群点击事件处理
    }));
  },
  renderPlayingLoudSpeaker(devices) {
    const markers = this.getMarker(); // 获取标记集合
    // const selectedDeviceCodes = markers.filter(marker => marker.values_.attributes.selected).map(marker => marker.values_.attributes.ds.deviceCode); // 获取选中的设备代码
    const playingDeviceCodes = []; // 播放中的设备代码集合
    const endPlayingDeviceCodes = []; // 结束播放的设备代码集合
    const newPlayingBgIds = devices.map(i => `LoudSpeakerPlayingBg_${i.deviceCode}`); // 新的播放背景ID集合
    const setNewPlayingBgIds = new Set(newPlayingBgIds); // 转换为集合
    const removeIds = this.cfg.loudSpeakerPlayingBgIds.filter(id => !setNewPlayingBgIds.has(id)); // 需要移除的ID集合
    removeIds.forEach(id => {
      const content = document.getElementById(id); // 获取DOM元素
      content && content.remove(); // 移除元素
      endPlayingDeviceCodes.push(id.replace('LoudSpeakerPlayingBg_', '')); // 更新结束播放的设备代码集合
      delete this.cfg.loudSpeakerPlayingBgOverlays[id]; // 删除覆盖物
    });
    this.cfg.loudSpeakerPlayingBgIds = this.cfg.loudSpeakerPlayingBgIds.filter(id => setNewPlayingBgIds.has(id)); // 更新播放背景ID集合
    devices.forEach(device => {
      const { deviceCode, longitude, latitude } = device; // 获取设备信息
      const playingBgId = `LoudSpeakerPlayingBg_${deviceCode}`; // 构造播放背景ID
      if (!this.cfg.loudSpeakerPlayingBgIds.includes(playingBgId)) {
        this.cfg.loudSpeakerPlayingBgIds.push(playingBgId); // 添加到播放背景ID集合
        playingDeviceCodes.push(deviceCode); // 添加到播放中的设备代码集合
        // const offsetX = selectedDeviceCodes.includes(deviceCode) ? 25 : 20; // 根据选中状态设置偏移量
        // const offsetY = selectedDeviceCodes.includes(deviceCode) ? -45 : -35; // 根据选中状态设置偏移量
        // const overlayIns = CommonMap.infoWindow(longitude * 1, latitude * 1,
        //     CommonMap.componentToHtml({
        //         component: LoudSpeakerPlayingBg, // 使用播放背景组件
        //         props: {},
        //         onClose: () => {
        //             return null;
        //         }
        //     }), 'popup',
        //     offsetX, offsetY, playingBgId, mapInstance,
        //     false, 'mainMap', () => {
        //         console.log('default implement');
        //     }, false);
        // this.cfg.loudSpeakerPlayingBgOverlays[playingBgId] = overlayIns; // 更新覆盖物集合
      }
    });
    this.afterPlayingChangeIcon(playingDeviceCodes, endPlayingDeviceCodes); // 更新图标
  },
  hiddenAllPlayingBg() {
    if (!this.cfg.showLoudSpeakerPlayingBg) {
      return; // 如果已经隐藏则返回
    }
    Object.keys(this.cfg.loudSpeakerPlayingBgOverlays).forEach(id => {
      this.cfg.loudSpeakerPlayingBgOverlays[id].getElement().style.display = 'none'; // 隐藏覆盖物
    });
    this.cfg.showLoudSpeakerPlayingBg = false; // 更新显示状态
  },
  showAllPlayingBg() {
    if (this.cfg.showLoudSpeakerPlayingBg) {
      return; // 如果已经显示则返回
    }
    Object.keys(this.cfg.loudSpeakerPlayingBgOverlays).forEach(id => {
      this.cfg.loudSpeakerPlayingBgOverlays[id].getElement().style.display = 'block'; // 显示覆盖物
    });
    this.cfg.showLoudSpeakerPlayingBg = true; // 更新显示状态
  },
  removeAllPlayingBg() {
    this.cfg.loudSpeakerPlayingBgIds.forEach(id => {
      const content = document.getElementById(id); // 获取DOM元素
      content && content.remove(); // 移除元素
      delete this.cfg.loudSpeakerPlayingBgOverlays[id]; // 删除覆盖物
    });
    this.cfg.loudSpeakerPlayingBgIds = []; // 清空播放背景ID集合
  },
  /**
   * 当标记被选中时触发的函数。
   * @param {Object} marker 被选中的标记对象。
   */
  onSelected(marker) {
    const playingDevcs = vue.$store.state.mockDataStore.curPlayingLoudSpeakerData.map(item => item.deviceCode); // 获取播放中的设备代码集合
    const [, large] = this.getIcon(marker.values_.attributes.ds); // 获取大图标
    const icon = playingDevcs.includes(marker.values_.attributes.ds.deviceCode) ? broadcastIcon.iconBroadcastPlayingL : large; // 根据播放状态选择图标
    const markers = this.getMarker(); // 获取标记集合
    const selectedMarker = markers.find(item => item.values_.attributes.ds.deviceCode === marker.values_.attributes.ds.deviceCode); // 查找选中的标记
    selectedMarker.style_ = calMakerStyle(icon, this.largeAnchor); // 更新选中标记样式
    selectedMarker.values_.attributes.selected = true; // 设置选中状态
    this.cfg.markersLayer.setSource(createSource({
      markers, // 更新标记集合
      type: LayerConst.LOUDSPEAKER, // 标记类型
      onclick: this.onClusterClick // 集群点击事件处理
    }));
    const { deviceCode } = selectedMarker.values_.attributes.ds; // 获取设备代码
    if (this.cfg.loudSpeakerPlayingBgOverlays[`LoudSpeakerPlayingBg_${deviceCode}`]) {
      this.cfg.loudSpeakerPlayingBgOverlays[`LoudSpeakerPlayingBg_${deviceCode}`].setOffset([25, -45]); // 更新播放动效偏移
    }
  },
  /**
   * 取消选中状态的函数。
   */
  cancelSelected() {
    const markers = this.getMarker(); // 获取标记集合
    if (!isNotEmpty(markers)) {
      return; // 如果标记为空则返回
    }
    this.initMarkers(markers); // 初始化标记样式
    this.cfg.markersLayer.setSource(createSource({
      markers, // 更新标记集合
      type: LayerConst.LOUDSPEAKER, // 标记类型
      onclick: this.onClusterClick // 集群点击事件处理
    }));
  }, // 初始化marker点
  /**
   * 初始化标记的函数。
   * @param {Array} markers 标记数组。
   */
  initMarkers(markers) {
    const playingDevcs = vue.$store.state.mockDataStore.curPlayingLoudSpeakerData.map(item => item.deviceCode); // 获取播放中的设备代码集合
    markers.forEach(marker => {
      const [small] = this.getIcon(marker.values_.attributes.ds); // 获取小图标
      const { deviceCode } = marker.values_.attributes.ds; // 获取设备代码
      let icon = small; // 默认使用小图标
      if (playingDevcs.includes(deviceCode)) {
        icon = broadcastIcon.iconBroadcastPlayingS; // 如果正在播放，使用播放图标
        if (this.cfg.loudSpeakerPlayingBgOverlays[`LoudSpeakerPlayingBg_${deviceCode}`]) {
          this.cfg.loudSpeakerPlayingBgOverlays[`LoudSpeakerPlayingBg_${deviceCode}`].setOffset([20, -35]); // 更新播放动效偏移
        }
      }
      marker.values_.attributes.selected = false; // 设置为未选中
      marker.style_ = calMakerStyle(icon, this.smallAnchor); // 更新标记样式
    });
  },
  /**
   * 获取标记数组的函数。
   * @returns {Array} 标记数组。
   */
  getMarker() {
    const { faultMarkers, otherMarkers, showFault } = this.cfg; // 获取配置
    if (showFault) {
      return [...faultMarkers, ...otherMarkers]; // 如果显示故障，返回所有标记
    }
    return otherMarkers; // 否则返回正常标记
  },
  /**
   * 清除所有标记的函数。
   */
  clear() {
    closeLeftSideWindowByIdPrefix(`${LayerConst.LOUDSPEAKER}-`, LayerConst.LOUDSPEAKER); // 关闭所有LOUDSPEAKER类型的左侧窗口
  },
  /**
   * 根据设备状态获取图标的函数。
   * @param {Object} data 设备状态数据。
   * @returns {Array} 图标URL数组。
   */
  getIcon(data) {
    const { deviceStatus } = data; // 获取设备状态
    if (deviceStatus === enums.LOUDSPEAKER.deviceStatus.idle.code) {
      return [broadcastIcon.iconBroadcastOnS, broadcastIcon.iconBroadcastOnL]; // 空闲状态图标
    }
    if (deviceStatus === enums.LOUDSPEAKER.deviceStatus.playing.code) {
      return [broadcastIcon.iconBroadcastPlayingS, broadcastIcon.iconBroadcastPlayingL]; // 播放状态图标
    }
    if (deviceStatus === enums.LOUDSPEAKER.deviceStatus.todayExistsFail.code) {
      return [broadcastIcon.iconBroadcastErrS, broadcastIcon.iconBroadcastErrL]; // 故障状态图标
    }
    return [broadcastIcon.iconBroadcastOffS, broadcastIcon.iconBroadcastOffL]; // 离线状态图标
  },
  /**
   * 过滤故障标记的函数。
   * @param {Object} param0 过滤配置。
   * @param {Object} param0.layer 标记图层。
   * @param {boolean} param0.show 是否显示故障。
   */
  filterFault({ layer, show }) {
    this.cfg.showFault = show; // 更新显示故障的配置
    const markers = this.getMarker(); // 获取标记集合
    this.initMarkers(markers); // 初始化标记样式
    layer.setSource(createSource({
      markers, // 更新标记集合
      type: LayerConst.LOUDSPEAKER, // 标记类型
      onclick: this.onClusterClick // 集群点击事件处理
    }));
    closeLeftSideWindowByIdPrefix(`${LayerConst.LOUDSPEAKER}-`, LayerConst.LOUDSPEAKER); // 关闭所有LOUDSPEAKER类型的左侧窗口
  },
  /**
   * 打开详情页面的函数。
   * @param {Object} selectedData 选中的设备数据。
   * @param {Function} onclose 关闭详情页面的回调函数。
   */
  openDetail(selectedData, onclose) {
    const markerId = `${LayerConst.LOUDSPEAKER}-${selectedData.deviceStatus}-${selectedData.deviceCode}`; // 构造详情页面的窗口ID
    openLeftSideWidow({
      windowId: markerId, // 打开详情窗口
      props: {
        ds: { ...selectedData }, // 传递设备数据
        markerType: LayerConst.LOUDSPEAKER // 标记类型
      },
      group: LayerConst.LOUDSPEAKER, // 详情页面关闭回调
      onclose
    });
  },
  /**
   * 集群点击事件的处理函数。
   * @param {Object} event 点击事件对象。
   * @param {Array} event.features 点击的特征数组。
   * @param {Object} event.geometry 点击的几何对象。
   */
  onClusterClick({ features, geometry }) {
    if (!features || features.length < 1) {
      return; // 如果没有特征则返回
    }
    const ds = features.map(feature => {
      const data = feature.values_.attributes.ds; // 获取设备数据
      return {
        key: data.deviceCode, // 设备代码
        type: '云广播', // 设备类型
        name: data.deviceName, // 设备名称
        status: data.deviceStatus, // 设备状态
        statusName: enums.LOUDSPEAKER.deviceStatusGroup[data.deviceStatus]?.name, // 状态名称
        color: enums.LOUDSPEAKER.deviceStatusGroup[data.deviceStatus]?.color, // 状态颜色
        ...data
      };
    });
    const [lng, lat] = geometry.getCoordinates(); // 获取几何对象的坐标
    clusterTip({
      ds, // 设备数据
      markerType: LayerConst.LOUDSPEAKER, // 标记类型
      lng, // 经度
      lat // 纬度
    });
  }
};
export default LOUDSPEAKER; // 导出LOUDSPEAKER对象▊
