import { getUserInfo } from "@/api/service/common";
import ConstEnum from '@/components/common/ConstEnum';
import RegionTreeData from "./RegionTreeData";

export const mockUserProvinceCityData = async () => {
    // 异步获取用户信息
    const { user } = await getUserInfo();
    // 获取地区树数据
    const { data } = RegionTreeData;
    // 将地区树数据赋值给用户对象
    user.areaTree = data;
    // 将用户信息挂载到全局window对象上，以便其他地方可以访问
    window.userInfo = user;
    // 检查用户地区树是否存在且至少有一个元素
    if (!user.areaTree || user.areaTree.length < 1) {
      return [false];
    }
    // 如果地区树的第一个元素ID为'100000'，则该元素代表全国，将其子节点赋值给areaTree
    if (user.areaTree[0].id === '100000') {
      user.areaTree = user.areaTree[0].children;
    }
    // 定义初始区域层级为省份
    let regionLevel = ConstEnum.REGION_LEVEL.province;
    // 初始化区域编码为空字符串
    let regionCode = '';
    // 根据地区树的结构判断默认区域层级和编码
    if (user.areaTree[0].children[0].children.length < 2) {
      // 如果省份下第一个城市的区县少于2个，则默认区域层级为区县
      regionLevel = ConstEnum.REGION_LEVEL.district;
      regionCode = user.areaTree[0].children[0].children[0].id;
      // 设置默认区域的ID数组
      window.userInfo.defArea = [
        user.areaTree[0].id,
        user.areaTree[0].children[0].id,
        user.areaTree[0].children[0].children[0].id
      ];
      // 设置默认区域的名称
      window.userInfo.defAreaName = user.areaTree[0].children[0].children[0].label;
    } else if (user.areaTree[0].children.length < 2) {
      // 如果省份下城市少于2个，则默认区域层级为城市
      regionLevel = ConstEnum.REGION_LEVEL.city;
      regionCode = user.areaTree[0].children[0].id;
      // 设置默认区域的ID数组
      window.userInfo.defArea = [
        user.areaTree[0].id,
        user.areaTree[0].children[0].id
      ];
      // 设置默认区域的名称
      window.userInfo.defAreaName = user.areaTree[0].children[0].label;
    } else {
      // 否则默认区域层级为省份
      regionLevel = ConstEnum.REGION_LEVEL.province;
      regionCode = user.areaTree[0].id;
      // 设置默认区域的ID数组
      window.userInfo.defArea = [
        user.areaTree[0].id
      ];
      // 设置默认区域的名称
      window.userInfo.defAreaName = user.areaTree[0].label;
    }

    // 创建一个临时数组用于存储处理后的地址信息
    let addressTemp = [];
    // 遍历用户地区树
    user.areaTree.forEach((item) => {
      // 设置当前节点的value为其id
      item.value = item.id;
      // 设置当前节点的区域层级为省份
      item.regionLevel = ConstEnum.REGION_LEVEL.province;
      // 如果当前节点有子节点
      if (item.children && item.children.length > 0) {
        // 创建一个临时数组用于存储子节点
        let sonLevelList = [];
        // 遍历子节点
        item.children.forEach((secondChild) => {
          // 设置子节点的value为其id
          secondChild.value = secondChild.id;
          // 设置子节点的区域层级为城市
          secondChild.regionLevel = ConstEnum.REGION_LEVEL.city;
          // 将子节点添加到临时数组中
          sonLevelList.push(secondChild);
          // 如果子节点有进一步的子节点
          if (secondChild.children && secondChild.children.length > 0) {
            // 创建一个临时数组用于存储孙子节点
            let thirdLevelList = [];
            // 遍历孙子节点
            secondChild.children.forEach((thirdChild) => {
              // 设置孙子节点的value为其id
              thirdChild.value = thirdChild.id;
              // 删除孙子节点的子节点，简化数据结构
              delete thirdChild.children;
              // 设置孙子节点的区域层级为区县
              thirdChild.regionLevel = ConstEnum.REGION_LEVEL.district;
              // 将孙子节点添加到临时数组中
              thirdLevelList.push(thirdChild);
            });
            // 将孙子节点数组赋值给子节点的children属性
            secondChild.children = thirdLevelList;
            // 如果孙子节点少于2个，则禁用该子节点
            secondChild.disabled = thirdLevelList.length < 2;
          }
        });
        // 将子节点数组赋值给当前节点的children属性
        item.children = sonLevelList;
        // 如果子节点少于2个，则禁用当前节点
        item.disabled = sonLevelList.length < 2;
      }
      // 将处理后的当前节点添加到临时数组中
      addressTemp.push(item);
    });
    // 将处理后的地址信息数组挂载到全局window对象上，以便其他地方可以访问
    window.userInfo.roleAreaTree = addressTemp;
    // 返回一个数组，包含布尔值true和一个对象，对象包含regionLevel和regionCode
    return [
      true, {
        regionLevel,
        regionCode
      }
    ];
  };

