<template>
  <div>
    <NewCardCarousel
      ref="carousel"
      :carouselList="captureList"
      :on-loaded="carouselLoaded"
      :on-change="carouselChange"
      :playing="playing"
    />
    <!-- 使用 NewCardCarousel 组件，绑定 captureList 数据和事件处理函数 -->
    <ProcessBar
      :currentHour="currentHour"
      :playing="playing"
      @changeDate="changeDate"
      @timeClickHandle="timeClickHandle"
      @playChange="playChange"
    />
  </div>
</template>
<script>
import CommonMap from '@/components/common/Map/CommonMap'
import PassCurrent from '@/components/page/ScreenPages/IntegratedMonitor/InfoWindow/PassCurrent.vue'
import StationInfo from '@/components/page/ScreenPages/IntegratedMonitor/InfoWindow/StationInfo.vue'
import NewCardCarousel from '@/components/page/ScreenPages/IntegratedMonitor/PassRoute/NewCardCarousel/index.vue'
import ProcessBar from '@/components/page/ScreenPages/IntegratedMonitor/PassRoute/ProcessBar/index.vue'
import Station from '@/components/page/ScreenPages/IntegratedMonitor/PassRoute/Station'
import dayjs from 'dayjs'
import {
  getEvtDevcCaptureByPage,
  getEvtDevcCaptureDetail,
} from '@/api/service/imScreenService'
import PassRouteLine from '@/components/page/ScreenPages/IntegratedMonitor/PassRoute/PassRouteLine'
import { debounce } from 'lodash'
import { transformPointToMercator } from '@/utils'

// 缓存对象，用于存储图层信息
const layerCache = {
  getLayer: layer => {
    return layerCache[layer] || {}
  },
}

// 定时器缓存对象，用于存储定时器信息
const timerCache = {
  captureEndTime: null,
}

// 当前过车点位的标识数组
const curPointIds = ['pass-current-point', 'pass-current-point-1']
// 左侧站点的标识数组
const leftSideIds = ['pass-sele-22-1', 'pass-sele-22-2']
// 右侧站点的标识数组
const rightSideIds = ['pass-sele-13-1', 'pass-sele-13-2']

// 单个站点的渲染实例
let singleStationIns = null

// 左侧站点的渲染实例
let leftStationIns = null

// 右侧站点的渲染实例
let rightStationIns = null

// 线路渲染实例
let passRouteLineIns = null

let routeMap = null // 地图

export default {
  inject: ['mapRef'],
  components: {
    NewCardCarousel,
    StationInfo,
    PassCurrent,
    ProcessBar,
  },
  props: {
    onMapLoad: {
      type: Function,
      default: () => {
        console.log('onMapLoad')
      },
    },
    mapId: {
      type: String,
    },
  },
  data() {
    /**
     * serverFlag: 0-服务端，1-前端
     */
    const [serverFlag, timerLong] = window.sysConfig.IS_SHOW_CAPTURE_TIME
    return {
      serverFlag,
      timerLong,
      currentInfo: null,
      currentPoint: null,
      stationsLayer: null,
      carouseId: 0,
      captureList: [],
      loaded: false,
      // 固定初始化的查询日期
      statTime: '2025-03-26',
      currentHour: 0,
      playing: false,
      curPassRoutePointId: curPointIds[1],
      curLeftSideId: leftSideIds[1],
      curRightSideId: rightSideIds[1],
    }
  },
  computed: {
    selectedRegion() {
      return this.$store.state.map.selectedRegion
    },
    curSelectCamera() {
      return this.$store.state.captureInfo.curSelectCamera
    },
  },
  watch: {
    curSelectCamera: {
      handler: function (newVal, oldVal) {
        if (newVal.deviceCode !== oldVal.deviceCode) {
          this.refreshData()
        }
      },
    },
    selectedRegion: {
      handler: function (newVal, oldVal) {
        if (newVal) {
          this.refreshData()
        }
      },
    },
    currentInfo: {
      handler: function (newVal, oldVal) {
        if (newVal) {
          this.drawPassRouteInfo()
        }
      },
      immediate: false,
    },
  },
  created() {
    routeMap = this.mapRef.getMapRef(this.mapId)
  },
  mounted() {
    this.getData(true)
    // this.refreshTodayData()
  },
  beforeDestroy() {
    this.clearMap()
    if (timerCache.routeTimer) {
      clearInterval(timerCache.routeTimer)
    }
  },
  methods: {
    // 切换区域或者切换摄像机时，清除地图上的相关资源
    refreshData() {
      this.clearMap()
      this.playChange(false)
      this.loaded = false
      this.currentInfo = null
      this.currentPoint = null
      this.currentHour = 0
      this.captureList = []
      this.getData(true)
      // if (this.isToday()) {
      //   this.refreshTodayData()
      // }
    },
    /**
     * 刷新今日数据
     */
    // refreshTodayData() {
    //   return
    //   timerCache.routeTimer && clearInterval(timerCache.routeTimer)
    //   if (this.serverFlag) {
    //     // 5分钟定时刷新铁路线路
    //     timerCache.routeTimer = setInterval(() => {
    //       this.getData()
    //     }, 60 * 1000 * this.timerLong)
    //   }
    // },
    /**
     * 时间轴的点击事件
     */
    timeClickHandle(hour) {
      const index = this.captureList.findIndex(item => {
        return item.captHour === hour
      })
      if (index > -1) {
        this.currentHour = hour
        this.playChange(false)
        this.$refs.carousel?.cutSwiper(index)
      } else {
        this.$message({
          type: 'warn',
          message: '此时刻无过车数据',
        })
      }
    },
    /**
     * 播放状态改变
     */
    playChange(flag) {
      this.playing = flag
      if (flag) {
        const index = this.captureList.findIndex(item => {
          return item.orderId === this.currentInfo.orderId
        })
        if (index === this.captureList.length - 1) {
          this.$refs.carousel?.cutSwiper(0)
        }
        this.$refs.carousel?.startPlay()
      } else {
        this.$refs.carousel?.stopPlay()
      }
    },
    /**
     * 判断是否为今天
     * @param {string} date - 日期字符串，格式为YYYY-MM-DD。
     * @returns {boolean} - 如果日期是今天，则返回true，否则返回false。
     */
    isToday(date = this.statTime) {
      return date === dayjs().format('YYYY-MM-DD')
    },
    /**
     * 时间进度条改变日期的函数。
     * currentTime:YYYY-MM-DD
     */
    changeDate(currentTime) {
      this.playChange(false)
      this.clearMap()
      this.currentInfo = null
      this.currentHour = 0
      if (this.isToday(currentTime)) {
        // 如果是当天，需要重置加载标志位
        this.loaded = false
        // this.refreshTodayData()
      } else {
        // 如果日期不是当天，则定时刷新数据任务结束
        timerCache.routeTimer && clearInterval(timerCache.routeTimer)
        timerCache.routeTimer = null
      }
      this.statTime = currentTime
      // 重新获取数据
      this.getData(true)
    },
    /**
     * 轮播图加载完成后的处理函数。
     * 目前没有具体的实现内容，可能是初始化轮播图的相关设置或者加载数据。
     */
    carouselLoaded() {
      // 轮播图加载完成后的处理
    },

    /**
     * 当轮播图项改变时触发的函数。
     * @param {number} newVal - 当前轮播图项的索引。
     * 根据新的索引更新当前关注的点，并重绘路线和站点。
     */
    carouselChange(newVal) {
      this.currentInfo = this.captureList[newVal]
      if (!this.currentHour) {
        this.currentHour = 0
        return
      }
      // 时间轴匹配
      this.currentHour = this.currentInfo.captHour
      // 如果是最后一项
      if (newVal === this.captureList.length - 1) {
        this.playChange(false)
      }
    },
    /**
     * 移除上一个
     */
    removeLastInfo() {
      if (passRouteLineIns) {
        passRouteLineIns.removePassRouteLine()
        passRouteLineIns = null
      }
      if (leftStationIns) {
        leftStationIns.removeLeftSide()
        leftStationIns = null
      }
      if (rightStationIns) {
        rightStationIns.removeRightSide()
        rightStationIns = null
      }
      if (singleStationIns) {
        singleStationIns.removeFocusStationWithAnimation()
        singleStationIns = null
      }
    },
    /**
     * 绘制地图上的相关资源
     */
    drawPassRouteInfo: debounce(function () {
      this.debounceDrawPassRouteInfo()
    }, 300),
    async debounceDrawPassRouteInfo() {
      const [resSuccess, resData] = await getEvtDevcCaptureDetail({
        orderId: this.currentInfo.orderId,
      })
      const data = resData
      const success = resSuccess
      this.removeLastInfo()
      if (success && data && data?.capture) {
        const { capture, link } = data
        this.currentPoint = {
          ...capture,
          shape: link?.[capture?.linkId]?.shape || '',
        }
        // 绘制站点
        this.drawPoints()
        // 绘制线路
        this.drawRouteV2()
      }
    },
    /**
     * 绘制路线的函数。
     * 根据当前点的信息，解析形状数据并绘制多边形路线。
     * 如果当前点没有形状数据，则不执行绘制操作。
     */
    drawRouteV2() {
      if (!this.currentPoint || !this.currentPoint.shape) {
        return
      }
      const pointArray = CommonMap.wktToPoint(this.currentPoint.shape)[0]
      passRouteLineIns = new PassRouteLine({
        props: {},
        pointArray,
        map: routeMap.mapInstance,
      })
    },

    /**
     * 绘制站点的函数。
     * 根据当前点的信息，绘制上一站、下一站和当前站，并调整地图范围以适应所有站点。
     * 如果当前点信息不完整，则不执行绘制操作。
     */
    drawPoints() {
      if (!this.currentPoint) {
        return
      }
      const { lon, lat, segmentName, captAddr, startStation, endStation } =
        this.currentPoint
      // 当前站点上一站点
      if (startStation && startStation.lon && startStation.lat) {
        const lng_lat = transformPointToMercator([
          Number(startStation.lon),
          Number(startStation.lat),
        ])
        const leftSideId =
          this.curLeftSideId === leftSideIds[1]
            ? leftSideIds[0]
            : leftSideIds[1]
        this.curLeftSideId = leftSideId
        leftStationIns = new Station({
          id: leftSideId,
          props: {
            name: startStation.stationName,
          },
          lng: lng_lat[0],
          lat: lng_lat[1],
          map: routeMap.mapInstance,
          className: 'fadeInStyle',
        }).drawSideLeft()
      }
      // 当前站点下一站点
      if (endStation && endStation.lon && endStation.lat) {
        const lng_lat = transformPointToMercator([
          Number(endStation.lon),
          Number(endStation.lat),
        ])
        const rightSideId =
          this.curRightSideId === rightSideIds[1]
            ? rightSideIds[0]
            : rightSideIds[1]
        this.curRightSideId = rightSideId
        rightStationIns = new Station({
          id: rightSideId,
          props: {
            name: endStation.stationName,
          },
          lng: lng_lat[0],
          lat: lng_lat[1],
          map: routeMap.mapInstance,
          className: 'fadeInStyle',
        }).drawSideRight()
      }
      // 当前站点，过车点位绘制单独处理
      this.drawPassRoutePoint({
        segmentName,
        captAddr,
        lon,
        lat,
      })
    },

    drawPassRoutePoint({ segmentName, captAddr, lon, lat }) {
      const passRouteLngLat = transformPointToMercator([
        Number(lon),
        Number(lat),
      ])
      const id =
        this.curPassRoutePointId === curPointIds[1]
          ? curPointIds[0]
          : curPointIds[1]
      this.curPassRoutePointId = id
      // 当前站点
      const stationIns = new Station({
        id,
        props: {
          segName: segmentName,
          address: captAddr,
        },
        lng: passRouteLngLat[0],
        lat: passRouteLngLat[1],
        map: routeMap.mapInstance,
        className: 'fadeInStyle',
      })
      stationIns.drawFocusStationWithAnimation()
      singleStationIns = stationIns
      // 地图居中，但中心点向上移一点
      const _lat = lat > 0 ? lat - 0.025 : lat
      const passRouteLngLatCenter = transformPointToMercator([
        Number(lon),
        Number(_lat),
      ])
      routeMap.mapInstance
        .getView()
        .animate({ center: passRouteLngLatCenter, duration: 2000 })
    },

    /**
     * 获取查询参数的函数。
     * 根据当前的时间和选中的区域格式化查询参数。
     * 如果服务器标志位为真，还会添加抓图的起止时间。
     * @returns {Object} 查询参数对象。
     */
    getParams() {
      const params = {
        alarmTimeStart: `${this.statTime} 00:00:00`,
        alarmTimeEnd: `${this.statTime} 23:59:59`,
        pageNum: 1,
        pageSize: 99999,
        deviceCode: this.curSelectCamera.deviceCode,
      }
      // 如果是当天，时间参数需要重置
      if (this.isToday()) {
        // 如果当天数据还未加载过
        Object.assign(params, {
          alarmTimeStart: `${this.statTime} 00:00:00`,
          alarmTimeEnd: `${this.statTime} 23:59:59`, // `${this.statTime} ${dayjs().format('HH:mm:ss')}`,
        })
        // 如果当天数据已经加载过，则增量查询数据
        if (this.loaded) {
          Object.assign(params, {
            alarmTimeStart: timerCache.captureEndTime,
            alarmTimeEnd: `${this.statTime} 23:59:59`, // `${this.statTime} ${dayjs().format('HH:mm:ss')}`,
          })
        }
        timerCache.captureEndTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
      }
      return params
    },
    formatCaptureData(data) {
      // 数据是倒序排序，前端正序排序展示
      return (data || [])
        .map(item => {
          return {
            ...item,
            captHour: dayjs(item.alarmTime, 'YYYY-MM-DD HH:mm:ss').hour(),
          }
        })
        .reverse()
    },
    /**
     * 处理今天的数据。
     * @param {boolean} init - 是否初始化。
     * @param {boolean} success - 是否成功。
     * @param {Object} data - 数据对象。
     */
    todayDataDispose(init, success, data) {
      let result = init ? [] : [...this.captureList]
      const addData = success ? this.formatCaptureData(data.rows) : []
      result.push(...addData)
      this.captureList = result
      // 当天的数据如果没加载过，那么从第一条开始展示
      if (!this.loaded) {
        this.currentInfo = this.captureList[0]
        this.currentHour = this.currentInfo ? this.currentInfo.captHour : 0
        this.$refs.carousel?.updateInitialSlide(0)
      }
      this.loaded = true
      if (addData.length > 0) {
        this.playChange(true)
      }
    },
    /**
     * 处理历史的数据。
     * @param {boolean} success - 是否成功。
     * @param {Object} data - 数据对象。
     */
    historyDataDispose(success, data) {
      this.$refs.carousel?.updateInitialSlide(0)
      this.captureList = success ? this.formatCaptureData(data.rows) : []
      this.currentInfo = this.captureList[0]
      this.currentHour = this.currentInfo.captHour
      if (this.captureList.length > 0) {
        this.playChange(true)
      }
    },
    /**
     * 异步获取数据的函数。
     * 根据当前的查询参数获取抓拍路线和图片链接。
     * 如果请求成功，更新抓拍列表，并根据需要更新当前显示的抓拍点和加载状态。
     */
    async getData(init = false) {
      const params = this.getParams()
      const [resSuccess, resData] = await getEvtDevcCaptureByPage(params)
      const data = resData
      const success = resSuccess
      if (!success) {
        this.$message({
          type: 'error',
          message: '过车数据查询异常',
        })
      }
      // 当天的处理逻辑和历史的分开
      if (this.isToday()) {
        this.todayDataDispose(init, success, data)
      } else {
        this.historyDataDispose(success, data)
      }
    },
    /**
     * 清除地图上所有标记和路线的函数。
     * 主要用于重置地图状态，以便显示新的数据。
     */
    clearMap() {
      if (passRouteLineIns) {
        passRouteLineIns.removePassRouteLine()
        passRouteLineIns = null
      }
      singleStationIns = null
      passRouteLineIns = null
      leftStationIns = null
      rightStationIns = null
      Station.clearStationByKey(curPointIds)
      Station.clearStationByKey(leftSideIds)
      Station.clearStationByKey(rightSideIds)
    },
  },
}
</script>
<style
  lang="less"
  src="../../../ScreenPages/IntegratedMonitor/PassRoute/index.less"
/>
