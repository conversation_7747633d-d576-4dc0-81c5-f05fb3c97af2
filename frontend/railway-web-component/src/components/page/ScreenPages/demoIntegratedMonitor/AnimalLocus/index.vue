<!--
 * @Description: 控制面板组件，用于畜牧详情-历史轨迹查询
 * @Author: liu.yongli
 * @Date: 2024-07-18 18:30:53
 * @LastEditTime: 2024-07-20 12:38:14
 * @LastEditors: liu.yongli
-->
<template>
  <!-- 控制面板组件，用于畜牧详情-历史轨迹查询 -->
  <CtlPanel class="animal_locus_main" :close="doClose">
    <!-- 标题区域 -->
    <template v-slot:title>轨迹查询</template>
    <template v-slot:content>
      <div class="animal_locus_main_content">
        <div class="animal_locus_item">
          <div class="animal_locus_item_title">
            <!-- 查询时间标题，红色星号表示必填项 -->
            <span :style="{ color: '#ff0000' }">*</span>查询时间
          </div>
          <div class="animal_locus_item_value">
            <el-date-picker
              v-model="selecteTimeArr"
              type="datetimerange"
              :popper-class="'screen_animal_locus'"
              :picker-options="pickerOptions"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              :clearable="false"
              @change="timeChange"
            ></el-date-picker>
          </div>
        </div>
        <div class="animal_locus_item">
          <div class="animal_locus_item_title">轨迹播放</div>
          <div class="animal_locus_item_value">
            <!-- 滑动条，用于控制轨迹播放进度 -->
            <el-slider
              v-model="currentIndex"
              :format-tooltip="formatTooltip"
              :max="maxIndex"
              @change="onSliderChange"
            ></el-slider>
          </div>
        </div>
      </div>
      <div class="animal_locus_main_btns">
        <!-- 开始按钮，点击后查询历史轨迹 -->
        <div class="animal_locus_main_btns_start" v-if="!isPlay" @click="queryHistoryTrack">开始</div>
        <!-- 暂停按钮，点击后暂停播放 -->
        <div class="animal_locus_main_btns_start" v-if="isPlay" @click="onStop">暂停</div>
        <!-- 重置按钮，点击后重置播放状态 -->
        <div class="animal_locus_main_btns_reset" @click="onReset">重置</div>
      </div>
    </template>
  </CtlPanel>
</template>

<script>
// import api from '@/api';
import dayjs from 'dayjs'
import duration from 'dayjs/plugin/duration'
// 扩展 duration 功能
dayjs.extend(duration)
import CtlPanel from '@/components/page/ScreenPages/IntegratedMonitor/CtlPanel'
import { Events } from '@/components/page/ScreenPages/IntegratedMonitor/enum/EventsEnum'
import AnimalLocusData from '../mockData/AnimalLocusData'
import { transformPointToMercator } from '@/utils'

// 地图事件常量
const MapEvent = Events.MapEvent
export default {
  props: {
    close: {
      type: Function,
      default: () => {
        console.log('close')
      },
    },
    propData: {
      type: Object,
      default: {},
    },
  },
  components: {
    CtlPanel,
  },
  // 销毁前钩子
  beforeDestroy() {
    // 清空定时器
    this.timer && clearInterval(this.timer)
    // 清空地图历史轨迹
    this.$EventBus.$emit(MapEvent.ANIMAL_GPS_HISTORY_LOCUS, {
      locusList: [],
      type: 'close',
      currentTime: '',
    })
  },
  data() {
    return {
      selecteTimeArr: [dayjs().subtract(4, 'hours'), dayjs()], //  时间  [dayjs('2024-07-19 10:00:00'), dayjs('2024-07-19 11:00:00')],//
      isPlay: false, // 是否正在播放
      currentIndex: 0, // 当前播放索引
      maxIndex: 48, // 最大播放索引
      locusList: [], // 轨迹列表
      timer: null, // 历史轨迹播放定时器
      pickerOptions: {
        // 禁用未来日期选择
        disabledDate(time) {
          return time.getTime() > Date.now()
        },
      },
    }
  },
  methods: {
    // 查询历史轨迹
    queryHistoryTrack() {
      // 校验入参是否适合
      // 检查选择的时间范围是否超过5小时
      if (
        dayjs(this.selecteTimeArr[1]).diff(
          dayjs(this.selecteTimeArr[0]),
          'hours'
        ) > 5
      ) {
        this.$message({
          // 如果超过5小时，显示警告消息
          message: `时间范围不能超过5小时`,
          type: 'warning',
        })
        return // 结束函数执行
      }
      console.info('selecteTimeArr==============', this.selecteTimeArr) // 输出选择的时间范围数组到控制台
      const deviceCode = this.propData.deviceCode // 获取设备码
      const data = (AnimalLocusData[deviceCode] || []).map(v => {
        // 3.0 地图转成墨卡托
        const lngLat = transformPointToMercator([Number(v.lng), Number(v.lat)])
        return {
          ...v,
          lng: lngLat[0], //  经度
          lat: lngLat[1], //  纬度
        }
      }) // 根据设备码获取动物位置数据，如果没有数据则为空数组
      if (
        dayjs(this.selecteTimeArr[0]).valueOf() ===
        dayjs(this.selecteTimeArr[1]).valueOf()
      ) {
        // 检查选择的时间范围开始和结束时间是否相同
        this.locusList = [] // 如果相同，将位置列表设置为空
      } else {
        // 计算时间间隔并重新分配数据时间点
        // 计算时间差（毫秒）
        // console.log(
        //   dayjs(this.selecteTimeArr[1]).diff(dayjs(this.selecteTimeArr[0]))
        // )
        const durationStr = dayjs.duration(
          dayjs(this.selecteTimeArr[1]).diff(dayjs(this.selecteTimeArr[0]))
        )
        const interval = durationStr.asMilliseconds() / data.length // 计算每个数据点之间的时间间隔（毫秒）
        data.forEach((item, i) => {
          // 遍历位置数据
          const newTime = dayjs(this.selecteTimeArr[0]).add(
            i * interval,
            'milliseconds'
          ) // 根据时间间隔计算新的时间点
          item.pt = newTime.format('YYYY-MM-DD HH:mm:ss') // 更新数据项的时间点为新的时间格式
        })
        this.locusList = data // 将处理后的位置数据赋值给位置列表
      }
      // 渲染位置数据到地图上
      if (this.locusList.length > 0) {
        // 检查位置列表是否有数据
        this.onPlay() // 如果有数据，调用onPlay方法开始播放位置
      } else {
        this.$message({
          // 如果没有数据，显示警告消息
          message: '暂无数据',
          type: 'warning',
        })
        this.$EventBus.$emit(MapEvent.ANIMAL_GPS_HISTORY_LOCUS, {
          // 发送事件通知地图组件更新位置列表为空
          locusList: [],
          type: 'close',
          currentTime: '',
        })
        return // 结束函数执行
      }
    },
    // 开始播放
    onPlay() {
      // 设置定时器，每隔1000毫秒（即1秒）执行一次箭头函数中的代码
      this.timer = setInterval(() => {
        // 当前索引加1，用于跟踪时间进度
        this.currentIndex++
        // 检查当前索引是否超过最大索引
        if (this.currentIndex > this.maxIndex) {
          // 如果超过最大索引，清除定时器，停止播放
          clearInterval(this.timer)
          // 设置播放状态为false，表示当前不在播放
          this.isPlay = false
          // 重置当前索引为0，以便下次播放可以从头开始
          this.currentIndex = 0
        } else {
          // 根据当前索引计算当前时间，每隔5分钟增加一次
          const currentTime = dayjs(
            dayjs(this.selecteTimeArr[0]).format('YYYY-MM-DD HH:mm:ss') // 获取初始时间并格式化
          )
            .add(this.currentIndex * 5, 'minutes') // 增加5分钟乘以当前索引
            .format('YYYY-MM-DD HH:mm:ss') // 格式化结果时间
          // 通过EventBus发送事件MapEvent.ANIMAL_GPS_HISTORY_LOCUS，并携带当前轨迹列表、类型和计算出的当前时间
          this.$EventBus.$emit(MapEvent.ANIMAL_GPS_HISTORY_LOCUS, {
            locusList: this.locusList, // 轨迹列表
            type: 'history', // 事件类型为历史轨迹
            currentTime: currentTime, // 当前时间
          })
        }
      }, 1000)
      // 设置播放状态为true，表示已经开始播放
      this.isPlay = true
    },

    // onStop 暂停 1.isPlay置为false, 2.停止定时器,
    onStop() {
      this.isPlay = false
      this.timer && clearInterval(this.timer)
    },
    // onReset 重置 1.isPlay置为false, 2.停止定时器, 3.currentIndex置为0, 4.
    onReset() {
      // 重置播放状态为停止
      this.isPlay = false
      // 将当前索引重置为0
      this.currentIndex = 0
      // 如果存在定时器，则清除定时器
      this.timer && clearInterval(this.timer)
      // 设置时间范围数组为当前时间的前4小时到当前时间
      this.selecteTimeArr = [dayjs().subtract(4, 'hours'), dayjs()]
      // 通过事件总线发送事件，清除地图上的动物GPS历史轨迹
      this.$EventBus.$emit(MapEvent.ANIMAL_GPS_HISTORY_LOCUS, {
        locusList: [], // 轨迹列表为空
        type: 'close', // 类型为关闭
        currentTime: '', // 当前时间为空
      })
    },
    // 进度条拖动事件处理函数
    onSliderChange(value) {
      // 更新当前索引为进度条的值
      this.currentIndex = value
      // 计算当前时间：从时间范围数组的第一个时间开始，加上当前索引乘以5分钟
      const currentTime = dayjs(
        dayjs(this.selecteTimeArr[0]).format('YYYY-MM-DD HH:mm:ss') // 获取时间范围数组的第一个时间
      )
        .add(this.currentIndex * 5, 'minutes') // 增加当前索引乘以5分钟
        .format('YYYY-MM-DD HH:mm:ss') // 格式化为字符串
      // 通过事件总线发送事件，更新地图上的动物GPS历史轨迹
      this.$EventBus.$emit(MapEvent.ANIMAL_GPS_HISTORY_LOCUS, {
        locusList: this.locusList, // 使用当前的轨迹列表
        type: 'history', // 类型为历史轨迹
        currentTime: currentTime, // 更新当前时间为计算出的时间
      })
    },

    /**
     * 关闭面板前的处理函数，1.如果在播放中，则停止播放，清空定时器；2.关闭窗口
     */
    doClose() {
      // 关闭控制面板前，如果正在喊话则停止喊话
      if (this.isPlay) {
        this.onReset()
      }
      this.$EventBus.$emit(MapEvent.ANIMAL_GPS_HISTORY_LOCUS, {
        locusList: [],
        type: 'close',
        currentTime: '',
      })
      this.close()
    },
    // 格式化进度条tooltip时间
    formatTooltip(value) {
      if (this.selecteTimeArr && this.selecteTimeArr.length == 2) {
        return dayjs(
          dayjs(this.selecteTimeArr[0]).format('YYYY-MM-DD HH:mm:ss')
        )
          .add(value * 5, 'minutes')
          .format('YYYY-MM-DD HH:mm:ss')
      } else {
        return '--:--:--'
      }
    },
    // 时间范围修改重新计算maxIndex
    timeChange(value) {
      console.info('timechange -------', value)
      // 如果清空了去提示
      if (!value) {
        this.$message({
          message: '查询时间不能为空',
          type: 'warning',
        })
        this.selecteTimeArr = [dayjs().subtract(4, 'hours'), dayjs()]
        return
      }
      // 校验入参是否适合
      if (dayjs(value[1]).diff(dayjs(value[0]), 'hours') > 5) {
        this.$message({
          message: `时间范围不能超过5小时`,
          type: 'warning',
        })
        return
      }
      this.timer && clearInterval(this.timer)
      // 当前进度条置为0
      this.currentIndex = 0
      this.isPlay = false
      // 设置最大值
      this.maxIndex = Math.ceil(
        dayjs(value[1]).diff(dayjs(value[0]), 'minutes') / 5
      )
      this.$EventBus.$emit(MapEvent.ANIMAL_GPS_HISTORY_LOCUS, {
        locusList: [],
        type: 'close',
        currentTime: '',
      })
    },
  },
}
</script>

<style lang="less" scoped  src="../../../ScreenPages/IntegratedMonitor/AnimalLocus/index.less" />
