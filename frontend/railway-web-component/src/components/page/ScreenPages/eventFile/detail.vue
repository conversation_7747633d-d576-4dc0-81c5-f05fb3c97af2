<!--
 * @Description:
 * @Author: liu.yongli
 * @Date: 2024-04-04 11:40:28
 * @LastEditTime: 2025-02-27 15:57:23
 * @LastEditors: liu.yongli
-->
<template>
  <div class="event_file_detail_main">
    <!-- 左侧事件详情信息 -->
    <div class="event_file_detail_left">
      <!-- 事件信息标题 -->
      <div class="event_file_detail_left_title">
        <img
          alt="事件信息"
          :src="require('@/assets/images/alarmEvent/eventFile/icon_marker_level_2.svg')"
        />&nbsp;事件信息
      </div>
      <!-- 事件标题 -->
      <div class="event_file_detail_left_item">
        <div class="event_file_detail_left_item_title">事件标题：</div>
        <div class="event_file_detail_left_item_value">{{ eventInfo?.title }}</div>
      </div>
      <!-- 事件编号 -->
      <div class="event_file_detail_left_item">
        <div class="event_file_detail_left_item_title">事件编号：</div>
        <div class="event_file_detail_left_item_value">{{ orderInfo?.warningOrderId }}</div>
      </div>
      <!-- 事件类型 -->
      <div class="event_file_detail_left_item">
        <div class="event_file_detail_left_item_title">事件类型：</div>
        <div
          class="event_file_detail_left_item_value"
        >{{['其它','其他'].includes(eventInfo.eventTypeName) ? orderInfo?.warningTypeName : eventInfo.eventTypeName }}</div>
      </div>
      <!-- 事件等级 -->
      <div class="event_file_detail_left_item">
        <div class="event_file_detail_left_item_title">事件等级：</div>
        <div
          :class="`event_file_detail_left_item_value level-tag${getCongestLevel(eventInfo.emergencyLevelName)}`"
        >{{ eventInfo?.emergencyLevelName }}</div>
      </div>
      <!-- 事发时间 -->
      <div class="event_file_detail_left_item">
        <div class="event_file_detail_left_item_title">事发时间：</div>
        <div class="event_file_detail_left_item_value">{{ getOccTime(eventInfo.occurTime) }}</div>
      </div>
      <!-- 已消散才有 -->
      <div class="event_file_detail_left_item" v-if="eventInfo.fusionStatus === '3'">
        <div class="event_file_detail_left_item_title">消散时间：</div>
        <div class="event_file_detail_left_item_value">{{ getOccTime(eventInfo.endTime) }}</div>
      </div>
      <!-- 经纬度 -->
      <div class="event_file_detail_left_item">
        <div class="event_file_detail_left_item_title">经纬度：</div>
        <div class="event_file_detail_left_item_value">
          <template
            v-if="orderInfo?.longitude && orderInfo?.longitude !== 'NaN'"
          >{{ `${orderInfo?.longitude},${orderInfo?.latitude}` }}</template>
        </div>
      </div>
      <div class="event_file_detail_left_item">
        <div class="event_file_detail_left_item_title">事件状态：</div>
        <div
          class="event_file_detail_left_item_value"
          :style="{color:getFusionStatus(eventInfo.fusionStatus).dictLabel=== '未消散'?'#fb913c':'#15bd94'}"
        >{{ getFusionStatus(eventInfo.fusionStatus).dictLabel }}</div>
      </div>
      <!-- 处置状态 -->
      <div class="event_file_detail_left_item">
        <div class="event_file_detail_left_item_title">处置状态：</div>
        <div
          class="event_file_detail_left_item_value"
          style="color:#4f9fff"
        >{{ orderInfo?.orderStatusName }}</div>
      </div>
      <!-- 办结方式，只有在状态为已办结时显示 -->
      <!-- ['已办结','已完结'] -->
      <!-- <div class="event_file_detail_left_item" v-if="orderInfo?.orderStatusName === '6'">
        <div class="event_file_detail_left_item_title">办结方式：</div>
        <div class="event_file_detail_left_item_value">{{ orderInfo?.warningTypeName }}</div>
      </div>-->
      <!-- 铁路线 -->
      <div class="event_file_detail_left_item">
        <div class="event_file_detail_left_item_title">铁路线路：</div>
        <div class="event_file_detail_left_item_value">{{ eventInfo.segmentName }}</div>
      </div>
      <!-- 事发地点 -->
      <div class="event_file_detail_left_item">
        <div class="event_file_detail_left_item_title">事发地点：</div>
        <div
          class="event_file_detail_left_item_value"
          :title="eventInfo?.eventAddress"
        >{{ eventInfo?.eventAddress }}</div>
      </div>
      <!-- 是否在铁路地界内 -->
      <div class="event_file_detail_left_item">
        <div class="event_file_detail_left_item_title">是否在铁路地界内：</div>
        <div class="event_file_detail_left_item_value">
          <template v-if="eventInfo?.isInner">{{ eventInfo.isInner === 'Y' ? '是' : '否'}}</template>
        </div>
      </div>
      <!-- 是否在安保区内 -->
      <div class="event_file_detail_left_item">
        <div class="event_file_detail_left_item_title">是否在安保区内：</div>
        <div class="event_file_detail_left_item_value">
          <template v-if="eventInfo?.isProtected">{{ eventInfo.isProtected === 'Y' ? '是' : '否' }}</template>
        </div>
      </div>
      <!-- <div class="event_file_detail_left_item" /> -->
      <!-- 事件描述 -->
      <!-- <div class="event_file_detail_left_item" style="width:100%">
        <div class="event_file_detail_left_item_title">事件描述：</div>
        <div
          class="event_file_detail_left_item_value"
          :title="eventInfo?.description"
        >{{ eventInfo?.description }}</div>
      </div>-->
      <!-- 事件概况 -->
      <div class="event_file_detail_left_item_long">
        <div class="event_file_detail_left_item_title">事件概况：</div>
        <div class="event_file_detail_left_item_value">
          <!-- <el-row>{{ orderInfo?.description }}</el-row> -->
          <el-row style="display: flex; width: 100%">
            <el-col :span="24">
              <!-- 10.雷达告警扩展字段列表 -->
              <el-col :span="24" v-if="orderInfo?.warningSource === '10'">
                <span v-show="extraFieldValueRadar('targetId')">
                  <span class="rowTitle" title="目标ID">目标ID：</span>
                  <span class="rowValue">{{ extraFieldValueRadar("targetId") }}；</span>
                </span>
                <span class="no-wrap" v-show="extraFieldValueRadar('targetName')">
                  <span class="rowTitle" title="目标名称">目标名称：</span>
                  <span class="rowValue">{{ extraFieldValueRadar("targetName") }}；</span>
                </span>
                <span class="no-wrap" v-show="extraFieldValueRadar('targetType')">
                  <span class="rowTitle" title="目标类型">目标类型：</span>
                  <span class="rowValue">{{ extraFieldValueRadar("targetType") }}；</span>
                </span>
                <span class="no-wrap" v-show="extraFieldValueRadar('speed')">
                  <span class="rowTitle" title="目标速度">目标速度：</span>
                  <span class="rowValue">{{ extraFieldValueRadar("speed") }}；</span>
                </span>
                <span class="no-wrap" v-show="extraFieldValueRadar('course')">
                  <span class="rowTitle" title="目标航向">目标航向：</span>
                  <span class="rowValue">{{ extraFieldValueRadar("course") }}；</span>
                </span>
                <span class="no-wrap" v-show="extraFieldValueRadar('targetLength')">
                  <span class="rowTitle" title="目标长度">目标长度：</span>
                  <span class="rowValue">{{ extraFieldValueRadar("targetLength")}}；</span>
                </span>
              </el-col>
              <!-- 1和12 AI告警，无人机告警扩展字段列表 -->
              <el-col
                :span="24"
                v-else-if="orderInfo?.warningSource === '1' || orderInfo?.warningSource === '12'"
              >
                <el-col
                  :span="24"
                  class="no-wrap"
                  v-show="extraData?.extraList?.length || (orderInfo?.description && algorithmConfig?.length &&  algorithmConfig.find((item) => item?.analysisPeriod || item?.timeInterval || item?.modelConfig?.length))"
                >
                  <span v-if="extraData?.extraList?.length" style="margin-bottom: 0">【算法识别结果】</span>
                  <template v-for="(item, index) in extraData?.extraList">
                    <span
                      :key="'extraItem__'+index"
                      :span="24"
                      class="no-wrap"
                      v-if="extendedFieldsOfAi.some((i) => i.code === item.attrType)"
                    >
                      <span
                        class="rowTitle"
                      >{{ extendedFieldsOfAi.find((i) => i.code === item.attrType).codeName }}：</span>
                      <span class="rowValue" :title="item.attrValue">{{ item.attrValue }}；</span>
                    </span>
                  </template>
                  <template v-if="orderInfo?.warningTypeId == '1819000'">
                    <template v-for="(item, index) in extraData?.extraList">
                      <span
                        :key="'extraListItem__'+index"
                        :span="24"
                        class="no-wrap"
                        v-if="kakouAlarmList.some((i) => i.value === item.attrType)"
                      >
                        <span
                          class="rowTitle"
                        >{{ kakouAlarmList.find((i) => i.value === item.attrType).label }}：</span>
                        <span class="rowValue" :title="item.attrValue">{{ item.attrValue }}；</span>
                      </span>
                    </template>
                  </template>
                  <span
                    v-if="orderInfo?.description && algorithmConfig?.length && algorithmConfig.find((item) => item?.analysisPeriod || item?.timeInterval || item?.modelConfig?.length)"
                  >
                    <template v-if="extraData?.extraList?.length">
                      <br />
                    </template>【算法配置参数】
                  </span>
                  <template v-for="(algorithmConfig, index) in algorithmConfig">
                    <span
                      :key="'algorithmConfigItem__'+index"
                      class="no-wrap"
                      v-if="algorithmConfig?.analysisPeriod?.length"
                    >
                      <span class="rowTitle">算法运行时段：</span>
                      <span
                        class="rowValue"
                        :title="algorithmConfig.analysisPeriod?.join(',')"
                      >{{ algorithmConfig.analysisPeriod?.join("，") }}；</span>
                    </span>
                    <span
                      :key="'algorithmConfigtimeIntervalItem__'+index"
                      class="no-wrap"
                      v-if="algorithmConfig?.timeInterval"
                    >
                      <span class="rowTitle">识别频率：</span>
                      <span
                        class="rowValue"
                        :title="algorithmConfig.timeInterval"
                      >{{ algorithmConfig.timeInterval }}；</span>
                    </span>
                    <span
                      :key="'algorithmConfigItem__'+i"
                      class="no-wrap"
                      v-for="(configItem, i) in algorithmConfig.modelConfig"
                    >
                      <span
                        class="inline-style"
                        v-if="configItem?.selectValue || configItem?.value"
                      >{{ configItem.name }}：{{ configItem.selectValue || configItem.value }}；</span>
                    </span>
                  </template>
                </el-col>
              </el-col>
              <!-- 4.摄像机告警扩展字段列表 -->
              <el-col :span="24" v-else-if="orderInfo?.warningSource === '4'">
                <template v-for="(item, index) in extraData?.extraList">
                  <span
                    :key="'extraItem__'+index"
                    class="no-wrap"
                    v-if=" extendedFieldsOfAi.some((i) => i.code === item.attrType )"
                  >
                    <span
                      class="rowTitle"
                    >{{ extendedFieldsOfAi.find((i) => i.code === item.attrType).codeName }}：</span>
                    <span class="rowValue">{{ item.attrValue }}；</span>
                  </span>
                </template>
                <template v-if="orderInfo?.warningTypeId == '1819000'">
                  <template v-for="(item, index) in extraData?.extraList">
                    <span
                      :key="'extraListItem__'+index"
                      class="no-wrap"
                      v-if="kakouAlarmList.some((i) => i.value === item.attrType)"
                    >
                      <span
                        class="rowTitle"
                      >{{ kakouAlarmList.find((i) => i.value === item.attrType).label }}：</span>
                      <span class="rowValue">{{ item.attrValue }}；</span>
                    </span>
                  </template>
                </template>
              </el-col>
              <!-- 8.物联设备扩展字段 -->
              <el-col :span="24" v-else-if="orderInfo?.warningSource === '8'">
                <span class="no-wrap">
                  <span class="rowTitle">监测指标编码：</span>
                  <span class="rowValue">{{ extraFieldValueIoT("monitorCode") }}；</span>
                </span>
                <span class="no-wrap" v-show="extraFieldValueIoT('monitorName')">
                  <span class="rowTitle">监测指标：</span>
                  <span class="rowValue">{{ extraFieldValueIoT("monitorName") }}；</span>
                </span>
                <span class="no-wrap" v-show="extraFieldValueIoT('monitorValue')">
                  <span class="rowTitle">监测值：</span>
                  <span class="rowValue">{{ extraFieldValueIoT("monitorValue") }}；</span>
                </span>
                <span class="no-wrap" v-show="  extraFieldValueIoT('thresholdValue') ">
                  <span class="rowTitle">告警阈值：</span>
                  <span class="rowValue">{{ extraFieldValueIoT("thresholdValue") }}；</span>
                </span>
              </el-col>
              <!-- 9.卫星告警扩展字段 -->
              <el-col
                :span="24"
                v-else-if="orderInfo?.warningSource === '9' && orderInfo?.description && isJsonString(orderInfo?.description)"
              >
                <span
                  class="no-wrap"
                  v-for="(desItem, index) in JSON.parse(orderInfo?.description)"
                  :key="'descriptionItem__'+index"
                >
                  <span class="rowTitle">{{ desItem.name }}：</span>
                  <span class="rowValue">{{ desItem.value || '-' }}；</span>
                </span>
              </el-col>
              <!-- 其他类型 -->
              <el-col
                v-if="orderInfo?.warningSource !== '8'"
                :span="24"
                class="value"
                style="width: 100%"
              >
                <template
                  v-if="orderInfo?.description && !['9', '1', '4'].includes(orderInfo?.warningSource)"
                >
                  <div class="description-box">{{ orderInfo?.description }}</div>
                </template>
              </el-col>
            </el-col>
          </el-row>
        </div>
      </div>
      <!-- 来源信息标题 -->
      <div class="event_file_detail_left_title event_file_detail_left_title_2">
        <img
          alt="来源信息"
          :src="require('@/assets/images/alarmEvent/eventFile/icon_marker_level_2.svg')"
        />&nbsp;来源信息
      </div>
      <!-- 事件来源 -->
      <div class="event_file_detail_left_item">
        <div class="event_file_detail_left_item_title">事件来源：</div>
        <div class="event_file_detail_left_item_value">{{ orderInfo?.warningSourceName }}</div>
      </div>
      <!-- 摄像机、无人机信息，特定来源时显示 -->
      <div
        class="event_file_detail_left_item"
        v-if="orderInfo?.warningSource == '1' || orderInfo?.warningSource == '3' || orderInfo?.warningSource == '4' || orderInfo?.warningSource == '12'"
      >
        <div
          class="event_file_detail_left_item_title"
        >{{ orderInfo?.warningSource === '12' ?'无人':'摄像' }}机名称：</div>
        <div
          :title="orderInfo?.deviceName"
          class="event_file_detail_left_item_value"
        >{{ orderInfo?.deviceName }}</div>
      </div>
      <div
        class="event_file_detail_left_item"
        v-if="orderInfo?.warningSource == '1' || orderInfo?.warningSource == '3' || orderInfo?.warningSource == '4' || orderInfo?.warningSource == '12'"
      >
        <div
          class="event_file_detail_left_item_title"
        >{{ orderInfo?.warningSource === '12' ?'无人':'摄像' }}机编号：</div>
        <div class="event_file_detail_left_item_value">{{ orderInfo?.deviceCode }}</div>
      </div>
      <div
        class="event_file_detail_left_item"
        v-if="orderInfo?.warningSource == '1' || orderInfo?.warningSource == '3' || orderInfo?.warningSource == '4' || orderInfo?.warningSource == '12'"
      >
        <div
          class="event_file_detail_left_item_title"
        >{{ orderInfo?.warningSource === '12' ?'无人':'摄像' }}机地址：</div>
        <div
          :title="orderInfo?.warningSource === '12' ? orderInfo?.uavDevInfoList?.location : orderInfo?.deviceAddress"
          class="event_file_detail_left_item_value"
        >{{ orderInfo?.warningSource === '12' ? orderInfo?.uavDevInfoList?.location : orderInfo?.deviceAddress }}</div>
      </div>
      <!-- 上报人信息，特定来源时显示 -->
      <div class="event_file_detail_left_item" v-if="orderInfo?.warningSource == '2'">
        <div class="event_file_detail_left_item_title">上报人姓名：</div>
        <div class="event_file_detail_left_item_value">{{ orderInfo?.findName }}</div>
      </div>
      <div class="event_file_detail_left_item" v-if="orderInfo?.warningSource == '2'">
        <div class="event_file_detail_left_item_title">联系方式：</div>
        <!-- findMobile 这个手机号没有格式化 -->
        <div class="event_file_detail_left_item_value">{{ orderInfo?.encryptionMobilePhone }}</div>
      </div>
      <!-- 通道信息，特定来源时显示 -->
      <div class="event_file_detail_left_item" v-if="orderInfo?.warningSource == '3'">
        <div class="event_file_detail_left_item_title">通道名称：</div>
        <div class="event_file_detail_left_item_value">{{ orderInfo?.channelName }}</div>
      </div>
      <div class="event_file_detail_left_item" v-if="orderInfo?.warningSource == '3'">
        <div class="event_file_detail_left_item_title">摄像机厂家：</div>
        <div class="event_file_detail_left_item_value">{{ orderInfo?.modelFactoryName }}</div>
      </div>
      <div class="event_file_detail_left_item" v-if="orderInfo?.warningSource == '3'">
        <div class="event_file_detail_left_item_title">摄像机高度：</div>
        <div
          class="event_file_detail_left_item_value"
          v-if="orderInfo?.height !== null"
        >{{ orderInfo?.height }}</div>
        <div class="event_file_detail_left_item_value" v-else>&nbsp;</div>
      </div>
      <!-- 整治流程标题 -->
      <div class="event_file_detail_left_title event_file_detail_left_title_2">
        <img
          alt="整治流程"
          :src="require('@/assets/images/alarmEvent/eventFile/icon_marker_level_2.svg')"
        />&nbsp;整治流程
      </div>
      <!-- 整治流程步骤 -->
      <div class="event_file_detail_left_step">
        <div v-for="(item, index) in stepList" :key="`${item.flowNodeTime}_${index}`">
          <div class="event_file_detail_left_step_order">
            <div class="event_file_detail_left_step_order_num">{{ index + 1 }}</div>
            <div class="event_file_detail_left_step_order_time">{{ item.flowNodeTime }}</div>
            <div
              :class="`event_file_detail_left_step_order_name ${index === 0 ? 'active' : ''}`"
            >{{ item.linkName }}</div>
          </div>
          <!-- 处置步骤详细信息 -->
          <div
            class="event_file_detail_left_step_content"
            v-if="item.realLinkType != '6' && item.realLinkType != '0'"
          >
            <div class="event_file_detail_left_step_item">
              <div class="event_file_detail_left_step_item_title">处置人：</div>
              <div class="event_file_detail_left_step_item_value">{{ item.processName }}</div>
            </div>
            <div class="event_file_detail_left_step_item">
              <div class="event_file_detail_left_step_item_title">所属组织：</div>
              <div
                class="event_file_detail_left_step_item_value"
                :title="getProcessOrg(item.processId)"
              >{{ getProcessOrg(item.processId) }}</div>
            </div>
            <div class="event_file_detail_left_step_item">
              <div class="event_file_detail_left_step_item_title">环节类型：</div>
              <div
                class="event_file_detail_left_step_item_value"
              >{{ item.realLinkType == '-1' ? '开始上报' : item.realLinkType == '1' ? '研判' : item.realLinkType == '2' ? '调度' : item.realLinkType == '4' ? '处置' : item.realLinkType == '5' ? '核实' : '' }}</div>
            </div>
            <div class="event_file_detail_left_step_item">
              <div class="event_file_detail_left_step_item_title">下一环节：</div>
              <div class="event_file_detail_left_step_item_value">{{ item.nextLinkName }}</div>
            </div>
            <div class="event_file_detail_left_step_item">
              <div class="event_file_detail_left_step_item_title">处理人：</div>
              <div class="event_file_detail_left_step_item_value">{{ item.nextLinkProcessName }}</div>
            </div>
            <div class="event_file_detail_left_step_item">
              <div class="event_file_detail_left_step_item_title">处置说明：</div>
              <div
                class="event_file_detail_left_step_item_value"
                :title="item.remark"
              >{{ item.remark }}</div>
            </div>
            <div class="event_file_detail_left_step_item">
              <div class="event_file_detail_left_step_item_title">环节处置时限：</div>
              <div
                class="event_file_detail_left_step_item_value"
              >{{ getLinkTimeLimit(item.linkId) }}</div>
            </div>
            <div
              class="event_file_detail_left_step_item"
              :style="{ width: isCheckMode ? '66%' : '33%' }"
            >
              <div class="event_file_detail_left_step_item_title">办理时长：</div>
              <div class="event_file_detail_left_step_item_value">
                {{ flowDealTime(item) }}&nbsp;&nbsp;
                <div
                  class="event_file_detail_left_step_item_value_overTime"
                  v-if="isCheckMode && checkOverTime(item)"
                >
                  &nbsp;&nbsp;
                  <i class="el-icon-time"></i>
                  &nbsp;&nbsp;超时:{{ getOverTimeStr(item) }}
                </div>
              </div>
            </div>
            <!-- 附件信息 -->
            <div
              class="event_file_detail_left_step_item_file"
              v-if="item.files?.length > 0 || item.orderFileList?.length > 0"
            >
              <div class="event_file_detail_left_step_item_title">附件：</div>
              <div
                class="event_file_detail_left_step_item_value"
                v-if="item.mediaFileList?.length > 0"
              >
                <div
                  v-for="(fileItem, fileIndex) in item.mediaFileList"
                  :key="`${fileItem.fileName}_${fileIndex}`"
                  class="file-image-box"
                >
                  <img
                    alt="附件"
                    :src="fileItem.resourceUrl"
                    class="file-image"
                    v-if="fileItem.resourceType == '1'"
                    @click="openImgViewer(item.mediaFileList, fileIndex)"
                    :title="fileItem.fileName"
                  />
                  <video
                    v-if="fileItem.resourceType === '2'"
                    muted
                    class="file-video"
                    @click="openImgViewer(item.mediaFileList, fileIndex)"
                    :title="fileItem.fileName"
                  >
                    <source :src="fileItem.resourceUrl" />
                  </video>
                  <img
                    alt="下载"
                    v-if="fileItem.resourceType === '3a'"
                    :src="require(`@/assets/images/alarmEvent/alarm/download_doc.png`)"
                    class="file-image"
                    @click="downLoadDoc(fileItem.resourceUrl, fileItem.fileName)"
                    :title="fileItem.fileName"
                  />
                </div>
              </div>
            </div>
            <div
              class="event_file_detail_left_step_item_file_docs"
              :style="{ marginTop: (item.mediaFileList?.length > 0 ? '0px' : pxToRem(-75)) }"
              v-if="item.docList?.length > 0"
            >
              <div
                class="event_file_detail_left_step_item_file_doc_name"
                @click="downLoadDoc(item2.resourceUrl, item2.fileName)"
                v-for="(item2, index) in item.docList"
                :key="`${item2.fileName}_${index}`"
              >{{ item2.fileName }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 右侧展示区域 -->
    <div class="event_file_detail_right">
      <div class="event_file_detail_right_content">
        <div class="event_file_detail_right_up">
          <!-- 视频播放器 -->
          <div
            id="event_file_detail_img_video"
            class="edit_main_right_map event_file_detail_img_video"
            v-if="fileType === 'video'"
          >
            <video-player
              class="video video-player vjs-custom-skin infoVideo-player lunboVideo"
              :playsinline="true"
              :options="getPlayerOptionsLittle(currentFileUrl)"
              @statechanged="playStatechanged($event)"
            />
            <div class="full_screen_button" @click="fullScreen('event_file_detail_img_video')">
              <img
                alt="缩放"
                :src="require(`@/assets/images/alarmEvent/alarm/${fullScreenElementId === 'event_file_detail_img_video' ? 'suoxiao_icon' : 'fangda_icon'}.svg`)"
              />
            </div>
          </div>
          <div
            id="event_file_detail_img_image"
            class="edit_main_right_map event_file_detail_img_image"
            v-if="fileType === 'image'"
          >
            <img alt="图片" :src="currentFileUrl" :style="{ width: '100%', height: '100%' }" />
            <div class="full_screen_button" @click="fullScreen('event_file_detail_img_image')">
              <img
                alt="缩放"
                :src="require(`@/assets/images/alarmEvent/alarm/${fullScreenElementId === 'event_file_detail_img_image' ? 'suoxiao_icon' : 'fangda_icon'}.svg`)"
              />
            </div>
          </div>
          <!-- 图片轮播 -->
          <!-- <el-carousel :interval="5000" arrow="always" v-if="fileType === 'image'">
            <el-carousel-item
              v-for="(item, index) in imgList"
              :key="index"
              :id="`event_file_detail_img_${index}`"
            >
              <img alt="图片" :src="item.fileUrl" :style="{ width: '100%', height: '100%' }" />
              <div class="full_screen_button" @click="fullScreen(`event_file_detail_img_${index}`)">
                <img
                  alt="缩放"
                  :src="require(`@/assets/images/alarmEvent/alarm/${fullScreenElementId === `event_file_detail_img_${index}` ? 'suoxiao_icon' : 'fangda_icon'}.svg`)"
                />
              </div>
            </el-carousel-item>
          </el-carousel>-->
          <!-- 地图展示 -->
          <div id="detail_map" class="edit_main_right_map" v-show="fileType === 'map'">
            <RemoteComponentSyncLoader
              v-if="components['common-comp-map']"
              showMapControl
              mapToolElement=".edit_main_right_map .maptooltele"
              :class="['detail-map','fix-map-tool']"
              chooseMapMemoryKey
              :config="components['common-comp-map']"
              :mapId="detailMapId"
              :defaultTileMode="5"
              :tileModes="tileModes"
            />
            <!-- mapToolElement 中类名需要包含父级中类名才展示出地图控制组件 -->
            <!-- 地图控制组件 通过 common-comp-map 组件自动生成 -->
            <div :class="['maptooltele', 'fix-map-tool']" />
            <!-- 地图类型切换铁路线路 -->
            <ChangeRailwayType
              v-if="littleMapShow"
              :mapId="detailMapId"
              :tileModes="tileModes"
              pageType="eventFile"
            />
            <div class="full_screen_button" @click="fullScreen(detailMapId)">
              <img
                alt="缩放"
                :src="require(`@/assets/images/alarmEvent/alarm/${fullScreenElementId === 'detail_map' ? 'suoxiao_icon' : 'fangda_icon'}.svg`)"
              />
            </div>
          </div>
        </div>
        <div class="time-line-wraper" v-if="progressTimer.length > 1">
          <TimeLineCarouselCom :list="progressTimer" @change="handleCurrentChange" />
        </div>
      </div>
      <div class="event_file_detail_right_down">
        <!-- 视频缩略图 -->
        <div
          v-if="videoList && videoList.length > 0"
          :class="['event_file_detail_right_down_panel',fileType == 'video' ? 'event_file_detail_right_down_item_selected' : 'event_file_detail_right_down_item']"
          @click.stop="onEventFileClick('video',videoList[0].src)"
        >
          <el-carousel :autoplay="false" arrow="always" @change="onPageChange">
            <el-carousel-item v-for="(item,index) in videoList" :key="`${item.src}_${index}`">
              <video-player
                @click.stop="onEventFileClick('video',item.src)"
                class="video video-player vjs-custom-skin infoVideo-player lunboVideo"
                :playsinline="true"
                ref="videoPlayer"
                :options="getPlayerOptionsLittle(item.src)"
                @statechanged="playStatechanged($event)"
                @ready="onPlayerReady($event, index)"
                @play="onPlayerPlay($event, index)"
              />
            </el-carousel-item>
          </el-carousel>
        </div>
        <!-- <div
          v-if="videoList && videoList.length > 0"
          :class="['event_file_detail_right_down_panel',fileType == 'video' ? 'event_file_detail_right_down_item_selected' : 'event_file_detail_right_down_item']"
          @click="onEventFileClick('video')"
        >
          <video-player
            class="video video-player vjs-custom-skin infoVideo-player lunboVideo"
            :playsinline="true"
            :options="playerOptionsLittle"
            @statechanged="playStatechanged($event)"
          />
        </div>-->
        <!-- 图片缩略图 -->
        <div
          v-if="imgList && imgList.length > 0"
          :class="['event_file_detail_right_down_panel',fileType == 'image' ? 'event_file_detail_right_down_item_selected' : 'event_file_detail_right_down_item']"
          @click.stop="onEventFileClick('image',imgList[0].fileUrl)"
        >
          <el-carousel :autoplay="false" arrow="always">
            <el-carousel-item v-for="(item,index) in imgList" :key="`${item.fileUrl}_${index}`">
              <img
                @click.stop="onEventFileClick('image',item.fileUrl)"
                alt="图片"
                :src="item.fileUrl"
                :style="{ width: '100%', height: '100%' }"
              />
            </el-carousel-item>
          </el-carousel>
        </div>
        <!-- 地图缩略图 -->
        <div
          :class="['event_file_detail_right_down_panel','main_map_wrap',fileType == 'map' ? 'event_file_detail_right_down_item_selected' : 'event_file_detail_right_down_item']"
          @click="onEventFileClick('map')"
        >
          <RemoteComponentSyncLoader
            v-if="components['common-comp-map']"
            :showMapControl="false"
            :class="['little-map']"
            chooseMapMemoryKey
            :defaultTileMode="5"
            :config="components['common-comp-map']"
            :mapId="littleMapId"
            :tileModes="tileModes"
          />
        </div>
      </div>
    </div>
    <!-- 全屏查看视频图片 -->
    <FileFullScreen v-if="showFileFull" :fileFullList="fileFullList" @closeFull="closeFull"></FileFullScreen>
  </div>
</template>
<script>
import api from '@/api'
import vue from '@/main'
import commonService, { getMapTiles } from '@/api/service/common'
import FileFullScreen from '@/components/common/file-full-screen.vue'
import {
  drawMultiLine,
  // removeLayer,
} from '@/components/common/Map/CommonCtMapOl'
import CommonMap from '@/components/common/Map/CommonMap'
import Station from '@/components/page/ScreenPages/IntegratedMonitor/PassRoute/Station'
import MapTool from '@/components/page/ScreenPages/IntegratedMonitor/Map/MapTool/index.vue'
import { createFullViewMap } from '@/utils/map3.0'
import CTMapOl from '@ct/ct_map_ol'
import { uuid } from '@/components/common/utils'
import { Events } from '@/components/page/ScreenPages/IntegratedMonitor/enum/EventsEnum'
import {
  // lookHere,
  // queryEvtCtlRole,
  queryEvtDetail,
  queryOrderInfo,
} from '@/api/service/imScreenService'
import dayjs from 'dayjs'
import {
  // dealStateEnm,
  alarmStatusListDataDefault,
} from '@/components/page/ScreenPages/IntegratedMonitor/EventFilter/typeDatas'
import TimeLineCarouselCom from '@/components/common/TimeLineCarousel/TimeLineCarouselCom'
import RemoteComponentSyncLoader from '@ct/remote-page-sync-loader/remote-page-sync-loader.umd.js'
import { getFisUrl, getBusinessFilesAsync } from '@/utils/index.js'
import { transformPointToMercator } from '@/utils'
import ChangeRailwayType from '@/components/common/Map/ChangeRailwayType'
import PassRouteLine from '@/components/page/ScreenPages/IntegratedMonitor/PassRoute/PassRouteLine'

const remoteComp = {
  'common-comp-map': { uniqueId: '2024042465120', version: '1.7.65' },
}

const congestLevelEnm = {
  重大: 1,
  严重: 2,
  一般: 3,
  轻微: 4,
}
let littleMap = null // 小地图对象
let detailMap = null // 大地图对象
let evtRoute = null // 事件路径
let evtLittleRoute = null // 事件路径

export default {
  inject: ['mapFlag', 'mapRef'],
  props: {
    recordInfo: {
      type: Object,
      default: () => {
        return {}
      },
    },
    isCheckMode: {
      type: Boolean,
      default: () => {
        return false
      },
    },
    tileModes: {
      type: Array,
      default: () => [],
    },
  },
  components: {
    FileFullScreen,
    MapTool,
    TimeLineCarouselCom,
    RemoteComponentSyncLoader,
    TimeLineCarouselCom,
    ChangeRailwayType,
  },
  data() {
    return {
      // 通用地图相关
      loaded: false,
      mapLoaded: false,
      mapInitFlag: this.mapFlag(), // 初始化函数返回值
      littleMapShow: false,
      detailMapId: 'detail_map',
      littleMapId: 'little_map',
      components: {}, // 配置组件
      linkDetailList: [], // 当前环节
      fileType: '', // 右侧展示的文件类型 video/image/map
      orderInfo: {}, // 基本信息
      eventInfo: {}, // 铁路界限内信息
      detailInfo: {}, // 详情信息
      imgList: [], // 图片列表
      videoList: [], // 视频列表
      stepList: [], // 处置步骤
      players: [], // 存储所有播放器实例
      playerOptions: {
        // 视频播放参数
        autoplay: true, // 自动播放
        loop: true, // 循环播放
        muted: true, // 静音
        preload: 'auto', // 自动加载
        language: 'zh-CN',
        fluid: true, // 流体布局
        hls: false,
        sources: [
          {
            type: 'video/mp4',
            src: '', // 视频源
          },
        ],
        aspectRatio: '16:9',
        poster: '', // 封面地址
        choosed: false, // 被选中的
        notSupportedMessage: '视频录像服务维护中', // 不支持时的提示信息
        controlBar: {
          timeDivider: false,
          durationDisplay: false,
          remainingTimeDisplay: false,
          fullscreenToggle: true, // 全屏按钮
        },
      },
      playerOptionsLittle: {
        // 小视频播放参数
        autoplay: false, // 不自动播放
        loop: true, // 循环播放
        muted: true, // 静音
        preload: 'auto', // 自动加载
        language: 'zh-CN',
        fluid: true, // 流体布局
        hls: false,
        sources: [
          {
            type: 'video/mp4',
            src: '', // 视频源
          },
        ],
        aspectRatio: '16:9',
        poster: '', // 封面地址
        choosed: false, // 被选中的
        notSupportedMessage: '视频录像服务维护中', // 不支持时的提示信息
        controlBar: {
          timeDivider: false,
          durationDisplay: false,
          remainingTimeDisplay: false,
          fullscreenToggle: false, // 全屏按钮
        },
      },
      cityName: '北京市',
      showFileFull: false, // 是否显示全屏查看附件
      fileFullList: null, // 全屏查看附件数据
      fullScreenElementId: null, // 全屏展示的元素id
      // 附件相关
      currentFileUrl: '',
      progressTimer: [],
      relAlarmIds: [],
      imgVideoInfo: { imgs: [], video: null }, //图片视频信息
      extendedFieldsOfAi: [],
      kakouAlarmList: [
        {
          label: '车牌号',
          value: 'vehicleNumber',
        },
        {
          label: '车辆类型',
          value: 'vehicleType',
        },
        {
          label: '车牌颜色',
          value: 'vehicleNumColor',
        },
        {
          label: '车辆颜色',
          value: 'vehicleColor',
        },
      ],
    }
  },
  created() {
    // 监听基本类型（通过函数）
    this.$watch(
      () => this.mapFlag(), // 监听函数返回值
      newVal => {
        this.mapInitFlag = newVal
      }
    )

    // 初始化
    this.getCommonData('extendedFieldsOfAi')
  },
  computed: {
    getOccTime() {
      return key => (key ? dayjs(key).format('YYYY-MM-DD HH:mm:ss') : key)
    },
    getCongestLevel() {
      return key => congestLevelEnm?.[key] || ''
    },
    getFusionStatus() {
      return key => {
        const obj = alarmStatusListDataDefault.find(v =>
          v.dictValue.includes(key)
        )
        return obj || {}
      }
    },
    getPlayerOptionsLittle() {
      return fileUrl => ({
        ...this.playerOptionsLittle,
        sources: [
          {
            type: 'video/mp4',
            src: fileUrl,
          },
        ],
      })
    },
    extraFieldValueRadar() {
      return value => {
        try {
          if (!this.orderInfo?.extraField) {
            return ''
          }
          let itemData = JSON.parse(this.orderInfo?.extraField)
          return itemData[value]
        } catch (e) {
          return ''
        }
      }
    },
    // 事件描述数据处理
    extraData() {
      const { extraField, warningSource } = this.orderInfo
      let result = {}
      if (extraField) {
        try {
          const res = JSON.parse(extraField)
          if (!res) {
            return result
          }
          const types = ['1', '4']
          if (types.includes(warningSource) || Array.isArray(res)) {
            let tempList = []
            res.map(item =>
              item.attrList?.forEach(node => {
                tempList.push(node)
              })
            )
            result.extraList = tempList
          } else {
            result = res
          }
        } catch (e) {
          console.error(e)
        }
      }
      return result
    },
    // 事件描述算法配置参数
    algorithmConfig() {
      let { description } = this.orderInfo
      const unitMap = {
        1: '小时',
        2: '分钟',
        3: '秒',
      }
      try {
        if (description) {
          description = JSON.parse(description)
          description.forEach(item => {
            if (item.analysisPeriod) {
              let analysisPeriod = item.analysisPeriod
              item.analysisPeriod = analysisPeriod.map(time => {
                return time.startTime + '-' + time.endTime
              })
            }
            if (item.timeInterval) {
              item.timeInterval =
                item.timeInterval + unitMap[item.timeIntervalUnit]
            }
          })
        }
      } catch (e) {
        return []
      }
      return description
    },
    // 事件描述
    extraFieldValueIoT() {
      return value => {
        try {
          if (!this.orderInfo?.extraField) {
            return ''
          }
          let itemData = JSON.parse(this.orderInfo?.extraField).monitors[0]
          return itemData[value]
        } catch (e) {
          return ''
        }
      }
    },
    isJsonString(str) {
      try {
        const strJson = JSON.parse(str)
        return typeof strJson !== 'number'
      } catch (e) {
        return false
      }
    },
  },
  watch: {
    mapInitFlag: {
      handler(val) {
        if (val && this.loaded) {
          // this.setMapStoreRef(this.mapRef.getMapRef(this.mapId))
          this.mapLoaded = true
          // 初始化页面
          this.initcom()
        }
      },
      immediate: true,
    },
  },
  beforeDestroy() {
    // 取消事件监听
    this.$EventBus.$off(
      Events.MAP_TOOL_EVT.MAP_LAYER_EXCHANGE,
      this.onMaLayerExchange
    )
    document.removeEventListener &&
      document.removeEventListener(
        'fullscreenchange',
        this.setFullScreenElementId
      )
    const _detailMapRef = this.mapRef.getMapRef(this.detailMapId)
    const _littleMapRef = this.mapRef.getMapRef(this.littleMapId)
    // 销毁大地图
    if (_detailMapRef) {
      // 销毁线路
      // 删除发光线路、虚线线路没有 removePassRouteLine方法
      evtRoute && evtRoute.removePassRouteLine?.()
      evtRoute = null
      // 销毁地图
      CTMapOl.MapControl.common.destroyMapInstance({
        mapRef: _detailMapRef,
      })
      detailMap = null
      this.mapRef.setMapRef(
        this.detailMapId,
        {
          mapRef: _detailMapRef,
        },
        true
      )
    }
    // 销毁小地图
    if (_littleMapRef) {
      // 销毁线路
      // 删除发光线路、虚线线路没有 removePassRouteLine方法
      evtLittleRoute && evtLittleRoute.removePassRouteLine?.()
      evtLittleRoute = null
      // 销毁地图
      CTMapOl.MapControl.common.destroyMapInstance({
        mapRef: _littleMapRef,
      })
      littleMap = null
      this.mapRef.setMapRef(
        this.littleMapId,
        {
          mapRef: _littleMapRef,
        },
        true
      )
    }
  },
  destroyed() {
    // 销毁站点
    Station.clearStation()
  },
  async mounted() {
    // 通用组件
    await this.initcom()
    // 加载完成后获取事件详情
    this.getEventDetail()
    // 监听全屏变化事件
    document.addEventListener('fullscreenchange', this.setFullScreenElementId)
    // 监听地图图层切换事件
    this.$EventBus.$on(
      Events.MAP_TOOL_EVT.MAP_LAYER_EXCHANGE,
      this.onMaLayerExchange
    )
  },
  methods: {
    // 初始化通用字典数据
    getCommonData(dictType) {
      commonService.getCommonPlatDictByDictType({ dictType }).then(res => {
        const { code, data } = res
        if (code === 200) {
          this.extendedFieldsOfAi = data?.map(item => ({
            codeName: item.dictLabel,
            code: item.dictValue,
          }))
        }
      })
    },
    /**
     * 远程组件加载
     */
    async initcom() {
      const { data } = await getFisUrl()
      const { configValue } = data || {}
      console.log('当前配置远程域名为---：', configValue)
      const { components, loaded } = await this.getRemoteComps(
        configValue,
        getBusinessFilesAsync
      )
      this.components = components
      this.loaded = loaded
    },
    /**
     * 获取远程组件
     * 配置项
     */
    async getRemoteComps(configValue, callback) {
      const param = {
        components: Object.values(remoteComp),
      }
      const { components, loaded } = await callback(configValue, param)
      return { components, loaded }
    },
    /**
     * 设置退出全屏模式
     */
    setFullScreenElementId() {
      if (!document.fullscreenElement) {
        // 退出全屏模式
        this.fullScreenElementId = null
      }
    },
    // 切换全屏模式
    fullScreen(id) {
      if (!document.fullscreenElement) {
        const element = document.getElementById(id)
        // 进入全屏
        if (element?.requestFullscreen) {
          element
            .requestFullscreen()
            .then(() => {
              this.fullScreenElementId = id
            })
            .catch(err => {
              console.error('进入全屏失败:', err)
            })
        }
      } else {
        // 退出全屏
        if (document.exitFullscreen) {
          document
            .exitFullscreen()
            .then(() => {
              this.fullScreenElementId = null
            })
            .catch(err => {
              console.error('退出全屏失败:', err)
            })
        }
      }
    },
    /**
     * 合并平台上传附件以及用户上传附件
     * @param record
     */
    mergeFiles(record) {
      let files = []
      const recordFiles =
        record.files && record.files.length > 0 ? record.files : []
      const orderFileList =
        record.orderFileList && record.orderFileList.length > 0
          ? record.orderFileList
          : []
      files.push(
        ...[...recordFiles, ...orderFileList].map(file => {
          return {
            ...file,
            resourceType: file.resourceType,
            resourceUrl: file.resourceUrl,
            fileName: file.fileName,
          }
        })
      )
      return files
    },
    // 查询事件详情
    async getEventDetail() {
      const [, resData] = await queryEvtDetail({
        id: this.recordInfo.id,
      })
      // 测试数据 resData ??  evtDetailData.data
      const data = resData
      // 取alarmList最后一个值
      const warningOrderId = data.alarmList?.length
        ? data.alarmList[data.alarmList.length - 1].alarmId
        : ''
      // 告警详情,取来源信息、处置信息
      const resOrder = await queryOrderInfo({ warningOrderId })

      // 测试数据
      const resOrderData = { data: resOrder?.[1] || {} }
      if (data) {
        const _data = {
          ...data,
          longitude: data.startLng,
          latitude: data.startLat,
        }
        this.eventInfo = _data
        this.detailInfo = resOrderData?.data
        this.orderInfo = resOrderData?.data?.order // 处置信息
        this.imgList = resOrderData?.data?.imgs
        this.videoList = resOrderData?.data?.video?.videoList
        // 处理流程、附件
        let stepListTemp = []
        if (resOrderData?.data?.records?.length > 0) {
          resOrderData.data.records.forEach(item => {
            item.docList = this.computeStepDocList(item)
            item.mediaFileList = this.computeStepMediaFileList(item)
            stepListTemp.push(item)
          })
        }
        this.stepList = [...stepListTemp].reverse()
        // this.$set(this.stepList, this.stepList)
        // 设置图片、视频
        this.getMediaInfo()
        // 初始地图
        this.initLittleMap()
        // 当前环节
        await this.queryLinkData()
      }

      // api
      //   .post(`${this.$env.VUE_APP_REQ_PREFIX_BIZ}/adsEvtInfo/detail`, {
      //     warningOrderId: this.recordInfo.orderId,
      //   })
      //   .then(res => {
      //     if (res.code == 200) {
      //       this.orderInfo = res.data.order
      //       this.eventInfo = res.data.evtInfo
      //       this.detailInfo = res.data
      //       this.imgList = res.data.imgs
      //       this.videoList = res.data.video?.videoList
      //       let stepListTemp = []
      //       if (res.data.records && res.data.records.length > 0) {
      //         res.data.records.forEach(item => {
      //           item.docList = this.computeStepDocList(item)
      //           item.mediaFileList = this.computeStepMediaFileList(item)
      //           stepListTemp.push(item)
      //         })
      //       }
      //       this.stepList = [...stepListTemp].reverse()
      //       if (res.data.video?.videoList?.length > 0) {
      //         this.playerOptions.sources = [
      //           {
      //             type: 'video/mp4',
      //             src: res.data.video.videoList[0].src,
      //           },
      //         ]
      //         this.playerOptionsLittle.sources = [
      //           {
      //             type: 'video/mp4',
      //             src: res.data.video.videoList[0].src,
      //           },
      //         ]
      //         this.onEventFileClick('video')
      //       } else if (res.data.imgs?.length > 0) {
      //         this.onEventFileClick('image')
      //       } else {
      //         this.onEventFileClick('map')
      //       }
      //       this.$set(this.stepList, this.stepList)
      //       this.initLittleMap()
      //     } else {
      //       this.$message({
      //         type: 'error',
      //         message: '查询详情失败：' + e.msg,
      //       })
      //     }
      //   })
    },
    // 查询告警的图片
    handleCurrentChange(item) {
      !['1', '2', '3'].includes(this.eventInfo?.dataSource) &&
        this.getAlarmImgVideo(this.relAlarmIds[item - 1])
    },
    /**
     * 获取图片、视频
     */
    getMediaInfo() {
      const relAlarmList = []
      this.progressTimer = (this.eventInfo?.alarmList || []).map(v => {
        relAlarmList.push(v.alarmId)
        return {
          ...v,
          date: dayjs(v.time).format('MM-DD'),
          clock: dayjs(v.time).format('HH:mm'),
        }
      })
      this.relAlarmIds = relAlarmList
      // 如果res.data.dataSource有值，且值为1/2/3则直接把res.data.fileIconUrl放入this.imgVideoInfo
      if (
        this.eventInfo.dataSource &&
        ['1', '2', '3'].includes(this.eventInfo.dataSource)
      ) {
        // 加载图片
        const imgSrc = this.eventInfo.fileImgUrlIcon
          ? this.eventInfo.fileImgUrlIcon
          : require('@/assets/images/alarmEvent/alarm/alarmNull.png')
        this.imgList = [
          {
            fileUrl: imgSrc,
          },
        ]
        // 选中图片高亮
        this.onEventFileClick('image', imgSrc)
        // 没有视频
        this.videoList = []
      } else {
        relAlarmList.length ? this.getAlarmImgVideo(relAlarmList[0]) : null
      }
    },
    /**
     * 查询视频、图片
     */
    async getAlarmImgVideo(alarmId) {
      const params = {
        warningOrderId: alarmId,
      }
      const res = await queryOrderInfo(params)
      // 测试数据
      const resOrderData = { data: res?.[1] || {} }
      // 图片、视频
      this.videoList = resOrderData.data?.video?.videoList || []
      // 测试数据
      // [
      //   { fileUrl: require('../../../../assets/movie.mp4') },
      //   { fileUrl: require('../../../../assets/movie.mp4') },
      // ]
      this.imgList = resOrderData.data?.imgs || []
      // [
      //   { fileUrl: require('../../../../assets/images/comm/logo.png') },
      //   { fileUrl: require('../../../../assets/images/comm/nonImg.png') },
      // ]
      // 加载有的数据
      if (this.videoList?.length > 0) {
        this.onEventFileClick('video', this.videoList[0].src)
      } else if (this.imgList?.length > 0) {
        this.onEventFileClick('image', this.imgList[0].fileUrl)
      } else {
        this.onEventFileClick('map')
      }
    },
    /**
     * 查询当前环节
     */
    async queryLinkData() {
      const resLinkData = await api.post(
        `${this.$env.VUE_APP_REQ_PREFIX_PLAT}/event/queryFlowLink`,
        {
          flowId: this.orderInfo?.flowId,
          curLink: this.orderInfo?.linkId,
          templateId: this.orderInfo?.flowTemplate, //模板id
          warningSource: this.orderInfo?.warningSource, //告警来源
        }
      )
      this.linkDetailList = resLinkData.data || []
    },
    // 计算处置流程中文档附件
    computeStepDocList(stepInfo) {
      let resList = []
      if (stepInfo.files && stepInfo.files.length > 0) {
        resList.push(
          ...stepInfo.files.filter(item => item.resourceType === '3')
        )
      }
      if (stepInfo.orderFileList && stepInfo.orderFileList.length > 0) {
        resList.push(
          ...stepInfo.orderFileList.filter(item => item.resourceType === '3')
        )
      }
      console.info('computeDocList res---------------------', resList)
      return resList
    },
    // 计算处置流程中媒体文件附件
    computeStepMediaFileList(stepInfo) {
      let videoList = []
      let picList = []
      if (stepInfo.files && stepInfo.files.length > 0) {
        picList.push(
          ...stepInfo.files.filter(item => item.resourceType === '1')
        )
        videoList.push(
          ...stepInfo.files.filter(item => item.resourceType === '2')
        )
      }
      if (stepInfo.orderFileList && stepInfo.orderFileList.length > 0) {
        picList.push(
          ...stepInfo.orderFileList.filter(item => item.resourceType === '1')
        )
        videoList.push(
          ...stepInfo.orderFileList.filter(item => item.resourceType === '2')
        )
      }
      console.info(
        'computeStepMediaFileList res---------------------',
        picList,
        videoList
      )
      return [...videoList, ...picList]
    },
    // 点击右侧下方事件视频/图片/地图信息
    onEventFileClick(type, currentFileUrl = '') {
      this.fileType = type
      this.currentFileUrl = currentFileUrl
      if (type == 'map') {
        this.initMap()
      }
    },
    /**
     * 视频播放失败重新加载
     */
    playStatechanged(status) {
      if (status.error) {
        console.log('player current update state', status)
        $($('.vjs-modal-dialog .vjs-modal-dialog-content')[0]).html(
          '视频正在努力生成中，请稍后...'
        )
      }
    },
    /**
     * 存储播放器实例
     * @param {*} player
     * @param {*} index
     */
    onPlayerReady(player, index) {
      // 存储播放器实例
      this.players[index] = player
    },
    /**
     * 当某个视频播放时，暂停其他所有视频
     * @param {*} player
     * @param {*} index
     */
    onPlayerPlay(player, index) {
      // 当某个视频播放时，暂停其他所有视频
      this.players.forEach((p, i) => {
        if (i !== index && p) {
          p.pause()
        }
      })
    },
    /**
     * 切换幻灯片时暂停之前播放的视频
     */
    onPageChange(index) {
      // 切换幻灯片时暂停之前播放的视频
      this.players?.[index]?.pause()
    },
    // 初始化地图
    async initMap() {
      if (!detailMap) {
        if (!window.hjCfg?.defZoom) {
          await getMapTiles()
        }
        const { MAP_CFG } = window.hjCfg
        detailMap = createFullViewMap(this.mapRef.getMapRef(this.detailMapId), {
          // center: [
          //   parseFloat(this.eventInfo?.longitude),
          //   parseFloat(this.eventInfo?.latitude),
          // ],
          zoom: 9.7,
          maxZoom: MAP_CFG.maxZoom || 18,
          minZoom: MAP_CFG.minZoom || 3,
        })
        // 遮罩
        // const maskLayer = new CTMapOl.layer.Vector({
        //   source: new CTMapOl.source.Vector(),
        // })
        // detailMap.addLayer(maskLayer)
        // 喇叭锚点图层
        // const markersLayer = new CTMapOl.layer.Vector({
        //   source: new CTMapOl.source.Vector(),
        // })
        // detailMap.addLayer(markersLayer)
        // 线路图层
        // const lineLayer = new CTMapOl.layer.Vector({
        //   source: new CTMapOl.source.Vector(),
        // })
        // detailMap.addLayer(lineLayer)
        // window.map = this.map
        // 渲染路段线路
        this.drawRoute(detailMap, 0)
        // 绘制站点
        this.drawStations(detailMap)
      }
    },
    // 初始化小地图
    async initLittleMap() {
      if (!littleMap) {
        if (!window.hjCfg?.defZoom) {
          await getMapTiles()
        }
        littleMap = createFullViewMap(this.mapRef.getMapRef(this.littleMapId), {
          // center: [
          //   parseFloat(this.eventInfo?.longitude),
          //   parseFloat(this.eventInfo?.latitude),
          // ],
          zoom: 7,
          maxZoom: 18,
          minZoom: 3,
        })
        this.littleMapShow = true
        // 喇叭锚点图层
        // const littleMarkersLayer = new CTMapOl.layer.Vector({
        //   source: new CTMapOl.source.Vector(),
        // })
        // littleMap.addLayer(littleMarkersLayer)
        // 线路图层
        // const littleLineLayer = new CTMapOl.layer.Vector({
        //   source: new CTMapOl.source.Vector(),
        // })
        // littleMap.addLayer(littleLineLayer)
        // window.littleMap = this.littleMap
        // 渲染路段线路
        this.drawRoute(littleMap, 1)
        // 绘制站点
        this.drawLittleStations(littleMap)
      }
    },
    // 绘制线路
    drawRoute(mapIns, key) {
      const segment = this.eventInfo.segmentLink
      if (!segment || !segment?.shape) {
        return
      }
      // 虚线线路
      // this.addDataForRoute(segment.shape, mapIns, key)
      // 发光线路
      this.addDataForRouteNew(segment.shape, mapIns, key)
    },
    /**
     * 虚线线路
     * @param mapIns
     * @param key
     * @param pointArray
     */
    addDataForRoute(shape, mapIns, key) {
      let pointArray = CommonMap.wktToPoint(shape, true)
      if (pointArray?.[0]?.length === 2) {
        pointArray = [pointArray]
      } else {
        // to do
      }
      const _route = drawMultiLine({
        paths: pointArray,
        layer: key === 0 ? evtRoute : evtLittleRoute,
        options: {
          strokeColor: '#D5FF00',
          strokeStyle: 'dashed',
        },
        mapIns: mapIns,
      })
      if (key === 0) evtRoute = _route
      else evtLittleRoute = _route
    },
    /**
     * 发光线路
     * 绘制路线的函数。
     * 根据当前点的信息，解析形状数据并绘制多边形路线。
     */
    addDataForRouteNew(shape, mapIns, key) {
      let pointArray = CommonMap.wktToPoint(shape)
      if (pointArray[0]?.length === 2) {
        pointArray = pointArray
      } else {
        pointArray = pointArray[0]
      }
      const _route = new PassRouteLine({
        props: {},
        pointArray,
        map: mapIns,
      })
      if (key === 0) evtRoute = _route
      else evtLittleRoute = _route
    },
    // 绘制站点
    drawStations(mapIns) {
      const stationList = this.eventInfo.stations
      if (stationList?.length > 0) {
        stationList.forEach((item, index) => {
          if (item.lon && item.lat) {
            const startL = transformPointToMercator([
              Number(item.lon),
              Number(item.lat),
            ])
            if (index % 2 == 0) {
              new Station({
                id: uuid(), //item.stationId,
                props: {
                  name: item.stationName,
                },
                lng: startL[0],
                lat: startL[1],
                map: mapIns,
              }).drawSideLeft()
            } else {
              new Station({
                id: uuid(), //item.stationId,
                props: {
                  name: item.stationName,
                },
                lng: startL[0],
                lat: startL[1],
                map: mapIns,
              }).drawSideRight()
            }
          }
        })
      }
      const lng_lat = transformPointToMercator([
        Number(this.eventInfo?.longitude),
        Number(this.eventInfo?.latitude),
      ])
      const _stationObj = {
        id: 'current-point',
        props: {
          // this.eventInfo?.segmentLink?.segmentName
          segName: this.eventInfo.segmentName,
          address: this.eventInfo?.eventAddress,
        },
        lng: lng_lat[0],
        lat: lng_lat[1],
        map: mapIns,
      }
      if (mapIns != littleMap) {
        // 当前站点
        new Station(_stationObj).drawFocusStation()
      } else {
        // 当前站点
        new Station(_stationObj).drawEventMarker()
      }
      mapIns.getView().animate({
        center: lng_lat,
        zoom: 9.7,
        duration: 1500,
      })
    },
    // 绘制小地图站点
    drawLittleStations(mapIns) {
      const stationList = this.eventInfo.stations
      // 绘制线路上站点
      if (stationList?.length > 0) {
        stationList.forEach((item, index) => {
          if (item.lon && item.lat) {
            const startL = transformPointToMercator([
              Number(item.lon),
              Number(item.lat),
            ])
            if (index % 2 == 0) {
              new Station({
                id: uuid(), //item.stationId,
                props: {
                  name: item.stationName,
                },
                lng: startL[0],
                lat: startL[1],
                map: mapIns,
              }).drawSideLeftLittle()
            } else {
              new Station({
                id: uuid(), //item.stationId,
                props: {
                  name: item.stationName,
                },
                lng: startL[0],
                lat: startL[1],
                map: mapIns,
              }).drawSideRightLittle()
            }
          }
        })
      }
      // 当前站点
      const lng_lat = transformPointToMercator([
        Number(this.eventInfo?.longitude),
        Number(this.eventInfo?.latitude),
      ])
      new Station({
        id: 'current-point',
        props: {
          segName: this.eventInfo?.segmentLink?.segmentName,
          address: this.eventInfo?.eventAddress,
        },
        lng: lng_lat[0],
        lat: lng_lat[1],
        map: mapIns,
      }).drawEventMarker()
      mapIns.getView().animate({
        center: lng_lat,
        zoom: 7,
        duration: 1500,
      })
    },
    // 地图改变模式
    onMaLayerExchange(newval) {
      if (newval === 0) {
        window.map.changeLayersHj('vector', 0)
      }
      if (newval === 1) {
        window.map.changeLayersHj('satellite', 1)
      }
      if (newval === 2) {
        window.map.changeLayersHj('vector', 2)
      }
    },
    // 环节处置时限，需要根据item.linkId匹配flows里linkId,取出流程步骤时限值
    getLinkTimeLimit(linkId) {
      let timeLimit = 0
      if (this.linkDetailList.length > 0) {
        this.linkDetailList.forEach(item => {
          if (item.linkId == linkId) {
            timeLimit = item.timeLimit
          }
        })
      }
      return this.transformTimeLimit(timeLimit)
    },
    // 环节处置时限从分钟转换为天小时分钟
    transformTimeLimit(timeLimit) {
      let rsStr = ''
      if (timeLimit) {
        // 如果超过1天显示天
        if (timeLimit / (60 * 24 * 60) > 1) {
          rsStr = `${Math.floor(timeLimit / (60 * 24 * 60))}天${Math.floor(
            (timeLimit % (60 * 24 * 60)) / 3600
          )}小时${Math.floor((timeLimit % (60 * 60)) / 60)}分钟`
        } else {
          rsStr = `${Math.floor(
            (timeLimit % (60 * 24 * 60)) / 3600
          )}小时${Math.floor((timeLimit % (60 * 60)) / 60)}分钟`
        }
      }
      return rsStr
    },
    // 流程节点办理时长
    flowDealTime(item) {
      if (item.flowEndTime && item.flowNodeTime) {
        return this.transformTimeLimit(
          dayjs(item.flowEndTime).diff(dayjs(item.flowNodeTime), 'second')
        )
      } else {
        return ''
      }
    },
    getTimeLimit(item) {
      let timeLimit = 0
      if (this.detailInfo.flows && this.detailInfo.flows.length > 0) {
        this.detailInfo.flows.forEach(flowItem => {
          if (flowItem.linkId == item.linkId) {
            timeLimit = flowItem.timeLimit
          }
        })
      }
      return timeLimit
    },
    // 检查是否超时
    checkOverTime(item) {
      let flowuseTime = 0
      // 如果节点有结束时间和开始时间
      if (item.flowEndTime && item.flowNodeTime) {
        flowuseTime = dayjs(item.flowEndTime).diff(
          dayjs(item.flowNodeTime),
          'second'
        )
      } else if (item.flowNodeTime) {
        // 如果节点有开始时间，没有结束时间，则计算当前时间与开始时间差
        flowuseTime = dayjs().diff(dayjs(item.flowNodeTime), 'second')
      } else {
        // 流程节点开始和结束都为空，脏数据
        flowuseTime = -1
      }
      // 获取环节要求时限
      let timeLimit = this.getTimeLimit(item)
      if (flowuseTime > 0 && timeLimit > 0 && flowuseTime > timeLimit) {
        return true
      } else {
        return false
      }
    },
    // 计算超时的时间
    getOverTimeStr(item) {
      let flowuseTime
      // 如果节点有结束时间和开始时间
      if (item.flowEndTime && item.flowNodeTime) {
        flowuseTime = dayjs(item.flowEndTime).diff(
          dayjs(item.flowNodeTime),
          'second'
        )
      } else if (item.flowNodeTime) {
        // 如果节点有开始时间，没有结束时间，则计算当前时间与开始时间差
        flowuseTime = dayjs().diff(dayjs(item.flowNodeTime), 'second')
      } else {
        flowuseTime = 0
      }
      // 获取环节要求时限
      let timeLimit = this.getTimeLimit(item)
      flowuseTime = flowuseTime - timeLimit
      return this.transformTimeLimit(flowuseTime)
    },
    // 遍历详情数据，获取处置流程里用户所属组织
    getProcessOrg(userId) {
      let processOrg = ''
      if (userId && this.detailInfo.users?.length > 0) {
        this.detailInfo.users.forEach(item => {
          if (item.userId == userId) {
            processOrg = item.dept?.deptName
          }
        })
      }
      return processOrg
    },
    /**
     * 信息图片预览
     */
    openImgViewer(item, index) {
      let fileFullList = {}
      fileFullList.isVideo = true
      fileFullList.index = index
      fileFullList.videoImgUrl = []
      item.forEach(item2 => {
        fileFullList.videoImgUrl.push(item2.resourceUrl)
      })
      this.fileFullList = fileFullList
      this.showFileFull = true
    },
    // 下载文档
    downLoadDoc(src, name) {
      api.downloadExcelPost(
        `${vue.$env.VUE_APP_REQ_PREFIX_PLAT}/system/downloadByUrl`,
        { fileName: name, url: src }
      )
    },
    /**
     * 关闭全屏预览
     */
    closeFull() {
      this.showFileFull = false
    },
  },
}
</script>
<style lang='less' scoped>
.full_screen_button {
  width: 40px;
  height: 40px;
  background: #3d4c57;
  position: absolute;
  right: 20px;
  bottom: 150px;
  cursor: pointer;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}
.event_file_detail_main {
  width: 100%;
  height: 100%;
  overflow: auto;
  .event_file_detail_left {
    width: calc(55% - 10px);
    margin-left: 10px;
    height: 100%;
    float: left;
    overflow-y: auto;
    .event_file_detail_left_title {
      width: 100%;
      height: 32px;
      margin-bottom: 20px;
      float: left;
      font-size: 14px;
      color: #e8f3ff;
      display: flex;
      align-items: center;
      background: url('~@/assets/images/alarmEvent/eventFile/bg_title_level_2.png')
        no-repeat 100% 100%;
      background-size: cover;
      img {
        margin-left: 6px;
      }
    }
    .event_file_detail_left_title_2 {
      margin-top: 10px;
    }
    .event_file_detail_left_item {
      width: 50%;
      height: 32px;
      float: left;
      font-size: 14px;
      line-height: 32px;
      white-space: nowrap;
      display: flex;
      .event_file_detail_left_item_title {
        width: auto;
        height: 100%;
        float: left;
        font-size: 14px;
        margin-left: 30px;
        color: rgba(232, 243, 255, 0.7);
      }
      .event_file_detail_left_item_value {
        width: auto;
        height: 100%;
        color: #e8f3ff;
        float: left;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }

      .level-tag1 {
        color: #ff0000;
      }

      .level-tag2 {
        color: #fb913c;
      }

      .level-tag3 {
        color: #ffe000;
      }

      .level-tag4 {
        color: #4f9fff;
      }
    }
    .event_file_detail_left_item_long {
      width: 100%;
      min-height: 32px;
      float: left;
      font-size: 14px;
      line-height: 32px;
      white-space: nowrap;
      display: flex;
      .event_file_detail_left_item_title {
        width: auto;
        height: 100%;
        float: left;
        font-size: 14px;
        margin-left: 30px;
        color: rgba(232, 243, 255, 0.7);
      }
      .event_file_detail_left_item_value {
        width: auto;
        height: 100%;
        color: #e8f3ff;
        float: left;
        white-space: normal;
        // text-overflow: ellipsis;
        // white-space: nowrap;
        // overflow: hidden;
      }
    }
    .event_file_detail_left_step {
      width: 100%;
      height: auto;
      float: left;
      .event_file_detail_left_step_order {
        height: 24px;
        width: calc(100% - 32px);
        float: left;
        margin-left: 32px;
        .event_file_detail_left_step_order_num {
          height: 24px;
          width: 24px;
          text-align: center;
          line-height: 24px;
          float: left;
          border-radius: 12px;
          background: #369eff;
          color: #ffffff;
          font-size: 12px;
        }
        .event_file_detail_left_step_order_time {
          height: 24px;
          width: auto;
          line-height: 24px;
          font-size: 14px;
          margin-left: 10px;
          float: left;
          color: #4f9fff;
        }

        .event_file_detail_left_step_order_name {
          height: 24px;
          line-height: 24px;
          width: auto;
          font-size: 14px;
          margin-left: 10px;
          float: left;
          background: rgba(79, 159, 255, 0.2);
          color: #4f9fff;
          padding-left: 10px;
          padding-right: 10px;
          border-radius: 6px;
        }
        .active {
          box-sizing: border-box;
          padding: 2px 5px;
          border-radius: 4px;
          margin-left: 12px;
          color: #ffffff;
          background-image: linear-gradient(90deg, #2fb1ec 8%, #155bd4);
        }
      }
      .event_file_detail_left_step_content {
        height: auto;
        width: calc(100% - 44px);
        float: left;
        margin-left: 44px;
        font-size: 14px;
        border-left: 1px solid #1c253f;
        .event_file_detail_left_step_item {
          width: 33%;
          height: 32px;
          line-height: 32px;
          float: left;
          color: #e8f3ff;
          display: flex;
          white-space: nowrap;
          .event_file_detail_left_step_item_title {
            width: auto;
            height: 100%;
            float: left;
            margin-left: 30px;
            color: rgba(232, 243, 255, 0.7);
          }
          .event_file_detail_left_step_item_value {
            width: auto;
            height: 100%;
            float: left;
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
            .event_file_detail_left_step_item_value_overTime {
              width: 174px;
              height: 28px;
              line-height: 28px;
              float: right;
              background: url('~@/assets/images/alarmEvent/eventFile/bg_overtime_long.png')
                no-repeat;
              background-size: 100% 100%;
            }
          }
        }

        .event_file_detail_left_step_item_file {
          width: 100%;
          height: 70px;
          float: left;
          color: #e8f3ff;
          display: flex;
          white-space: nowrap;
          margin-bottom: 6px;
          .event_file_detail_left_step_item_title {
            width: 40px;
            height: 100%;
            float: left;
            margin-left: 30px;
            color: rgba(232, 243, 255, 0.7);
          }
          .event_file_detail_left_step_item_value {
            width: calc(100% - 40px);
            height: 100%;
            float: left;
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
          }
        }
        .event_file_detail_left_step_item_file_docs {
          width: calc(100% - 70px);
          height: auto;
          float: left;
          margin-left: 70px;
          margin-bottom: 12px;
          .event_file_detail_left_step_item_file_doc_name {
            font-family: PingFangSC-Regular, sans-serif;
            font-size: 14px;
            color: #4f9fff;
            letter-spacing: 0;
            font-weight: 400;
            text-decoration: underline #4f9fff;
            margin-right: 18px;
            float: left;
            cursor: pointer;
          }
        }
      }
    }
  }
  .event_file_detail_right {
    width: 43.5%;
    height: 100%;
    float: left;
    margin-left: 0.5%;
    display: flex;

    .lunboVideo {
      width: 100%;
      height: 100%;
      /deep/.video-js {
        height: 100%;
      }
    }

    .event_file_detail_right_content {
      display: flex;
      flex-direction: column;
      flex: 1;
      margin-right: 2%;
    }

    .event_file_detail_right_up {
      width: 100%;
      height: 60%;
      background: url('~@/assets/images/alarmEvent/eventFile/bg_video_big.png')
        no-repeat;
      background-size: 100% 100%;
      color: #fff;
      display: flex;
      align-items: center;

      .event_file_detail_img_video,
      .event_file_detail_img_image {
        .full_screen_button {
          bottom: 10px;
        }
      }
      .edit_main_right_map {
        width: 90%;
        height: 85%;
        margin-left: 5%;
        position: relative;

        .maptooltele {
          width: 100%;
          height: 100%;
          pointer-events: none;
          position: absolute;
          z-index: 2;
          ::v-deep .map-tools {
            pointer-events: all;
            transition: all 0.53s;
            position: absolute;
            bottom: 0;
            right: 0;

            .tile-control,
            .compass-tool,
            .zoom-tool,
            .scale-line {
              z-index: 0;
            }

            .tile-control {
              position: absolute;
              right: 56px;
              bottom: 68px;

              .ctmap-union-layer-switcher {
                width: 88px !important;

                &:hover {
                  // px-to-rem(339)
                  width: 256px !important;
                }
              }

              // .ctmap-union-layer-switcher__layerlist {
              //   // 修改常规
              //   // 设置图层控制中替换为深色地图样式
              //   div[data-index='0'] {
              //     background-image: url('~@/components/page/ScreenPages/IntegratedMonitor/Map/MapTool/img/2D-dark-map.png') !important;
              //     background-repeat: no-repeat;

              //     .map-tile-name::before {
              //       content: '深色地图';
              //       position: absolute;
              //       background: #fff;
              //     }
              //   }
              //   // 悬浮时的样式
              //   div[data-index='0'].map-tile-item__active,
              //   div[data-index='0']:hover {
              //     .map-tile-name::before {
              //       content: '深色地图';
              //       position: absolute;
              //       color: #ffffff !important;
              //       background: #1373e6 !important;
              //     }
              //   }

              //   // 修改地形
              //   // 设置图层控制中替换为深色地图样式
              //   div[data-index='2'] {
              //     background-image: url('~@/components/page/ScreenPages/IntegratedMonitor/Map/MapTool/img/2D-map.jpeg') !important;
              //     background-repeat: no-repeat;

              //     .map-tile-name::before {
              //       content: '常规地图';
              //       position: absolute;
              //       background: #fff;
              //     }
              //   }
              //   // 悬浮时的样式
              //   div[data-index='2'].map-tile-item__active,
              //   div[data-index='2']:hover {
              //     .map-tile-name::before {
              //       content: '常规地图';
              //       position: absolute;
              //       color: #ffffff !important;
              //       background: #1373e6 !important;
              //     }
              //   }

              //   .map-tile-name {
              //     color: #172537;
              //   }

              //   .map-tile-item:hover,
              //   .map-tile-item__active {
              //     .map-tile-name {
              //       color: #e8f3fe;
              //     }
              //   }
              // }
            }
          }
        }
      }
    }
    .event_file_detail_right_down {
      height: 100%;
      width: 200px;
      padding-top: 50px;
      color: #fff;
      display: flex;
      flex-direction: column;

      .event_file_detail_right_down_panel {
        width: 100%;
        height: 150px;

        & + div {
          margin-top: 10px;
        }

        ::v-deep .el-carousel {
          width: 90%;
          height: 85%;
          margin-left: 5%;
          .el-carousel__container {
            height: 100%;
          }
          .el-carousel__indicators {
            display: none;
          }
        }
      }
      .event_file_detail_right_down_item {
        float: left;
        background: url('~@/assets/images/alarmEvent/eventFile/bg_video_no_selected.png')
          no-repeat;
        background-size: 100% 100%;
        display: flex;
        align-items: center;
      }
      .event_file_detail_right_down_item_selected {
        float: left;
        background: url('~@/assets/images/alarmEvent/eventFile/bg_video_selected.png')
          no-repeat;
        background-size: 100% 100%;
        display: flex;
        align-items: center;
      }
      .main_map_wrap {
        .little-map {
          position: relative;
          width: 90%;
          height: 85%;
          margin-left: 5%;
        }
      }
    }

    //视频图片轮播盒子
    .video-img-carousel-box {
      width: 100%;
      height: 90%;
      box-sizing: border-box;
    }

    .time-line-wraper {
      padding: 17px 0;
      margin-top: 20px;
      // border-bottom: 1px solid rgba(255, 255, 255, 0.1);

      /deep/.progress-wrapper {
        background: #0a1f3b;
        padding: 5px 10px;
        border-radius: 20px;
      }
    }
  }
  .file-image-box {
    margin: 0 2px;
    float: left;
    cursor: pointer;
  }
  .file-image {
    width: 70px;
    height: 70px;
    border-radius: 5px;
  }
  .file-video {
    height: 70px;
    width: 70px;
    object-fit: fill;
    border-radius: 5px;
  }
}
</style>
