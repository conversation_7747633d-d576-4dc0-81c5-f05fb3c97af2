<template>
  <div class="evt-handle-wrap" v-loading="sysLoading">
    <div class="preload" />
    <!-- <div id='mainMap' class='main-map' ref='mainMap'></div>-->
    <!-- chooseMapMemoryKey="eventFileMapHandle" -->
    <RemoteComponentSyncLoader
      v-if="components['common-comp-map']"
      showMapControl
      mapToolElement=".evt-handle-wrap .maptooltele"
      :class="['main-map','fix-map-tool']"
      :config="components['common-comp-map']"
      :mapId="mapId"
      chooseMapMemoryKey
      :defaultTileMode="5"
      :tileModes="tileModes"
    />
    <!-- 地图控制组件 通过 common-comp-map 组件自动生成 -->
    <div :class="['maptooltele', 'fix-map-tool']" />
    <!-- 当地图加载完毕且系统加载完成时，显示以下内容  -->
    <template v-if="loaded && mapLoaded && !sysLoading">
      <!-- 地图类型切换铁路线路 -->
      <ChangeRailwayType :mapId="mapId" :tileModes="tileModes" pageType="eventFile" />
      <Map :mapId="mapId" :onMapLoad="onMapLoad" />
      <template v-if="cityDataLoaded">
        <WindowWrapLeftSide ref="leftWrap" />
        <WindowWrapBottomSide ref="bottomWrap" />
        <Legend screenType="monitor" />
      </template>
      <!-- <MapTool useType="eventFile" /> -->
    </template>
    <!-- 地图工具 configItemMemoryNameMenu="paranomaSettingMenu"
    configItemMemoryNameMap="paranomaSettingMap"-->
    <RemoteComponentSyncLoader
      v-if="components['common-comp-tool-box']"
      :class="['tool-box', 'fix-map-tool']"
      :toolsList="toolsList"
      :overlayOptions="{
        use3DHeight: false
      }"
      :defaultCheckedIds="[4]"
      :config="components['common-comp-tool-box']"
      :mapId="mapId"
      :customLayers="customLayers"
      alarmFilteTeleport=".evt-handle-wrap"
    />
    <!-- 周边分析 -->
    <RemoteComponentSyncLoader
      v-if="components['common-comp-around-analysis']"
      :class="['aroundanalysis']"
      :config="components['common-comp-around-analysis']"
      :mapId="mapId"
    />
    <!-- 指点飞行组件 -->
    <RemoteComponentSyncLoader
      v-if="flycomponents['common-comp-guide-flight']"
      :config="flycomponents['common-comp-guide-flight']"
      :mapId="mapId"
      :right="460"
      :top="180"
    />
    <RemoteComponentSyncLoader
      v-if="components['common-comp-track-popup']"
      :config="components['common-comp-track-popup']"
      :mapId="mapId"
    />
    <RemoteComponentSyncLoader
      v-if="components['common-comp-dialog-tool']"
      :config="components['common-comp-dialog-tool']"
      :mapId="mapId"
    />
    <RemoteComponentSyncLoader
      v-if="components['common-comp-layers-tool']"
      :config="components['common-comp-layers-tool']"
      :mapId="mapId"
    />
    <RemoteComponentSyncLoader
      v-if="components['common-comp-uav-check-auth']"
      :config="components['common-comp-uav-check-auth']"
      :mapId="mapId"
    />
    <div class="tree-wrapper">
      <RemoteComponentSyncLoader
        v-if="components['common-comp-uav-tree']"
        :class="['dtreebox']"
        :config="components['common-comp-uav-tree']"
        :mapId="mapId"
      /> 
      <RemoteComponentSyncLoader
        v-if="components['common-comp-tree-recorder']"
        :class="['dtreebox']"
        :config="components['common-comp-tree-recorder']"
        :mapId="mapId"
      />
    </div>
  </div>
</template>

<script>
import api from '@/api'
import commonService from '@/api/service/common'
// import MapOptions from '@/components/common/Map/CommonMap'
import CommonMap from '@/components/common/Map/CommonMap'
import { CfgEnum } from '@/components/page/ScreenPages/IntegratedMonitor/enum/CfgEnum'
import EventDetailInfo from '@/components/page/ScreenPages/IntegratedMonitor/EventDetailInfo/index.vue'
import Map from '@/components/page/ScreenPages/IntegratedMonitor/Map/index.vue'
import LayerConst from '@/components/page/ScreenPages/IntegratedMonitor/Map/LayerConst'
import { maplayer } from '@/components/page/ScreenPages/IntegratedMonitor/Map/maplayer'
import SideCtlPanel from '@/components/page/ScreenPages/IntegratedMonitor/SideCtlPanel/index.vue'
import ToolBox from '@/components/page/ScreenPages/IntegratedMonitor/ToolBox/index.vue'
import WindowWrapBottomSide from '@/components/page/ScreenPages/IntegratedMonitor/WindowWrapBottomSide/index.vue'
import WindowWrapLeftSide from '@/components/page/ScreenPages/IntegratedMonitor/WindowWrapLeftSide/index.vue'
import MapTool from '@/components/page/ScreenPages/IntegratedMonitor/Map/MapTool/index.vue'
//  drawCircle,
import { drawCircle30 } from '@/utils/map3.0' // 绘制圆
// import { transformPointToMercator } from '@/utils'
import RemoteComponentSyncLoader from '@ct/remote-page-sync-loader/remote-page-sync-loader.umd.js'
import { getFisUrl, getBusinessFilesAsync } from '@/utils/index.js'
import CTMapOl from '@ct/ct_map_ol'
import {
  RES_RADIUSData,
  // orderResourceData,
} from '@/components/page/ScreenPages/eventFile/mockData'
import { mapMutations } from 'vuex'
import Legend from '@/components/common/legend.vue' // 图例
import ChangeRailwayType from '@/components/common/Map/ChangeRailwayType'

// 用于缓存图层的对象
const layerCache = {
  getLayer: layer => {
    return layerCache[layer] || {}
  },
}
// 定义需要显示在地图上的图层类型
const dicValue = [LayerConst.LOUDSPEAKER, LayerConst.CAMERA]
// 用于缓存配置信息的对象
const cfgCache = {}

// 使用到的远程组件定义
// key是组件内部名称，应当与uniqueId指代的具体组件完全对应，用于维护者自己区分和填写在RemoteComponentSyncLoader内
const remoteComp = {
  'common-comp-map': { uniqueId: '2024042465120', version: '1.7.65' },
  'common-comp-tool-box': { uniqueId: '2024042422835', version: '1.7.65' },
  'common-comp-dialog-tool': { uniqueId: '2024082000537', version: '1.7.00' },
  'common-comp-layers-tool': { uniqueId: '2024082008622', version: '1.7.00' },
  'common-comp-around-analysis': {
    uniqueId: '2024042467840',
    version: '1.6.90',
  },
  'common-comp-footer': { uniqueId: '2024042461157', version: '1.6.80' },
  'common-comp-alarm-detail': { uniqueId: '2024042421530', version: '1.7.65' },
  'common-comp-alarm-detail-large': {
    uniqueId: '2024072609683',
    version: '1.7.65',
  },
  'common-comp-tree': { uniqueId: '2024042483471', version: '1.7.20' },
  'common-comp-iot-tree': { uniqueId: '2024042457412', version: '1.6.90' },
  'common-comp-radar-tree': { uniqueId: '2024042467191', version: '1.6.90' },
  'common-comp-horn-tree': { uniqueId: '2024042488249', version: '1.6.90' },
  'common-comp-uav-tree': { uniqueId: '2024042495052', version: '1.7.50' },
  'common-comp-source-tree': { uniqueId: '2024042494149', version: '1.6.90' },
  'common-comp-grid-tree': { uniqueId: '2024042446567', version: '1.6.90' },
  'common-comp-grid-operator-tree': {
    uniqueId: '2024042477521',
    version: '1.6.90',
  },
  'common-comp-track-popup': { uniqueId: '2024042449828', version: '1.7.10' },
  'common-comp-tree-recorder': { uniqueId: '2024052417174', version: '1.7.20' },
  'common-comp-tool-spot': { uniqueId: '2024042413387', version: '1.6.80' },
  'common-comp-tool-space': { uniqueId: '2024042408209', version: '1.6.80' },
  'common-comp-tool-compound': { uniqueId: '2024042436593', version: '1.6.80' },
  'common-comp-tool-complex': { uniqueId: '2024042424703', version: '1.6.80' },
  'common-comp-tool-swiper': { uniqueId: '2024042410687', version: '1.6.70' },
  'common-comp-uav-check-auth': {
    uniqueId: '2025052195256',
    version: '1.7.50',
  },
}

// 指点飞行
const flyComp = {
  'common-comp-guide-flight': {
    uniqueId: '2025012119577',
    version: '1.7.50',
  },
}

export default {
  inject: ['mapFlag', 'mapRef'],
  components: {
    SideCtlPanel,
    Map,
    WindowWrapLeftSide,
    ToolBox,
    WindowWrapBottomSide,
    MapTool,
    RemoteComponentSyncLoader,
    Legend,
    ChangeRailwayType, // 切换铁路线路
  },
  props: {
    evtItem: {
      type: Object,
      required: true,
      default: {},
    },
    tileModes: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      mapId: 'mainMaps',
      // 地图是否加载完成
      mapLoaded: false,
      mapInitFlag: this.mapFlag(), // 初始化函数返回值
      loaded: false,
      components: {}, // 远程组件使用的配置项们
      initedFlyComp: false, // 指点飞行
      flycomponents: {}, // 指点飞行配置项
      // 用户省市数据
      cityDataLoaded: false,
      // 系统加载状态
      sysLoading: true,
      // 默认图层 1摄像机和4无人机
      customLayers: [
        {
          id: 4,
          footerBtns: [
            {
              key: 'fxkz', // 飞行控制
            },
            {
              key: 'zdfx', // 指点飞行
            },
            {
              key: 'sssp', // 实时视频
            },
          ],
        },
      ],
      // 远程组件使用的配置项们
      // 工具箱开启的功能列表
      toolsList: [
        {
          key: 'mapControl',
        },
        {
          key: 'smallTools',
        },
        {
          key: 'lookHere',
        },
        {
          key: 'layersControl',
        },
      ],
    }
  },
  watch: {
    mapInitFlag: {
      handler(val) {
        if (val && this.loaded) {
          this.setMapStoreRef(this.mapRef.getMapRef(this.mapId))
          this.mapLoaded = true
          // 指点飞行
          if (!this.initedFlyComp) {
            this.getFlyComp()
          }
          // 初始化页面
          this.initcom()
          // console.log('this.tileModes', this.tileModes)
        }
      },
      immediate: true,
    },
  },
  beforeDestroy() {
    // 销毁地图
    CTMapOl.MapControl.common.destroyMapInstance({
      mapRef: this.mapRef.getMapRef(this.mapId),
    })
    this.setMapStoreRef(null)
    this.mapRef.setMapRef(
      this.mapId,
      {
        mapRef: this.mapRef.getMapRef(this.mapId),
      },
      true
    )
    // this.resetMapFlag()
    // this.mapInitFlag = null
  },
  created() {
    // 监听基本类型（通过函数）
    this.$watch(
      () => this.mapFlag(), // 监听函数返回值
      newVal => {
        this.mapInitFlag = newVal
      }
    )
    // 视频初期偏移量
    window._remoteMetadata = window._remoteMetadata || {}
    const videoPositionRight = Math.ceil((window.innerHeight / 1032) * 100 * 5)
    window._remoteMetadata.videoPositionRight = videoPositionRight
  },
  async mounted() {
    const { data } = await getFisUrl()
    const { configValue } = data || {}
    console.log('当前配置远程域名为---：', configValue)
    const { components, loaded } = await this.getRemoteComps(
      configValue,
      getBusinessFilesAsync
    )
    this.components = components
    this.loaded = loaded

    // 执行下面的操作
    this.changeTheme()
  },
  methods: {
    ...mapMutations('map', ['setMapStoreRef']),
    /**
     * 初始化页面
     */
    async initcom() {
      this.sysLoading = false
    },
    /**
     * 获取远程组件
     * 配置项
     */
    async getRemoteComps(configValue, callback) {
      const param = {
        components: Object.values(remoteComp),
      }
      const { components, loaded } = await callback(configValue, param)
      return { components, loaded }
    },
    /**
     * 地图加载完成后执行的函数
     * 主要用于初始化地图配置，设置地图加载后的各种操作
     */
    onMapLoad(mapInstance) {
      // 初始化系统配置
      window.sysConfig = {
        detailMode: 'exclusive',
      }
      // 标记地图已加载
      this.cityDataLoaded = true
      // 等待DOM更新
      this.$nextTick(() => {
        // 将侧边栏组件引用赋值给全局变量
        window.leftWrap = this.$refs.leftWrap
        window.bottomWrap = this.$refs.bottomWrap
        // 加载事件响应半径配置
        commonService
          .getDictListByCatCode(CfgEnum.EVENT_RES_RADIUS.Key)
          .then(res => {
            const { resData } = res ?? {}
            const data = resData?.length ? resData : RES_RADIUSData.data
            const { codeName, code: radius } = data[0]
            // 缓存配置信息
            cfgCache[CfgEnum.EVENT_RES_RADIUS.Key] = {
              codeName,
              code: radius,
              value: Number(radius),
            }
            // 执行地图加载后的挂载操作
            this.thisMounted()
          })
      })
    },
    /**
     * 地图加载完成后执行的挂载操作
     * 主要用于添加事件详情组件和事件相关的标记
     */
    thisMounted() {
      // 在左侧窗格添加事件详情组件
      window.leftWrap.addContent({
        component: EventDetailInfo,
        props: {
          orderId: this.evtItem.id,
          detail: this.evtItem,
          closeable: false,
          markerType: LayerConst.ALARM_EVENT,
        },
        key: `eventDetailId_${this.evtItem.id}`,
        group: 'eventDetailGroup',
        onclose: this.closeInfo,
      })
      // 添加事件标记
      this.onAddMarker({
        dicValue: LayerConst.ALARM_EVENT,
        initData: [{ ...this.evtItem }],
        stopClick: true,
      })
      // 添加事件周围的标记
      this.addEventArroundMarker()
    },
    /**
     * 添加标记到地图上
     * @param {Object} param 添加标记的参数
     * @param {string} param_dicValue 标记的图层代码
     * @param {Array} param_initData 初始数据
     * @param {boolean} param_stopClick 是否阻止点击事件
     */
    async onAddMarker(param) {
      const { dicValue: layerCode, initData, stopClick } = param
      const layer = maplayer.getLayer(layerCode)
      const { markersLayer, markers } = await layer.draw({
        layerCode,
        onclick: (attr, markerFeature) => {
          // 取消marker选中状态
          layer.cancelSelected(layerCode)
        },
        initData: initData || null,
        stopClick: stopClick || false,
        mapInstance: this.mapRef.getMapRef(this.mapId).mapInstance,
      })
      // 缓存标记信息
      layerCache[layerCode] = {
        layer: markersLayer,
        objs: markers,
      }
    },
    /**
     * 添加事件周围的标记和圆形区域
     */
    async addEventArroundMarker() {
      const mapInstance = this.mapRef.getMapRef(this.mapId).mapInstance
      // 根据事件位置和配置的半径绘制圆形区域
      const { latitude, longitude } = this.evtItem
      const param = {
        lat: latitude,
        lon: longitude,
        radius: cfgCache[CfgEnum.EVENT_RES_RADIUS.Key].value,
      }
      // 清除已有的圆形
      if (cfgCache.evtAroundCircle) {
        // MapOptions.removeLayer(mapInstance, cfgCache.evtAroundCircle)
        cfgCache.evtAroundCircle.destroy()
        cfgCache.evtAroundCircle = null
      }
      // 绘制圆形
      cfgCache.evtAroundCircle = drawCircle30(
        mapInstance,
        [Number(longitude), Number(latitude)],
        cfgCache[CfgEnum.EVENT_RES_RADIUS.Key].value
      )
      // 将地图中心移动到事件位置
      mapInstance.mapToCenter([Number(longitude), Number(latitude)])
      // 根据配置请求并显示相关资源标记
      const url = `${this.$env.VUE_APP_REQ_PREFIX_BIZ}/adsEvtTypeConfig/qryWarningOrderResource`
      if (dicValue.length > 0) {
        param.dicValue = dicValue
      }
      const { data: resData } = await api.post(url, param)
      // 测试数据
      const data = resData // ?? orderResourceData.data
      if (data) {
        const { hornDeviceVoList, industryDeviceTreesList, resourceConfsList } =
          data
        // 添加扬声器标记
        await this.onAddMarker({
          dicValue: LayerConst.LOUDSPEAKER,
          initData: hornDeviceVoList || [],
        })
        // 添加摄像头标记
        await this.onAddMarker({
          dicValue: LayerConst.CAMERA,
          initData: industryDeviceTreesList || [],
        })
        // 根据配置添加其他资源标记
        if (resourceConfsList) {
          Object.keys(resourceConfsList).forEach(key => {
            this.onAddMarker({
              dicValue: key,
              initData: resourceConfsList[key] || [],
            })
          })
        }
      } else {
        // 如果请求失败，则清除地图上的所有标记
        this.clearMapMarker()
      }
    },
    /**
     * 关闭事件详情窗格并移除事件周围的标记
     */
    /**
     * 关闭事件详情窗格
     */
    closeInfo() {
      window.leftWrap.closeContent()
      this.onRemoveAroundMarker()
    },
    /**
     * 移除事件周围的标记和圆形区域
     */
    onRemoveAroundMarker() {
      // 遍历并移除所有事件周围的标记和圆形区域
      dicValue.forEach(key => {
        let layer = layerCache.getLayer(key).layer
        CommonMap.removeLayer(mapInstance, layer)
        delete layerCache[key]
        maplayer.getLayer(key).clear(key)
      })
    },
    /**
     * 指点飞行组件初始化
     * 获取配置
     */
    async getFlyComp() {
      const { data } = await getFisUrl()
      const { configValue } = data || {}
      const param = {
        components: Object.values(flyComp),
      }
      const { components, loaded } = await getBusinessFilesAsync(
        configValue,
        param
      )
      if (loaded) {
        this.flycomponents = components
        this.initedFlyComp = true
      }
    },
    /**
     * 切换主题
     * 行业主题
     */
    changeTheme() {
      document.documentElement.setAttribute('data-theme', `theme-wiseblue`)
      this.$globalEventBus.$emit('data-theme', 'theme-wiseblue')
      console.log('通用', 2)
    },
  },
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/px-to-rem';

// preload元素是一个不可见元素，它的目的就是用来预加载一些资源
.preload {
  background: url('~@/assets/images/common/left_show.png') no-repeat left
      center/100% 100%,
    url('~@/assets/images/common/right_show.png') no-repeat left center/100%
      100%;
}
.main-map {
  width: calc(100% - px-to-rem(24));
  height: 100%;
  margin: 0 px-to-rem(12);
}
.evt-handle-wrap {
  position: relative;
  width: 100%;
  height: 100%;

  .map_legend_main_outter {
    bottom: 1.5rem !important;
    right: 0.3rem !important;
  }

  .left-side-window {
    position: absolute;
    top: px-to-rem(12);
    bottom: px-to-rem(12);
    left: px-to-rem(24);
    max-height: initial;

    ::v-deep .detail-con {
      flex-basis: px-to-rem(460) !important;
    }
  }
  .bottom-side-window {
    position: absolute;
    bottom: px-to-rem(12);
    left: px-to-rem(396);
  }

  // ::v-deep {
  //   i[c-tip~='到这里'],
  //   .uav-tree-dialog-footer:has(.icon-tongyong_icon_daozheli_s_30),
  //   .footerIconArea:has(.icon-icon_daozheli_30_n),
  //   .icon-icon_guanlian_20_n,
  //   .footerIconArea:has(.icon-icon_zhoubianfenxi_30_n),
  //   .footer .f_cont .icon-icon_zhoubianfenxi_30_n {
  //     display: none !important;
  //   }
  // }

  .aroundanalysis {
    top: px-to-rem(88);
    right: px-to-rem(481);
    &.hide {
      right: px-to-rem(83);
    }
  }

  .tree-wrapper {
    position: absolute;
    flex: 1;
    top: px-to-rem(108);
    left: px-to-rem(24);
    height: calc(100% - px-to-rem(182));
    .dtreebox {
      ::v-deep .treeBox {
        left: px-to-rem(-2);
      }
    }
  }

  ::v-deep .tool-box-container {
    display: none;
  }

  .tool-box {
    top: px-to-rem(48);
    right: px-to-rem(18);
    transition: all 0.3s;
    &.fix-map-tool {
      right: px-to-rem(416);
    }

    // :deep {
    //   // .tool-box-trigger,
    //   // .tool-box-trigger__icon {
    //   //   display: none;
    //   //   // background-image: url('../../assets/images/common/icon_gongju.png') !important;
    //   // }
    // }
  }

  .maptooltele {
    width: 100%;
    height: 100%;
    pointer-events: none;
    position: relative;
    z-index: 2;
    ::v-deep .map-tools {
      pointer-events: all;
      transition: all 0.53s;
      position: absolute;
      bottom: 0;
      right: 0;

      .tile-control,
      .compass-tool,
      .zoom-tool,
      .scale-line {
        z-index: 0;
      }

      .tile-control {
        position: absolute;
        right: px-to-rem(56);
        bottom: px-to-rem(68);

        .ctmap-union-layer-switcher {
          width: px-to-rem(88) !important;

          &:hover {
            // px-to-rem(339)
            width: px-to-rem(256) !important;
          }
        }

        // .ctmap-union-layer-switcher__layerlist {
        //   // 修改常规
        //   // 设置图层控制中替换为深色地图样式
        //   div[data-index='0'] {
        //     background-image: url('~@/components/page/ScreenPages/IntegratedMonitor/Map/MapTool/img/2D-dark-map.png') !important;
        //     background-repeat: no-repeat;

        //     .map-tile-name::before {
        //       content: '深色地图';
        //       position: absolute;
        //       background: #fff;
        //     }
        //   }
        //   // 悬浮时的样式
        //   div[data-index='0'].map-tile-item__active,
        //   div[data-index='0']:hover {
        //     .map-tile-name::before {
        //       content: '深色地图';
        //       position: absolute;
        //       color: #ffffff !important;
        //       background: #1373e6 !important;
        //     }
        //   }

        //   // 修改地形
        //   // 设置图层控制中替换为深色地图样式
        //   div[data-index='2'] {
        //     background-image: url('~@/components/page/ScreenPages/IntegratedMonitor/Map/MapTool/img/2D-map.jpeg') !important;
        //     background-repeat: no-repeat;

        //     .map-tile-name::before {
        //       content: '常规地图';
        //       position: absolute;
        //       background: #fff;
        //     }
        //   }
        //   // 悬浮时的样式
        //   div[data-index='2'].map-tile-item__active,
        //   div[data-index='2']:hover {
        //     .map-tile-name::before {
        //       content: '常规地图';
        //       position: absolute;
        //       color: #ffffff !important;
        //       background: #1373e6 !important;
        //     }
        //   }

        //   .map-tile-name {
        //     color: #172537;
        //   }

        //   .map-tile-item:hover,
        //   .map-tile-item__active {
        //     .map-tile-name {
        //       color: #e8f3fe;
        //     }
        //   }
        // }
      }
    }
  }
 
}
</style>