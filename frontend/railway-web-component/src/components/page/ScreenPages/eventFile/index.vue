<!--
 * @Description:
 * @Author: liu.yongli
 * @Date: 2024-04-03 10:59:15
 * @LastEditTime: 2025-02-21 16:27:10
 * @LastEditors: liu.yongli
-->
<template>
  <div class="event_file_main">
    <!-- 显示事件文件内容，当不显示详情和处理时 -->
    <div class="event_file_content" v-if="!showDetail && !showDeal">
      <!-- 事件文件选项卡 -->
      <div class="event_file_tabs">
        <!-- 智能报警选项卡 -->
        <div
          :class="activeTab === '1' ? 'event_file_tab_item_selected' : 'event_file_tab_item'"
          @click="onClickTab('1')"
        >智能报警</div>
        <!-- 人工上报选项卡 -->
        <div
          :class="
            activeTab === '2'
              ? 'event_file_tab_item_selected'
              : 'event_file_tab_item'
          "
          @click="onClickTab('2')"
        >人工上报</div>
      </div>
      <div class="event_file_line"></div>
      <!-- 事件文件参数 -->
      <div class="event_file_params" id="event_file_params">
        <!-- 收藏和检查模式选项 -->
        <div class="event_file_param_item event_file-model-wrap">
          <el-checkbox v-model="isCollect">我的收藏</el-checkbox>
          <el-checkbox v-model="isCheckMode">
            检查模式
            <el-tooltip placement="top" effect="dark" popper-class="tooltip-dark">
              <div slot="content" :style="{ lineHeight: pxToRem(18) }">打开时可查看各环节的处置能效信息</div>
              <img
                alt="提示"
                style="margin-left: 5px;"
                :src="require('@/assets/images/alarmEvent/eventFile/icon_ask.svg')"
              />
            </el-tooltip>
          </el-checkbox>
        </div>
        <!-- 搜索框 -->
        <div class="event_file_param_item">
          <div class="event_file_param_item_title">搜索</div>
          <div class="event_file_param_item_content">
            <el-input placeholder="请输入关键字" v-model="keyWord">
              <i slot="suffix" class="el-input__icon el-icon-search" @click="changePage(1)"></i>
            </el-input>
          </div>
        </div>
        <!-- 是否重点关注 -->
        <div class="event_file_param_item">
          <div class="event_file_param_item_title">是否重点关注</div>
          <div class="event_file_param_item_content">
            <el-select v-model="isFocus" placeholder="请选择" clearable @change="onIsFocusChange">
              <el-option label="重点关注" :value="true"></el-option>
              <el-option label="非重点关注" :value="false"></el-option>
            </el-select>
          </div>
        </div>
        <!-- 区域选择 -->
        <div class="event_file_param_item">
          <div class="event_file_param_item_title">区域</div>
          <div class="event_file_param_item_content">
            <el-cascader
              v-model="areaIds"
              :options="areaList"
              :props="{ checkStrictly: true, multiple: true }"
              :popper-class="'event_file_cascader'"
              clearable
              collapse-tags
            ></el-cascader>
          </div>
        </div>
        <!-- 事件等级选择 -->
        <div class="event_file_param_item">
          <div class="event_file_param_item_title">事件等级</div>
          <div class="event_file_param_item_content">
            <el-select v-model="eventLevels" placeholder="请选择" multiple clearable>
              <el-option
                v-for="item in eventLevelList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </div>
        </div>
        <div class="event_file_param_item">
          <div class="event_file_param_item_title">事件状态</div>
          <div class="event_file_param_item_content">
            <el-select v-model="eventState" placeholder="请选择" multiple clearable>
              <el-option
                v-for="item in eventStateList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </div>
        </div>
        <!-- 办理状态选择 -->
        <!-- <div class="event_file_param_item">
          <div class="event_file_param_item_title">办理状态</div>
          <div class="event_file_param_item_content">
            <el-select v-model="eventStatus" multiple placeholder="请选择" collapse-tags clearable>
              <el-option
                v-for="item in eventStatusList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </div>
        </div>-->
        <!-- 处置环节选择 -->
        <div class="event_file_param_item">
          <div class="event_file_param_item_title">处置状态</div>
          <div class="event_file_param_item_content">
            <el-select v-model="eventOrderStatus" placeholder="请选择" multiple clearable>
              <el-option
                v-for="item in evtOrderStatusList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </div>
        </div>
        <!-- <div class="event_file_param_item" v-if="eventStatus.length == 1 && eventStatus[0] == '0'">
          <div class="event_file_param_item_title">处置环节</div>
          <div class="event_file_param_item_content">
            <el-select v-model="dealProcess" multiple placeholder="请选择" collapse-tags clearable>
              <el-option
                v-for="item in dealProcessList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </div>
        </div>-->
        <!-- 完结方式选择 -->
        <!-- <div class="event_file_param_item" v-if="eventStatus.length == 1 && eventStatus[0] == '1'">
          <div class="event_file_param_item_title">完结方式</div>
          <div class="event_file_param_item_content">
            <el-select v-model="finishTypes" multiple placeholder="请选择" collapse-tags clearable>
              <el-option
                v-for="item in finishTypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </div>
        </div>-->
        <!-- 事件类型选择 -->
        <div class="event_file_param_item">
          <div class="event_file_param_item_title">事件类型</div>
          <div class="event_file_param_item_content">
            <el-select v-model="eventTypes" multiple placeholder="请选择" collapse-tags clearable>
              <!-- eventTypeList -->
              <el-option
                v-for="(item, index) in evtTypeList"
                :key="index"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </div>
        </div>
        <!-- 摄像机名称选择 -->
        <div class="event_file_param_item" v-if="activeTab === '1'">
          <div class="event_file_param_item_title">摄像机名称</div>
          <div class="event_file_param_item_content">
            <el-select v-model="cameraId" filterable placeholder="请选择" clearable key="1">
              <el-option
                v-for="item in cameraList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </div>
        </div>
        <!-- 上报人选择 -->
        <div class="event_file_param_item" v-if="activeTab === '2'">
          <div class="event_file_param_item_title">上报人</div>
          <div class="event_file_param_item_content">
            <el-select
              v-model="reporter"
              placeholder="请选择"
              filterable
              multiple
              collapse-tags
              clearable
              key="2"
            >
              <el-option
                v-for="item in reporterList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </div>
        </div>
        <!-- 事发时间选择 -->
        <div class="event_file_param_item">
          <div class="event_file_param_item_title">事发时间</div>
          <div class="event_file_param_item_content">
            <el-date-picker
              style="margin-left: 0;"
              v-model="happenTimeArr"
              type="daterange"
              range-separator="至"
              :popper-class="'screen'"
              :picker-options="pickerOptions"
              start-placeholder="开始日期时间"
              end-placeholder="结束日期时间"
              align="right"
              :clearable="false"
            ></el-date-picker>
          </div>
        </div>
      </div>
      <!-- 查询和重置按钮 -->
      <el-row type="flex" justify="flex" class="search-wrap">
        <el-col :span="18"></el-col>
        <el-col
          :span="6"
          :style="{
            display: 'flex',
            justifyContent: 'end',
            paddingRight: pxToRem(10),
          }"
        >
          <el-button
            icon="el-icon-search"
            class="search-btn-hover"
            :style="{
              backgroundImage:
                'linear-gradient(90deg, #2FB1EC 8%, #155BD4 100%)',
              border: 'none',
              width: pxToRem(100),
              color: '#ffffff',
            }"
            @click="queryBtnHandle"
          >查询</el-button>
          <el-button icon="el-icon-refresh-right" class="reset-btn-hover" @click="resetParams">重置</el-button>
          <el-button
            icon="el-icon-arrow-down"
            v-if="showOpenParam"
            class="reset-btn-hover"
            @click="onClickOpenParam"
          >展开</el-button>
          <el-button
            icon="el-icon-arrow-up"
            v-if="showParkUpParam"
            class="reset-btn-hover"
            @click="onClickOpenParam"
          >收起</el-button>
        </el-col>
      </el-row>
      <!-- 表格显示事件数据 -->
      <el-table
        class="search_table"
        v-loading="loading"
        :data="tableData"
        :header-cell-class-name="handleHeaderClass"
        @filter-change="filterHandler"
      >
        <el-table-column
          align="left"
          prop="title"
          label="事件名称"
          :show-overflow-tooltip="true"
          width="250"
        >
          <template slot-scope="scope">
            <div :style="{ width: '240px', display: 'flex' }">
              {{ scope.row.title }}&nbsp;&nbsp;
              <div v-if="scope.row.isTimeout === '1' && isCheckMode" class="event_overTime">
                <i class="el-icon-time"></i>&nbsp;&nbsp;超时
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          align="left"
          prop="eventTypeName"
          label="事件类型"
          :show-overflow-tooltip="true"
          :filter-multiple="false"
          columnKey="eventTypes"
          :filters="eventTypesQuickFilterOptions"
          :filtered-value="filterList.eventTypes"
        ></el-table-column>
        <el-table-column align="left" prop="occurTime" label="事发时间"></el-table-column>
        <el-table-column
          align="left"
          prop="eventAddress"
          label="事发地点"
          :show-overflow-tooltip="true"
          width="250"
        ></el-table-column>
        <el-table-column
          align="left"
          prop="emergencyLevelName"
          label="事件等级"
          :filter-multiple="false"
          columnKey="emergencyLevel"
          :filters="emergencyLevelQuickFilterOptions"
          :filtered-value="filterList.emergencyLevel"
        ></el-table-column>
        <!-- <el-table-column
          align="left"
          prop="warningTypeName"
          label="事件类型"
          :show-overflow-tooltip="true"
          width="200"
          :filter-multiple="false"
          columnKey="warningType"
          :filters="eventTypeQuickFilterOptions"
          :filtered-value="filterList.warningType"
        >-->
        <el-table-column
          align="left"
          prop="fusionStatus"
          label="事件状态"
          :filter-multiple="false"
          columnKey="eventState"
          :filters="fusionStatusQuickFilterOptions"
          :filtered-value="filterList.eventState"
        >
          <template slot-scope="scope">
            <span>{{ getFusionStatus(scope.row.fusionStatus).dictLabel }}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column
          align="left"
          prop="orderStatus"
          label="办理状态"
          :filter-multiple="false"
          columnKey="orderStatus"
          :filters="orderStatusQuickFilterOptions"
          :filtered-value="filterList.orderStatus"
        >
          <template slot-scope="scope">
            <div
              :style="{
                color:
                  scope.row.orderStatusName == '已完结' ? '#ffffff' : '#4F9FFF',
              }"
            >{{ scope.row.orderStatusName == '已完结' ? '已完结' : '处理中' }}</div>
          </template>
        </el-table-column>-->
        <!-- <el-table-column
          align="left"
          prop="linkName"
          label="处置环节"
          :filter-multiple="false"
          columnKey="linkId"
          :filters="linkIdQuickFilterOptions"
          :filtered-value="filterList.linkId"
        ></el-table-column>-->
        <el-table-column
          align="left"
          prop="alarmStatus"
          label="处置状态"
          :filter-multiple="false"
          columnKey="eventOrderStatus"
          :filters="orderStatusQuickFilterOptions"
          :filtered-value="filterList.eventOrderStatus"
        >
          <template slot-scope="scope">
            <span>{{ getOrderStatusName(scope.row.alarmStatus) }}</span>
          </template>
        </el-table-column>
        <el-table-column align="left" prop="order" label="操作" width="150">
          <template slot-scope="scope">
            <div class="deal-wrap">
              <el-button type="text" @click="onClickDetail(scope.row)" icon="el-icon-tickets">
                {{
                scope.row.alarmStatus == '6' ||
                scope.row.alarmStatus == '0' ||
                scope.row.alarmStatus == '7'
                ? '归档详情'
                : '详情'
                }}
              </el-button>
              <el-button
                type="text"
                @click="onClickEdit(scope.row)"
                icon="el-icon-edit"
                v-if="
                scope.row.alarmStatus != '6' &&
                scope.row.alarmStatus != '0' &&
                scope.row.alarmStatus != '7'
              "
              >处置</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="event_file_bottom_pagination">
        <el-divider :style="{ background: 'red' }"></el-divider>
        <el-pagination
          class="config-table-pagination"
          @size-change="changeSize"
          @current-change="changePage"
          :current-page="pageInfo.pageNum"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="pageInfo.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pageInfo.total"
        ></el-pagination>
      </div>
    </div>
    <!-- 显示详情内容 -->
    <div class="event_file_content" v-if="showDetail">
      <div class="event_file_tabs">
        <div class="event_file_tab_item_selected">
          {{
          currentRecord.alarmStatus == '6' ||
          currentRecord.alarmStatus == '0' ||
          currentRecord.alarmStatus == '7'
          ? '归档详情'
          : '详情'
          }}
        </div>
        <div class="event_file_tab_back" @click="onClickBack">
          <img alt="返回" :src="require('@/assets/images/alarmEvent/eventFile/icon_back.svg')" />&nbsp;返回
        </div>
      </div>
      <div class="event_file_line"></div>
      <div class="event_file_detail">
        <Detail :tileModes="tileModes" :recordInfo="currentRecord" :isCheckMode="isCheckMode" />
      </div>
    </div>
    <!-- 显示事件处置内容 -->
    <div class="event_file_content" v-if="showDeal">
      <div class="event_file_tabs">
        <div class="event_file_tab_item_selected">事件处置</div>
        <div class="event_file_tab_back" @click="onClickBack">
          <img alt="返回" :src="require('@/assets/images/alarmEvent/eventFile/icon_back.svg')" />&nbsp;返回
        </div>
      </div>
      <div class="event_file_line"></div>
      <div class="event_file_detail">
        <EvtHandle :tileModes="tileModes" :evt-item="currentRecord" />
      </div>
    </div>
  </div>
</template>
<script>
import api from '@/api'
import commonService, {
  getUserInfo,
  supplyRegionTree,
} from '@/api/service/common'
import ConstEnum from '@/components/common/ConstEnum'
import EvtHandle from '@/components/page/ScreenPages/eventFile/EvtHandle'
import dayjs from 'dayjs'
import Detail from './detail.vue'
// , mapGetters
import { mapMutations, mapActions } from 'vuex'
import ClearVideo from '@/utils/mixins/clearVideo'
import { alarmStatusListDataDefault } from '@/components/page/ScreenPages/IntegratedMonitor/EventFilter/typeDatas.js'
// import { fusionListData } from '@/components/page/ScreenPages/eventFile/mockData'
import { getInfo } from '@/utils/index.js' // 获取url参数
import { getMapTiles } from '@/api/service/common' // 导入获取地图瓦片的服务
import ss from '@/components/page/ScreenPages/IntegratedMonitor/Map/MapTool/img/2D-dark-map.png'
import cg from '@/components/page/ScreenPages/IntegratedMonitor/Map/MapTool/img/2D-map.jpeg'
import wx from '@/components/page/ScreenPages/IntegratedMonitor/Map/MapTool/img/satellite-map.jpeg'
import { setMapService } from '@/utils/common'

export default {
  mixins: [ClearVideo],
  // props: {},
  components: {
    Detail,
    EvtHandle,
  },
  // beforeCreate() {},
  created() {
    // 测试本地调用测试环境接口
    // api.get(
    //   `/rw-screen-demo/railway-dict-service/dictValue/qryByCatCode?catCode=MAP_ADDR`,
    //   {}
    // )
    // 初始化查询条件的下拉数据
    // this.initDictData('40', 'eventSourcesList')
    // 初始化事件等级下拉数据 eventLevelList
    this.initEventLevelList()

    // 根据tab初始化事件类型
    // this.initEventTypeList()
    // 初始化摄像机树下拉数据
    this.initCameraTreeData()
    // 初始化上报人下拉数据
    this.initReportPersonList()
    // 初始化处置环节
    // this.initDealProcessList()
    // 事件类型
    this.fetchEventData()
    // 事件来源
    // this.fetchEventSourceData()
    // 查询处置状态
    this.fetchOrderStatusData()
  },
  async mounted() {
    // 本机连测试环境需要生成token，否则调用接口会报401
    if (this.$env.VUE_APP_REQ_SDK === 'false') {
      await getInfo()
    }

    // 初始化
    this.initTimeOut()
    // 查询列表数据
    this.getTableData(0)
    // 处置详情处理回调
    this.$EventBus.$on('refreshAlarmList', this.refreshList)
    try {
      // 导入获取地图瓦片的服务
      await getMapTiles()
      // 设置地图瓦片图层
      this.tileModes = setMapService(this.tileModes)
    } catch {
      console.log('导入获取地图瓦片的服务失败')
    }
    // 获取requestSDK('getInfo')需要延迟
    setTimeout(() => {
      // 初始化区域树下拉数据
      this.initUserAreaTreeData()
    }, 500)
  },
  beforeDestroy() {
    this.$EventBus.$off('refreshAlarmList', this.refreshList)
  },
  computed: {
    // 是否显示展开按钮，当处置状态/事件来源两个条件，需要展示附加的子条件时，则需要展示
    showOpenParam() {
      return this.showOpenBtn // (this.eventStatus.length == 1) && this.showOpenBtn
    },
    showParkUpParam() {
      return !this.showOpenBtn
    },
    evtTypeList() {
      return this.$store.state.event.evtTypeList.map(v => ({
        label: v.dictLabel,
        value: v.dictValue,
        text: v.dictLabel,
      }))
    },
    evtOrderStatusList() {
      return this.$store.state.event.evtOrderStatusList.map(v => ({
        label: v.dictLabel,
        value: v.dictValue,
        text: v.dictLabel,
      }))
    },
    getFusionStatus() {
      return key => {
        const obj = alarmStatusListDataDefault.find(v =>
          v.dictValue.includes(key)
        )
        return obj || {}
      }
    },
    getOrderStatusName() {
      return key => {
        const _evtOrderStatusList =
          this.$store.state.event.evtOrderStatusList || []
        const obj = _evtOrderStatusList.find(v => v.dictValue === key)
        return obj?.dictLabel || ''
      }
    },
  },
  data() {
    return {
      // 地图服务
      tileModes: [
        {
          modeId: 5,
          name: '深色地图',
          mapType: '2D',
          imgUrl: ss,
          tileType: 'vector',
          layerUrls: [
            {
              url: '',
              // url: 'http://10.43.82.110:9239/geoserver/railway_space/gwc/service/wmts?layer=railway_space:railway-project-dark&style=&tilematrixset=WebMercatorQuad&Service=WMTS&Request=GetTile&Version=1.0.0&Format=image/png&TileMatrix={z}&TileCol={x}&TileRow={y}',
              // url: 'http://webst01.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}',
            },
          ],
        },
        {
          modeId: 1,
          name: '卫星地图',
          imgUrl: wx,
          tileType: 'satellite',
          mapType: '2D',
        },
        {
          modeId: 6,
          name: '常规地图',
          imgUrl: cg,
          tileType: 'vector',
          mapType: '2D',
          layerUrls: [
            {
              url: '',
              // url: 'http://10.43.82.110:9239/geoserver/railway_space/gwc/service/wmts?layer=railway_space:railway-project&style=&tilematrixset=WebMercatorQuad&Service=WMTS&Request=GetTile&Version=1.0.0&Format=image/png&TileMatrix={z}&TileCol={x}&TileRow={y}',
            },
          ],
        },
      ],
      loading: false,
      showDetail: false, // true:展示详情；false:展示列表
      showDeal: false, // true:展示处置；false:展示列表
      activeTab: '1', // alarm:告警事件；danger:隐患
      isFocus: true, // 重点关注
      isCollect: false, // 是否收藏
      isCheckMode: false, // 是否检查模式
      keyWord: '', // 搜索关键字
      areaIds: [], // 区域，支持多选
      eventLevels: [], // 事件等级，支持多选
      eventStatus: ['0'], // 处置状态,支持多选，选一个才能展示条件参数
      dealProcess: [], // 处置环节，支持多选
      finishTypes: [], // 办结方式，支持多选
      eventTypes: [], // 事件类型，支持多选
      eventTypesQuickFilterOptions: [], // 事件类型快捷筛选下拉选项
      eventSources: [], // 事件来源，支持多选
      cameraId: '', // AI识别来源，摄像机
      reporter: [], // 人工上报来源，上报人，
      pickerOptions: {
        // 禁用未来日期选择
        disabledDate(time) {
          return time.getTime() > Date.now()
        },
      },
      happenTimeArr: [dayjs().subtract(1, 'months'), dayjs()], // 事发时间 [开始时间，结束时间]
      // 处置状态
      eventOrderStatus: [],
      // 事件状态
      eventState: [],
      eventStateList: alarmStatusListDataDefault.map(v => ({
        label: v.dictLabel,
        value: v.dictValue,
        text: v.dictLabel,
      })),
      fusionStatusQuickFilterOptions: [], // 事件状态筛选
      areaList: [
        { label: '合肥', value: '1' },
        { label: '南京', value: '2' },
      ], // 区域下拉数据window.userInfo.roleAreaTree
      eventLevelList: [], // 事件等级下拉数据
      eventStatusList: [
        { label: '处理中', value: '0' },
        { label: '已完结', value: '1' },
      ], // 处置状态下拉数据
      dealProcessList: [
        { label: '待研判', value: '1' },
        { label: '待调度', value: '2' },
        { label: '待处置', value: '4' },
        { label: '待核实', value: '5' },
      ], // 处置环节下拉数据
      finishTypeList: [
        { label: '重复报警', value: '7' },
        { label: '误报', value: '0' },
        { label: '已办结/验收销号', value: '6' },
      ], // 办结方式下拉数据
      eventTypeList: [], // 事件类型下拉数据 { label: '告警', value: 'alarm' }, { label: '隐患', value: 'danger' }
      cameraList: [], // 摄像机下拉数据
      reporterList: [], // 上报人下拉数据
      eventSourcesList: [],
      tableData: [], // 表格数据
      showOpenBtn: false, // 展示展开按钮，需要
      pageInfo: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      }, // 分页信息
      currentRecord: {}, // 当前详情/编辑的记录
      emergencyLevelQuickFilterOptions: [], // 事件等级快捷筛选下拉选项
      orderStatusQuickFilterOptions: [], // 办理状态快捷筛选下拉选项
      // linkIdQuickFilterOptions: [], // 处置环节快捷筛选下拉选项
      filterList: {},
      activeFilterColumn: '', // 当前过滤选择的列 key
    }
  },
  methods: {
    ...mapMutations('map', [
      'setMapType', // 设置地图类型
    ]),
    ...mapActions('event', [
      'fetchEventSourceData',
      'fetchEventData',
      'fetchOrderStatusData',
    ]),
    /**
     * 查询列表数据
     */
    queryBtnHandle() {
      this.activeFilterColumn = ''
      this.filterList = {}
      this.changePage(1)
    },
    /**
     * 初始化接口超时时间
     */
    async initTimeOut() {
      const res = await api.post(
        `${this.$env.VUE_APP_REQ_PREFIX_BIZ}/fusionEvent/updateEventTime`,
        {}
      )
      if (!res?.data)
        this.$message({
          type: 'error',
          message: '初始化超时接口失败：' + res.msg,
        })
    },
    getFocusRemark() {
      if (this.isFocus === true) {
        return 'Y'
      } else if (this.isFocus === false) {
        return 'N'
      } else {
        return ''
      }
    },
    /**
     * 刷新列表
     */
    refreshList() {
      // 查询列表数据
      this.getTableData(Object.keys(this.filterList).length === 0 ? 0 : 1)
    },
    /**
     * 初始化快捷筛选数据
     */
    getParams() {
      // “人工上报”事件为事件来源「第三方告警」 +「人工上报」 + 「一键告警」
      const eventSourceList = ['2', '3', '11']
      return {
        eventSourceList: this.activeTab === '1' ? undefined : eventSourceList,
        notEventSourceList:
          this.activeTab === '2' ? undefined : eventSourceList,
        isOpen: this.isCheckMode ? 'Y' : undefined,
        isCollection: this.isCollect ? '1' : undefined, // 1:收藏；0：未收藏
        isCheckMode: this.isCheckMode ? '1' : undefined, // 检查模式
        keyword: this.keyWord,
        isFocus: this.getFocusRemark(), // 重点关注
        emergencyLevelList:
          this.eventLevels.length === 0 ? null : this.eventLevels, // 事件等级
        // 事件状态 全选传 null
        fusionStatusList:
          this.eventState.length === 0 ||
          this.eventState.length === this.eventStateList.length
            ? null
            : this.eventState.join().split(','),
        // 处置状态 全选传 null
        alarmStatusList:
          this.eventOrderStatus.length === 0 ||
          this.eventOrderStatus.length === this.evtOrderStatusList.length
            ? null
            : this.eventOrderStatus,
        // 事件类型 全选传 null
        eventTypeList:
          this.eventTypes.length === 0 ||
          this.eventTypes.length === this.evtTypeList.length
            ? null
            : this.eventTypes,
        // warningSourceList: this.getTableParamSource(), //[this.activeTab],// 事件来源
        // orderStatusList: this.getStatusArr(), // 事件状态
        // linkIdList:
        //   this.eventStatus.length == 1 && this.eventStatus[0] == '0'
        //     ? this.dealProcess
        //     : [],
        // warningTypeIdList: this.eventTypes, // 事件类型
        startDate: dayjs(this.happenTimeArr[0]).format('YYYY-MM-DD 00:00:00'),
        endDate: dayjs(this.happenTimeArr[1]).format('YYYY-MM-DD 23:59:59'),
        // deviceCode: this.cameraId,
        pageNum: this.pageInfo.pageNum,
        pageSize: this.pageInfo.pageSize,
      }
    },
    // 查询列表数据 入参type=0表示正常查询，type=1表示快捷筛选
    async getTableData(type) {
      this.loading = true
      let params = this.getParams()
      // 区域入参
      this.handleArea(params)
      // 如果来源是AI，需要上传摄像机
      if (this.activeTab === '1' && this.cameraId) {
        params.deviceCode = this.cameraId
      }
      // 如果来源是人工上报需要提供上报人入参filterable
      if (this.activeTab === '2' && this.reporter) {
        // params.userIds = this.reporter
        params.userNames = this.reporter
      }
      // 查询快捷筛选下拉信息
      if (type === 0) {
        this.filterList = {}
        this.initQuickFilterData(params)
      }
      if (type === 1) {
        params = this.quickFilterHandleParam(params)
      }
      // 查询列表数据
      const resData = await api.post(
        `${this.$env.VUE_APP_REQ_PREFIX_BIZ}/fusionEvent/fusionList`,
        params
      )
      this.loading = false
      // 测试数据
      const res = resData // resData?.data ? resData : fusionListData
      if (res?.data) {
        this.pageInfo.total = res.data.total
        this.tableData = res.data.list
      } else {
        this.$message({
          type: 'error',
          message: '列表加载失败' + res.msg,
        })
      }
    },
    /**
     * 区域入参
     * @param {*} params
     */
    handleArea(params) {
      if (!this.areaIds || this.areaIds.length < 1) {
        return
      }
      let provinceIds = []
      let cityIds = []
      let countyIds = []
      this.areaIds.forEach(item => {
        if (item.length === 1) {
          provinceIds.push(item[0])
        }
        if (item.length === 2) {
          cityIds.push(item[1])
        }
        if (item.length === 3) {
          countyIds.push(item[2])
        }
      })
      if (provinceIds.length > 0) {
        params.provinceIds = provinceIds
      }
      if (cityIds.length > 0) {
        params.cityIds = cityIds
      }
      if (countyIds.length > 0) {
        params.countyIds = countyIds
      }
    },
    /**
     * 组件数据
     * @param {*} handleStat
     * @param {*} endStat
     */
    getOrderStatusQuickFilterOptions(handleStat, endStat) {
      if (handleStat) {
        return [
          {
            value: '0',
            text: '处理中',
          },
        ]
      } else if (endStat) {
        return [
          {
            value: '1',
            text: '已完结',
          },
        ]
      } else {
        return [
          {
            value: '0',
            text: '处理中',
          },
          {
            value: '1',
            text: '已完结',
          },
        ]
      }
    },
    // 初始化快捷筛选下拉数数据
    initQuickFilterData(params) {
      api
        .post(
          `${this.$env.VUE_APP_REQ_PREFIX_BIZ}/fusionEvent/fusionListGroup`,
          params
        )
        .then(res => {
          if (res.code == 200) {
            // 事件类型
            this.eventTypesQuickFilterOptions = res.data.event_type?.map(
              val => ({
                value: val.dicValue,
                text: val.dicName,
              })
            )
            // 事件等级
            this.emergencyLevelQuickFilterOptions =
              res.data.emergency_level?.map(val => ({
                value: val.dicValue,
                text: val.dicName,
              }))
            // 处置状态
            this.orderStatusQuickFilterOptions = res.data.orderStatus?.map(
              val => ({
                value: val.dicValue,
                text: val.dicName,
              })
            )
            const fusionStatusObj = {
              未消散: '',
              已消散: '',
            }
            // 组织数据格式- 未消散：'1,2'
            res.data.fusionStatus.length &&
              res.data.fusionStatus.forEach(val => {
                if ('1,2'.includes(val.dicValue)) {
                  fusionStatusObj['未消散'] += `${val.dicValue},`
                } else {
                  fusionStatusObj['已消散'] = val.dicValue
                }
              })
            // 有数据加入到数组中
            const _fusionStatus = []
            Object.keys(fusionStatusObj).forEach(
              v =>
                fusionStatusObj[v] &&
                _fusionStatus.push({
                  value: fusionStatusObj[v],
                  text: v,
                })
            )
            this.fusionStatusQuickFilterOptions = _fusionStatus
            // const handleStat =
            //   this.eventStatus &&
            //   this.eventStatus.length === 1 &&
            //   this.eventStatus[0] === '0'
            // const endStat =
            //   this.eventStatus &&
            //   this.eventStatus.length === 1 &&
            //   this.eventStatus[0] === '1'
            // this.orderStatusQuickFilterOptions =
            //   this.getOrderStatusQuickFilterOptions(handleStat, endStat)
            // this.linkIdQuickFilterOptions = res.data.linkIdList?.map(val => ({
            //   value: val.dicValue,
            //   text: val.dicName,
            // }))
          } else {
            this.$message({
              type: 'error',
              message: '快捷筛选接口加载失败' + res.msg,
            })
          }
        })
    },
    // 动态设置表头类名
    handleHeaderClass(e) {
      // 动态设置表头类名
      if (this.activeFilterColumn === e.column?.columnKey) {
        return 'active-filter-column'
      } else {
        return ''
      }
    },
    // 快捷筛选，入参拼装
    quickFilterHandleParam(params) {
      const filterObj = this.filterList
      if (filterObj.eventTypes) {
        // 事件类型 length === 0 表示全选传 null
        params.eventTypeList =
          filterObj.eventTypes.length === 0 ? null : filterObj.eventTypes
      } else if (filterObj.eventState) {
        // 事件状态 length === 0 表示全选传 null
        params.fusionStatusList =
          filterObj.eventState.length === 0
            ? null
            : filterObj.eventState.join().split(',')
      } else if (filterObj.eventOrderStatus) {
        // 处置状态 length === 0 表示全选传 null
        params.alarmStatusList =
          filterObj.eventOrderStatus.length === 0
            ? null
            : filterObj.eventOrderStatus
      } else if (filterObj.emergencyLevel) {
        // 事件等级
        params.emergencyLevelList =
          filterObj.emergencyLevel.length === 0
            ? null
            : filterObj.emergencyLevel
      } else {
        // to do
      }
      return params

      // if (
      //   keyArr?.[0] === 'warningType' &&
      //   this.filterList.warningType.length > 0
      // ) {
      //   params.warningTypeIdList = this.filterList.warningType
      // }

      // if (
      //   keyArr &&
      //   keyArr[0] == 'orderStatus' &&
      //   this.filterList.orderStatus.length > 0
      // ) {
      //   if (this.filterList.orderStatus[0] == '0') {
      //     params.orderStatusList = ['1', '2', '3', '4', '5']
      //   } else if (this.filterList.orderStatus[0] == '1') {
      //     params.orderStatusList = ['0', '6', '7']
      //   } else {
      //     params.orderStatusList = ['1', '2', '3', '4', '5', '0', '6', '7']
      //   }
      // }
      // if (
      //   keyArr &&
      //   keyArr[0] == 'linkId' &&
      //   this.filterList.linkId.length > 0
      // ) {
      //   params.linkIdList = this.filterList.linkId
      // }
    },
    // 获取查询条件-来源
    getTableParamSource() {
      if (this.activeTab == '2') {
        return [this.activeTab]
      } else {
        let sourceArr = []
        if (this.eventSourcesList && this.eventSourcesList.length > 0) {
          this.eventSourcesList.forEach(item => {
            if (item.value != '2') {
              sourceArr.push(item.value)
            }
          })
          return sourceArr
        } else {
          return ['1', '3', '4', '5', '6', '7', '8', '9']
        }
      }
    },
    // 是否重点关注下拉值变化，需要重新查询事件类型下拉值
    onIsFocusChange() {
      this.initEventTypeList()
    },
    // 初始化查询条件下拉数据
    initDictData(dictType, tagId) {
      commonService
        .getCommonPlatDictByDictType({ dictType })
        .then(res => {
          const { code, data } = res
          if (code === 200) {
            this[tagId] = data?.map(item => {
              return { label: item.dictLabel, value: item.dictValue }
            })
          } else {
            this.$message({
              type: 'error',
              message: '通用平台字典加载失败' + res.msg,
            })
          }
        })
        .catch(e => {
          this.$message({
            type: 'error',
            message: '通用平台字典加载失败' + e.message,
          })
        })
    },
    // 初始化事件类型下拉数据
    initEventTypeList() {
      this.eventTypes = []
      api
        .post(
          `${this.$env.VUE_APP_REQ_PREFIX_BIZ}/adsEvtTypeConfig/findOrderAlarm`,
          { isFocus: this.getFocusRemark(), isOpen: 'Y' }
        )
        .then(res => {
          if (res.code == 200) {
            const typeList = []
            res.data?.forEach(item => {
              typeList.push({ label: item.typeName, value: item.typeValue })
            })
            this.eventTypeList = typeList
          } else {
            this.eventTypeList = []
            this.$message({
              type: 'error',
              message: '事件类型加载失败' + res.msg,
            })
          }
        })
    },
    // 初始化事件等级下拉数据 eventLevelList
    async initEventLevelList() {
      api
        .get(
          `${this.$env.VUE_APP_REQ_PREFIX_BIZ}/adsEvtTypeConfig/getAllEmergencyLevel`,
          {}
        )
        .then(res => {
          if (res.code == 200) {
            const levelList = []
            res.data?.forEach(item => {
              levelList.push({ label: item.dicName, value: item.dicValue })
            })
            this.eventLevelList = levelList
          } else {
            this.eventLevelList = []
            this.$message({
              type: 'error',
              message: '事件等级加载失败' + res.msg,
            })
          }
        })
    },
    // 初始化用户区域树数据
    async initUserAreaTreeData() {
      const { user } = await getUserInfo()
      console.log('初始化用户区域树数据--', user)
      await supplyRegionTree(user)
      window.userInfo = user
      // 调用接口查询省市数据
      if (!user.areaTree || user.areaTree.length < 1) {
        this.$message.error({
          type: 'error',
          message: '查询用户权限城市失败！',
        })
      }
      if (user.areaTree[0].id === '100000') {
        user.areaTree = user.areaTree[0].children
      }
      if (user.areaTree[0].children[0].children.length < 2) {
        window.userInfo.defArea = [
          user.areaTree[0].id,
          user.areaTree[0].children[0].id,
          user.areaTree[0].children[0].children[0].id,
        ]
        window.userInfo.defAreaName =
          user.areaTree[0].children[0].children[0].label
      } else if (user.areaTree[0].children.length < 2) {
        window.userInfo.defArea = [
          user.areaTree[0].id,
          user.areaTree[0].children[0].id,
        ]
        window.userInfo.defAreaName = user.areaTree[0].children[0].label
      } else {
        window.userInfo.defArea = [user.areaTree[0].id]
        window.userInfo.defAreaName = user.areaTree[0].label
      }
      let addressTemp = []
      user.areaTree.forEach(item => {
        item.value = item.id
        item.regionLevel = ConstEnum.REGION_LEVEL.province
        if (item.children && item.children.length > 0) {
          let sonLevelList = []
          item.children.forEach(secondChild => {
            secondChild.value = secondChild.id
            secondChild.regionLevel = ConstEnum.REGION_LEVEL.city
            sonLevelList.push(secondChild)
            if (secondChild.children && secondChild.children.length > 0) {
              let thirdLevelList = []
              secondChild.children.forEach(thirdChild => {
                thirdChild.value = thirdChild.id
                delete thirdChild.children
                thirdChild.regionLevel = ConstEnum.REGION_LEVEL.district
                thirdLevelList.push(thirdChild)
              })
              secondChild.children = thirdLevelList
              secondChild.disabled = thirdLevelList.length < 2
            }
          })
          item.children = sonLevelList
          item.disabled = sonLevelList.length < 2
        }
        addressTemp.push(item)
      })
      this.areaList = addressTemp
    },
    // 获取摄像机树数据
    initCameraTreeData() {
      api
        .get(`${this.$env.VUE_APP_REQ_PREFIX_PLAT}/device/getAllDevice`, {
          displayMode: 1,
        })
        .then(res => {
          if (res.code == 200) {
            this.cameraList = res.data?.map(item => {
              return {
                value: item.deviceCode,
                label: item.deviceName,
              }
            })
          } else {
            this.$message({
              type: 'error',
              message: '区域树数据加载失败' + res.msg,
            })
          }
        })
    },
    // 初始化上报人数据
    initReportPersonList() {
      api
        .get(
          `${this.$env.VUE_APP_REQ_PREFIX_PLAT}/system/selectUserListByTenantId`,
          { pageSize: 100, pageNum: 1 }
        )
        .then(res => {
          if (res.code == 200) {
            this.reporterList = res.data?.map(item => {
              return {
                ...item,
                value: item.userName, // userId,
                label: item.nickName,
              }
            })
          } else {
            this.$message({
              type: 'error',
              message: '上报人数据加载失败' + res.msg,
            })
          }
        })
    },
    // 初始化处置环节下拉数据
    initDealProcessList() {
      api
        .post(
          `${this.$env.VUE_APP_REQ_PREFIX_BIZ}/adsEvtTypeConfig/getLinkInfoList`,
          {}
        )
        .then(res => {
          if (res.code == 200) {
            let proceseListTep = []
            res.data?.forEach(item => {
              if (item.dicName != '开始' && item.dicName != '办结') {
                proceseListTep.push({
                  value: item.dicValue,
                  label: item.dicName,
                })
              }
            })
            this.dealProcessList = proceseListTep
          } else {
            this.$message({
              type: 'error',
              message: '处置环节数据加载失败' + res.msg,
            })
          }
        })
    },
    // 计算接口需要的处置状态数组
    getStatusArr() {
      if (this.eventStatus && this.eventStatus.length == 1) {
        // 处置中
        if (this.eventStatus[0] == '0') {
          return ['1', '2', '3', '4', '5']
        }
        // 已结束
        if (this.eventStatus[0] == '1') {
          if (this.finishTypes && this.finishTypes.length > 0) {
            return this.finishTypes
          }
          return ['0', '6', '7']
        }
      } else if (this.eventStatus && this.eventStatus.length == 2) {
        return ['0', '1', '2', '3', '4', '5', '6', '7']
      } else {
        return ['0', '1', '2', '3', '4', '5', '6', '7']
      }
    },
    // 点击tab,重新初始化事件类型下拉，查询默认数据
    onClickTab(tabName) {
      if (this.activeTab != tabName) {
        this.activeTab = tabName
      }
      this.resetParams()
      this.initEventTypeList()
    },
    // 重点关注修改后，需要重新初始化事件类型下拉
    onFocusChange(checked) {
      console.info('---------onFocusChange--------', checked)
      this.initEventTypeList()
    },
    // 点击展开/收起按钮
    onClickOpenParam() {
      this.showOpenBtn = !this.showOpenBtn
      // 需要隐藏掉多余条件
      if (this.showOpenBtn) {
        let element = document.getElementById('event_file_params')
        element.style.height = this.pxToRem(100)
      } else {
        let element = document.getElementById('event_file_params')
        element.style.height = 'auto'
      }
    },
    // 重置
    resetParams() {
      this.activeFilterColumn = ''
      this.eventState = []
      this.eventOrderStatus = []
      this.isFocus = true // 重点关注
      this.isCollect = false // 是否收藏
      this.isCheckMode = false // 是否检查模式
      this.keyWord = '' // 搜索关键字
      this.areaIds = [] // 区域，支持多选
      this.eventLevels = [] // 事件等级，支持多选, 通用平台只支持单选
      this.eventTypes = []
      this.eventStatus = ['0'] // 处置状态,支持多选，选一个才能展示条件参数
      this.dealProcess = [] // 处置环节，支持多选
      this.finishTypes = [] // 办结方式，支持多选
      this.eventTypes = [] // 事件类型，支持多选
      this.eventSources = [] // 事件来源，支持多选
      this.cameraId = undefined // AI识别来源，摄像机
      this.reporter = undefined // 人工上报来源，上报人，
      this.happenTimeArr = [dayjs().subtract(1, 'months'), dayjs()] // 事发时间 [开始时间，结束时间]
      this.pageInfo.pageNum = 1
      this.showOpenBtn = false
      this.getTableData(0)
    },
    // 修改页容量
    changeSize(val) {
      this.pageInfo.pageSize = val
      this.pageInfo.pageNum = 1
      this.getTableData(Object.keys(this.filterList).length === 0 ? 0 : 1)
    },
    // 切换页
    changePage(val) {
      this.pageInfo.pageNum = val
      this.getTableData(Object.keys(this.filterList).length === 0 ? 0 : 1)
    },
    // 点击详情
    onClickDetail(record) {
      this.currentRecord = {
        ...record,
        latitude: record.startLat,
        longitude: record.startLng,
      }
      this.showDetail = true
    },
    // 点击处置
    onClickEdit(record) {
      this.currentRecord = {
        ...record,
        latitude: record.startLat,
        longitude: record.startLng,
      }
      this.showDeal = true
    },
    // 点击返回
    onClickBack() {
      window.leftWrap && window.leftWrap.closeAllContent()
      window.bottomWrap && window.bottomWrap.closeAllContent()
      this.showDetail = false
      this.showDeal = false
      // 把地图设置为黑色底色
      // this.setMapType(2)
    },
    // 表头过滤
    filterHandler(value, row, column) {
      // 获取动态表头key
      try {
        const activeColumn = Object.keys(value).find(
          key => value[key].length > 0
        )
        this.activeFilterColumn = activeColumn || null
      } catch {
        console.log('过滤参数异常')
      }

      this.filterList = value
      this.pageInfo.pageNum = 1
      // console.info('fileter parem--------------', value, row, column)
      this.getTableData(1)
    },
  },
}
</script>
<style lang="less" scoped>
@import './index.less';
</style> 