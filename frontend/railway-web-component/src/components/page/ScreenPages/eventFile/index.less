.el-table-filter {
  background-color: #172537;
  color: #d2dfee;
  border: none;

  .el-table-filter__list-item:hover {
    color: #fefefe !important;
    background: rgba(79, 159, 255, 0.4) !important;
  }
  .el-table-filter__list-item.is-active {
    color: #4f9fff !important;
    background: rgba(79, 159, 255, 0.4) !important;
  }
}

.el-table__column-filter-trigger i {
  font-size: 16px !important;
  color: #c0c4cc !important;
  transform: rotateZ(180deg);
}

.el-icon-arrow-down:before {
  content: "\E6E1" !important;
}

.event_file_main {
  width: 100%;
  height: 100%;
  background: #000527;
  .event_file_content {
    display: flex;
    flex-direction: column;
    height: calc(100% - 48px);
    width: calc(100% - 48px);
    background: url("~@/assets/images/alarmEvent/eventFile/bg.png") no-repeat;
    margin: 24px 24px;
    background-size: 100% 100%;
    position: relative;
    // tab行
    .event_file_tabs {
      width: 100%;
      height: 50px;
      .event_file_tab_item {
        height: 50px;
        width: 144px;
        color: #4f9fff;
        font-size: 16px;
        text-align: center;
        float: left;
        line-height: 60px;
        cursor: pointer;
      }
      .event_file_tab_item_selected {
        height: 50px;
        width: 144px;
        color: #e8f3ff;
        font-size: 16px;
        text-align: center;
        text-shadow: 0 0 4px #4f9fff;
        background: url("~@/assets/images/alarmEvent/eventFile/tab_selected.png")
          no-repeat;
        background-size: cover;
        float: left;
        line-height: 60px;
        cursor: pointer;
      }
      .event_file_tab_back {
        width: 60px;
        height: 50px;
        float: right;
        font-size: 16px;
        line-height: 50px;
        color: #e8f3ff;
        cursor: pointer;
        img {
          height: 11px;
        }
      }
    }
    // tab下有一条线
    .event_file_line {
      width: 100%;
      height: 2px;
      background: url("~@/assets/images/alarmEvent/eventFile/long_line.png")
        no-repeat;
      background-size: 100%;
    }
    // 查询条件
    .event_file_params {
      width: 100%;
      min-height: 96px;
      height: auto;
      overflow: hidden;

      .event_file-model-wrap {
        display: flex;
        align-items: center;
        padding-left: 50px;
      }

      .event_file_param_item {
        width: 25%;
        height: 35px;
        margin-top: 10px;
        float: left;
        // line-height: 32px;
        margin-bottom: 1px;
        display: flex;
        .event_file_param_item_title {
          width: 100px;
          height: 35px;
          float: left;
          font-size: 16px;
          color: #ffffff;
          letter-spacing: 0;
          text-align: right;
          line-height: 35px;
          margin-right: 10px;
        }
        .event_file_param_item_content {
          width: calc(100% - 120px);
          height: 35px;
          float: left;
          margin-right: 10px;
          overflow-y: auto;
          display: flex;
          /deep/.el-input__inner {
            width: 100%;
            background: rgba(79, 159, 255, 0.2);
            border-radius: 4px;
            border: none;
            color: #e8f3ff;
            height: 35px !important;
            line-height: 35px;
          }
          /deep/.el-select {
            width: 100%;
            color: #e8f3ff;
            margin: 0;
          }
          /deep/.el-range-editor .el-range-input {
            background: #1a3159;
            color: #e8f3ff;
          }
          /deep/.el-date-editor .el-range-separator {
            color: #ffffff;
          }
          ::v-deep .el-cascader,
          ::v-deep .el-cascader--small {
            width: 100%;
            height: 100%;
          }
          ::v-deep .el-cascader__tags .el-tag:first-child {
            max-width: 80%;
          }
          ::v-deep .el-cascader__tags .el-tag .el-icon-close {
            background: none;
            color: #4f9fff;
          }
          ::v-deep .el-tag.el-tag--info {
            background: rgba(79, 159, 255, 0.2);
            border-radius: 4px;
            color: #4f9fff;
            border: none;
          }
          ::v-deep .el-select .el-tag__close.el-icon-close {
            background: none;
            color: #4f9fff;
          }
          ::v-deep .el-cascader__dropdown {
            border: none !important;
            background: #172537 !important;
          }
        }
        .el-checkbox__input.is-checked + .el-checkbox__label {
          color: #e8f3ff;
        }
        ::v-deep .el-checkbox__input.is-checked + .el-checkbox__label {
          color: #ffffff;
        }
        ::v-deep .el-checkbox {
          color: #aaaaaa;
          display: flex;
          align-items: center;

          .el-checkbox__label {
            display: flex;
            align-items: center;
            font-size: 16px;
          }
        }
        ::v-deep .el-checkbox__inner {
          border: 1.14px solid #5179a9;
          background-color: #0c152f;
        }
        ::v-deep .is-checked .el-checkbox__inner {
          background-color: #409eff;
          border-color: #409eff;
        }
      }
    }
    .deal-wrap,
    .search-wrap {
      ::v-deep .el-button {
        font-size: 16px;
      }
    }
    .event_file_bottom_pagination {
      width: 100%;
      // height: 50px;
      // position: absolute;
      // bottom: 20px;
      margin-bottom: 10px;
      ::v-deep .el-pagination {
        // text-align: end;
        padding-right: 23px;
        display: flex;
        align-items: center;
        justify-content: right;
      }
      ::v-deep .el-input__inner {
        background-color: #0d2753;
        border: none;
      }
      ::v-deep .el-pagination .btn-next,
      ::v-deep .el-pagination .btn-prev {
        background-color: #0d2753;
        color: #eeeeee;
        border-radius: 6px;
      }
      ::v-deep .el-pagination button:disabled {
        background-color: #0d2753;
      }
      ::v-deep .el-pagination .el-pager li.active {
        background: #4f9fff;
        border-radius: 6px;
        height: 28px;
        width: 28px;
        margin-left: 2px;
        margin-right: 2px;
      }
      ::v-deep .el-pagination .el-pager li {
        margin-left: 2px;
        margin-right: 2px;
        border-radius: 6px;
        background-color: #0d2753;
      }
    }
    .search_table {
      width: calc(100% - 20px);
      flex: 1;
      // height: calc(100% - 290px);
      // max-height: 550px;
      margin-left: 10px;
      margin-top: 10px;
      overflow-y: auto;
      border: none !important;
      ::v-deep .el-loading-mask {
        background: rgba(23, 37, 55, 0.85)
          url("~@/assets/images/comm/loading-dark.gif") 50% 50% no-repeat;
        background-size: 100px 80px;

        .el-loading-spinner {
          display: none;
        }
      }
      .event_overTime {
        width: 74px;
        height: 24px;
        padding-left: 5px;
        float: right;
        background: url("~@/assets/images/alarmEvent/eventFile/bg_overtime.png")
          no-repeat 100% 100%;
      }
      ::v-deep .el-table__body-wrapper {
        background: #0c152f;
      }
    }
    /deep/.el-table .el-table__header-wrapper {
      /* 高亮当前筛选列的文字 */
      .active-filter-column > .cell {
        color: #409eff !important; /* Element 主题色 */
      }
      .el-table__cell > .cell {
        color: unset;
      }
    }
    .event_file_detail {
      width: 100%;
      height: calc(100% - 72px) !important;
      margin-top: 10px;
    }
    /deep/.el-table th.el-table__cell {
      background: rgb(25 48 88) !important;
      color: #e8f3ff;
      border-right: 1px solid rgba(79, 159, 255, 0.2);
      border-bottom: none;
    }
    /deep/.el-table {
      font-size: 16px;
    }
    /deep/.el-table tr {
      background-color: #0c152f;
    }
    /deep/.el-table .el-table__cell,
    /deep/.el-table--small .el-table__cell {
      padding: 8px 0 !important;
    }
    /deep/.el-table {
      color: #e8f3ff;
      background: #0c152f;
    }
    /deep/.el-table--border .el-table__cell {
      border-right: none;
    }
    /deep/.el-table td.el-table__cell {
      border-bottom: 1px solid #131f41;
    }
    /deep/.el-table::before {
      background-color: #0c152f !important;
    }
    ::v-deep .el-table tr:hover td {
      background-color: rgb(32, 58, 103) !important;
    }
    ::v-deep .el-divider {
      background-color: #22315c;
      // width: calc(100% - 20px);
      margin: 12px 0;
    }
    .search-btn-hover:hover,
    .search-btn-hover:focus {
      background-image: linear-gradient(
        90deg,
        rgb(47, 177, 236) 18%,
        rgb(21, 91, 212) 90%
      ) !important;
      color: rgb(205, 205, 205) !important;
    }
    .reset-btn-hover {
      background: rgba(19, 115, 231, 0.2);
      border: none;
      width: 94px;
      color: rgb(238, 238, 238);
    }
    .reset-btn-hover:hover,
    .reset-btn-hover:focus {
      background: rgba(19, 115, 231, 0.2) !important;
      color: rgb(205, 205, 205) !important;
    }
  }
}
::v-deep .el-checkbox__input.is-disabled .el-checkbox__inner {
  background-color: #999999 !important;
}
