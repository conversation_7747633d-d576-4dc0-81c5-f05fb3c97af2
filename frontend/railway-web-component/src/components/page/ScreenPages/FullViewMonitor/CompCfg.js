/**
 * 导入组件处理类和常用工具。
 * @import ComponentHandle 组件处理类，用于创建和管理组件。
 * @import uuid 生成唯一标识的工具函数。
 * @import 各种组件 组件库中的具体组件，用于在界面上展示不同类型的统计数据。
 */
import ComponentHandle from '@/components/common/ComponentHandle';
import { uuid } from '@/components/common/utils';
import DeviceStat from '@/components/page/ScreenPages/FullViewMonitor/DeviceStat/index.vue';
import EvtAutoSummery from '@/components/page/ScreenPages/FullViewMonitor/EvtAutoSummery/index.vue';
import EvtOverview from '@/components/page/ScreenPages/FullViewMonitor/EvtOverview/index.vue';
import EvtTop from '@/components/page/ScreenPages/FullViewMonitor/EvtTop/index.vue';
import FacilityStat from '@/components/page/ScreenPages/FullViewMonitor/FacilityStat/index.vue';
import ManageAreaInfo from '@/components/page/ScreenPages/FullViewMonitor/ManageAreaInfo/index.vue';
import SiteInfo from '@/components/page/ScreenPages/FullViewMonitor/SiteInfo/index.vue';
import InnerIframe from '@/components/page/ScreenPages/IntegratedMonitor/InnerIframe/InnerIframe.vue';
import AlarmList from '@/components/page/ScreenPages/IntegratedMonitor/AlarmList';
import EventStatistics from '@/components/page/ScreenPages/IntegratedMonitor/EventStatistics';
/**
 * 组件映射对象，定义了各种统计类型的组件对应关系。
 * 通过键值对的方式，将统计类型映射到具体的组件类。
 */
export default {
  JURISDICTION_OVERVIEW_STAT: ManageAreaInfo, // 辖区概况统计
  STATION_INFO: SiteInfo, // 站点信息
  EVENT_OVERVIEW_STAT: EvtOverview, // 事件总览
  EVENT_AREA_STAT_TOP5: EvtTop, // 事件TOP5
  AUTO_HANDLE_ANALYSIS: EvtAutoSummery, // 自动化处置分析
  DEVICE_STAT: DeviceStat, // 设备统计
  FACILITY_STAT: FacilityStat, // 设施统计
  inner_iframe: InnerIframe, // 内嵌iframe
  import_focus_event_statistics: EventStatistics, // 事件统计
  import_focus_event_list: AlarmList, // 事件列表

  /**
   * 根据代码动态创建组件。
   * 此函数用于根据给定的代码和包装器，动态创建对应组件实例。
   * @param {Object} param0 创建组件所需的参数对象。
   * @param {string} param0.code 组件代码，用于从组件映射对象中查找对应的组件类。
   * @param {HTMLElement} param0.wrapper 组件要挂载的HTML元素。
   * @param {HTMLElement} param0.props 入参。
   * @param {Object} param0.mapRef 组件要注入的地图实例。
   */
  getComponent({
    code,
    wrapper,
    props,
    mapRef = null,
  }) {
    let compCode = code
    if (props.showTech === 'IFRAME') {
      compCode = 'inner_iframe'
    }
    console.log('getComponent==================', compCode, wrapper, props)
    // 使用组件处理类的createComponent方法创建组件实例。传入组件包装器、组件类、空的props对象和一个唯一的key。
    ComponentHandle.createComponent({
      wrapper: wrapper,
      component: this[code],
      props: props,
      key: uuid(),
      mapRef,
    });
  },
  /**
   * 根据代码获取组件
   *
   * @param code 组件代码
   * @returns 返回对应代码的组件
   */
  getComponentByCode(item) {
    // 内嵌iframe
    const _code = item.showTech === 'IFRAME' ? 'inner_iframe' : item.compCode
    return this[_code]
  }
};
