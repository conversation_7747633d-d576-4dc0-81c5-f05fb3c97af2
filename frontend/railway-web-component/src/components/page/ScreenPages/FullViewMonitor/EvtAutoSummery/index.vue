<template>
  <!-- 组件DetailInfo用于展示自动化处置信息的摘要 -->
  <DetailInfo
    class="evt-auto-summery-wrap"
    v-loading="loading"
    :style="`min-width: ${changePxToRem(minWidth)};min-height: ${changePxToRem(minHeight)};max-width: ${changePxToRem(maxWidth)};max-height: ${changePxToRem(maxHeight)};height: ${changePxToRem(maxHeight)}`"
  >
    <!-- 使用template v-slot:title-icon来定义标题图标 -->
    <template v-slot:title-icon>
      <svg-icon svg-name="icon_event_view" />
    </template>
    <!-- 使用template v-slot:title来定义标题文本 -->
    <template v-slot:title>{{ compName }}</template>
    <!-- 使用template v-slot:detail-content来定义详细内容区域 -->
    <template v-slot:detail-content>
      <div class="pie-index">
        <!-- 展示自动化处置率的圆形进度条 -->
        <div class="pie-content">
          <el-progress
            type="circle"
            :percentage="autoIndex.autoHandleRate"
            :stroke-width="10"
            define-back-color="#254A80"
          ></el-progress>
        </div>
        <!-- 展示事件总数 -->
        <div class="tag tag-total">
          <div class="tag-index">
            <span class="value number-font">{{ formatVal(autoIndex.allNum) }}</span>
            <span class="unit">件</span>
          </div>
          <div class="tag-title">事件总数</div>
        </div>
        <!-- 展示扬声器触发告警数 -->
        <div class="tag tag-speaker">
          <div class="tag-title">扬声器触发事件数</div>
          <div class="tag-index">
            <span class="value number-font">{{ formatVal(autoIndex.autoHandleNum) }}</span>
            <span class="unit">件</span>
          </div>
        </div>
      </div>
    </template>
  </DetailInfo>
</template>

<script>
import {
  // getAutoHandleEvtStatistics,
  getThemeCompByCode,
  getStatisAnalysis,
} from '@/api/service/fullviewService' // 获取接口
import { formatVal } from '@/components/common/utils' // 格式化数字
import DetailInfo from '@/components/page/ScreenPages/FullViewMonitor/DetailInfo' // 详情信息组件
// import { evtAutoAnalysisData } from '@/components/page/ScreenPages/FullViewMonitor/mockData' // 数据
import { mapGetters } from 'vuex' // vuex
import dayjs from 'dayjs' // 时间
import { getFullViewEvtParams } from '@/components/common/utils' // 获取事件筛选参数

/**
 * 自动化处置信息组件
 * 用于展示特定区域的自动化处置事件的统计信息，包括总事件数和扬声器触发告警数。
 */
export default {
  computed: {
    // 监听选中的区域，以更新数据
    selectedRegion() {
      return this.$store.state.map.selectedRegion
    },
    // 筛选按钮状态
    evtFilterSwitch() {
      return this.$store.state.map.evtFilterSwitch
    },
    // vuex
    ...mapGetters('map', ['formatSelectedRegion']),
    // 转换px为rem
    changePxToRem() {
      return val => this.pxToRem(+(val ? val.replace('px', '') : val))
    },
  },
  watch: {
    // 监听选中的区域
    selectedRegion: {
      handler: function (newVal, oldVal) {
        if (newVal) {
          // 筛选
          this.onEventFilterChange({
            filterState: this.filterState,
          })
        }
      },
    },
    // 监听筛选按钮状态
    evtFilterSwitch: {
      handler: function (newVal) {
        if (!newVal) {
          this.filterState = ''
          // 筛选
          this.onEventFilterChange()
        }
      },
    },
  },
  components: {
    DetailInfo, // 详情信息组件
  },
  data: function () {
    return {
      autoIndex: {}, // 存储自动化处置事件的统计信息
      loading: true, // 控制加载状态
      minWidth: '366px', // 最小宽度
      minHeight: '316px', // 最小高度
      maxWidth: '366px', // 最大宽度
      maxHeight: '316px', // 最大高度
      compName: '自动化处置信息', // 组件名称
      filterState: '', // 工具箱事件筛选的状态
    }
  },
  mounted() {
    // 组件挂载后获取主题组件配置并查询自动化处置事件统计信息
    this.qryThemeCompByCode(this.qryEvtAutoIndex)
    this.$EventBus.$on('onEventFilterChange', this.onEventFilterChange)
  },
  beforeDestroy() {
    // 销毁时移除事件筛选过滤条件回调处理
    this.$EventBus.$off('onEventFilterChange')
  },
  methods: {
    formatVal,
    /**
     * 事件筛选过滤条件回调处理
     */
    onEventFilterChange(e = {}) {
      this.filterState = e.filterState || ''
      // 查询
      this.qryEvtAutoIndex()
    },
    /**
     * 查询自动化处置事件的统计信息
     */
    async qryEvtAutoIndex() {
      // 接口
      const [, resData] = await getStatisAnalysis({
        // 默认查半年
        startDate: dayjs().subtract(6, 'months').format('YYYY-MM-DD 00:00:00'),
        endDate: dayjs().format('YYYY-MM-DD 23:59:59'),
        ...getFullViewEvtParams(this.filterState), // 获取事件筛选参数
      })
      // 测试数据 evtAutoAnalysisData.data
      const data = resData
      // 判断
      if (Object.keys(data)?.length) {
        const { num, rate, amount } = data
        // 赋值
        this.autoIndex = {
          autoHandleNum: num,
          autoHandleRate: rate,
          allNum: amount,
        }
      }
    },
    /**
     * 根据组件代码查询主题组件配置
     * @param {Function} call 查询后的回调函数
     */
    async qryThemeCompByCode(
      call = () => {
        console.log('qryThemeCompByCode')
      }
    ) {
      // 接口
      const [success, data] = await getThemeCompByCode({
        compCode: 'AUTO_HANDLE_ANALYSIS',
      })
      // 判断
      if (success && data) {
        const { minWidth, minHeight, maxWidth, maxHeight, compName } = data
        // 更新组件的尺寸和名称
        this.minWidth = minWidth || '366' // 最小宽度
        this.minHeight = minHeight || '316' // 最小高度
        this.maxWidth = maxWidth || '366' // 最大高度
        this.maxHeight = maxHeight || '316' // 最大高度
        this.compName = compName || '自动化处置分析'
      }
      this.loading = false // 结束加载状态
      call()
    },
  },
}
</script>

<style lang='less' src='./index.less' />
