.fullview-detail-info-wrap.evt-auto-summery-wrap {
  width: 366px;

  .pie-index {
    position: relative;
    height: 270px;
    display: flex;
    align-items: center;
    justify-content: center;

    .pie-content {
      width: 200px;
      height: 200px;
      background: url("./img/wanchenglv_beijing.png") 100% 100% no-repeat;
      background-size: 100% 100%;
      display: flex;
      align-items: center;
      justify-content: center;

      .el-progress-circle {
        width: 110px !important;
        height: 110px !important;
      }

      .el-progress--circle .el-progress__text,
      .el-progress--dashboard .el-progress__text {
        font-family: DINAlternate-Bold;
        font-size: 32px !important;
        letter-spacing: 0;
        font-weight: bold;
        background: linear-gradient(180deg, #fefefe 40%, #1fddff);
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent !important;
      }
    }

    .tag {
      position: absolute;

      .tag-index {
        display: flex;
        align-items: center;
        .value {
          font-size: 20px;
          margin-right: 5px;
        }

        .unit {
          font-family: PingFangSC-Regular;
          font-size: 12px;
          color: rgba(255, 255, 255, 0.58);
          letter-spacing: 0;
          text-align: right;
          line-height: 18px;
          font-weight: 400;
        }
      }

      .tag-title {
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #e8f3ff;
        display: flex;
        align-items: center;
        font-weight: 400;
        width: 142px;
        height: 36px;
      }

      &.tag-total {
        bottom: 14px;
        left: 36px;

        .tag-title {
          background: url("./img/biaoqian_zuo.png") 100% 100% no-repeat;
          background-size: 100% 100%;
        }
      }

      &.tag-speaker {
        top: 14px;
        right: 36px;
        display: flex;
        align-items: flex-end;
        flex-direction: column;

        .tag-title {
          background: url("./img/biaoqian_you.png") 100% 100% no-repeat;
          background-size: 100% 100%;
          justify-content: flex-end;
        }
      }
    }
  }
}
