<!--
 * @Description: 提供一个可定制的标题组件，支持图标、文本和关闭按钮的插槽。
 * @Autor: he.chao
 * @Date: 2022-10-19 15:07:55
 * @LastEditors: liu.yongli
 * @LastEditTime: 2023-12-13 14:36:14
-->
<template>
  <!-- 主标题组件，包含图标、文本和关闭按钮的插槽 -->
  <span class='main-title'>
    <!-- 插槽用于放置自定义图标 -->
    <span class='main-title-icon'>
      <slot name='icon'></slot>
    </span>
    <!-- 插槽用于放置标题文本 -->
    <slot name='text'></slot>
    <!-- 插槽用于放置关闭按钮 -->
    <slot name='close'></slot>
  </span>
</template>

<script>
export default {
  // 初始化数据
  data: function() {
    return {};
  },
  // 定义组件的方法
  methods: {}
};
</script>

<!-- 使用 scoped 属性的 CSS，确保样式仅应用于当前组件 -->
<style scoped lang='less' src='./index.less' />
