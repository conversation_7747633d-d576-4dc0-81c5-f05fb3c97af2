<!--
 * @Description: 提供一个可定制的子标题组件，包括图标、文本和关闭按钮等功能。
 * @Autor: he.chao
 * @Date: 2022-10-19 15:07:55
 * @LastEditors: liu.yongli
 * @LastEditTime: 2023-12-13 14:36:14
-->
<template>
  <!-- 子标题组件容器，包含图标、文本和关闭按钮插槽 -->
  <span class='sub-title'>
    <!-- 子标题图标占位符 -->
    <span class='sub-title-icon'>
    </span>
    <!-- 文本内容插槽，用于放置子标题的文本 -->
    <slot name='text'></slot>
    <!-- 关闭按钮插槽，用于放置关闭按钮，可自定义关闭操作 -->
    <slot name='close'></slot>
  </span>
</template>

<script>
export default {
  // 初始化数据方法，目前未使用
  data: function() {
    return {};
  },
  // 组件的方法定义，目前未使用
  methods: {}
};
</script>

<!-- 使用 scoped 属性的样式定义，确保样式仅作用于当前组件 -->
<style scoped lang='less' src='./index.less' />
