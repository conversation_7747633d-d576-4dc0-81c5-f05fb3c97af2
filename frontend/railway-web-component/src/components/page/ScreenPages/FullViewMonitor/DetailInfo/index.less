@import "../common";

@content-width: 360px;
.fullview-detail-info-wrap {
  width: auto;
  height: auto;
  padding: 0 12px;
  border-radius: 8px;
  box-sizing: border-box;
  background-color: transparent;
  background-image: url("./img/bg_bottom_400.png"), url("./img/bg_632.png");
  background-repeat: no-repeat, no-repeat;
  background-position: 100% 100%, 100% 98%;
  background-size: 100% 10px, 100% 100%;

  &.fullview-detail-info-wrap {
    border-radius: 0;
  }
  > div {
    width: 100%;
  }

  .detail-info-header {
    font-family: PingFangSC-Medium;
    font-size: 14px;
    color: #e8f3ff;
    letter-spacing: 0;
    font-weight: 500;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;

    .el-icon-error {
      font-size: 18px;
      margin-left: auto;
    }
  }

  .detail-info-contentNew {
    height: calc(100% - 52px);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-evenly;
    padding-bottom: 12px;
    overflow: hidden;
    overflow-y: scroll;

    > div {
      width: 100%;
      margin-top: 6px;
    }

    .detail-info-sub-title {
      font-family: PingFangSC-Regular;
      font-size: 14px;
      height: 30px;
      color: #e8f3ff;
      letter-spacing: 0;
      font-weight: 400;
      display: flex;
      align-items: center;
      justify-content: flex-start;
    }

    .detail-info-sub-content {
      height: auto;

      &.single-head {
        margin-left: 20px;
        background: #192b41;
        border: 1px dashed #254368;
        width: calc(100% - 40px);
        margin-top: 10px;
        border-radius: 4px;
        position: relative;
        cursor: pointer;
      }
    }

    .detail-info-item {
      padding-left: 30px;
      display: flex;
      align-items: flex-start;
      justify-content: flex-start;
      margin: 6px 0;

      .detail-info-item-label {
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: rgba(232, 243, 255, 0.7);
        letter-spacing: 0;
        font-weight: 400;
      }

      .detail-info-item-value {
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #e8f3ff;
        letter-spacing: 0;
        font-weight: 400;
        display: flex;
        align-items: flex-start;
        justify-content: flex-start;

        .value {
          max-width: 200px; /* 定义容器宽度 */
          word-break: break-all; /* 指定超过容器宽度后将不再按字符断开而直接换行 */
        }

        .el-icon-document-copy {
          margin-left: 6px;
        }
      }
    }
  }

  .detail-bottom-content {
    width: 100%;
    height: 40px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }
}

.main-title {
  width: 100%;
  height: 40px;
  padding: 0 10px;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  background: url("./img/bg_main_title_bg.svg") 100% 100% no-repeat;
  background-size: contain;
  font-family: PingFangSC-Medium;
  font-size: 16px;
  color: #e8f3ff;
  letter-spacing: 0;
  text-shadow: 2px 2px 2px rgba(0, 0, 0, 0.5);
  font-weight: 500;

  .main-title-icon {
    height: 30px;
    width: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 10px;

    .icon {
      margin-left: -6px;
    }

    /deep/.svg-icon {
      margin-right: 0px !important;
    }
  }

  .el-icon-error {
    cursor: pointer;
    align-self: flex-start;
  }
}

.sub-title {
  width: 100%;
  height: 30px;
  color: @color;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  background: url("./img/title_sub_title_bg.png") 100% 100% no-repeat;
  background-size: 100% 50%;

  .sub-title-icon {
    height: 20px;
    width: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: url("./img/icon_sub_title_icon.svg") 100% 100% no-repeat;
    background-size: 100% 100%;
    margin: 0 10px;
  }
}
