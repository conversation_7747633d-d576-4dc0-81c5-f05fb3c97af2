<!--
 * @Description:
 * @Autor: he.chao
 * @Date: 2022-10-19 15:07:55
 * @LastEditors: liu.yongli
 * @LastEditTime: 2023-12-13 14:36:14
-->
<template>
  <div class="fullview-detail-info-wrap">
    <div class="detail-info-header">
      <MainTitle>
        <template v-slot:icon>
          <slot name="title-icon"></slot>
        </template>
        <template v-slot:text>
          <slot name="title"></slot>
        </template>

        <template v-if="close" v-slot:close>
          <i class="el-icon-error" @click="close" />
        </template>
      </MainTitle>
    </div>
    <template>
      <div class="detail-info-contentNew">
        <slot name="detail-content" />
      </div>
    </template>
  </div>
</template>
<script>
import MainTitle from '@/components/page/ScreenPages/FullViewMonitor/DetailInfo/MainTitle'
import SubTitle from '@/components/page/ScreenPages/FullViewMonitor/DetailInfo/SubTitle.vue'

/**
 * 该组件是一个标题组件，提供了主标题和副标题的显示功能。
 * 它可以被其他组件所使用，通过传入不同的标题内容来展示不同的标题。
 *
 * @component SubTitle
 * @component MainTitle
 *
 * @prop {Function} close - 关闭组件的回调函数。当需要关闭组件时，调用此函数。
 */
export default {
  // 计算属性定义
  computed: {},
  // 接收的外部属性
  props: {
    // close属性是一个函数类型，用于关闭组件。如果没有提供，默认为null。
    close: {
      type: Function,
      default: null,
    },
  },
  // 使用的子组件
  components: {
    SubTitle,
    MainTitle,
  },
  // 数据属性初始化
  data() {
    return {}
  },
  // 方法定义
  methods: {},
}
</script>

<style lang='less' src='./index.less' scoped />
