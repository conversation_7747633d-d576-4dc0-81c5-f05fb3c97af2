<template>
  <!-- 设备状态图标展示区域 -->
  <div class="device-stat-wrap">
    <!-- 遍历索引列表，展示每个设备状态图标 -->
    <IconIndex
      v-for="(item, index) in indexList"
      :key="`device_stat_${index}`"
      :prop-data="{
          ...item,
          flag: 'sum'
      }"
    />
  </div>
</template>

<script>
import { getThemeCompByCode } from '@/api/service/fullviewService'
import IconIndex from '@/components/page/ScreenPages/FullViewMonitor/IconIndex/index.vue'
import { defaultParam } from './defaultCompParam'
import { DEVICE_STATData } from '@/components/page/ScreenPages/FullViewMonitor/mockData'

/**
 * 设备状态组件
 * 该组件用于展示设备的状态图标。
 *
 * @component IconIndex 图标组件，用于展示单个设备状态
 */
export default {
  components: { IconIndex },
  data() {
    return {
      // 存储图标列表数据
      indexList: [],
    }
  },
  props: {},
  mounted() {
    // 页面加载时获取设备状态图标配置
    this.qryThemeCompByCode()
  },
  methods: {
    /**
     * 根据组件代码查询主题组件配置
     * 该方法用于初始化设备状态图标的数据。
     *
     * @async
     * @method qryThemeCompByCode
     * @returns {void}
     */
    async qryThemeCompByCode() {
      // 调用接口获取主题组件配置
      const [, resData] = await getThemeCompByCode({
        compCode: 'DEVICE_STAT',
      })
      const data = resData ?? DEVICE_STATData.data
      if (data?.compParamList) {
        // 解析返回数据，获取图标列表，并赋值给indexList
        const { compParamList } = data
        this.indexList =
          compParamList && compParamList.length > 0
            ? compParamList
            : defaultParam
      }
    },
  },
}
</script>

<style lang='less' src='./index.less' />
