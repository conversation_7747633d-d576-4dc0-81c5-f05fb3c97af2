export const getThemeCompByCodeData = {
  data: {
    "compId": 6,
    "compName": "设备统计",
    "compCode": "DEVICE_STAT",
    "compIconUrl": "",
    "compImgUrl": "https://video-platform.obs.cn-north-4.myhuaweicloud.com/slyy2/dev/1.0/c1b0cdc5cff2ef16225b5a90bd2f4eed/ce205b9e8534fac2610f0b18e874604b/3ad8f7d8d9444eb3908f91ae4775a123.png?AccessKeyId=CRFYWADDOJHXLKROOIXX&Expires=1740639792&Signature=DlJvSjkJ16QfNIgH5jwFy0jv1Ek%3D",
    "defaultWidth": "1920",
    "defaultHeight": "70",
    "minWidth": "1920",
    "minHeight": "70",
    "maxWidth": "1920",
    "maxHeight": "70",
    "compUrl": "/xxx/xx/",
    "showTech": "NATIVE",
    "compParam": "",
    "isSystem": "Y",
    "modelCodes": "1001613",
    "state": "A",
    "tenantId": "121062346",
    "industryCode": "800001",
    "creatorId": "",
    "creatorName": "",
    "gmtCreate": "2025-02-21T11:28:48",
    "gmtModify": "2025-02-26T15:03:27",
    "modifierId": "16afc782cea14666bc02ac170a6d725f",
    "modifierName": "railway01",
    "compParamList": [
      {
        "paramId": 1,
        "compId": 6,
        "paramName": "摄像机",
        "paramCode": "CAMERA",
        "iconUrl": "https://video-platform.obs.cn-north-4.myhuaweicloud.com/slyy2/prd/1.0/bb08f84d9ddf54459ea7b705612bafa1/dc8318b68c285babc3f6b00029c115ab/f4a02c86b7f64ef997ec6c32c186dc67.svg?AccessKeyId=CRFYWADDOJHXLKROOIXX&Expires=1740639785&Signature=NlToAq3ifrZ7tpJmsBQ%2FEZ2cOp0%3D",
        "seq": 1,
        "paramJson": null,
        "isDefault": "Y",
        "state": "A",
        "creatorId": "",
        "creatorName": "",
        "gmtCreate": "2025-02-21T11:42:22",
        "gmtModify": "2025-02-26T15:03:27",
        "modifierId": "16afc782cea14666bc02ac170a6d725f",
        "modifierName": "railway01",
        "tenantId": "121062346",
        "industryCode": null,
        "defaultValue": ""
      },
      {
        "paramId": 2,
        "compId": 6,
        "paramName": "云广播",
        "paramCode": "HORN",
        "iconUrl": "https://video-platform.obs.cn-north-4.myhuaweicloud.com/slyy2/prd/1.0/bb08f84d9ddf54459ea7b705612bafa1/dc8318b68c285babc3f6b00029c115ab/01df65757cc446e78561556967c911a7.svg?AccessKeyId=CRFYWADDOJHXLKROOIXX&Expires=1740639785&Signature=KXarepzj1N4EZBzwI5aq5eyJ%2FE0%3D",
        "seq": 2,
        "paramJson": null,
        "isDefault": "Y",
        "state": "A",
        "creatorId": "",
        "creatorName": "",
        "gmtCreate": "2025-02-21T11:42:22",
        "gmtModify": "2025-02-26T15:03:27",
        "modifierId": "16afc782cea14666bc02ac170a6d725f",
        "modifierName": "railway01",
        "tenantId": "121062346",
        "industryCode": null,
        "defaultValue": ""
      }
    ]
  }
}

export const evtStatisticsData = {
  "data": {
    "unfinishedEvent": 13,
    "totalEvent": 68,
    "finishedEvent": 31
  }
}

export const evtTypesStatisticsData = {
  data: {
    // 人工上报告警数
    artEvent: 21,
    // 智能告警数
    aiEvent: 54,
    "list": [
      {
        "rate": 34.7,
        "num": 92,
        "warningTypeName": "行人检测告警"
      },
      {
        "rate": 12.5,
        "num": 33,
        "warningTypeName": "垃圾堆放监控告警"
      },
      {
        "rate": 12.5,
        "num": 33,
        "warningTypeName": "车辆闯入监控告警"
      },
      {
        "rate": 7.5,
        "num": 20,
        "warningTypeName": null
      },
      {
        "rate": 4.9,
        "num": 13,
        "warningTypeName": "实时人流量监控告警"
      },
      {
        "rate": 4.5,
        "num": 12,
        "warningTypeName": "人脸检测告警"
      },
      {
        "rate": 3.4,
        "num": 9,
        "warningTypeName": null
      },
      {
        "rate": 2.6,
        "num": 7,
        "warningTypeName": "水质检测告警"
      },
      {
        "rate": 2.6,
        "num": 7,
        "warningTypeName": "人员闯入监控告警"
      },
      {
        "rate": 2.6,
        "num": 7,
        "warningTypeName": "违规采砂监控"
      },
      {
        "rate": 1.9,
        "num": 5,
        "warningTypeName": "车牌识别告警"
      },
      {
        "rate": 1.5,
        "num": 4,
        "warningTypeName": "围栏越界报警告警"
      },
      {
        "rate": 1.5,
        "num": 4,
        "warningTypeName": "船只检测告警"
      },
      {
        "rate": 1.5,
        "num": 4,
        "warningTypeName": "指定场所周界监控告警"
      },
      {
        "rate": 1.1,
        "num": 3,
        "warningTypeName": "移动侦测告警告警"
      },
      {
        "rate": 1.1,
        "num": 3,
        "warningTypeName": "人员闯入高铁铁轨"
      },
      {
        "rate": 0.8,
        "num": 2,
        "warningTypeName": "铁路沿线硬飘浮物识别告警"
      },
      {
        "rate": 0.8,
        "num": 2,
        "warningTypeName": "人员徘徊监控告警"
      },
      {
        "rate": 0.4,
        "num": 1,
        "warningTypeName": "目标丢失检测告警"
      },
      {
        "rate": 0.4,
        "num": 1,
        "warningTypeName": "滞留徘徊检测告警"
      },
      {
        "rate": 0.4,
        "num": 1,
        "warningTypeName": "动物闯入识别告警"
      },
      {
        "rate": 0.4,
        "num": 1,
        "warningTypeName": "火点检测"
      },
      {
        "rate": 0.4,
        "num": 1,
        "warningTypeName": "雷达、AIS目标从外部闯入电子围栏"
      }
    ]
  }
}

export const evtTop5Data = {
  "data1": [
    {
      "cityName": "向超",
      "num": 40,
      "provinceName": "天津市",
      "countyName": "界首市"
    }
  ],
  data: [
    {
      "cityName": "北京市北京市石景山区",
      "num": 61,
      "realNum": 0
    },
    {
      "cityName": "北京市北京市西城区",
      "num": 60,
      "realNum": 0
    },
    {
      "cityName": "北京市北京市朝阳区",
      "num": 50,
      "realNum": 0
    },
    {
      "cityName": "北京市北京市东城区",
      "num": 38,
      "realNum": 0
    },
    {
      "cityName": "山西省太原市阳曲县",
      "num": 36,
      "realNum": 0
    }
  ]
}

export const evtAutoAnalysisData = {
  "data": {
    "amount": 408,
    "rate": 68,
    "num": 83
  }
}

export const evtLayerData = {
  "data": [
    {
      "id": 208,
      "dataSource": "4",
      "fusionStatus": "1",
      "warningTypeId": "1212104",
      "warningTypeName": "指定场所周界监控告警",
      "eventSource": "2",
      "title": "test1125",
      "directionType": null,
      "eventAddress": "北京市北京市东城区东华门街道西长安街4号",
      "startLng": 116.39,
      "startLat": 39.91,
      "endLng": 116.39,
      "endLat": 39.91,
      "eventAddressGeo": null,
      "lineName": null,
      "nearIoiName": null,
      "deviceName": null,
      "deviceCode": null,
      "channelCode": null,
      "description": "tets",
      "firstAlarmCode": "202506121210623462598569",
      "alarmStatus": "1",
      "updateTime": "2025-06-12T11:26:04",
      "occurTime": "2025-06-12T11:26:04",
      "endTime": null,
      "fileImgUrlIcon": "https://video-platform.obs.cn-north-4.myhuaweicloud.com/slyy2/dev/1.0/bf639ce7c0571f822f5db1642b26552c/47956bd0b900cf4db139620df06e2d6a/9345631c418d4225995e7ddb3a045194.png?AccessKeyId=CRFYWADDOJHXLKROOIXX&Expires=1749801777&Signature=0%2BdaTWjSjjS6CfSjCeMiqbqBzxw%3D",
      "lastEventId": "202506121210623462598569",
      "cityCode": "110100",
      "cityName": "北京市",
      "provinceCode": "110000",
      "provinceName": "北京市",
      "countyCode": "110101",
      "countyName": "东城区",
      "tenantId": "121062346",
      "findName": "railway01",
      "industryCode": "800001",
      "gmtCreate": "2025-06-12T11:27:01",
      "gmtModify": "2025-06-12T11:27:01",
      "duration": null,
      "effectLength": null,
      "alarmIds": null,
      "alarmList": null,
      "mobilePhone": null,
      "channelName": null,
      "remark": null,
      "eventType": "5",
      "eventTypeName": "其他",
      "semantic": null,
      "congestIndex": null,
      "congestLevel": null,
      "avgSpeed": null,
      "linkStates": null,
      "isCollecltion": "0",
      "emergencyLevel": "3",
      "emergencyLevelName": "一般",
      "stations": null,
      "linkId": null,
      "segmentId": null,
      "isInner": null,
      "segmentLink": null
    }
  ]
}

export const DEVICE_STATData = {
  data: {
    compParamList: [
      {
        "paramId": 1,
        "compId": 6,
        "paramName": "摄像机",
        "paramCode": "CAMERA",
        "iconUrl": "https://video-platform.obs.cn-north-4.myhuaweicloud.com/slyy2/prd/1.0/bb08f84d9ddf54459ea7b705612bafa1/dc8318b68c285babc3f6b00029c115ab/f4a02c86b7f64ef997ec6c32c186dc67.svg?AccessKeyId=CRFYWADDOJHXLKROOIXX&Expires=1740639785&Signature=NlToAq3ifrZ7tpJmsBQ%2FEZ2cOp0%3D",
        "seq": 1,
        "paramJson": null,
        "isDefault": "Y",
        "state": "A",
        "creatorId": "",
        "creatorName": "",
        "gmtCreate": "2025-02-21T11:42:22",
        "gmtModify": "2025-02-26T15:03:27",
        "modifierId": "16afc782cea14666bc02ac170a6d725f",
        "modifierName": "railway01",
        "tenantId": "121062346",
        "industryCode": null,
        "defaultValue": ""
      },
      {
        "paramId": 2,
        "compId": 6,
        "paramName": "云广播",
        "paramCode": "HORN",
        "iconUrl": "https://video-platform.obs.cn-north-4.myhuaweicloud.com/slyy2/prd/1.0/bb08f84d9ddf54459ea7b705612bafa1/dc8318b68c285babc3f6b00029c115ab/01df65757cc446e78561556967c911a7.svg?AccessKeyId=CRFYWADDOJHXLKROOIXX&Expires=1740639785&Signature=KXarepzj1N4EZBzwI5aq5eyJ%2FE0%3D",
        "seq": 2,
        "paramJson": null,
        "isDefault": "Y",
        "state": "A",
        "creatorId": "",
        "creatorName": "",
        "gmtCreate": "2025-02-21T11:42:22",
        "gmtModify": "2025-02-26T15:03:27",
        "modifierId": "16afc782cea14666bc02ac170a6d725f",
        "modifierName": "railway01",
        "tenantId": "121062346",
        "industryCode": null,
        "defaultValue": ""
      }
    ]
  }
}

export const indexByCodeData = {
  data: {
    "offline": 6,
    "total": 24,
    "paramCode": "CAMERA",
    "online": 18,
    "onlineRate": "75"
  }
}

export const FACILITY_STATData = {
  data: {
    compParamList: [
      {
        "paramId": 3,
        "compId": 7,
        "paramName": "桥梁",
        "paramCode": "R:202405271745072609",
        "iconUrl": "https://video-platform.obs.cn-north-4.myhuaweicloud.com/slyy2/prd/1.0/bb08f84d9ddf54459ea7b705612bafa1/dc8318b68c285babc3f6b00029c115ab/efc104283f3243fabe399366b252a9b3.svg?AccessKeyId=CRFYWADDOJHXLKROOIXX&Expires=1740639810&Signature=MouKDlS%2FMBUeBB8DeG8UoKLk%2F%2Bs%3D",
        "seq": 1,
        "paramJson": null,
        "isDefault": "Y",
        "state": "A",
        "creatorId": "",
        "creatorName": "",
        "gmtCreate": "2025-02-21T11:42:22",
        "gmtModify": "2025-02-26T15:03:56",
        "modifierId": "16afc782cea14666bc02ac170a6d725f",
        "modifierName": "railway01",
        "tenantId": "121062346",
        "industryCode": null,
        "defaultValue": ""
      },
      {
        "paramId": 4,
        "compId": 7,
        "paramName": "隧道",
        "paramCode": "R:202406041411449843",
        "iconUrl": "https://video-platform.obs.cn-north-4.myhuaweicloud.com/slyy2/prd/1.0/bb08f84d9ddf54459ea7b705612bafa1/dc8318b68c285babc3f6b00029c115ab/f26c5dd9a2d545a3b32166dc821aa525.svg?AccessKeyId=CRFYWADDOJHXLKROOIXX&Expires=1740639810&Signature=OWkLf%2B%2BB1ph54uqbQqY2DC9HI2A%3D",
        "seq": 2,
        "paramJson": null,
        "isDefault": "Y",
        "state": "A",
        "creatorId": "",
        "creatorName": "",
        "gmtCreate": "2025-02-21T11:42:22",
        "gmtModify": "2025-02-26T15:03:56",
        "modifierId": "16afc782cea14666bc02ac170a6d725f",
        "modifierName": "railway01",
        "tenantId": "121062346",
        "industryCode": null,
        "defaultValue": ""
      }
    ]
  }
}

export const indexByCodeData2 = {
  data: {
    "total": 3,
    "paramCode": "R:202405271745072609"
  }
}

export const lineStatSummaryData = {
  data: {
    "statId": null,
    "statTime": null,
    "tenantId": null,
    "industryCode": null,
    "districtCode": null,
    "lineLevel": null,
    "lineLevelName": null,
    "totalNum": 6426,
    "totalLength": null,
    "totalLengthFmt": "118457.8",
    "isLock": null,
    "gmtCreate": null,
    "gmtModify": null
  }
}