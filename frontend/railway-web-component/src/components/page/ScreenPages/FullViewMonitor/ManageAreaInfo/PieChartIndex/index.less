@import "../../common";

.pie-chart-index {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .pie-chart-wrap {
    width: 110px;
    height: 110px;
    background: url("./img/bg_pie_char.png") 100% 100% no-repeat;
    background-size: 100% 100%;
  }

  .index-list-wrap {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    height: 100%;

    .index-list-item {
      margin-left: 12px;
      min-width: 220px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      background-image: linear-gradient(
        90deg,
        #174a79 10%,
        #174a7994 30%,
        transparent
      );
      color: @color;
      padding: 0;

      &:not(:first-child) {
        margin-top: 12px;
      }

      .index-label {
        font-size: 14px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: auto;

        .icon-yuan {
          width: 5px;
          height: 5px;
          margin: 0 12px;
          display: inline-block;
          transform: rotate(45deg);
          border: 2px solid rgba(237, 81, 88, 1);

          &::before {
            content: "";
          }
        }
      }

      .index-value {
        display: flex;
        align-items: center;
        justify-content: center;

        .index-value-num {
          margin-right: 4px;
          display: flex;
          align-items: center;
          justify-content: center;

          .num {
            width: 45px;
            font-size: 20px;
            text-align: right;
          }

          .unit {
            margin-left: 6px;
            font-family: PingFangSC-Regular;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.58);
            letter-spacing: 0;
            text-align: right;
            font-weight: 400;
          }
        }

        .index-value-percent {
          width: 50px;
          height: 30px;
          font-size: 20px;
          line-height: 30px;
          text-shadow: 0 4px 4px #003f64;
          display: flex;
          align-items: center;
          justify-content: center;

          &::before {
            content: "";
            display: flex;
            width: 2px;
            height: 12px;
            background-color: #8b8b8b;
            margin-right: 3px;
          }
        }
      }
    }
  }
}
