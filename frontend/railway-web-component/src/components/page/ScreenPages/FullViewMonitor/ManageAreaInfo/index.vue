<template>
  <!-- 管理区域信息组件，用于展示辖区的统计信息，如路段数量、里程统计等 -->
  <DetailInfo
    class="manage-area-info-wrap"
    v-loading="loading"
    :style="`min-width: ${changePxToRem(minWidth)};min-height: ${changePxToRem(minHeight)};max-width: ${changePxToRem(maxWidth)};max-height: ${changePxToRem(maxHeight)};height: ${changePxToRem(maxHeight)}`"
  >
    <!-- 设置标题图标 -->
    <template v-slot:title-icon>
      <svg-icon svg-name="icon_manage_area" />
    </template>
    <!-- 设置标题 -->
    <template v-slot:title>{{ compName }}</template>
    <!-- 展示路段数量统计 -->
    <template v-slot:detail-content>
      <AreaIndex />
      <SubTitle>
        <template v-slot:text>路段数量统计</template>
      </SubTitle>
      <!-- 使用饼图展示不同等级路段的数量 -->
      <PieChartIndex
        key="pie-levle"
        v-if="!loading"
        :list-data="lineLevel"
        :pie-data="lineLevelPieData"
        unit="条"
      />
      <SubTitle>
        <template v-slot:text>路段里程统计</template>
      </SubTitle>
      <!-- 使用饼图展示不同等级路段的里程 -->
      <PieChartIndex
        key="pie-length"
        v-if="!loading"
        :list-data="lineLength"
        :pie-data="lineLengthPieData"
        unit="km"
      />
      <SubTitle>
        <template v-slot:text>路段类型里程统计</template>
      </SubTitle>
      <RailwayType />
    </template>
  </DetailInfo>
</template>

<script>
import {
  getLineLevelLengthStat,
  getLineLevelNumStat,
  getThemeCompByCode,
} from '@/api/service/fullviewService'
import DetailInfo from '@/components/page/ScreenPages/FullViewMonitor/DetailInfo'
import SubTitle from '@/components/page/ScreenPages/FullViewMonitor/DetailInfo/SubTitle.vue'
import AreaIndex from '@/components/page/ScreenPages/FullViewMonitor/ManageAreaInfo/AreaIndex'
import PieChartIndex from '@/components/page/ScreenPages/FullViewMonitor/ManageAreaInfo/PieChartIndex'
import RailwayType from '@/components/page/ScreenPages/FullViewMonitor/ManageAreaInfo/RailwayType'
import { toPercentText } from '@/utils/common'
import { mapGetters } from 'vuex'

/**
 * 管理区域信息页面，用于展示选定区域的统计信息，包括路段数量、里程统计等。
 */
export default {
  components: {
    SubTitle,
    DetailInfo,
    AreaIndex,
    PieChartIndex,
    RailwayType,
  },
  computed: {
    // 获取所选区域的信息，用于统计
    selectedRegion() {
      return this.$store.state.map.selectedRegion
    },
    changePxToRem() {
      return val => this.pxToRem(+(val ? val.replace('px', '') : val))
    },
    // 从vuex映射获取格式化后的所选区域信息
    ...mapGetters('map', ['formatSelectedRegion']),
  },
  watch: {
    // 当所选区域变更时，更新统计信息
    selectedRegion: {
      handler: function (newVal, oldVal) {
        if (newVal) {
          this.qryLineLevelNumStat()
          this.qryLineLevelLengthStat()
        }
      },
    },
  },
  data: function () {
    return {
      // 路段等级数量统计数据
      lineLevel: [],
      // 路段等级数量饼图数据
      lineLevelPieData: [],
      // 路段等级里程统计数据
      lineLength: [],
      // 路段等级里程饼图数据
      lineLengthPieData: [],
      // 加载状态
      loading: true,
      // 组件最小宽度
      minWidth: '366px',
      // 组件最小高度
      minHeight: '630px',
      // 组件最大宽度
      maxWidth: '366px',
      // 组件最大高度
      maxHeight: '630px',
      // 组件名称
      compName: '辖区概况',
    }
  },
  mounted() {
    // 初始化时获取组件配置并加载统计信息
    this.qryThemeCompByCode(() => {
      this.qryLineLevelNumStat()
      this.qryLineLevelLengthStat()
    })
  },
  methods: {
    /**
     * 格式化数据
     */
    formatData(data) {
      return data.map(item => {
        return {
          name: item.name,
          value: item.totalNum,
        }
      })
    },
    /**
     * 查询路段等级数量统计
     */
    async qryLineLevelNumStat() {
      const [success, data] = await getLineLevelNumStat({
        ...this.formatSelectedRegion,
      })
      let resLineLevel = []
      let resLineLevelPieData = []
      if (success && data && data.length > 0) {
        let totalNum = _.sumBy(data, function (o) {
          return o.totalNum ? o.totalNum : 0
        })
        resLineLevel = data.map(item => {
          return {
            name: item.lineLevelName,
            totalNum: item.totalNum,
            percent: toPercentText(item.totalNum, totalNum),
          }
        })
        resLineLevelPieData = this.formatData(resLineLevel)
      }
      this.lineLevel = resLineLevel
      this.lineLevelPieData = resLineLevelPieData
    },
    /**
     * 查询路段等级里程统计
     */
    async qryLineLevelLengthStat() {
      const [success, data] = await getLineLevelLengthStat({
        ...this.formatSelectedRegion,
      })
      let resLength = []
      let resLengthPieData = []
      if (success && data && data.length > 0) {
        let totalNum = _.sumBy(data, function (o) {
          return o.totalLengthFmt ? Number(o.totalLengthFmt) : 0
        })
        resLength = data.map(item => {
          let numberLength = Number(item.totalLengthFmt)
          return {
            name: item.lineLevelName,
            totalNum: Number(item.totalLengthFmt),
            percent: toPercentText(numberLength, totalNum),
          }
        })
        resLengthPieData = this.formatData(resLength)
      }
      this.lineLength = resLength
      this.lineLengthPieData = resLengthPieData
    },
    /**
     * 根据组件代码查询组件配置信息
     * @param {Function} call - 回调函数，在获取组件配置后调用
     */
    async qryThemeCompByCode(
      call = () => {
        console.log('qryThemeCompByCode')
      }
    ) {
      const [success, data] = await getThemeCompByCode({
        compCode: 'JURISDICTION_OVERVIEW_STAT',
      })
      if (success && data) {
        const { minWidth, minHeight, maxWidth, maxHeight, compName } = data
        this.minWidth = minWidth || '366'
        this.minHeight = minHeight || '632'
        this.maxWidth = maxWidth || '366'
        this.maxHeight = maxHeight || '632'
        this.compName = compName || '辖区概况统计'
      }
      this.loading = false
      call()
    },
  },
}
</script>

<style lang='less' src='./index.less' />
