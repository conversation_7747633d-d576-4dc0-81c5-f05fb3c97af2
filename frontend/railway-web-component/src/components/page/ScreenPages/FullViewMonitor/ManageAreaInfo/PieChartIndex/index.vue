<template>
  <!-- 统计图表组件，用于展示饼图和相关统计数据 -->
  <div class="pie-chart-index">
    <!-- 饼图区域 -->
    <div class="pie-chart-wrap">
      <VCharts v-if="pieDataStat.length>0" :data="pieDataStat" :color="colorList" />
    </div>
    <!-- 数据列表区域 -->
    <div class="index-list-wrap">
      <div v-for="(item,index) in listData" class="index-list-item" :key="`${item.name}_${index}`">
        <!-- 数据标签 -->
        <div class="index-label">
          <span class="icon-yuan" :style="`border-color:${colorList[index% colorList.length]}` " />
          {{ item.name }}
        </div>
        <!-- 数据值和占比 -->
        <div class="index-value">
          <div class="index-value-num">
            <span class="num number-font over-flow-hidden">{{ formatVal(item.totalNum) }}</span>
            <span class="unit">{{ unit }}</span>
          </div>
          <span class="index-value-percent unit number-font">{{ formatVal(item.percent) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { formatVal } from '@/components/common/utils'
import VCharts from '@/components/common/VCharts'
import DicConst from '../../enums/DicConst'

// 颜色列表，用于图表和数据标签的配色
const colorList = ['#ED5158FF', '#15BD94', '#FFA940']

export default {
  components: {
    VCharts,
  },
  props: {
    /**
     * 数据单位配置项
     * @property {String} unit - 数据的单位显示，默认为'条'
     */
    unit: {
      type: String,
      default: '条',
    },
    /**
     * 饼图数据配置项
     * @property {Array} pieData - 饼图所需的数据数组，默认为空数组
     */
    pieData: {
      type: Array,
      default: [],
    },
    /**
     * 列表数据配置项
     * @property {Array} listData - 列表展示所需的数据数组，默认为空数组
     */
    listData: {
      type: Array,
      default: [],
    },
  },
  // 监听pieData的变化，实时更新图表数据
  watch: {
    pieData: {
      handler(newVal, oldVal) {
        this.pieDataStat = newVal
      },
      deep: true,
    },
  },
  data() {
    return {
      // 定义颜色列表，用于图表或其他元素的配色方案
      colorList,
      // 引用常量字典，包含一些预定义的常量值，用于在组件中保持数据的一致性和复用性
      DicConst,
      // 初始化饼图数据状态，为空数组，后续根据业务逻辑进行填充
      // 初始化图表数据状态
      pieDataStat: [],
    }
  },
  mounted() {
    // 组件挂载后的逻辑
  },
  methods: {
    formatVal,
  },
}
</script>

<style lang='less' src='./index.less' />
