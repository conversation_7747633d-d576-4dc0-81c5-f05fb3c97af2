.area-index-wrap {
   display: flex;
   align-items: center;
   justify-content: space-evenly;

   .index-wrap {
      display: flex;
      align-items: center;
      justify-content: flex-start;

      .svg-icon {
         width: 60px;
         height: 54px;
      }

      .label-index {
         font-family: PingFangSC-Regular;
         font-size: 14px;
         color: #E8F3FF;
         letter-spacing: 0;
         font-weight: 400;
         display: flex;
         flex-direction: column;
         align-items: flex-start;
         justify-content: center;

         .val {
            font-family: DINAlternateBold;
            font-size: 32px;
            letter-spacing: 0;
            font-weight: 700;
            display: flex;
            align-items: flex-end;
            justify-content: flex-end;

            .unit {
               font-family: PingFangSC-Regular;
               font-size: 12px;
               color: rgba(255, 255, 255, 0.65);
               letter-spacing: 0;
               font-weight: 400;
            }
         }
      }
   }
}
