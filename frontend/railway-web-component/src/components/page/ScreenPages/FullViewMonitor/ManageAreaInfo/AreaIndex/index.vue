<template>
  <div class="area-index-wrap">
    <!-- 展示辖区路段总数 -->
    <div class="index-wrap">
      <svg-icon svg-name="icon_area_seg" />
      <div class="label-index">
        <span class="label">辖区路段</span>
        <span class="val number-font">
          {{ formatVal(areaIndex.totalNum) }}
          <span class="unit">条</span>
        </span>
      </div>
    </div>
    <!-- 展示辖区总里程 -->
    <div class="index-wrap">
      <svg-icon svg-name="icon_area_mile" />
      <div class="label-index">
        <span class="label">辖区总里程</span>
        <span class="val number-font">
          {{ formatVal(areaIndex.totalLengthFmt) }}
          <span class="unit">km</span>
        </span>
      </div>
    </div>
  </div>
</template>

<script>
import { getLineStatSummary } from '@/api/service/fullviewService'
import { formatVal } from '@/components/common/utils'
import { mapGetters } from 'vuex'
import { lineStatSummaryData } from '@/components/page/ScreenPages/FullViewMonitor/mockData'
/**
 * 区域统计组件
 * 用于展示选定区域的路段总数和总里程
 */
export default {
  computed: {
    // 从 Vuex 中获取选定的区域信息
    selectedRegion() {
      return this.$store.state.map.selectedRegion
    },
    // 使用 mapGetters 将格式化后的选定区域信息映射为计算属性
    ...mapGetters('map', ['formatSelectedRegion']),
  },
  watch: {
    // 当选定的区域变更时，更新统计数据
    selectedRegion: {
      handler: function (newVal, oldVal) {
        if (newVal) {
          this.qryData()
        }
      },
    },
  },
  data() {
    return {
      // 存储辖区统计信息
      areaIndex: {},
    }
  },
  mounted() {
    // 页面加载时初始化统计数据
    this.qryData()
  },
  methods: {
    formatVal,
    /**
     * 获取区域统计数据
     */
    async qryData() {
      // 调用 API 获取线路统计摘要
      const [, resData] = await getLineStatSummary({
        ...this.formatSelectedRegion,
      })
      const data = resData ?? lineStatSummaryData.data
      if (Object.keys(data)?.length) {
        // 解构并更新辖区路段总数和总里程
        const { totalNum, totalLengthFmt } = data
        this.areaIndex = {
          totalNum,
          totalLengthFmt,
        }
      }
    },
  },
}
</script>

<style lang='less' src='./index.less' />
