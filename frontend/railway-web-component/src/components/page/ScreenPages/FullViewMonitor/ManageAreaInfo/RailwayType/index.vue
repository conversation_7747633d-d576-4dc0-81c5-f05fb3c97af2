<!--
 * @Description:
 * @Autor: he.chao
 * @Date: 2022-10-19 15:07:55
 * @LastEditors: liu.yongli
 * @LastEditTime: 2023-12-13 14:36:14
-->
<template>
  <div class="railway-type-wrap">
    <el-empty :image-size="20" v-if="lineUsageNumStat.length < 1" />
    <div
      v-for="(item,idx) in lineUsageNumStat"
      class="type-index-wrap"
      :key="`${item.name}_${idx}`"
    >
      <span class="index-label">{{ item.name }}</span>
      <span class="index-val">
        <span class="index-val-num number-font">
          {{
          formatVal(item.totalNum)
          }}
        </span>
        <span class="index-val-unit">km</span>
      </span>
    </div>
  </div>
</template>

<script>
import { getLineUsageLengthStat } from '@/api/service/fullviewService'
import { formatVal } from '@/components/common/utils'
import { mapGetters } from 'vuex'

export default {
  // 计算属性：获取地图事件层开关状态
  computed: {
    /**
     * 获取当前选中的区域
     * @returns {string} 选中的区域
     */
    selectedRegion() {
      return this.$store.state.map.selectedRegion //需要监听的属性
    },
    // 从store中获取格式化后的选中区域信息
    ...mapGetters('map', ['formatSelectedRegion']),
  },
  // 监视区域变更
  watch: {
    /**
     * 监视选中区域的变化
     * 当选中区域变化时，触发qryLineUsageNumStat方法查询线路使用数量统计
     * @param {string} newVal 新选中的区域
     * @param {string} oldVal 旧选中的区域
     */
    selectedRegion: {
      handler: function (newVal, oldVal) {
        if (newVal) {
          this.qryLineUsageNumStat()
        }
      },
    },
  },
  data: function () {
    return {
      lineUsageNumStat: [],
    }
  },
  mounted() {
    this.qryLineUsageNumStat()
  },
  methods: {
    formatVal,
    /**
     * 异步查询线路使用数量统计信息。
     *
     * 该方法通过调用getLineUsageLengthStat接口，获取指定区域的线路使用长度统计信息。
     * 返回的数据将被格式化为更易于使用的对象数组，每个对象包含线路名称和总长度。
     */
    async qryLineUsageNumStat() {
      /* 调用接口获取线路使用长度统计信息，传入格式化的区域选择信息 */
      const [success, data] = await getLineUsageLengthStat({
        ...this.formatSelectedRegion,
      })

      /* 初始化结果数组 */
      let res = []

      /* 当接口调用成功且返回数据非空时，处理返回的数据 */
      if (success && data && data.length) {
        /* 将数据格式化为所需对象数组 */
        res = data.map(item => {
          return {
            name: item.lineUsageName /* 线路名称 */,
            totalNum: item.totalLengthFmt /* 总长度 */,
          }
        })
      }

      /* 更新线使用数量统计信息的状态 */
      this.lineUsageNumStat = res
    },
  },
}
</script>

<style lang='less' src='./index.less' />
