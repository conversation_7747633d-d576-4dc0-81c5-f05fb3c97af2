<!--
 * @Description: 底部侧边窗口组件
 * @Autor: he.chao
 * @Date: 2022-10-19 15:07:55
 * @LastEditors: liu.yongli
 * @LastEditTime: 2023-12-13 14:36:14
-->
<template>
  <div id="bottom-side-window-ins" class="bottom-side-window"></div>
</template>

<script>
import CompCfg from '@/components/page/ScreenPages/FullViewMonitor/CompCfg'

/**
 * 底部侧边窗口组件的脚本部分。
 * 该组件用于在屏幕的底部侧边显示一系列组件。
 *
 * @export
 * @returns {Object} 组件定义对象
 */
export default {
  props: {
    componentList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    /**
     * 组件内容窗口的对象，用于存储和管理内容窗口的信息。
     *
     * @type {Object}
     */
    return {
      contentWidows: {},
      layoutCfgBottom: window.layoutCfg && window.layoutCfg.bottom || []
    }
  },
  watch: {
    // 监听 window.layoutCfg 的变化，并根据layoutCfg配置加载左侧窗口的组件
    componentList: {
      handler(newVal, oldVal) {
        console.log('componentBottom watch=================:', newVal, oldVal);
        if (newVal !== oldVal && newVal.length > 0) {
          this.initPage()
        }
      },
      deep: true
    }
  },
  mounted() {
    console.log('componentBottom mounted=================:', this.componentList)
    this.$nextTick(() => {
      console.log('componentBottom nextTick=================:', this.componentList)
      this.initPage()
      // 根据布局配置，初始化底部侧边窗口的组件
    //   layoutCfg && layoutCfg.bottom && layoutCfg.bottom.map(item => {
    //     CompCfg.getComponent({
    //       code: item.compCode,
    //       wrapper: 'bottom-side-window-ins'
    //     });
    //   });
    })
  },
  methods: {
    initPage() {
      console.log('componentBottom initPage=================:', this.componentList);
        this.componentList?.map(item => {
        // 通过CompCfg获取组件配置，并传入组件代码和容器元素
          CompCfg.getComponent({
            code: item.compCode,
            wrapper: 'bottom-side-window-ins',
            props: item || {}
          })
        })
    }
  }
}
</script>

<style lang='less' src='./index.less' scoped />
