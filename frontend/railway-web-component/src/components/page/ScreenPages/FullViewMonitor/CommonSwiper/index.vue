<!--
 * @Description:
 * @Autor: he.chao
 * @Date: 2022-10-19 15:07:55
 * @LastEditors: liu.yongli
 * @LastEditTime: 2023-12-13 14:36:14
-->
<template>
  <div class='swiper-container' :id='id'>
    <div class='swiper-wrapper'>
      <slot name='swiper-slide'></slot>
    </div>
    <!-- Add Pagination -->
    <div class='swiper-pagination'></div>
  </div>
</template>

<script>
import Swiper from 'swiper';
import 'swiper/css/swiper.min.css';

export default {
  props: {
    id: {
      type: String,
      default: 'swiper'
    },
    // 轮播间隔时间
    delay: {
      type: Number,
      default: 3000
    }
  },
  data: function() {
    return {};
  },
  mounted() {
    this.initSwiper();
  },
  methods: {
    /**
     * 初始化Swiper实例
     * 该方法用于创建或重建Swiper实例，当组件需要更新Swiper配置或重新初始化Swiper时调用。
     * 如果已存在Swiper实例，则先销毁旧实例以避免内存泄漏。
     */
    // 初始化轮播
    initSwiper() {
      // 检查是否存在当前Swiper实例，如果存在，则销毁它
      if (this.swiper) {
        this.swiper.destroy();
      }
      // 创建新的Swiper实例，使用`this.id`作为容器的唯一标识
      // 初始化轮播
      this.swiper = new Swiper(`#${this.id}`, {
        // 自动播放配置，设置播放延迟为3000毫秒，不在最后一个滑块停止播放，且在用户交互时不停止播放
        autoplay: {
          delay: 3000,
          stopOnLastSlide: false,
          disableOnInteraction: false
        },
        // 分页器配置，将分页器元素设置为'.swiper-pagination'，并使分页器点击生效
        pagination: {
          el: '.swiper-pagination',
          clickable: true,
          // 自定义分页器子弹的渲染方法
          renderBullet: function(index, className) {
            // 返回一个带有自定义类名的span元素作为分页器子弹
            return `<span class='cust-page ${className}'></span>`;
          }
        }
      });
    }
  }
};
</script>

<style lang='less' src='./index.less' />
