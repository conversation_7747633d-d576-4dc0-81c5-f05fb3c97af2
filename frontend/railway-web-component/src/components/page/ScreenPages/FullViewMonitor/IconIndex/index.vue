<template>
  <!-- 图标展示组件，用于显示特定图标的数量或比例 -->
  <div class="icon-index-wrap">
    <!-- 显示图标，使用propData中的iconUrl作为源 -->
    <img alt="图标" :src="iconUrl" class="svg-icon" :c-tip="propData.paramName" />
    <!-- 当propData.flag为"sum"时，显示数量和总数的比例 -->
    <div v-if="propData.flag === 'sum'" class="index-label">
      <span class="label">{{ propData.paramName }} 在线/总数</span>
      <div class="index">
        <span class="index-val number-font">{{ formatVal(indexVal) }}</span>
        /{{ formatVal(sum) }}
      </div>
    </div>
    <!-- 当propData.flag不为"sum"时，只显示总数 -->
    <div v-else class="index-label">
      <span class="label">{{ propData.paramName }}</span>
      <span class="index">{{ formatVal(sum) }}</span>
    </div>
  </div>
</template>

<script>
import { getIndexByCode } from '@/api/service/fullviewService'
import materialService from '@/api/service/materialService'
import { formatVal } from '@/components/common/utils'
import { mapGetters } from 'vuex'
import { indexByCodeData } from '@/components/page/ScreenPages/FullViewMonitor/mockData'

export default {
  props: {
    propData: {
      type: Object,
      default: () => ({
        flag: 'sum',
      }),
    },
  },
  data() {
    return {
      indexVal: 0, // 在线数量
      sum: 0, // 总数量
      iconUrl: '', // 图标URL
    }
  },
  computed: {
    selectedRegion() {
      // 获取选中的区域
      return this.$store.state.map.selectedRegion
    },
    ...mapGetters('map', ['formatSelectedRegion']),
  },
  watch: {
    selectedRegion: {
      handler(newVal, oldVal) {
        // 当选中区域变化时，更新指数值
        if (newVal) {
          this.getIndexVal()
        }
      },
    },
  },
  mounted() {
    // 组件挂载后，初始化指数值和图标URL
    this.getIndexVal()
    this.getIcon()
  },
  methods: {
    /**
     * 格式化值函数的说明
     * 这里应该描述formatVal函数的作用，参数和返回值
     */
    formatVal,
    /**
     * 异步获取指数值
     * 本函数通过调用API，使用选定区域和paramCode来获取在线和总数数据
     * @returns {void}
     */
    async getIndexVal() {
      // 调用API获取指数数据，其中包含了在线和总数
      const param = {
        ...this.formatSelectedRegion,
        paramCode: this.propData.paramCode,
      }
      const [, resData] = await getIndexByCode(param)
      const data = resData ?? indexByCodeData.data
      // 如果请求成功且数据有效，则更新指数值和总数
      if (data) {
        this.indexVal = data.online
        this.sum = data.total
      }
    },
    /**
     * 异步获取图标URL
     * 本函数通过调用API，将原始图标URL转换为加密的可访问URL
     * @returns {void}
     */
    async getIcon() {
      // 调用服务获取加密的文件URL
      const { code, data: fileSrcs } = await materialService.qryNewFileUrl({
        fileUrls: [this.propData.iconUrl],
      })
      // 如果请求成功且数据有效，则更新图标URL
      if (code === 200 && fileSrcs && fileSrcs.length > 0) {
        this.iconUrl = fileSrcs[0].encryptUrl
      }
    },
  },
}
</script>

<style lang='less' src='./index.less' />
