/**
 * 常量字典对象，用于存储系统中使用的各种常量值。
 * 这些常量被设计为全局唯一，不应在系统中的其他地方被修改。
 */
const DicConst = {
  /**
   * 报警状态和报警来源的常量定义。
   * 这些常量用于标识不同的报警状态和报警来源，在系统中作为枚举使用。
   */
  DIC_CODE: {
    ALARM_STATUS: 'ALARM_STATUS', // 报警状态常量
    ALARM_SOURCE: 'ALARM_SOURCE'  // 报警来源常量
  },
  /**
   * 事件来源的常量定义。
   * HUMAN 表示事件来源为人操作。
   */
  EVT_SOURCE: {
    // 「人工上报」「一键告警」「第三方告警」 
    HUMAN: "2,3,11" // 事件来源为人工上报
  },
  /**
   * 模型代码的常量定义。
   * OVERALL 表示整体模型代码。
   */
  MODEL_CODE: {
    modelCode: 'OVERALL' // 模型代码常量
  }
};
export default DicConst;
