<!--
 * @Description: 顶部侧边窗口组件
 * @Autor: he.chao
 * @Date: 2022-10-19 15:07:55
 * @LastEditors: liu.yongli
 * @LastEditTime: 2025-07-01 00:47:10
-->
<template>
  <div id='top-side-window-ins' class='top-side-window'>
  </div>
</template>

<script>
import CompCfg from '@/components/page/ScreenPages/FullViewMonitor/CompCfg'

/**
 * 顶部侧边窗口组件的定义。
 *
 * 该组件用于在页面顶部侧边显示一系列配置的组件。通过动态加载的方式，
 * 根据layoutCfg中配置的顶部侧边组件代码，来初始化相应的组件。
 */
export default {
  props: {
    componentList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      // 用于存储内容窗口的相关数据
      contentWidows: {},
      layoutCfgTop: window.layoutCfg && window.layoutCfg.top || []
    }
  },
  watch: {
    // 监听 window.layoutCfg 的变化，并根据layoutCfg配置加载左侧窗口的组件
    componentList: {
      handler(newVal, oldVal) {
        console.log('componentTop watch=================:', newVal, oldVal)
        if (newVal !== oldVal && newVal.length > 0) {
          this.initPage()
        }
      },
      deep: true
    }

  }, // 空对象表示该组件不监听任何数据变化
  mounted() {
    // 在组件挂载后，通过$nextTick确保DOM更新后，初始化顶部侧边的组件
    console.log('componentright mounted=================:', this.componentList)
    this.$nextTick(() => {
      console.log('componentright nextTick=================:', this.componentList)
      this.initPage()
    //   if (layoutCfg && layoutCfg.top) {
    //     layoutCfg.top.forEach(item => {
    //       // 根据配置的compCode获取组件配置，并在指定的wrapper中初始化该组件
    //       CompCfg.getComponent({
    //         code: item.compCode,
    //         wrapper: 'top-side-window-ins'
    //       });
    //     });
    //   }
    })
  },
  methods: {
    // 方法区，目前尚未配置具体方法
    initPage() {
      console.log('componentList initPage=================:', this.componentList)
        this.componentList?.map(item => {
        // 通过CompCfg获取组件配置，并传入组件代码和容器元素
          CompCfg.getComponent({
            code: item.compCode,
            wrapper: 'top-side-window-ins',
            props: item || {}
          })
        })
    }
  }
}
</script>

<style lang='less' src='./index.less' />
