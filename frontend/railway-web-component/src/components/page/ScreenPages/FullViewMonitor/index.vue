<template>
  <!-- 监控视图容器，全屏显示地图及相关控件 -->
  <div class="view-monitor-wrap" v-loading="sysLoading">
    <div class="preload" />
    <!--
      showMouseLocation 右下角显示当前鼠标经纬度位置，默认不开启
      showMapControl 初始化完成后，是否在右下角显示地图切换、比例尺工具。默认开启
      tileModes 定义可供切换的地图类型。0、1、2、3 分别代表 常规地图、卫星地图、三维地形图、二维地形图。默认启用全部
    -->
    <RemoteComponentSyncLoader
      v-if="components['common-comp-map']"
      showMapControl
      mapToolElement=".view-monitor-wrap .maptooltele"
      :class="['main-map' ]"
      chooseMapMemoryKey
      :config="components['common-comp-map']"
      :mapId="mapId"
      :defaultTileMode="5"
      :tileModes="tileModes"
    />
    <!-- 主地图容器 -->
    <!-- <div id="mainMap" class="main-map" ref="mainMap"></div> -->
    <!-- 地图控制组件 -->
    <!-- 通过 common-comp-map 组件自动生成 -->
    <div :class="['maptooltele', 'fix-map-tool']" />
    <!-- 当地图加载完毕且系统加载完成时，显示以下内容  -->
    <template v-if="mapComLoaded && mapLoaded && !sysLoading">
      <!-- 地图类型切换 -->
      <ChangeRailwayType :mapId="mapId" :tileModes="tileModes" pageType="FullViewMonitor" />
      <!-- 地图组件，用于加载地图功能 -->
      <Map :mapId="mapId" :onMapLoad="onMapLoad" />
      <template v-if="loaded && cityDataLoaded">
        <!-- 顶部窗口控件容器 -->
        <WindowWrapTopSide
          v-if="layoutCfgStatus.top"
          ref="topWrap"
          :componentList="componentConfig.top"
        />
        <!-- 左侧窗口控件容器 -->
        <WindowWrapLeftSide
          v-if="layoutCfgStatus.left"
          ref="leftWrap"
          :componentList="componentConfig.left"
          :leftRightComponents="leftRightComponents"
        />
        <!-- 右侧窗口控件容器，包含过滤器、区域选择和地图工具 -->
        <WindowWrapRightSide
          ref="rightWrap"
          :componentList="componentConfig.right"
          :leftRightComponents="leftRightComponents"
        >
          <template v-slot:ctls>
            <!-- 过滤器，根据权限开关显示 -->
            <!-- <Filters v-if="$store.state.map.evtFilterSwitch" /> -->
            <EventFilterDialog v-if="$store.state.map.evtFilterSwitch" class="event-filter-wrap" />
            <!-- 区域选择，用于切换显示区域 -->
            <RegionSelect :mapId="mapId" :onRegionChange="onRegionChange" />
            <!-- 地图工具，提供地图操作功能 -->
            <!-- <MapTool useType="fullView" /> -->
            <!-- 图例，显示地图图例 -->
            <Legend screenType="fullView" class="legend-wrap" />
          </template>
        </WindowWrapRightSide>
        <!-- 底部窗口控件容器 -->
        <WindowWrapBottomSide
          v-if="layoutCfgStatus.bottom"
          ref="bottomWrap"
          :componentList="componentConfig.bottom"
        />
        <!-- 事件图层，用于显示事件标记 -->
        <EvtLayer :mapId="mapId" />
        <!-- 热力图层，用于显示热度分布 -->
        <HeatMapLayer :mapId="mapId" />
      </template>
    </template>
  </div>
</template>
<script>
import EventFilterDialog from '@/components/page/ScreenPages/IntegratedMonitor/EventFilter/EventFilterDialog' // 事件过滤器
import RemoteComponentSyncLoader from '@ct/remote-page-sync-loader/remote-page-sync-loader.umd.js' // 远程组件同步加载器
import { parseQueryString } from '@/common/parse-qs.js' // 解析地址栏参数
import { getInfo, getFisUrl, getBusinessFilesAsync } from '@/utils/index.js' // 获取用户信息
import { getComponentLayout, supplyRegionTree } from '@/api/service/common' // 获取布局配置
import ConstEnum from '@/components/common/ConstEnum' // 常量枚举
import { getUserLocalStorage } from '@/components/common/utils' // 获取用户本地存储
// import DicConst from '@/components/page/ScreenPages/FullViewMonitor/enums/DicConst' // 字典常量
import Filters from '@/components/page/ScreenPages/FullViewMonitor/Filters' // 事件过滤器
import ManageAreaInfo from '@/components/page/ScreenPages/FullViewMonitor/ManageAreaInfo' // 管理区域信息
import Map from '@/components/page/ScreenPages/FullViewMonitor/Map' // 地图
import EvtLayer from '@/components/page/ScreenPages/FullViewMonitor/Map/MapLayers/EvtLayer.vue' // 事件图层
import HeatMapLayer from '@/components/page/ScreenPages/FullViewMonitor/Map/MapLayers/HeatMapLayer.vue' // 热力图层
import WindowWrapBottomSide from '@/components/page/ScreenPages/FullViewMonitor/WindowWrapBottomSide' // 底部窗口控件
import WindowWrapLeftSide from '@/components/page/ScreenPages/FullViewMonitor/WindowWrapLeftSide' // 左侧窗口控件
import WindowWrapRightSide from '@/components/page/ScreenPages/FullViewMonitor/WindowWrapRightSide' // 右侧窗口控件
import WindowWrapTopSide from '@/components/page/ScreenPages/FullViewMonitor/WindowWrapTopSide' // 顶部窗口控件
import { CfgEnum } from '@/components/page/ScreenPages/IntegratedMonitor/enum/CfgEnum' // 配置枚举
import MapTool from '@/components/page/ScreenPages/IntegratedMonitor/Map/MapTool' // 地图工具
import RegionSelect from '@/components/page/ScreenPages/IntegratedMonitor/Map/RegionSelect' // 区域选择
import Legend from '@/components/common/legend.vue' // 图例
import { mapMutations, mapActions } from 'vuex' // vuex
import ClearVideo from '@/utils/mixins/clearVideo' // 清除视频
import api from '@/api'
import ss from '@/components/page/ScreenPages/IntegratedMonitor/Map/MapTool/img/2D-dark-map.png'
import cg from '@/components/page/ScreenPages/IntegratedMonitor/Map/MapTool/img/2D-map.jpeg'
import wx from '@/components/page/ScreenPages/IntegratedMonitor/Map/MapTool/img/satellite-map.jpeg'
import ChangeRailwayType from '@/components/common/Map/ChangeRailwayType'
import { getMapTiles } from '@/api/service/common' // 导入获取地图瓦片的服务
import { setMapService } from '@/utils/common'

// 使用到的远程组件定义
// key是组件内部名称，应当与uniqueId指代的具体组件完全对应，用于维护者自己区分和填写在RemoteComponentSyncLoader内
const remoteComp = {
  'common-comp-map': { uniqueId: '2024042465120', version: '1.7.65' },
}

export default {
  inject: ['mapFlag', 'mapRef'], // 注入mapFlag和mapRef
  mixins: [ClearVideo], // mixins
  components: {
    // 注册组件
    RegionSelect,
    MapTool,
    Map,
    WindowWrapLeftSide,
    WindowWrapRightSide,
    WindowWrapTopSide,
    WindowWrapBottomSide,
    ManageAreaInfo,
    Filters,
    EvtLayer,
    HeatMapLayer,
    Legend,
    RemoteComponentSyncLoader,
    EventFilterDialog,
    ChangeRailwayType,
  },
  data() {
    return {
      tileModes: [
        {
          modeId: 5,
          name: '深色地图',
          mapType: '2D',
          imgUrl: ss,
          tileType: 'vector',
          layerUrls: [
            {
              url: '',
              // url: 'http://10.43.82.110:9239/geoserver/railway_space/gwc/service/wmts?layer=railway_space:railway-project-dark&style=&tilematrixset=WebMercatorQuad&Service=WMTS&Request=GetTile&Version=1.0.0&Format=image/png&TileMatrix={z}&TileCol={x}&TileRow={y}',
              // url: 'http://webst01.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}',
            },
          ],
        },
        {
          modeId: 1,
          name: '卫星地图',
          imgUrl: wx,
          tileType: 'satellite',
          mapType: '2D',
        },
        {
          modeId: 6,
          name: '常规地图',
          imgUrl: cg,
          tileType: 'vector',
          mapType: '2D',
          layerUrls: [
            {
              url: '',
              // url:  'http://10.43.82.110:9239/geoserver/railway_space/gwc/service/wmts?layer=railway_space:railway-project&style=&tilematrixset=WebMercatorQuad&Service=WMTS&Request=GetTile&Version=1.0.0&Format=image/png&TileMatrix={z}&TileCol={x}&TileRow={y}',
            },
          ],
        },
      ],
      // 地图是否加载完成
      mapLoaded: false,
      // 地图是否加载完成
      mapComLoaded: false,
      // 用户省市数据
      cityDataLoaded: false,
      // 系统加载状态
      sysLoading: true,
      // 组件配置状态
      layoutCfgStatus: {
        left: false,
        top: false,
        bottom: false,
      },
      layoutJsonObjLoad: false, // 布局配置加载完成
      mapId: 'mainMap',
      userInfo: null,
      // customLayers: [
      //   {
      //     id: 4,
      //     footerBtns: [
      //       {
      //         key: 'fxkz', // 飞行控制
      //       },
      //       {
      //         key: 'zdfx', // 指点飞行
      //       },
      //       {
      //         key: 'sssp', // 实时视频
      //       },
      //     ],
      //   },
      // ], // 默认图层 1摄像机和2无人机
      // initedFlyComp: false, // 指点飞行
      // flycomponents: {}, // 指点飞行配置项
      mapInitFlag: this.mapFlag(), // 初始化函数返回值
      loaded: false, // 是否加载完成
      components: {}, // 远程组件使用的配置项们
      defaultComponentList: {
        top: [{ compName: '设备统计', compCode: 'DEVICE_STAT', seq: 1 }],
        left: [
          {
            compName: '辖区概况统计',
            compCode: 'JURISDICTION_OVERVIEW_STAT',
            seq: 1,
          },
          { compName: '站点信息', compCode: 'STATION_INFO', seq: 2 },
        ],
        bottom: [{ compName: '设施统计', compCode: 'FACILITY_STAT', seq: 1 }],
        right: [
          { compName: '事件总览', compCode: 'EVENT_OVERVIEW_STAT', seq: 1 },
          {
            compName: '事件高发地点top5',
            compCode: 'EVENT_AREA_STAT_TOP5',
            seq: 2,
          },
          {
            compName: '自动化处置分析',
            compCode: 'AUTO_HANDLE_ANALYSIS',
            seq: 3,
          },
        ],
        map: [{ compName: '地图组件', compCode: 'MAP_ADDR' }],
      },
      leftHide: false, // 左侧隐藏状态
      leftRightComponents: {}, // 左右两侧的组件对象
      // 组件配置
      componentConfigTemp: {
        top: [],
        left: [],
        right: [],
        bottom: [],
      },
      // 组件配置
      componentConfig: {
        top: [],
        left: [],
        right: [],
        bottom: [],
      },
      fisConfig: null, // 配置信息
    }
  },
  watch: {
    // 地图是否加载完成
    mapInitFlag: {
      handler(val) {
        if (val && !this.mapComLoaded) {
          // 地图实例
          this.setMapStoreRef(this.mapRef.getMapRef(this.mapId))
          this.mapLoaded = true
          this.mapComLoaded = true
          // 指点飞行
          // if (!this.initedFlyComp) {
          //   this.getFlyComp()
          // }
          // 初始化页面上悬浮面板方法
          this.initcom()
        }
      },
      immediate: true,
    },
  },
  created() {
    // 监听基本类型（通过函数）
    this.$watch(
      () => this.mapFlag(), // 监听函数返回值
      newVal => {
        this.mapInitFlag = newVal
      }
    )
    // 保存地址栏 VIEW_ID
    const qsObj = parseQueryString(window.location.search)
    sessionStorage.setItem('VIEW_ID', qsObj.viewId) // 测试id '1001685'
    // 视频初期偏移量
    window._remoteMetadata = window._remoteMetadata || {} // 视频偏移量
    const videoPositionRight = Math.ceil((window.innerHeight / 1032) * 100 * 5) // 5%
    window._remoteMetadata.videoPositionRight = videoPositionRight // 视频初始偏移量
  },
  async mounted() {
    try {
      // 导入获取地图瓦片的服务
      await getMapTiles()
      // 设置地图瓦片图层
      this.tileModes = setMapService(this.tileModes)
    } catch {
      console.log('地图瓦片服务加载失败')
    }

    const _info = await getInfo() // 获取用户信息
    this.userInfo = _info?.user || {} // 用户信息
    const { data } = await getFisUrl() // 获取配置信息
    const { configValue } = data || {} // 配置信息
    this.fisConfig = configValue
    console.log('当前配置远程域名为---：', configValue)
    // 获取布局配置
    const { components, loaded } = await this.getRemoteComps(
      configValue,
      getBusinessFilesAsync
    )
    this.components = components // 远程组件配置项
    this.loaded = loaded // 是否加载完成
    this.changeTheme() // 初始化主题
  },
  methods: {
    ...mapMutations('map', ['setSelectionRange', 'setMapStoreRef']), // vuex mutations
    // mapActions
    ...mapActions('event', [
      'fetchEventSourceData',
      'fetchEventData',
      'fetchOrderStatusData',
    ]),
    /**
     * 初始化页面上悬浮面板组件需要的方法
     */
    async initcom() {
      // 事件类型
      this.fetchEventData()
      // 事件来源
      this.fetchEventSourceData()
      // 查询处置状态
      this.fetchOrderStatusData()
      // 初始化系统配置
      await this.iniSysConfig()
    },
    /**
     * 初始化用户省市数据
     * @async
     */
    async onMapLoad(map) {
      // 初始化用户省市数据
      await this.initUserProvinceCityData()
      this.cityDataLoaded = true // 用户省市数据加载完成
      if (!this.layoutJsonObjLoad) {
        // 遍历组件配置的左右组件信息，查询组件的配置信息
        this.getCompDetailConfig()
      }

      this.$nextTick(() => {
        // 将窗口控件引用赋值给全局变量，以便于地图控件直接调用
        window.leftWrap = this.$refs.leftWrap // 左侧窗口控件
        window.rightWrap = this.$refs.rightWrap // 右侧窗口控件
        window.topWrap = this.$refs.topWrap // 顶部窗口控件
        window.bottomWrap = this.$refs.bottomWrap // 底部窗口控件
      })
    },
    /**
     * 初始化用户省市数据
     * @async
     * @returns {Promise<void>}
     */
    async initUserProvinceCityData() {
      // 获取用户信息
      // const { user } = await getUserInfo()
      const user = this.userInfo
      // 补充区域树数据
      await supplyRegionTree(user)
      window.userInfo = user
      // 根据用户权限查询省市数据
      if (!user.areaTree || user.areaTree.length < 1) {
        this.$message.error('查询用户权限城市失败！')
      }
      // 处理用户区域树数据
      if (user.areaTree[0].id === '100000') {
        user.areaTree = user.areaTree[0].children
      }
      // 设置默认区域
      let regionLevel = ConstEnum.REGION_LEVEL.province
      let regionCode = ''
      // 判断用户区域树数据，设置默认区域
      if (user.areaTree[0].children[0].children.length < 2) {
        regionLevel = ConstEnum.REGION_LEVEL.district // 区
        regionCode = user.areaTree[0].children[0].children[0].id // 区
        // 设置默认区域
        window.userInfo.defArea = [
          user.areaTree[0].id,
          user.areaTree[0].children[0].id,
          user.areaTree[0].children[0].children[0].id,
        ]
        // 设置默认区域名称
        window.userInfo.defAreaName =
          user.areaTree[0].children[0].children[0].label
      } else if (user.areaTree[0].children.length < 2) {
        // 市
        regionLevel = ConstEnum.REGION_LEVEL.city
        regionCode = user.areaTree[0].children[0].id
        // 设置默认区域
        window.userInfo.defArea = [
          user.areaTree[0].id,
          user.areaTree[0].children[0].id,
        ]
        // 设置默认区域名称
        window.userInfo.defAreaName = user.areaTree[0].children[0].label
      } else {
        // 省
        regionLevel = ConstEnum.REGION_LEVEL.province
        regionCode = user.areaTree[0].id
        window.userInfo.defArea = [user.areaTree[0].id]
        window.userInfo.defAreaName = user.areaTree[0].label
      }
      // 用户缓存数据初始化用户选择区域
      const userHandleCache = getUserLocalStorage(
        CfgEnum.STORAGE_KEY.SELECT_REGION
      )
      // 获取用户缓存数据
      if (userHandleCache) {
        const {
          nodes,
          cityName,
          regionLevel: cacheRegionLevel,
          regionCode: cacheRegionCode,
        } = userHandleCache
        // 设置默认区域
        window.userInfo.defArea = nodes
        window.userInfo.defAreaName = cityName
        regionCode = cacheRegionCode
        regionLevel = cacheRegionLevel
      }
      // 设置选择范围
      this.setSelectionRange({
        regionCode,
        regionLevel,
      })
      // 处理用户区域树数据，用于区域选择控件
      let addressTemp = []
      // 用户区域树数据
      user.areaTree.forEach(item => {
        // 数据开始
        item.value = item.id
        item.regionLevel = ConstEnum.REGION_LEVEL.province
        // 子节点
        if (item.children && item.children.length > 0) {
          let sonLevelList = []
          // 处理子节点
          item.children.forEach(secondChild => {
            secondChild.value = secondChild.id
            secondChild.regionLevel = ConstEnum.REGION_LEVEL.city
            sonLevelList.push(secondChild)
            // 节点
            if (secondChild.children && secondChild.children.length > 0) {
              let thirdLevelList = []
              secondChild.children.forEach(thirdChild => {
                thirdChild.value = thirdChild.id
                delete thirdChild.children
                thirdChild.regionLevel = ConstEnum.REGION_LEVEL.district
                thirdLevelList.push(thirdChild)
              })
              secondChild.children = thirdLevelList
              secondChild.disabled = thirdLevelList.length < 2
            }
          })
          // 子节点
          item.children = sonLevelList
          item.disabled = sonLevelList.length < 2
        }
        // 数据结束
        addressTemp.push(item)
      })
      // 设置用户区域树数据
      window.userInfo.roleAreaTree = addressTemp
    },
    /**
     * 初始化系统配置
     * @async
     * @returns {Promise<void>}
     */
    async iniSysConfig() {
      // 输出性能日志
      console.log('performance iniSysConfig start', new Date().getTime())
      let layoutJsonObj = {}
      // 获取布局配置 sessionStorage.getItem('VIEW_ID')
      const [success, data] = await getComponentLayout({
        modelCode: sessionStorage.getItem('VIEW_ID'), // 模型代码常量 '1001676' '487'
        isDefault: 'Y',
      })
      console.log(
        'performance iniSysConfig result',
        new Date().getTime(),
        success,
        data
      )
      if (!success || !data || !data.layoutJsonObj) {
        console.error('!!!!系统组件配置信息获取失败')
        layoutJsonObj = {
          top: [{ compName: '设备统计', compCode: 'DEVICE_STAT', seq: 1 }],
          left: [
            {
              compName: '辖区概况统计',
              compCode: 'JURISDICTION_OVERVIEW_STAT',
              seq: 1,
            },
            { compName: '站点信息', compCode: 'STATION_INFO', seq: 2 },
          ],
          bottom: [{ compName: '设施统计', compCode: 'FACILITY_STAT', seq: 1 }],
          right: [
            { compName: '事件总览', compCode: 'EVENT_OVERVIEW_STAT', seq: 1 },
            {
              compName: '事件高发地点top5',
              compCode: 'EVENT_AREA_STAT_TOP5',
              seq: 2,
            },
            {
              compName: '自动化处置分析',
              compCode: 'AUTO_HANDLE_ANALYSIS',
              seq: 3,
            },
          ],
          map: [{ compName: '地图组件', compCode: 'MAP_ADDR' }],
        }
        // this.getConfigComponent(this.defaultComponentList)
        window.layoutCfg = layoutJsonObj
        this.componentConfig = layoutJsonObj
        this.layoutJsonObjLoad = true
      } else {
        layoutJsonObj = data.layoutJsonObj
        this.componentConfigTemp = data.layoutJsonObj
        // window.layoutCfg = this.defaultComponentList
      }

      this.layoutCfgStatus = {
        top: layoutJsonObj && layoutJsonObj.top && layoutJsonObj.top.length > 0,
        left:
          layoutJsonObj && layoutJsonObj.left && layoutJsonObj.left.length > 0,
        bottom:
          layoutJsonObj &&
          layoutJsonObj.bottom &&
          layoutJsonObj.bottom.length > 0,
      }
      console.info(
        'full view layoutCfgStatus===============',
        this.layoutCfgStatus
      )
      this.sysLoading = false
    },
    // 查询组件的详情配置
    async getCompDetailConfig() {
      const compCodeArr = []
      this.componentConfigTemp.left.forEach(item => {
        compCodeArr.push(
          api.get(
            `${this.$env.VUE_APP_REQ_PREFIX_BIZ}/adsLayoutThemeComp/getThemeCompByCode`, // 接口地址
            {
              compCode: item.compCode, // 模型代码常量
            }
          )
        )
      })
      this.componentConfigTemp.right.forEach(item => {
        compCodeArr.push(
          api.get(
            `${this.$env.VUE_APP_REQ_PREFIX_BIZ}/adsLayoutThemeComp/getThemeCompByCode`, // 接口地址
            {
              compCode: item.compCode, // 模型代码常量
            }
          )
        )
      })

      const res = await Promise.all(compCodeArr)
      if (res.length < 1) {
        console.error('!!!!系统配置信息获取失败')
        return
      }
      const componentList = {}
      // this.componentConfig.left.forEach(item => {
      //   componentList[item.compCode] = { uniqueId: item.id, version: item.version }
      // })
      // this.componentConfig.right.forEach(item => {
      //   componentList[item.compCode] = { uniqueId: item.id, version: item.version }
      // })
      console.info('res------', res)
      res.forEach((item, index) => {
        if (item.code === 200 && item.data) {
          componentList[item.data.compCode] = {
            uniqueId: item.data.compParamList?.find(
              item2 => item2.paramName === 'comp_id'
            )?.paramCode,
            version: item.data.compParamList?.find(
              item2 => item2.paramName === 'comp_version'
            )?.paramCode,
          }
          // 如果是内嵌式组件，需要把内嵌属性和compUrl赋值给 componentConfig
          if (this.componentConfigTemp.left.length > index) {
            this.componentConfigTemp.left[index].compUrl = item.data.compUrl
            this.componentConfigTemp.left[index].showTech = item.data.showTech
            this.componentConfigTemp.left[index].defaultWidth =
              item.data.defaultWidth
            this.componentConfigTemp.left[index].defaultHeight =
              item.data.defaultHeight
            // 如果是自带组件
            this.componentConfigTemp.left[index].monitorComp =
              item.data.compParamList?.find(
                item2 => item2.paramName === 'comp_id'
              )?.paramCode
          } else {
            this.componentConfigTemp.right[
              index - this.componentConfigTemp.left.length
            ].compUrl = item.data.compUrl
            this.componentConfigTemp.right[
              index - this.componentConfigTemp.left.length
            ].showTech = item.data.showTech
            this.componentConfigTemp.right[
              index - this.componentConfigTemp.left.length
            ].defaultWidth = item.data.defaultWidth
            this.componentConfigTemp.right[
              index - this.componentConfigTemp.left.length
            ].defaultHeight = item.data.defaultHeight
            // 如果是自带组件
            this.componentConfigTemp.right[
              index - this.componentConfigTemp.left.length
            ].monitorComp = item.data.compParamList?.find(
              item2 => item2.paramName === 'comp_id'
            )?.paramCode
          }
        }
      })

      window.layoutCfg = this.componentConfig
      // 删除组件componentList中不是内嵌组件，而且uniqueId和version为空，则删除该组件
      const keys = Object.keys(componentList)
      keys.forEach(key => {
        if (
          componentList[key].uniqueId == null &&
          componentList[key].version == null
        ) {
          delete componentList[key]
        }
      })
      // console.info(
      //   '当前配置的组件列表------',
      //   componentList,
      //   this.componentConfig
      // )
      await this.getConfigComponent(componentList)
      this.componentConfig = this.componentConfigTemp
    },
    // 获取配置的组件
    async getConfigComponent(componentList) {
      console.info(
        'getConfigComponent------',
        componentList,
        this.componentConfig
      )
      let configValue = this.fisConfig
      if (!this.fisConfig) {
        const { data } = await getFisUrl()
        configValue = data || {}
        this.fisConfig = configValue
      }
      const param = {
        components: Object.values(componentList),
      }
      let _configValue = configValue
      // 如果是一个对象时
      if (typeof configValue === 'object' && configValue !== null) {
        _configValue = configValue.configValue
      }
      const { components, loaded } = await getBusinessFilesAsync(
        _configValue,
        param
      )
      if (loaded) {
        this.leftRightComponents = components
        // setTimeout(() => {
        //   this.$forceUpdate()
        // }, 10000)
      }
    },
    /**
     * 区域改变时的处理
     * @param {Object} region 改变后的区域信息
     */
    onRegionChange(region) {
      // 设置选择范围
      this.setSelectionRange(region)
    },

    /**
     * 获取远程组件
     * 配置项
     */
    async getRemoteComps(configValue, callback) {
      const param = {
        components: Object.values(remoteComp),
      }
      const { components, loaded } = await callback(configValue, param)
      return { components, loaded }
    },
    /**
     * 切换主题
     * 行业主题
     */
    changeTheme() {
      document.documentElement.setAttribute('data-theme', `theme-wiseblue`)
      this.$globalEventBus.$emit('data-theme', 'theme-wiseblue')
      console.log('通用', 2)
    },
  },
}
</script>
<style lang="scss" scoped src="./index.scss" />
<style lang="scss" scoped>
@import '~@/assets/styles/px-to-rem';

// preload元素是一个不可见元素，它的目的就是用来预加载一些资源
.preload {
  background: url('~@/assets/images/common/left_show.png') no-repeat left
      center/100% 100%,
    url('~@/assets/images/common/right_show.png') no-repeat left center/100%
      100%;
}
.view-monitor-wrap {
  position: relative;
  width: 100%;
  height: 100%;

  // ::v-deep {
  //   i[c-tip~='到这里'],
  //   .uav-tree-dialog-footer:has(.icon-tongyong_icon_daozheli_s_30),
  //   .footerIconArea:has(.icon-icon_daozheli_30_n),
  //   .icon-icon_guanlian_20_n,
  //   .footerIconArea:has(.icon-icon_zhoubianfenxi_30_n),
  //   .footer .f_cont .icon-icon_zhoubianfenxi_30_n {
  //     display: none !important;
  //   }
  // }
  ::v-deep .common-comp-alarm-detail {
    .alarm-info-box.common-comp-alarm-detail-module {
      z-index: 100 !important;
    }
  }
  ::v-deep .common-comp-footer__footer_func_area {
    z-index: 1;
  }

  .legend-wrap {
    bottom: px-to-rem(150) !important;
  }

  .maptooltele {
    width: 100%;
    height: 100%;
    pointer-events: none;
    position: relative;
    z-index: 2;
    ::v-deep .map-tools {
      pointer-events: all;
      transition: all 0.53s;
      position: absolute;
      bottom: 0;
      right: px-to-rem(375) !important;

      .tile-control,
      .compass-tool,
      .zoom-tool,
      .scale-line {
        z-index: 0;
      }

      .tile-control {
        position: absolute;
        right: px-to-rem(56);
        bottom: px-to-rem(68);

        .ctmap-union-layer-switcher {
          width: px-to-rem(88) !important;

          &:hover {
            // px-to-rem(339)
            width: px-to-rem(256) !important;
          }
        }

        // .ctmap-union-layer-switcher__layerlist {
        //   // 修改常规
        //   // 设置图层控制中替换为深色地图样式
        //   div[data-index='0'] {
        //     background-image: url('../IntegratedMonitor/Map/MapTool/img/2D-dark-map.png') !important;
        //     background-repeat: no-repeat;

        //     .map-tile-name::before {
        //       content: '深色地图';
        //       position: absolute;
        //       background: #fff;
        //     }
        //   }
        //   // 悬浮时的样式
        //   div[data-index='0'].map-tile-item__active,
        //   div[data-index='0']:hover {
        //     .map-tile-name::before {
        //       content: '深色地图';
        //       position: absolute;
        //       color: #ffffff !important;
        //       background: #1373e6 !important;
        //     }
        //   }

        //   // 修改地形
        //   // 设置图层控制中替换为深色地图样式
        //   div[data-index='2'] {
        //     background-image: url('../IntegratedMonitor/Map/MapTool/img/2D-map.jpeg') !important;
        //     background-repeat: no-repeat;

        //     .map-tile-name::before {
        //       content: '常规地图';
        //       position: absolute;
        //       background: #fff;
        //     }
        //   }
        //   // 悬浮时的样式
        //   div[data-index='2'].map-tile-item__active,
        //   div[data-index='2']:hover {
        //     .map-tile-name::before {
        //       content: '常规地图';
        //       position: absolute;
        //       color: #ffffff !important;
        //       background: #1373e6 !important;
        //     }
        //   }

        //   .map-tile-name {
        //     color: #172537;
        //   }

        //   .map-tile-item:hover,
        //   .map-tile-item__active {
        //     .map-tile-name {
        //       color: #e8f3fe;
        //     }
        //   }
        // }
      }
    }
    // &.fix-map-tool {
    //   ::v-deep .map-tools {
    //     // transition: all 0.53s;
    //     // right: px-to-rem(383) !important; // 用于控制地图控制工具的位置
    //     right: px-to-rem(395) !important;
    //   }
    // }
  }
  .tool-box {
    top: px-to-rem(48);
    right: px-to-rem(18);
    transition: all 0.3s;
    &.fix-map-tool {
      right: px-to-rem(416);
    }

    // :deep {
    //   // .tool-box-trigger,
    //   // .tool-box-trigger__icon {
    //   //   display: none;
    //   //   // background-image: url('../../assets/images/common/icon_gongju.png') !important;
    //   // }
    // }
  }

  .mapsearch {
    position: absolute;
    top: px-to-rem(48);
    left: px-to-rem(416);
    transition: all 0.3s;
    z-index: 99; // 左wrapper为1
    &.fix-mapsearch {
      left: px-to-rem(24);
    }
  }

  .chartmodule {
    + .chartmodule {
      margin-top: px-to-rem(12);
    }
  }

  .aroundanalysis {
    top: px-to-rem(88);
    right: px-to-rem(481);
    &.hide {
      right: px-to-rem(83);
    }
  }

  .tree-wrapper {
    position: absolute;
    flex: 1;
    top: px-to-rem(108);
    left: px-to-rem(24);
    height: calc(100% - px-to-rem(182));
  }
  // 告警筛选定位
  ::v-deep .alarm-filte {
    top: px-to-rem(94);
    left: calc(50% - px-to-rem(438));
    .common-iw-s.el-picker-panel.el-popper[x-placement^='bottom'] {
      margin-top: px-to-rem(12);
    }
  }

  .event-filter-wrap {
    top: 90px;
    right: 300px;
  }
}
// .top-background {
//   width: 100%;
//   height: px-to-rem(200);
//   top: px-to-rem(-51);
//   transition: all 0.5s;
//   pointer-events: none;
//   position: absolute;
//   display: flex;
//   justify-content: center;
//   background: url('~@/assets/images/common/dataCenterTopBg.png') no-repeat 0 0 /
//     100% px-to-rem(166);
// }

// .searchmapp {
//   &.navipotision {
//     ::v-deep .d-search-map__navigation-popup__wrapper {
//       top: 0;
//       right: px-to-rem(-258);
//     }
//   }
// }

// .toolsubdialog {
//   ::v-deep .SpotSearch,
//   ::v-deep .SpotSpace,
//   ::v-deep .map-swiper-container {
//     top: px-to-rem(107);
//     right: px-to-rem(475);
//   }

//   &.hide {
//     ::v-deep .SpotSearch,
//     ::v-deep .SpotSpace,
//     ::v-deep .map-swiper-container {
//       right: px-to-rem(85);
//     }
//   }

//   &.max-width {
//     ::v-deep .tool_spot_detail {
//       .innercomp-abcontainer {
//         width: calc(100vw - px-to-rem(65)) !important;
//       }
//     }
//   }
//   &.min-width {
//     ::v-deep .tool_spot_detail {
//       .innercomp-abcontainer {
//         width: calc(100vw - px-to-rem(865)) !important;
//       }
//     }
//   }
//   &.hide-left-width {
//     ::v-deep .tool_spot_detail {
//       .innercomp-abcontainer {
//         width: calc(100vw - px-to-rem(475)) !important;
//       }
//     }
//   }
//   &.hide-right-width {
//     ::v-deep .tool_spot_detail {
//       .innercomp-abcontainer {
//         width: calc(100vw - px-to-rem(465)) !important;
//       }
//     }
//   }
//   ::v-deep .tool_spot_detail {
//     .innercomp-abcontainer {
//       position: fixed;
//     }
//   }
// }
</style>