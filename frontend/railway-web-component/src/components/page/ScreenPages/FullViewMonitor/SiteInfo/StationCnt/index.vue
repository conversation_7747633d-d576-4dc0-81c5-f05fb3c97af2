<template>
  <!-- 辖区站点模块，用于显示辖区内的站点总数 -->
  <div class='station-cnt-index'>
    <!-- 显示模块标题 -->
    <span class='index-label'>
      辖区站点
    </span>
    <!-- 显示站点总数及其单位 -->
    <div class='index-wrap'>
      <div class='index-value number-font'>
        <!-- 使用formatVal方法格式化站点总数 -->
        {{ formatVal(totalNum) }}
      </div>
      <div class='index-unit'>
        个
      </div>
    </div>
  </div>
</template>

<script>
import { formatVal } from '@/components/common/utils';

export default {
  props: {
    /**
     * 总站点数，是一个数字类型
     * @type {Number}
     * @default 0
     */
    totalNum: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {};
  },
  mounted() {
    // 组件挂载后的逻辑，此处为空
  },
  methods: {
    /**
     * 格式化数值显示
     * @param {Number} val - 需要格式化的数值
     * @return {String} - 格式化后的字符串
     */
    formatVal
  }
};
</script>

<style lang='less' src='./index.less' />
