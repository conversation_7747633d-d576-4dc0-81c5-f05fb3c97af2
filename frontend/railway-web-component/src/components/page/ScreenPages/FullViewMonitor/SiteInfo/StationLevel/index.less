.station-level-list-wrap {
  width: 100%;
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: #FFFFFF;
  letter-spacing: 0;
  font-weight: 400;

  > div {
    margin-top: 6px;
  }

  .list-header {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-start;

    > span {
      display: inline-block;
      height: 24px;
      line-height: 24px;
      font-family: PingFangSC-Regular;
      font-size: 14px;
      color: #0CE6FF;
      letter-spacing: 0;
      font-weight: 400;
      background-image: linear-gradient(45deg, #184168, transparent);
    }
  }

  .list-content {
    width: 100%;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: flex-start;

    .list-item {
      width: 100%;
      height: 28px;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      background: url("./img/bg_list_item.png") 100% 100% no-repeat;
      background-size: 100% 100%;

      &:not(:first-child) {
        margin-top: 6px;
      }

      .column-index {
        background: url("./img/bg_top3.png") 100% 100% no-repeat;
        background-size: 100% 100%;
      }

      &:first-child .column-index {
        background: url("./img/bg_top1.png") 100% 100% no-repeat;
        background-size: 100% 100%;
      }

      &:nth-child(2) .column-index {
        background: url("./img/bg_top2.png") 100% 100% no-repeat;
        background-size: 100% 100%;
      }
    }
  }


}

.station-level-wrap {
  .swiper-container {
    width: 100%;
    height: 100%;
  }
}

.over-flow-hidden {
  white-space: nowrap; //强制文本在一行内输出
  overflow: hidden; //隐藏溢出部分
  text-overflow: ellipsis; //对溢出部分加上...
}
