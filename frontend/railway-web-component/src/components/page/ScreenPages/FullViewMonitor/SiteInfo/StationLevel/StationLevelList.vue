<template>
  <!-- 站点级别列表包装元素 -->
  <div class='station-level-list-wrap'>
    <!-- 列表头部，包含各列标题 -->
    <div class='list-header'>
      <!-- 遍历列定义，生成列标题 -->
      <template v-for='(column, index) in columns'>
        <!-- 特殊处理序号列 -->
        <span v-if='column.dataIndex === "index"'
              :style='`width: ${column.width};text-align: ${column.align};`'
        >
          序号
        </span>
        <!-- 处理非序号列 -->
        <span v-else
              :style='`width: ${column.width};text-align: ${column.align};`'
        >
          {{ column.title }}
        </span>
      </template>
    </div>
    <!-- 列表内容区域 -->
    <div class='list-content'>
      <!-- 遍历数据项，生成每一行 -->
      <div :key='`item-${dsIndex}`' class='list-item' v-for='(data, dsIndex) in ds'>
        <!-- 遍历列定义，生成每一列 -->
        <template v-for='(column, index) in columns'>
          <!-- 特殊处理序号列 -->
          <span v-if='column.dataIndex === "index"'
                :key='`column-${index}`'
                class='column-index column'
                :style='`width: ${column.width};text-align: ${column.align};`'>
            {{ dsIndex + 1 }}
          </span>
          <!-- 处理非序号列，包含溢出提示处理 -->
          <span v-else
                :c-tip='column.showOverflowTooltip? data[column.dataIndex]:null'
                :key='`column-${index}`'
                :class='`column ${column.showOverflowTooltip ? "over-flow-hidden":""}`'
                :style='`width: ${column.width};text-align: ${column.align};`'>
            {{ data[column.dataIndex] }}
          </span>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  /* 组件属性定义 */
  props: {
    /* 数据源，列表的数据项 */
    ds: {
      type: Array,
      default: () => {
        return [];
      }
    },
    /* 列定义，描述列表的每一列 */
    columns: {
      type: Array,
      default: () => {
        return [];
      }
    }
  },
  /* 组件内部数据定义 */
  data: function() {
    return {};
  },
  /* 组件方法定义 */
  methods: {}
};
</script>

<!-- 组件样式引用 -->
<style lang='less' src='./index.less' />
