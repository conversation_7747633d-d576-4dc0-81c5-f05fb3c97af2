<!--
 * @Description: 站点等级展示组件，用于在全屏监控页面中展示各个站点的等级信息。
 * @Autor: he.chao
 * @Date: 2022-10-19 15:07:55
 * @LastEditors: liu.yongli
 * @LastEditTime: 2023-12-13 14:36:14
-->
<template>
  <div class="station-level-wrap">
    <!-- 当没有数据时，显示空内容占位符 -->
    <el-empty :image-size="20" v-if="ds.length < 1" />
    <!-- 当有数据时，使用CommonSwiper组件展示站点等级列表 -->
    <CommonSwiper v-else ref="stationSwiper" id="station-level-swiper">
      <template v-slot:swiper-slide>
        <!-- 循环遍历数据集，每个数据块为一个Swiper的滑动项 -->
        <div v-for="(ds, index) in dsArray" class="swiper-slide">
          <StationLevelList :ds="ds" :columns="columns" />
        </div>
      </template>
    </CommonSwiper>
  </div>
</template>

<script>
import CommonSwiper from '@/components/page/ScreenPages/FullViewMonitor/CommonSwiper';
import StationLevelList from '@/components/page/ScreenPages/FullViewMonitor/SiteInfo/StationLevel/StationLevelList.vue';

/**
 * 站点等级组件。
 * 用于展示站点的等级信息，通过Swiper组件以滑动的方式展示多个站点。
 */
export default {
  components: {
    StationLevelList,
    CommonSwiper,
  },
  props: {
    ds: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    /**
     * 监听数据变化，当数据到达时进行处理。
     */
    ds: {
      handler: function (newVal, oldVal) {
        if (newVal.length > 0) {
          this.getDsArray();
        }
      },
      deep: true,
    },
  },
  data() {
    return {
      columns: [
        // 表格列定义
        {
          title: '序号',
          dataIndex: 'index',
          key: 'index',
          width: '10%',
          align: 'center',
        },
        {
          title: '站点名称',
          dataIndex: 'station',
          key: 'station',
          width: '25%',
          align: 'center',
          showOverflowTooltip: true,
        },
        {
          title: '铁路名称',
          dataIndex: 'railwayName',
          key: 'railwayName',
          width: '45%',
          align: 'center',
          showOverflowTooltip: true,
        },
        {
          title: '站点等级',
          dataIndex: 'stationLevelDesc',
          key: 'stationLevelDesc',
          align: 'center',
          width: '20%',
        },
      ],
      dsArray: [],
    };
  },
  mounted() {
    this.getDsArray();
  },
  methods: {
    /**
     * 根据数据集划分成多个数组块，每个数组块用于一个Swiper Slide。
     */
    getDsArray() {
      if (!this.ds || this.ds.length < 1) {
        return;
      }
      this.dsArray = _.chunk(this.ds, 5);
      this.$nextTick(() => {
        // 初始化Swiper组件
        this.$refs.stationSwiper.initSwiper();
      });
    },
  },
};
</script>

<style lang='less' src='./index.less' />
