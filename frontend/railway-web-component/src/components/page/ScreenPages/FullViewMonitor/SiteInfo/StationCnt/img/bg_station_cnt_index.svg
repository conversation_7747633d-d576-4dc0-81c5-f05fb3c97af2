<?xml version="1.0" encoding="UTF-8"?>
<svg width="393px" height="45px" viewBox="0 0 393 45" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>bg_辖区站点_38</title>
    <defs>
        <linearGradient x1="0%" y1="50%" x2="100%" y2="50%" id="linearGradient-1">
            <stop stop-color="#0A8BD4" offset="0%"></stop>
            <stop stop-color="#000B11" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="49.7599748%" x2="100%" y2="50.2400282%" id="linearGradient-2">
            <stop stop-color="#066096" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#5AC3FF" offset="100%"></stop>
        </linearGradient>
        <filter x="-0.6%" y="-6.9%" width="101.2%" height="113.9%" filterUnits="objectBoundingBox" id="filter-3">
            <feGaussianBlur stdDeviation="0.520010387" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="0%" y1="50%" x2="0%" y2="50%" id="linearGradient-4">
            <stop stop-color="#0A8BD4" offset="0%"></stop>
            <stop stop-color="#000B11" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="100%" y1="50%" x2="0%" y2="50%" id="linearGradient-5">
            <stop stop-color="#066096" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#5AC3FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0.0016%" y1="49.9998%" x2="100.002%" y2="49.9998%" id="linearGradient-6">
            <stop stop-color="#106190" offset="0%"></stop>
            <stop stop-color="#023451" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0.0016%" y1="49.9998%" x2="100.0019%" y2="49.9998%" id="linearGradient-7">
            <stop stop-color="#106190" offset="0%"></stop>
            <stop stop-color="#023451" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0.0016%" y1="49.9998%" x2="100.002%" y2="49.9998%" id="linearGradient-8">
            <stop stop-color="#106190" offset="0%"></stop>
            <stop stop-color="#023451" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="100%" y1="50%" x2="-46.2465%" y2="50%" id="linearGradient-9">
            <stop stop-color="#00EFFF" offset="0%"></stop>
            <stop stop-color="#4CFFE5" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <polygon id="path-10" points="4.49743001 5.66632411 0 3.12638804e-13 2.56984187 3.12638804e-13 7.06806351 5.66633544 2.56984187 11.3333282 0 11.3333282"></polygon>
        <filter x="-297.1%" y="-185.3%" width="694.2%" height="470.6%" filterUnits="objectBoundingBox" id="filter-11">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="7" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.00392156886   0 0 0 0 0.864338239   0 0 0 0 0.929411769  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="100%" y1="50%" x2="-46.2465%" y2="50%" id="linearGradient-12">
            <stop stop-color="#00EFFF" offset="0%"></stop>
            <stop stop-color="#4CFFE5" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <polygon id="path-13" points="11.5654935 5.66632667 7.06806351 0 9.63790839 0 14.1353383 5.66632667 9.63716632 11.3333333 7.06806351 11.3333333"></polygon>
        <filter x="-297.1%" y="-185.3%" width="694.3%" height="470.6%" filterUnits="objectBoundingBox" id="filter-14">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="7" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.00392156886   0 0 0 0 0.864338239   0 0 0 0 0.929411769  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="100%" y1="50%" x2="-46.2465%" y2="50%" id="linearGradient-15">
            <stop stop-color="#00EFFF" offset="0%"></stop>
            <stop stop-color="#4CFFE5" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <polygon id="path-16" points="2.12030075 24.1666667 0 24.1666667 0 10 2.12030075 10"></polygon>
        <filter x="-990.4%" y="-148.2%" width="2080.9%" height="396.5%" filterUnits="objectBoundingBox" id="filter-17">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="7" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.00392156886   0 0 0 0 0.864338239   0 0 0 0 0.929411769  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="全景展示-大屏" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="1-1全景展示-全域" transform="translate(-22.000000, -782.000000)">
            <g id="站点信息" transform="translate(24.000000, 740.000000)">
                <g transform="translate(12.000000, 0.000000)" id="bg_辖区站点_38">
                    <g transform="translate(0.000000, 46.000000)">
                        <polygon id="矩形_3_拷贝_2" stroke="url(#linearGradient-2)" stroke-width="1.41353383" fill-opacity="0.300000012" fill="url(#linearGradient-1)" opacity="0.300000012" filter="url(#filter-3)" points="376 12.9711976 376 30.1688196 365.129745 38 10.4988699 38 0 30.1746442 0 13.3163516 10.6239888 5.27777778 365.27115 5.27777778"></polygon>
                        <polygon id="矩形_3_拷贝" stroke="url(#linearGradient-5)" stroke-width="1.41353383" fill-opacity="0.300000012" fill="url(#linearGradient-4)" points="376 7.69341983 376 24.8910418 365.129745 32.7222222 10.4988699 32.7222222 0 24.8968664 0 8.03857383 10.6239888 0 365.27115 -4.58653177e-14"></polygon>
                        <g id="编组-50" transform="translate(12.000000, 1.000000)" opacity="0.300000012">
                            <path d="M41.7062132,0 L16.5996567,32 L0,32 L25.1065565,0 L41.7062132,0 Z M76.975808,0 L51.8692539,32 L35.268906,32 L60.37546,0 L76.975808,0 Z" id="差集" fill="url(#linearGradient-6)"></path>
                            <path d="M112.244025,0 L87.1374687,32 L70.5378119,32 L95.6443684,0 L112.244025,0 Z M148.218998,0 L123.112444,32 L106.512096,32 L131.61865,0 L148.218998,0 Z" id="差集" fill="url(#linearGradient-7)"></path>
                            <path d="M183.487215,0 L158.380659,32 L141.781002,32 L166.887558,0 L183.487215,0 Z M218.756121,0 L193.650274,32 L177.049908,32 L202.156464,0 L218.756121,0 Z" id="差集" fill="url(#linearGradient-8)"></path>
                            <path d="M254.025716,0 L228.919162,32 L212.318814,32 L237.425368,0 L254.025716,0 Z M290,0 L264.893446,32 L248.293098,32 L273.399652,0 L290,0 Z" id="差集" fill="url(#linearGradient-7)"></path>
                        </g>
                        <polygon id="矩形_5" fill="#0EC5FA" points="361.864662 14.875 356.917293 14.875 356.917293 12.0416667 361.864662 12.0416667"></polygon>
                        <polygon id="矩形_5_拷贝" fill="#0EC5FA" opacity="0.600000024" points="361.864662 22.6666667 356.917293 22.6666667 356.917293 19.8333333 361.864662 19.8333333"></polygon>
                        <polygon id="矩形_5_拷贝_2" fill="#0EC5FA" opacity="0.400000006" points="361.864662 30.4583333 356.917293 30.4583333 356.917293 27.625 361.864662 27.625"></polygon>
                        <g id="编组-49" transform="translate(14.000000, 11.000000)">
                            <g id="形状_4_拷贝">
                                <use fill="black" fill-opacity="1" filter="url(#filter-11)" xlink:href="#path-10"></use>
                                <use fill="url(#linearGradient-9)" fill-rule="evenodd" xlink:href="#path-10"></use>
                            </g>
                            <g id="形状_4_拷贝_2">
                                <use fill="black" fill-opacity="1" filter="url(#filter-14)" xlink:href="#path-13"></use>
                                <use fill="url(#linearGradient-12)" fill-rule="evenodd" xlink:href="#path-13"></use>
                            </g>
                        </g>
                        <g id="矩形_4">
                            <use fill="black" fill-opacity="1" filter="url(#filter-17)" xlink:href="#path-16"></use>
                            <use fill="url(#linearGradient-15)" fill-rule="evenodd" xlink:href="#path-16"></use>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>
