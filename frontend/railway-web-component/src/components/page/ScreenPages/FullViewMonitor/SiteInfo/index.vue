<template>
  <!-- 站点信息组件，用于展示站点的相关统计数据和等级分布 -->
  <DetailInfo
    class="site-info-wrap"
    v-loading="loading"
    :style="`min-width: ${changePxToRem(minWidth)};min-height: ${changePxToRem(minHeight)};max-width: ${changePxToRem(maxWidth)};max-height: ${changePxToRem(maxHeight)};height: ${changePxToRem(maxHeight)}`"
  >
    <!-- 自定义标题图标 -->
    <template v-slot:title-icon>
      <svg-icon svg-name="icon_station" />
    </template>
    <!-- 自定义标题文本 -->
    <template v-slot:title>{{ compName}}</template>
    <!-- 细节内容区域，包含站点总数和等级分布两个子组件 -->
    <template v-slot:detail-content>
      <StationCnt :total-num="stationNum" />
      <StationLevel :ds="stationList" />
    </template>
  </DetailInfo>
</template>

<script>
import {
  getLineStationList,
  getThemeCompByCode,
} from '@/api/service/fullviewService'
import DetailInfo from '@/components/page/ScreenPages/FullViewMonitor/DetailInfo/index.vue'
import StationCnt from '@/components/page/ScreenPages/FullViewMonitor/SiteInfo/StationCnt/index.vue'
import StationLevel from '@/components/page/ScreenPages/FullViewMonitor/SiteInfo/StationLevel/index.vue'

import { mapGetters } from 'vuex'

/**
 * 站点信息组件，用于展示指定区域的站点总数和等级分布情况。
 */
export default {
  components: {
    DetailInfo,
    StationCnt,
    StationLevel,
  },
  computed: {
    // 获取选中的区域信息
    selectedRegion() {
      return this.$store.state.map.selectedRegion
    },
    changePxToRem() {
      return val => this.pxToRem(+(val ? val.replace('px', '') : val))
    },
    // 从vuex映射获取格式化后的区域信息
    ...mapGetters('map', ['formatSelectedRegion']),
  },
  // 监听选中区域的变化，更新数据
  watch: {
    selectedRegion: {
      handler: function (newVal, oldVal) {
        if (newVal) {
          this.qryLineStationList()
        }
      },
    },
  },
  data() {
    return {
      stationList: [], // 站点列表数据
      stationNum: 0, // 站点总数
      loading: true, // 加载状态
      minWidth: '366px', // 组件最小宽度
      minHeight: '316px', // 组件最小高度
      maxWidth: '366px', // 组件最大宽度
      maxHeight: '316px', // 组件最大高度
      compName: '站点信息', // 组件名称
    }
  },
  mounted() {
    // 页面加载时查询组件配置并初始化数据
    this.qryThemeCompByCode(this.qryLineStationList)
  },
  methods: {
    /**
     * 查询指定区域的站点列表信息。
     */
    async qryLineStationList() {
      const [success, data] = await getLineStationList({
        ...this.formatSelectedRegion,
      })
      let res = 0
      let resList = []
      if (success && data && data.length > 0) {
        resList = data.map(item => {
          return {
            station: item.stationName, // 站点名称
            railwayName: item.lineName, // 所属线路名称
            stationLevelDesc: item.stationLevelName, // 站点等级描述
          }
        })
        res = data.length // 站点总数
      }
      this.stationNum = res
      this.stationList = resList
    },
    /**
     * 根据组件代码查询组件配置，并初始化组件的尺寸和名称。
     * @param {Function} call - 查询数据后的回调函数
     */
    async qryThemeCompByCode(
      call = () => {
        console.log('qryThemeCompByCode')
      }
    ) {
      const [success, data] = await getThemeCompByCode({
        compCode: 'STATION_INFO',
      })
      if (success && data) {
        const { minWidth, minHeight, maxWidth, maxHeight, compName } = data
        // 设置组件的尺寸和名称
        this.minWidth = minWidth || '366'
        this.minHeight = minHeight || '316'
        this.maxWidth = maxWidth || '366'
        this.maxHeight = maxHeight || '316'
        this.compName = compName || '站点信息'
      }
      this.loading = false // 结束加载状态
      call()
    },
  },
}
</script>

<style lang='less' src='./index.less' />
