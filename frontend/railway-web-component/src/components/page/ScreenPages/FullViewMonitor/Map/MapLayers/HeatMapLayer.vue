<template></template>
<script>
import CommonMap from '@/components/common/Map/CommonMap'
import { creatHeatMap } from '@/utils/map3.0'
import { mapGetters } from 'vuex'
import { transformPointToMercator } from '@/utils'
// import { evtLayerData } from '@/components/page/ScreenPages/FullViewMonitor/mockData'
import { getMapEvts } from '@/api/service/imScreenService'
import dayjs from 'dayjs'
import { getFullViewEvtParams } from '@/components/common/utils'
// 存储地图图层对象的容器
const mapLayers = {}

/**
 * 组件定义，用于管理热力地图图层的显示与隐藏
 */
export default {
  inject: ['mapRef'],
  props: {
    mapId: {
      type: String,
    },
  },
  computed: {
    /**
     * 获取热力地图图层开关状态
     * @returns {boolean} 热力地图图层的开关状态
     */
    heatMapLayerSwitch() {
      return this.$store.state.map.heatMapLayerSwitch
    },
    selectedRegion() {
      return this.$store.state.map.selectedRegion //需要监听的属性
    },
    evtFilter() {
      return this.$store.state.event.evtFilter //需要监听的属性
    },
    // 筛选按钮状态
    evtFilterSwitch() {
      return this.$store.state.map.evtFilterSwitch
    },
    ...mapGetters('map', ['formatSelectedRegion']),
    ...mapGetters('event', ['formatEvtFilter']),
  },
  watch: {
    /**
     * 监听热力地图图层开关状态的变化
     * @param {boolean} newVal 新的开关状态
     * @param {boolean} oldVal 旧的开关状态
     */
    heatMapLayerSwitch(newVal, oldVal) {
      if (!newVal) {
        this.removeHeatMap()
        return
      }
      this.initLayer()
    },
    selectedRegion: {
      handler: function (newVal, oldVal) {
        this.commonHandler(newVal, oldVal)
      },
    },
    // evtFilter: {
    //   handler: function (newVal, oldVal) {
    //     this.commonHandler(newVal, oldVal)
    //   },
    //   deep: true,
    // },
    // 监听筛选按钮状态
    evtFilterSwitch: {
      handler: function (newVal) {
        if (!newVal) {
          this.filterState = ''
          this.onEventFilterChange()
        }
      },
    },
  },
  data() {
    return {
      // 热力地图的数据点集合
      heatData: {
        type: 'FeatureCollection',
        features: [],
      },
    }
  },
  mounted() {
    // 在组件挂载后，根据热力地图图层开关状态初始化图层
    this.initLayer()
    this.$EventBus.$on('onEventFilterChange', this.onEventFilterChange)
  },
  beforeDestroy() {
    this.$EventBus.$off('onEventFilterChange')
  },
  methods: {
    /**
     * 事件筛选过滤条件回调处理
     */
    onEventFilterChange(e = {}) {
      this.filterState = e.filterState || ''
      setTimeout(() => {
        // 延迟执行，避免切换太快导致数据未更新
        this.commonHandler(this.heatMapLayerSwitch)
      }, 500)
    },
    commonHandler(newVal, oldVal) {
      if (newVal) {
        this.initLayer()
      }
    },
    /**
     * 初始化热力地图图层
     * 如果已存在热力地图图层，则先移除，再重新创建
     */
    async initLayer() {
      this.removeHeatMap()
      if (!this.heatMapLayerSwitch) {
        return
      }
      const [, resData] = await getMapEvts({
        // 默认查当日
        startDate: dayjs().format('YYYY-MM-DD 00:00:00'),
        endDate: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        ...getFullViewEvtParams(this.filterState),
      })
      // 测试数据 evtLayerData.data
      const data = resData
      // 创建标记点数组
      const markers = []
      let res = []
      if (data?.length > 0) {
        data.map(item => {
          const { startLat, startLng } = item
          markers.push({
            type: 'Feature',
            geometry: {
              type: 'Point',
              coordinates: transformPointToMercator([
                Number(startLng),
                Number(startLat),
              ]),
            },
          })
        })
        res = markers
      }
      if (res.length < 1) {
        return
      }
      this.heatData.features = res
      // 使用给定的数据创建热力地图图层并添加到地图中
      mapLayers.heatMapLayer = creatHeatMap({
        mapInstance: this.mapRef.getMapRef(this.mapId).mapInstance,
        heatData: this.heatData,
      })
    },
    /**
     * 移除热力地图图层
     * 如果热力地图图层存在，则从地图上移除
     */
    removeHeatMap() {
      if (mapLayers.heatMapLayer) {
        CommonMap.removeLayer(
          this.mapRef.getMapRef(this.mapId).mapInstance,
          mapLayers.heatMapLayer
        )
      }
    },
  },
}
</script>
