<template></template>
<script>
import { transformPointToMercator } from '@/utils'
// import { evtLayerData } from '@/components/page/ScreenPages/FullViewMonitor/mockData'
import { getMapEvts } from '@/api/service/imScreenService'
import dayjs from 'dayjs'
import { getFullViewEvtParams } from '@/components/common/utils'
/**
 * 事件图层组件
 * 用于在地图上显示和管理事件标记层
 */
import CommonMap from '@/components/common/Map/CommonMap'
// import { uuid } from '@/components/common/utils'
import DicConst from '@/components/page/ScreenPages/FullViewMonitor/enums/DicConst'
import { evtIcon } from '@/components/page/ScreenPages/FullViewMonitor/Map/MapLayers/markerIcon'
import { imageMarker, useClusterLayer } from '@/utils/map3.0'
import { mapGetters } from 'vuex'

// 定义地图层对象
const mapLayer = {}

export default {
  inject: ['mapRef'],
  props: {
    mapId: {
      type: String,
    },
  },
  // 计算属性：获取地图事件层开关状态
  computed: {
    evtLayerSwitch() {
      return this.$store.state.map.evtLayerSwitch //需要监听的属性
    },
    selectedRegion() {
      return this.$store.state.map.selectedRegion //需要监听的属性
    },
    evtFilter() {
      return this.$store.state.event.evtFilter //需要监听的属性
    },
    // 筛选按钮状态
    evtFilterSwitch() {
      return this.$store.state.map.evtFilterSwitch
    },
    ...mapGetters('map', ['formatSelectedRegion']),
    ...mapGetters('event', ['formatEvtFilter']),
  },
  // 监视地图事件层开关状态的变化
  watch: {
    evtLayerSwitch(newVal, oldVal) {
      // 当开关关闭时，移除事件图层
      if (!newVal) {
        this.removeLayer()
        return
      }
      // 当开关打开时，初始化或重新添加事件图层
      this.initLayer()
    },
    selectedRegion: {
      handler: function (newVal, oldVal) {
        this.commonHandler(newVal, oldVal)
      },
    },
    // evtFilter: {
    //   handler: function (newVal, oldVal) {
    //     this.commonHandler(newVal, oldVal)
    //   },
    //   deep: true,
    // },
    // 监听筛选按钮状态
    evtFilterSwitch: {
      handler: function (newVal) {
        if (!newVal) {
          this.filterState = ''
          this.onEventFilterChange()
        }
      },
    },
  },
  // 数据属性初始化
  data: function () {
    return {}
  },
  // 组件挂载后，根据开关状态初始化事件图层
  mounted() {
    this.initLayer()
    this.$EventBus.$on('onEventFilterChange', this.onEventFilterChange)
  },
  beforeDestroy() {
    this.$EventBus.$off('onEventFilterChange', this.onEventFilterChange)
  },
  methods: {
    /**
     * 事件筛选过滤条件回调处理
     */
    onEventFilterChange(e = {}) {
      this.filterState = e.filterState || ''
      this.commonHandler(this.evtLayerSwitch)
    },
    commonHandler(newVal, oldVal) {
      if (newVal) {
        this.initLayer()
      }
    },
    /**
     * 初始化事件图层。
     * 此方法用于在地图上添加事件标记层。如果事件标记开关未打开，则直接返回。
     * 通过调用getWarnOrderLayer接口获取事件数据，并根据数据创建标记点。
     * 使用聚合层管理这些标记点，以提高地图的可读性。
     * 如果接口调用成功且返回数据有效，则创建并添加标记点到地图上。
     */
    // 初始化事件图层方法
    async initLayer() {
      // 先移除旧的图层，避免重复添加
      this.removeLayer()
      // 如果事件图层开关未打开，则直接返回
      console.log('图层开关--', this.evtLayerSwitch)
      if (!this.evtLayerSwitch) {
        return
      }
      // 调用接口获取事件数据
      const [_, resData] = await getMapEvts({
        // 默认查当日
        startDate: dayjs().format('YYYY-MM-DD 00:00:00'),
        endDate: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        ...getFullViewEvtParams(this.filterState),
      })
      // 测试数据 evtLayerData.data
      const data = resData
      // 创建标记点数组
      const markers = []
      // 如果接口调用成功且返回数据不为空，则处理数据创建标记点
      if (data?.length > 0) {
        data.map(item => {
          const { eventSource, startLat, startLng } = item
          // 根据事件来源选择不同的图标
          markers.push(
            imageMarker(
              transformPointToMercator([Number(startLng), Number(startLat)]),
              {
                icon: DicConst.EVT_SOURCE.HUMAN.split(',').includes(eventSource)
                  ? evtIcon.HumanIcon
                  : evtIcon.AIIcon,
                anchor: [40, 80],
              }
            )
          )
        })
      }
      // 使用聚合层管理标记点，并添加到地图上
      // 使用聚合层管理标记点，并添加到地图
      mapLayer.evtLayer = useClusterLayer({
        markers,
        map: this.mapRef.getMapRef(this.mapId).mapInstance,
        onClusterClick: (features, geometry) => {
          console.log('onClusterClick')
        },
        type: 'evt',
      })
    },
    /**
     * 移除事件图层。
     * 此方法用于从地图上移除事件标记层。如果图层存在，则调用CommonMap.removeLayer方法移除。
     * 这样做可以确保在重新初始化图层时，不会出现旧的图层残留。
     */
    // 移除事件图层方法
    removeLayer() {
      // 如果事件图层存在，则从地图上移除
      if (mapLayer.evtLayer) {
        CommonMap.removeLayer(
          this.mapRef.getMapRef(this.mapId).mapInstance,
          mapLayer.evtLayer
        )
      }
    },
  },
}
</script>
