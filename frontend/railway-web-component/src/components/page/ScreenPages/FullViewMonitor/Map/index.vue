<template></template>
<script>
// import { getMapTiles } from '@/api/service/common' // 导入获取地图瓦片的服务
import CommonMap from '@/components/common/Map/CommonMap' // 导入通用地图组件
import { Events } from '@/components/page/ScreenPages/IntegratedMonitor/enum/EventsEnum' // 导入事件枚举
import { createFullViewMap } from '@/utils/map3.0' // 导入创建全景地图的工具函数
/**
 * 缓存对象，用于存储图层信息
 */
const layerCache = {
  getLayer: layer => {
    return layerCache[layer] || {} // 返回指定图层的信息，如果不存在则返回空对象
  },
}

/**
 * 地图事件枚举，用于处理地图上的各种事件
 */
// const MapEvent = Events.MapEvent // 从事件枚举中获取地图事件
let _map = null
export default {
  inject: ['mapRef'],
  props: {
    /**
     * 地图加载后的回调函数
     */
    onMapLoad: {
      type: Function, // 指定属性类型为函数
      default: () => {
        console.log('onMapLoad') // 默认回调函数，输出日志信息
      },
    },
    /**
     * 地图的初始中心点
     */
    center: {
      type: Array, // 指定属性类型为数组
      default: () => {
        return [116.38813, 39.89446] // 默认中心点坐标（北京）
      },
    },
    mapId: {
      type: String,
    },
  },
  data() {
    return {
      mapLoaded: false, // 地图是否加载完成的状态标识
    }
  },
  mounted() {
    this.initMap() // 组件挂载时初始化地图
    this.$EventBus.$on(
      Events.MAP_TOOL_EVT.MAP_LAYER_EXCHANGE,
      this.onMaLayerExchange
    ) // 监听图层切换事件
  },
  methods: {
    /**
     * 处理图层切换事件
     * @param {number} newval 切换的图层索引
     */
    onMaLayerExchange(newval) {
      if (newval === 0) {
        window.map.changeLayersHj('vector', 0) // 切换到矢量图层
      }
      if (newval === 1) {
        window.map.changeLayersHj('satellite', 1) // 切换到卫星图层
      }
      if (newval === 2) {
        window.map.changeLayersHj('vector', 2) // 切换到矢量图层
      }
    },
    /**
     * 初始化地图
     */
    async initMap() {
      // console.log('初始化地图--')
      // await getMapTiles() // 异步获取地图瓦片
      const { MAP_CFG } = window.hjCfg // 从全局配置中获取地图配置
      _map = createFullViewMap(this.mapRef.getMapRef(this.mapId), {
        center: this.center || MAP_CFG.defCenter || [116.38813, 39.89446], // 设置地图中心点
        zoom: MAP_CFG.defZoom || 12, // 设置默认缩放级别
        maxZoom: MAP_CFG.maxZoom || 17, // 设置最大缩放级别
        minZoom: MAP_CFG.minZoom || 1, // 设置最小缩放级别
      })
      // _map.on('singleclick', e => {
      //   let pixel = _map.getEventPixel(e.originalEvent) // 获取点击事件的像素位置
      //   let currentFeature = _map.forEachFeatureAtPixel(
      //     pixel,
      //     function (feature, layer) {
      //       return feature // 获取点击位置的地图特征
      //     }
      //   )
      //   CommonMap.closeTip() // 关闭地图提示
      //   // 如果点击到地图上的锚点对象，currentFeature就不为空
      //   if (currentFeature) {
      //     if (
      //       currentFeature.values_.features &&
      //       currentFeature.values_.features.length === 1 &&
      //       currentFeature.values_.features[0].values_.attributes.onclick
      //     ) {
      //       // 聚合图层散落出来的点才可以点击
      //       currentFeature.values_.features[0].values_.attributes.onclick(
      //         currentFeature.values_.features[0].values_.attributes,
      //         currentFeature
      //       )
      //     } else if (
      //       currentFeature.values_.features &&
      //       currentFeature.values_.features.length > 1 &&
      //       this.map.getView().getZoom() === this.map.getView().getMaxZoom()
      //     ) {
      //       currentFeature.values_.attributes.onclick(
      //         currentFeature.values_.features,
      //         currentFeature.getGeometry()
      //       )
      //     } else if (
      //       currentFeature.values_.attributes &&
      //       currentFeature.values_.attributes.onclick
      //     ) {
      //       currentFeature.values_.attributes.onclick(
      //         currentFeature.values_.attributes,
      //         currentFeature
      //       )
      //     } else {
      //       console.warn('没有绑定onclick方法') // 如果没有绑定点击事件，输出警告信息
      //     }
      //   }
      // })
      this.onMapLoad(_map) // 调用地图加载回调函数
      this.mapLoaded = true // 更新地图加载状态
    },
    /**
     * 地图缩放级别改变时的处理函数
     * @param {number} zoom 当前的缩放级别
     */
    onMapZoomChange(zoom) {
      _map && CommonMap.closeTip(_map) // 关闭地图提示
    },
  },
}
</script>
