<template>
  <!-- 事件总览组件，用于展示事件概述和告警类型统计 -->
  <DetailInfo
    class="evt-overview-wrap"
    v-loading="loading"
    :style="`min-width: ${changePxToRem(minWidth)};min-height: ${changePxToRem(minHeight)};max-width: ${changePxToRem(maxWidth)};max-height: ${changePxToRem(maxHeight)};height: ${changePxToRem(maxHeight)}`"
  >
    <!-- 设置标题图标 -->
    <template v-slot:title-icon>
      <svg-icon svg-name="icon_event_view" />
    </template>
    <!-- 设置标题文本，包含事件总览的名称和描述 -->
    <template v-slot:title>
      {{ compName }}
      <span class="evt-overview-title-desc">(起)</span>
    </template>
    <!-- 设置详细内容区域，包含事件来源统计和告警类型统计 -->
    <template v-slot:detail-content>
      <EvtSrcCnt v-if="evtSrcIndex?.aiNum + ''" :evtSrcIndex="evtSrcIndex" />
      <!-- 设置子标题文本为"告警类型统计" -->
      <SubTitle>
        <template v-slot:text>事件类型统计</template>
      </SubTitle>
      <!-- 显示告警类型统计轮播图，如果没有数据则显示空内容提示 -->
      <div style="width: 100%; margin: 0">
        <el-empty :image-size="20" v-if="dsArray.length < 1" />
        <CommonSwiper v-else ref="evtTypeCnt" id="evtTypeSwiper" :delay="3500">
          <!-- 为每个告警类型创建一个轮播图项 -->
          <template v-slot:swiper-slide>
            <div
              v-for="(ds, index) in dsArray"
              class="swiper-slide"
              :key="`swiper-slide_${index }`"
            >
              <EvtTypeCnt :ds="ds" />
            </div>
          </template>
        </CommonSwiper>
      </div>
    </template>
  </DetailInfo>
</template>

<script>
import {
  getThemeCompByCode,
  getWarnTypeStatistics,
  getEvtTypesStatistics,
} from '@/api/service/fullviewService' // 获取数据接口
import CommonSwiper from '@/components/page/ScreenPages/FullViewMonitor/CommonSwiper' // 轮播图组件
import DetailInfo from '@/components/page/ScreenPages/FullViewMonitor/DetailInfo/index.vue' // 详细信息组件
import SubTitle from '@/components/page/ScreenPages/FullViewMonitor/DetailInfo/SubTitle.vue' // 子标题组件
import EvtSrcCnt from '@/components/page/ScreenPages/FullViewMonitor/EvtOverview/EvtSrcCnt.vue' // 事件类型统计组件
import EvtTypeCnt from '@/components/page/ScreenPages/FullViewMonitor/EvtOverview/EvtTypeCnt.vue' // 告警类型统计组件
import StationLevelList from '@/components/page/ScreenPages/FullViewMonitor/SiteInfo/StationLevel/StationLevelList.vue' // 站点层级列表
import { mapGetters } from 'vuex' // Vuex
import { getFullViewEvtParams } from '@/components/common/utils' // 获取数据
import dayjs from 'dayjs' // 时间处理
import {
  // evtStatisticsData,
  evtTypesStatisticsData,
} from '@/components/page/ScreenPages/FullViewMonitor/mockData' // mock数据

/**
 * 事件总览组件，用于展示选定区域的事件概述和告警类型统计。
 *
 * @component DetailInfo 组件用于展示详细信息，包括标题、图标和内容。
 * @component EvtSrcCnt 组件用于展示事件来源统计。
 * @component SubTitle 组件用于展示子标题。
 * @component EvtTypeCnt 组件用于展示告警类型统计。
 * @component CommonSwiper 组件用于实现轮播效果。
 */
export default {
  components: {
    StationLevelList, // 站点层级列表
    DetailInfo, // 详细信息组件
    SubTitle, // 子标题组件
    EvtSrcCnt, // 事件类型统计组件
    EvtTypeCnt, // 告警类型统计组件
    CommonSwiper, // 轮播图组件,
  },
  // 计算属性：获取选定区域的信息，用于请求数据。
  computed: {
    // 获取选定区域的信息。
    selectedRegion() {
      return this.$store.state.map.selectedRegion //需要监听的属性
    },
    // 筛选按钮状态
    evtFilterSwitch() {
      return this.$store.state.map.evtFilterSwitch
    },
    //// 获取格式化后的选定区域信息。
    ...mapGetters('map', ['formatSelectedRegion']),
    // 转换px为rem
    changePxToRem() {
      return val => this.pxToRem(+(val ? val.replace('px', '') : val))
    },
  },
  // 监视选定区域的变化，更新告警类型统计。
  watch: {
    selectedRegion: {
      handler: function (newVal) {
        if (newVal) {
          // 筛选
          this.onEventFilterChange({
            filterState: this.filterState,
          })
        }
      },
    },
    // 监听筛选按钮状态
    evtFilterSwitch: {
      handler: function (newVal) {
        if (!newVal) {
          this.filterState = ''
          // 筛选
          this.onEventFilterChange()
        }
      },
    },
  },
  data: function () {
    return {
      dsArray: [], // 存储告警类型统计数据的数组。
      loading: true, // 控制加载状态。
      minWidth: '366px', // 最小宽度。
      minHeight: '316px', // 最小高度。
      maxWidth: '366px', // 最大宽度。
      maxHeight: '316px', // 最大高度。
      compName: '事件总览', // 组件名称。
      evtSrcIndex: {}, // 存储告警类型统计数据的对象。
      filterState: '', // 工具箱事件筛选的状态
    }
  },
  mounted() {
    // 组件挂载后，请求组件配置和告警类型统计数据。
    this.qryThemeCompByCode(this.qryEvtSrcIndex)
    // 筛选监测事件筛选变化
    this.$EventBus.$on('onEventFilterChange', this.onEventFilterChange)
  },
  beforeDestroy() {
    // 销毁事件监听
    this.$EventBus.$off('onEventFilterChange')
  },
  methods: {
    /**
     * 事件筛选过滤条件回调处理
     */
    onEventFilterChange(e = {}) {
      this.filterState = e.filterState || ''
      // 请求告警类型统计数据。
      this.qryEvtSrcIndex()
    },
    /**
     * 请求告警类型统计数据，根据选定区域分组。
     *
     * @async
     * @returns {void}
     */
    async qryEvtTypeIndex() {
      // 请求统计数据。
      const [success, data] = await getWarnTypeStatistics({
        ...this.formatSelectedRegion,
      })
      let res = []
      // 处理数据。
      if (success && data && data.length > 0) {
        res = _.chunk(data, 4) // 将数据分组为4个一组，用于创建轮播图项。
      }
      // 将数据分组为4个一组，用于创建轮播图项。
      this.dsArray = res
      this.$nextTick(() => {
        this.$refs.evtTypeCnt?.initSwiper?.() // 初始化轮播图。
      })
    },
    /**
     * 查询事件来源统计指数数据
     * @async
     */
    async qryEvtSrcIndex() {
      this.loading = true
      // 接口
      const [, resData] = await getEvtTypesStatistics({
        // 默认查半年
        startDate: dayjs().subtract(6, 'months').format('YYYY-MM-DD 00:00:00'),
        endDate: dayjs().format('YYYY-MM-DD 23:59:59'),
        ...getFullViewEvtParams(this.filterState), // 获取筛选条件
      })
      // 测试数据
      const data = resData ?? evtTypesStatisticsData.data
      const sumObj = {
        // 人工上报告警数
        manualNum: data.artEvent || 0,
        // 智能告警数
        aiNum: data.aiEvent || 0,
      }
      this.evtSrcIndex = sumObj
      const sum = sumObj.manualNum * 1 + sumObj.aiNum * 1
      // 计算比率
      const sumList = data.list?.length
        ? data.list.map(v => ({
            ...v,
            typeName: v.warningTypeName,
            rate:
              sum > 0 ? parseFloat((((v.num * 1) / sum) * 100).toFixed(1)) : 0,
          }))
        : []
      // 将数据分组为4个一组，用于创建轮播图项。
      this.dsArray = sumList.length > 0 ? _.chunk(sumList, 4) : []
      this.$nextTick(() => {
        this.$refs.evtTypeCnt?.initSwiper?.() // 初始化轮播图。
      })
      this.loading = false
    },
    /**
     * 请求组件配置，用于设置组件的尺寸和名称。
     *
     * @param {Function} call 请求完成后要调用的函数。
     * @async
     * @returns {void}
     */
    async qryThemeCompByCode(
      call = () => {
        console.log('qryThemeCompByCode')
      }
    ) {
      // 请求组件配置。
      const [success, data] = await getThemeCompByCode({
        compCode: 'EVENT_OVERVIEW_STAT',
      })
      // 判断
      if (success && data) {
        // 获取数据
        const { minWidth, minHeight, maxWidth, maxHeight, compName } = data
        this.minWidth = minWidth || '366'
        this.minHeight = minHeight || '316'
        this.maxWidth = maxWidth || '366'
        this.maxHeight = maxHeight || '316'
        this.compName = compName || '事件总览'
      }
      this.loading = false
      call()
    },
  },
}
</script>
<style lang='less' src='./index.less' />
