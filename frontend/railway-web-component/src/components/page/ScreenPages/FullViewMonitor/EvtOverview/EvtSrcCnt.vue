<template>
  <div class="evt-cnt-wrap" style="margin: 0;">
    <!-- 事件统计区域 -->
    <div class="evt-stat-cnt-wrap">
      <svg-icon svg-name="icon_evt_stat" />
      <div class="evt-stat-index">
        <!-- 告警总数统计项 -->
        <div class="evt-index-item">
          <span class="index-label">事件总数</span>
          <span class="index-value" style="color:  #ED5158">{{ formatVal(evtIndex.totalEvent) }}</span>
        </div>
        <!-- 处置中统计项 -->
        <div class="evt-index-item">
          <span class="index-label">处置中</span>
          <span
            class="index-value"
            style="color:  #4F9FFF"
          >{{ formatVal(evtIndex.unfinishedEvent) }}</span>
        </div>
        <!-- 已完成统计项 -->
        <div class="evt-index-item">
          <span class="index-label">已完成</span>
          <span class="index-value" style="color: #15BD94">{{ formatVal(evtIndex.finishedEvent) }}</span>
        </div>
      </div>
    </div>
    <!-- 事件来源统计区域 -->
    <div class="evt-type-index">
      <div class="evt-person-evt">
        <!-- 智能告警统计项 -->
        <span class="index-label">智能报警</span>
        <span class="index-value number-font">{{formatVal(evtSrcIndex.aiNum)}}</span>
      </div>
      <div class="evt-link"></div>
      <div class="evt-person-evt">
        <!-- 人工上报统计项 -->
        <span class="index-label">人工上报</span>
        <span class="index-value number-font">{{ formatVal(evtSrcIndex.manualNum)}}</span>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getEvtStatistics,
  // getEvtTypesStatistics,
} from '@/api/service/fullviewService'
import { formatVal } from '@/components/common/utils'
import { mapGetters } from 'vuex'
import { getFullViewEvtParams } from '@/components/common/utils'
import dayjs from 'dayjs'
import {
  evtStatisticsData,
  // evtTypesStatisticsData,
} from '@/components/page/ScreenPages/FullViewMonitor/mockData'

/**
 * 全景视图事件统计组件
 * 该组件展示事件总数、处置中事件数、已完成事件数，以及智能告警和人工上报事件数。
 * 数据通过调用API获取，并根据区域变化实时更新。
 */
export default {
  props: {
    // 事件来源统计指数
    evtSrcIndex: {
      type: Object,
      default: () => {
        return {
          aiNum: 0,
          manualNum: 0,
        }
      },
    },
  },
  computed: {
    // 监听并获取选中的区域信息
    selectedRegion() {
      return this.$store.state.map.selectedRegion
    },
    // 筛选按钮状态
    evtFilterSwitch() {
      return this.$store.state.map.evtFilterSwitch
    },
    ...mapGetters('map', ['formatSelectedRegion']),
  },
  watch: {
    // 当选中区域变化时，更新事件统计数据
    selectedRegion: {
      handler: function (newVal) {
        if (newVal) {
          this.onEventFilterChange({
            filterState: this.filterState,
          })
        }
      },
    },
    // 监听筛选按钮状态
    evtFilterSwitch: {
      handler: function (newVal) {
        if (!newVal) {
          this.filterState = ''
          this.onEventFilterChange()
        }
      },
    },
  },
  data: function () {
    return {
      filterState: '', // 工具箱事件筛选的状态
      evtIndex: {}, // 事件统计指数
      // evtSrcIndex: {}, // 事件来源统计指数
    }
  },
  mounted() {
    this.onEventFilterChange()
    this.$EventBus.$on('onEventFilterChange', this.onEventFilterChange)
  },
  beforeDestroy() {
    this.$EventBus.$off('onEventFilterChange')
  },
  methods: {
    formatVal,
    /**
     * 事件筛选过滤条件回调处理
     */
    onEventFilterChange(e = {}) {
      this.filterState = e.filterState || ''
      this.qryEvtIndexData()
      // this.qryEvtSrcIndex()
    },
    /**
     * 查询事件统计指数数据
     * @async
     */
    async qryEvtIndexData() {
      const [success, resData] = await getEvtStatistics({
        // 默认查半年
        startDate: dayjs().subtract(6, 'months').format('YYYY-MM-DD 00:00:00'),
        endDate: dayjs().format('YYYY-MM-DD 23:59:59'),
        ...getFullViewEvtParams(this.filterState),
      })
      // 测试数据
      const data = resData ?? evtStatisticsData.data
      this.evtIndex = data
    },
    /**
     * 查询事件来源统计指数数据
     * @async
     */
    // async qryEvtSrcIndex() {
    //   const [, resData] = await getEvtTypesStatistics({
    //     // 默认查半年
    //     startDate: dayjs().subtract(6, 'months').format('YYYY-MM-DD 00:00:00'),
    //     endDate: dayjs().format('YYYY-MM-DD 23:59:59'),
    //     ...getFullViewEvtParams(this.filterState),
    //   })
    //   // 测试数据
    //   const data = resData ?? evtTypesStatisticsData.data
    //   this.evtSrcIndex = {
    //     // 人工上报告警数
    //     manualNum: data.artEvent?.length
    //       ? data.artEvent.reduce((total, item) => total + item.num * 1, 0)
    //       : 0,
    //     // 智能告警数
    //     aiNum: data.artEvent?.length
    //       ? data.aiEvent.reduce((total, item) => total + item.num * 1, 0)
    //       : 0,
    //   }
    // },
  },
}
</script>

<style lang='less' src='./index.less' />
