<template>
  <!-- 事件类型统计区块 -->
  <div class="evt-type-cnt-wrap">
    <!-- 索引列表容器 -->
    <div class="index-list-wrap">
      <!-- 遍历数据项，展示每个事件类型的统计信息 -->
      <div v-for="item in ds" class="index-list-item" :key="item.typeName">
        <!-- 事件类型标签，包含图标和名称 -->
        <div class="index-label">
          <span class="icon-yuan" />
          {{ item.typeName }}
        </div>
        <!-- 事件类型的统计值，包括数量和占比 -->
        <div class="index-value">
          <!-- 事件数量展示，带提示功能 -->
          <div class="index-value-num">
            <span class="num number-font" :c-tip="item.num">{{ formatVal(item.num) }}</span>
            <span class="unit">起</span>
          </div>
          <!-- 事件占比展示 -->
          <span class="index-value-percent unit number-font">{{ formatVal(item.rate) }}%</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { formatVal } from '@/components/common/utils'

export default {
  props: {
    ds: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {}
  },
  mounted() {
    // 组件挂载后的逻辑，此处为空
  },
  methods: {
    // 格式化数值的方法，用于显示
    formatVal,
  },
}
</script>

<style lang='less' src='./index.less' />
