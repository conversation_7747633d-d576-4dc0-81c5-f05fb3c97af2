@import "../common";

.fullview-detail-info-wrap.evt-overview-wrap {
  width: 366px;

  .swiper-slide {
    width: 100% !important;
    margin-right: 0px !important;
  }

  .evt-overview-title-desc {
    font-family: PingFangSC-Regular;
    font-size: 12px;
    color: rgba(232, 243, 255, 0.6);
    letter-spacing: 0;
    line-height: 22px;
    text-shadow: 2px 2px 2px rgba(0, 0, 0, 0.5);
    font-weight: 400;
    margin-left: 6px;
  }

  .evt-cnt-wrap {
    margin: 0;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: flex-start;

    .evt-stat-cnt-wrap {
      width: 100%;
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: url("./img/bg_evt_stat.png") 100% 100% no-repeat;
      background-size: 100% 25px;

      .svg-icon {
        width: 42px;
        height: 42px;
        margin: 0;
      }

      .evt-stat-index {
        width: 80%;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: space-evenly;
        background-image: linear-gradient(
          90deg,
          rgba(58, 119, 229, 0) 0%,
          rgba(58, 119, 229, 0.5) 49%,
          rgba(58, 119, 229, 0) 100%
        );

        .evt-index-item {
          display: flex;
          align-items: baseline;
          .index-label {
            font-family: PingFangSC-Regular;
            font-size: 12px;
            color: #e8f3ff;
            letter-spacing: 0;
            text-align: right;
            line-height: 18px;
            font-weight: 400;
            margin-right: 5px;
          }

          .index-value {
            font-family: DINAlternate-Bold;
            font-size: 20px;
            letter-spacing: 0;
            font-weight: 700;
          }
        }
      }
    }

    .evt-type-index {
      display: flex;
      align-items: center;
      justify-content: space-evenly;

      > div {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      .index-label {
        margin-left: 12px;
      }

      .index-value {
        font-size: 20px;
        letter-spacing: 0;
        text-shadow: 0 3px 3px #003f64;
        font-weight: 700;
        margin-right: 12px;
      }

      .evt-intell-evt {
        width: 135px;
        height: 40px;
        font-family: PingFangSC-Medium;
        font-size: 14px;
        color: #ffffff;
        text-shadow: 0 2px 4px #4f9fff;
        font-weight: 500;
        background: url("./img/bg_evt_type_1.png") 100% 100% no-repeat;
        background-size: 100% 100%;
      }

      .evt-link {
        width: 88px;
        height: 50px;
        background: url("./img/icon_evt_type_c.svg") no-repeat;
        background-size: cover;
      }

      .evt-person-evt {
        width: 128px;
        height: 40px;
        font-family: PingFangSC-Medium;
        font-size: 14px;
        color: #4f9fff;
        font-weight: 500;
        background: url("./img/bg_evt_type.png") 100% 100% no-repeat;
        background-size: 100% 100%;
      }
    }
  }

  .evt-type-cnt-wrap {
    margin-top: 6px;

    .index-list-wrap {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      flex-wrap: wrap;
      height: 100%;

      .index-list-item {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: @color;
        padding: 0;

        .index-label {
          font-size: 14px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: auto;

          .icon-yuan {
            width: 5px;
            height: 5px;
            margin: 0 6px;
            display: inline-block;
            transform: rotate(45deg);
            border: 2px solid #9e70dd;

            &:before {
              content: "";
            }
          }
        }

        .index-value {
          display: flex;
          align-items: center;
          justify-content: center;

          .index-value-num {
            display: flex;
            align-items: center;
            justify-content: center;

            .num {
              width: 40px;
              font-size: 20px;
              text-align: right;
            }

            .unit {
              margin-left: 6px;
              font-family: PingFangSC-Regular;
              font-size: 14px;
              color: rgba(255, 255, 255, 0.58);
              letter-spacing: 0;
              text-align: right;
              font-weight: 400;
            }
          }

          .index-value-percent {
            display: flex;
            width: 50px;
            height: 30px;
            font-size: 20px;
            line-height: 30px;
            text-shadow: 0 4px 4px #003f64;
            margin-left: 4px;
            align-items: center;
            justify-content: flex-start;

            &::before {
              content: "";
              display: flex;
              width: 2px;
              height: 12px;
              background-color: #8b8b8b;
              margin-right: auto;
            }
          }
        }

        &:nth-child(1) {
          .index-label {
            .icon-yuan {
              border: 2px solid #ed5158;
            }
          }
        }

        &:nth-child(2) {
          .index-label {
            .icon-yuan {
              border: 2px solid #ffa940;
            }
          }
        }

        &:nth-child(3) {
          .index-label {
            .icon-yuan {
              border: 2px solid #15bd94;
            }
          }
        }

        &:nth-child(4) {
          .index-label {
            .icon-yuan {
              border: 2px solid #4f9fff;
            }
          }
        }

        &:nth-child(5) {
          .index-label {
            .icon-yuan {
              border: 2px solid #1fddff;
            }
          }
        }
      }
    }
  }
}
