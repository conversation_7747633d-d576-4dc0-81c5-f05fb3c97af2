<!--
 * @Description: 用于展示设施统计数据的组件
 * @Autor: he.chao
 * @Date: 2022-10-19 15:07:55
 * @LastEditors: liu.yongli
 * @LastEditTime: 2023-12-13 14:36:14
-->
<template>
  <div class="facility-stat-wrap">
    <!-- 使用IconIndex组件循环展示索引列表中的每一项 -->
    <IconIndex
      v-for="(item, index) in indexList"
      :key="`facility_stat_${index}`"
      :prop-data="{
          ...item
      }"
    />
  </div>
</template>

<script>
import { getThemeCompByCode } from '@/api/service/fullviewService' // 获取主题组件配置
import IconIndex from '@/components/page/ScreenPages/FullViewMonitor/IconIndex/index.vue' // 图标索引组件
import { defaultParam } from './defaultCompParam' // 默认参数
import { FACILITY_STATData } from '@/components/page/ScreenPages/FullViewMonitor/mockData' // 设施统计数据

export default {
  components: { IconIndex },
  data() {
    return {
      // 存储索引列表数据
      indexList: [],
    }
  },
  props: {},
  mounted() {
    // 页面挂载后查询主题组件配置
    this.qryThemeCompByCode()
  },
  methods: {
    /**
     * 根据组件代码查询主题组件配置
     * @async
     * @returns {void}
     */
    async qryThemeCompByCode() {
      // 查询
      const [, resData] = await getThemeCompByCode({
        compCode: 'FACILITY_STAT',
      })
      const data = resData ?? FACILITY_STATData.data
      // 如果查询成功且返回数据，则将查询结果赋值给索引列表
      if (data?.compParamList) {
        const { compParamList } = data
        this.indexList =
          compParamList && compParamList.length > 0
            ? compParamList
            : defaultParam
      }
    },
  },
}
</script>
<style lang='less' src='./index.less' />
