<!--
 * @Description: 该组件用于在左侧窗口中显示内容。
 * @Autor: he.chao
 * @Date: 2022-10-19 15:07:55
 * @LastEditors: liu.yongli
 * @LastEditTime: 2025-07-07 17:30:31
-->
<template>
  <div v-if="layoutCfgLeft" id="left-side-window-ins" class="left-side-window">
    <template v-for="(item, index) in componentList">
      <RemoteComponentSyncLoader
        v-if="item.monitorComp"
        :key="'leftCompenent_'+index"
        :config="getleftRightComponents(item.compCode)"
        :provinceId="provinceId"
        :cityId="cityId"
        :intervalTime="120000"
        :title="item.compName"
        :outerWidth="item.defaultWidth ? Number(item.defaultWidth) : 0"
        :outerHeight="item.defaultHeight ? Number(item.defaultHeight) : 0"
        :compUrl="item.compUrl"
      />
      <template v-else-if="!item.monitorComp && !alreadyInitFullViewComp[item.compCode]">
        <component
          :key="'leftCompenent_'+index"
          :is="getComponentByCode(item)"
          v-bind="getProps(item)"
        />
      </template>
      <!-- {{ !item.monitorComp && !alreadyInitFullViewComp[item.compCode] ? initFullViewComp(item, 'left-side-window-ins') : null }} -->
    </template>
  </div>
</template>

<script>
import CompCfg from '@/components/page/ScreenPages/FullViewMonitor/CompCfg'
import RemoteComponentSyncLoader from '@ct/remote-page-sync-loader/remote-page-sync-loader.umd.js'

/**
 * @description: 左侧窗口组件
 * @module: LeftSideWindow
 */
export default {
  components: {
    RemoteComponentSyncLoader,
  },
  // 空对象表示该组件不接收任何父组件传递的属性
  props: {
    componentList: {
      type: Array,
      default: () => [],
    },
    leftRightComponents: {
      type: Object,
      default: {},
    },
  },
  /**
   * @description: 组件数据
   * @returns {Object} contentWidows - 内容窗口的对象，用于存储和管理窗口内容
   */
  data() {
    return {
      contentWidows: {},
      layoutCfgLeft: false,
      alreadyInitFullViewComp: {}, // 已经初始化的全景展示组件
    }
  },
  computed: {
    // 获取数据
    getleftRightComponents() {
      return key => this.leftRightComponents[key]
    },
    getComponentByCode() {
      return item => CompCfg.getComponentByCode(item)
    },
    getProps() {
      return value => {
        return { propData: value }
      }
    },
  },
  watch: {
    // 监听 window.layoutCfg 的变化，并根据layoutCfg配置加载左侧窗口的组件
    componentList: {
      handler(newVal, oldVal) {
        if (newVal !== oldVal && newVal.length > 0) {
          this.$forceUpdate()
        }
      },
      deep: true,
    },
  }, // 空对象表示该组件不监听任何数据变化
  /**
   * @description: 组件挂载后执行的函数
   * @remarks: 在组件挂载到DOM后，通过$nextTick确保DOM更新完毕，再根据layoutCfg配置加载左侧窗口的组件
   */
  mounted() {
    // 如果layoutCfg及其中的left配置存在，则遍历left数组中的每个项
    this.$nextTick(() => {
      // this.initPage()
      this.layoutCfgLeft = true
      this.$forceUpdate()
    })
  },
  methods: {
    // 初始化全景展示组件到dom上
    initFullViewComp(item, domId) {
      const div = document.getElementById(domId)
      if (!div) {
        setTimeout(() => {
          this.initFullViewComp(item, domId)
        }, 1000)
        return
      }
      CompCfg.getComponent({
        code: item.compCode,
        wrapper: domId,
        props: item,
      })
      this.alreadyInitFullViewComp[item.compCode] = true
    },
    initPage() {
      this.componentList?.map(item => {
        // 通过CompCfg获取组件配置，并传入组件代码和容器元素
        CompCfg.getComponent({
          code: item.compCode,
          wrapper: 'left-side-window-ins',
          props: item || {},
        })
      })
    },
  },
}
</script>

<style lang='less' src='./index.less' scoped />