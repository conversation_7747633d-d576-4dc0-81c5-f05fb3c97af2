<!--
 * @Description:
 * @Autor: he.chao
 * @Date: 2022-10-19 15:07:55
 * @LastEditors: liu.yongli
 * @LastEditTime: 2023-12-13 14:36:14
-->
<template>
  <div class="filters-wrap">
    <!-- 日期选择器，用于选择告警时间范围 -->
    <el-date-picker
      v-model="selectedEvtTime"
      type="daterange"
      range-separator="至"
      start-placeholder="开始日期"
      end-placeholder="结束日期"
      align="left"
      @change="evtTimeChange"
      :picker-options="pickerOptions"
    ></el-date-picker>
    <!-- 多选组件，用于选择告警来源 -->
    <MuiltiSelect
      placeholder="告警来源"
      :selectedAll="true"
      :ds="dicEvtSrc"
      :reset="reset"
      :okHandle="evtSrcChange"
    />
    <!-- 多选组件，用于选择告警状态 -->
    <MuiltiSelect
      placeholder="告警状态"
      :selectedAll="true"
      :ds="dicEvtStat"
      :reset="reset"
      :okHandle="evtStatChange"
    />
    <div class="title alarm-title">
      <!-- 下拉菜单，用于选择告警类型 -->
      <el-dropdown
        ref="typeDrop"
        trigger="click"
        :class="dropdownShow.alarmTypeShow ? 'titleSelected' : ''"
        @visible-change="dropdownChangeType"
      >
        <span
          class="el-dropdown-link"
          :class="evtTypeListCopy.find(o => o.checked) ? 'title-selected' : ''"
        >
          告警类型
          <i
            :class="`el-icon--right ${dropdownShow.alarmTypeShow ? 'el-icon-arrow-up' : 'el-icon-arrow-down'}`"
          />
        </span>
        <el-dropdown-menu
          slot="dropdown"
          class="drop-down-select alarm-type-dropdown-menu iwhale-speciesLYstyle"
        >
          <div class="input-bg">
            <div class="input-box">
              <!-- 标签显示已选择的告警类型 -->
              <el-tag
                v-if="evtTypeListCopy.find(o => o.checked)"
                closable
                class="first-tag"
                @close="itemClick('evtTypeListCopy',evtTypeListCopy.find(o => o.checked))"
              >{{ evtTypeListCopy.find(o => o.checked).label }}</el-tag>
              <!-- 显示多余的选中项数量 -->
              <el-tag
                v-if="evtTypeListCopy.filter(o => o.checked).length > 1"
                class="more-tag"
              >+{{ evtTypeListCopy.filter(o => o.checked).length - 1 }}</el-tag>
              <div style="flex: 1">
                <!-- 输入框用于搜索告警类型 -->
                <el-input
                  v-model="keywordValue"
                  class="search-input"
                  clearable
                  :placeholder="evtTypeListCopy.filter(o => o.checked).length ? '' : '输入关键字'"
                  @clear="clearTypeListShow()"
                />
              </div>
              <div class="searchTypeDiv right_line" @click="searchChange">
                <i class="el-input__icon el-icon-search" style="font-size: 18px;color:aliceblue;" />
              </div>
            </div>
          </div>
          <div class="select-container" style="max-width: 300px">
            <!-- 循环显示告警类型选项 -->
            <div
              v-for="(item, index) in evtTypeListShow"
              :key="`type_${index}`"
              :class="`select-item ${item.checked ? 'selectedType' : ''}`"
            >
              <div style="display: flex;cursor: pointer;">
                <!-- 复选框用于选择告警类型 -->
                <el-checkbox
                  style="margin-right: 10px"
                  :value="item.checked"
                  @click.stop
                  @change="itemClick('evtTypeListCopy', item)"
                >{{ item.label }}</el-checkbox>
              </div>
            </div>
          </div>
          <div class="judgeBtnDiv">
            <!-- 确定按钮，应用选择的告警类型 -->
            <el-button type="primary" @click="evtTypeChange(2)">确定</el-button>
            <!-- 重置按钮，清空选择的告警类型 -->
            <el-button
              @click="()=>{
                this.evtTypeListCopy = this.evtTypeListCopy.map(item => {
                  item.checked = false
                  return item;
                });
                this.evtTypeListShow = [...this.evtTypeListCopy];
                this.keywordValue = '';
              }"
            >重置</el-button>
          </div>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </div>
</template>
<script>
import commonService from '@/api/service/common'
import { findOrderAlarm } from '@/api/service/fullviewService'
import DicConst from '@/components/page/ScreenPages/FullViewMonitor/enums/DicConst'
import MuiltiSelect from '@/components/page/ScreenPages/FullViewMonitor/Filters/MuiltiSelect.vue'
import dayjs from 'dayjs'
import { mapMutations } from 'vuex'
export default {
  components: { MuiltiSelect },
  data: function () {
    return {
      selectedEvtTime: [], // 选中的告警时间范围
      selectedEvtStat: '', // 选中的告警状态
      selectedEvtSrc: '', // 选中的告警来源
      selectedEvtType: '', // 选中的告警类型
      dropdownShow: {
        alarmTypeShow: false, // 告警类型下拉菜单是否显示
      },
      keywordValue: '', // 搜索关键字
      dicEvtSrc: [], // 告警来源字典数据
      dicEvtStat: [], // 告警状态字典数据
      dicEvtType: [], // 告警类型字典数据
      evtTypeListCopy: [], // 告警类型列表的副本
      evtTypeListShow: [], // 显示的告警类型列表
      pickerOptions: {
        // 日期选择器的选项
        disabledDate(time) {
          dayjs().subtract(6, 'M')
          return (
            time.getTime() > Date.now() ||
            dayjs(time.getTime()) < dayjs().subtract(6, 'M')
          )
        },
        shortcuts: [
          {
            text: '今日',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: '3日内',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 3)
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: '7日内',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            },
          },
        ],
      },
    }
  },
  mounted() {
    // 组件挂载时初始化过滤条件
    this.initFilter()
  },
  beforeDestroy() {
    // 组件销毁前重置过滤条件
    this.setEvtFilter({
      warningTypeIdList: [],
      orderStatusList: [],
      warningSourceList: [],
      alarmTimeStart: null,
      alarmTimeEnd: null,
    })
  },
  methods: {
    ...mapMutations('event', ['setEvtFilter']),
    /**
     * 格式化字典数据
     */
    formatDictData(data) {
      return data.map(item => {
        return {
          label: item.codeName,
          value: item.dicValue,
        }
      })
    },
    /**
     * 异步获取字典代码，包括告警来源和告警状态
     * 该方法通过调用commonService来获取字典列表，分别对应告警来源和告警状态
     * 如果获取成功，将字典数据映射为label-value对的形式存储在对应的属性中
     */
    async getDicCode() {
      // 同时发起两个异步请求获取告警来源和告警状态的字典数据
      const req = [
        await commonService.getDictListByCatCode(
          DicConst.DIC_CODE.ALARM_SOURCE
        ),
        await commonService.getDictListByCatCode(
          DicConst.DIC_CODE.ALARM_STATUS
        ),
      ]
      // 等待所有请求完成
      const reps = await Promise.all(req)
      // 处理告警来源字典数据
      if (reps[0]) {
        const { code, data } = reps[0]
        // 如果返回码不是200或数据为空，则不处理
        if (code !== 200 || !data || data.length < 1) {
          return
        }
        // 将告警来源数据映射为label-value对的形式
        this.dicEvtSrc = this.formatDictData(data)
      }
      // 处理告警状态字典数据
      if (reps[1]) {
        const { code, data } = reps[1]
        // 如果返回码不是200或数据为空，则不处理
        if (code !== 200 || !data || data.length < 1) {
          return
        }
        // 将告警状态数据映射为label-value对的形式
        this.dicEvtStat = this.formatDictData(data)
      }
    },
    /**
     * 初始化过滤条件，包括获取字典数据和订单告警信息
     * 该方法首先调用getDicCode方法获取字典数据，然后获取订单告警信息，并根据这些信息初始化过滤条件
     */
    async initFilter() {
      await this.getDicCode()
      // 获取订单告警信息
      const reps = await findOrderAlarm()
      const [success, data] = reps
      // 如果获取失败或数据为空，则不处理
      if (!success || !data || data.length < 1) {
        return
      }
      // 将告警类型数据映射为label-value对的形式，并初始化相关属性
      const res = []
      data.map(item => {
        res.push({
          label: item.typeName,
          value: item.typeValue,
        })
      })
      this.dicEvtType = [...res]
      this.evtTypeListCopy = [...res]
      this.evtTypeListShow = [...res]
    },
    /**
     * 处理项的点击事件
     * 根据点击的项的状态，更新该项的选中状态
     * @param {string} key - 相关属性的键名
     * @param {object} item - 被点击的项
     */
    // 告警状态/告警来源/告警类型
    itemClick(key, item) {
      this[key] = this[key].map(o => {
        if (o.value === item.value) {
          o.checked = !o.checked
        }
        return o
      })
    },
    /**
     * 处理告警类型下拉菜单显示状态的变化
     * @param {boolean} show - 下拉菜单的显示状态
     */
    dropdownChangeType(show) {
      this.dropdownShow = {
        ...this.dropdownShow,
        alarmTypeShow: show,
      }
    },
    /**
     * 处理搜索框内容变化的事件
     * 根据当前的搜索关键字，更新显示的告警类型列表
     */
    searchChange() {
      this.evtTypeListShow = []
      this.evtTypeListShow = this.evtTypeListCopy.filter(
        o => o.label.indexOf(this.keywordValue) !== -1
      )
    },
    /**
     * 重置告警类型列表的显示
     * 将显示列表重置为原始列表的副本
     */
    clearTypeListShow() {
      this.evtTypeListShow = [...this.evtTypeListCopy]
    },
    /**
     * 处理告警类型选择的变化
     * 根据当前选中的告警类型，更新过滤条件，并隐藏告警类型下拉菜单
     */
    evtTypeChange(val) {
      const selected = []
      this.evtTypeListCopy.map(item => {
        if (item.checked) {
          selected.push(item.value)
        }
      })
      this.setEvtFilter({
        warningTypeIdList: selected,
      })
      this.$refs.typeDrop.hide()
    },
    /**
     * 处理告警状态选择的变化
     * 根据当前选中的告警状态，更新过滤条件

 * @param {array} val - 当前选中的告警状态项
     */
    evtStatChange(val) {
      const selected = val.map(item => {
        return item
      })
      this.setEvtFilter({
        orderStatusList: selected,
      })
    },
    /**
     * 处理告警来源选择的变化
     * 根据当前选中的告警来源，更新过滤条件
     * @param {array} val - 当前选中的告警来源项
     */
    evtSrcChange(val) {
      const selected = val.map(item => {
        return item
      })
      this.setEvtFilter({
        warningSourceList: selected,
      })
    },
    /**
     * 处理告警时间选择的变化
     * 根据当前选中的告警时间范围，更新过滤条件
     * @param {array} val - 当前选中的告警时间范围
     */
    evtTimeChange(val) {
      this.setEvtFilter({
        alarmTimeStart: val && val[0],
        alarmTimeEnd: val && val[1],
      })
    },
    /**
     * 重置过滤条件
     * 目前此方法为空，预留用于未来实现重置过滤条件的逻辑
     */
    reset() {
      console.log('reset')
    },
  },
}
</script>
<style lang='less' src='./index.less' />
