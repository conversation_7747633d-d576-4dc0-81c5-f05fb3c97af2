@import "../common";

@top: 90px;
.filters-wrap {
  display: flex;
  position: absolute;
  align-items: center;
  justify-content: flex-start;
  right: 300px;
  top: @top;
  background-color: @background-color;
  border-radius: 4px;

  .el-date-editor--daterange.el-input__inner {
    width: 220px;
  }
  .el-select {
    margin: 0;
    width: 140px;
    display: flex;
    align-items: center;

    .el-select__tags-text {
      max-width: 40px !important;
    }
    &::before {
      content: "";
      width: 3px;
      height: 20px;
      border-radius: 2px;
      background-color: #ffffff80;
    }
  }

  .el-date-editor {
    margin: 0;
  }


  .title {
    padding: 5px;
    position: relative;
    flex: 1;
    justify-content: center;
    display: flex;
    align-items: center;
    width: 100px;

    .title {
      &::before {
        content: '';
        width: 1px;
        height: 10px;
        background: rgba(232, 243, 254, 0.35);
        position: absolute;
        left: 0;
        top: calc(50% - 5px);
      }
    }
  }

  .alarm-title {
    .el-dropdown {
      line-height: 26px;
      flex: 1;
      border-radius: 5px;
      cursor: pointer;
      color: #c0c4cc;

      &:hover {
        background: @background-color;
      }

      // 覆盖dropdown的箭头
      .el-icon-arrow-up {
        transform: rotate(180deg);
        background-image: url('~@/assets/images/alarmEvent/alarm/arrow_up2.png');
        background-size: 18px;
        background-position: center;
        background-repeat: no-repeat;
        width: 18px;
        height: 18px;
        margin-left: 0;
        vertical-align: sub;

        &::before {
          content: '';
        }
      }

      .el-icon-arrow-down {
        background-image: url('~@/assets/images/alarmEvent/alarm/arrow_up2.png');
        background-size: 18px;
        background-position: center;
        background-repeat: no-repeat;
        width: 18px;
        height: 18px;
        margin-left: 0;
        vertical-align: sub;

        &::before {
          content: '';
        }
      }
    }
  }

  .alarm-title .title-selected {
    color: #409eff !important;

    .el-icon-arrow-up,
    .el-icon-arrow-down {
      background-image: url('~@/assets/images/alarmEvent/alarm/arrow_up.png');
    }
  }
  .el-date-editor--daterange.el-input__inner{
    background: transparent;
  }
}

.iwhale-speciesLYstyle {
  //transform: translate(120px, 0); // 对齐最右侧。要求的位置不在dropdown范围内
  .select-container {
    max-height: 400px;
    overflow: auto;
    border: none;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;

    .select-item {
      padding: 6px 6px;
      color: white;

      &:hover {
        background: rgba(130, 181, 255, 0.1);
      }

      .el-checkbox {
        color: white;
      }
    }

    .select-input-item {
      width: 240px;
      padding: 0px 6px;
      margin-top: 10px;

      .el-input__inner {
        background-color: #3b5474 !important;
      }

    }

    .collapseTitle {
      display: flex;
      align-items: center;
      height: 20px;
      line-height: 20px;
      flex-grow: 1;
      width: 0;
      cursor: default;

      > span {
        display: flex;
        align-items: center;
        padding: 0 5px;
        color: #e8f3fe;
        background: #1373e6 100%;
        border-radius: 4px;

        + span {
          margin-left: 6px;
        }
      }

      .collapseTitleName {
        max-width: 70%;

        span {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .collapseTitleClose {
          cursor: pointer;
          flex-shrink: 0;
          height: 18px;
          width: 18px;
          background: url('~@/assets/images/alarmEvent/alarm/del.png');
        }
      }
    }
  }

  .select-container .selectedType,
  .select-container .selected {
    color: #409eff;
  }


  .input-bg {
    width: 300px;
    padding: 6px 6px 0 6px;
    border-radius: 8px 8px 0 0;

    .input-box {
      padding: 3px;
      background: @background-color;
      display: flex;
      flex-wrap: nowrap;
      justify-content: left;
      align-items: center;
      border-radius: 3px;

      .el-input__inner {
        height: 24px;
      }
    }

    .select-container {
      border-top-left-radius: 0;
      border-top-right-radius: 0;
      max-height: 367px;
    }

    .more-tag,
    .first-tag {
      border: none;
      max-width: 100px;
      position: relative;
      overflow: hidden;
      text-overflow: ellipsis;
      padding: 1px 8px;

      .el-tag__close.el-icon-close {
        height: 18px;
        width: 18px;
        margin: 0px auto;
        position: absolute;
        top: 3px;
        right: 1px;
      }
    }

    .more-tag {
      margin-left: 4px;
    }

    .first-tag {
      padding-right: 11px !important;
    }

    ::v-deep input:disabled::-webkit-input-placeholder {
      -webkit-text-fill-color: rgba(255, 255, 255, 0);
    }

    .search-input {
      .el-input__clear {
        line-height: 24px;
        color: #e8f3fe;
      }

      .el-input__inner {
        color: #e8f3fe;
        text-align: left;
        border-radius: 3px;
        font-size: 14px;
        padding-right: 10px !important;
        border: none;
        background: transparent;
      }
    }

    .el-button--mini {
      width: 30px;
      height: 30px;
      color: #e8f3fe;
      font-size: 14px;
      outline: none;
      cursor: pointer;
      padding: 0;
      position: absolute;
      right: 0;
      top: 0;
      background: none;
      border: none;
    }

    .searchTypeDiv {
      cursor: pointer;

      .el-input__icon {
        line-height: 20px;
      }
    }
  }

  .el-checkbox-group {
    display: flex;
    flex-direction: column;
  }

  .judgeBtnDiv {
    display: flex;
    justify-content: flex-end;
    flex-wrap: nowrap;
    width: 95%;
    padding: 6px;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;

    .el-button {
      padding: 4px 10px;
      height: 24px;
      color: @color;
      background-color: @background-color;
      border-color: @border-color;
    }

    .el-button--primary {
      background-color: @theme-color !important;
      border-color: @theme-color !important;
    }
  }
}

.drop-down-select {
  padding: 0 !important;
  margin: 9px 0 0 0 !important;
  background: linear-gradient(180deg, rgba(0, 19, 30, 0.74) 0%, #00131E 100%);
  border-radius: 8px;
  border: none;

  .popper__arrow {
    display: none;
  }
}
