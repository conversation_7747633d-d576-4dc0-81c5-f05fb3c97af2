<template>
  <div class='title alarm-title'>
    <!-- 下拉菜单组件，用于选择告警状态 -->
    <el-dropdown
      ref='muiltiSelect'
      trigger='click'
      placement='bottom-start'
      :class="dropdownShow? 'titleSelected' : ''"
      @visible-change='dropdownChanges'
    >
        <!-- 菜单触发按钮，显示当前选中的状态或占位符 -->
        <span
          class='el-dropdown-link'
          :class="selectedItem.length>0 ? 'title-selected' : ''"
          @click='dropdownChanges'
        >
            {{ placeholder }}
            <i
              :class="`${dropdownShow ? 'el-icon--right el-icon-arrow-up' : 'el-icon--right el-icon-arrow-down'}`"
            />
        </span>
      <!-- 下拉菜单内容，包含全部选项和具体状态选项 -->
      <el-dropdown-menu slot='dropdown' class='drop-down-select iwhale-speciesLYstyle'>
        <!-- 选择容器，用于布局和样式控制 -->
        <div class='select-container' style='min-width: 178px'>
          <!-- 全部选项，用于选中所有状态或取消所有状态 -->
          <el-checkbox
            v-model='isStatusAllSelect'
            style='width: calc(100% - 12px)'
            class='select-item'
            :indeterminate='!isStatusAllSelect && selectedItem.length > 0'
            @change='(val)=>{
                this.selectedItem = val ? [...(this.ds.map(i => i.value))] : []
              }'
          >
            全部
          </el-checkbox>
          <!-- 状态选项组，用户可以选择具体的告警状态 -->
          <el-checkbox-group
            v-model='selectedItem'
            @change='()=>{
                  const len = this.selectedItem.length
                  this.isStatusAllSelect = len > 0 && len === this.ds.length
                }'
          >
            <el-checkbox
              v-for='(item, index) in ds'
              :key='index'
              class='select-item'
              :label='item.value'
            >
              {{ item.label }}
            </el-checkbox>
          </el-checkbox-group>
        </div>
        <!-- 操作按钮区域，包含确定和重置按钮 -->
        <div class='judgeBtnDiv'>
          <!-- 确定按钮，用于确认当前选择并触发回调 -->
          <el-button
            type='primary'
            class='judgeFalseBtn'
            @click='()=>{
              this.$refs.muiltiSelect.hide()
              okHandle(selectedItem);
            }'
          >
            确定
          </el-button>
          <!-- 重置按钮，用于取消所有选择并重置为默认状态 -->
          <el-button
            class='judgeCloseBtn'
            @click='()=>{
                this.selectedItem = [...(ds.map(i => i.value))]
                this.dropdownShow=false
                this.isStatusAllSelect = selectedAll;
                this.reset();
              }'
          >重置
          </el-button>
        </div>
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>

<script>
export default {
  props: {
    placeholder: {
      type: String,
      default: '请选择'
    },
    // 告警状态改变的回调函数
    dropdownChangeStatus: {
      type: Function,
      default: function() {
        console.log('dropdownChangeStatus');
      }
    },
    selectedAll: {
      type: Boolean,
      default: false
    },
    // 告警状态选项的数据源
    ds: {
      type: Array,
      default: function() {
        return [
          {
            label: '全部',
            value: ''
          }
        ];
      }
    },
    // 重置选择的回调函数
    reset: {
      type: Function,
      default: function() {
        console.log('reset');
      }
    },
    // 确认选择的回调函数
    okHandle: {
      type: Function,
      default: function() {
        console.log('okHandle');
      }
    }
  },
  data: function() {
    return {
      // 是否全选告警状态的标志
      isStatusAllSelect: this.selectedAll,
      // 当前选中的告警状态
      selectedItem: [],
      // 下拉菜单是否显示
      dropdownShow: false
    };
  },
  watch: {
    // 监听数据源的变化，更新选中状态
    ds: {
      handler(val) {
        this.selectedItem = this.selectedAll ? [...(val.map(i => i.value))] : [];
      },
      deep: true
    }
  },
  methods: {
    // 控制下拉菜单显示和隐藏的函数
    dropdownChanges(val) {
      this.dropdownShow = val;
    }
  }
};
</script>

<style lang='less' src='./index.less' />
