.fullview-detail-info-wrap.evt-top-location-wrap {
  width: 366px;

  .top-location-wrap {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-evenly;
    padding-bottom: 10px;
    height: 100%;

    .list-item {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: flex-start;

      .item-info {
        font-family: PingFangSC-Medium;
        font-size: 14px;
        color: #ffffff;
        line-height: 20px;
        font-weight: 500;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .info-label {
          width: 70%;
        }

        .value {
          font-size: 20px;
        }

        .unit {
          font-family: PingFangSC-Regular;
          font-size: 12px;
          color: rgba(255, 255, 255, 0.58);
          letter-spacing: 0;
          text-align: right;
          line-height: 18px;
          font-weight: 400;
          margin-left: 6px;
        }
      }

      .percent {
        width: 100%;
        height: 10px;
        background: url("./img/jindutiao_bg.png") 100% 100% no-repeat;
        background-size: 100% 100%;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        box-sizing: border-box;
        padding: 0 5px;

        .percent-bar {
          width: 100%;
          height: 5px;
          background-image: linear-gradient(270deg, #4dece4 0%, #008cff 100%);
        }
      }

      &:not(:first-child) {
        margin-top: 12px;
      }

      &:nth-child(1) {
        .percent {
          .percent-bar {
            background-image: linear-gradient(270deg, #ff4052 0%, #9e0311 100%);
          }
        }
      }

      &:nth-child(2) {
        .percent {
          .percent-bar {
            background-image: linear-gradient(270deg, #ffa940 0%, #a96007 99%);
          }
        }
      }
    }
  }
}
