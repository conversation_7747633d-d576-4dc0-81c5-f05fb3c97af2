<template>
  <!-- 组件DetailInfo用于展示详细信息，通过class和style属性控制其样式和布局 -->
  <!-- 使用v-loading指令控制加载状态，通过插槽分别定义标题图标、标题和详细内容 -->
  <DetailInfo
    class="evt-top-location-wrap"
    v-loading="loading"
    :style="`min-width: ${changePxToRem(minWidth)};min-height: ${changePxToRem(minHeight)};max-width: ${changePxToRem(maxWidth)};max-height: ${changePxToRem(maxHeight)};height: ${changePxToRem(maxHeight)}`"
  >
    <template v-slot:title-icon>
      <!-- 使用svg-icon组件显示标题图标 -->
      <svg-icon svg-name="icon_event_view" />
    </template>
    <template v-slot:title>
      <!-- 显示组件名称 -->
      {{ compName }}
    </template>
    <template v-slot:detail-content>
      <!-- 定义详细内容的布局，展示每个区域的事件数量和占比 -->
      <div class="top-location-wrap">
        <div v-for="item in evtTop" class="list-item" :key="item.regionName">
          <div class="item-info">
            <span class="info-label over-flow-hidden">{{ item.regionName }}</span>
            <div class="info-value">
              <span class="value number-font">{{ formatVal(item.num) }}</span>
              <span class="unit">起</span>
            </div>
          </div>
          <div class="percent">
            <i class="percent-bar" :style="`width: ${item.percent}%`" />
          </div>
        </div>
      </div>
    </template>
  </DetailInfo>
</template>

<script>
import {
  getEventNumByArea,
  getThemeCompByCode,
  getStatisEventByArea,
} from '@/api/service/fullviewService' // 接口
import DetailInfo from '@/components/page/ScreenPages/FullViewMonitor/DetailInfo' // 详情
import { mapGetters } from 'vuex' // Vuex
import { formatVal } from '../../../../common/utils' // 工具
import dayjs from 'dayjs' // 时间
import { evtTop5Data } from '@/components/page/ScreenPages/FullViewMonitor/mockData' // mock数据
import { getFullViewEvtParams } from '@/components/common/utils' // 工具
import _ from 'lodash' // 工具

/* 组件定义，用于展示事件高发地的前五名 */
export default {
  computed: {
    /* 从 Vuex 中获取选中的区域信息 */
    selectedRegion() {
      return this.$store.state.map.selectedRegion
    },
    // 筛选按钮状态
    evtFilterSwitch() {
      return this.$store.state.map.evtFilterSwitch
    },
    // vuex
    ...mapGetters('map', ['formatSelectedRegion']),
    // px转rem
    changePxToRem() {
      return val => this.pxToRem(+(val ? val.replace('px', '') : val))
    },
  },
  watch: {
    selectedRegion: {
      /* 当选中区域变化时，更新事件数据 */
      handler: function (newVal) {
        if (newVal) {
          // 事件筛选过滤
          this.onEventFilterChange({
            filterState: this.filterState,
          })
        }
      },
    },
    // 监听筛选按钮状态
    evtFilterSwitch: {
      handler: function (newVal) {
        if (!newVal) {
          this.filterState = ''
          // 事件筛选过滤
          this.onEventFilterChange()
        }
      },
    },
  },
  components: {
    DetailInfo,
  },
  data: function () {
    return {
      evtTop: [] /* 存储事件数据 */,
      loading: true /* 控制加载状态 */,
      minWidth: '366px', // 最小宽度
      minHeight: '316px', // 最小高度
      maxWidth: '366px', // 最大宽度
      maxHeight: '316px', // 最大高度
      compName: '事件高发地top5', // 组件名称
      filterState: '', // 工具箱事件筛选的状态
    }
  },
  mounted() {
    /* 初始化时获取组件配置并加载事件数据 */
    this.qryThemeCompByCode(this.qryEvtTop5)
    this.$EventBus.$on('onEventFilterChange', this.onEventFilterChange)
  },
  beforeDestroy() {
    this.$EventBus.$off('onEventFilterChange')
  },
  methods: {
    formatVal,
    /**
     * 事件筛选过滤条件回调处理
     */
    onEventFilterChange(e = {}) {
      this.filterState = e.filterState || ''
      this.qryEvtTop5()
    },
    /* 异步获取事件数据并更新evtTop */
    async qryEvtTop() {
      // 获取事件数据
      const [success, data] = await getEventNumByArea({
        ...this.formatSelectedRegion,
        dimensionFlag: 1,
      })
      let res = []
      // 判断
      if (success && data && data.length > 0) {
        // 取值
        const cnt = _.sumBy(data, function (o) {
          return o.num ? o.num : 0
        })
        // 计算百分比
        res = data.map(item => {
          return {
            regionName: item.regionName,
            num: item.num,
            percent: (item.num / cnt).toFixed(2) * 100, // 百分比
          }
        })
      }
      this.evtTop = res
    },
    /* 异步获取事件数据并更新evtTop */
    async qryEvtTop5() {
      // 获取数据
      const [, resData] = await getStatisEventByArea({
        // 默认查半年
        startDate: dayjs().subtract(6, 'months').format('YYYY-MM-DD 00:00:00'),
        endDate: dayjs().format('YYYY-MM-DD 23:59:59'),
        ...getFullViewEvtParams(this.filterState), // 筛选条件
      })
      // 测试数据
      const data = resData ?? evtTop5Data.data
      let res = []
      // 判断
      if (data?.length > 0) {
        // 取值
        const cnt = _.sumBy(data, function (o) {
          return o.num ? o.num : 0
        })
        // 计算百分比
        res = data.map(item => {
          return {
            regionName: item.countyName,
            num: item.num,
            percent: (item.num / cnt).toFixed(2) * 100, // 百分比
          }
        })
      }
      this.evtTop = res
    },
    /* 异步获取组件配置并更新组件大小和名称 */
    async qryThemeCompByCode(
      call = () => {
        console.log('qryThemeCompByCode')
      }
    ) {
      // 获取组件配置
      const [success, data] = await getThemeCompByCode({
        compCode: 'EVENT_AREA_STAT_TOP5',
      })
      // 判断
      if (success && data) {
        const { minWidth, minHeight, maxWidth, maxHeight, compName } = data
        this.minWidth = minWidth || '366' // 最小宽度
        this.minHeight = minHeight || '316' // 最小高度
        this.maxWidth = maxWidth || '366' // 最大宽度
        this.maxHeight = maxHeight || '316' // 最大高度
        this.compName = compName || '事件高发地点TOP5' // 组件名称
      }
      this.loading = false
      call() // 调用
    },
  },
}
</script>

<style lang='less' src='./index.less' />
