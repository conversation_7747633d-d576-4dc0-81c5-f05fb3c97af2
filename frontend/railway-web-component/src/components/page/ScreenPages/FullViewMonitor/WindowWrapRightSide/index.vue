<template>
  <!-- 右侧窗口容器 -->
  <div class="right-side-window-wrap">
    <!-- 控制栏容器 -->
    <div class="ctl-wrap">
      <!-- 插入槽用于自定义控制按钮 -->
      <slot name="ctls" />
      <!-- 切换按钮组容器 -->
      <div class="switch-wrap">
        <!-- 事件按钮，根据状态切换选中样式，点击触发maplayerClick方法 -->
        <div
          c-tip="事件"
          :class="`map-tool-btn ${$store.state.map.evtLayerSwitch ? 'selected':''}`"
          @click="maplayerClick(MapLayers.evtAlarm)"
        >
          <svg-icon svg-name="icon_alarm_evt" />
        </div>
        <!-- 热力图按钮，根据状态切换选中样式，点击触发maplayerClick方法 -->
        <div
          c-tip="热力图"
          :class="`map-tool-btn ${$store.state.map.heatMapLayerSwitch ? 'selected':''}`"
          @click="maplayerClick(MapLayers.heatMap)"
        >
          <svg-icon svg-name="icon_heat_map" />
        </div>
        <!-- 筛选按钮，根据状态切换选中样式，点击触发maplayerClick方法 -->
        <div
          c-tip="筛选"
          :class="`map-tool-btn ${$store.state.map.evtFilterSwitch ? 'selected':''}`"
          @click="maplayerClick(MapLayers.evtFilter)"
        >
          <svg-icon
            svg-name="icon_filter_switch"
            :style="{width: pxToRem(20),height: pxToRem(20)}"
          />
        </div>
      </div>
    </div>
    <!-- 右侧窗口内容容器 -->
    <div v-if="layoutCfgRight" id="right-side-window-ins" class="right-side-window">
      <template v-for="(item, index) in componentList">
        <RemoteComponentSyncLoader
          v-if="item.monitorComp"
          :key="'rightCompenent_'+index"
          :config="getleftRightComponents(item.compCode)"
          :provinceId="provinceId"
          :cityId="cityId"
          :intervalTime="120000"
          :title="item.compName"
          :outerWidth="item.defaultWidth ? Number(item.defaultWidth) : 0"
          :outerHeight="item.defaultHeight ? Number(item.defaultHeight) : 0"
          :compUrl="item.compUrl"
        />
        <template v-else-if="!item.monitorComp && !alreadyInitFullViewComp[item.compCode]">
          <component
            :key="'rightCompenent_'+index"
            :is="getComponentByCode(item)"
            v-bind="getProps(item)"
          />
        </template>
        <!-- {{ !item.monitorComp && !alreadyInitFullViewComp[item.compCode] ? initFullViewComp(item, 'right-side-window-ins') : null }} -->
      </template>
    </div>
  </div>
</template>

<script>
import RemoteComponentSyncLoader from '@ct/remote-page-sync-loader/remote-page-sync-loader.umd.js'
import CompCfg from '@/components/page/ScreenPages/FullViewMonitor/CompCfg'
import { mapMutations } from 'vuex'

// 定义地图层类型常量
const MapLayers = {
  heatMap: 'heatMap',
  evtAlarm: 'evtAlarm',
  evtFilter: 'evtFilter',
}

export default {
  components: {
    RemoteComponentSyncLoader,
  },
  // 空对象表示该组件不接收任何父组件传递的属性
  props: {
    componentList: {
      type: Array,
      default: () => [],
    },
    leftRightComponents: {
      type: Object,
      default: {},
    },
  },
  computed: {
    // 获取数据
    getleftRightComponents() {
      return key => this.leftRightComponents[key]
    },
    getComponentByCode() {
      return item => CompCfg.getComponentByCode(item)
    },
    getProps() {
      return value => {
        return { propData: value }
      }
    },
  },
  data() {
    return {
      // 地图层类型常量
      MapLayers,
      // 用于存储内容窗口的对象
      contentWidows: {},
      // 用于标记点击事件的标志对象
      maplayerClickFlag: {},
      // 是否配置了右侧组件
      layoutCfgRight: false,
      layoutCfgLeft: (window.layoutCfg && window.layoutCfg.left) || [],
      alreadyInitFullViewComp: {}, // 已经初始化的全景展示组件
    }
  },
  watch: {
    // 监听 window.layoutCfg 的变化，并根据layoutCfg配置加载左侧窗口的组件
    componentList: {
      handler(newVal, oldVal) {
        if (newVal !== oldVal && newVal.length > 0) {
          this.$forceUpdate()
        }
      },
      deep: true,
    },
  }, // 空对象表示该组件不监听任何数据变化
  mounted() {
    // 如果layoutCfg及其中的left配置存在，则遍历left数组中的每个项
    this.$nextTick(() => {
      // this.initPage()
      this.layoutCfgRight = true
    })
  },
  methods: {
    // 从vuex的mapMutations引入切换地图层状态的 mutation
    ...mapMutations('map', [
      'setEvtLayerSwitch',
      'setHeatMapLayerSwitch',
      'setEvtFilterSwitch',
      'setEventFilterParams', // 设置事件筛选参数
    ]),
    /**
     * 切换地图层方法
     * @param {string} key 地图层类型常量
     * 切换不同类型的地图层可见性
     */
    maplayerClick(key) {
      switch (key) {
        case MapLayers.heatMap:
          this.setHeatMapLayerSwitch(!this.$store.state.map.heatMapLayerSwitch)
          break
        case MapLayers.evtAlarm:
          this.setEvtLayerSwitch(!this.$store.state.map.evtLayerSwitch)
          break
        case MapLayers.evtFilter:
          const state = !this.$store.state.map.evtFilterSwitch
          // 重置过滤条件
          state === false && this.setEventFilterParams({})
          this.setEvtFilterSwitch(state)
          break
        default:
          break
      }
    },
    // 初始化全景展示组件到dom上
    initFullViewComp(item, domId) {
      console.log('initFullViewComp rigth==================', item, domId)
      const div = document.getElementById(domId)
      if (!div) {
        setTimeout(() => {
          this.initFullViewComp(item, domId)
        }, 1000)
        return
      }
      CompCfg.getComponent({
        code: item.compCode,
        wrapper: domId,
        props: item,
      })
      this.alreadyInitFullViewComp[item.compCode] = true
    },
    initPage() {
      this.componentList?.map(item => {
        // 通过CompCfg获取组件配置，并传入组件代码和容器元素
        CompCfg.getComponent({
          code: item.compCode,
          wrapper: 'left-side-window-ins',
          props: item || {},
        })
      })
    },
  },
}
</script>

<style lang='less' src='./index.less' />
