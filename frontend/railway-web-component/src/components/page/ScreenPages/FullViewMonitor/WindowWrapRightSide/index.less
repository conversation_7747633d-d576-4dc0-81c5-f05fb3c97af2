.right-side-window-wrap {
  position: fixed;
  z-index: 2;
  top: 0;
  right: 0;
  // width: 400px;
  height: 100%;
  background-image: linear-gradient(
    93deg,
    rgba(11, 23, 45, 0) 0%,
    rgba(11, 23, 45, 0.01) 10%,
    rgba(11, 23, 45, 0.8) 70%,
    #010917 100%
  );
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  padding-right: 12px;
  @top: 90px;
  .ctl-wrap {
    height: 100%;
    width: 40px;
    position: absolute;
    .region-select-wrap {
      right: 90px;
      top: @top;
    }

    .map-control-tool {
      right: 55px;
      bottom: 5rem;
    }

    .switch-wrap {
      position: absolute;
      right: 45px;
      top: @top;
      display: flex;
      flex-direction: column;
      justify-content: center;

      > div {
        margin-bottom: 10px;
      }

      .map-tool-btn {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;
        background: #151a20;
        border: 3px solid;
        border-radius: 8px;
        border-image: linear-gradient(to top, #516989 50%, #6989b4 100%) 1
          stretch;
        clip-path: inset(0 round 6px);
        cursor: pointer;

        &.selected {
          border-radius: 8px;
          border-image: linear-gradient(to top, #549bf6 50%, white 100%) 1
            stretch;
          clip-path: inset(0 round 6px);
          background: #549bf6;
        }

        .svg-icon {
          height: 30px;
          width: 30px;
          margin: 0;
        }
      }
    }
  }

  .right-side-window {
    height: 100%;
    overflow: hidden;
    overflow-y: scroll;
    background-color: transparent;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: flex-start;

    > div {
      margin-top: 12px;
    }
  }
}
