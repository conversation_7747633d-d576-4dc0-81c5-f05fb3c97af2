<template></template>

<script>
// import vue from '@/main'
import { createWebSocket } from '@/utils/websocket'
import { mapActions } from 'vuex'
import ConstEnum from '@/components/common/ConstEnum'
// import { mockData } from './mockData'
import { wssAuth } from '@/api/service/imScreenService'

export default {
  data() {
    return {
      wsInstance: null,
      messages: [],
    }
  },
  computed: {
    // 选中的区域
    selectedRegion() {
      return this.$store.state.map.selectedRegion // 需要监听的属性
    },
    // 摄像机图层是否准备好了
    cameraLayerReady() {
      return this.$store.state.captureInfo.cameraLayerReady // 需要监听的属性
    },
  },
  watch: {
    // 监听 selectedRegion 的变化
    selectedRegion: {
      handler: function (newVal, oldVal) {
        if (newVal) {
          this.changeRegionHandle()
        }
      },
      deep: true,
    },
    // 监听 cameraLayerReady 的变化
    cameraLayerReady: {
      handler: function (newVal, oldVal) {
        if (newVal && !this.wsInstance) {
          this.initWs()
          console.log('监听 cameraLayerReady 的变化', newVal)
        }
      },
      immediate: true,
    },
  },

  beforeDestroy() {
    if (this.wsInstance) {
      this.wsInstance.close()
    }
    this.initCaptureInfo()
  },

  methods: {
    ...mapActions('captureInfo', ['addCaptureTask', 'initCaptureInfo']),
    async initWs() {
      // mock逻辑
      //   this.onNewCaptureInfos(mockData);
      //   return;
      // 通过协议获取ws
      const wsService = location.origin.includes('http:') ? 'ws' : 'wss'
      const [success, data] = await wssAuth()
      if (success && data) {
        this.wsInstance = createWebSocket({
          // vue.$env.VUE_APP_CAPTURE_WS_PREFIX
          url: `${wsService}://${window.location.hostname}/industry-11153/ws/${data}?tenantId=${window.userInfo.tenantId}&industryCode=${window.userInfo.industryCode}`,
          onMessage: msg => {
            this.onNewCaptureInfos(msg)
          },
          onOpen: () => {
            console.log('CaptureInfo WebSocket connected')
          },
          onClose: event => {
            console.log('CaptureInfo WebSocket closed', event)
          },
        })
      }
    },
    onNewCaptureInfos(data) {
      // 没有设备信息的丢弃
      if (!data.deviceCode) {
        return
      }
      const { regionCode, regionLevel } = this.selectedRegion
      const regionLevelKey = this.getRegionLevelKey(regionLevel)
      const isRegionMatch = data?.[regionLevelKey] === regionCode
      // 非当前区域的数据
      if (isRegionMatch) {
        this.addCaptureTask({
          deviceCode: data.deviceCode,
          captureInfo: data,
        })
      }
    },
    getRegionLevelKey(regionLevel) {
      if (ConstEnum.REGION_LEVEL.district === regionLevel) {
        return 'countyId'
      } else if (ConstEnum.REGION_LEVEL.city === regionLevel) {
        return 'cityId'
      } else if (ConstEnum.REGION_LEVEL.province === regionLevel) {
        return 'provinceId'
      } else {
        return ''
      }
    },
    // 区域切换以后，重新设置当前区域的抓拍数据
    changeRegionHandle() {
      this.initCaptureInfo()
    },
  },
}
</script>

<style scoped></style>
