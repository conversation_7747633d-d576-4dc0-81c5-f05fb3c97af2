<!--
 * @Description: 组件用于播放音频。
 * @Autor: he.chao
 * @Date: 2022-10-19 15:07:55
 * @LastEditors:u.yongli
 * @LastEditTime: 2023-12-13 14:36:14
-->
<template>
  <!-- 音频元素，使用绑定的编码URL作为音频源，自动播放并显示控件，隐藏可见性并设置高度为1像素 -->
  <audio
    :src="encodeUrl"
    autoplay="autoplay"
    controls="controls"
    :id="audioId"
    :style="{ visibility: 'hidden', height: '1px',width:'100%' }"
  ></audio>
</template>
<script>
import materialService from '@/api/service/materialService'
export default {
  // 接收音频源URL和音频ID作为属性
  props: {
    audioSrc: {
      type: String,
      default: '', // 默认值为空字符串
    },
    audioId: {
      type: String,
      default: '', // 默认值为空字符串
    },
  },
  // 监听音频源URL的变化，当有新值时尝试播放音频
  watch: {
    audioSrc: {
      handler(newVal) {
        if (newVal) {
          this.playMp3() // 调用播放音频的方法
        }
      },
      immediate: true, // 立即执行监听器函数
    },
  },
  data() {
    return {
      encodeUrl: '', // 用于存储编码后的音频URL
    }
  },
  mounted() {
    // 组件挂载后立即尝试播放音频
    this.playMp3()
  },
  methods: {
    /**
     * 播放音频。
     * 如果音频源URL有效，则获取编码后的URL并开始播放。
     */
    async playMp3() {
      if (!this.audioSrc) {
        return // 如果音频源URL无效，则返回
      }
      const audio = document.getElementById(this.audioId) // 获取音频元素
      this.encodeUrl = await this.getAudioUrl(this.audioSrc) // 获取编码后的音频URL
      audio.play() // 播放音频
    },
    /**
     * 停止播放音频并重置音频播放器。
     */
    stop() {
      const audio = document.getElementById(this.audioId) // 获取音频元素
      this.encodeUrl = null // 重置编码后的音频URL
      audio.currentTime = 0 // 重置音频播放时间
      audio.pause() // 暂停音频
    },
    /**
     * 获取加密的音频URL。
     * @param {string} audioUrl 音频的原始URL。
     * @returns {Promise<string>} 加密后的音频URL。
     */
    async getAudioUrl(audioUrl) {
      let url = ''
      const { code, data } = await materialService.qryNewVoiceUrl({
        fileUrls: [audioUrl],
      }) // 调用服务获取加密URL
      if (code === 200 && data && data.length === 1) {
        url = data[0].encryptUrl // 如果响应成功且数据有效，获取加密URL
      }
      return url // 返回加密后的URL
    },
  },
}
</script>
