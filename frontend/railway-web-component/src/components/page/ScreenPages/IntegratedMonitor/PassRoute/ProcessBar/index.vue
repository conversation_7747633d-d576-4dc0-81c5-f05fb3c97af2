<!--
    @Description: 过车数据时间选择
    @Author: li.haoyang
    @Date: 2024-08-02 10:59:15
    @LastEditTime: 2024-08-02 19:41:56
    @LastEditors: li.haoyang
-->
<template>
  <div class="process-bar-wrap">
    <div :class="`process-bar-date ${datePickerOpen ? 'open' : 'close'}`">
      <el-date-picker
        v-model="dateParam"
        type="date"
        placeholder="选择"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        :clearable="false"
        :editable="false"
        :picker-options="pickerOptions"
        @blur="datePickerChange(false)"
        @focus="datePickerChange(true)"
        @change="dateParamChange"
      ></el-date-picker>
    </div>
    <div class="process-bar-times">
      <div
        :class="`timeItem ${currentHour === index ? 'selected' : ''}`"
        v-for="(item, index) in times"
        :key="index"
        @click="timeClickHandle(index)"
      >
        <div class="timeDot" />
        <div class="timeName">{{ item }}</div>
      </div>
    </div>
    <div :class="`play ${playing ? 'pause-icon' : 'play-icon'}`" @click="changePlay"></div>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import commonService from '@/api/service/common'

export default {
  components: {},
  props: {
    currentHour: {
      type: Number,
      default: 0,
    },
    playing: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      // 选择的日期
      dateParam: dayjs().format('YYYY-MM-DD'),
      datePickerOpen: false,
      currentDate: 0,
      times: Array(24)
        .fill(0)
        .map((_, i) => (i > 9 ? i : `0${i}`)),

      pickerOptions: {
        disabledDate(time) {
          return (
            time.getTime() > Date.now() ||
            dayjs(time.getTime()) < dayjs().subtract(7, 'd')
          )
        },
      },
    }
  },
  mounted() {
    this.iniSysConfig()
  },
  methods: {
    async iniSysConfig() {
      const { code: cfgCode, data: cfgData } =
        await commonService.getDictListByCatCode('DEVC_CAPTURE_VALID_PERIOD')
      // 处理系统配置数据
      if (cfgCode !== 200 || !cfgData || cfgData.length < 1) {
        return
      }
      const { dicValue } = cfgData[0]
      if (!isNaN(dicValue)) {
        this.pickerOptions = {
          disabledDate(time) {
            return (
              time.getTime() > Date.now() ||
              dayjs(time.getTime()) < dayjs().subtract(dicValue, 'd')
            )
          },
        }
      }
    },
    dateParamChange(value) {
      this.$emit('changeDate', value)
    },
    timeClickHandle(index) {
      this.$emit('timeClickHandle', index)
    },
    changePlay() {
      this.$emit('playChange', !this.playing)
    },
    datePickerChange(flag) {
      this.datePickerOpen = flag
    },
  },
}
</script>

<style lang="less" src="./index.less" />
