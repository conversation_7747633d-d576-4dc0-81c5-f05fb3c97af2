import CTMapOl from '@ct/ct_map_ol';
import { arrowLineStyles, getShadowLineStyle } from '@/utils/map3.0';
import LineCalcWorker from 'worker-loader!./line-calc.worker';
import { transformPointToMercator } from '@/utils'

const lineStyle = new CTMapOl.style.Style({
  fill: new CTMapOl.style.Fill({ color: '#0055ff' }),
  stroke: new CTMapOl.style.Stroke({
    color: '#9DAA48',
    // color: '#FFFFFF',
    width: 10,
    lineCap: 'square', // Line cap style: butt, round, or square.
    // lineDash: [30, 40],
    radius: 0,
    glow: 1,
  }),
});

const lineBgStyle = new CTMapOl.style.Style({
  fill: new CTMapOl.style.Fill({ color: '#0055ff' }),
  stroke: new CTMapOl.style.Stroke({
    // color: 'rgba(255, 255, 0, 0.5)',
    color: '#FFBF00',
    width: 18,
    lineCap: 'square', // Line cap style: butt, round, or square.
    // lineDash: [30, 40],
    radius: 0,
    glow: 1,
  }),
});

// const shadowLineStyle = new CTMapOl.style.Style({
//     renderer: (coords, state) => {
//         const ctx = state.context;
//         ctx.save();

//         // 设置阴影参数
//         ctx.shadowColor = 'rgba(255,191,0,1)';
//         ctx.shadowBlur = 20;        // 阴影模糊半径
//         ctx.shadowOffsetX = 0;      // X 方向偏移
//         ctx.shadowOffsetY = 1;      // Y 方向偏移

//         // 绘制线
//         ctx.beginPath();
//         ctx.moveTo(coords[0][0], coords[0][1]);
//         for (let i = 1; i < coords.length; i++) {
//             ctx.lineTo(coords[i][0], coords[i][1]);
//         }
//         ctx.strokeStyle = '#D5FF00';    // 主体线颜色
//         ctx.lineWidth = 10;          // 主体线宽度
//         ctx.stroke();

//         ctx.restore();
//     }
// });

/**
 * PassRouteLine类代表一个过车线路，提供在地图上绘制和移除的方法。
 *
 * @class PassRouteLine
 * @param {Object} config - 过车线路的配置对象
 * @param {Object} [config.props={}] - 传递给过车线路组件的属性
 * @param {number} config.pointArray - 过车线路的经纬度
 * @param {Object} config.map - 地图对象
 */
export default class PassRouteLine {
  constructor({
    props = {},
    pointArray,
    map,
  }) {
    // 初始化过车线路实例属性
    this.props = props;
    this.pointArray = pointArray;
    this.map = map;
    // 线路数据
    this.lineData = null;
    // 线路长度
    this.lineDistance = null;
    // 分段绘制线路相关
    this.lineSum = 100;
    this.lineStart = 1;
    // 单位毫秒
    this.lineCalcInterval = 10;
    // 线路图层
    this.lineLayer = null;
    this.lineFeature = null;
    this.renderLine = false;
    this.lineStyle = {
      lineWidth: 20,
      strokeStyle: '#feebb9',
      // shadowColor: 'rgba(0,1,20,0.2)',
      // shadowColor:'rgba(255,191,0,1)',
      shadowColor: '#FFBF00',
      shadowBlur: 40,
      shadowOffsetX: 0,
      shadowOffsetY: 1,
    };
    // 线路背景图层
    this.renderTimer = null;
    // 线路流动相关
    this.animationTimer = null;
    this.offset = 0;
    // 线路消失相关
    this.fadeOutColor = [
      // {strokeStyle: '#FFAB00', shadowColor: '#FFBF00'},
      { strokeStyle: '#DD990E', shadowColor: 'rgba(255,191,0,0.8)' },
      { strokeStyle: '#D79E2C', shadowColor: 'rgba(255,191,0,0.8)' },
      { strokeStyle: '#DBAF56', shadowColor: 'rgba(255,191,0,0.6)' },
      { strokeStyle: '#E6C37C', shadowColor: 'rgba(255,191,0,0.4)' },
      { strokeStyle: 'rgba(168,154,125,0.4)', shadowColor: 'rgba(255,191,0,0.2)' },
    ];
    this.fadeOutColorIndex = 0;
    // 线路消失的时候每个颜色切换时长
    this.fadeOutColorCutInterval = 30;
    // 线路计算
    this.worker = null;
    this.init();
    return this;
  }

  init() {
    this.lineLayer = new CTMapOl.layer.Vector({
      source: new CTMapOl.source.Vector(),
      zIndex: 9,
      updateWhileAnimating: true, // 动画时更新
      updateWhileInteracting: true, // 交互时更新
    });
    this.map.addLayer(this.lineLayer);
    // 延迟进行线路绘制，给地图移动一个时间
    setTimeout(() => {
      this.drawPassRouteLine();
    }, 500);
  }

  tmLineStyle(offset) {
    return new CTMapOl.style.Style({
      stroke: new CTMapOl.style.Stroke({
        color: '#ffffff',
        width: 10,
        lineDash: [10, 30],
        lineDashOffset: offset,
        lineCap: 'square',
        radius: 0,
      }),
      fill: new CTMapOl.style.Fill({
        color: [16, 130, 255, 0.5],
      }),
    });
  }

  rovingLineStyleFunction(offset) {
    const styles = [
      new CTMapOl.style.Style({
        fill: new CTMapOl.style.Fill({
          color: 'rgba(16, 130, 255, 1)', //填充颜色
        }),
        stroke: new CTMapOl.style.Stroke({
          width: 10, //边界宽度
          color: [16, 130, 255, 1], //边界颜色
          lineCap: 'square',
          radius: 0,
        }),
      }),
      this.tmLineStyle(offset)
    ];
    return styles;
  }


  startAnimation() {
    this.offset = 0;
    this.animationTimer = setInterval(() => {
      this.offset -= 1;
      this.lineFeature.setStyle(this.rovingLineStyleFunction(this.offset));
    }, 30);
  }

  startLineCalcWorker() {
    // vue.$worker.run(() => {
    //     this.subRenderLine();
    // })
    // return
    // import('./line-calc.worker.js').then((workerModule) => {
    //     debugger
    //     this.worker = new workerModule.default();
    //   });
    console.log(new Date().getTime())
    this.worker = new LineCalcWorker();
    console.log(new Date().getTime())
    // this.worker = new Worker(new URL('@/components/page/ScreenPages/IntegratedMonitor/PassRoute/line-calc.worker.js', import.meta.url));

    // this.worker = new Worker('@/components/page/ScreenPages/IntegratedMonitor/PassRoute/line-calc.worker.js');
    // this.worker.postMessage({ module: CTMapOl });
    this.worker.postMessage({
      type: 'start',
      lineData: this.lineData,
      chunkCount: this.lineSum,
      interval: this.lineCalcInterval,
      startIndex: this.lineStart + 1,
      lineLength: this.lineDistance
    });

    // 接收分块数据
    this.worker.onmessage = (e) => {
      if (e.data.type === 'chunk') {
        if (!this.renderLine) {
          console.log(new Date().getTime())
          this.lineLayer.getSource().addFeatures([this.lineFeature]);
          this.renderLine = true;
        }
        this.lineFeature
          .getGeometry()
          .appendCoordinate(transformPointToMercator(e.data.coordinates));
      }
    };
  }

  /**
   * 分段渲染线路
   * @returns
   */
  subRenderLine() {
    this.lineStart++;
    // 加载完最后一段
    if (this.lineStart > this.lineSum) {
      // this.startAnimation();
      return;
    }
    const alongPath = CTMapOl.turf.along(
      this.lineData,
      this.lineDistance * (this.lineStart / this.lineSum)
    );
    this.lineFeature
      .getGeometry()
      .appendCoordinate(alongPath.geometry.coordinates);
    this.renderTimer = setTimeout(() => {
      this.subRenderLine()
    }, 10);
  }

  /**
   * 在地图上分段绘制过车线路，类似于流动效果
   * 绘制前先绘制灰色线段？
   * 绘制结束以后增加阴影，清除灰色线段
   */
  drawPassRouteLine() {
    const data = {
      type: 'Feature',
      properties: {},
      geometry: {
        type: 'LineString',
        coordinates: this.pointArray,
      },
    };

    this.lineData = data;

    // 线的长度 ， 墨卡托座标是
    const distance = CTMapOl.turf.lineDistance(data);
    this.lineDistance = distance;
    // 空的feature
    const path = CTMapOl.turf.along(data, distance / this.lineSum);
    const linePath = CTMapOl.turf.lineString([
      transformPointToMercator(this.pointArray[0]),
      transformPointToMercator(path.geometry.coordinates), // [116.34527486, 39.95060945]
    ]);
    this.lineFeature = new CTMapOl.format.GeoJSON().readFeature(
      linePath
    );
    const shadowLineStyle = getShadowLineStyle(this.lineStyle);
    this.lineFeature.setStyle((_feature, resolution) =>
      arrowLineStyles(_feature, resolution, shadowLineStyle)
    );
    // 等worker返回第一次计算以后再渲染到地图上
    // this.lineLayer.getSource().addFeatures([this.lineFeature]);
    // this.subRenderLine();
    // 分段渲染交给worker去计算处理
    console.log(new Date().getTime())
    this.startLineCalcWorker();
  }

  // 渐变移除函数
  removeLineWithAnimation(feature, duration = 2000) {
    feature.setStyle((item) => {
      const opacity = item.get('opacity') ? item.get('opacity') : 1;
      return new CTMapOl.style.Style({
        stroke: new CTMapOl.style.Stroke({
          color: `rgba(254, 235, 185, ${opacity})`,
          width: 10,
          lineCap: 'square', // Line cap style: butt, round, or square.
          // lineDash: [30, 40],
          radius: 0,
          glow: 1,
        }),
      });
    });
    const startTime = Date.now();
    feature.set('opacity', 1);

    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);
      const opacity = 1 - progress;
      feature.set('opacity', opacity);
      feature.changed();

      if (progress < 1) {
        requestAnimationFrame(animate);
      } else {
        this.lineLayer.getSource().clear(true);
      }
    };

    requestAnimationFrame(animate);
  }

  fadeOutLine() {
    if (this.fadeOutColorIndex >= this.fadeOutColor.length) {
      return;
    }
    const newStyle = getShadowLineStyle(this.fadeOutColor[this.fadeOutColorIndex]);
    this.lineFeature.setStyle(newStyle);
    this.fadeOutColorIndex++;
    setTimeout(() => {
      this.fadeOutLine();
    }, this.fadeOutColorCutInterval);
  }

  /**
   * 在地图上清除过车线路
   * 首先定时设置透明度从1->0.2
   * 最后置为灰色？
   */
  removePassRouteLine() {
    // this.fadeOutLine();
    this.removeLineWithAnimation(this.lineFeature)
    return
    setTimeout(() => {
      this.worker && this.worker.terminate();
      this.renderTimer && clearTimeout(this.renderTimer);
      this.animationTimer && clearInterval(this.animationTimer);
      this.lineLayer.getSource().clear(true);
      this.map.removeLayer(this.lineLayer);
    }, this.fadeOutColorCutInterval * (this.fadeOutColor.length + 1));
  }
}
