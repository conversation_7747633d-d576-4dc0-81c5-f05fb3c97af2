.pass-route-wrap {
    position: absolute;
    right: 50%;
    bottom: 44px;
    width: 1200px;
    transform: translate(50%, 0px);
    .el-carousel__container {
      width: 100%;
      height: 316px;
    }

    .el-carousel__item--card {
      width: 300px;
      height: 190px;
      top: 60px;
    }
    .img-container {
      background: url("./img/bg_pass_300.gif") 100% 100% no-repeat;
      background-size: 100% 100%;
      display: flex;
      align-items: center;
      justify-content: center;

      &.is-active {
        font-size: 12px;
        width: 500px;
        height: 316px;
        background: url("./img/bg_pass_500.gif") 100% 100% no-repeat;
        background-size: 100% 100%;
        top: 0;
        margin: 0 40px;
        opacity: 1;

        .pass-time {
          left: 32px;
          top: 30px;
          width: 138px;
          height: 32px;
          background-image: linear-gradient(82deg, #5493F1 0%, #2A5ADF 96%);
          border-radius: 4px 0px 8px 4px;
        }

        img {
          width: 428px;
          height: 252px;
        }
      }
      img {
        width: 264px;
        height: 154px;
      }

      .pass-time {
        position: absolute;
        left: 18px;
        top: 18px;
        width: 120px;
        height: 29px;
        font-family: PingFangSC-Medium;
        font-size: 12px;
        color: #E8F3FF;
        font-weight: 500;
        background-image: linear-gradient(180deg, #073143 0%, #031620 100%);
        border-radius: 4px 0px 8px 4px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
