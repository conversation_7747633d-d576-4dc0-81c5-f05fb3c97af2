/**
 * 线路计算线程
 */
// importScripts(`${self.location.origin}/industry-11151/js/turf.min.js`);
importScripts(`${process.env.BASE_URL}/js/turf.min.js`);

self.onmessage = (e) => {
    if (e.data.type === 'start') {
        const { lineData, chunkCount, interval, lineLength } = e.data;
        const chunkLength = lineLength / chunkCount;

        let currentChunk = 1;

        function processNextChunk() {
            // PassRouteLine中会渲染第一段以后再把数据交给worker处理，所以这里先给index++
            currentChunk++;
            if (currentChunk > chunkCount) {
                self.postMessage({ type: 'complete' });
                return;
            }

            // 使用 Turf 计算当前分段的线
            const along = turf.along(lineData, chunkLength * currentChunk);

            // 发送坐标数据（Transferable 优化）
            const coordinates = along.geometry.coordinates;
            const buffer = new Float64Array(coordinates.flat()).buffer;

            self.postMessage({
                type: 'chunk',
                coordinates: coordinates,
            }, [buffer]); // 使用 Transferable 减少拷贝

            setTimeout(processNextChunk, interval);
        }

        processNextChunk();
    }
};
