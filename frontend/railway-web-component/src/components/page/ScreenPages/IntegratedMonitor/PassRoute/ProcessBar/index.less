.process-bar-wrap {
  position: absolute;
  right: 50%;
  bottom: 12px;
  width: 980px;
  height: 56px;
  transform: translate(50%, 0px);
  background: #172537;
  border: 1px solid rgba(23, 125, 220, 0.8);
  border-radius: 27px;
  display: flex;
  align-items: center;
  z-index: 1;
  pointer-events: all;
  .process-bar-date {
    height: 100%;
    background: #4f9fff;
    border-radius: 27px;
    width: 115px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: DINAlternate-Bold;
    font-size: 14px;
    color: #ffffff;
    letter-spacing: 0;
    font-weight: 700;
    position: relative;
    &.close {
      &::after {
        content: "";
        position: absolute;
        top: calc(50% - (9px / 2));
        right: 15px;
        width: 8px;
        height: 9px;
        background: url("../img/date_picker_close_arrow.svg") 100% 100%
          no-repeat;
        background-size: 100% 100%;
      }
    }

    &.open {
      &::after {
        content: "";
        position: absolute;
        top: calc(50% - (9px / 2));
        right: 15px;
        width: 8px;
        height: 9px;
        background: url("../img/date_picker_open_arrow.svg") 100% 100% no-repeat;
        background-size: 100% 100%;
      }
    }
    .el-date-editor.el-input {
      margin-left: 0 !important;
      width: 100%;
      .el-input__inner {
        background: transparent;
        border: none;
        padding: 0 0 0 12px;
        font-family: DINAlternate-Bold;
        font-size: 14px;
        color: #ffffff;
        letter-spacing: 0;
        font-weight: 700;
      }
      .el-input__prefix {
        display: none;
      }
    }
  }
  .process-bar-times {
    flex: 1;
    display: flex;
    justify-content: space-between;
    margin: 0 8px;
    position: relative;
    height: 100%;
    &::before {
      content: "";
      width: calc(100% - 16px);
      height: 6px;
      position: absolute;
      top: 12px;
      left: 6px;
      background: #dcdfe6;
      border-radius: 10px;
    }
    .timeItem {
      cursor: pointer;
      z-index: 1;
      .timeDot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: rgba(232, 232, 232, 0.5);
        box-sizing: content-box;
        position: relative;
        margin: 9px 6px;
        &::before {
          content: "";
          position: absolute;
          top: 3px;
          left: 3px;
          width: 6px;
          height: 6px;
          background-color: rgba(255, 255, 255, 0.9);
          border-radius: 50%;
        }
      }
      .timeName {
        font-family: DINAlternate-Bold;
        font-size: 12px;
        color: #ffffff;
        letter-spacing: 0;
        font-weight: 700;
        width: 24px;
        height: 14px;
        line-height: 14px;
        text-align: center;
      }
      &.selected {
        .timeDot {
          background: #4f9fff;
          &::before {
            top: 2px;
            left: 2px;
            width: 8px;
            height: 8px;
            background: #ffffff;
          }
        }
        .timeName {
          background: #4f9fff;
          border-radius: 6px;
        }
      }
    }
  }
  .play {
    width: 60px;
    height: 100%;
    cursor: pointer;
    position: relative;

    &.pause-icon {
      &::after {
        content: "";
        position: absolute;
        top: calc(50% - (28px / 2));
        right: calc(50% - (22px / 2));
        width: 22px;
        height: 28px;
        background: url("../img/pass_route_pause_icon.svg") 100% 100% no-repeat;
        background-size: 100% 100%;
      }
    }

    &.play-icon {
      &::after {
        content: "";
        position: absolute;
        top: calc(50% - (24px / 2));
        right: calc(50% - (28px / 2));
        width: 28px;
        height: 24px;
        background: url("../img/pass_route_play_icon.svg") 100% 100% no-repeat;
        background-size: 100% 100%;
      }
    }
  }
}

.el-picker-panel.el-date-picker.el-popper {
  color: #e8f3ff;
  border: 1px solid transparent;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  background: #172537;
  border-radius: 4px;
  line-height: 30px;
  margin: 5px 0;

  .el-date-table td.in-range div {
    background-color: rgba(64, 158, 255, 0.55) !important;
    color: #e8f3ff !important;
  }

  .el-date-range-picker__content.is-left {
    border-color: #fefefe2e;
  }

  .el-picker-panel__icon-btn {
    color: #fefefe;

    &:hover {
      color: #409eff;
    }
  }
  .el-date-table {
    th {
      color: #e8f3ff;
      border-bottom: solid 1px #fefefe2e;
    }

    td.next-month,
    td.prev-month {
      color: #c0c4cc91;
    }

    td.disabled div {
      background-color: transparent;
      opacity: 1;
      cursor: not-allowed;
      color: #c0c4cc91;
    }
    td.available:hover {
      span {
        color: #e8f3ff;
        background: rgba(64, 158, 255, 0.55);
        border-radius: 24px;
      }
    }
  }
  .el-picker-panel__icon-btn {
    color: #ffffff;
  }
}
