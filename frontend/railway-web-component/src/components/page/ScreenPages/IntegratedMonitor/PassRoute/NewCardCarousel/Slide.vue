<template>
  <!-- 使用Swiper组件的滑块页面结构 -->
  <div class="swiper-slide">
    <!-- 图片容器，用于展示过车图片和时间信息 -->
    <div class="img-container">
      <!-- 显示过车时间，使用dayjs.js格式化日期时间 -->
      <span class="pass-time">
        过车时间：{{
        dayjs(propData.alarmTime).format(ConstEnum.DATE_FORMAT.hh_mm_ss)
        }}
      </span>
      <!-- 展示第一张过车图片 -->
      <div class="video-wrap" v-if="propData.videoUrl">
        <!-- ref="videoPlayer" -->
        <video-player
          class="video video-player vjs-custom-skin infoVideo-player lunboVideo"
          :playsinline="true"
          :options="playerOptions"
          @ended="onVideoEnded"
          @error="onVideoError"
        />
      </div>
      <img
        v-else
        alt="图片"
        :src="
            propData.fileImgUrlSrc ||
            propData.fileImgUrlIcon ||
            propData.imgUrl ||
            staticImg
          "
      />
    </div>
  </div>
</template>

  <script>
import dayjs from 'dayjs' // 引入dayjs.js库，用于日期时间处理
import ConstEnum from '@/components/common/ConstEnum' // 引入常量枚举，用于日期格式化
// import { set } from 'ol/transform';

export default {
  /* 计算属性，用于提供常量枚举到模板 */
  computed: {
    ConstEnum() {
      return ConstEnum
    },
  },
  /* 组件接收的属性，propData用于传递过车数据 */
  props: {
    propData: {
      type: Object,
      default: () => {
        return {}
      },
    },
    activeIndex: {
      type: Number | String,
      default: -1,
    },
    slideIdx: {
      type: Number | String,
      default: -1,
    },
  },
  watch: {
    activeIndex(val) {
      this.playerOptions.autoplay = val === this.slideIdx
    },
  },
  data() {
    /* 组件数据初始化 */
    return {
      staticImg: require('@/assets/images/comm/nonImg.png'), // 默认图片
      playerOptions: {
        // 小视频播放参数
        autoplay: true, // 自动播放
        loop: false, // 循环播放
        muted: true, // 静音
        preload: 'auto', // 自动加载
        language: 'zh-CN',
        fluid: true, // 流体布局
        hls: false,
        sources: [
          {
            type: 'video/mp4',
            src: '', // 视频源
          },
        ],
        aspectRatio: '16:9',
        poster: '', // 封面地址
        choosed: false, // 被选中的
        notSupportedMessage: '视频录像服务维护中', // 不支持时的提示信息
        controlBar: {
          timeDivider: false,
          durationDisplay: false,
          remainingTimeDisplay: false,
          fullscreenToggle: false, // 全屏按钮
        },
      },
    }
  },
  mounted() {
    /* 组件挂载后的逻辑 */
    if (this.propData.videoUrl) {
      this.playerOptions.autoplay = this.slideIdx === 0
      this.playerOptions.sources = [
        {
          type: 'video/mp4',
          src: this.propData.videoUrl, // 'https://www.runoob.com/try/demo_source/movie.mp4', //
        },
      ]
    }
  },
  /* 提供dayjs方法，用于格式化日期时间 */
  methods: {
    dayjs(data) {
      return dayjs(data)
    },
    onVideoEnded() {
      this.$emit('onVideoEnded', {
        end: true,
        activeIndex: this.slideIdx,
      })
    },
    onVideoError() {
      this.$emit('onVideoError', {
        end: true,
        activeIndex: this.slideIdx,
      })
    },
  },
}
</script>

  <!-- 引入组件的样式文件 -->
  <style lang="less" src="./index.less" />
