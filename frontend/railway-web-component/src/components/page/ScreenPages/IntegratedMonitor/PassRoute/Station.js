import CommonMap from '@/components/common/Map/CommonMap';
import CurrentPoint from '@/components/page/ScreenPages/IntegratedMonitor/InfoWindow/CurrentPoint.vue';
import CurrentPointLittle from '@/components/page/ScreenPages/IntegratedMonitor/InfoWindow/CurrentPointLittle.vue';
import PassCurrent from '@/components/page/ScreenPages/IntegratedMonitor/InfoWindow/PassCurrent.vue';
import StationInfo from '@/components/page/ScreenPages/IntegratedMonitor/InfoWindow/StationInfo.vue';
import StationInfoLittle from '@/components/page/ScreenPages/IntegratedMonitor/InfoWindow/StationInfoLittle.vue';
import StationInfoRight from '@/components/page/ScreenPages/IntegratedMonitor/InfoWindow/StationInfoRight.vue';
import { initLayer } from '@/components/page/ScreenPages/IntegratedMonitor/Map/MapLayerHandle/CommonHandle';
import { passRouteIcon } from '@/components/page/ScreenPages/IntegratedMonitor/Map/marker';
import { imageMarker } from '@/utils/map3.0';

// 用于缓存Station实例，避免重复创建
const cache = {};

/**
 * Station类代表一个站点，提供在地图上绘制标记和信息窗口的方法。
 *
 * @class Station
 * @param {Object} config - 站点的配置对象
 * @param {string} config.id - 站点的唯一标识
 * @param {Object} [config.props={}] - 传递给站点组件的属性
 * @param {number} config.lat - 站点的纬度
 * @param {number} config.lng - 站点的经度
 * @param {Object} config.map - 地图对象
 */
export default class Station {
  constructor({
    id,
    props = {},
    lat,
    lng,
    map,
    className = ''
  }) {
    // 检查缓存中是否已存在该站点实例，如果存在则更新属性并返回实例
    if (cache[id]) {
      cache[id].props = props;
      cache[id].lat = lat;
      cache[id].lng = lng;
      cache[id].map = map;
      return cache[id];
    }
    // 初始化站点实例属性
    this.id = id;
    this.props = props;
    this.lat = lat;
    this.lng = lng;
    this.map = map;
    this.className = className;
    // 将新实例添加到缓存中
    cache[id] = this;
    return this;
  }

  /**
   * 获取站点的经纬度。
   *
   * @returns {Object} 包含纬度和经度的对象
   */
  getLatLng() {
    return {
      lat: this.lat,
      lng: this.lng
    };
  }

  /**
   * 在地图上绘制站点标记。
   */
  drawMarker() {
    // 初始化或更新站点图层
    this.stationsLayer = initLayer(this.stationsLayer, this.map);
    // 清除图层中的旧标记
    this.stationsLayer.getSource().clear(true);
    // 创建并添加新的标记
    const markers = imageMarker([this.lng, this.lat], {
      icon: passRouteIcon.iconPassRouteStation,
      anchor: [27, 10],
      text: '定位'
    }, {
      id: `meta-icon-center`, ...{}
    });
    this.stationsLayer.getSource().addFeatures([markers]);
  }

  /**
   * 在地图左侧绘制站点信息窗口。
   *
   * @returns {Station} 当前站点实例，用于链式调用
   */
  drawSideLeft() {
    // 创建信息窗口
    const winId = this.id + '-info-left';
    CommonMap.infoWindow(this.lng, this.lat,
      CommonMap.componentToHtml({
        component: StationInfo,
        props: {
          ...this.props,
          className: this.className,
        },
        onClose: () => {
          const content = document.getElementById(winId);
          content.remove();
        }
      }), 'popup',
      -30, 0, winId, this.map,
      false, 'mainMap', () => {
        console.log('default implement');
      }, false);
    // 绘制站点标记
    // this.drawMarker();
    return this;
  }

  /**
   * 在地图右侧绘制站点信息窗口。
   *
   * @returns {Station} 当前站点实例，用于链式调用
   */
  drawSideRight() {
    // 创建信息窗口
    const winId = this.id + '-info-right';
    CommonMap.infoWindow(this.lng, this.lat,
      CommonMap.componentToHtml({
        component: StationInfoRight,
        props: {
          ...this.props,
          className: this.className,
        },
        onClose: () => {
          const content = document.getElementById(winId);
          content.remove();
        }
      }), 'popup',
      30, 0, winId, this.map,
      false, 'mainMap', () => {
        console.log('default implement');
      }, false);
    // 绘制站点标记
    // this.drawMarker();
    return this;
  }

  /**
   * 移除左侧的站点信息
   */
  removeLeftSide() {
    const id = this.id + '-info-left';
    this.removeSideWithAnimation(id, StationInfo, -30)
    // 清除站点标记
    if (this.stationsLayer) {
      this.stationsLayer.getSource().clear(true);
    }
  }

  /**
  * 移除右侧的站点信息
  */
  removeRightSide() {
    const id = this.id + '-info-right';
    this.removeSideWithAnimation(id, StationInfoRight, 30)
    // 清除站点标记
    if (this.stationsLayer) {
      this.stationsLayer.getSource().clear(true);
    }
  }

  /**
   * 移除站点信息的弹窗，谈出动画
   * @param {是否是左边的站点} isLeft
   */
  removeSideWithAnimation(winId, Comp, offsetX) {
    CommonMap.infoWindow(this.lng, this.lat,
      CommonMap.componentToHtml({
        component: Comp,
        props: {
          ...this.props,
          className: 'fadeOutStyle',
        },
        onClose: () => {
          const content = document.getElementById(winId);
          content.remove();
        }
      }), 'popup',
      offsetX, 0, winId, this.map,
      false, 'mainMap', () => {
        console.log('default implement');
      }, false);
    setTimeout(() => {
      document.getElementById(winId)?.remove();
    }, 2000)
  }

  /**
   * 在地图上绘制当前关注的站点及其相关信息。
   */
  drawFocusStation() {
    // 绘制当前关注的站点信息窗口
    const currentStation = this.id + '-current-station';
    CommonMap.infoWindow(this.lng, this.lat,
      CommonMap.componentToHtml({
        component: PassCurrent,
        props: this.props,
        onClose: () => {
          const content = document.getElementById(currentStation);
          content.remove();
        }
      }), 'popup',
      -50, 0, currentStation, this.map,
      false, 'mainMap');

    // 绘制当前关注的站点标记
    const windowCurrentPoint = this.id + 'current-point';
    CommonMap.infoWindow(this.lng, this.lat,
      CommonMap.componentToHtml({
        component: CurrentPoint,
        props: this.props,
        onClose: () => {
          const content = document.getElementById(windowCurrentPoint);
          content.remove();
        }
      }), 'popup',
      0, 75, windowCurrentPoint, this.map,
      false, 'mainMap');
  }

  /**
   * 在地图上绘制当前关注的站点及其相关信息，可以通过className触发内置的动画
   */
  drawFocusStationWithAnimation() {
    // 绘制当前关注的站点信息窗口
    const currentStation = this.id + '-current-station';
    CommonMap.infoWindow(this.lng, this.lat,
      CommonMap.componentToHtml({
        component: PassCurrent,
        props: {
          ...this.props,
          className: this.className,
        },
        onClose: () => {
          const content = document.getElementById(currentStation);
          content.remove();
        }
      }), 'popup',
      -50, 0, currentStation, this.map,
      false, 'mainMap');

    // 绘制当前关注的站点标记
    const windowCurrentPoint = this.id + 'current-point';
    CommonMap.infoWindow(this.lng, this.lat,
      CommonMap.componentToHtml({
        component: CurrentPoint,
        props: {
          ...this.props,
          className: this.className,
        },
        onClose: () => {
          const content = document.getElementById(windowCurrentPoint);
          content.remove();
        }
      }), 'popup',
      0, 75, windowCurrentPoint, this.map,
      false, 'mainMap');
  }

  /**
   * 移除关注的站点及其相关信息，渐出效果动画
   */
  removeFocusStationWithAnimation() {
    document.getElementById(this.id + '-current-station')?.remove();
    document.getElementById(this.id + 'current-point')?.remove();
    // 绘制当前关注的站点信息窗口
    const currentStation = this.id + '-current-station-222';
    CommonMap.infoWindow(this.lng, this.lat,
      CommonMap.componentToHtml({
        component: PassCurrent,
        props: {
          ...this.props,
          className: 'fadeOutStyle',
        },
        onClose: () => {
          const content = document.getElementById(currentStation);
          content.remove();
        }
      }), 'popup',
      -50, 0, currentStation, this.map,
      false, 'mainMap');

    // 绘制当前关注的站点标记
    const windowCurrentPoint = this.id + 'current-point-222';
    CommonMap.infoWindow(this.lng, this.lat,
      CommonMap.componentToHtml({
        component: CurrentPoint,
        props: {
          ...this.props,
          className: 'fadeOutStyle',
        },
        onClose: () => {
          const content = document.getElementById(windowCurrentPoint);
          content.remove();
        }
      }), 'popup',
      0, 75, windowCurrentPoint, this.map,
      false, 'mainMap');
    setTimeout(() => {
      document.getElementById(currentStation)?.remove();
      document.getElementById(windowCurrentPoint)?.remove();
    }, 2000)
  }

  /**
   * 在地图上绘制事件标记。
   */
  drawEventMarker() {
    // 创建事件标记
    const windowCurrentPoint = this.id + 'event-point';
    CommonMap.infoWindow(this.lng, this.lat,
      CommonMap.componentToHtml({
        component: CurrentPointLittle,
        props: this.props,
        onClose: () => {
          const content = document.getElementById(windowCurrentPoint);
          content.remove();
        }
      }), 'popup',
      0, 36, windowCurrentPoint, this.map,
      false, 'mainMap');
  }

  /**
   * 清除所有站点在地图上的标记和信息窗口。
   */
  static clearStation() {
    // 遍历缓存，清除每个站点的标记和信息窗口
    Object.keys(cache).forEach(key => {
      Station.clearStationByKey([key]);
    });
  }

  /**
   * 根据键值清除特定站点在地图上的标记和信息窗口。
   *
   * @param {Array} keys - 站点标识符的数组
   */
  static clearStationByKey(keys) {
    keys.forEach(key => {
      // 从缓存中获取站点实例
      if (!cache[key]) {
        return;
      }
      // 清除站点标记
      if (cache[key].stationsLayer) {
        cache[key].stationsLayer.getSource().clear(true);
      }
      // 关闭信息窗口
      CommonMap.closeInfoWindowByPrefix(cache[key].id, cache[key].map, false);
    });
  }

  /**
   * 清除所有站点的缓存。
   */
  static clearCache() {
    // 遍历缓存，清除每个站点的缓存
    Object.keys(cache).forEach(key => {
      Station.clearCacheByKey(key);
    });
  }
  /**
   * 静态方法：根据键清除缓存
   * 该方法用于根据给定的键从缓存中清除对应的站点数据，包括地图上的层和信息窗口。
   * @param {string} key - 缓存的键，用于定位特定的站点数据。
   */
  static clearCacheByKey(key) {
    // 如果缓存中存在stationsLayer，则从地图上移除该层
    if (cache[key].stationsLayer) {
      CommonMap.removeLayer(cache[key].map, cache[key].stationsLayer);
    }
    // 关闭与给定键相关的信息窗口
    CommonMap.closeInfoWindowByPrefix(cache[key].id, cache[key].map, false);
    // 从缓存中删除键对应的条目
    delete cache[key];
  }

  /**
   * 绘制左侧小信息窗口
   * 该方法用于在地图上绘制站点的左侧小信息窗口，并返回当前对象以支持链式调用。
   * @returns {object} 返回当前对象以支持链式调用。
   */
  drawSideLeftLittle() {
    // 确定信息窗口的唯一标识符
    const winId = this.id + '-info-left';
    // 在地图上创建信息窗口，使用预定义的组件和属性
    CommonMap.infoWindow(this.lng, this.lat,
      CommonMap.componentToHtml({
        component: StationInfoLittle,
        props: this.props,
        onClose: () => {
          // 当信息窗口关闭时，移除对应的DOM元素
          const content = document.getElementById(winId);
          content.remove();
        }
      }), 'popup',
      -30, 0, winId, this.map,
      false, 'mainMap');
    // 绘制站点标记
    this.drawMarker();
    // 返回当前对象以支持链式调用
    return this;
  }

  /**
   * 绘制右侧小信息窗口
   * 该方法用于在地图上绘制站点的右侧小信息窗口，并返回当前对象以支持链式调用。
   * @returns {object} 返回当前对象以支持链式调用。
   */
  drawSideRightLittle() {
    // 确定信息窗口的唯一标识符
    const winId = this.id + '-info-right';
    // 在地图上创建信息窗口，使用预定义的组件和属性，设置信息窗口向右偏移
    CommonMap.infoWindow(this.lng, this.lat,
      CommonMap.componentToHtml({
        component: StationInfoLittle,
        className: 'right',
        props: this.props,
        onClose: () => {
          // 当信息窗口关闭时，移除对应的DOM元素
          const content = document.getElementById(winId);
          content.remove();
        }
      }), 'popup',
      30, 0, winId, this.map,
      false, 'mainMap');
    // 绘制站点标记
    this.drawMarker();
    // 返回当前对象以支持链式调用
    return this;
  }
}
