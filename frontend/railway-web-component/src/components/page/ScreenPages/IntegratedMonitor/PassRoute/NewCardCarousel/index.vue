<template>
  <!-- 轮播图容器，使用Swiper库实现轮播效果 -->
  <div class="carousel-wrap swiper-containeres">
    <!-- Swiper的滑动内容包裹器 -->
    <div class="swiper-wrapper">
      <!-- 使用Slide组件渲染每个轮播项 -->
      <Slide
        v-for="(item, index) in carouselList"
        :key="`${item.alarmTime}-${index}`"
        :prop-data="item"
        :data-swiper-autoplay="getSwiperDelay(item)"
        :activeIndex="initialSlide"
        :slideIdx="index"
        @onVideoEnded="videoEnded"
        @onVideoError="videoError"
      />
    </div>
    <!-- 下一个滑动箭头，用于手动触发轮播 -->
    <!-- Add Arrows -->
    <div class="swiper-button-next"></div>
    <!-- 上一个滑动箭头，用于手动触发轮播 -->
    <div class="swiper-button-prev"></div>
  </div>
</template>

<script>
import ConstEnum from '@/components/common/ConstEnum'
import CommonMap from '@/components/common/Map/CommonMap'
import Slide from '@/components/page/ScreenPages/IntegratedMonitor/PassRoute/NewCardCarousel/Slide.vue'
import dayjs from 'dayjs'
import Swiper from 'swiper'
import 'swiper/css/swiper.min.css'

export default {
  components: { Slide },
  props: {
    carouselList: {
      type: Array,
      default: () => [],
    },
    onChange: {
      type: Function,
      default: () => {
        console.log('onChange')
      },
    },
    playing: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    ConstEnum() {
      return ConstEnum
    },
    isVideo() {
      // 当前项是否为视频
      return key => this.carouselList?.[key]?.videoUrl || ''
    },
  },
  watch: {
    carouselList: {
      // 当轮播图列表更新时，初始化Swiper实例
      handler: function () {
        this.$nextTick(() => {
          this.initSwiper()
        })
      },
      immediate: false,
    },
    playing(val) {
      const isVideo = this.isVideo(this.initialSlide)
      if (val && this.isEnd && isVideo) {
        this.isEnd = false
        this.swiper.slideNext()
      }
    },
  },
  beforeDestroy() {
    // 销毁Swiper实例以避免内存泄漏
    this.swiper.destroy()
  },
  data: function () {
    return {
      swiper: null, // swiper实例
      initialSlide: 0, // 初始滑动位置
      imgShowTime: (window?.sysConfig?.CAPTURE_IMG_SHOW_TIME || 3) * 1000, // 图片显示时间，配置读不到就默认3s
      // videoShowTime: (window?.sysConfig?.CAPTURE_VIDEO_SHOW_TIME || 0) * 1000, // 视频显示时间，配置读不到就默认10s
      isSlide: true, // 记录滚动项是否为视频
      isEnd: false, // 记录视频是否播放结束
    }
  },
  mounted() {
    // 页面挂载后初始化Swiper实例
    this.initSwiper()
  },
  methods: {
    /**
     * 获取当前时刻的dayjs对象。
     *
     * @returns {Object} 返回一个dayjs对象，表示当前时间。
     */
    dayjs() {
      return dayjs()
    },
    /**
     * 初始化Swiper实例。
     *
     * 此函数用于创建或更新Swiper实例，根据当前的状态和配置。如果已经存在一个Swiper实例，
     * 则先销毁它，然后创建一个新的实例。这确保了Swiper实例总是根据最新的需求进行配置。
     */
    initSwiper() {
      const that = this
      // 如果Swiper实例已存在，先销毁它
      if (this.swiper) {
        this.swiper.destroy()
      }
      // 创建新的Swiper实例
      this.swiper = new Swiper('.swiper-containeres', {
        containerClass: 'swiper-containeres',
        initialSlide: that.initialSlide,
        direction: 'horizontal',
        slidesPerView: 3,
        centeredSlides: true,
        spaceBetween: 30,
        simulateTouch: false,
        observer: true, // 监听Swiper的容器变化
        observeParents: true, // 监听父元素变化
        autoplay: {
          delay: 8000,
          stopOnLastSlide: true,
          disableOnInteraction: false,
        },
        pagination: {
          el: '.swiper-pagination',
          clickable: true,
        },
        navigation: {
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev',
        },
        on: {
          slideChange: function () {
            that.slideItemEnd(this.activeIndex)
            // 当前项
            that.initialSlide = this.activeIndex
            // console.log('carouselList-0-', that.initialSlide);
            const isVideo = that.isVideo(that.initialSlide)
            if (isVideo && !that.isSlide) {
              // 如果是视频停止播放
              that.isSlide = false
              that.stopPlay()
            } else {
              that.isSlide = true
              that.startPlay()
              that.onChange(this.activeIndex)
            }
          },
        },
      })

      // 初始化是视频让它播放完后滚动
      setTimeout(() => {
        const isVideo = this.isVideo(0)
        if (isVideo) this.stopPlay()
        else this.startPlay()
      }, 20)
    },

    slideItemEnd(activeIndex) {
      // 下/上一个滚动项是否为视频，如果是视频标记未播放
      let _idx = -1
      if (this.initialSlide > activeIndex) {
        _idx = this.initialSlide - 1
      } else {
        _idx =
          this.initialSlide === activeIndex
            ? this.initialSlide
            : this.initialSlide + 1
      }
      const isVideo = this.isVideo(_idx)
      this.isEnd = isVideo ? false : true
    },

    setSwiperSlide(values) {
      this.swiper.slideNext()
      // 下一个滚动项是否为视频
      const isVideo = this.isVideo(values.activeIndex + 1)
      this.isSlide = isVideo ? false : true
      if (!this.isSlide) this.stopPlay()
    },
    videoEnded(values = {}) {
      // 是否是当前放大的视频项
      if (this.initialSlide !== values.activeIndex) return
      this.isEnd = true
      // 已经停止滚动
      if (!this.playing) return
      setTimeout(() => {
        this.setSwiperSlide(values)
      }, 500)
    },

    videoError(values = {}) {
      // 是否是当前放大的视频项
      if (this.initialSlide !== values.activeIndex) return
      this.isEnd = true
      // 已经停止滚动
      if (!this.playing) return
      // 不读配置时间了
      // 如果视频间隔太短滚动太快，加上了 1.5s 的时间
      //   const time =
      //     this.videoShowTime > 1000
      //       ? this.videoShowTime
      //       : this.videoShowTime + 1500;
      setTimeout(() => {
        this.setSwiperSlide(values)
      }, 1500)
    },

    getSwiperDelay(item) {
      if (item && item.videoUrl) {
        // 设置视频一个最大的时间，为了让视频播放完成后自己滚动下一个
        return 24 * 60 * 60 * 1000 // this.videoShowTime;
      }
      return this.imgShowTime
    },

    /**
     * 向Swiper中添加一个新的滑块。
     *
     * @param {Object} props - 新滑块的属性对象。此对象可以包含滑块的具体内容和配置。
     */
    appendSlide(props = {}) {
      console.table(props)
      const [, el] = CommonMap.componentToHtml({
        component: Slide,
        props,
      })
      this.swiper.appendSlide(el)
      this.swiper.update()
    },

    /**
     * 更新Swiper的初始滑块索引。
     *
     * 此函数用于在滑块数量改变后，调整Swiper的初始滑块位置。它通过减去一个指定的数值来更新初始滑块索引，
     * 使得Swiper可以保持在相对应的位置。
     *
     * @param {Number} num - 需要从初始滑块索引中减去的数值。
     */
    updateInitialSlide(num) {
      this.initialSlide = num
    },
    startPlay() {
      if (this.swiper && this.swiper.autoplay) {
        this.swiper.autoplay.start()
      }
    },
    stopPlay() {
      if (this.swiper && this.swiper.autoplay) {
        this.swiper.autoplay.stop()
      }
    },
    cutSwiper(index) {
      if (this.swiper) {
        this.swiper.slideTo(index)
      }
    },
  },
}
</script>

<style lang="less" src="./index.less" />
