.carousel-wrap.swiper-containeres {
  overflow: hidden;
  position: absolute;
  right: 50%;
  bottom: 80px;
  width: 100%;
  transform: translate(50%, 0px) rotateY(180deg);
  .swiper-button-prev {
    width: 40px;
    height: 40px;
    &::after {
      content: "";
      width: 40px;
      height: 40px;
      background: url("../img/icon_swiper_left_40_n.svg") 100% 100% no-repeat;
      background-size: 100% 100%;
    }
  }
  .swiper-button-next {
    width: 40px;
    height: 40px;
    &::after {
      content: "";
      width: 40px;
      height: 40px;
      background: url("../img/icon_swiper_right_40_n.svg") 100% 100% no-repeat;
      background-size: 100% 100%;
    }
  }

  .swiper-slide {
    width: 500px;
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .swiper-slide-active {
    .img-container {
      font-size: 12px;
      width: 500px;
      height: 316px;
      background: url("../img/bg_pass_500.gif") 100% 100% no-repeat;
      background-size: 100% 100%;
      top: 0;
      opacity: 1;
      padding: 32px 36px;
      box-sizing: border-box;
      transform: scale(1);

      .pass-time {
        right: 32px;
        top: 30px;
        width: 138px;
        height: 32px;
        background-image: linear-gradient(82deg, #5493f1 0%, #2a5adf 96%);
        border-radius: 4px 0px 8px 4px;
      }

      img {
        width: 428px;
        height: 252px;
      }

      .video-wrap {
        width: 428px;
        height: 252px;
      }
      .video-player {
        width: 100%;
        height: 100%;
        /deep/ video {
          object-fit: cover;
        }
        /deep/ .vjs-big-play-button {
          top: 50% !important;
          left: 50% !important;
          height: 30px !important;
          line-height: 30px !important;
          width: 30px;
          border-radius: 50%;
          background-color: rgba(8, 0, 0, 0.4);
          border: none;
          margin: 0 !important;
          transform: translate(-50%, -50%);
        }
        /deep/ .video-js {
          height: 100% !important;
          border-radius: 4px;
        }
        /deep/ .vjs-poster {
          border-radius: 4px;
          background-size: cover;
        }
        /deep/ .vjs-modal-dialog-content {
          display: none;
        }
        /deep/ .vjs-icon-placeholder:before {
          font-size: 24px !important;
          color: #fffdef !important;
        }
      }
    }
  }

  .img-container {
    position: relative;
    background: url("../img/bg_pass_300.gif") 100% 100% no-repeat;
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 300px;
    height: 190px;
    opacity: 0.5;
    transform: scale(0.9);
    transform-origin: center center;

    img {
      width: 264px;
      height: 154px;
      transform: rotateY(180deg);
    }

    .video-wrap {
      width: 264px;
      height: 154px;
    }
    .video-player {
      width: 100%;
      height: 100%;
      transform: rotateY(180deg);
      /deep/ video {
        object-fit: cover;
      }
      /deep/ .vjs-big-play-button {
        top: 50% !important;
        left: 50% !important;
        height: 30px !important;
        line-height: 30px !important;
        width: 30px;
        border-radius: 50%;
        background-color: rgba(8, 0, 0, 0.4);
        border: none;
        margin: 0 !important;
        transform: translate(-50%, -50%);
      }
      /deep/ .video-js {
        height: 100% !important;
        border-radius: 4px;
      }
      /deep/ .vjs-poster {
        border-radius: 4px;
        background-size: cover;
      }
      /deep/ .vjs-modal-dialog-content {
        display: none;
      }
      /deep/ .vjs-icon-placeholder:before {
        font-size: 24px !important;
        color: #fffdef !important;
      }
    }

    .pass-time {
      position: absolute;
      right: 18px;
      top: 18px;
      width: 120px;
      height: 29px;
      font-family: PingFangSC-Medium;
      font-size: 12px;
      color: #e8f3ff;
      font-weight: 500;
      background-image: linear-gradient(180deg, #073143 0%, #031620 100%);
      border-radius: 4px 0px 8px 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      transform: rotateY(180deg);
      z-index: 1;
    }
  }
}
