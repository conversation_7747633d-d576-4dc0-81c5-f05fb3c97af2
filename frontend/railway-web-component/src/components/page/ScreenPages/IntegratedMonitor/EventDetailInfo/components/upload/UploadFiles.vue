<template>
  <div>
    <div class="image-video">
      <!-- 遍历urlList数组，生成文件内容展示 -->
      <div
        class="file-content"
        v-for="(item, index) in urlList"
        :key="index"
        @click="clickOne(index)"
        v-loading="item.loading"
      >
        <!-- 如果文件类型为图片，使用el-image组件展示 -->
        <el-image
          v-if="item.type === '1'"
          class="file-image"
          :src="item.fileUrl"
          fit="cover"
        />
        <!-- 如果文件类型为视频，使用video-player组件展示 -->
        <video-player
          v-if="item.type === '2'"
          class="file-video vjs-custom-skin"
          :playsinline="true"
          :options="item.videoOptions"
          @canplay="saveVideoCoverImg($event, item)"
        />
        <!-- 如果文件类型为文档，使用img标签展示下载图标 -->
        <img
          v-if="item.type === '3'"
          alt="下载"
          :src="require(`@/assets/images/alarmEvent/alarm/download_doc.png`)"
          class="file-image"
          @click.stop="downloadHandle(item.fileUrl, item.fileName)"
          :title="item.fileName"
        />
        <!-- 如果处于编辑模式且文件未加载中且不是文档类型，显示操作按钮 -->
        <div
          v-if="isEdit && !item.loading && item.type !== '3'"
          class="btns"
          @click.stop
        >
          <i class="el-icon-view btn" @click.stop="clickOne(index)"></i>
          <i class="el-icon-delete btn" @click.stop="deleteOne(index)"></i>
        </div>
      </div>
      <!-- 如果处于编辑模式且文件数量未达到最大值，显示上传按钮 -->
      <div class="file-content" v-if="isEdit && urlList.length < maxLength">
        <el-upload
          ref="upload"
          class="file-upload"
          list-type="picture-card"
          action=""
          :accept="uploadAccept"
          :http-request="doUpload"
          :before-upload="beforeUpload"
        >
          <i class="el-icon-plus"></i>
        </el-upload>
      </div>
    </div>
    <!-- 文件全屏预览组件 -->
    <FileFullScreen
      v-if="showImageViwer"
      :fileFullList="fileFullList"
      @closeFull="closeImgViwer"
    />
  </div>
</template>
<script>
import api from '@/api';
import { downloadFile } from '@/api/service/common';
import FileFullScreen from '@/components/common/file-full-screen.vue';
import $ from 'jquery';
import { UUID } from 'uuidjs';
import ImageViewer from './image-viewer.vue';
import vue from '@/main';

// 定义文件类型及其MIME类型
const fileType = {
  jpg: 'jpg',
  jpeg: 'jpeg',
  png: 'png',
  gif: 'gif',
  mp4: 'mp4',
  docx: 'vnd.openxmlformats-officedocument.wordprocessingml.document',
  txt: 'plain',
  pdf: 'pdf',
  doc: 'msword',
  ppt: 'vnd.ms-powerpoint',
  pptx: 'vnd.openxmlformats-officedocument.presentationml.presentation',
  svg: 'svg+xml',
  includes: (type, accept) => {
    if (!accept || !type) {
      return false;
    }
    const legalType = Object.keys(fileType).find(
      (key) =>
        typeof fileType[key] !== 'function' &&
        accept.includes(key) &&
        fileType[key] === type
    );
    return !!legalType;
  },
};
export default {
  components: {
    FileFullScreen,
    ImageViewer,
  },
  props: {
    files: {
      /**
       * 文件列表属性，包含文件类型、ID、名称和URL
       */
      type: Array,
      default: () => [],
    },
    isEdit: {
      // 是否处于编辑模式
      type: Boolean,
      default: false,
    },
    maxLength: {
      // 最大上传文件数量
      type: Number,
      default: 99,
    },
    maxSize: {
      // 最大上传文件大小（MB）
      type: Number,
      default: 100,
    },
    accept: {
      // 允许上传的文件格式
      type: String,
      default: 'jpg,jpeg,png,gif,mp4',
    },
    unAcceptErrMsg: {
      // 不允许上传时的提示信息
      type: String,
      default: '只支持上传.jpg,.jpeg,.png,.gif格式的文件',
    },
  },
  data() {
    return {
      uploadAccept: '', // 允许上传的文件格式
      urlList: [], // 文件列表
      fileFullList: {}, // 全屏预览文件列表
      showImageViwer: false, // 是否显示预览
      imageIndex: 0, // 当前预览的文件索引
      videoOptions: {
        // 视频播放器选项
        muted: true,
        language: 'zh-CN',
        preload: 'auto',
        playbackRates: [0.5, 1.0, 1.5, 2.0],
        autoplay: false,
        loop: false,
        fluid: true,
        hls: true,
        html5: {
          hls: {
            withCredentials: false,
          },
        },
        sources: [
          {
            type: 'video/mp4',
            src: '',
          },
        ],
        aspectRatio: '16:9',
        choosed: false,
        notSupportedMessage: ' ',
        controlBar: {
          volumeControl: false,
          timeDivider: false,
          durationDisplay: false,
          remainingTimeDisplay: false,
          fullscreenToggle: true,
        },
      },
    };
  },
  watch: {
    files: {
      handler: function () {
        this.initList();
      },
      deep: true,
      immediate: true,
    },
    accept: {
      handler: function (accept) {
        accept = accept.replace(/ /g, '').split(',');
        let uploadAccept = [];
        accept.forEach((item) => {
          uploadAccept.push('.' + item.toLowerCase());
        });
        this.uploadAccept = uploadAccept.join(',');
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    // 处理文件下载
    downloadHandle(resourceUrl, fileName) {
      api.downloadExcelPost(
        `${vue.$env.VUE_APP_REQ_PREFIX_PLAT}/system/downloadByUrl`,
        { fileName, url: resourceUrl }
      );
    },
    /**
     * 提交文件进行上传。
     * @param {Object} data - 包含文件及相关信息的对象。
     * @param {File} data.file - 待上传的文件。
     */
    doUpload(data) {
      let _this = this;
      const file = data.file;
      const item = {
        uuid: this.getUUID(),
        loading: true,
        type: '3',
      };
      // 根据文件类型设置item的类型
      if (file.type.includes('image')) {
        item.type = '1';
      }
      if (file.type.includes('video')) {
        item.type = '2';
        item.videoOptions = JSON.parse(JSON.stringify(this.videoOptions));
      }
      // 使用FileReader读取文件并转换为DataURL
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        const arr = reader.result.split(',');
        const mime = arr[0].match(/:(.*?);/)[1];
        const bytes = atob(arr[1]);
        let n = bytes.length;
        const ia = new Uint8Array(n);
        while (n--) {
          ia[n] = bytes.charCodeAt(n);
        }
        const blob = new Blob([ia], { type: mime });
        const fileUrl = window.URL.createObjectURL(blob);
        item.fileUrl = fileUrl;
        if (item.type === '2') {
          item.videoOptions.sources[0].src = fileUrl;
        }
        this.urlList.push(item);
        // 构建FormData并发送POST请求进行文件上传
        const formData = new FormData();
        formData.append('file', file);
        api
          .post(`${this.$env.VUE_APP_REQ_PREFIX_PLAT}/system/upload`, {
            base64: reader.result,
            fileName: file.name,
          })
          .then((res) => {
            if (res.code == 200) {
              item.loading = false;
              item.fileId = res.data.fileId;
              item.fileName = res.data.fileName;
              item.fileUrl = res.data.fileUrl;
              this.change();
            } else {
              item.loading = false;
              this.$message({
                type: 'error',
                message: '上传失败',
              });
              this.uploadError(item);
            }
          });
      };
    },
    /**
     * 将文件转换为Base64编码。
     * @param {File} file - 待转换的文件。
     * @returns {Promise} - 包含Base64编码结果的Promise对象。
     */
    getBase64(file) {
      return new Promise(function (resolve, reject) {
        let reader = new FileReader();
        let imgResult = '';
        reader.readAsDataURL(file);
        reader.onload = function () {
          imgResult = reader.result;
        };
        reader.onerror = function (error) {
          reject(error);
        };
        reader.onloadend = function () {
          resolve(imgResult);
        };
      });
    },
    /**
     * 处理文件上传失败的情况。
     * @param {Object} item - 上传失败的文件项。
     */
    uploadError(item) {
      for (let i = 0; i < this.urlList.length; i++) {
        if (this.urlList[i].uuid === item.uuid) {
          this.urlList.splice(i, 1);
          return false;
        }
      }
    },
    /**
     * 保存视频封面图
     * @param {Object} e - 事件对象
     * @param {Object} item - 视频项数据
     * @returns {boolean} - 如果视频项已有UUID或封面URL，则不执行操作，返回false
     *
     * 此函数用于从视频的第一帧生成封面图像，并将其上传到服务器。
     * 它首先检查视频项是否已经具有UUID或封面URL，如果是，则不执行任何操作。
     * 如果没有，它会创建一个canvas元素，从视频中绘制第一帧到canvas上，
     * 然后将canvas内容转换为base64编码的图像URL，并上传到服务器。
     * 上传成功后，更新视频项的封面URL，并触发变更事件。
     */
    saveVideoCoverImg(e, item) {
      // 检查视频项是否已具有UUID或封面URL，如果是，则不处理
      if (!item.uuid || item.coverUrl !== undefined) {
        return false;
      }
      // 初始化封面URL为空字符串
      item.coverUrl = '';
      // 获取视频元素
      const video = $(e.el_).find('video')[0];
      // 创建canvas元素
      const canvas = document.createElement('canvas');
      // 设置canvas的宽度和高度，如果视频的宽高不可用，则使用默认值80
      canvas.width = video.clientWidth || 80;
      canvas.height = video.clientHeight || 80;
      // 获取canvas的2D绘图上下文
      const ctx = canvas.getContext('2d');
      // 将视频的第一帧绘制到canvas上
      ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
      // 将canvas内容转换为base64编码的图像URL
      const img = canvas.toDataURL('image/png');
      // 定义上传的文件名
      const name = '封面.png';
      // 发送POST请求，上传封面图像
      api
        .post(`${this.$env.VUE_APP_REQ_PREFIX_PLAT}/system/upload`, {
          base64: img,
          fileName: name,
        })
        .then((res) => {
          // 检查上传是否成功，如果成功，则更新视频项的封面URL，并触发变更事件
          if (res.code == 200) {
            item.coverUrl = res.data.fileUrl;
            this.change(0);
          }
        });
    },
    /**
     * 在文件上传之前进行验证。
     * @param {Object} file - 需要上传的文件对象。
     * @returns {boolean} - 如果文件不满足条件，则返回false阻止上传。
     *
     * 此函数用于在用户尝试上传文件之前检查文件是否符合要求。
     * 它验证了文件类型、文件数量和文件大小是否符合设定的限制。
     * 如果文件不符合任何条件，将向用户显示警告消息，并阻止上传。
     */
    beforeUpload(file) {
      // 从文件类型中提取MIME类型的主要和子类型
      const [, accept] = file.type.split('/');
      // 检查文件类型是否在允许的上传类型列表中
      if (!fileType.includes(accept, this.uploadAccept)) {
        this.$message({
          type: 'warning',
          message: `上传格式仅支持${this.accept}`,
        });
        return false;
      }
      // 检查已上传文件数量加上当前文件是否超过最大允许数量
      if (this.urlList.length + 1 > this.maxLength) {
        this.$message({
          type: 'warning',
          message: '最多允许上传' + this.maxLength + '个文件',
        });
        return false;
      }
      // 检查文件大小是否超过最大允许大小
      if (file.size > 1024 * 1024 * this.maxSize) {
        this.$message({
          type: 'warning',
          message: '大小超出' + this.maxSize + 'MB' + '，请调整文件大小后上传',
        });
        return false;
      }
    },
    /**
     * 初始化文件列表。
     */
    initList() {
      const files = JSON.parse(JSON.stringify(this.files));
      files.forEach((item) => {
        if (item.type === '2') {
          item.type = '2';
          item.videoOptions = JSON.parse(JSON.stringify(this.videoOptions));
          item.videoOptions.sources[0].src = item.fileUrl;
        }
      });
      this.urlList = files;
      this.change(0);
    },
    /**
     * 点击预览图片或视频。
     * @param {number} index - 被点击项的索引。
     */
    clickOne(index) {
      this.imageIndex = index;
      const fileFullList = {};
      fileFullList.isVideo = true;
      fileFullList.index = index;
      fileFullList.videoImgUrl = [];
      this.urlList.map((item2) => {
        if (['1', '2'].includes(item2.type)) {
          fileFullList.videoImgUrl.push(item2.fileUrl);
        }
      });
      this.fileFullList = fileFullList;
      this.showImageViwer = true;
    },
    /**
     * 删除一个文件项。
     * @param {number} index - 被删除项的索引。
     */
    deleteOne(index) {
      this.urlList.splice(index, 1);
      this.change(-1);
    },
    /**
     * 文件列表改变时触发的事件。
     * @param {number} type - 变更类型，可为0或其他。
     */
    change(type) {
      const files = [];
      this.urlList.forEach((item) => {
        if (!item.loading) {
          files.push(item);
        }
      });
      this.$emit('change', files, type);
    },
    /**
     * 关闭图片预览窗口。
     */
    closeImgViwer() {
      this.showImageViwer = false;
    },
    /**
     * 生成一个UUID。
     * @returns {string} - 生成的UUID。
     */
    getUUID() {
      return UUID.generate();
    },
  },
};
</script>
<style lang='less' scoped>
.image-video {
  display: flex;
  flex-wrap: wrap;
  .file-content {
    width: 80px;
    height: 80px;
    border-radius: 4px;
    margin: 0 8px 8px 0;
    cursor: pointer;
    overflow: hidden;
    position: relative;
    &:hover {
      .btns {
        display: flex;
      }
    }
    .btns {
      display: none;
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 2;
      align-items: center;
      justify-content: center;
      color: #fff;
      font-size: 18px;
      background: rgba(0, 0, 0, 0.4);
      cursor: default;
      .btn {
        margin: 0 5px;
        cursor: pointer;
        /deep/ &.el-icon-view:before {
          font-family: iconfont;
          content: '\E605';
        }
      }
    }
    .file-upload {
      width: 100%;
      height: 100%;
      /deep/ .el-upload-list {
        display: none;
      }
      /deep/ .el-upload--picture-card {
        width: 100%;
        height: 100%;
        border-radius: 4px;
        line-height: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px dashed #c1ccda;
        background: #fbfdff;
      }
    }
    .file-image {
      width: 100%;
      height: 100%;
    }
    .file-video {
      width: 100%;
      height: 100%;
      pointer-events: none;
      /deep/ video {
        object-fit: cover;
      }
      /deep/ .vjs-big-play-button {
        top: 50% !important;
        left: 50% !important;
        height: 30px !important;
        line-height: 30px !important;
        width: 30px;
        border-radius: 50%;
        background-color: rgba(8, 0, 0, 0.4);
        border: none;
        margin: 0 !important;
        transform: translate(-50%, -50%);
      }
      /deep/ .video-js {
        height: 100% !important;
        border-radius: 4px;
      }
      /deep/ .vjs-poster {
        border-radius: 4px;
        background-size: cover;
      }
      /deep/ .vjs-modal-dialog-content {
        display: none;
      }
      /deep/ .vjs-icon-placeholder:before {
        font-size: 24px !important;
        color: #fffdef !important;
      }
    }
  }
}
</style>
