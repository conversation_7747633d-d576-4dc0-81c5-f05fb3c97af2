<!--
 * @Description  : 转领导批示处理弹窗
 * <AUTHOR> wnj
 * @Date         : 2023-12-12 15:40:10
 * @LastEditors  : wnj
 * @LastEditTime : 2023-12-26 14:50:26
 * @FilePath     :  / src / components / page / alarmEvent / components / alarmRight / alarmLeaderDialog.vue
-->
<template>
  <!-- 使用自定义面板组件CtlPanel，包含关闭功能 -->
  <CtlPanel class='evt-handle-wrap alarm-dispatch-outter false-alarm-wrap' :close='toClose'>
    <template v-slot:title>
      <!-- 显示传入的标题 -->
      {{ propData.title }}
    </template>
    <template v-slot:content>
      <!-- 内容区域，包含加载状态 -->
      <div class='dispatch-con evt-handle-form el-form' v-loading='dataLoading'>
        <!-- 选择领导下拉树 -->
        <div class='dispose-item-box'>
          <div class='dispose-item-left'>
            <div class='dispose-xinghao'>*</div>
            <div class='dispose-item-label'>选择领导</div>
          </div>
          <div class='dispose-item-right people-select-tree-box'>
            <!-- 使用el-select组件选择领导 -->
            <el-select
              ref='peopleSelect'
              class='c-select'
              popper-class='c-select-dropdown'
              v-model='leaderValue'
              placeholder='请选择人员'
              collapse-tags
              :popper-append-to-body='false'
              filterable
              :filter-method='selectTreeFilter'
              @change='selectChange'
            >
              <el-option
                :value='leaderObject'
                style='height: auto'
                class='people-option-tree-box'
              >
                <div class='people-option-tree-div'>
                  <!-- 使用el-tree组件显示领导树 -->
                  <el-tree
                    :data='leadersTree'
                    node-key='id'
                    ref='leadersTree'
                    accordion
                    default-expand-all
                    highlight-current
                    :show-checkbox='false'
                    :check-on-click-node='true'
                    :props='defaultProps'
                    icon-class='el-icon-arrow-down'
                    :filter-node-method='filterNode'
                    @check-change='handleCheck'
                  >
                    <div
                      class='custom-tree-node'
                      slot-scope='{ node, data }'
                    >
                      <div>
                        <div class='custom-tree-node-label'>
                          <!-- 如果有userId则显示图片 -->
                          <img
                            v-if='data.userId'
                            alt='人员'
                            class='people-tree-img'
                            src='@/assets/images/alarmEvent/alarm/treePersion.png'
                          />
                          <div
                            :c-tip='data.label'
                            c-tip-placement='top'
                            class='node-label'
                            :style="{
                                                        color: data.disabled
                                                            ? '#bbbbbb'
                                                            : data.checked ===
                                                              true
                                                            ? '#FFF'
                                                            : '#ffffff',
                                                        'max-width':
                                                            !data.userId
                                                                ? '250px'
                                                                : '210px',
                                                    }"
                          >
                            {{ data.label }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </el-tree>
                </div>
              </el-option>
            </el-select>
          </div>
        </div>
        <!-- 批示内容输入框 -->
        <div class='dispose-item-box'>
          <div class='dispose-item-left'>
            <div class='dispose-xinghao'>*</div>
            <div class='dispose-item-label'>批示内容</div>
          </div>
          <div class='dispose-item-right'>
            <!-- 使用AlarmPhrases组件输入批示内容 -->
            <AlarmPhrases
              v-model='remarksText'
              :approvalListParam='{
                catCode: "LEADERFLAG"
              }'
              placeholder='请输入内容'
              :maxLength='500'
              @update='isPhrasesUnfold'
              :isUnfold.sync='isUnfold'
            />
          </div>
        </div>
      </div>
      <!-- 提交和取消按钮 -->
      <div class='dispatch-buttons'>
        <div class='dispatch-button dispatch-button-blue' @click='toSubmit'>
          <span>确定</span>
        </div>
        <div class='dispatch-button' @click='toClose'>
          <span>取消</span>
        </div>
      </div>
    </template>
  </CtlPanel>
</template>
<script>
import api from '@/api';
import CtlPanel from '@/components/page/ScreenPages/IntegratedMonitor/CtlPanel/index.vue';
import AlarmPhrases from '../components/alarm-phrases';
export default {
  components: {
    CtlPanel,
    AlarmPhrases
  },
  props: {
    propData: {
      type: Object,
      default: {}
    },
    close: {
      type: Function,
      default: () => {
        console.log('close');
      }
    },
    okCalBack: {
      type: Function,
      default: () => {
        console.log('okCalBack');
      }
    }
  },
  data() {
    return {
      alarmInfo: this.propData.alarmInfo, // 报警信息
      dataLoading: true, // 数据加载状态
      leaderValue: '', // 选择的领导值
      leaderObject: [], // 前端展示的领导对象
      leaderIdList: [], // 领导人ID列表
      leadersTree: [], // 领导人员树数据
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      isUnfold: '', // 常用语展开状态
      remarksText: '' // 批示内容
    };
  },
  created() {
    this.getLeaderTree(); // 组件创建时获取领导树数据
  },
  methods: {
    /**
     * 常用语判断弹窗高度
     */
    isPhrasesUnfold(val) {
      this.isUnfold = val;
    },
    /**
     * 改变加载等待状态
     */
    changeLoading() {
      this.dataLoading = false;
    },
    /**
     * 人员树结点过滤
     */
    selectTreeFilter(text) {
      this.$refs.leadersTree.filter(text);
    },
    filterNode(value, data) {
      if (!value) {
        return true;
      }
      return data.label.indexOf(value) !== -1;
    },
    /**
     * 处理空数据成空字符串
     * @param str
     */
    formatNullDataToString(str) {
      if (str) {
        return str;
      } else {
        return '';
      }
    },
    /**
     * 获取领导人数据
     */
    getLeaderTree() {
      let params = {};
      params.roleId = '8'; // 角色ID，前端写死
      params.provinceId = this.formatNullDataToString(
        this.alarmInfo.order.provinceId
      );
      params.cityId = this.formatNullDataToString(
        this.alarmInfo.order.cityId
      );
      params.countyId = this.formatNullDataToString(
        this.alarmInfo.order.countyId
      );
      params.deviceCode = this.formatNullDataToString(
        this.alarmInfo.order.deviceCode
      );
      params.warningSource = this.formatNullDataToString(
        this.alarmInfo.order.warningSource
      );
      api.post(
        `${this.$env.VUE_APP_REQ_PREFIX_PLAT}/event/getLeaderByTenantIdShowTree`,
        params
      ).then((res) => {
        if (res.code == 200) {
          let treeData = res.data;
          this.formatPeopleTree(treeData);
          this.leadersTree = treeData;
          this.dataLoading = false;
        } else {
          this.dataLoading = false;
          this.$message({
            type: 'error',
            message: res.msg || '领导人数据查询失败！'
          });
        }
      });
    },
    /**
     * 格式化树
     */
    formatPeopleTree(tree) {
      for (const element of tree) {
        element.checked = false;
        element.id = element.nodeId ? element.nodeId : element.userId;
        element.label = element.userName
          ? element.userName
          : element.nodeName;
        if (!element.userId) {
          element.disabled = true;
        }
        if (element.children && element.children.length > 0) {
          this.formatPeopleTree(element.children);
        }
      }
    },
    /**
     * 选择领导人树
     */
    selectChange(e) {
      let arrNew = [];
      const dataLength = this.leaderObject.length;
      const eLength = e.length;
      for (let i = 0; i < dataLength; i++) {
        for (let j = 0; j < eLength; j++) {
          if (e[j] === this.leaderObject[i].label) {
            arrNew.push(this.leaderObject[i]);
          }
        }
      }
      this.$refs.leadersTree.setCheckedNodes(arrNew); // 设置勾选的值
    },
    /**
     * 选中树
     */
    handleCheck(data, item) {
      if (item) {
        this.$refs.leadersTree.setCheckedKeys([data.id]);
      }
      this.handleCheckChange();
    },
    // 处理选中数据
    handleCheckChange() {
      let res = this.$refs.leadersTree.getCheckedNodes(true, true); // 获取选中的节点
      let arr = [];
      let arrId = [];
      res.forEach((item) => {
        this.leaderValue = item.label;
        arr.push(item);
        arrId.push(item.id);
      });
      this.leaderObject = arr;
      this.leaderIdList = arrId;
      // 设置选中状态
      this.formatPeopleTree(this.leadersTree);
      let allRes = this.$refs.leadersTree.getCheckedNodes();
      setTimeout(() => {
        allRes.forEach((item) => {
          item.checked = true;
        });
      }, 200);
    },
    /**
     * 确认提交
     */
    toSubmit() {
      if (this.dataLoading) {
        return false;
      }
      if (this.leaderValue === '') {
        this.$message({
          type: 'error',
          message: '请选择人员'
        });
        return false;
      }
      if (this.remarksText === '') {
        this.$message({
          type: 'error',
          message: '请完善批示内容'
        });
        return false;
      }
      const params = {};
      params.orderId = this.alarmInfo.order.warningOrderId;
      params.content = this.remarksText;
      params.leaderIdList = this.leaderIdList;
      api.post(
        `${this.$env.VUE_APP_REQ_PREFIX_PLAT}/event/addLeaderAudit`,
        params
      ).then((res) => {
        if (res.code == 200) {
          this.$message({
            type: 'success',
            message: res.msg
          });
          this.toClose();
          this.okCalBack();
        } else {
          this.$message({
            type: 'error',
            message: res.msg || '转领导批示失败！'
          });
        }
      });
    },
    /**
     * 取消提交
     */
    toClose() {
      this.close && this.close();
    }
  }
};
</script>
<style lang='less' src='./index.less' />
<style lang='less' scoped>
// 人员树样式
.people-select-tree-box {
  /deep/ .el-tree {
    min-height: 32px;
    background: transparent;
    .el-tree-node__content {
      background: transparent;
      .el-checkbox {
        .el-checkbox__input {
          .el-checkbox__inner {
            background: rgba(25, 137, 250, 0.7);
            border: 0;
          }
        }
      }
      .el-icon-arrow-down {
        &:before {
          content: "\e732";
          font-family: "iconfont" !important;
          color: #ffffff;
          font-size: 14px;
        }
      }
      .el-icon-arrow-down.is-leaf {
        opacity: 0;
        margin-left: -18px;
      }
    }
    .el-tree-node__content:hover {
      background: rgba(25, 137, 250, 0.4);
    }
    .el-tree-node__children {
      .is-current {
        background: rgba(25, 137, 250, 0.4);
      }
    }
    .custom-tree-node {
      font-size: 14px;
      overflow: hidden;
      height: 100%;
      line-height: 26px;
      text-overflow: ellipsis;
      white-space: normal;
      color: #ffffff;
      .custom-tree-node-label {
        display: flex;
        width: 100%;
        align-items: center;
        .people-tree-img {
          margin-left: 2px;
        }
        .node-label {
          color: #ffffff;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          margin: 0 4px;
        }
      }
    }
    // 无数据时的样式
    .el-tree__empty-block {
      min-height: 180px;
      .el-tree__empty-text {
        display: flex;
        flex-direction: column;
        color: #ffffff;
        &:before {
          // content: url("~@/assets/image/monitorWarnNew/alarmList/noData-ly.png");
          width: 68px;
          height: 68px;
        }
      }
    }
  }
  /deep/ .el-select__tags {
    .el-tag.el-tag--info {
      color: rgba(13, 201, 133, 1);
      background: rgba(13, 201, 133, 0.2);
      line-height: 20px;
      border: 0;
      .el-tag__close {
        color: #0dc985;
        background: transparent;
        transform: scale(0.7);
        border: 0;
      }
    }
  }
  /deep/ .el-input .el-input__suffix .el-input__suffix-inner .el-input__icon {
    transform: unset !important;
    &:before {
      content: "\e72c";
      font-family: iconfont;
    }
  }
}

/deep/ .c-select-dropdown .el-select-dropdown__list {
  max-height: 180px;
  padding: 0;
}

/deep/ .c-select-dropdown .el-select-dropdown__list .people-option-tree-box {
  padding: unset;
  &:hover {
    background: transparent !important;
  }
  .people-option-tree-div {
    .el-tree {
      background: transparent;
      .el-tree-node__content {
        background: transparent;
        .el-checkbox {
          .el-checkbox__input {
            .el-checkbox__inner {
              background: rgba(25, 137, 250, 0.7);
              border: 0;
            }
          }
        }
        .el-icon-arrow-down {
          &:before {
            content: "\e732";
            font-family: "iconfont" !important;
            color: #ffffff;
            font-size: 14px;
          }
        }
        .el-icon-arrow-down.is-leaf {
          opacity: 0;
          margin-left: -18px;
        }
      }
      .el-tree-node__content:hover {
        background: rgba(25, 137, 250, 0.4);
      }
      .custom-tree-node {
        font-size: 14px;
        overflow: hidden;
        height: 100%;
        line-height: 26px;
        text-overflow: ellipsis;
        white-space: normal;
        color: #ffffff;
        .custom-tree-node-label {
          display: flex;
          width: 100%;
          align-items: center;
          .people-tree-img {
            margin-left: 2px;
          }
          .node-label {
            color: #ffffff;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            margin: 0 4px;
          }
        }
      }
    }
  }
}
</style>
