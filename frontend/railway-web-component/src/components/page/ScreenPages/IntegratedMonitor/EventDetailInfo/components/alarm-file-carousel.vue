<!-- 告警详情中环节信息的图片视频 全屏展示组件 -->
<template>
  <div class="alarm-file-carousel-box" @mousedown="stopDrag">
    <!-- 上方小窗口轮播 -->
    <el-carousel
      ref="fileCarousel"
      trigger="click"
      :height="pxToRem(190)"
      :interval="3000"
      :autoplay="true"
    >
      <!-- 视频轮播项 -->
      <el-carousel-item v-for="(videoItem, videoIndex) in videoList" :key="`wind1-${videoIndex}`">
        <!-- 视频元素 -->
        <video
          v-if="!onloadVideoErr"
          muted
          class="carousel-video-min"
          @mouseenter="mouseoverFile(videoItem, 'video')"
          @mouseout="touchClose"
        >
          <source :src="videoItem" />
        </video>
        <!-- 视频加载错误时显示的图片 -->
        <div v-else class="carousel-img-err-box">
          <img
            alt="播放异常"
            src="@/assets/images/alarmEvent/alarm/playErr.png"
            fit="contain"
            class="carousel-img-err"
          />
        </div>
        <!-- 视频播放按钮 -->
        <div v-if="!onloadImgErr" class="carousel-video-play">
          <i class="iconfont icon-bofang5 video-play-class"></i>
        </div>
        <!-- 全屏查看按钮 -->
        <div class="full-btn-class" @click="openVideoRepet(videoIndex)">
          <img
            alt="全屏"
            class="alarm-icon"
            :src="require(`@/assets/images/alarmEvent/alarm/fangda_icon.svg`)"
          />
        </div>
      </el-carousel-item>
      <!-- 图片轮播项 -->
      <el-carousel-item
        v-for="(imgItem, imgIndex) in imgList"
        :key="`wind2-${imgIndex + videoList.length}`"
      >
        <!-- 图片元素 -->
        <img
          alt="图片"
          v-if="!onloadImgErr"
          :src="imgItem"
          fit="contain"
          class="carousel-img-min"
          @mouseenter="mouseoverFile(imgItem, 'img')"
          @mouseout="touchClose"
        />
        <!-- 图片加载错误时显示的图片 -->
        <div v-else class="carousel-img-err-box">
          <img
            alt="加载异常"
            src="@/assets/images/alarmEvent/alarm/playErr.png"
            fit="contain"
            class="carousel-img-err"
          />
        </div>
        <!-- 全屏查看按钮 -->
        <div class="full-btn-class isImg" @click="openVideoRepet(videoList.length + imgIndex)">
          <img
            alt="全屏"
            class="alarm-icon"
            :src="require(`@/assets/images/alarmEvent/alarm/fangda_icon.svg`)"
          />
        </div>
      </el-carousel-item>
    </el-carousel>
    <!-- 下方文件列表 -->
    <div class="bottom-file-btn-box">
      <!-- 左侧按钮 -->
      <div class="bottom-left-btn" @click="leftOrRight('left')">
        <i class="el-icon-caret-left"></i>
      </div>
      <!-- 文件列表内容 -->
      <div class="bottom-content-box">
        <!-- 视频列表项 -->
        <template v-for="(videoItem2, videoIndex2) in videoList">
          <div
            :id="id"
            class="bottom-content-item"
            :class="{ err: onloadVideoErr }"
            :key="`wind3-${videoIndex2}`"
            @click="changeCarIndex(videoIndex2)"
          >
            <video
              v-if="!onloadVideoErr"
              muted
              class="carousel-video-botttom"
              @mouseenter="mouseoverFile(videoItem2, 'video')"
              @mouseout="touchClose"
            >
              <source :src="videoItem2" @error="handleVideoError" />
            </video>
            <!-- 视频加载错误时显示的图片 -->
            <img
              alt="播放异常"
              v-else
              src="@/assets/images/alarmEvent/alarm/playErr.png"
              fit="contain"
              class="carousel-img-bottom-err"
            />
          </div>
        </template>
        <!-- 图片列表项 -->
        <template v-for="(imgItem2, imgIndex2) in imgList">
          <div
            :id="id"
            class="bottom-content-item"
            :class="{ err: onloadImgErr }"
            :key="`wind4-${imgIndex2}`"
            @click="
            changeCarIndex(
              videoList.length > 0 ? videoList.length + imgIndex2 : imgIndex2
            )
          "
          >
            <img
              v-if="!onloadImgErr"
              alt="图片"
              :src="imgItem2"
              fit="contain"
              class="carousel-img-bottom"
              @mouseenter="mouseoverFile(imgItem2, 'img')"
              @mouseout="touchClose"
              @error="handleImgError"
            />
            <!-- 图片加载错误时显示的图片 -->
            <img
              v-else
              alt="加载异常"
              src="@/assets/images/alarmEvent/alarm/playErr.png"
              fit="contain"
              class="carousel-img-bottom-err"
            />
          </div>
        </template>
      </div>
      <!-- 右侧按钮 -->
      <div class="bottom-right-btn" @click="leftOrRight('right')">
        <i class="el-icon-caret-right"></i>
      </div>
    </div>
    <!-- 放大查看 非全屏 -->
    <div class="max-file-box" v-if="showBigDialog !== ''">
      <!-- 视频播放器 -->
      <video-player
        v-if="showBigDialog === 'video'"
        class="video video-player vjs-custom-skin infoVideo-player lunboVideo"
        :playsinline="true"
        :options="playerOptions"
        @statechanged="playStatechanged($event)"
      />
      <!-- 放大图片 -->
      <img
        v-if="showBigDialog === 'img'"
        alt="图片"
        :src="bigImgUrl"
        fit="contain"
        class="carousel-img-big"
      />
    </div>
    <!-- 全屏查看组件 -->
    <FileFullScreen v-if="fullList.showDialog" :fileFullList="fullList" @closeFull="closeFull" />
  </div>
</template>
<script>
import FileFullScreen from '@/components/common/file-full-screen.vue'
import { uuid } from '@/components/common/utils'
import $ from 'jquery'
export default {
  name: 'alarm-file-carousel',
  components: {
    FileFullScreen,
  },
  props: {
    id: {
      type: String,
      default: () => uuid(),
    },
    alarmInfo: {
      type: Object,
      default: () => ({}),
    },
  },
  watch: {
    alarmInfo: {
      handler() {
        this.disposeFile() // 处理告警详情图片视频用于轮播展示
      },
      deep: true, // json 深度匹配
      immediate: true, // 初始化时执行
    },
  },
  data() {
    return {
      staticImg: require('@/assets/images/alarmEvent/alarm/alarmNull.png'), // 告警默认图片
      onloadVideoErr: false, // 视频是否加载失败
      onloadImgErr: false, // 图片是否加载失败
      videoList: [], // 轮播视频数据
      imgList: [], // 轮播图片数据
      fullList: {
        // 全屏文件数据
        showDialog: false, // 视频dialog显示标识
        videoImgUrl: [], // 视频url
        index: 0,
      },
      showBigDialog: '', // 是否展示大窗口
      bigImgUrl: '', // 放大非全屏的图片地址
      playerOptions: {
        playbackRates: [0.5, 1.0, 1.5, 2.0], // 播放速度
        autoplay: true, // 如果true,浏览器准备好时开始回放。
        loop: true, // 导致视频一结束就重新开始。
        muted: true, // 默认情况下将会消除任何音频。
        preload: 'auto', // 建议浏览器在<video>加载元素后是否应该开始下载视频数据。auto浏览器选择最佳行为,立即开始加载视频（如果浏览器支持）
        language: 'zh-CN',
        fluid: true, // 当true时，Video.js player将拥有流体大小。换句话说，它将按比例缩放以适应其容器。
        hls: false,
        sources: [
          {
            // type: 'application/x-mpegURL',
            type: 'video/mp4',
            src: '',
          },
        ],
        aspectRatio: '16:9',
        poster: '', // 封面地址
        choosed: false, // 被选中的
        notSupportedMessage: '视频录像服务维护中', // 允许覆盖Video.js无法播放媒体源时显示的默认信息。
        controlBar: {
          timeDivider: false,
          durationDisplay: false,
          remainingTimeDisplay: false,
          fullscreenToggle: false, // 全屏按钮
        },
      },
    }
  },
  beforeDestroy() {
    $('body').unbind('click', this.touchClose)
  },
  created() {
    // 初始化监听页面点击click事件 用于点击空白区域关闭放大图片
    $('body').bind('click', event => {
      if (!$(event.target).closest('.max-file-box').length) {
        // 在这里编写操作代码
        this.touchClose()
      }
    })
  },
  methods: {
    /**
     * 禁止拖动
     */
    stopDrag(e) {
      e.stopPropagation()
    },
    /**
     * 禁用鼠标右键
     */
    disableRightClick(e) {
      e.preventDefault()
    },
    /**
     * 处理告警详情图片视频用于轮播展示
     */
    disposeFile() {
      let fullList = []
      let videoList = []
      let imgList = []
      if (this.alarmInfo.video && this.alarmInfo.video.videoList.length > 0) {
        // 视频数据
        for (let item of this.alarmInfo.video.videoList) {
          videoList.push(item.src)
          fullList.push(item.src)
        }
      }
      if (this.alarmInfo.imgs && this.alarmInfo.imgs.length > 0) {
        // 图片数据
        for (let item of this.alarmInfo.imgs) {
          imgList.push(item.fileUrl)
          fullList.push(item.fileUrl)
        }
      }
      this.videoList = videoList
      this.imgList = imgList
      this.fullList.videoImgUrl = fullList
      if (this.fullList.videoImgUrl.length === 0) {
        // 如果没有图片和视频 显示默认图片
        this.imgList.push(this.staticImg)
        this.fullList.videoImgUrl.push(this.staticImg)
      }
    },
    /**
     * 底部左右按钮
     */
    leftOrRight(type) {
      if (this.fullList.videoImgUrl.length <= 5) {
        return false
      }
      let boxLeft = Number($(`#${this.id}`).css('margin-left').slice(0, -2)) // 底部按钮盒子高度 删掉px 转数字
      let maxLeft = -((this.fullList.videoImgUrl.length - 5) * 64 - 3) // 超出5个的长度 3是初始有3px marging-left
      if (type === 'left') {
        // 向左
        if (boxLeft < 3) {
          // 初始有3px marging-left
          boxLeft = boxLeft + 64 // 单个item图片宽度58 边距6
          $(`#${this.id}`).css('margin-left', boxLeft + 'px')
        }
      } else {
        // 向右
        if (boxLeft > maxLeft) {
          boxLeft = boxLeft - 64 // 单个item图片宽度58 边距6
          $(`#${this.id}`).css('margin-left', boxLeft + 'px')
        }
      }
    },
    /**
     * 选中底部图片列表item
     */
    changeCarIndex(index) {
      this.$refs.fileCarousel.setActiveItem(index)
      setTimeout(() => {
        // 选中图片停止轮播 （因为会同时触发touchClose方法 所以延时0.2秒）
        this.isAutoplay = false
      }, 200)
    },
    /**
     * 全屏查看数据处理
     */
    openVideoRepet(index) {
      this.fullList.showDialog = true
      this.fullList.index = index
    },
    /**
     * 关闭全屏查看
     */
    closeFull() {
      this.fullList.showDialog = false
      this.fullList.index = 0
    },
    /**
     * 视频图片加载失败
     */
    handleVideoError(e) {
      this.onloadVideoErr = true
    },
    handleImgError(e) {
      this.onloadImgErr = true
    },
    /**
     * 视频放大 非全屏
     */
    mouseoverFile(data, type) {
      if (type === 'video') {
        this.bigImgUrl = ''
        this.playerOptions.sources[0].src = data
      } else {
        this.bigImgUrl = data
        this.playerOptions.sources[0].src = ''
      }
      this.showBigDialog = type
    },
    /**
     * 视频播放失败重新加载
     */
    playStatechanged(status) {
      if (status.error) {
        console.log('player current update state', status)
        $($('.vjs-modal-dialog .vjs-modal-dialog-content')[0]).html(
          '告警视频正在努力生成中，请稍后...'
        )
      }
    },
    /**
     * 点击空白处关闭视频图片放大
     */
    touchClose() {
      this.showBigDialog = ''
    },
  },
}
</script>
<style lang="less" scoped>
.alarm-file-carousel-box {
  width: 100%;
  height: 100%;
  /*隐藏跑马灯控制条*/
  /deep/ .el-carousel {
    .el-carousel__indicators {
      display: none;
    }
  }
  .carousel-video-min {
    height: 100%;
    width: 100%;
    object-fit: fill;
    border-radius: 5px;
  }
  .carousel-img-err-box {
    height: 100%;
    width: 100%;
    display: flex;
    align-content: center;
    justify-content: center;
    flex-wrap: wrap;
  }
  .carousel-img-err {
    width: 20px;
    height: 20px;
    transform: translateX(-10000px);
    filter: drop-shadow(#ffffff 10000px 0);
  }
  .carousel-img-min {
    width: 100%;
    height: 190px;
    border-radius: 5px;
  }
  .carousel-video-play {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    color: #ffffff;
    position: relative;
    top: calc(-50% - 20px);
    left: calc(50% - 20px);
    display: flex;
    flex-wrap: wrap;
    align-content: center;
    justify-content: center;
    .video-play-class {
      transform: scale(2);
      transform-origin: center;
    }
  }
  .full-btn-class {
    width: 40px;
    height: 40px;
    color: #ffffff;
    position: absolute;
    bottom: 0;
    right: 0;
    // background:url("~@/assets/images/alarmEvent/alarm/ly-map-xz.png") center no-repeat !important;
    background: #3d4c57;
    border-radius: 8px;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }

  .bottom-file-btn-box {
    width: 100%;
    height: 34px;
    margin-top: 12px;
    display: flex;
    .bottom-left-btn {
      width: 12px;
      font-size: 12px;
      height: 100%;
      background: #0d1a26;
      border-radius: 4px 0 0 4px;
      color: #ffffff;
      display: flex;
      flex-wrap: wrap;
      align-content: center;
      justify-content: center;
      cursor: pointer;
      &:hover {
        background: rgba(130, 181, 255, 0.1);
      }
    }
    .bottom-content-box {
      width: 322px;
      height: 100%;
      display: flex;
      overflow: hidden;
      position: relative;
      .bottom-content-item {
        width: 58px;
        min-width: 58px;
        height: 100%;
        border-radius: 4px;
        margin: 0 3px;
        display: flex;
        .carousel-video-botttom {
          height: 100%;
          width: 58px;
          object-fit: fill;
          border-radius: 5px;
        }
        .carousel-img-bottom {
          width: 58px;
          height: 100%;
          border-radius: 4px;
        }
        .carousel-img-bottom-err {
          width: 20px;
          height: 20px;
          transform: translateX(-10000px);
          filter: drop-shadow(#ffffff 10000px 0);
        }
      }
      .err {
        background: rgba(130, 181, 255, 0.1);
        display: flex;
        flex-wrap: wrap;
        align-content: center;
        justify-content: center;
      }
    }
    .bottom-right-btn {
      width: 12px;
      font-size: 12px;
      height: 100%;
      background: #0d1a26;
      border-radius: 0 4px 4px 0;
      color: #ffffff;
      display: flex;
      flex-wrap: wrap;
      align-content: center;
      justify-content: center;
      cursor: pointer;
      &:hover {
        background: rgba(130, 181, 255, 0.1);
      }
    }
  }
  //图片视频放大样式
  .max-file-box {
    width: 926px;
    height: 522px;
    border-radius: 8px;
    background: linear-gradient(180deg, rgba(0, 19, 30, 0.7) 0%, #00131e 100%);
    border-image: linear-gradient(
        360deg,
        rgba(7, 91, 74, 0.75),
        rgba(7, 91, 74, 0.3)
      )
      1 1;
    position: fixed;
    top: 58px;
    left: 375px;
    overflow: hidden;
    z-index: 99;
    .carousel-img-big {
      width: 100%;
      height: 100%;
    }
  }
  //轮播图左右切换按钮
  /deep/ .el-carousel .el-carousel__container .el-carousel__arrow {
    background: rgb(0 0 0 / 51%);
  }
  /deep/ .el-carousel .el-carousel__container .el-carousel__arrow:hover {
    background: rgb(0 0 0 / 51%);
  }
}
</style>
