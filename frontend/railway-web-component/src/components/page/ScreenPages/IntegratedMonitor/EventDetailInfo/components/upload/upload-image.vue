<template>
  <div>
    <!-- 图片和视频展示区域 -->
    <div class="image-video">
      <!-- 循环展示文件内容 -->
      <div class="file-content" v-for="(item, index) in urlList" :key="index" @click="clickOne(index)" v-loading="item.loading">
        <!-- 如果文件类型是图片，使用el-image组件展示 -->
        <el-image
          v-if="item.type === '1'"
          class="file-image"
          :src="item.fileUrl"
          fit="cover"
        />
        <!-- 如果文件类型是视频，使用video-player组件展示 -->
        <video-player
          v-if="item.type === '2'"
          class="file-video vjs-custom-skin"
          :playsinline="true"
          :options="item.videoOptions"
          @canplay="saveVideoCoverImg($event, item)"
        />
        <!-- 编辑模式下显示操作按钮 -->
        <div v-if="isEdit && !item.loading" class="btns" @click.stop>
          <i class="el-icon-view btn" @click="clickOne(index)"></i>
          <i class="el-icon-delete btn" @click="deleteOne(index)"></i>
        </div>
      </div>
      <!-- 编辑模式下显示上传按钮 -->
      <div class="file-content" v-if="isEdit && urlList.length < maxLength">
        <el-upload
          ref="upload"
          class="file-upload"
          list-type="picture-card"
          action=""
          :accept="uploadAccept"
          :http-request="doUpload"
          :before-upload="beforeUpload"
        >
          <i class="el-icon-plus"></i>
        </el-upload>
      </div>
    </div>
    <!-- 图片预览组件 -->
    <ImageViewer v-if="showImageViwer" :urlList="urlList" :initial-index="imageIndex" :onClose="closeImgViwer" />
  </div>
</template>
<script>
import api from '@/api';
import $ from 'jquery';
import { UUID } from 'uuidjs';
import ImageViewer from './image-viewer.vue';
export default {
  name: 'image-video',
  components: {
    ImageViewer
  },
  props: {
    imgs: { // 图片列表
      type: Array,
      default: () => []
    },
    videos: { // 视频列表
      type: Array,
      default: () => []
    },
    isEdit: { // 是否编辑模式
      type: Boolean,
      default: false
    },
    maxImgLength: { // 最大上传图片数量
      type: Number,
      default: 99
    },
    maxVideoLength: { // 最大上传视频数量
      type: Number,
      default: 99
    },
    maxLength: { // 最大上传文件数量
      type: Number,
      default: 99
    },
    maxImgSize: { // 最大上传图片大小，单位MB
      type: Number,
      default: 100
    },
    maxVideoSize: { // 最大上传视频大小，单位MB
      type: Number,
      default: 100
    },
    accept: { // 允许上传的文件格式
      type: String,
      default: 'jpg,jpeg,png,gif,mp4'
    },
    unAcceptErrMsg: { // 不允许上传的提示信息
      type: String,
      default: '只支持上传.jpg,.jpeg,.png,.gif格式的文件'
    }
  },
  data() {
    return {
      uploadAccept: '', // 上传接受的文件格式
      urlList: [], // 文件列表
      showImageViwer: false, // 是否显示图片预览
      imageIndex: 0, // 当前预览的图片索引
      videoOptions: { // 视频播放器属性
        muted: true,
        language: "zh-CN",
        preload: "auto",
        playbackRates: [0.5, 1.0, 1.5, 2.0],
        autoplay: false,
        loop: false,
        fluid: true,
        hls: true,
        html5: {
          hls: {
            withCredentials: false
          }
        },
        sources: [
          {
            type: "video/mp4",
            src: '' // 视频URL地址
          }
        ],
        aspectRatio: "16:9",
        choosed: false,
        notSupportedMessage: " ",
        controlBar: {
          volumeControl: false,
          timeDivider: false,
          durationDisplay: false,
          remainingTimeDisplay: false,
          fullscreenToggle: true
        }
      },
    }
  },
  watch: {
    imgs: {
      handler: function() {
        this.initList(); // 初始化文件列表
      },
      deep: true,
      immediate: true
    },
    videos: {
      handler: function() {
        this.initList(); // 初始化文件列表
      },
      deep: true,
      immediate: true
    },
    accept: {
      handler: function(accept) {
        accept = accept.replace(/ /g, '').split(',');
        let uploadAccept = [];
        accept.forEach(item => {
          uploadAccept.push('.' + item.toLowerCase());
        });
        this.uploadAccept = uploadAccept.join(','); // 设置上传接受的文件格式
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    /**
     * 文件上传提交
     * @param {Object} data - 上传文件的数据
     */
    doUpload(data) {
      let _this = this
      const file = data.file;
      const item = {
        uuid: this.getUUID(),
        loading: true
      }
      if(file.type.includes('image')) {
        item.type = '1'; // 设置文件类型为图片
      }
      if(file.type.includes('video')) {
        item.type = '2'; // 设置文件类型为视频
        item.videoOptions = JSON.parse(JSON.stringify(this.videoOptions))
      }
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        const arr = reader.result.split(',');
        const mime = arr[0].match(/:(.*?);/)[1];
        const bytes = atob(arr[1]);
        let n = bytes.length;
        const ia = new Uint8Array(n);
        while (n--) {
          ia[n] = bytes.charCodeAt(n);
        }
        const blob = new Blob([ia], { type: mime });
        const fileUrl = window.URL.createObjectURL(blob);
        item.fileUrl = fileUrl;
        if(item.type === '2') {
          item.videoOptions.sources[0].src = fileUrl; // 设置视频源URL
        }
        this.urlList.push(item); // 添加文件到列表
        const formData = new FormData();
        formData.append('file', file);
        api.post(
            `${this.$env.VUE_APP_REQ_PREFIX_PLAT}/system/upload`,
            { base64: reader.result, fileName: file.name }
        ).then(res => {
            if (res.code == 200) {
                item.loading = false;
                item.fileId = res.data.fileId;
                item.fileName = res.data.fileName;
                item.fileUrl = res.data.fileUrl;
                this.change(1); // 触发文件改变事件
            } else {
                item.loading = false;
                this.$message({
                    type: 'error',
                    message: '上传失败'
                })
                this.uploadError(item); // 处理上传失败
            }
        })
      }
    },
    /**
     * 获取文件的Base64编码
     * @param {File} file - 文件对象
     * @returns {Promise} - 返回文件的Base64编码
     */
    getBase64(file) {
        return new Promise(function (resolve, reject) {
            let reader = new FileReader();
            let imgResult = "";
            reader.readAsDataURL(file);
            reader.onload = function () {
                imgResult = reader.result;
            };
            reader.onerror = function (error) {
                reject(error);
            };
            reader.onloadend = function () {
                resolve(imgResult);
            };
        });
    },
    /**
     * 文件上传失败处理
     * @param {Object} item - 文件项
     */
    uploadError(item) {
      for(let i = 0; i < this.urlList.length; i ++) {
        if(this.urlList[i].uuid === item.uuid) {
          this.urlList.splice(i, 1); // 从列表中移除失败的文件
          return false;
        }
      }
    },
    /**
     * 保存视频覆盖图
     * @param {Event} e - 事件对象
     * @param {Object} item - 文件项
     */
    saveVideoCoverImg(e, item) {
      let _this = this
      if(!item.uuid || item.coverUrl !== undefined) {
        return false;
      }
      item.coverUrl = '';
      const video = $(e.el_).find('video')[0];
      const canvas = document.createElement('canvas');
      canvas.width = video.clientWidth || 80;
      canvas.height = video.clientHeight || 80;
      const ctx = canvas.getContext('2d');
      ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
      const img = canvas.toDataURL('image/png');
      const name = '封面.png';
      api.post(
                `${this.$env.VUE_APP_REQ_PREFIX_PLAT}/system/upload`,
                { base64: img, fileName: name }
            ).then(res => {
                if (res.code == 200) {
                    item.coverUrl = res.data.fileUrl; // 设置视频封面URL
                    this.change(0); // 触发文件改变事件
                }
            })
    },
    /**
     * 文件上传校验
     * @param {File} file - 文件对象
     * @returns {Boolean} - 返回是否允许上传
     */
    beforeUpload(file) {
      const [type, accept] = file.type.split('/');
      if(type === 'image') {
        if(!this.uploadAccept.includes(accept)) {
          this.$message({
            type: 'warning',
            message: '不支持上传' + accept + '格式的图片'
          })
          return false;
        }
        if(this.countImgLength() + 1 > this.maxImgLength) {
          this.$message({
            type: 'warning',
            message: '最多允许上传' + this.maxImgLength + '张图片'
          })
          return false;
        }
        if(file.size > 1024 * 1024 * this.maxImgSize) {
          this.$message({
            type: 'warning',
            message: '图片大小超出' + this.maxImgSize + 'MB' + '，请调整图片大小后上传'
          })
          return false;
        }
      }else if (type === 'video') {
        if(!this.uploadAccept.includes(accept)) {
          this.$message({
            type: 'warning',
            message: '不支持上传' + accept + '格式的视频'
          })
          return false;
        }
        if(this.countVideoLength() + 1 > this.maxVideoLength) {
          this.$message({
            type: 'warning',
            message: '最多允许上传' + this.maxVideoLength + '个视频'
          })
          return false;
        }
        if(file.size > 1024 * 1024 * this.maxVideoSize) {
          this.$message({
            type: 'warning',
            message: '视频大小超出' + this.maxVideoSize + 'MB' + '，请调整视频大小后上传'
          })
          return false;
        }
      }else {
        this.$message({
          type: 'warning',
          message: this.unAcceptErrMsg
        })
        return false;
      }
    },
    /**
     * 计算图片个数
     * @returns {Number} - 返回图片数量
     */
    countImgLength() {
      let count = 0;
      this.urlList.forEach(item => {
        if(item.type === '1') {
          count ++;
        }
      });
      return count;
    },
    /**
     * 计算视频个数
     * @returns {Number} - 返回视频数量
     */
    countVideoLength() {
      let count = 0;
      this.urlList.forEach(item => {
        if(item.type === '2') {
          count ++;
        }
      });
      return count;
    },
    /**
     * 初始化文件列表
     */
    initList() {
      const imgs = JSON.parse(JSON.stringify(this.imgs));
      const videos = JSON.parse(JSON.stringify(this.videos));
      imgs.forEach(item => item.type = '1');
      videos.forEach(item => {
        item.type = '2';
        item.videoOptions = JSON.parse(JSON.stringify(this.videoOptions));
        item.videoOptions.sources[0].src = item.fileUrl;
      });
      this.urlList = [...imgs, ...videos];
      this.change(0); // 触发文件改变事件
    },
    /**
     * 点击预览
     * @param {Number} index - 当前点击的文件索引
     */
    clickOne(index) {
      this.imageIndex = index;
      this.showImageViwer = true; // 显示图片预览
    },
    /**
     * 删除文件
     * @param {Number} index - 要删除的文件索引
     */
    deleteOne(index) {
      this.urlList.splice(index, 1); // 从列表中删除文件
      this.change(-1); // 触发文件改变事件
    },
    /**
     * 文件改变事件
     * @param {Number} type - 改变类型
     */
    change(type) {
      const imgs = [];
      const videos = [];
      this.urlList.forEach(item => {
        if(item.loading) {
          return false;
        }
        if(item.type === '1') {
          imgs.push(item);
        }
        if(item.type === '2') {
          videos.push(item);
        }
      });
      this.$emit('change', imgs, videos, type); // 触发组件事件
    },
    /**
     * 关闭图片预览
     */
    closeImgViwer() {
      this.showImageViwer = false; // 关闭图片预览
    },
    /**
     * 生成UUID
     * @returns {String} - 返回生成的UUID
     */
    getUUID() {
      return UUID.generate(); // 生成唯一标识符
    },
  }
}
</script>
<style lang="less" scoped>
  .image-video{
    display: flex;
    flex-wrap: wrap;
    .file-content{
      width: 80px;
      height: 80px;
      border-radius: 4px;
      margin: 0 8px 8px 0;
      cursor: pointer;
      overflow: hidden;
      position: relative;
      &:hover{
        .btns{
          display: flex;
        }
      }
      .btns{
        display: none;
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 2;
        align-items: center;
        justify-content: center;
        color: #fff;
        font-size: 18px;
        background: rgba(0, 0, 0, 0.4);
        cursor: default;
        .btn{
          margin: 0 5px;
          cursor: pointer;
          /deep/ &.el-icon-view:before{
            font-family: iconfont;
            content: "\E605";
          }
        }
      }
      .file-upload{
        width: 100%;
        height: 100%;
        /deep/ .el-upload-list{
          display: none;
        }
        /deep/ .el-upload--picture-card{
          width: 100%;
          height: 100%;
          border-radius: 4px;
          line-height: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          border: 1px dashed #C1CCDA;
          background: #FBFDFF;
        }
      }
      .file-image{
        width: 100%;
        height: 100%;
      }
      .file-video{
        width: 100%;
        height: 100%;
        pointer-events: none;
        /deep/ video{
          object-fit: cover;
        }
        /deep/ .vjs-big-play-button{
          top: 50% !important;
          left: 50% !important;
          height: 30px !important;
          line-height: 30px !important;
          width: 30px;
          border-radius: 50%;
          background-color: rgba(8,0,0,0.4);
          border: none;
          margin: 0 !important;
          transform: translate(-50%, -50%);
        }
        /deep/ .video-js {
          height: 100% !important;
          border-radius: 4px;
        }
        /deep/ .vjs-poster {
          border-radius: 4px;
          background-size: cover;
        }
        /deep/ .vjs-modal-dialog-content{
          display: none;
        }
        /deep/ .vjs-icon-placeholder:before {
          font-size: 24px!important;
          color: #fffdef!important;
        }
      }
    }
  }
</style>
