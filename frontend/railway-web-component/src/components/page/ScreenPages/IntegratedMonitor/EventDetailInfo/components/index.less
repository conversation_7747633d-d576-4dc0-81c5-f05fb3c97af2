@import "../../common.less";

.alarm-phrases {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;

  .comment-ctl {
    position: absolute;
    right: 25px;
    bottom: 3px;
    padding: 7px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: rgb(37 63 93);
    width: 220px;

    .comments-tag {
      font-family: PingFangSC-Regular;
      font-size: 14px;
      color: rgb(232 243 255 / 60%);
      letter-spacing: 0;
      font-weight: 400;
      cursor: pointer;
      padding-left: 12px;
      box-sizing: border-box;
      width: 85%;
    }

    .comments-manage {
      color: @theme-color;
      background: transparent;
      cursor: pointer;
    }
  }

  .commentSelect {
    .el-input--suffix .el-input__inner {
      padding-right: 66px;
      height: auto !important;
    }
  }

  .comments-input-group {
    position: absolute;
    width: 285px;
    bottom: 34px;
    background: @background-color;
    border: 1px solid #000;
    box-shadow: -3px -3px 3px rgba(0, 0, 0, 0.5);
    border-top: none;
    display: flex;
    flex-direction: column;
    border-radius: 4px;
    z-index: 1;

    .group-list {
      flex: 1;
      overflow: hidden;
      overflow-y: auto;

      .comment-input-single {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        height: 45px;
        border-bottom: 1px solid @border-color;

        .input-outter {
          display: flex;
          align-items: center;
          width: calc(100% - 86px);
          margin-right: 12px;
          font-size: 14px;

          span {
            width: 28px;
            margin-left: 10px;
          }
        }

        .input-btn-group {
          display: flex;
          align-items: center;

          span {
            display: flex;
          }

          svg {
            width: 16px;
            height: 16px;
            cursor: pointer;
          }
        }
      }
    }

    .comments-input-save {
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: end;
      padding-right: 12px;
      border-top: 1px solid rgba(0, 0, 0, 0.06);
    }
  }
}

.audit-comments-popper {
  width: 249px;

  li {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .comment-opt-group {
      display: flex;
      align-items: center;
      width: 90%;

      > span {
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    span.selected {
      padding-top: 8px;
    }
  }
}
