<!--
 * @Description  : 核实
 * <AUTHOR> wnj
 * @Date         : 2023-12-11 10:58:52
 * @LastEditors  : wnj
 * @LastEditTime : 2023-12-26 14:50:20
 * @FilePath     :  / src / components / page / alarmEvent / components / alarmRight / alarmDisposeDialog.vue
-->
<!--  告警各环节处理弹窗  -->
<template>
  <!-- 使用 CtlPanel 组件包裹整个弹窗 -->
  <CtlPanel class="evt-handle-wrap alarm-dispatch-outter" :close="toClose">
    <!-- 弹窗标题插槽 -->
    <template v-slot:title>{{ alarmDisposeData.title }}</template>
    <!-- 弹窗内容插槽 -->
    <template v-slot:content>
      <!-- 处理表单，加载时显示 loading -->
      <div class="dispatch-con evt-handle-form" v-loading="dataLoading">
        <!-- 选择下一环节 -->
        <div class="dispose-item-box">
          <div class="dispose-item-left" :class="{'chuZhi-left' : isChu<PERSON>hi}">
            <div class="dispose-xinghao">*</div>
            <div class="dispose-item-label">下一环节</div>
          </div>
          <div class="dispose-item-right" :class="{'chuZhi-right' : isChuZhi}">
            <!-- 下拉选择框，选择下一环节 -->
            <el-select
              class="c-select"
              popper-class="c-select-dropdown"
              placeholder="请选择"
              v-model="nextLink"
              :disabled="nextLinkList && nextLinkList.length === 1"
              @change="changeNextLink"
            >
              <!-- 遍历环节列表，生成选项 -->
              <el-option
                v-for="item in nextLinkList"
                :key="item.curLink"
                :label="item.linkName"
                :value="item.curLink"
              ></el-option>
            </el-select>
          </div>
        </div>
        <!-- 处理人下拉树 -->
        <div class="dispose-item-box" v-if="!EndLinkId.includes(realLinkType)">
          <div class="dispose-item-left" :class="{'chuZhi-left' : isChuZhi}">
            <div class="dispose-xinghao">*</div>
            <div class="dispose-item-label">处理人</div>
          </div>
          <div class="dispose-item-right" :class="{'chuZhi-right' : isChuZhi}">
            <!-- 选择处理人组件 -->
            <PeopleSelectTree
              ref="rightPeopleSelect"
              :alarmInfo="alarmDisposeData?.alarmInfo"
              :getFlowUserParams="getFlowUserParams"
              :showAllTreeBtn="showAllTreeBtn"
              :dataFilterPermiss="dataFilterPermiss"
              @dispatchCheckChange="dispatchCheckChange"
              @dataLoading="changeLoading"
            />
          </div>
        </div>
        <!-- 环节说明 -->
        <div class="dispose-item-box">
          <div class="dispose-item-left" :class="{'chuZhi-left' : isChuZhi}">
            <div class="dispose-item-label">核实说明</div>
          </div>
          <div class="dispose-item-right" :class="{'chuZhi-right' : isChuZhi}">
            <!-- 常用语组件 -->
            <AlarmPhrases
              v-model="remarksText"
              placeholder="请输入内容"
              :orderStatus="alarmDisposeData.alarmInfo.order.orderStatus"
              :maxLength="200"
              @update="isPhrasesUnfold"
              :isUnfold.sync="isUnfold"
            />
          </div>
        </div>
      </div>
      <!-- 提交和取消按钮 -->
      <div class="dispatch-buttons">
        <div class="dispatch-button dispatch-button-blue" @click="toSubmit">
          <span>确定</span>
        </div>
        <div class="dispatch-button" @click="toClose">
          <span>取消</span>
        </div>
      </div>
    </template>
  </CtlPanel>
</template>
<script>
import api from '@/api'
import CtlPanel from '@/components/page/ScreenPages/IntegratedMonitor/CtlPanel/index.vue'
import { EndLinkId } from '@/components/page/ScreenPages/IntegratedMonitor/EventDetailInfo/AlarmInfoContent/LinkConst'
import AlarmPhrases from '../components/alarm-phrases.vue'
import AlarmSuppress from '../components/alarm-suppress.vue'
import PeopleSelectTree from '../components/peopleSelectTree.vue'
export default {
  components: {
    CtlPanel,
    PeopleSelectTree,
    AlarmPhrases,
    AlarmSuppress,
  },
  props: {
    propData: {
      type: Object,
      default: {},
    },
    close: {
      type: Function,
      default: () => {
        console.log('close')
      },
    },
    okCalBack: {
      type: Function,
      default: () => {
        console.log('okCalBack')
      },
    },
  },
  data() {
    return {
      EndLinkId,
      alarmDisposeData: this.propData,
      dataLoading: true,
      isChuZhi: false,
      nextLinkList: [], //所有的环节
      nextLink: '', //选中的下一环节
      showAllTree: false, //是否展示全部处理人树
      showAllTreeBtn: false, //是否展示全部处理人按钮
      linkId: '', //下一环节id
      linkName: '', //下一环节名称
      realLinkType: '', //真实环节id
      msgNotifyType: '', //通知方式
      nextEnableAcceptanceMode: '', //下一环节是否开启受理
      timeLimit: '', //下一环节超期时间
      timeLimitAcceptance: '', //下一环节受理时间
      dataFilterPermiss: '', //是否开启过滤权限 0 否 1是
      alarmFlowId: '', //告警流程id
      userType: '', //用户类型
      getFlowUserParams: {
        //查询处置人员入参
        item: {}, //下一环节数据
        recordsInfo: [], // 回退时需要的环节信息
      },
      dispatchNameValue: [],
      processDTOList: [], //处理人入参
      remarksText: '', //流程处理时的处理说明
      isUnfold: false, //展开常用语 弹窗变大
      isSuppress: false, //是否开启压制
    }
  },
  created() {
    console.log(
      '%c █░░░░░░░░░░░░█ ,注释 created时的alarmDisposeData数据',
      'color: #FAC800',
      this.alarmDisposeData
    )
    this.isChuZhi = true
    this.getNextLink(this.alarmDisposeData.alarmInfo.order)
  },
  methods: {
    /**
     * 查询下一环节
     * @param {Object} item - 当前环节信息
     */
    getNextLink(item) {
      const params = {
        flowId: item.flowId,
        curLink: item.linkId,
        templateId: item.flowTemplate, //模板id
        warningSource: item.warningSource, //告警来源
      }
      api
        .post(
          `${this.$env.VUE_APP_REQ_PREFIX_PLAT}/event/queryNextLink`,
          params
        )
        .then(res => {
          if (res.code == 200) {
            this.nextLinkList = res.data
            if (res.data.length < 2) {
              //只有一个环节时直接赋值
              this.nextLink = this.nextLinkList[0].curLink
              this.changeNextLink(this.nextLink)
              this.dataLoading = false
            } else {
              this.dataLoading = false
            }
          } else {
            this.dataLoading = false
            this.$message({
              type: 'error',
              message: res.msg || '下一环节查询失败！',
            })
          }
        })
    },
    /**
     * 用户选中下一环节
     * @param {String} val - 选中的环节值
     */
    changeNextLink(val) {
      let choosed
      choosed = this.nextLinkList.find(item => item.curLink === val)
      console.log(
        '%c █░░░░░░░░░░░░█ ,注释 用户选中下一环节',
        'color: #FAC800',
        choosed
      )
      this.linkId = val
      this.linkName = choosed.linkName
      this.realLinkType = choosed.realLinkType
      this.msgNotifyType = choosed.msgNotifyType
      this.nextEnableAcceptanceMode = choosed.enableAcceptanceMode
      this.timeLimitAcceptance = choosed.timeLimitAcceptance
      this.timeLimit = choosed.timeLimit
      this.dataFilterPermiss = choosed.dataFilterPermiss
      //当“是否过滤数据权限dataFilterPermiss”配置为否， “是否显示客户管理员customAdmin” 配置为是时，下拉列表底部不显示“显示全部处理人”按钮。
      this.showAllTreeBtn = !(
        choosed.dataFilterPermiss === '0' && choosed.customAdmin === '1'
      )
      this.alarmFlowId = choosed.flowId
      this.userType = choosed.userType
      let a = {}
      a.flowId = choosed.flowId
      a.linkId = val
      a.orderId = this.alarmDisposeData.alarmInfo.order.warningOrderId
      //赋值获取人员数的参数
      this.getFlowUserParams = {
        item: a,
        recordsInfo: [],
      }
    },
    /**
     * 赋值处置人员树返回的数据
     * @param {Object} param - 处理人数据
     */
    dispatchCheckChange(param) {
      this.processDTOList = param.processDTOList
      this.dispatchNameValue = param.dispatchNameValue
    },
    /**
     * 常用语判断弹窗高度
     * @param {Boolean} val - 是否展开
     */
    isPhrasesUnfold(val) {
      this.isUnfold = val
    },
    /**
     * 改变加载等待状态
     */
    changeLoading() {
      this.dataLoading = false
    },
    /**
     * 确认提交
     */
    toSubmit() {
      if (this.dataLoading) {
        return false
      }
      this.toSubmitProcess(this.alarmDisposeData.title)
    },
    /**
     * 取消提交
     */
    toClose() {
      this.close()
    },
    /**
     * 正常工单流转核实
     * @param {String} type - 工单类型
     */
    toSubmitProcess(type) {
      if (this.nextLink === '') {
        //校验选择环节
        this.$message({
          type: 'error',
          message: `请选择下一环节`,
        })
        return false
      }
      if (
        this.dispatchNameValue.length === 0 &&
        !EndLinkId.includes(this.realLinkType)
      ) {
        //校验处置
        this.$message({
          type: 'error',
          message: '请选择处理人',
        })
        return false
      }
      let alarmInfo = this.alarmDisposeData.alarmInfo
      this.toVerify(alarmInfo)
    },
    /**
     * 核实环节入参处理
     * @param {Object} alarmInfo - 告警信息
     */
    toVerify(alarmInfo) {
      let params = {
        orderId: alarmInfo.order.warningOrderId,
        warningOrderId: alarmInfo.order.warningOrderId,
        flowId: alarmInfo.order.flowId, //流程ID
        currentLink: alarmInfo.order.linkId, //当前环节ID
        // isSign: '0',//是否会签
        msgNotifyType: this.msgNotifyType, //通知方式
        isNextLink: this.isNextLink, //是否流转1：流转下个环节  0：不流转 isNextLink除了需要完成会签的时候是0  其他时候都是1
        remark: this.remarksText, //说明
        realLinkType: this.realLinkType, // 真实环节类型(1研判2调度4处置5核实6办结)
        linkId: this.linkId, //下一环节
        linkName: this.linkName,
        orderStatus: this.linkId, //订单状态（与linkId相同）
        enableAcceptanceMode: alarmInfo.order.enableAcceptanceMode, //受理状态（取详情接口返回的事件该字段（0：不开启 1：开启（待受理）2：已受理 3：受理完成）
        timeLimitAcceptance: this.timeLimitAcceptance, //受理时限
        timeLimit: this.timeLimit,
        attrId: this.linkId, //流程环节id
        nextEnableAcceptanceMode: this.nextEnableAcceptanceMode, //下一环节是否开启受理状态（（0:不开启 1:开启））
        judgeResult: '1',
        flowLinkType: '0', //工单流转类型 0 默认 1转派 2回退
        processDTOList: this.processDTOList, //人员入参
        warningTypeId: alarmInfo.order.warningTypeId, //告警类型
      }
      params.orderApproveVOList = [] //核实入参
      params.orderApproveVOList[0] = {}
      params.orderApproveVOList[0].approveResult = this.remarksText
      params.orderApproveVOList[0].approveStatus = '2'
      if (alarmInfo.order?.changeAddCodeName?.isChange) {
        params = this.toAddressData(params, alarmInfo)
      }
      this.doSubmitProcess(params)
    },
    /**
     * 修改地址时处理地址数据
     * @param {Object} params - 参数对象
     * @param {Object} alarmInfo - 告警信息
     * @returns {Object} - 更新后的参数对象
     */
    toAddressData(params, alarmInfo) {
      params.gridId = alarmInfo.order.gridId //网格ID
      params.gridName = alarmInfo.order.gridName //网格名称
      params.checkGridFlag = 1 //是否修改网格信息和水平方位角
      if (
        alarmInfo.order.warningSource === '1' ||
        alarmInfo.order.warningSource === '4'
      ) {
        params.horiAzimuthAngle = alarmInfo.order.horiAzimuthAngle //水平方位角
      }
      params.address = alarmInfo.order.changeAddCodeName.address //详细地址名称
      params.longitude = alarmInfo.order.changeAddCodeName.longitude //经度名称
      params.latitude = alarmInfo.order.changeAddCodeName.latitude //维度名称
      params.provinceId = alarmInfo.order.changeAddCodeName.provinceId //省份id
      params.provinceName = alarmInfo.order.changeAddCodeName.provinceName //省份名称
      params.cityId = alarmInfo.order.changeAddCodeName.cityId //地市名称
      params.cityName = alarmInfo.order.changeAddCodeName.cityName //地市名称
      params.countyId = alarmInfo.order.changeAddCodeName.countyId //区县编码
      params.countyName = alarmInfo.order.changeAddCodeName.countyName //区县名称
      return params
    },
    /**
     * 提交工单流转
     * @param {Object} params - 提交参数
     */
    doSubmitProcess(params) {
      api
        .post(
          `${this.$env.VUE_APP_REQ_PREFIX_PLAT}/event/submitProcess`,
          params
        )
        .then(res => {
          if (res.code == 200) {
            this.$message({
              type: 'success',
              message: res.msg,
            })
            this.okCalBack()
          } else {
            this.$message({
              type: 'error',
              message: res.msg || '提交工单流转失败！',
            })
          }
        })
    },
  },
}
</script>
<style lang='less' src='./index.less' />
