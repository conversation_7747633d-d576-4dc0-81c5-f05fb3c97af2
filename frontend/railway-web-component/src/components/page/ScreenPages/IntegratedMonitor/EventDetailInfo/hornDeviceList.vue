<!--
 * @Description  : 大喇叭列表
 * <AUTHOR> wnj
 * @Date         : 2023-12-22 11:07:17
 * @LastEditors  : wnj
 * @LastEditTime : 2023-12-28 14:57:04
 * @FilePath     :  / src / components / page / alarmEvent / components / alarmRight / hornDeviceList.vue
-->
<template>
    <!-- 外层容器，绑定鼠标按下事件以阻止拖动 -->
    <div class="horn-device-list-outter"  @mousedown="stopDrag">
        <!-- 标题栏，包含标题文本和关闭按钮 -->
        <div class="device-title">
            <span>大喇叭列表</span>
            <!-- 关闭按钮，点击时触发关闭对话框方法 -->
            <img alt="关闭" class="alarm-close" :src="require(`@/assets/images/alarmEvent/alarm/close_icon.svg`)" @click="closeDialog"/>
        </div>
        <!-- 设备列表容器，显示加载状态 -->
        <div class="device-con" v-loading="loading">
            <!-- 遍历设备列表，生成每个设备项 -->
            <div class="device-item" :class="{ 'clicked': curDeviceCode === item.deviceCode }" v-for="(item) in devcList" :key="item.deviceCode" @click="location(item)">
              <!-- 图标 -->
              <!-- 喇叭 -->
              <template v-if="item.deviceCode">
                <!-- 根据设备状态显示不同的图标 -->
                <template v-if='item.deviceStatus === "1"'>
                  <!-- 喇叭开启状态的图标 -->
                  <svg class="tree-img" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="..." fill="#E8F3FE"></path>
                    <path
                      d="..." fill="#13BE88"></path>
                    <path
                      d="..." fill="#FFFFFF"></path>
                  </svg>
                </template>
                <template v-else>
                  <!-- 喇叭关闭状态的图标 -->
                  <svg class="tree-img" viewBox="0 0 1024 1024" version="1.1"
                    xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="..." fill="#E8F3FE" opacity=".5"></path>
                    <path
                      d="..." fill="#ED5158"></path>
                    <path
                      d="..." fill="#FFFFFF"></path>
                  </svg>
                </template>
              </template>
                <!-- 设备名称 -->
                <span class="item-text">
                    <span class="item-name" :title="item.deviceName">{{ item.deviceName }}</span>
                </span>
            </div>
        </div>
    </div>
</template>
<script>
import api from '@/api';
import bus from '@/components/common/bus.js';
export default {
    name: 'hornDeviceList',
    props: {
        provinceId: { // 省份编码
            type: String,
            default: '110000'
        },
        cityId: { // 地市编码
            type: String,
            default: ''
        },
    },
    computed: {
        dataParam() {
            // 使用计算属性承载参数。省市区编码任意一个更新才处理
            const { provinceId, cityId } = this
            return {
                provinceId,
                cityId,
            }
        }
    },
    watch: {
        dataParam: {
            handler(val) {
                // 如果省市编码有变化，则重新获取列表
                if (val?.provinceId || val?.cityId) {
                    this.getList()
                }
            },
            immediate: true
        },
    },
    components: {
    },
    data: function () {
        return {
            loading: false, // 加载状态
            devcList: [], // 设备列表
            curDeviceCode: '', // 当前选中的设备编码
        };
    },
    created() {
        // 组件创建时获取设备列表
        this.getList();
    },
    destroyed() {
        // 组件销毁时，清除定位标记
        bus.$emit("locationMarkerByType", {});
    },
    methods: {
        /**
         * 禁止拖动
         */
        stopDrag(e) {
            e.stopPropagation();
        },
         /**
         * 列表数据查询
         */
        getList(){
            const params = {
                provinceId: this.provinceId,
                cityId: this.cityId,
            };
            this.loading = true
            api.get(
                `${this.$env.VUE_APP_REQ_PREFIX_PLAT}/analysis/getHornListByAreaId`,
                params
            ).then(res => {
                if (res.code == 200) {
                    this.devcList = res.data || [];
                }
                this.loading = false
            })
        },
        /**
         * 关闭弹窗
         */
        closeDialog() {
            this.$emit('closeFunDialog');
            bus.$emit("locationMarkerByType", {});
        },
        /**
         * 大喇叭定位
         */
         location(data) {
            this.curDeviceCode = data.deviceCode;
            bus.$emit("locationMarkerByType", { key: 'yunguangbo', data });
         }
    },
};
</script>
<style lang="less">
    .horn-device-list-outter {
        width: 350px;
        height: 530px;
        background: rgba(23, 37, 55, 0.9);
        border-radius: 8px;
        position: fixed;
        left: 420px;
        top: 100px;
        color: #fff;
        font-size: 14px;
        font-family: PingFangSC-Regular, sans-serif;
        padding: 12px 14px;
        .device-title{
            display: flex;
            justify-content: space-between;
            padding-bottom: 10px;
            position: relative;
            .alarm-close{
                position: absolute;
                right: -1.5rem;
                top: -1.5rem;
                cursor: pointer;
                font-size: 1rem;
            }
            &::after{
                content: "";
                position: absolute;
                bottom: 0;
                left: 0;
                right: 0;
                height: 2px;
                background: linear-gradient(90deg,#cde3ff,rgba(205,227,255,.24));
            }
        }
        .device-con {
            height: calc(100% - 25px);
            overflow-x: hidden;
            overflow-y: auto;
            color:  rgba(255,255,255,0.65);
            .device-item{
                height: 2rem;
                display: flex;
                align-items: center;
                justify-content: flex-start;
                padding: 0 0.5rem;
                .tree-img {
                    width: 18px;
                    height: 18px;
                    margin-right: 7px;
                    -ms-flex-negative: 0;
                    flex-shrink: 0;
                }
                .item-text{
                    display: flex;
                    .item-name{
                        max-width: 17rem;
                        margin-left: 0.5rem;
                        overflow: hidden;
                        text-overflow:ellipsis;
                        white-space: nowrap;
                        color: #e8f2fe;
                    }
                }
                background: linear-gradient(90deg,rgba(134,176,227,.24),rgba(134,176,227,0));
                &:nth-child(even) {
                    background: rgb(255 255 255 / 0);
                }
                &:hover{
                    background-image: linear-gradient(90deg, rgba(25, 137, 250, 0.8) 0%, rgba(0, 0, 0, 0) 100%);
                    .item-text .item-name{
                        color: #FFF;
                    }
                }
                &.clicked {
                    background-image: linear-gradient(90deg, rgba(25, 137, 250, 0.8) 0%, rgba(0, 0, 0, 0) 100%);
                    .item-text .item-name{
                        color: #FFF;
                    }
                }
            }
        }
    }
    /deep/ .el-loading-mask {
            background-color: #48484800;
        }
</style>
