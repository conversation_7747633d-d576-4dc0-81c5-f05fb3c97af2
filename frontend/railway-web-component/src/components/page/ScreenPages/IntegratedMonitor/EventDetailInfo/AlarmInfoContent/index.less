@import "../../common";

.alarm-info-content-box {
  width: 100%;
  height: 100%;
  overflow-y: auto;

  .alarm-info-title-box {
    width: 100%;
    height: 29px;
    background: url(../../../../../../assets/images/alarmEvent/alarm/alarm_detail_title.png)
      no-repeat;
    background-size: 100%;
    position: relative;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #e8f3ff;
    letter-spacing: 0;
    font-weight: 400;
    padding: 0 12px 0 30px;
    display: flex;
    justify-content: space-between;
    box-sizing: border-box;
    cursor: pointer;

    .iconWrap {
      margin-top: 14px;
    }

    div:nth-child(1) {
      margin-top: 3px;
    }
  }

  .source-content-box {
    width: 100%;
    padding: 12px 0;
    box-sizing: border-box;

    > div:not(:last-child) {
      margin-bottom: 4px;
    }
    .infoDataTitle {
      font-size: 14px;
      text-align: right;
      font-weight: 400;
      color: rgba(255, 255, 255, 0.7);
    }

    .infoDataContent {
      font-size: 14px;
      text-align: left;
      font-weight: 400;
      color: #ffffff;
      padding-right: 5px;
    }

    .infoDataContentLong {
      font-size: 14px;
      text-align: left;
      font-weight: 400;
      color: #ffffff;
      white-space: nowrap; //强制让文字不能换行；
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .form-item-overtime {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      background-image: linear-gradient(45deg, #e24757, transparent);
      border-radius: 4px;

      .el-icon-time {
        margin: 0 5px;
      }
    }

    .level-tag1 {
      color: #ff0000;
    }

    .level-tag2 {
      color: #fb913c;
    }

    .level-tag3 {
      color: #ffe000;
    }

    .level-tag4 {
      color: #4f9fff;
    }

    .addressChange {
      display: flex;
      flex-wrap: wrap;
      align-content: center;
      justify-content: center;

      .address-btn {
        width: 60px;
        height: 25px;
        line-height: 23px;
        color: #ffffff;
      }
    }
  }

  .el-row {
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
  }
}

.text-source {
  width: 14px;
  height: 14px;
  background: #fac800;
  border-radius: 7px;
  line-height: 14px;
  color: #ffffff;
  font-size: 12px;
  margin: 6px 0 0 0;
  text-align: center;
}

.timeline-box {
  margin-top: 20px;

  .el-card {
    .el-card__body,
    .el-main {
      padding: 0;
    }

    background: transparent;
    border: none;
  }

  .el-timeline-item__dot {
    top: -15px;

    .handle-line-head {
      margin-left: 17px;

      .handle-line-title {
        box-sizing: border-box;
        padding: 2px 5px;
        border-radius: 4px;
        margin-left: 12px;
        color: @theme-color;
        background: @input-inner;
      }

      &.active {
        .handle-line-date {
          color: @theme-color;
        }

        .handle-line-title {
          box-sizing: border-box;
          padding: 2px 5px;
          border-radius: 4px;
          margin-left: 12px;
          color: #ffffff;
          background-image: @confirm-btn-bg;
        }
      }
    }
  }

  .el-timeline-item__wrapper {
    padding-left: 0;
  }

  .el-timeline-item__content {
    min-height: 12px;
  }

  .el-timeline-item__tail {
    border-left: 2px solid #8989896e;
  }

  .timeline-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #385373;

    &.active {
      background: @theme-color;
    }
  }

  &.el-timeline .el-timeline-item:last-child .el-timeline-item__tail {
    display: block;
  }
}
