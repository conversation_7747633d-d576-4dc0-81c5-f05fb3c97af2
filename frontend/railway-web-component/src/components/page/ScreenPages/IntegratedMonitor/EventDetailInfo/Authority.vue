<!--
 * @Description: 根据权限点显示不同的内容
 * @Autor: he.chao
 * @Date: 2022-10-19 15:07:55
 * @LastEditors: liu.yongli
 * @LastEditTime: 2023-12-13 14:36:14
-->
<template>
  <!-- 如果具有权限，则显示插槽内容 -->
  <div v-if="auth">
    <slot></slot>
  </div>
</template>
<script>
import { HandleEvent } from './HandleWinCfg'

/**
 * 权限处理器对象，包含各种事件的权限判断逻辑
 */
const AuthHandler = {
  [HandleEvent.leaderGuidance.name]: (order, linkItem) => {
    /**
     * 判断是否具有领导引导权限
     * @param {Object} order 订单信息
     * @param {Object} linkItem 链接项信息
     * @returns {boolean} 返回权限判断结果
     */
    const { ifEdit, orderStatus } = order
    // 判断条件：订单可编辑、链接项存在且领导标志为'1'、订单状态不为'6'
    return (
      ifEdit === 1 &&
      linkItem &&
      linkItem.leaderFlag === '1' &&
      orderStatus !== '6'
    )
  },
  [HandleEvent.rollback.name]: (order, linkItem) => {
    /**
     * 判断是否具有回退权限
     * @param {Object} order 订单信息
     * @param {Object} linkItem 链接项信息
     * @returns {boolean} 返回权限判断结果
     */
    const { ifEdit, enableAcceptanceMode } = order
    // 判断条件：订单可编辑、链接项存在且回退标志为'1'、受理模式不为'1'
    return (
      ifEdit === 1 &&
      linkItem &&
      linkItem.rollbackFlag === '1' &&
      enableAcceptanceMode !== '1'
    )
  },
  [HandleEvent.trueAlarm.name]: (order, linkItem) => {
    /**
     * 判断是否具有真实报警权限
     * @param {Object} order 订单信息
     * @param {Object} linkItem 链接项信息
     * @returns {boolean} 返回权限判断结果
     */
    const { ifEdit, orderStatus } = order
    // 判断条件：订单可编辑且订单状态为'1'
    return ifEdit === 1 && orderStatus === '1'
  },
  /**
   * 判断订单是否满足处置的条件。
   * @param {Object} order - 订单对象。
   * @param {number} order.ifEdit - 订单的编辑权限标志。
   * @param {string} order.orderStatus - 订单的状态。
   * @returns {boolean}
   */
  [HandleEvent.disposition.name]: order => {
    const { ifEdit, orderStatus } = order
    // 判断条件：订单可编辑且订单状态为'4'
    return ifEdit === 1 && orderStatus === '4'
  },
  /**
   * 判断订单是否满足核实的条件。
   * @param {Object} order - 订单对象。
   * @param {number} order.ifEdit - 订单的编辑权限标志。
   * @param {string} order.orderStatus - 订单的状态。
   * @returns {boolean}
   */
  [HandleEvent.verification.name]: order => {
    const { ifEdit, orderStatus } = order
    // 判断条件：订单可编辑且订单状态为'5'
    return ifEdit === 1 && orderStatus === '5'
  },
  /**
   * 判断订单是否满足受理的条件。
   * @param {Object} order - 订单对象。
   * @param {number} order.ifEdit - 订单的编辑权限标志。
   * @param {string} order.orderStatus - 订单的状态。
   * @returns {boolean}
   */
  [HandleEvent.acceptance.name]: order => {
    const { ifEdit, enableAcceptanceMode } = order
    // 判断条件：订单可编辑且受理模式为'1'
    return ifEdit === 1 && enableAcceptanceMode === '1'
  },
  /**
   * 判断订单是否满足转派的条件。
   * @param {Object} order - 订单对象。
   * @param {number} order.ifEdit - 订单的编辑权限标志。
   * @param {string} order.orderStatus - 订单的状态。
   * @returns {boolean}
   */
  [HandleEvent.transfer.name]: (order, linkItem) => {
    const { ifEdit, enableAcceptanceMode } = order
    // 判断条件：订单可编辑、链接项存在且转派标志为'1'、受理模式不为'1'
    return (
      ifEdit === 1 &&
      linkItem &&
      linkItem.turnFlag === '1' &&
      enableAcceptanceMode !== '1'
    )
  },
  /**
   * 判断订单是否满足误报的条件。
   * @param {Object} order - 订单对象。
   * @param {number} order.ifEdit - 订单的编辑权限标志。
   * @param {string} order.orderStatus - 订单的状态。
   * @returns {boolean} - 如果订单满足编辑状态为1且状态为'1'的条件，则返回true，否则返回false。
   */
  [HandleEvent.falseAlarm.name]: order => {
    const { ifEdit, orderStatus } = order
    // 判断条件：订单可编辑且订单状态为'1'
    return ifEdit === 1 && orderStatus === '1'
  },
  /**
   * 判断订单是否满足重复提醒的条件。
   * @param {Object} order - 订单对象。
   * @param {number} order.ifEdit - 订单的编辑权限标志。
   * @param {string} order.orderStatus - 订单的状态。
   * @returns {boolean} - 如果订单满足编辑状态为1且状态为'1'的条件，则返回true，否则返回false。
   */
  [HandleEvent.repeatAlarm.name]: order => {
    const { ifEdit, orderStatus } = order
    // 判断条件：订单可编辑且订单状态为'1'
    return ifEdit === 1 && orderStatus === '1'
  },
  /**
   * 判断订单是否满足调度的条件。
   * @param {Object} order - 订单对象。
   * @param {number} order.ifEdit - 订单的编辑权限标志。
   * @param {string} order.orderStatus - 订单的状态。
   * @returns {boolean} - 如果订单满足编辑状态为1且状态为'2'的条件，则返回true，否则返回false。
   */
  [HandleEvent.scheduling.name]: order => {
    const { ifEdit, orderStatus } = order
    // 判断条件：订单可编辑且订单状态为'2'
    return ifEdit === 1 && orderStatus === '2'
  },
  /**
   * 判断订单是否满足关闭的条件。
   * @param {Object} order - 订单对象。
   * @param {number} order.ifEdit - 订单的编辑权限标志。
   * @param {string} order.orderStatus - 订单的状态。
   * @returns {boolean} - 如果订单满足编辑状态为1且状态不为'0'、'6'、'7'之一的条件，则返回true，否则返回false。
   */
  [HandleEvent.close.name]: order => {
    const { ifEdit, orderStatus } = order
    // 判断条件：订单可编辑且订单状态不为'0'、'6'、'7'
    return ifEdit === 1 && !['0', '6', '7'].includes(orderStatus)
  },
}
export default {
  props: {
    authPoint: {
      type: String,
      default: '',
    },
    alarmInfo: {
      type: Object,
      default: {},
    },
    linkDetail: {
      type: Array,
      default: [],
    },
  },
  watch: {
    // 监听alarmInfo的变化，触发权限处理
    alarmInfo(newAlarmInfo, oldAlarmInfo) {
      this.handleAuth()
    },
    // 监听linkDetail的变化，触发权限处理
    linkDetail(newLinkDetail, oldLinkDetail) {
      this.handleAuth()
    },
  },
  data() {
    return {
      // auth用于存储当前权限状态
      auth: false,
    }
  },
  mounted() {
    // 组件挂载时处理权限
    this.handleAuth()
  },
  methods: {
    handleAuth() {
      const { order = {} } = this.alarmInfo
      // 根据订单信息查找对应的链接项
      const linkItem = this.linkDetail.find(
        item => String(item.linkId) === order.linkId
      )
      // 根据权限点和订单信息判断权限
      this.auth =
        AuthHandler[this.authPoint] &&
        AuthHandler[this.authPoint](order, linkItem)
    },
  },
}
</script>
