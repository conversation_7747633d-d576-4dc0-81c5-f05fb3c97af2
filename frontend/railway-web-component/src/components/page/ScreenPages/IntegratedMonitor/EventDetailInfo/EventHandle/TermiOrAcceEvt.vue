<!--
 * @Description  : 处置
 * <AUTHOR> wnj
 * @Date         : 2023-12-11 10:58:52
 * @LastEditors  : wnj
 * @LastEditTime : 2023-12-26 14:50:20
 * @FilePath     :  / src / components / page / alarmEvent / components / alarmRight / alarmDisposeDialog.vue
-->
<!-- 告警各环节处理弹窗 -->
<template>
  <!-- 使用 CtlPanel 组件包裹内容，设置关闭事件为 toClose -->
  <CtlPanel class='evt-handle-wrap alarm-dispatch-outter terminate-evt' :close='toClose'>
    <!-- 定义标题插槽，显示告警处理数据的标题 -->
    <template v-slot:title>
      {{ alarmDisposeData.title }}
    </template>
    <!-- 定义内容插槽 -->
    <template v-slot:content>
      <div class='dispatch-con evt-handle-form'>
        <!-- 根据标题显示不同的提示信息 -->
        <div class='text-tip' v-if=" alarmDisposeData.title === '受理' || alarmDisposeData.title === '关闭'">
          {{ alarmDisposeData.title === '受理' ? '确定是否要受理该事件？' : '是否要将当前的事件关闭？' }}
        </div>
      </div>
      <!-- 操作按钮 -->
      <div class='dispatch-buttons'>
        <!-- 确定按钮，点击触发 toSubmit 方法 -->
        <div class='dispatch-button dispatch-button-blue' @click='toSubmit'><span>确定</span></div>
        <!-- 取消按钮，点击触发 toClose 方法 -->
        <div class='dispatch-button' @click='toClose'><span>取消</span></div>
      </div>
    </template>
  </CtlPanel>
</template>
<script>
import api from '@/api'; // 导入 API 模块
import CtlPanel from '@/components/page/ScreenPages/IntegratedMonitor/CtlPanel/index.vue'; // 导入 CtlPanel 组件
export default {
  components: {
    CtlPanel // 注册 CtlPanel 组件
  },
  props: {
    propData: {
      type: Object,
      default: {} // 默认值为空对象
    },
    close: {
      type: Function,
      default: () => {
        console.log('close'); // 默认关闭函数
      }
    },
    okCalBack: {
      type: Function,
      default: () => {
        console.log('okCalBack'); // 默认确认回调函数
      }
    }
  },
  data() {
    return {
      alarmDisposeData: this.propData, // 告警处理数据
      linkId: '', // 下一环节 ID
      timeLimit: '' // 下一环节超期时间
    };
  },
  methods: {
    /**
     * 确认提交
     */
    toSubmit() {
      this.toAcceptOrClose(); // 调用受理或关闭事件方法
    },
    /**
     * 取消提交
     */
    toClose() {
      this.close(); // 调用关闭方法
    },
    /**
     * 受理和关闭事件
     */
    toAcceptOrClose() {
      let alarmInfo = this.alarmDisposeData.alarmInfo; // 获取告警信息
      if (this.alarmDisposeData.title === '受理') {
        this.acceptEvent(alarmInfo); // 如果标题为受理，调用受理事件方法
      } else {
        this.closeEvent(alarmInfo); // 否则调用关闭事件方法
      }
    },
    /**
     * 确认受理事件
     */
    acceptEvent(alarmInfo) {
      let params = {
        warningOrderId: alarmInfo.order.warningOrderId, // 告警订单 ID
        linkId: alarmInfo.order.linkId, // 环节 ID
        flowId: alarmInfo.order.flowId, // 流程 ID
        timeLimit: alarmInfo.order.timeLimit, // 环节超限时间
        address: alarmInfo.order.address, // 事发地点
        orderStatus: alarmInfo.order.orderStatus, // 事件状态
        warningTypeId: alarmInfo.order.warningTypeId, // 告警类型
        alarmTime: alarmInfo.order.alarmTime // 告警时间
      };
      // 发送受理请求
      api.post(
        `${this.$env.VUE_APP_REQ_PREFIX_PLAT}/event/accept`,
        params
      ).then(res => {
        if (res.code == 200) {
          this.okCalBack(); // 调用确认回调
          this.$message({
            type: 'success',
            message: res.msg // 显示成功消息
          });
        } else {
          this.$message({
            type: 'error',
            message: res.msg || '确认受理事件失败！' // 显示错误消息
          });
        }
      });
    },
    /**
     * 关闭事件 (直接完结事件)
     */
    closeEvent(alarmInfo) {
      let params = {
        warningTypeId: alarmInfo.order.warningTypeId, // 告警类型
        warningOrderList: [] // 告警订单列表
      };
      let object = {};
      object.warningOrderId = alarmInfo.order.warningOrderId; // 告警订单 ID
      object.currentLink = alarmInfo.order.linkId; // 当前环节 ID
      params.warningOrderList.push(object); // 添加到订单列表
      if (alarmInfo.order?.changeAddCodeName?.isChange) {
        params = this.toAddressData(params, alarmInfo); // 如果地址有变更，处理地址数据
      }
      // 发送关闭请求
      api.post(
        `${this.$env.VUE_APP_REQ_PREFIX_PLAT}/event/batchEndWarningOrder`,
        params
      ).then(res => {
        if (res.code == 200) {
          this.$message({
            type: 'success',
            message: res.msg // 显示成功消息
          });
          this.okCalBack(); // 调用确认回调
        } else {
          this.$message({
            type: 'error',
            message: res.msg || '关闭事件失败！' // 显示错误消息
          });
        }
      });
    },
    /**
     * 修改地址时处理地址数据
     */
    toAddressData(params, alarmInfo) {
      // 修改数据处理
      params.gridId = alarmInfo.order.gridId; // 网格 ID
      params.gridName = alarmInfo.order.gridName; // 网格名称
      params.checkGridFlag = 1; // 是否修改网格信息和水平方位角
      if (alarmInfo.order.warningSource === '1' || alarmInfo.order.warningSource === '4') {
        params.horiAzimuthAngle = alarmInfo.order.horiAzimuthAngle; // 水平方位角
      }
      params.address = alarmInfo.order.changeAddCodeName.address; // 详细地址名称
      params.longitude = alarmInfo.order.changeAddCodeName.longitude; // 经度名称
      params.latitude = alarmInfo.order.changeAddCodeName.latitude; // 纬度名称
      params.provinceId = alarmInfo.order.changeAddCodeName.provinceId; // 省份 ID
      params.provinceName = alarmInfo.order.changeAddCodeName.provinceName; // 省份名称
      params.cityId = alarmInfo.order.changeAddCodeName.cityId; // 地市 ID
      params.cityName = alarmInfo.order.changeAddCodeName.cityName; // 地市名称
      params.countyId = alarmInfo.order.changeAddCodeName.countyId; // 区县 ID
      params.countyName = alarmInfo.order.changeAddCodeName.countyName; // 区县名称
      return params; // 返回处理后的参数
    }
  }
};
</script>
<style lang='less' src='./index.less' />
<style lang='less' scoped>
</style>
