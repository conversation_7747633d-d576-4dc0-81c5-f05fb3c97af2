<!-- 环节说明文本框 + 常用语 -->
<template>
  <div class="alarm-phrases">
    <!-- 文本输入框，用于输入或显示常用语 -->
    <el-input
      type="textarea"
      resize="none"
      :rows="autosizeRow"
      :placeholder="placeholder"
      :maxlength="maxLength"
      v-model="val"
      class="text-class"
      :style="{marginBottom:pxToRem(6)}"
    ></el-input>
    <!-- 下拉选择框，用于选择常用语 -->
    <el-select
      ref="commentSelect"
      :value="commentSel"
      placeholder="常用语"
      popper-class="audit-comments-popper"
      @change="commentSelHandler"
      class="commentSelect"
      :disabled="commentsManageFlag"
    >
      <!-- 常用语选项 -->
      <el-option
        v-for="(item, index) in auditComments"
        :key="item.dictId"
        :label="item.dicName"
        :value="index"
      >
        <!-- 显示常用语选项 -->
        <div
          :c-tip="item.dicName"
          class="comment-opt-group"
          :class="{ selected: index === commentSel }"
        >
          <span>{{ index + 1 }}.{{ item.dicName }}</span>
        </div>
        <!-- 选中标记 -->
        <span v-if="index === commentSel" class="selected">
          <svg-icon
            style="margin:0;width: 18px;height: 18px;"
            svg-name="icon_select_selected_16_blue"
          />
        </span>
      </el-option>
    </el-select>
    <!-- 常用语管理和展开控制 -->
    <div class="comment-ctl" :style="{fontSize:pxToRem(14)}">
      <div class="comments-tag" @click="expandList">常用语</div>
      <div class="comments-manage" ref="commentsManage" @click="commentsManageHandler(true)">管理</div>
    </div>
    <!-- 常用语编辑输入框组 -->
    <div
      class="comments-input-group"
      v-resize="resizeFn"
      :style="{position:'fixed',top:`${popTop}px`,left: `${popLeft}px`,height: `${popHeight}px`,zIndex: 9,display:commentsManageFlag ? 'flex': 'none'}"
    >
      <div class="group-list">
        <!-- 单个常用语输入框 -->
        <div
          class="comment-input-single"
          v-for="(item, index) in editCommentsShow"
          :key="item.displayOrder + '_' + item.dicValue"
        >
          <div class="input-outter">
            <span>{{ index + 1 }}.</span>
            <el-input
              :value="item.dicName"
              placeholder="请输入常用语"
              @focus="allowInputIndexBind(item.displayOrder)"
              @input="inputChange"
              label="index"
            />
          </div>
          <!-- 输入框操作按钮组 -->
          <div class="input-btn-group">
            <!-- 添加常用语按钮 -->
            <span @click="addComment()">
              <svg-icon
                v-if="index + 1 === editCommentsShow.length"
                svg-name="icon_add_circle_16_n"
              />
            </span>
            <!-- 删除常用语确认弹窗 -->
            <el-popconfirm
              title="确认删除该常用语？"
              class="delete-phrases-tip"
              @confirm="deleteComment(item.displayOrder - 1)"
              style="margin-left: 10px"
            >
              <svg-icon slot="reference" svg-name="icon_delete_circle_16_n"></svg-icon>
            </el-popconfirm>
          </div>
        </div>
      </div>
      <!-- 保存和取消按钮 -->
      <div class="comments-input-save">
        <span class="btn btn-confirm" @click="saveHandler">保存</span>
        <span class="btn btn-cancel" @click="commentsManageHandler(false)">取消</span>
      </div>
    </div>
  </div>
</template>
<script>
import api from '@/api'
import {
  getApprovalList,
  postApprovalDetail,
} from '@/api/service/imScreenService'
import dayjs from 'dayjs'
export default {
  name: 'alarm-phrases',
  props: {
    value: {}, // 绑定的值
    maxLength: {
      default: 200, // 最大长度
    },
    approvalListParam: {
      type: Object,
      default: null, // 审批列表参数
    },
    placeholder: {}, // 输入框占位符
    isUnfold: {
      default: false, // 是否展开
    },
    orderStatus: {}, // 订单状态
    scale: {
      default: 1, // 缩放比例
    },
  },
  data() {
    return {
      expand: false, // 常用语是否展开
      isEdit: false, // 是否编辑常用语
      phrasesList: [], // 常用语列表
      phrasesCount: 0, // 常用语计数
      val: '', // 输入框绑定值
      popLeft: 0, // 弹出框左侧位置
      popTop: 0, // 弹出框顶部位置
      popHeight: 200, // 弹出框高度
      isUnfoldNew: '', // 新的展开状态
      autosizeRow: 3, // 自动调整行数
      auditComments: [], // 审核意见列表
      editComments: [], // 编辑中的意见列表
      commentSel: null, // 选中的评论索引
      commentsManageFlag: false, // 评论管理模式标志
      allowInputIndex: null, // 允许输入的索引
    }
  },
  watch: {
    isUnfold(val) {
      this.isUnfoldNew = val // 监听展开状态变化
    },
    isUnfoldNew(val) {
      this.$emit('update', Boolean(val)) // 触发更新事件
    },
    value(val) {
      this.val = val // 监听绑定值变化
    },
    val(val) {
      this.$emit('input', val) // 触发输入事件
    },
    _phrasesList() {
      // 判断有几条数据为空
      let count = 0
      this.phrasesList.forEach(item => {
        if (item.content) {
          count++
        }
      })
      this.phrasesCount = count // 更新常用语计数
    },
  },
  computed: {
    editCommentsShow() {
      return this.editComments.filter(val => val.state === 1) // 过滤出有效的编辑意见
    },
  },
  mounted() {
    this.qryDictList() // 查询字典列表
    this.$nextTick(() => {
      this.resizeFn() // 调整弹出框位置
    })
  },
  methods: {
    /**
     * 获取常用语
     */
    getList() {
      api
        .post(
          `${this.$env.VUE_APP_REQ_PREFIX_PLAT}/alarm/commonly/getProcessCommonWords`,
          {}
        )
        .then(res => {
          if (res.code == 200) {
            this.phrasesList = res.data // 更新常用语列表
          } else {
            this.$message({
              type: 'error',
              message: res.msg || '常用语查询失败！', // 查询失败提示
            })
          }
        })
    },
    /**
     * 展开或收起常用语列表
     */
    unfold() {
      this.isUnfoldNew = !this.isUnfoldNew // 切换展开状态
      this.autosizeRow = 3 // 重置行数
    },
    /**
     * 开始编辑常用语
     */
    editBegin() {
      this.isEdit = !this.isEdit // 切换编辑状态
    },
    /**
     * 提交编辑后的常用语
     */
    editPhrases() {
      api
        .post(
          `${this.$env.VUE_APP_REQ_PREFIX_PLAT}/alarm/commonly/updateProcessCommonWords`,
          this.phrasesList
        )
        .then(res => {
          if (res.code == 200) {
            this.$message({
              type: 'success',
              message: res.msg, // 编辑成功提示
            })
            this.editBegin() // 结束编辑
          } else {
            this.$message({
              type: 'error',
              message: res.msg || '编辑常用语失败！', // 编辑失败提示
            })
          }
        })
    },
    /**
     * 选择常用语
     * @param {string} val - 常用语内容
     */
    choosePhrases(val) {
      if (!this.isEdit) {
        this.val = val // 更新输入框值
        this.unfold() // 收起常用语列表
      }
    },
    /**
     * 展开或收起评论选项列表
     */
    expandList() {
      this.expand = !this.expand // 切换展开状态
      this.$refs.commentSelect.toggleMenu(this.expand) // 切换菜单
    },
    /**
     * 异步查询审批意见列表
     */
    async qryDictList() {
      let result = []
      const [success, data] = await getApprovalList(
        this.approvalListParam || {
          catCode: `LINK_APPROVAL_${this.orderStatus}`,
        }
      )
      if (success && data && data.length > 0) {
        result = data
        result.sort(
          (a, b) => a.displayOrder - b.displayOrder // 按显示顺序排序
        )
      }
      this.auditComments = result // 更新审核意见列表
    },
    /**
     * 处理评论选项选择事件
     * @param {number} index - 选项索引
     */
    commentSelHandler(index) {
      if (this.commentsManageFlag) {
        return // 如果在管理模式下，阻止选择
      }
      this.commentSel = Number(index) // 更新选中索引
      this.val = this.auditComments[index].dicName // 更新输入框值
    },
    /**
     * 调整评论选项弹出框的位置
     */
    resizeFn() {
      const react = this.$refs.commentSelect.$el.getBoundingClientRect()
      this.popTop = react.top - this.popHeight // 计算弹出框顶部位置
      this.popLeft = react.left // 计算弹出框左侧位置
      console.log(this.popTop, this.popLeft) // 输出位置
    },
    /**
     * 开启或关闭评论选项管理模式
     * @param {boolean} flag - 管理模式标志
     */
    commentsManageHandler(flag) {
      if (flag) {
        this.editComments = this.auditComments.length
          ? [...this.auditComments] // 复制审核意见列表
          : [
              {
                dicValue: dayjs().format('YYYYMMDDHHmmss'), // 生成唯一值
                dicName: '',
                state: 1,
                displayOrder: 1,
              },
            ]
      }
      this.commentsManageFlag = flag // 更新管理模式标志
    },
    /**
     * 添加新的审批意见选项
     */
    addComment() {
      this.editComments.push({
        dicValue: dayjs().format('YYYYMMDDHHmmss'), // 生成唯一值
        dicName: '',
        state: 1,
        displayOrder:
          this.editComments.filter(val => val.state === 1).length + 1, // 更新显示顺序
      })
    },
    /**
     * 删除审批意见选项
     * @param {number} index - 选项索引
     */
    deleteComment(index) {
      this.editComments[index].state = 0 // 标记为删除
      let order = 1
      const commentsNew = [...this.editComments]
      commentsNew.forEach(val => {
        val.displayOrder = order++ // 更新显示顺序
      })
      commentsNew.sort((a, b) => a.displayOrder - b.displayOrder) // 按显示顺序排序
      this.editComments = commentsNew // 更新编辑意见列表
    },
    /**
     * 绑定可输入的评论索引
     * @param {number} index - 选项索引
     */
    allowInputIndexBind(index) {
      this.allowInputIndex = index // 更新允许输入的索引
    },
    /**
     * 处理评论选项的输入变化
     * @param {string} e - 输入内容
     */
    inputChange(e) {
      const index = this.editComments.findIndex(
        val => val.displayOrder === this.allowInputIndex && val.state === 1
      )
      this.editComments[index].dicName = e // 更新意见名称
      this.$forceUpdate() // 强制更新视图
    },
    /**
     * 异步保存审批意见。
     * 验证编辑中的意见是否都填写了有效的名称，然后提交这些意见。
     * 如果有未填写的内容，则提示用户并停止提交。
     * 如果所有内容都填写了，则为每个意见分配一个分类代码，并尝试提交。
     * 提交成功或失败后，都会给用户相应的反馈。
     */
    async saveHandler() {
      // 初始化一个数组，用于存储存在错误的行号。
      const errorLine = []
      // 遍历编辑中的意见，检查是否有空的名称。
      this.editCommentsShow.forEach((val, idx) => {
        if (val.dicName === '' || val.dicName.trim() === '') {
          errorLine.push(idx + 1)
        }
      })
      // 如果存在错误行号，提示用户并停止提交。
      if (errorLine.length) {
        this.$message({
          type: 'error',
          message: `第${errorLine.join(',')}内容无效,请填写完整`,
        })
        return
      }
      // 准备分类代码，如果已有则使用，否则根据订单状态生成一个新的。
      const catCode = this.approvalListParam || {
        catCode: `LINK_APPROVAL_${this.orderStatus}`,
      }
      // 为每个待提交的意见分配分类代码。
      this.editComments.map(item => {
        item.catCode = catCode.catCode
      })
      // 尝试提交审批意见。
      const [success, data] = await postApprovalDetail(this.editComments)
      // 提交成功时，给用户成功提示，并刷新字典列表。
      if (success) {
        this.$message({
          type: 'success',
          message: '操作成功',
        })
        await this.qryDictList()
      } else {
        // 提交失败时，给用户错误提示。
        this.$message({
          type: 'error',
          message: '操作失败：' + data,
        })
      }
    },
  },
}
</script>
<style src='./index.less' lang='less'>
</style>
