@import "../../../common";
//详情展示附件样式
.fileBox {
  display: flex;
}

.file-imageAll-main-box {
  overflow: hidden;
}

.file-imageAll-box {
  display: flex;
  transition: all 0.5s;
  flex-wrap: wrap;
}

.file-image-box {
  margin: 0 12px;

  .el-link {
    &.el-link--default {
      color: @theme-color;
    }

    .el-link--inner {
      max-width: 145px;
      white-space: nowrap; //强制文本在一行内输出
      overflow: hidden; //隐藏溢出部分
      text-overflow: ellipsis; //对溢出部分加上...
    }
  }
}

.file-image {
  width: 80px;
  height: 80px;
}

.file-video {
  height: 80px;
  width: 80px;
  object-fit: fill;
}

.fileLeft {
  width: 20px;
  height: 38px;
  color: #ffffff;
  position: absolute;
  float: left;
  font-size: 25px;
  line-height: 35px;
  text-align: center;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 5px 0 0 5px;
}

.fileRight {
  width: 20px;
  height: 38px;
  color: #ffffff;
  position: absolute;
  left: 286px;
  float: right;
  font-size: 25px;
  line-height: 35px;
  text-align: center;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 0 5px 5px 0;
}
