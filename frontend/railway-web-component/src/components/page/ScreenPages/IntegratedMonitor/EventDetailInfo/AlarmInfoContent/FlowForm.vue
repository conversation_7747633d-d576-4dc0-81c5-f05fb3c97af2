<!--
 * @Description:
 * @Autor: he.chao
 * @Date: 2022-10-19 15:07:55
 * @LastEditors: liu.yongli
 * @LastEditTime: 2023-12-13 14:36:14
-->
<template>
  <div class="source-content-box">
    <!-- 遍历formItems对象的键，根据不同的inputType展示不同的组件或内容 -->
    <template v-for="(item,itmIdx) in Object.keys(formItems)">
      <!-- 文件类型input，展示文件审查组件 -->
      <template v-if="formItems[item].inputType === INPUT_TYPE.file">
        <el-row :key="`${formItems[item].label}_${itmIdx}_0`">
          <!-- 如果有标签，展示标签 -->
          <el-col
            :span="8"
            v-if="formItems[item].label"
            class="infoDataTitle"
          >{{ formItems[item].label }}：</el-col>
          <!-- 展示文件审查组件 -->
          <el-col :span="16" class="infoDataContent">
            <FileReview :files="record[item]" />
          </el-col>
        </el-row>
      </template>
      <!-- 无标签的文本类型input，根据开关状态和显示条件展示内容 -->
      <template v-else-if="formItems[item].inputType === INPUT_TYPE.noneLabel">
        <!-- 受监测开关控制的展示逻辑 -->
        <template v-if="formItems[item].switch">
          <!-- 如果有显示条件，且满足条件，则展示内容 -->
          <template v-if="formItems[item].show">
            <el-row
              v-if="$store.state.map.checkMode && formItems[item].show(alarmInfo, record)"
              :key="`${formItems[item].label}_${itmIdx}_1`"
            >
              <el-col :span="24" class="infoDataContent">
                <!-- 如果有自定义HTML内容，展示HTML -->
                <template v-if="formItems[item].getHtml">
                  <div v-html="formItems[item].getHtml(alarmInfo, record)"></div>
                </template>
                <!-- 否则展示格式化后的内容或原始记录 -->
                <template
                  v-else
                >{{ formItems[item].format ? formItems[item].format(alarmInfo, record) : record[item] }}</template>
              </el-col>
            </el-row>
          </template>
          <!-- 如果没有显示条件，但处于监测模式，则展示内容 -->
          <template v-else>
            <el-row v-if="$store.state.map.checkMode" :key="`${formItems[item].label}_${itmIdx}_2`">
              <el-col :span="24" class="infoDataContent">
                <template v-if="formItems[item].getHtml">
                  <div v-html="formItems[item].getHtml(alarmInfo, record)"></div>
                </template>
                <template
                  v-else
                >{{ formItems[item].format ? formItems[item].format(alarmInfo, record) : record[item] }}</template>
              </el-col>
            </el-row>
          </template>
        </template>
        <!-- 不受监测开关控制的展示逻辑 -->
        <template v-else>
          <!-- 如果有显示条件，且满足条件，则展示内容 -->
          <template v-if="formItems[item].show">
            <el-row
              v-if="formItems[item].show(alarmInfo, record)"
              :key="`${formItems[item].label}_${itmIdx}_3`"
            >
              <el-col :span="24" class="infoDataContent">
                <template v-if="formItems[item].getHtml">
                  <div v-html="formItems[item].getHtml(alarmInfo, record)"></div>
                </template>
                <template
                  v-else
                >{{ formItems[item].format ? formItems[item].format(alarmInfo, record) : record[item] }}</template>
              </el-col>
            </el-row>
          </template>
          <!-- 如果没有显示条件，则直接展示内容 -->
          <template v-else>
            <el-row :key="`${formItems[item].label}_${itmIdx}_4`">
              <el-col :span="24" class="infoDataContent">
                <template v-if="formItems[item].getHtml">
                  <div v-html="formItems[item].getHtml(alarmInfo, record)"></div>
                </template>
                <template
                  v-else
                >{{ formItems[item].format ? formItems[item].format(alarmInfo, record) : record[item] }}</template>
              </el-col>
            </el-row>
          </template>
        </template>
      </template>
      <!-- 普通文本类型input，根据开关状态和显示条件展示内容 -->
      <template v-else>
        <!-- 受监测开关控制的展示逻辑 -->
        <template v-if="formItems[item].switch">
          <!-- 如果有显示条件，且满足条件，则展示内容 -->
          <template v-if="formItems[item].show">
            <el-row
              v-if="$store.state.map.checkMode && formItems[item].show(alarmInfo, record)"
              :key="`${item.label}_${itmIdx}_5`"
            >
              <!-- 展示标签 -->
              <el-col :span="8" class="infoDataTitle">{{ formItems[item].label }}：</el-col>
              <!-- 展示内容 -->
              <el-col :span="16" class="infoDataContent">
                <template v-if="formItems[item].getHtml">
                  <div v-html="formItems[item].getHtml(alarmInfo, record)"></div>
                </template>
                <template
                  v-else
                >{{ formItems[item].format ? formItems[item].format(alarmInfo, record) : record[item] }}</template>
              </el-col>
            </el-row>
          </template>
          <!-- 如果没有显示条件，但处于监测模式，则展示内容 -->
          <template v-else>
            <el-row v-if="$store.state.map.checkMode" :key="`${formItems[item].label}_${itmIdx}_6`">
              <el-col :span="8" class="infoDataTitle">{{ formItems[item].label }}：</el-col>
              <el-col :span="16" class="infoDataContent">
                <template v-if="formItems[item].getHtml">
                  <div v-html="formItems[item].getHtml(alarmInfo, record)"></div>
                </template>
                <template
                  v-else
                >{{ formItems[item].format ? formItems[item].format(alarmInfo, record) : record[item] }}</template>
              </el-col>
            </el-row>
          </template>
        </template>
        <!-- 不受监测开关控制的展示逻辑 -->
        <template v-else>
          <!-- 如果有显示条件，且满足条件，则展示内容 -->
          <template v-if="formItems[item].show">
            <el-row
              v-if="formItems[item].show(alarmInfo, record)"
              :key="`${formItems[item].label}_${itmIdx}_7`"
            >
              <el-col :span="8" class="infoDataTitle">{{ formItems[item].label }}：</el-col>
              <el-col :span="16" class="infoDataContent">
                <template v-if="formItems[item].getHtml">
                  <div v-html="formItems[item].getHtml(alarmInfo, record)"></div>
                </template>
                <template
                  v-else
                >{{ formItems[item].format ? formItems[item].format(alarmInfo, record) : record[item] }}</template>
              </el-col>
            </el-row>
          </template>
          <!-- 如果没有显示条件，则直接展示内容 -->
          <template v-else>
            <el-row :key="`${formItems[item].label}_${itmIdx}_8`">
              <el-col :span="8" class="infoDataTitle">{{ formItems[item].label }}：</el-col>
              <el-col :span="16" class="infoDataContent">
                <template v-if="formItems[item].getHtml">
                  <div v-html="formItems[item].getHtml(alarmInfo, record)"></div>
                </template>
                <template
                  v-else
                >{{ formItems[item].format ? formItems[item].format(alarmInfo, record) : record[item] }}</template>
              </el-col>
            </el-row>
          </template>
        </template>
      </template>
    </template>
  </div>
</template>
<script>
import FileReview from '@/components/page/ScreenPages/IntegratedMonitor/EventDetailInfo/components/FileReview'
import { INPUT_TYPE } from './LinkConst'
/**
 * @component 该组件用于展示文件审核信息。
 * @prop {Object} record 审核记录对象，包含审核的具体信息。
 * @prop {Object} alarmInfo 报警信息对象，可能包含与审核相关的警告信息。
 * @prop {Object} formItems 表单项目对象，可能用于在审核过程中收集额外的信息。
 */
export default {
  components: { FileReview },
  props: {
    record: {
      type: Object,
      default: () => ({}),
    },
    alarmInfo: {
      type: Object,
      default: () => ({}),
    },
    formItems: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      INPUT_TYPE,
    }
  },
  methods: {},
}
</script>
<style lang='less' src='./index.less' />
