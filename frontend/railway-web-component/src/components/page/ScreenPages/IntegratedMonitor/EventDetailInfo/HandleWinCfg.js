// 导入不同的组件，用于处理不同类型的告警事件
import AlarmDisposeDialog from '@/components/page/ScreenPages/IntegratedMonitor/EventDetailInfo/EventHandle/alarmDisposeDialog.vue';
import AlarmLeaderDialog from '@/components/page/ScreenPages/IntegratedMonitor/EventDetailInfo/EventHandle/alarmLeaderDialog.vue';
import DistributeAndCorrection from '@/components/page/ScreenPages/IntegratedMonitor/EventDetailInfo/EventHandle/DistributeAndCorrection.vue';
import Verification from '@/components/page/ScreenPages/IntegratedMonitor/EventDetailInfo/EventHandle/Verification.vue';
import AlarmRepeatDialog from './EventHandle/alarmRepeatDialog.vue';
import FalseAlarm from './EventHandle/FalseAlarm.vue';
import TermiOrAcceEvt from './EventHandle/TermiOrAcceEvt.vue';

// 定义一个对象，用于描述不同的事件处理类型
export const HandleEvent = {
  rollback: {
    name: 'Rollback',
    desc: '回退' // 回退事件
  },
  trueAlarm: {
    name: 'TrueAlarm',
    desc: '真警' // 真警事件
  },
  disposition: {
    name: 'Disposition',
    desc: '处置' // 处置事件
  },
  verification: {
    name: 'Verification',
    desc: '核实' // 核实事件
  },
  acceptance: {
    name: 'Acceptance',
    desc: '受理' // 受理事件
  },
  transfer: {
    name: 'Transfer',
    desc: '转派' // 转派事件
  },
  falseAlarm: {
    name: 'FalseAlarm',
    desc: '误报' // 误报事件
  },
  repeatAlarm: {
    name: 'RepeatAlarm',
    desc: '重复告警' // 重复告警事件
  },
  scheduling: {
    name: 'Scheduling',
    desc: '调度' // 调度事件
  },
  close: {
    name: 'Close',
    desc: '关闭' // 关闭事件
  },
  leaderGuidance: {
    name: 'LeaderGuidance',
    desc: '转领导批示' // 转领导批示事件
  }
};

// 默认导出一个对象，包含不同事件处理的配置
export default {
  // 回退事件处理配置
  [HandleEvent.rollback.desc]: {
    component: () => AlarmDisposeDialog, // 使用AlarmDisposeDialog组件
    props: (alarmInfo) => {
      return {
        title: HandleEvent.rollback.desc, // 设置标题为“回退”
        alarmInfo // 传递告警信息
      };
    },
    onok: (handleInstance) => {
      handleInstance.refreshInfoAndList(true); // 刷新信息和列表
    }
  },
  // 真警事件处理配置
  [HandleEvent.trueAlarm.desc]: {
    component: () => AlarmDisposeDialog,
    props: (alarmInfo) => {
      return {
        title: HandleEvent.trueAlarm.desc,
        alarmInfo
      };
    },
    onok: (handleInstance) => {
      handleInstance.refreshInfoAndList(true);
    }
  },
  // 处置事件处理配置
  [HandleEvent.disposition.desc]: {
    component: () => {
      return AlarmDisposeDialog;
    },
    props: (alarmInfo) => {
      return {
        title: HandleEvent.disposition.desc,
        alarmInfo
      };
    },
    onok: (handleInstance) => {
      handleInstance.refreshInfoAndList(true);
    }
  },
  // 核实事件处理配置
  [HandleEvent.verification.desc]: {
    component: () => Verification, // 使用Verification组件
    props: (alarmInfo) => {
      return {
        title: HandleEvent.verification.desc,
        alarmInfo
      };
    },
    onok: (handleInstance) => {
      handleInstance.refreshInfoAndList(true);
    }
  },
  // 受理事件处理配置
  [HandleEvent.acceptance.desc]: {
    component: () => TermiOrAcceEvt, // 使用TermiOrAcceEvt组件
    props: (alarmInfo) => {
      return {
        title: HandleEvent.acceptance.desc,
        alarmInfo
      };
    },
    onok: (handleInstance) => {
      handleInstance.refreshInfoAndList(true);
    }
  },
  // 转派事件处理配置
  [HandleEvent.transfer.desc]: {
    component: () => AlarmDisposeDialog,
    props: (alarmInfo) => {
      return {
        title: HandleEvent.transfer.desc,
        alarmInfo
      };
    },
    onok: (handleInstance) => {
      handleInstance.refreshInfoAndList(true);
    }
  },
  // 误报事件处理配置
  [HandleEvent.falseAlarm.desc]: {
    component: () => FalseAlarm, // 使用FalseAlarm组件
    props: (alarmInfo) => {
      return {
        title: HandleEvent.falseAlarm.desc,
        alarmInfo
      };
    },
    onok: (handleInstance) => {
      handleInstance.closeInfoAndList(true); // 关闭信息和列表
    }
  },
  // 重复告警事件处理配置
  [HandleEvent.repeatAlarm.desc]: {
    component: () => AlarmRepeatDialog, // 使用AlarmRepeatDialog组件
    props: (alarmInfo) => {
      return {
        title: HandleEvent.repeatAlarm.desc,
        alarmInfo
      };
    },
    onok: (handleInstance) => {
      handleInstance.closeInfoAndList(true);
    }
  },
  // 调度事件处理配置
  [HandleEvent.scheduling.desc]: {
    component: (alarmInfo) => {
      const { order, records } = alarmInfo;
      const link = records.find(item => item.linkId === order.linkId);
      // 根据条件选择不同的组件
      if (link && link.linkName.indexOf('整治派发') > -1) {
        return DistributeAndCorrection;
      }
      return AlarmDisposeDialog;
    },
    props: (alarmInfo) => {
      return {
        title: HandleEvent.scheduling.desc,
        alarmInfo
      };
    },
    onok: (handleInstance) => {
      handleInstance.refreshInfoAndList(true);
    }
  },
  // 关闭事件处理配置
  [HandleEvent.close.desc]: {
    component: () => TermiOrAcceEvt,
    props: (alarmInfo) => {
      return {
        title: HandleEvent.close.desc,
        alarmInfo
      };
    },
    onok: (handleInstance) => {
      handleInstance.refreshInfoAndList(true);
    }
  },
  // 转领导批示事件处理配置
  [HandleEvent.leaderGuidance.desc]: {
    component: () => AlarmLeaderDialog, // 使用AlarmLeaderDialog组件
    props: (alarmInfo) => {
      return {
        title: HandleEvent.leaderGuidance.desc,
        alarmInfo
      };
    },
    onclose: (handleInstance) => {
      console.log('onclose', handleInstance); // 关闭时输出日志
    }
  },
  // 窗口配置
  winCfg: {
    idPrefix: 'evtProcessWinCfg' // 窗口ID前缀
  }
};
