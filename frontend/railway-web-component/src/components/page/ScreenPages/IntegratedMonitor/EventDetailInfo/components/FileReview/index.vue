<!--
 * @Description:
 * @Autor: he.chao
 * @Date: 2022-10-19 15:07:55
 * @LastEditors: liu.yongli
 * @LastEditTime: 2023-12-13 14:36:14
-->
<template>
  <div class="fileBox">
    <div class="file-imageAll-main-box" @mouseover="isIn = true" @mouseout="isIn = false">
      <div class="file-imageAll-box">
        <div
          v-for="(fileItem, fileIndex) in previewFiles"
          :key="`previewFiles-${fileIndex}`"
          class="file-image-box"
        >
          <img
            v-if="fileItem.resourceType === '1'"
            alt="资源"
            :src="fileItem.resourceUrl"
            class="file-image"
            @click="openImgViewer(previewFiles, fileIndex)"
          />
          <video v-else muted class="file-video" @click="openImgViewer(previewFiles, fileIndex)">
            <source :src="fileItem.resourceUrl" />
          </video>
        </div>
        <div
          v-for="(fileItem, fileIndex) in otherFiles"
          :key="`otherFiles-${fileIndex}`"
          class="file-image-box"
        >
          <el-link
            :c-tip="fileItem.fileName"
            @click="downloadHandle(fileItem.resourceUrl,fileItem.fileName)"
          >{{ fileItem.fileName }}</el-link>
        </div>
      </div>
    </div>
    <!-- 全屏查看视频图片 -->
    <FileFullScreen v-if="showFileFull" :fileFullList="fileFullList" @closeFull="closeFull"></FileFullScreen>
  </div>
</template>

<script>
// import { downloadFile } from '@/api/service/common'
import api from '@/api'
import FileFullScreen from '@/components/common/file-full-screen.vue'
import vue from '@/main'

export default {
  components: { FileFullScreen },
  props: {
    files: {
      type: Array,
      default: () => [],
    },
  },
  data: function () {
    return {
      isIn: false, //鼠标经过
      showFileFull: false, //是否显示全屏查看附件
      fileFullList: null, //全屏查看附件数据
    }
  },
  computed: {
    previewFiles() {
      let result = []
      result = this.files.filter(item => ['1', '2'].includes(item.resourceType))

      return result
    },
    otherFiles() {
      let result = []
      result = this.files.filter(
        item => !['1', '2'].includes(item.resourceType)
      )
      return result
    },
  },
  methods: {
    downloadHandle(resourceUrl, fileName) {
      api.downloadExcelPost(
        `${vue.$env.VUE_APP_REQ_PREFIX_PLAT}/system/downloadByUrl`,
        { fileName, url: resourceUrl }
      )
    },
    /**
     * 阻止右键点击事件的默认行为。
     * 该方法的目的是为了禁用右键菜单，以提供更安全的用户体验或防止右键菜单被用于不正当目的。
     * @param {Event} e - 右键点击事件对象。
     */
    disableRightClick(e) {
      e.preventDefault()
    },
    /**
     * 打开图像查看器。
     * 该方法用于启动一个全屏的图像查看器，显示一组图像或视频的缩略图。
     * @param {Array} item - 包含资源URL的数组。
     * @param {Number} index - 当前项目的索引，用于在查看器中导航。
     */
    openImgViewer(item, index) {
      let fileFullList = {}
      fileFullList.isVideo = true
      fileFullList.index = index
      fileFullList.videoImgUrl = []
      item.forEach(item2 => {
        fileFullList.videoImgUrl.push(item2.resourceUrl)
      })
      this.fileFullList = fileFullList
      this.showFileFull = true
    },
    /**
     * 关闭全屏预览。
     * 该方法用于关闭由openImgViewer方法打开的全屏图像或视频预览。
     */
    closeFull() {
      this.showFileFull = false
    },
  },
}
</script>

<style lang='less' src='./index.less' />
