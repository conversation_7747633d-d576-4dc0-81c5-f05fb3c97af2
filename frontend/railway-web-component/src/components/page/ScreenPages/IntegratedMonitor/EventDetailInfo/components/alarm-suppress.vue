<!-- 告警压制 -->
<template>
  <div class="alarm-suppress-box">
    <!-- 单选按钮组，用于选择告警压制类型 -->
    <el-radio-group v-model="suppressTypeValue" @change="changeSuppressType">
      <!-- 五分钟不告警选项 -->
      <el-radio :label="1">五分钟不告警</el-radio>
      <!-- 一小时不告警选项 -->
      <el-radio :label="2">一小时不告警</el-radio>
      <!-- 今日不告警选项 -->
      <el-radio :label="3">今日不告警</el-radio>
      <div class='radio-self'>
        <!-- 自定义告警压制选项 -->
        <el-radio :label='4'>自定义</el-radio>
        <!-- 天数选择框 -->
        <div class='sup-day-box'>
          <el-select
            class='c-select'
            popper-class='c-select-dropdown'
            v-model='dayValue'
            @change='changeSuppressDay'
            :disabled='isDayDisable'
            placeholder='请选择'
          >
            <!-- 天数选项 -->
            <el-option
              v-for='item in suppressSelect.day'
              :key='item.value'
              :label='item.label'
              :value='item.value'>
            </el-option>
          </el-select>
        </div>
        <!-- 小时选择框 -->
        <div class='sup-day-box'>
          <el-select
            class='c-select'
            popper-class='c-select-dropdown'
            v-model='hourValue'
            @change='changeSuppressHour'
            :disabled='isHourDisable'
            placeholder='请选择'
          >
            <!-- 小时选项 -->
            <el-option
              v-for='item in suppressSelect.hour'
              :key='item.value'
              :label='item.label'
              :value='item.value'>
            </el-option>
          </el-select>
        </div>
        <!-- 分钟选择框 -->
        <div class='sup-day-box'>
          <el-select
            class='c-select'
            popper-class='c-select-dropdown'
            v-model='minuteModel'
            :disabled='isMinDisable'
            placeholder='请选择'
          >
            <!-- 分钟选项 -->
            <el-option
              v-for='item in suppressSelect.minute'
              :key='item.value'
              :label='item.label'
              :value='item.value'>
            </el-option>
          </el-select>
        </div>
      </div>
    </el-radio-group>
  </div>
</template>
<script>
export default {
  data() {
    return {
      suppressTypeValue: 1, // 当前选中的告警压制类型
      isDayDisable: true, // 天数选择框是否禁用
      isHourDisable: true, // 小时选择框是否禁用
      isMinDisable: true, // 分钟选择框是否禁用
      dayValue: '0', // 选中的天数
      hourValue: '0', // 选中的小时数
      minuteModel: '0', // 选中的分钟数
     /* day 天数选项列表 */
     /* hour 小时选项列表 */
     /* minute 分钟选项列表 */
      suppressSelect: {
        day: [{
          value: '0',
          label: '0天'
        }, {
          value: '1',
          label: '1天'
        }, {
          value: '2',
          label: '2天'
        }, {
          value: '3',
          label: '3天'
        }, {
          value: '4',
          label: '4天'
        }, {
          value: '5',
          label: '5天'
        }, {
          value: '6',
          label: '6天'
        }, {
          value: '7',
          label: '7天'
        }, {
          value: '8',
          label: '8天'
        }, {
          value: '9',
          label: '9天'
        }, {
          value: '10',
          label: '10天'
        }, {
          value: '11',
          label: '11天'
        }, {
          value: '12',
          label: '12天'
        }, {
          value: '13',
          label: '13天'
        }, {
          value: '14',
          label: '14天'
        }, {
          value: '15',
          label: '15天'
        }, {
          value: '16',
          label: '16天'
        }, {
          value: '17',
          label: '17天'
        }, {
          value: '18',
          label: '18天'
        }, {
          value: '19',
          label: '19天'
        }, {
          value: '20',
          label: '20天'
        }, {
          value: '21',
          label: '21天'
        }, {
          value: '22',
          label: '22天'
        }, {
          value: '23',
          label: '23天'
        }, {
          value: '24',
          label: '24天'
        }, {
          value: '25',
          label: '25天'
        }, {
          value: '26',
          label: '26天'
        }, {
          value: '27',
          label: '27天'
        }, {
          value: '28',
          label: '28天'
        }, {
          value: '29',
          label: '29天'
        }, {
          value: '30',
          label: '30天'
        }],
        hour: [{
          value: '0',
          label: '0时'
        }, {
          value: '1',
          label: '01时'
        }, {
          value: '2',
          label: '02时'
        }, {
          value: '3',
          label: '03时'
        }, {
          value: '4',
          label: '04时'
        }, {
          value: '5',
          label: '05时'
        }, {
          value: '6',
          label: '06时'
        }, {
          value: '7',
          label: '07时'
        }, {
          value: '8',
          label: '08时'
        }, {
          value: '9',
          label: '09时'
        }, {
          value: '10',
          label: '10时'
        }, {
          value: '11',
          label: '11时'
        }, {
          value: '12',
          label: '12时'
        }, {
          value: '13',
          label: '13时'
        }, {
          value: '14',
          label: '14时'
        }, {
          value: '15',
          label: '15时'
        }, {
          value: '16',
          label: '16时'
        }, {
          value: '17',
          label: '17时'
        }, {
          value: '18',
          label: '18时'
        }, {
          value: '19',
          label: '19时'
        }, {
          value: '20',
          label: '20时'
        }, {
          value: '21',
          label: '21时'
        }, {
          value: '22',
          label: '22时'
        }, {
          value: '23',
          label: '23时'
        }, {
          value: '24',
          label: '24时'
        }],
        minute: [{
          value: '0',
          label: '0分'
        }, {
          value: '1',
          label: '01分'
        }, {
          value: '2',
          label: '02分'
        }, {
          value: '3',
          label: '03分'
        }, {
          value: '4',
          label: '04分'
        }, {
          value: '5',
          label: '05分'
        }, {
          value: '6',
          label: '06分'
        }, {
          value: '7',
          label: '07分'
        }, {
          value: '8',
          label: '08分'
        }, {
          value: '9',
          label: '09分'
        }, {
          value: '10',
          label: '10分'
        }, {
          value: '11',
          label: '11分'
        }, {
          value: '12',
          label: '12分'
        }, {
          value: '13',
          label: '13分'
        }, {
          value: '14',
          label: '14分'
        }, {
          value: '15',
          label: '15分'
        }, {
          value: '16',
          label: '16分'
        }, {
          value: '17',
          label: '17分'
        }, {
          value: '18',
          label: '18分'
        }, {
          value: '19',
          label: '19分'
        }, {
          value: '20',
          label: '20分'
        }, {
          value: '21',
          label: '21分'
        }, {
          value: '22',
          label: '22分'
        }, {
          value: '23',
          label: '23分'
        }, {
          value: '24',
          label: '24分'
        }, {
          value: '25',
          label: '25分'
        }, {
          value: '26',
          label: '26分'
        }, {
          value: '27',
          label: '27分'
        }, {
          value: '28',
          label: '28分'
        }, {
          value: '29',
          label: '29分'
        }, {
          value: '30',
          label: '30分'
        }, {
          value: '31',
          label: '31分'
        }, {
          value: '32',
          label: '32分'
        }, {
          value: '33',
          label: '33分'
        }, {
          value: '34',
          label: '34分'
        }, {
          value: '35',
          label: '35分'
        }, {
          value: '36',
          label: '36分'
        }, {
          value: '37',
          label: '37分'
        }, {
          value: '38',
          label: '38分'
        }, {
          value: '39',
          label: '39分'
        }, {
          value: '40',
          label: '40分'
        }, {
          value: '41',
          label: '41分'
        }, {
          value: '42',
          label: '42分'
        }, {
          value: '43',
          label: '43分'
        }, {
          value: '44',
          label: '44分'
        }, {
          value: '45',
          label: '45分'
        }, {
          value: '46',
          label: '46分'
        }, {
          value: '47',
          label: '47分'
        }, {
          value: '48',
          label: '48分'
        }, {
          value: '49',
          label: '49分'
        }, {
          value: '50',
          label: '50分'
        }, {
          value: '51',
          label: '51分'
        }, {
          value: '52',
          label: '52分'
        }, {
          value: '53',
          label: '53分'
        }, {
          value: '54',
          label: '54分'
        }, {
          value: '55',
          label: '55分'
        }, {
          value: '56',
          label: '56分'
        }, {
          value: '57',
          label: '57分'
        }, {
          value: '58',
          label: '58分'
        }, {
          value: '59',
          label: '59分'
        }, {
          value: '60',
          label: '60分'
        }],
      },
      suppressTime: 0, // 提交的压制时间，单位：分钟
    };
  },
  methods: {
    /**
     * 根据选定的告警压制类型，配置相应的压制时长和启用/禁用自定义时间选项。
     * 改变告警压制方式
     */
    changeSuppressType() {
      // 根据不同的告警压制类型，设置默认的压制时长和启用/禁用自定义时间选项
      switch (this.suppressTypeValue) {
        case 1: // 五分钟不告警
          this.suppressTime = 5;
          break;
        case 2: // 一小时不告警
          this.suppressTime = 60;
          break;
        case 3: // 今日不告警
          this.suppressTime = this.todaySuppressTime();
          break;
        case 4: // 自定义
          this.isDayDisable = false;
          this.isHourDisable = false;
          this.isMinDisable = false;
          break;
        default:
          return false;
      }
    },
    /**
     * 根据选定的天数，动态调整小时和分钟选项的可用性。
     * 当选择30天时，小时和分钟选项被禁用。
     * 改变天数
     */
    changeSuppressDay() {
      // 如果选择的是30天，则小时和分钟选项被禁用，否则启用
      if (this.dayValue === '30') {
        this.isHourDisable = true;
        this.hourValue = '0';
        this.isMinDisable = true;
        this.minuteModel = '0';
      } else {
        this.isHourDisable = false;
        this.isMinDisable = false;
      }
    },
    /**
     * 根据选定的小时数，动态调整分钟选项的可用性。
     * 当选择29天24小时时，分钟选项被禁用。
     * 改变小时
     */
    changeSuppressHour() {
      // 如果选择的是29天24小时，则分钟选项被禁用，否则启用
      if (this.dayValue === '29' && this.hourValue === '24') {
        this.isMinDisable = true;
        this.minuteModel = '0';
      } else {
        this.isHourDisable = false;
        this.isMinDisable = false;
      }
    },
    /**
     * 计算今日不告警的压制时长，以分钟为单位。
     * 今日不告警 时间计算
     * @returns {number} 今日剩余时间的分钟数。
     */
    todaySuppressTime() {
      // 获取当前日期和时间，计算到今日结束的总分钟数
      let date = new Date();
      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      let strDate = date.getDate();
      let minute = date.getMinutes();
      let hour = date.getHours();
      let second = date.getSeconds();
      let endTime = year + '-' + month + '-' + strDate + ' 23:59:59';
      let startTime = year + '-' + month + '-' + strDate + ' ' + hour + ':' + minute + ':' + second;
      const dateOne = new Date(startTime);
      const dateTwo = new Date(endTime);
      return (dateTwo.getTime() - dateOne.getTime()) / 1000 / 60; // 返回分钟数
    }
  }
}
</script>
<style scoped lang="less">
  .alarm-suppress-box {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    /deep/ .el-radio-group {
      display: flex;
      flex-wrap: wrap;
    }
    /deep/ .radio-self {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      .el-radio {
        margin-right: 0;
      }
    }
    /deep/ .el-radio {
      margin: 11px 6px 11px 0;
      .el-radio__input {
        .el-radio__inner {
          border: 0;
          background: rgba(25, 137, 250, 0.3);
          box-shadow: inset 0px 0px 3px 0px rgba(25, 137, 250, 0.6);
        }
        .is-checked {
          &:after {
            transform: translate(-50%, -50%) scale(1.3);
          }
        }
      }
      .el-radio__label {
        padding-left: 6px;
        font-size: 14px;
        font-weight: 400;
        color: #FFFFFF;
      }
    }
    .sup-day-box {
      width: 78px;
      height: 32px;
      margin: 0 3px;
      /deep/ .el-input.is-disabled .el-input__inner {
        background: rgba(71, 94, 128, 0.5);
        border-radius: 4px;
        border: 0px;
        color: #fff;
      }
      /deep/ .el-input__inner {
        background: rgba(71, 94, 128, 0.5);
        border-radius: 4px;
        border-color: #409EFF;
        color: #fff;
      }
    }
  }
</style>
