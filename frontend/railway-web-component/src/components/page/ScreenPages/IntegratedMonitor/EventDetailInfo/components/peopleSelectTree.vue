<!--告警处理页面的处理人树-->
<template>
  <div class="people-select-tree-box">
    <!-- 使用el-select组件创建一个可选择的下拉框 -->
    <el-select
      ref="peopleSelect"
      class="c-select"
      popper-class="c-select-dropdown"
      v-model="dispatchNameValue"
      placeholder="请选择人员"
      collapse-tags
      :popper-append-to-body="false"
      multiple
      filterable
      :filter-method="selectTreeFilter"
      @change="selectDispatchChange"
    >
      <!-- 定义下拉选项，使用el-option组件 -->
      <el-option :value="dispatchObject" style="height: auto;" class="people-option-tree-box">
        <div class="people-option-tree-div">
          <!-- 使用el-tree组件展示处理人树 -->
          <el-tree
            :data="dispatchTree"
            show-checkbox
            node-key="id"
            ref="dispatchTree"
            accordion
            default-expand-all
            highlight-current
            :default-checked-keys="treeKey"
            :props="defaultProps"
            icon-class="el-icon-arrow-down"
            :filter-node-method="filterNode"
            @check-change="dispatchCheckChange"
          >
            <!-- 自定义树节点的显示内容 -->
            <div class="custom-tree-node" slot-scope="{ node, data }">
              <div>
                <div class="custom-tree-node-label">
                  <!-- 如果节点有userId，则显示对应的图片 -->
                  <img
                    alt="人员"
                    v-if="data.userId"
                    class="people-tree-img"
                    src="@/assets/images/alarmEvent/alarm/treePersion.png"
                  />
                  <!-- 显示节点标签 -->
                  <div
                    :c-tip="data.label"
                    c-tip-placement="top"
                    class="node-label"
                    :style="{ 'max-width': !data.userId ? '250px' : '210px'}"
                  >{{ data.label }}</div>
                  <div>
                    <!-- 如果用户有处理权限和查看权限，则显示提示图标 -->
                    <div
                      c-tip="该用户拥有告警的处理权限和查看权限"
                      c-tip-placement="top"
                      class="text-source"
                      v-if="data.userId && data.purviewFlag === '1'"
                      style="margin: 3px 0px"
                    >?</div>
                  </div>
                </div>
              </div>
            </div>
          </el-tree>
        </div>
      </el-option>
    </el-select>
  </div>
</template>
<script>
import api from '@/api'
export default {
  name: 'tree-select',
  components: {},
  props: {
    alarmInfo: {
      //事件信息
      type: Object,
      default: {},
    },
    getFlowUserParams: {
      //查询处理人时的入参
      type: Object,
      default: {},
    },
    showAllTreeBtn: {
      //是否显示<显示全部处理人>按钮
      type: Boolean,
      default: false,
    },
    dataFilterPermiss: {
      //开启过滤权限 0否 1是
      type: String,
      default: '',
    },
  },
  watch: {
    getFlowUserParams: {
      handler() {
        // 当getFlowUserParams变化时，调用getTenantTree方法
        this.getTenantTree(
          this.getFlowUserParams.item,
          this.getFlowUserParams.recordsInfo
        )
      },
      deep: true,
      // immediate: true,//初始化时执行
    },
  },
  data() {
    return {
      isShowTooltip: false,
      defaultProps: {
        children: 'children',
        label: 'label',
      },
      dispatchTree: [], //页面要显示的处理人树
      allPowerTreeData: {}, //全部处理人数据
      isAllTree: false, //是否显示全部处理人
      treeKey: [], //默认选中的节点
      dispatchNameValue: '',
      dispatchObject: [],
      processDTOList: [], //人员树入参；
    }
  },
  methods: {
    /**
     * 根据当前报警信息和流程ID获取处理人员树结构数据。
     * @param {Object} nextLink 流程导航对象，包含流程ID、链接ID等信息。
     * @param {Array} recordsInfo 历史记录信息，用于回退到之前的处理人。
     * @returns {Boolean} 如果nextLink值为6，则返回false，停止查询；否则继续查询并处理返回的树结构数据。
     */
    getTenantTree(nextLink, recordsInfo) {
      // 构建查询参数，包含租户ID、省份ID、城市ID、县城ID、设备码、警源等信息
      let params = {
        tenantId: this.formatNullDataToString(this.alarmInfo.order.tenantId),
        provinceId: this.formatNullDataToString(
          this.alarmInfo.order.provinceId
        ),
        cityId: this.formatNullDataToString(this.alarmInfo.order.cityId),
        countyId: this.formatNullDataToString(this.alarmInfo.order.countyId),
        deviceCode: this.formatNullDataToString(
          this.alarmInfo.order.deviceCode
        ),
        warningSource: this.formatNullDataToString(
          this.alarmInfo.order.warningSource
        ),
        flowId: nextLink.flowId,
        curLink: nextLink.linkId,
        warningOrderId: nextLink.orderId
          ? nextLink.orderId
          : nextLink.warningOrderId,
      }
      // 如果nextLink的值为6，则直接返回false，不进行查询
      if (String(this.nextLink) === '6') {
        return false
      }
      // 调用API进行查询处理人员树结构
      api
        .post(
          `${this.$env.VUE_APP_REQ_PREFIX_PLAT}/event/getFlowByIdUserTreeNew`,
          params
        )
        .then(res => {
          // 如果响应码不为200，提示查询失败
          if (res.code != 200) {
            this.$message({
              type: 'error',
              message: res.msg || '处理人员树查询失败！',
            })
            return
          }
          // 处理返回的树结构数据，并赋值给相关变量
          let treeData = []
          treeData.push(res.data.sysPowerInfo)
          this.formatPeopleTree(treeData)
          this.dispatchTree = treeData //赋值有权限的处理人
          this.dispatchTree[0].isFirst = true
          this.allPowerTreeData = res.data //备份全部的处理人
          // 根据是否有历史记录，选择默认选中项或回退到上次提交的人员
          if (recordsInfo.length < 1) {
            // 默认选中树
            this.$nextTick(() => {
              this.treeKey = Object.keys(
                this.$refs?.dispatchTree?.store?.nodesMap || []
              )
              this.dispatchCheckChange()
            })
            this.$emit('dataLoading') //通知父级停止加载等待
            return
          } //回退流程设置选中上次提交人员
          let id
          for (const item2 of recordsInfo) {
            if (String(item2.linkId) === String(nextLink.linkId)) {
              id = item2.processId
            }
          }
          this.$nextTick(() => {
            this.treeKey = [id]
            this.dispatchCheckChange()
          })
          this.$emit('dataLoading') //通知父级停止加载等待
        })
    },
    /**
     * 将空数据转换为空字符串
     * @param {any} str 待处理的数据
     * @returns {string} 处理后的字符串
     */
    formatNullDataToString(str) {
      if (str) {
        return str
      } else {
        return ''
      }
    },
    /**
     * 处置人员树结点过滤方法
     * @param {string} text 过滤文本
     */
    selectTreeFilter(text) {
      this.$refs.dispatchTree.filter(text)
    },
    /**
     * 树结构数据节点过滤函数
     * @param {string} value 过滤值
     * @param {Object} data 节点数据
     * @returns {boolean} 过滤结果
     */
    filterNode(value, data) {
      if (!value) {
        return true
      }
      return data.label.indexOf(value) !== -1
    },
    /**
     * 处理人员选择变化事件
     * @param {Array} e 选中的节点数组
     */
    selectDispatchChange(e) {
      let arrNew = []
      const dataLength = this.dispatchObject.length
      const eLength = e.length
      for (let i = 0; i < dataLength; i++) {
        for (let j = 0; j < eLength; j++) {
          if (e[j] === this.dispatchObject[i].label) {
            arrNew.push(this.dispatchObject[i])
          }
        }
      }
      this.$refs.dispatchTree.setCheckedNodes(arrNew) //设置勾选的值
    },
    /**
     * 处理选中节点变化事件
     */
    dispatchCheckChange() {
      // 处理选中数据，并通过事件传递给父组件
      let res = this.$refs.dispatchTree.getCheckedNodes(true, true) //这里两个true，1. 是否只是叶子节点 2. 是否包含半选节点（就是使得选择的时候不包含父节点）
      let arrLabel = []
      let arr = []
      this.processDTOList = []
      res.forEach(item => {
        let a = {}
        a.processId = item.id
        a.processName = item.label
        this.processDTOList.push(a)
        arrLabel.push(item.label)
        arr.push(item)
      })
      this.dispatchObject = arr
      this.dispatchNameValue = arrLabel
      // 设置选中状态
      this.formatPeopleTree(this.dispatchTree)
      let allRes = this.$refs.dispatchTree.getCheckedNodes()
      setTimeout(() => {
        allRes.forEach(item => {
          item.checked = true
        })
      }, 200)
      // 回调选中数据
      let param = {
        processDTOList: this.processDTOList,
        dispatchNameValue: this.dispatchNameValue,
      }
      this.$emit('dispatchCheckChange', param)
    },
    /**
     * 格式化人员树结构数据
     * @param {Array} tree 待格式化的树结构数据
     */
    formatPeopleTree(tree) {
      for (let i = 0; i < tree.length; i++) {
        tree[i].checked = false
        tree[i].id = tree[i].nodeId ? tree[i].nodeId : tree[i].userId
        tree[i].label = tree[i].userName ? tree[i].userName : tree[i].nodeName
        if (tree[i].children && tree[i].children.length > 0) {
          this.formatPeopleTree(tree[i].children)
        }
      }
    },
  },
}
</script>
<style scoped lang='less'>
//下拉框的input
.people-select-tree-box {
  width: 100%;
  /deep/ .el-scrollbar {
    width: 247px;
  }
  /deep/ .el-select__tags {
    .el-select__input {
      color: #ffffff;
    }
    .el-tag.el-tag--info {
      color: rgba(25, 137, 250, 1);
      background: rgba(25, 137, 250, 0.2);
      line-height: 20px;
      border: 0;
      .el-tag__close {
        color: #1989fa;
        background: transparent;
        transform: scale(0.7);
        border: 0;
      }
    }
  }
  /deep/ .el-input .el-input__suffix .el-input__suffix-inner .el-input__icon {
    transform: unset !important;
    &:before {
      content: '\e72c';
      font-family: iconfont;
    }
  }
}

/deep/ .c-select-dropdown .el-select-dropdown__list {
  max-height: 180px;
  padding: 0;
}

/deep/ .c-select-dropdown .el-select-dropdown__list .people-option-tree-box {
  padding: unset;
  &:hover {
    background: transparent !important;
  }
  .people-option-tree-div {
    .el-tree {
      min-height: 32px;
      background: transparent;
      .el-tree-node__content {
        background: transparent;
        .el-checkbox {
          .el-checkbox__input {
            .el-checkbox__inner {
              background: transparent;
              border: 1px solid #305d92;
            }
            &.is-checked {
              .el-checkbox__inner {
                background: #4f9fff;
              }
            }
          }
        }
        .el-icon-arrow-down {
          &:before {
            content: '\e732';
            font-family: 'iconfont' !important;
            color: #ffffff;
            font-size: 14px;
          }
        }
        .el-icon-arrow-down.is-leaf {
          opacity: 0;
          margin-left: -18px;
        }
      }
      .el-tree-node__content:hover {
        background: rgba(25, 137, 250, 0.4);
      }
      .custom-tree-node {
        font-size: 14px;
        overflow: hidden;
        height: 100%;
        line-height: 26px;
        text-overflow: ellipsis;
        white-space: normal;
        color: #ffffff;
        .custom-tree-node-label {
          display: flex;
          width: 100%;
          align-items: center;
          .people-tree-img {
            margin-left: 2px;
          }
          .node-label {
            color: #ffffff;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            margin: 0 4px;
          }
        }
      }
      //无数据
      .el-tree__empty-block {
        min-height: 180px;
        .el-tree__empty-text {
          display: flex;
          flex-direction: column;
          color: #ffffff;
          //   &:before {
          //     content: url("~@/assets/image/monitorWarnNew/alarmList/noData-ly.png");
          //     width: 68px;
          //     height: 68px;
          //   }
        }
      }
    }
  }
}
.tooltip-content {
  color: #ffffff;
}
.showAll {
  width: 100%;
  height: 32px;
  line-height: 26px;
  font-weight: 400;
  font-size: 14px;
  text-align: center;
  cursor: pointer;
  pointer-events: auto;
  background: rgba(2, 137, 109, 0.2);
  box-shadow: inset 0px 0px 3px 0px rgba(2, 137, 109, 0.6);
  border-radius: 0px 0px 8px 8px;
  color: #ffffff;
}
.text-source {
  float: left;
  margin-left: 3px;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  width: 18px;
  height: 18px;
  //   background-image: url("~@/assets/image/monitorWarnNew/alarmList/hint-ly.png") ;
}
</style>
