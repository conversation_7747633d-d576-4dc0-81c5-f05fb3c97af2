/**
 * 设置输入类型和结束链接ID的常量。
 */
import dayjs from 'dayjs';

export const INPUT_TYPE = {
  file: 'file',
  noneLabel: 'noneLabel'
};
export const EndLinkId = ['0', '7', '6'];

/**
 * 定义环节类型的常量对象，包含各种环节类型的描述和值。
 */
const RealLinkTypeConst = {
  // -1开始上报
  '-1': {
    desc: '开始上报',
    value: '-1'
  },
  // 调度是 2
  '2': {
    desc: '调度',
    value: '2'
  },
  // 协商核实是 3
  '3': {
    desc: '协商核实',
    value: '3'
  },
  // 核实是 5
  '5': {
    desc: '核实',
    value: '5'
  },
  // 研判是 1
  '1': {
    desc: '研判',
    value: '1'
  },
  // 处置是 4
  '4': {
    desc: '处置',
    value: '4'
  }
};

/**
 * 处置效能的对象，包含超时标记的配置。
 */
const handleEffect = {
  overTimeFlag: {
    switch: true,
    show: (detail, record) => {
      // 节点是否超时
      return checkOverTime(record, detail);
    },
    inputType: INPUT_TYPE.noneLabel,
    getHtml: (detail, record) => {
      // 超时显示
      return `<div class='form-item-overtime' >
                <i class='el-icon-time'></i>
                超时:  ${getOverTimeStr(record, detail)}
              </div>`;
    }
  }
};

/**
 * 配置处置流程的表单，根据不同的环节模式(linkMode)提供不同的验证和表单字段格式化函数。
 */
// 处置流程表单配置
export default {
  linkMode3: {
    validate: (detail, record) => {
      return EndLinkId.includes(record.linkId);
    },
    linkNameFormat: (detail, record) => {
      const {
        records,
      } = detail;
      if (records[1] && records[1].nextLinkName) {
        return records[1].nextLinkName;
      }
      return '已完结';
    },
    form: {
      processName: {
        label: '操作人'
      },
      processOrg: {
        label: '操作人组织',
        format: (detail, record) => {
          return getProcessOrg(detail, record.processId);
        }
      },
      realLinkType: {
        label: '环节类型',
        format: (detail, record) => {
          return RealLinkTypeConst[record.realLinkType]?.desc || '-';
        }
      }
    }
  },
  linkMode2: {
    validate: (detail, record) => {
      return /['整治派发']+/gm.test(record.linkName);
    },
    form: {
      processName: {
        label: '操作人'
      },
      processOrg: {
        label: '操作人组织',
        format: (detail, record) => {
          return getProcessOrg(detail, record.processId);
        }
      },
      linkTimeLimit: {
        label: '处理时限',
        switch: true,
        format: (detail, record) => {
          return getLinkTimeLimit(record.linkId, detail);
        }
      },
      handleTime: {
        label: '办理时长',
        switch: true,
        format: (detail, record) => {
          // 节点办理时长
          return flowDealTime(record);
        }
      },
      ...handleEffect,
      realLinkType: {
        label: '环节类型',
        format: (detail, record) => {
          return RealLinkTypeConst[record.realLinkType].desc || '-';
        }
      },
      personInChargeOrg: {
        label: '责任单位'
      },
      personInCharge: {
        label: '责任人'
      },
      correctionTime: {
        label: '整改截止日期'
      },
      nextLinkName: {
        label: '下一环节'
      },
      scheme: {
        label: '整改方案'
      },
      orderFileList: {
        inputType: 'file'
      },
      sectionChief: {
        label: '段长'
      }
    }
  },
  linkMode1: {
    validate: (detail, record) => {
      return true;
    },
    form: {
      processName: {
        label: '操作人'
      },
      processOrg: {
        label: '操作人组织',
        format: (detail, record) => {
          return getProcessOrg(detail, record.processId);
        }
      },
      ...handleEffect,
      realLinkType: {
        label: '环节类型',
        format: (detail, record) => {
          return RealLinkTypeConst[record.realLinkType].desc || '-';
        }
      },
      nextLinkName: {
        label: '下一环节'
      },
      nextLinkProcessName: {
        label: '处理人'
      },
      remark: {
        label: '处置说明'
      },
      linkTimeLimit: {
        label: '处理时限',
        switch: true,
        format: (detail, record) => {
          return getLinkTimeLimit(record.linkId, detail);
        }
      },
      handleTime: {
        label: '办理时长',
        switch: true,
        format: (detail, record) => {
          // 节点办理时长
          return flowDealTime(record);
        }
      },
      orderFileList: {
        inputType: 'file',
        label: '附件'
      }
    }
  },
};

/**
 * 根据用户ID从用户详情中查找并返回用户所在组织的名称。
 * @param {Object} detail - 用户详情。
 * @param {string} userId - 用户ID。
 * @returns {string} 用户所在组织的名称或'-'。
 */
const getProcessOrg = (detail, userId) => {
  if (!userId || !detail.users || detail.users.length <= 0) {
    return '-';
  }
  for (let i = 0, count = detail.users.length; i < count; i += 1) {
    let item = detail.users[i];
    if (item.userId === userId) {
      return item.dept?.deptName;
    }
  }
  return '-';
};

/**
 * 将处置时限从分钟转换为天、小时和分钟的字符串格式。
 * @param {number} timeLimit - 处置时限，单位为分钟。
 * @returns {string} 转换后的字符串，格式为'天小时分钟'。
 */
// 环节处置时限从分钟转换为天小时分钟
const transformTimeLimit = (timeLimit) => {
  let rsStr = '';
  if (timeLimit) {
    const secondsInDay = 86400;
    const secondsInHour = 3600;

    // 计算天数
    const days = Math.floor(timeLimit / secondsInDay);
    // 计算剩余秒数转换为小时
    const remainingSecondsAfterDays = timeLimit % secondsInDay;
    const hours = Math.floor(remainingSecondsAfterDays / secondsInHour);
    // 计算剩余秒数转换为分钟
    const minutes = Math.floor(
      (remainingSecondsAfterDays % secondsInHour) / 60);
    return `${days}天${hours}小时${minutes}分钟`;
  }

  return rsStr;
};

/**
 * 根据链接ID和警情信息获取环节的处置时限。
 * @param {string} linkId - 环节ID。
 * @param {Object} alarmInfo - 警情信息。
 * @returns {string} 环节处置时限，转换为天、小时和分钟的字符串格式。
 */
// 环节处置时限，需要根据item.linkId匹配flows里linkId,取出流程步骤时限值
const getLinkTimeLimit = (linkId, alarmInfo) => {
  let timeLimit = 0;
  if (alarmInfo.flows && alarmInfo.flows.length > 0) {
    alarmInfo.flows.forEach(item => {
      if (item.linkId == linkId) {
        timeLimit = item.timeLimit;
      }
    });
  }
  return transformTimeLimit(timeLimit);
};

/**
 * 计算流程节点的办理时长。
 * @param {Object} item - 流程节点信息。
 * @returns {string} 办理时长，格式为'天小时分钟'，如果无法计算则返回'-'。
 */
// 流程节点办理时长
const flowDealTime = (item) => {
  if (item.flowEndTime && item.flowNodeTime) {
    return transformTimeLimit(
      dayjs(item.flowEndTime).diff(dayjs(item.flowNodeTime), 'minute'));
  } else {
    return '-';
  }
};

/**
 * 检查给定环节是否超时。
 * @param {Object} item - 环节信息。
 * @param {Object} alarmInfo - 警情信息。
 * @returns {boolean} 如果环节超时则返回true，否则返回false。
 */
// 检查是否超时
const checkOverTime = (item, alarmInfo) => {
  let flowuseTime = 0;
  // 如果节点有结束时间和开始时间
  if (item.flowEndTime && item.flowNodeTime) {
    flowuseTime = dayjs(item.flowEndTime)
      .diff(dayjs(item.flowNodeTime), 'minute');
  } else if (item.flowNodeTime) {
    // 如果节点有开始时间，没有结束时间，则计算当前时间与开始时间差
    flowuseTime = dayjs().diff(dayjs(item.flowNodeTime), 'minute');
  } else {
    // 流程节点开始和结束都为空，脏数据
    flowuseTime = -1;
  }

  // 获取环节要求时限
  let timeLimit = 0;
  if (alarmInfo.flows && alarmInfo.flows.length > 0) {
    alarmInfo.flows.forEach(flowItem => {
      if (flowItem.linkId == item.linkId) {
        timeLimit = flowItem.timeLimit;
      }
    });
  }

  if (flowuseTime > 0 && timeLimit > 0 && flowuseTime > timeLimit) {
    return true;
  } else {
    return false;
  }

};

/**
 * 计算超时的时间字符串。
 * @param {Object} item - 包含流程节点信息的对象。
 * @param {Object} alarmInfo - 包含警情信息的对象。
 * @returns {string} 超时的时间字符串，如果无法计算则返回'-'。
 */
const getOverTimeStr = (item, alarmInfo) => {
  // 初始化流程使用时间为0
  let flowuseTime = 0;

  // 如果节点有结束时间和开始时间
  if (item.flowEndTime && item.flowNodeTime) {
    // 计算节点结束时间和开始时间之间的秒数差
    flowuseTime = dayjs(item.flowEndTime)
      .diff(dayjs(item.flowNodeTime), 'seconds');
  } else if (item.flowNodeTime) {
    // 如果只有节点开始时间，计算当前时间和开始时间之间的秒数差
    flowuseTime = dayjs().diff(dayjs(item.flowNodeTime), 'seconds');
  } else {
    // 如果没有开始时间或结束时间，返回'-'
    return '-';
  }

  // 初始化环节时限为0
  let timeLimit = 0;

  // 如果alarmInfo中包含流程信息
  if (alarmInfo.flows && alarmInfo.flows.length > 0) {
    // 遍历流程信息，寻找与当前节点linkId匹配的项
    alarmInfo.flows.forEach(flowItem => {
      if (flowItem.linkId === item.linkId) {
        // 如果找到匹配项，设置环节时限
        timeLimit = flowItem.timeLimit;
      }
    });
  }

  // 减去环节时限，得到实际超时时间
  flowuseTime = flowuseTime - timeLimit;

  // 调用transformTimeLimit函数转换时间，并返回结果
  return transformTimeLimit(flowuseTime);
};

