@import "../../common";

.evt-handle-wrap {
  width: 376px;
  padding: 0 12px;
  border-radius: 8px;
  box-sizing: border-box;
  &.ctl-panel {
    height: auto;
  }

  .ctl-content {
    height: auto;
    max-height: 370px;
    padding-bottom: 12px;
    overflow: hidden;
    overflow-y: scroll;
  }

  .evt-handle-form {
    margin: 12px 0;
    .el-form-item__content {
      width: calc(100% - 79px);
    }
    .el-select {
      width: 100%;
      .el-select__tags .el-tag {
        background-color: rgba(25, 137, 250, 0.2);
        color: #1989fa;
        border: none;
        .el-select__tags-text {
          max-width: 135px;
        }
      }
      .el-select__tags .el-tag.el-tag--info .el-tag__close.el-icon-close {
        color: #1989fa;
        background-color: transparent;
        &::before {
          color: #1989fa;
        }
      }
    }
    .el-select-dropdown {
      box-shadow: -3px 2px 4px #000000bd;
    }
    .el-input.is-disabled .el-input__inner {
      background: @input-inner;
      border: none;
    }
    .evt-handle-form-btn .el-form-item__content {
      display: flex;
      align-items: center;
      justify-content: flex-start;
    }
  }
}

.alarm-dispatch-outter {
  .dispatch-con {
    position: relative;

    .dispose-item-box {
      width: 352px;
      display: flex;
      align-items: flex-start;
      justify-content: center;
      margin: 6px 0;

      .dispose-item-left {
        width: calc(100% - 255px);
        height: 32px;
        margin-right: 12px;
        display: flex;
        justify-content: flex-end;

        .dispose-xinghao {
          font-size: 14px;
          font-weight: 400;
          color: #ed5158;
          height: 32px;
          line-height: 32px;
          margin-right: 2px;
        }

        .is-chuzhi {
          margin-right: 20px;
        }

        .dispose-item-label {
          height: 32px;
          font-size: 14px;
          font-weight: 400;
          color: #ffffff;
          line-height: 32px;
          text-align: right;
          margin-right: 10px;
        }

        .dispose-tip {
          display: flex;
          align-content: center;
          flex-wrap: wrap;
          margin-left: -21px;

          .text-source {
            cursor: pointer;
            float: left;
            margin: 0;
            background-size: 100% 100%;
            background-repeat: no-repeat;
            width: 14px;
            height: 14px;
            color: #fefefe;
            background-image: url("~@/assets/images/alarmEvent/weather/zhushi_icon.svg");
          }
        }
      }

      .dispose-item-right {
        width: 265px;
        display: flex;

        .dispose-upload {
          .image-video {
            .file-content {
              width: 70px;
              height: 70px;
              margin: 0 5px 6px 0;
              display: flex;

              .file-upload {
                .el-upload {
                  border: 1px dashed rgba(151, 151, 151, 1);
                  border-radius: 8px;
                  background: transparent;

                  .el-icon-plus {
                    &:before {
                      content: url("~@/assets/images/alarmEvent/alarm/shangchuan_icon.svg") !important;
                      transform: scale(0.7); /* 将图片缩小为原来的 50% */
                      transform-origin: center center; /* 设置缩小的原点为图片左上角 */
                    }
                  }
                }
              }
            }
          }
        }

        .el-select {
          width: 100%;
          display: flex;
          height: auto;
          margin: 0 !important;
        }

        .el-input {
          .el-input__inner {
            height: auto !important;
            &:focus {
              border: 1px solid @focus-color;
            }
          }
        }

        .el-input__inner,
        .el-textarea__inner {
          background: @input-inner;
          border-radius: 4px;
          border: 0px;
          color: #ffffffbd;
        }
      }

      .dispose-item-left-suppress {
        width: 100%;
        display: flex;

        .dispose-item-label {
          height: 32px;
          font-size: 14px;
          font-weight: 400;
          color: #ffffff;
          line-height: 32px;
        }

        .dispose-tip {
          display: flex;
          align-content: center;
          flex-wrap: wrap;

          .text-source {
            cursor: pointer;
            float: left;
            margin: 0 3px 0 3px;
            background-size: 100% 100%;
            background-repeat: no-repeat;
            width: 14px;
            height: 14px;
            background-image: url("~@/assets/images/alarmEvent/weather/zhushi_icon.svg");
          }
        }

        .suppress-switch-box {
          display: flex;
          flex-wrap: wrap;
          align-content: center;
        }
      }

      .dispose-item-right-suppress {
        margin-left: calc(100% - 320px);
        width: 320px;
      }
    }

    .text-tip {
      width: 352px;
      height: 58px;
      font-size: 14px;
      font-weight: 400;
      color: #ffffff;
      line-height: 70px;
      text-align: center;
    }
  }

  .dispatch-buttons {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;

    .dispatch-button {
      width: 80px;
      height: 32px;
      // background-image: linear-gradient(90deg, #2FB1EC 8%, #155BD4 100%);
      background: #2f4360;
      border-radius: 4px;
      line-height: 32px;
      text-align: center;
      font-size: 12px;
      cursor: pointer;
      &:not(:first-child) {
        margin-left: 12px;
      }
    }

    .dispatch-button-blue {
      background-image: linear-gradient(90deg, #2fb1ec 8%, #155bd4 100%);
      background-image: linear-gradient(90deg, #2fb1ec 8%, #155bd4 100%);
    }

    .dispatch-button-red {
      background: #612d2d;
      color: #ff7272;
    }
  }

  /deep/ .el-loading-mask {
    background-color: rgb(0 0 0 / 47%);
  }
}

.evt-handle-wrap&.false-alarm-wrap {
  .evt-handle-form {
    min-height: 100px;
  }
}
.evt-handle-wrap&.terminate-evt {
  .evt-handle-form {
    min-height: 100px;
  }
}
