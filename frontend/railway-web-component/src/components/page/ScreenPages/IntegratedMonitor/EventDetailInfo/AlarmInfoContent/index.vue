<!--
 * @Description  : 事件详情内容
 * <AUTHOR> wnj
 * @Date         : 2023-12-11 09:27:13
 * @LastEditors  : wnj
 * @LastEditTime : 2023-12-11 09:48:36
 * @FilePath     :  / src / components / page / alarmEvent / components / alarmRight / alarmInfoContent.vue
-->
<!-- 监测预警告警详情:告警来源 事件信息 各环节信息-->
<template>
  <div class="alarm-info-content-box" v-if="alarmInfo" @mousedown="stopDrag">
    <!--  事件信息  -->
    <div class="alarm-info-title-box" @click="showWhat('2')">
      <div>事件信息</div>
      <div class="iconWrap">
        <i :class="showWhatArr.includes('2') ? 'el-icon-caret-top' : 'el-icon-caret-bottom'"></i>
      </div>
    </div>
    <div v-if="showWhatArr.includes('2')" class="source-content-box">
      <el-row>
        <el-col :span="7" class="infoDataTitle">事件标题：</el-col>
        <el-col :span="17" class="infoDataContent">{{ alarmInfo.title }}</el-col>
      </el-row>
      <el-row>
        <el-col :span="7" class="infoDataTitle">事件类型：</el-col>
        <el-col
          :span="17"
          class="infoDataContentLong"
        >{{['其它','其他'].includes(alarmInfo.eventTypeName)?alarmInfo.order?.warningTypeName:alarmInfo.eventTypeName }}</el-col>
      </el-row>
      <el-row>
        <el-col :span="7" class="infoDataTitle">事件等级：</el-col>
        <!-- congestLevel -->
        <el-col
          :span="17"
          :class="`infoDataContentLong level-tag${getCongestLevel(alarmInfo.emergencyLevelName)}`"
        >{{ alarmInfo.emergencyLevelName }}</el-col>
      </el-row>
      <el-row>
        <el-col :span="7" class="infoDataTitle">事发时间：</el-col>
        <el-col :span="17" class="infoDataContent">{{ getOccTime(alarmInfo.occurTime) }}</el-col>
      </el-row>
      <!-- 已消散 -->
      <el-row v-if="alarmInfo.fusionStatus === '3'">
        <el-col :span="7" class="infoDataTitle">消散时间：</el-col>
        <el-col :span="17" class="infoDataContent">{{ getOccTime(alarmInfo.endTime) }}</el-col>
      </el-row>
      <!-- <el-row>
        <el-col :span="7" class="infoDataTitle">结束时间：</el-col>
        <el-col :span="17" class="infoDataContent">{{ getOccTime(alarmInfo.endTime) }}</el-col>
      </el-row>-->
      <el-row>
        <el-col :span="7" class="infoDataTitle">事发地点：</el-col>
        <el-col
          :span="17"
          class="infoDataContentLong"
          style="white-space:normal"
        >{{ alarmInfo.eventAddress }}</el-col>
      </el-row>
      <el-row>
        <el-col :span="7" class="infoDataTitle">事件描述：</el-col>
        <el-col
          :span="17"
          class="infoDataContentLong"
          style="white-space:normal"
        >{{ alarmInfo.description }}</el-col>
      </el-row>
      <el-row>
        <el-col :span="7" class="infoDataTitle">事件状态：</el-col>
        <el-col
          :span="5"
          class="infoDataContent"
          :style="{color:getFusionStatus(alarmInfo.fusionStatus).dictLabel=== '未消散'?'#fb913c':'#15bd94'}"
        >{{ getFusionStatus(alarmInfo.fusionStatus).dictLabel }}</el-col>
      </el-row>
      <el-row>
        <el-col :span="7" class="infoDataTitle">处置状态：</el-col>
        <el-col :span="5" class="infoDataContent" style="color:#4f9fff">
          <!-- alarmInfo.order.orderStatus ? getDealState(alarmInfo.order.orderStatus):'' -->
          {{ alarmInfo.order.orderStatusName }}
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="7" class="infoDataTitle">处置环节：</el-col>
        <el-col :span="17" class="infoDataContent">{{ handleInfo }}</el-col>
      </el-row>
      <el-row>
        <el-col :span="11" class="infoDataTitle">是否在铁路地界内：</el-col>
        <el-col
          :span="13"
          class="infoDataContentLong"
        >{{ eventInfo && eventInfo.isInner == 'Y' ? '是' : '否' }}</el-col>
      </el-row>
      <el-row>
        <el-col :span="10" class="infoDataTitle">是否在安保区内：</el-col>
        <el-col
          :span="14"
          class="infoDataContentLong"
        >{{ eventInfo && eventInfo.isProtected == 'Y' ? '是' : '否' }}</el-col>
      </el-row>
    </div>

    <!--  告警来源  -->
    <div class="alarm-info-title-box" @click="showWhat('1')">
      <div>来源信息</div>
      <div class="iconWrap">
        <i :class="showWhatArr.includes('1') ? 'el-icon-caret-top' : 'el-icon-caret-bottom'"></i>
      </div>
    </div>
    <!-- <div class="source-content-box">
      <el-row>
        <el-col :span="7" class="infoDataTitle">事件来源：</el-col>
        <el-col :span="17" class="infoDataContentLong">{{ getEvtSource(alarmInfo.eventSource) }}</el-col>
      </el-row>
    </div>-->
    <!--告警来源:无人机告警-->
    <div
      v-if="showWhatArr.includes('1') && alarmInfo.eventSource === '12'"
      class="source-content-box"
    >
      <el-row>
        <el-col :span="7" class="infoDataTitle">无人机编号：</el-col>
        <el-col
          :span="17"
          class="infoDataContentLong"
          :title="alarmInfo.order?.deviceCode || ''"
        >{{ alarmInfo.order?.deviceCode || '' }}</el-col>
      </el-row>
      <el-row>
        <el-col :span="7" class="infoDataTitle">无人机名称：</el-col>
        <el-col
          :span="17"
          class="infoDataContentLong"
          :title="alarmInfo.order?.deviceName || ''"
        >{{ alarmInfo.order?.deviceName || '' }}</el-col>
      </el-row>
      <el-row>
        <el-col :span="7" class="infoDataTitle">无人机厂家：</el-col>
        <el-col
          :span="17"
          class="infoDataContentLong"
          :title="alarmInfo.order?.uavDevInfoList?.modelFactoryName || ''"
        >{{ alarmInfo.order?.uavDevInfoList?.modelFactoryName || '' }}</el-col>
      </el-row>
      <el-row>
        <el-col :span="7" class="infoDataTitle">无人机型号：</el-col>
        <el-col
          :span="17"
          class="infoDataContentLong"
          :title="alarmInfo.order?.uavDevInfoList?.deviceModelCode || ''"
        >{{ alarmInfo.order?.uavDevInfoList?.deviceModelCode || '' }}</el-col>
      </el-row>
      <el-row>
        <el-col :span="7" class="infoDataTitle">无人机地址：</el-col>
        <el-col
          :span="17"
          class="infoDataContent"
          :title="alarmInfo.order?.uavDevInfoList?.location || ''"
        >{{ alarmInfo.order?.uavDevInfoList?.location || '' }}</el-col>
      </el-row>
      <el-row>
        <el-col :span="7" class="infoDataTitle">置信度：</el-col>
        <el-col
          :span="17"
          class="infoDataContent"
          :title="alarmInfo.order?.confidenceLevel"
        >{{ alarmInfo.order?.confidenceLevel }}</el-col>
      </el-row>
    </div>
    <!--告警来源:Ai告警-->
    <div
      v-if="showWhatArr.includes('1') && alarmInfo?.order?.warningSource === '1'"
      class="source-content-box"
    >
      <el-row>
        <el-col :span="7" class="infoDataTitle">事件来源：</el-col>
        <el-col :span="17" class="infoDataContentLong">{{ alarmInfo.order.warningSourceName }}</el-col>
      </el-row>
      <el-row>
        <el-col :span="8" class="infoDataTitle">摄像机编号：</el-col>
        <el-col
          :span="16"
          class="infoDataContentLong"
          :title="alarmInfo?.order?.deviceCode"
        >{{ alarmInfo?.order?.deviceCode }}</el-col>
      </el-row>
      <el-row>
        <el-col :span="8" class="infoDataTitle">摄像机名称：</el-col>
        <el-col
          :span="16"
          class="infoDataContentLong"
          :title="alarmInfo?.order?.deviceName"
        >{{ alarmInfo?.order?.deviceName }}</el-col>
      </el-row>
      <el-row>
        <el-col :span="8" class="infoDataTitle">摄像机地址：</el-col>
        <el-col
          :span="16"
          class="infoDataContentLong"
          :title="alarmInfo?.order?.deviceAddress"
        >{{ alarmInfo?.order?.deviceAddress }}</el-col>
      </el-row>
    </div>
    <!--告警来源:人工告警-->
    <div
      v-if="showWhatArr.includes('1') && alarmInfo?.order?.warningSource === '2'"
      class="source-content-box"
    >
      <el-row>
        <el-col :span="7" class="infoDataTitle">事件来源：</el-col>
        <el-col :span="17" class="infoDataContentLong">{{ alarmInfo.order.warningSourceName }}</el-col>
      </el-row>
      <el-row>
        <el-col :span="7" class="infoDataTitle">上报人：</el-col>
        <el-col :span="17" class="infoDataContent">{{alarmInfo?.order?.findName}}</el-col>
      </el-row>
      <el-row>
        <el-col :span="7" class="infoDataTitle">联系方式：</el-col>
        <el-col :span="17" class="infoDataContent">{{ alarmInfo?.order?.encryptionMobilePhone }}</el-col>
      </el-row>
    </div>
    <!--告警来源:一键告警-->
    <div
      v-if="showWhatArr.includes('1') && alarmInfo.order.warningSource === '3'"
      class="source-content-box"
    >
      <el-row>
        <el-col :span="7" class="infoDataTitle">事件来源：</el-col>
        <el-col :span="17" class="infoDataContentLong">{{ alarmInfo.order.warningSourceName }}</el-col>
      </el-row>
      <el-row>
        <el-col :span="7" class="infoDataTitle">摄像机编号：</el-col>
        <el-col
          :span="17"
          class="infoDataContentLong"
          :title="alarmInfo?.order?.deviceCode"
        >{{ alarmInfo?.order?.deviceCode }}</el-col>
      </el-row>
      <el-row>
        <el-col :span="7" class="infoDataTitle">摄像机名称：</el-col>
        <el-col
          :span="17"
          class="infoDataContentLong"
          :title="alarmInfo?.order?.deviceName"
        >{{ alarmInfo?.order?.deviceName }}</el-col>
      </el-row>
      <el-row>
        <el-col :span="7" class="infoDataTitle">通道名称：</el-col>
        <el-col
          :span="17"
          class="infoDataContentLong"
          :title="alarmInfo?.order?.channelName"
        >{{ alarmInfo?.order?.channelName }}</el-col>
      </el-row>
      <el-row>
        <el-col :span="7" class="infoDataTitle">摄像机地址：</el-col>
        <el-col
          :span="17"
          class="infoDataContentLong"
          :title="alarmInfo?.order?.deviceAddress"
        >{{ alarmInfo?.order?.deviceAddress }}</el-col>
      </el-row>
      <el-row>
        <el-col :span="7" class="infoDataTitle">摄像机厂家：</el-col>
        <el-col :span="17" class="infoDataContent">{{ alarmInfo?.order?.modelFactoryName }}</el-col>
      </el-row>
      <el-row>
        <el-col :span="7" class="infoDataTitle">摄像机高度：</el-col>
        <el-col
          :span="17"
          class="infoDataContent"
          v-if="alarmInfo?.order?.height !== null"
        >{{ alarmInfo?.order?.height }}m</el-col>
        <el-col :span="17" class="infoDataContent" v-else>&nbsp;</el-col>
      </el-row>
    </div>
    <!--告警来源:摄像机告警-->
    <div
      v-if="showWhatArr.includes('1') && alarmInfo?.order?.warningSource === '4'"
      class="source-content-box"
    >
      <el-row>
        <el-col :span="7" class="infoDataTitle">事件来源：</el-col>
        <el-col :span="17" class="infoDataContentLong">{{ alarmInfo.order.warningSourceName }}</el-col>
      </el-row>
      <el-row>
        <el-col :span="7" class="infoDataTitle">摄像机编号：</el-col>
        <el-col
          :span="17"
          class="infoDataContentLong"
          :title="alarmInfo?.order?.deviceCode"
        >{{ alarmInfo?.order?.deviceCode }}</el-col>
      </el-row>
      <el-row>
        <el-col :span="7" class="infoDataTitle">摄像机名称：</el-col>
        <el-col
          :span="17"
          class="infoDataContentLong"
          :title="alarmInfo?.order?.deviceName"
        >{{ alarmInfo?.order?.deviceName }}</el-col>
      </el-row>
      <el-row>
        <el-col :span="7" class="infoDataTitle">通道名称：</el-col>
        <el-col
          :span="17"
          class="infoDataContentLong"
          :title="alarmInfo?.order?.channelName"
        >{{ alarmInfo?.order?.channelName }}</el-col>
      </el-row>
      <el-row>
        <el-col :span="7" class="infoDataTitle">摄像机地址：</el-col>
        <el-col
          :span="17"
          class="infoDataContentLong"
          :title="alarmInfo?.order?.deviceAddress"
        >{{ alarmInfo?.order?.deviceAddress }}</el-col>
      </el-row>
      <el-row>
        <el-col :span="7" class="infoDataTitle">摄像机经纬度：</el-col>
        <el-col
          :span="17"
          class="infoDataContentLong"
          :title="
            alarmInfo?.order?.deviceLongitude +
            ',' +
            alarmInfo?.order?.deviceLatitude
          "
        >
          {{
          alarmInfo?.order?.deviceLongitude +
          ',' +
          alarmInfo?.order?.deviceLatitude
          }}
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="7" class="infoDataTitle">摄像机厂家：</el-col>
        <el-col :span="17" class="infoDataContent">
          {{
          alarmInfo?.order?.modelFactoryName
          }}
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="7" class="infoDataTitle">摄像机高度：</el-col>
        <el-col
          :span="17"
          class="infoDataContent"
          v-if="alarmInfo?.order?.height != null"
        >{{ alarmInfo?.order?.height }}m</el-col>
        <el-col :span="17" class="infoDataContent" v-if="alarmInfo?.order?.height == null">&nbsp;</el-col>
      </el-row>
      <el-row
        v-if="
          alarmInfo?.order?.isSecondJudge !== '' &&
          alarmInfo?.order?.isSecondJudge != null
        "
      >
        <el-col :span="7" class="infoDataTitle">是否二次研判：</el-col>
        <el-col :span="17" class="infoDataContent" v-if="alarmInfo?.order?.isSecondJudge === '0'">否</el-col>
        <el-col :span="17" class="infoDataContent" v-if="alarmInfo?.order?.isSecondJudge === '1'">是</el-col>
      </el-row>
      <el-row
        v-if="
          alarmInfo?.order?.isSecondJudge === '1' &&
          alarmInfo?.order?.secondJudgeResult !== '' &&
          alarmInfo?.order?.secondJudgeResult != null
        "
      >
        <el-col :span="7" class="infoDataTitle">二次研判结果：</el-col>
        <el-col
          :span="17"
          class="infoDataContent"
          v-if="alarmInfo?.order?.secondJudgeResult === '0'"
        >真</el-col>
        <el-col
          :span="17"
          class="infoDataContent"
          v-else-if="alarmInfo?.order?.secondJudgeResult === '1'"
        >假</el-col>
        <el-col :span="17" class="infoDataContent" v-else>算法未分析</el-col>
      </el-row>
      <el-row>
        <el-col :span="7" class="infoDataTitle">置信度：</el-col>
        <el-col :span="17" class="infoDataContent">
          {{
          alarmInfo?.order?.confidenceLevel
          }}
        </el-col>
      </el-row>
      <el-row v-if="alarmInfo?.order?.horiAzimuthAngle != null">
        <el-col :span="7" class="infoDataTitle" style="display: flex;align-items: center">
          <el-tooltip effect="dark" placement="top">
            <div slot="content">事件点到摄像机正北的水平方位角。</div>
            <div class="text-source" style="margin: 0;">?</div>
          </el-tooltip>
          <div>水平方位角：</div>
        </el-col>
        <el-col :span="17" class="infoDataContent">
          {{
          alarmInfo?.order?.horiAzimuthAngle
          }}
        </el-col>
      </el-row>
    </div>
    <!--告警来源:卫星告警-->
    <div
      v-if="showWhatArr.includes('1') && alarmInfo?.order?.warningSource === '9'"
      class="source-content-box"
    >
      <el-row>
        <el-col :span="7" class="infoDataTitle">事件来源：</el-col>
        <el-col :span="17" class="infoDataContentLong">{{ alarmInfo.order.warningSourceName }}</el-col>
      </el-row>
      <el-row>
        <el-col :span="7" class="infoDataTitle">卫星：</el-col>
        <el-col :span="17" class="infoDataContent">
          {{
          alarmInfo?.order?.deviceName
          }}
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="7" class="infoDataTitle">下垫面类型：</el-col>
        <el-col :span="17" class="infoDataContentLong">
          {{
          extraFieldValueWeiXing('underlyingSurfaceType')
          }}
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="7" class="infoDataTitle">火区面积：</el-col>
        <el-col :span="17" class="infoDataContentLong">
          {{
          extraFieldValueWeiXing('fireArea')
          }}
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="7" class="infoDataTitle">置信度：</el-col>
        <el-col :span="17" class="infoDataContentLong">
          {{
          alarmInfo?.order?.confidenceLevel
          }}
        </el-col>
      </el-row>
      <el-row v-if="alarmInfo?.order?.satelliteDevice.deviceCode">
        <el-col :span="7" class="infoDataTitle">摄像机编号：</el-col>
        <el-col
          :span="17"
          class="infoDataContentLong"
          :title="alarmInfo?.order?.satelliteDevice.deviceCode"
        >{{ alarmInfo?.order?.satelliteDevice.deviceCode }}</el-col>
      </el-row>
      <el-row v-if="alarmInfo?.order?.satelliteDevice.deviceCode">
        <el-col :span="7" class="infoDataTitle">摄像机名称：</el-col>
        <el-col
          :span="17"
          class="infoDataContentLong"
          :title="alarmInfo?.order?.satelliteDevice.deviceName"
        >{{ alarmInfo?.order?.satelliteDevice.deviceName }}</el-col>
      </el-row>
      <el-row v-if="alarmInfo?.order?.satelliteDevice.deviceCode">
        <el-col :span="7" class="infoDataTitle">摄像机地址：</el-col>
        <el-col
          :span="17"
          class="infoDataContentLong"
          :title="alarmInfo?.order?.satelliteDevice.location"
        >{{ alarmInfo?.order?.satelliteDevice.location }}</el-col>
      </el-row>
      <el-row v-if="alarmInfo?.order?.satelliteDevice.deviceCode">
        <el-col :span="7" class="infoDataTitle">摄像机经纬度：</el-col>
        <el-col
          :span="17"
          class="infoDataContentLong"
          :title="
            alarmInfo?.order?.satelliteDevice.longitude +
            ',' +
            alarmInfo?.order?.satelliteDevice.latitude
          "
        >
          {{
          alarmInfo?.order?.satelliteDevice.longitude +
          ',' +
          alarmInfo?.order?.satelliteDevice.latitude
          }}
        </el-col>
      </el-row>
      <el-row v-if="alarmInfo?.order?.satelliteDevice.deviceCode">
        <el-col :span="7" class="infoDataTitle">摄像机厂家：</el-col>
        <el-col :span="17" class="infoDataContent">
          {{
          alarmInfo?.order?.satelliteDevice.modelCodeName
          }}
        </el-col>
      </el-row>
      <el-row v-if="alarmInfo?.order?.satelliteDevice.deviceCode">
        <el-col :span="7" class="infoDataTitle">摄像机高度：</el-col>
        <el-col
          :span="17"
          class="infoDataContent"
          v-if="alarmInfo?.order?.satelliteDevice.height != null"
        >{{ alarmInfo?.order?.satelliteDevice.height }}m</el-col>
        <el-col
          :span="17"
          class="infoDataContent"
          v-if="alarmInfo?.order?.satelliteDevice.height == null"
        >&nbsp;</el-col>
      </el-row>
      <el-row v-if="alarmInfo?.order?.satelliteDevice.deviceCode">
        <el-col :span="7" class="infoDataTitle">抓图时间：</el-col>
        <el-col
          :span="17"
          class="infoDataContentLong"
          :title="alarmInfo?.order?.satelliteDevice.snapShotTime"
        >{{ alarmInfo?.order?.satelliteDevice.snapShotTime }}</el-col>
      </el-row>
    </div>
    <!--告警来源:物联告警-->
    <div
      v-if="showWhatArr.includes('1') && alarmInfo?.order?.warningSource === '8'"
      class="source-content-box"
    >
      <el-row>
        <el-col :span="7" class="infoDataTitle">事件来源：</el-col>
        <el-col :span="17" class="infoDataContentLong">{{ alarmInfo.order.warningSourceName }}</el-col>
      </el-row>
      <el-row>
        <el-col :span="9" class="infoDataTitle">物联设备编号：</el-col>
        <el-col
          :span="15"
          class="infoDataContentLong"
          :title="alarmInfo?.order?.deviceCode"
        >{{ alarmInfo?.order?.deviceCode }}</el-col>
      </el-row>
      <el-row>
        <el-col :span="9" class="infoDataTitle">物联设备名称：</el-col>
        <el-col
          :span="15"
          class="infoDataContentLong"
          :title="alarmInfo?.order?.deviceName"
        >{{ alarmInfo?.order?.deviceName }}</el-col>
      </el-row>
      <el-row>
        <el-col :span="9" class="infoDataTitle">物联设备经纬度：</el-col>
        <el-col
          :span="15"
          class="infoDataContentLong"
          :title="
            alarmInfo?.order?.videoLongitude + ',' + alarmInfo?.order?.videoLatitude
          "
        >
          {{
          alarmInfo?.order?.videoLongitude
          ? alarmInfo?.order?.videoLongitude +
          ',' +
          alarmInfo?.order?.videoLatitude
          : ''
          }}
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="9" class="infoDataTitle">物联设备地址：</el-col>
        <el-col
          :span="15"
          class="infoDataContentLong"
          :title="alarmInfo?.order?.deviceAddress"
        >{{ alarmInfo?.order?.deviceAddress }}</el-col>
      </el-row>
      <el-row>
        <el-col :span="9" class="infoDataTitle">物联设备厂家：</el-col>
        <el-col
          :span="15"
          class="infoDataContentLong"
          :title="alarmInfo?.order?.modelFactoryName"
        >{{ alarmInfo?.order?.modelFactoryName }}</el-col>
      </el-row>
      <el-row>
        <el-col :span="9" class="infoDataTitle">物联设备型号：</el-col>
        <el-col :span="15" class="infoDataContent">
          {{
          alarmInfo?.order?.deviceCode
          }}
        </el-col>
      </el-row>
    </div>
    <!--告警来源:雷达告警-->
    <div
      v-if="showWhatArr.includes('1') && alarmInfo?.order?.warningSource === '10'"
      class="source-content-box"
    >
      <el-row>
        <el-col :span="7" class="infoDataTitle">事件来源：</el-col>
        <el-col :span="17" class="infoDataContentLong">
          {{
          alarmInfo.order.warningSourceName
          }}
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="7" class="infoDataTitle">雷达编号：</el-col>
        <el-col
          :span="17"
          class="infoDataContentLong"
          :title="alarmInfo?.order?.deviceCode"
        >{{ alarmInfo?.order?.deviceCode }}</el-col>
      </el-row>
      <el-row>
        <el-col :span="7" class="infoDataTitle">雷达名称：</el-col>
        <el-col
          :span="17"
          class="infoDataContentLong"
          :title="alarmInfo?.order?.deviceName"
        >{{ alarmInfo?.order?.deviceName }}</el-col>
      </el-row>
      <el-row>
        <el-col :span="7" class="infoDataTitle">雷达经纬度：</el-col>
        <el-col
          :span="17"
          class="infoDataContentLong"
          :title="
            alarmInfo?.order?.videoLongitude + ',' + alarmInfo?.order?.videoLatitude
          "
        >
          {{
          alarmInfo?.order?.videoLongitude + ',' + alarmInfo?.order?.videoLatitude
          }}
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="7" class="infoDataTitle">雷达地址：</el-col>
        <el-col
          :span="17"
          class="infoDataContentLong"
          :title="alarmInfo?.order?.deviceAddress"
        >{{ alarmInfo?.order?.deviceAddress }}</el-col>
      </el-row>
      <el-row>
        <el-col :span="7" class="infoDataTitle">雷达厂家：</el-col>
        <el-col :span="17" class="infoDataContentLong">
          {{
          alarmInfo?.order?.modelFactoryName
          }}
        </el-col>
      </el-row>
    </div>
    <!-- 处置信息 -->
    <div class="alarm-info-title-box" @click="showWhat('evtInfo')">
      <div>处置信息</div>
      <div class="iconWrap">
        <i :class="showWhatArr.includes('evtInfo') ? 'el-icon-caret-top' : 'el-icon-caret-bottom'"></i>
      </div>
    </div>
    <div v-if="showWhatArr.includes('evtInfo')" class="source-content-box">
      <el-timeline v-if="recordsInfo && recordsInfo.length > 0" class="timeline-box">
        <el-timeline-item
          placement="top"
          v-for="(item, index) in recordsInfo"
          :key="index"
          color="#0bbd87"
        >
          <template v-slot:dot>
            <i :class="`timeline-dot ${index === 0 ? 'active' : ''}`" />
            <div :class="`handle-line-head ${index === 0 ? 'active' : ''}`">
              <span class="handle-line-date">{{ item.flowNodeTime }}</span>
              <span class="handle-line-title">{{ getNodeName(alarmInfo, item) }}</span>
            </div>
          </template>
          <el-card>
            <FlowForm
              :key="`flowForm${index}`"
              :record="item"
              :alarmInfo="alarmInfo"
              :formItems="getForm(alarmInfo,item)"
            />
          </el-card>
        </el-timeline-item>
      </el-timeline>
    </div>
    <!-- 全屏查看视频图片 -->
    <FileFullScreen v-if="showFileFull" :fileFullList="fileFullList" @closeFull="closeFull"></FileFullScreen>
  </div>
</template>

<script>
import FileFullScreen from '@/components/common/file-full-screen.vue'
import FlowForm from '@/components/page/ScreenPages/IntegratedMonitor/EventDetailInfo/AlarmInfoContent/FlowForm.vue'
import dayjs from 'dayjs'
import { mapGetters } from 'vuex'
import LinkConst from './LinkConst'
import {
  dealStateEnm,
  alarmStatusListDataDefault,
} from '@/components/page/ScreenPages/IntegratedMonitor/EventFilter/typeDatas'
const congestLevelEnm = {
  重大: 1,
  严重: 2,
  一般: 3,
  轻微: 4,
}
export default {
  name: 'alarmInfoContent',
  components: {
    FlowForm,
    FileFullScreen,
  },
  props: {
    mapId: {
      type: String,
      default: 'map',
    },
    alarmInfo: {
      type: Object,
      default: {},
    },
    recordsInfo: {
      type: Array,
      default: [],
    },
    showRelAlarm: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    alarmInfo: {
      handler(newVal) {
        if (!newVal.records) {
          return
        }
        const { order, records } = newVal
        const handleInfoData = records.find(
          item => item.linkId === order.linkId
        )
        this.handleInfo = handleInfoData?.linkName
        this.eventInfo = newVal
      },
      deep: true, //json 深度匹配
      immediate: true, //初始化时执行
    },
  },
  data() {
    return {
      LinkConst,
      showWhatArr: ['1', '2', 'evtInfo'], //展示信息 1:告警来源 2:事件信息
      eventInfo: {}, // 铁路界限内信息
      showRelInfo: false, //是否显示关联事件
      showFileFull: false, //是否显示全屏查看附件
      fileFullList: null, //全屏查看附件数据
      handleInfo: '',
      isIn: false, //鼠标经过
      moveIndex: 0, //左移动次数
      isChangeAddress: false, //修改地址按钮状态切换
      copyMarkerData: {}, //备份地图选点
    }
  },
  computed: {
    ...mapGetters('map', ['getCheckMode']),
    extraFieldValueWeiXing() {
      return value => {
        try {
          if (!this.alarmInfo?.order?.extraField) {
            return ''
          }
          let itemData = JSON.parse(this.alarmInfo?.order?.extraField || {})
          return itemData[value]
        } catch (e) {
          return ''
        }
      }
    },
    getOccTime() {
      return key => (key ? dayjs(key).format('YYYY-MM-DD HH:mm:ss') : key)
    },
    getFusionStatus() {
      return key => {
        const obj = alarmStatusListDataDefault.find(v =>
          v.dictValue.includes(key)
        )
        return obj || {}
      }
    },
    // getEvtType() {
    //   return key => {
    //     const obj = this.$store.state.event?.evtTypeList?.find(
    //       v => v.dictValue === key
    //     )
    //     return obj?.dictLabel || ''
    //   }
    // },
    getCongestLevel() {
      return key => congestLevelEnm?.[key]
    },
    getDealState() {
      return key => dealStateEnm?.[key]
    },
    getEvtSource() {
      return key => {
        const obj = this.$store.state.event?.evtSourceList?.find(
          v => v.dictValue === key
        )
        return obj?.dictLabel || ''
      }
    },
  },
  // created() {},
  methods: {
    /**
     * 阻止元素拖动事件，防止默认行为导致的页面拖动
     * @param {Event} e - 拖动事件对象
     */
    stopDrag(e) {
      e.stopPropagation()
    },

    /**
     * 阻止右键菜单，默认情况下禁用右键菜单可以增加安全性或自定义右键操作
     * @param {Event} e - 右键点击事件对象
     */
    disableRightClick(e) {
      e.preventDefault()
    },

    /**
     * 控制信息的展开和收起，基于类型切换显示状态
     * @param {string} type - 信息的类型，用于判断和控制显示逻辑
     */
    showWhat(type) {
      if (
        !type &&
        (this.showRelAlarm || this.alarmInfo?.order?.orderStatus === '1')
      ) {
        // 是关联事件 显示告警来源 告警详情
        this.showWhatArr = ['1', '2']
      } else {
        // 非关联事件，动态更新显示类型数组
        if (this.showWhatArr.includes(type)) {
          this.showWhatArr = this.showWhatArr.filter(item => item !== type)
        } else {
          this.showWhatArr.push(type)
        }
      }
    },

    /**
     * 打开图片预览，支持多张图片
     * @param {Array} item - 包含图片资源URL的数组
     * @param {number} index - 当前图片在数组中的索引，用于视频截图预览
     */
    openImgViewer(item, index) {
      let fileFullList = {}
      fileFullList.isVideo = true
      fileFullList.index = index
      fileFullList.videoImgUrl = []
      item.forEach(item2 => {
        fileFullList.videoImgUrl.push(item2.resourceUrl)
      })
      this.fileFullList = fileFullList
      this.showFileFull = true
    },

    /**
     * 关闭全屏图片预览
     */
    closeFull() {
      this.showFileFull = false
    },

    /**
     * 将处置时限从分钟转换为天、小时、分钟的格式显示
     * @param {number} timeLimit - 处置时限，单位为分钟
     * @returns {string} 格式化后的处置时限字符串
     */
    transformTimeLimit(timeLimit) {
      let rsStr = ''
      if (timeLimit) {
        // 如果超过1天显示天
        if (timeLimit / (60 * 24) > 1) {
          rsStr = `${Math.floor(timeLimit / (60 * 24))}天${Math.floor(
            (timeLimit % (60 * 24)) / 60
          )}小时${timeLimit % 60}分钟`
        } else {
          rsStr = `${Math.floor(timeLimit / 60)}小时${timeLimit % 60}分钟`
        }
      }
      return rsStr
    },

    /**
     * 根据环节ID获取对应的处置时限
     * @param {string} linkId - 环节ID
     * @returns {string} 处置时限，格式为天/小时/分钟
     */
    getLinkTimeLimit(linkId) {
      let timeLimit = 0
      if (this.alarmInfo.flows && this.alarmInfo.length > 0) {
        this.alarmInfo.flows.forEach(item => {
          if (item.linkId === linkId) {
            timeLimit = item.timeLimit
          }
        })
      }
      return this.transformTimeLimit(timeLimit)
    },

    /**
     * 计算流程节点的办理时长
     * @param {Object} item - 流程节点信息
     * @returns {string} 办理时长，格式为小时/分钟
     */
    flowDealTime(item) {
      if (item.flowEndTime && item.flowNodeTime) {
        return this.transformTimeLimit(
          dayjs(item.flowEndTime).diff(dayjs(item.flowNodeTime), 'minute')
        )
      } else {
        return ''
      }
    },
    getTimeLimit(item) {
      let timeLimit = 0
      if (this.alarmInfo.flows && this.alarmInfo.flows.length > 0) {
        this.alarmInfo.flows.forEach(flowItem => {
          if (flowItem.linkId === item.linkId) {
            timeLimit = flowItem.timeLimit
          }
        })
      }
      return timeLimit
    },
    /**
     * 检查当前流程节点是否超时
     * @param {Object} item - 流程节点信息
     * @returns {boolean} 节点是否超时
     */
    checkOverTime(item) {
      let flowuseTime = 0
      // 如果节点有结束时间和开始时间
      if (item.flowEndTime && item.flowNodeTime) {
        flowuseTime = dayjs(item.flowEndTime).diff(
          dayjs(item.flowNodeTime),
          'minute'
        )
      } else if (item.flowNodeTime) {
        // 如果节点有开始时间，没有结束时间，则计算当前时间与开始时间差
        flowuseTime = dayjs().diff(dayjs(item.flowNodeTime), 'minute')
      } else {
        // 流程节点开始和结束都为空，脏数据
        flowuseTime = -1
      }
      // 获取环节要求时限
      let timeLimit = this.getTimeLimit(item)
      return flowuseTime > 0 && timeLimit > 0 && flowuseTime > timeLimit
    },

    /**
     * 计算超时的时间长度
     * @param {Object} item - 流程节点信息
     * @returns {string} 超时时间，格式为小时/分钟
     */
    getOverTimeStr(item) {
      let flowuseTime
      // 如果节点有结束时间和开始时间
      if (item.flowEndTime && item.flowNodeTime) {
        flowuseTime = dayjs(item.flowEndTime).diff(
          dayjs(item.flowNodeTime),
          'minute'
        )
      } else if (item.flowNodeTime) {
        // 如果节点有开始时间，没有结束时间，则计算当前时间与开始时间差
        flowuseTime = dayjs().diff(dayjs(item.flowNodeTime), 'minute')
      } else {
        flowuseTime = 0
      }
      // 获取环节要求时限
      let timeLimit = this.getTimeLimit(item)
      flowuseTime = flowuseTime - timeLimit
      return this.transformTimeLimit(flowuseTime)
    },

    /**
     * 根据告警信息和节点信息获取表单配置
     * @param {Object} alarmInfo - 告警信息
     * @param {Object} item - 流程节点信息
     * @returns {Object} 表单配置对象
     */
    getForm(alarmInfo, item) {
      const linkConst = Object.keys(LinkConst).find(key =>
        LinkConst[key].validate(alarmInfo, item)
      )
      return (
        (linkConst && LinkConst[linkConst] && LinkConst[linkConst].form) || {}
      )
    },

    /**
     * 根据告警信息和节点信息获取节点名称
     * @param {Object} alarmInfo - 告警信息
     * @param {Object} item - 流程节点信息
     * @returns {string} 节点名称
     */
    getNodeName(alarmInfo, item) {
      const linkConst = Object.keys(LinkConst).find(key =>
        LinkConst[key].validate(alarmInfo, item)
      )
      return LinkConst[linkConst] && LinkConst[linkConst].linkNameFormat
        ? LinkConst[linkConst].linkNameFormat(alarmInfo, item)
        : item.linkName
    },
  },
}
</script>


<style lang='less' src='./index.less' />
