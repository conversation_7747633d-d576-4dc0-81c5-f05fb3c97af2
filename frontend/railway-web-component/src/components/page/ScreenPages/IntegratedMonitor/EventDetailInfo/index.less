.fun-btn-active-class {
  //按钮点击后效果 文字变黄 背景增加
  font-size: 18px;
  color: #1989fa !important;
  opacity: 0.97 !important;
}

.fun-btn-active-class2 {
  //按钮点击后效果 文字变黄 背景增加
  font-size: 18px;
  background-color: #1989fa !important;
  opacity: 0.97 !important;
}

.alarm-detail-outter {
  width: 360px;
  height: 100%; // fit-content;
  // max-height: 960px;
  background: #172537;
  border-radius: 8px;
  color: #fff;
  font-size: 14px;
  font-family: PingFangSC-Regular;
  padding: 12px 0;
  pointer-events: all;
  box-sizing: border-box;
  display: flex;
  flex-flow: column;
  // position: relative;
  .titleWrap {
    padding: 0 12px 0 6px;
    height: 34px;
    width: 100%;
    box-sizing: border-box;
    position: relative;
    .title {
      width: 100%;
      height: 100%;
      background: url("~@/assets/images/alarmEvent/alarm/event_index_bg.svg")
        no-repeat 100% 100%;
      background-size: 100%;
      display: flex;
      &::before {
        content: "";
        width: 21px;
        height: 23px;
        margin-left: 21px;
        margin-top: 2px;
        background: url("~@/assets/images/alarmEvent/alarm/event_index_title_icon.svg")
          no-repeat 100% 100%;
        background-size: 100%;
      }
      .titlName {
        font-family: PingFangSC-Medium;
        font-size: 14px;
        color: #e8f3ff;
        letter-spacing: 0;
        line-height: 20px;
        font-weight: 500;
        margin-left: 24px;
        margin-top: 4px;
      }
      .unit {
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: rgba(232, 243, 255, 0.6);
        letter-spacing: 0;
        line-height: 20px;
        font-weight: 400;
        margin-top: 4px;
      }
    }
    .titleIcons {
      display: flex;
      position: absolute;
      right: 12px;
      top: 0;
      .collection-icon {
        width: 16px;
        height: 16px;
        cursor: pointer;
      }
      .alarm-close {
        width: 16px;
        height: 16px;
        margin-left: 12px;
        cursor: pointer;
      }
    }
  }
  //视频图片轮播盒子
  .video-img-carousel-box {
    width: 100%;
    height: 190px; // 242px;
    margin-top: 6px;
    padding: 0 12px;
    box-sizing: border-box;
  }

  .detail-tools {
    display: flex;
    padding: 12px;
    box-sizing: border-box;

    & > div:not(:first-child) {
      margin-left: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
    }
    .tool-img {
      margin-right: 14px;
      cursor: pointer;
    }
    .tool-clicked {
      // filter: drop-shadow(#1989FA 1000px 0);
      // transform: translateX(-1000px);
      filter: invert(48%) sepia(97%) saturate(3808%) hue-rotate(196deg)
        brightness(101%) contrast(101%);
    }
    .fun-btn-class {
      width: 18px;
      height: 18px;
      font-size: 24px;
      color: #ffffff;
      line-height: 18px;
      text-align: center;
      cursor: pointer;
      &:not(:first-child) {
        margin-left: 12px;
      }
    }
  }
  .detail-con {
    flex-basis: 550px;
    // flex: 1;
    overflow: hidden;
    padding: 0 12px;
    box-sizing: border-box;

    .detail-item {
      font-size: 14px;
      padding-left: 26px;
      // height: 26px;
      line-height: 26px;
      display: flex;
      span:nth-child(1) {
        width: 70px;
        text-align: right;
      }
      span:nth-child(2) {
        width: calc(100% - 70px);
        color: #fff;
      }
    }
    .el-collapse {
      border-top: 0px;
      border-bottom: 0px;
    }
    .el-collapse-item__header {
      height: 26px;
      line-height: 26px;
      font-size: 14px;
      background-color: rgba(130, 181, 255, 0.1);
      color: #fff;
      border-bottom: 0px solid rgba(23, 37, 55, 0.9);
      border-left: 2px solid #e7f3fe;
      margin-bottom: 5px;
      span {
        margin-left: 16px;
      }
    }
    .el-collapse-item__wrap {
      background-color: transparent;
      border-bottom: 1px solid rgba(23, 37, 55, 0.9);
    }
    .el-collapse-item__content {
      color: rgba(255, 255, 255, 0.65);
      padding-bottom: 0px;
    }
  }
  .detail-buttons {
    display: flex;
    flex-wrap: wrap;
    margin-top: 12px;

    .alarm-bottom-btn-div {
      display: flex;
      flex-wrap: wrap;
    }
    .detail-button {
      width: 80px;
      height: 32px;
      // background-image: linear-gradient(90deg, #2FB1EC 8%, #155BD4 100%);
      background: #2f4360;
      border-radius: 4px;
      line-height: 32px;
      text-align: center;
      margin-left: 5px;
      margin-bottom: 5px;
      font-size: 12px;
      cursor: pointer;
    }
    .detail-button-blue {
      background-image: linear-gradient(90deg, #2fb1ec 8%, #155bd4 100%);

      &:hover,
      &.selected {
        opacity: 0.8;
      }
    }
    .detail-button-red {
      background: rgba(237, 81, 88, 0.2);
      color: #ed5158;
    }
  }
}
