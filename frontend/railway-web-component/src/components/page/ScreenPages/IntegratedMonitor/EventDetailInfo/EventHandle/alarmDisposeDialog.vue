<!--
 * @Description  : 处置
 * <AUTHOR> wnj
 * @Date         : 2023-12-11 10:58:52
 * @LastEditors  : wnj
 * @LastEditTime : 2023-12-26 14:50:20
 * @FilePath     :  / src / components / page / alarmEvent / components / alarmRight / alarmDisposeDialog.vue
-->
<!--  告警各环节处理弹窗  -->
<template>
  <!-- 使用 CtlPanel 组件包裹弹窗内容 -->
  <CtlPanel class="evt-handle-wrap alarm-dispatch-outter" :close="toClose">
    <!-- 弹窗标题插槽 -->
    <template v-slot:title>{{ alarmDisposeData.title }}</template>
    <!-- 弹窗内容插槽 -->
    <template v-slot:content>
      <!-- 弹窗内容主体，包含加载状态 -->
      <div class="dispatch-con evt-handle-form" v-loading="dataLoading">
        <!-- 选择环节部分 -->
        <div class="dispose-item-box">
          <div class="dispose-item-left" :class="{'chuZhi-left' : isChuZhi}">
            <div class="dispose-xinghao">*</div>
            <div class="dispose-item-label">{{ linkLabel }}</div>
          </div>
          <div class="dispose-item-right" :class="{'chuZhi-right' : isChuZhi}">
            <!-- 环节选择下拉框 -->
            <el-select
              class="c-select"
              popper-class="c-select-dropdown"
              placeholder="请选择"
              v-model="nextLink"
              :disabled="nextLinkList && nextLinkList.length === 1"
              @change="changeNextLink"
            >
              <!-- 遍历环节列表生成选项 -->
              <el-option
                v-for="item in nextLinkList"
                :key="item.curLink"
                :label="item.linkName"
                :value="item.curLink"
              ></el-option>
            </el-select>
          </div>
        </div>
        <!-- 处理人选择部分 -->
        <div class="dispose-item-box">
          <div class="dispose-item-left" :class="{'chuZhi-left' : isChuZhi}">
            <div class="dispose-xinghao">*</div>
            <div class="dispose-item-label">处理人</div>
          </div>
          <div class="dispose-item-right" :class="{'chuZhi-right' : isChuZhi}">
            <!-- 使用 PeopleSelectTree 组件选择处理人 -->
            <PeopleSelectTree
              ref="rightPeopleSelect"
              :alarmInfo="alarmDisposeData?.alarmInfo"
              :getFlowUserParams="getFlowUserParams"
              :showAllTreeBtn="showAllTreeBtn"
              :dataFilterPermiss="dataFilterPermiss"
              @dispatchCheckChange="dispatchCheckChange"
              @dataLoading="changeLoading"
            />
          </div>
        </div>
        <!-- 环节说明部分 -->
        <div class="dispose-item-box">
          <div class="dispose-item-left" :class="{'chuZhi-left' : isChuZhi}">
            <div class="dispose-item-label">
              {{ alarmDisposeData.title === '真警' ? '研判说明' : alarmDisposeData.title === '调度'
              ? '调度说明'
              : alarmDisposeData.title === '处置'
              ? '处置说明'
              : alarmDisposeData.title === '转派'
              ? '转派说明'
              : '回退说明' }}
            </div>
          </div>
          <div class="dispose-item-right" :class="{'chuZhi-right' : isChuZhi}">
            <!-- 使用 AlarmPhrases 组件输入说明 -->
            <AlarmPhrases
              v-model="remarksText"
              placeholder="请输入内容"
              :approvalListParam="approvalListParam"
              :orderStatus="alarmDisposeData.alarmInfo.order.orderStatus"
              :maxLength="200"
              @update="isPhrasesUnfold"
              :isUnfold.sync="isUnfold"
            />
          </div>
        </div>
        <!-- 附件上传部分 -->
        <div
          class="dispose-item-box"
          v-if="alarmDisposeData.title === '调度' || alarmDisposeData.title === '处置'"
        >
          <div class="dispose-item-left" :class="{'chuZhi-left' : isChuZhi}">
            <div class="dispose-xinghao is-chuzhi" v-if="alarmDisposeData.title === '处置'">*</div>
            <div class="dispose-tip">
              <!-- 上传格式提示 -->
              <div
                c-tip="上传格式支持png、jpg、jpeg、mp4、txt、doc、docx、pdf、ppt、pptx，单个文件上传不得超过10M，最多可上传9个"
                c-tip-placement="top"
                class="text-source"
              >?</div>
            </div>
            <div class="dispose-item-label">上传附件</div>
          </div>
          <div
            class="dispose-item-right"
            :class="{'chuZhi-right' : isChuZhi}"
            @contextmenu="disableRightClick"
          >
            <!-- 使用 UploadFiles 组件上传附件 -->
            <UploadFiles
              class="dispose-upload"
              :accept="accept"
              :isEdit="true"
              :maxLength="9"
              :maxSize="10"
              @change="changeFile"
              :unAcceptErrMsg="`只支持上传${accept}格式的文件`"
            />
          </div>
        </div>
      </div>
      <!-- 确认和取消按钮 -->
      <div class="dispatch-buttons">
        <div class="dispatch-button dispatch-button-blue" @click="toSubmit">
          <span>确定</span>
        </div>
        <div class="dispatch-button" @click="toClose">
          <span>取消</span>
        </div>
      </div>
    </template>
  </CtlPanel>
</template>
<script>
import api from '@/api'
import CtlPanel from '../../CtlPanel/index.vue'
import AlarmPhrases from '../components/alarm-phrases.vue'
import AlarmSuppress from '../components/alarm-suppress.vue'
import PeopleSelectTree from '../components/peopleSelectTree.vue'
import UploadFiles from '../components/upload/UploadFiles.vue'
export default {
  components: {
    CtlPanel,
    PeopleSelectTree,
    AlarmPhrases,
    UploadFiles,
    AlarmSuppress,
  },
  props: {
    propData: {
      type: Object,
      default: {},
    },
    close: {
      type: Function,
      default: () => {
        console.log('close')
      },
    },
    okCalBack: {
      type: Function,
      default: () => {
        console.log('okCalBack')
      },
    },
  },
  data() {
    return {
      linkLabel: this.getLinkLabel(), // 环节名称
      approvalListParam: this.getApprovalListParam(), // 审批人入参
      accept: 'png,jpg,jpeg,mp4,txt,doc,docx,pdf,ppt,pptx', // 附件上传格式
      alarmDisposeData: this.propData, // 告警处置数据
      dataLoading: true, // 数据加载状态
      isChuZhi: false, // 是否处置环节
      nextLinkList: [], // 所有的环节
      nextLink: '', // 选中的下一环节
      showAllTree: false, // 是否展示全部处理人树
      showAllTreeBtn: false, // 是否展示全部处理人按钮
      linkId: '', // 下一环节id
      linkName: '', // 下一环节名称
      realLinkType: '', // 真实环节id
      msgNotifyType: '', // 通知方式
      nextEnableAcceptanceMode: '', // 下一环节是否开启受理
      timeLimit: '', // 下一环节超期时间
      timeLimitAcceptance: '', // 下一环节受理时间
      dataFilterPermiss: '', // 是否开启过滤权限 0 否 1是
      alarmFlowId: '', // 告警流程id
      userType: '', // 用户类型
      getFlowUserParams: {
        // 查询处置人员入参
        item: {}, // 下一环节数据
        recordsInfo: [], // 回退时需要的环节信息
      },
      dispatchNameValue: [], // 处理人名称
      processDTOList: [], // 处理人入参
      remarksText: '', // 流程处理时的处理说明
      isUnfold: false, // 展开常用语 弹窗变大
      isSuppress: false, // 是否开启压制
    }
  },
  created() {
    console.log(
      '%c █░░░░░░░░░░░░█ ,注释 created时的alarmDisposeData数据',
      'color: #FAC800',
      this.alarmDisposeData
    )
    // 根据告警处置数据的标题决定调用不同的环节查询方法
    if (this.alarmDisposeData.title === '转派') {
      this.getReassignLink(this.alarmDisposeData.alarmInfo.order)
    } else if (this.alarmDisposeData.title === '回退') {
      this.getBackLink(this.alarmDisposeData.alarmInfo.order)
    } else {
      if (this.alarmDisposeData.title === '处置') {
        this.isChuZhi = true
      }
      this.getNextLink(this.alarmDisposeData.alarmInfo.order)
    }
  },
  methods: {
    /**
     * 禁用鼠标右键
     */
    disableRightClick(e) {
      e.preventDefault()
    },
    /**
     * 查询可转派环节
     */
    getReassignLink(item) {
      const { linkId, flowTemplate, flowId, warningSource } = item
      const params = {
        curLink: linkId,
        alarmFlowId: flowId,
        templateId: flowTemplate,
        warningSource: warningSource,
      }
      api
        .post(
          `${this.$env.VUE_APP_REQ_PREFIX_PLAT}/event/queryReassignLink`,
          params
        )
        .then(res => {
          if (res.code == 200) {
            this.nextLinkList = res.data
            if (res.data.length < 2) {
              this.nextLink = this.nextLinkList[0].curLink
              this.changeNextLink(this.nextLink)
            } else {
              this.dataLoading = false
            }
          } else {
            this.dataLoading = false
            this.$message({
              type: 'error',
              message: res.msg || '可转派环节查询失败！',
            })
          }
        })
    },
    /**
     * 查询回退环节
     */
    getBackLink(item) {
      const params = {
        orderId: item.orderId ? item.orderId : item.warningOrderId,
        linkId: item.realLinkType,
        flowId: item.flowId,
        templateId: item.flowTemplate, //模板id
      }
      api
        .post(
          `${this.$env.VUE_APP_REQ_PREFIX_PLAT}/event/queryRollbackLink`,
          params
        )
        .then(res => {
          if (res.code == 200) {
            let goBackLink = res.data
            this.nextLinkList = []
            goBackLink.forEach(item2 => {
              let a = {}
              if (item2.linkId !== 8) {
                a.curLink = item2.linkId
                a.linkId = item2.linkId
                a.linkName = item2.linkName
                a.flowId = item2.flowId
                a.realLinkType = item2.realLinkType
                a.msgNotifyType = item2.msgNotifyType
                a.dataFilterPermiss = item2.dataFilterPermiss
                a.customAdmin = item2.customAdmin
                a.nextEnableAcceptance = item2.enableAcceptanceMode
                a.timeLimitAcceptance = item2.timeLimitAcceptance
                a.timeLimit = item2.timeLimit
                this.nextLinkList.push(a)
              }
            })
            if (res.data.length < 2) {
              this.nextLink = this.nextLinkList[0].curLink
              this.changeNextLink(this.nextLink)
            } else {
              this.dataLoading = false
            }
          } else {
            this.dataLoading = false
            this.$message({
              type: 'error',
              message: res.msg || '回退环节查询失败！',
            })
          }
        })
    },
    /**
     * 查询下一环节
     */
    getNextLink(item) {
      const params = {
        flowId: item.flowId,
        curLink: item.linkId,
        templateId: item.flowTemplate, //模板id
        warningSource: item.warningSource, //告警来源
      }
      api
        .post(
          `${this.$env.VUE_APP_REQ_PREFIX_PLAT}/event/queryNextLink`,
          params
        )
        .then(res => {
          if (res.code == 200) {
            this.nextLinkList = res.data
            if (res.data.length < 2) {
              //只有一个环节时直接赋值
              this.nextLink = this.nextLinkList[0].curLink
              this.changeNextLink(this.nextLink)
            } else {
              this.dataLoading = false
            }
          } else {
            this.dataLoading = false
            this.$message({
              type: 'error',
              message: res.msg || '下一环节查询失败！',
            })
          }
        })
    },
    /**
     * 用户选中下一环节
     */
    changeNextLink(val) {
      let choosed
      choosed = this.nextLinkList.find(item => item.curLink === val)
      console.log(
        '%c █░░░░░░░░░░░░█ ,注释 用户选中下一环节',
        'color: #FAC800',
        choosed
      )
      this.linkId = val
      this.linkName = choosed.linkName
      this.realLinkType = choosed.realLinkType
      this.msgNotifyType = choosed.msgNotifyType
      this.nextEnableAcceptanceMode = choosed.enableAcceptanceMode
      this.timeLimitAcceptance = choosed.timeLimitAcceptance
      this.timeLimit = choosed.timeLimit
      this.dataFilterPermiss = choosed.dataFilterPermiss
      //当“是否过滤数据权限dataFilterPermiss”配置为否， “是否显示客户管理员customAdmin” 配置为是时，下拉列表底部不显示“显示全部处理人”按钮。
      this.showAllTreeBtn = !(
        choosed.dataFilterPermiss === '0' && choosed.customAdmin === '1'
      )
      this.alarmFlowId = choosed.flowId
      this.userType = choosed.userType
      let a = {}
      a.flowId = choosed.flowId
      a.linkId = val
      a.orderId = this.alarmDisposeData.alarmInfo.order.warningOrderId
      //赋值获取人员数的参数
      this.getFlowUserParams = {
        item: a,
        recordsInfo:
          this.alarmDisposeData.title === '回退'
            ? this.alarmDisposeData.alarmInfo.records
            : [],
      }
    },
    /**
     * 赋值处置人员树返回的数据
     */
    dispatchCheckChange(param) {
      this.processDTOList = param.processDTOList
      this.dispatchNameValue = param.dispatchNameValue
    },
    /**
     * 常用语判断弹窗高度
     */
    isPhrasesUnfold(val) {
      this.isUnfold = val
    },
    /**
     * 上传组件回调
     */
    changeFile(files, flag) {
      if (flag === 1) {
        this.$message({
          type: 'success',
          message: '上传成功',
        })
      }
      if (flag === -1) {
        this.$message({
          type: 'success',
          message: '删除成功',
        })
      }
      this.fileList = files.map(it => {
        return {
          fileId: it.fileId,
          fileName: it.fileName,
          fileUrl: it.fileUrl,
          coverUrl: it.coverUrl,
          fileType: it.type, //1:图片 2:视频 3:文件
        }
      })
    },
    /**
     * 改变加载等待状态
     */
    changeLoading() {
      this.dataLoading = false
    },
    /**
     * 确认提交
     */
    toSubmit() {
      if (this.dataLoading) {
        return false
      }
      if (this.alarmDisposeData.title !== '回退') {
        this.toSubmitProcess(this.alarmDisposeData.title)
      } else {
        this.toRollBack()
      }
    },
    getApprovalListParam() {
      if (this.propData.title === '回退') {
        return {
          catCode: 'ROLLBACKFLAG',
        }
      }
      if (this.propData.title === '转派') {
        return {
          catCode: 'TURNFLAG',
        }
      }
      return
    },
    /**
     * 取消提交
     */
    toClose() {
      this.close()
    },
    getLinkLabel() {
      if (this.propData.title === '回退') {
        return '回退环节'
      } else if (this.propData.title === '转派') {
        return '转派环节'
      } else {
        return '下一环节'
      }
    },
    /**
     * 回退流转
     */
    toRollBack() {
      let alarmInfo = this.alarmDisposeData.alarmInfo
      if (this.nextLink === '') {
        //校验选择回退环节
        this.$message({
          type: 'error',
          message: '请选择回退环节',
        })
        return false
      }
      if (this.dispatchNameValue.length === 0) {
        //校验处置人
        this.$message({
          type: 'error',
          message: '请选择处理人',
        })
        return false
      }
      let params = {
        orderId: alarmInfo.order.warningOrderId,
        warningOrderId: alarmInfo.order.warningOrderId,
        flowId: alarmInfo.order.flowId, //流程ID
        currentLink: alarmInfo.order.linkId, //当前环节ID
        linkId: this.linkId, //要回退的环节id
        linkName: this.linkName,
        realLinkType: this.realLinkType, //要回退的真实环节id
        msgNotifyType: this.msgNotifyType, //通知方式
        remark: this.remarksText, //说明
        orderStatus: alarmInfo.order.orderStatus, //订单状态（与linkId相同）
        flowLinkType: '2', //工单流转类型 0 默认 1转派 2回退
        processDTOList: this.processDTOList, //人员入参
        timeLimit: this.timeLimit,
        enableAcceptanceMode: alarmInfo.order.enableAcceptanceMode, //受理状态（取详情接口返回的事件该字段（0：不开启 1：开启（待受理）2：已受理 3：受理完成）
        timeLimitAcceptance: this.timeLimitAcceptance, //受理时限
        attrId: this.linkId, //流程环节id
        nextEnableAcceptanceMode: this.nextEnableAcceptanceMode, //下一环节是否开启受理状态（（0:不开启 1:开启））
      }
      if (alarmInfo.order?.changeAddCodeName?.isChange) {
        params = this.toAddressData(params, alarmInfo)
      }
      api
        .post(`${this.$env.VUE_APP_REQ_PREFIX_PLAT}/event/rollBack`, params)
        .then(res => {
          if (res.code == 200) {
            this.$message({
              type: 'success',
              message: res.msg,
            })
            this.okCalBack()
          } else {
            this.$message({
              type: 'error',
              message: res.msg || '回退流转失败！',
            })
          }
        })
    },
    /**
     * 正常工单流转 真警、调度、处置 转派
     */
    toSubmitProcess(type) {
      if (this.nextLink === '') {
        //校验选择环节
        this.$message({
          type: 'error',
          message: `请选择${this.linkLabel}`,
        })
        return false
      }
      if (this.dispatchNameValue.length === 0) {
        //校验处置人
        this.$message({
          type: 'error',
          message: '请选择处理人',
        })
        return false
      }
      if (type === '处置' && this.fileList?.length < 1) {
        //校验处置环节附件上传
        this.$message({
          type: 'error',
          message: '请先完善附件上传信息',
        })
        return false
      }
      let alarmInfo = this.alarmDisposeData.alarmInfo
      switch (type) {
        case '真警':
          this.toJudgeTrue(alarmInfo)
          break
        case '调度':
          this.toDispatch(alarmInfo)
          break
        case '处置':
          this.toDisposal(alarmInfo)
          break
        case '转派':
          this.toReassign(alarmInfo)
          break
        default:
          return false
      }
    },
    /**
     * 真警流转入参处理
     */
    toJudgeTrue(alarmInfo) {
      let params = {
        orderId: alarmInfo.order.warningOrderId,
        warningOrderId: alarmInfo.order.warningOrderId,
        flowId: alarmInfo.order.flowId, //流程ID
        currentLink: alarmInfo.order.linkId, //当前环节ID
        msgNotifyType: this.msgNotifyType, //通知方式
        isNextLink: this.isNextLink, //是否流转1：流转下个环节  0：不流转 isNextLink除了需要完成会签的时候是0  其他时候都是1
        remark: this.remarksText, //说明
        realLinkType: this.realLinkType, // 真实环节类型(1研判2调度4处置5核实6办结)
        linkId: this.linkId, //下一环节
        linkName: this.linkName,
        orderStatus: this.linkId, //订单状态（与linkId相同）
        enableAcceptanceMode: alarmInfo.order.enableAcceptanceMode, //受理状态（取详情接口返回的事件该字段（0：不开启 1：开启（待受理）2：已受理 3：受理完成）
        timeLimitAcceptance: this.timeLimitAcceptance, //受理时限
        attrId: this.linkId, //流程环节id
        timeLimit: this.timeLimit,
        nextEnableAcceptanceMode: this.nextEnableAcceptanceMode, //下一环节是否开启受理状态（（0:不开启 1:开启））
        judgeResult: '1',
        flowLinkType: '0', //工单流转类型 0 默认 1转派 2回退
        processDTOList: this.processDTOList, //人员入参
        warningTypeId: alarmInfo.order.warningTypeId, //告警类型
      }
      if (alarmInfo.order?.changeAddCodeName?.isChange) {
        params = this.toAddressData(params, alarmInfo)
      }
      this.doSubmitProcess(params)
    },
    /**
     * 调度入参处理
     */
    toDispatch(alarmInfo) {
      let params = {
        orderId: alarmInfo.order.warningOrderId,
        warningOrderId: alarmInfo.order.warningOrderId,
        flowId: alarmInfo.order.flowId, //流程ID
        currentLink: alarmInfo.order.linkId, //当前环节ID
        msgNotifyType: this.msgNotifyType, //通知方式
        isNextLink: this.isNextLink, //是否流转1：流转下个环节  0：不流转 isNextLink除了需要完成会签的时候是0  其他时候都是1
        remark: this.remarksText, //说明
        realLinkType: this.realLinkType, // 真实环节类型(1研判2调度4处置5核实6办结)
        linkId: this.linkId, //下一环节
        linkName: this.linkName,
        orderStatus: this.linkId, //订单状态（与linkId相同）
        enableAcceptanceMode: alarmInfo.order.enableAcceptanceMode, //受理状态（取详情接口返回的事件该字段（0：不开启 1：开启（待受理）2：已受理 3：受理完成）
        timeLimitAcceptance: this.timeLimitAcceptance, //受理时限
        timeLimit: this.timeLimit,
        attrId: this.linkId, //流程环节id
        nextEnableAcceptanceMode: this.nextEnableAcceptanceMode, //下一环节是否开启受理状态（（0:不开启 1:开启））
        judgeResult: '1',
        flowLinkType: '0', //工单流转类型 0 默认 1转派 2回退
        processDTOList: this.processDTOList, //人员入参
        warningTypeId: alarmInfo.order.warningTypeId, //告警类型
      }
      if (alarmInfo.order?.changeAddCodeName?.isChange) {
        params = this.toAddressData(params, alarmInfo)
      }
      let orderFileDTOList = []
      this.fileList.forEach(item => {
        let a = {}
        a.resourceId = item.fileId
        a.resourceUrl = item.fileUrl
        a.fileName = item.fileName
        a.resourceType = item.fileType //1:图片 2:视频 3:文件
        orderFileDTOList.push(a)
      })
      params.orderFileDTOList = orderFileDTOList
      this.doSubmitProcess(params)
    },
    /**
     * 处置环节入参处理
     */
    toDisposal(alarmInfo) {
      let params = {
        orderId: alarmInfo.order.warningOrderId,
        warningOrderId: alarmInfo.order.warningOrderId,
        flowId: alarmInfo.order.flowId, //流程ID
        currentLink: alarmInfo.order.linkId, //当前环节ID
        msgNotifyType: this.msgNotifyType, //通知方式
        isNextLink: this.isNextLink, //是否流转1：流转下个环节  0：不流转 isNextLink除了需要完成会签的时候是0  其他时候都是1
        remark: this.remarksText, //说明
        realLinkType: this.realLinkType, // 真实环节类型(1研判2调度4处置5核实6办结)
        linkId: this.linkId, //下一环节
        linkName: this.linkName,
        orderStatus: this.linkId, //订单状态（与linkId相同）
        enableAcceptanceMode: alarmInfo.order.enableAcceptanceMode, //受理状态（取详情接口返回的事件该字段（0：不开启 1：开启（待受理）2：已受理 3：受理完成）
        timeLimitAcceptance: this.timeLimitAcceptance, //受理时限
        timeLimit: this.timeLimit,
        attrId: this.linkId, //流程环节id
        nextEnableAcceptanceMode: this.nextEnableAcceptanceMode, //下一环节是否开启受理状态（（0:不开启 1:开启））
        judgeResult: '1',
        flowLinkType: '0', //工单流转类型 0 默认 1转派 2回退
        processDTOList: this.processDTOList, //人员入参
        warningTypeId: alarmInfo.order.warningTypeId, //告警类型
      }
      let processImgList = []
      this.fileList.forEach(item => {
        let a = {}
        a.resourceId = item.fileId
        a.resourceUrl = item.fileUrl
        a.fileName = item.fileName
        a.resourceType = item.fileType //1:图片 2:视频 3:文件
        processImgList.push(a)
      })
      params.processOrderVOList = []
      params.processOrderVOList[0] = {}
      params.processOrderVOList[0].orderStatus = '1'
      params.processOrderVOList[0].processOpinion = this.remarksText //处置说明
      params.processOrderVOList[0].processImgList = processImgList //处置附件入参
      if (alarmInfo.order?.changeAddCodeName?.isChange) {
        params = this.toAddressData(params, alarmInfo)
      }
      this.doSubmitProcess(params)
    },
    /**
     * 转派入参处理
     */
    toReassign(alarmInfo) {
      let params = {
        beforLink: alarmInfo.order.linkId,
        flowTemplate: alarmInfo.order.flowTemplate,
        warningOrderId: alarmInfo.order.warningOrderId,
        enableAcceptanceMode: alarmInfo.order.enableAcceptanceMode, //受理状态（取详情接口返回的事件该字段（0：不开启 1：开启（待受理）2：已受理 3：受理完成）
        alarmFlowId: this.alarmFlowId, //告警流程id
        userType: this.userType, //用户类型
        curLink: this.linkId,
        linkName: this.linkName,
        realLinkType: this.realLinkType,
        msgNotifyType: this.msgNotifyType, //通知方式
        timeLimitAcceptance: this.timeLimitAcceptance, //受理时限
        timeLimit: this.timeLimit,
        attrId: this.linkId, //流程环节id
        nextEnableAcceptanceMode: this.nextEnableAcceptanceMode, //下一环节是否开启受理状态（（0:不开启 1:开启））
        processDTOList: this.processDTOList, //人员入参
        remark: this.remarksText, //说明
      }
      if (alarmInfo.order?.changeAddCodeName?.isChange) {
        params = this.toAddressData(params, alarmInfo)
      }
      this.doSubmitReassign(params)
    },
    /**
     * 修改地址时处理地址数据
     */
    toAddressData(params, alarmInfo) {
      // 修改数据处理
      params.gridId = alarmInfo.order.gridId //网格ID
      params.gridName = alarmInfo.order.gridName //网格名称
      params.checkGridFlag = 1 //是否修改网格信息和水平方位角
      if (
        alarmInfo.order.warningSource === '1' ||
        alarmInfo.order.warningSource === '4'
      ) {
        params.horiAzimuthAngle = alarmInfo.order.horiAzimuthAngle //水平方位角
      }
      params.address = alarmInfo.order.changeAddCodeName.address //详细地址名称
      params.longitude = alarmInfo.order.changeAddCodeName.longitude //经度名称
      params.latitude = alarmInfo.order.changeAddCodeName.latitude //维度名称
      params.provinceId = alarmInfo.order.changeAddCodeName.provinceId //省份id
      params.provinceName = alarmInfo.order.changeAddCodeName.provinceName //省份名称
      params.cityId = alarmInfo.order.changeAddCodeName.cityId //地市名称
      params.cityName = alarmInfo.order.changeAddCodeName.cityName //地市名称
      params.countyId = alarmInfo.order.changeAddCodeName.countyId //区县编码
      params.countyName = alarmInfo.order.changeAddCodeName.countyName //区县名称
      return params
    },
    /**
     * 提交工单流转
     */
    doSubmitProcess(params) {
      api
        .post(
          `${this.$env.VUE_APP_REQ_PREFIX_PLAT}/event/submitProcess`,
          params
        )
        .then(res => {
          if (res.code == 200) {
            this.$message({
              type: 'success',
              message: res.msg,
            })
            this.okCalBack()
          } else {
            this.$message({
              type: 'error',
              message: res.msg || '提交工单流转失败！',
            })
          }
        })
    },
    /**
     * 提交转派
     */
    doSubmitReassign(params) {
      api
        .post(`${this.$env.VUE_APP_REQ_PREFIX_PLAT}/event/reassignment`, params)
        .then(res => {
          if (res.code == 200) {
            this.$parent.refreshInfoAndList()
            this.$message({
              type: 'success',
              message: res.msg,
            })
          } else {
            this.$message({
              type: 'error',
              message: res.msg || '提交转派失败！',
            })
          }
        })
    },
  },
}
</script>
<style lang='less' src='./index.less' />
