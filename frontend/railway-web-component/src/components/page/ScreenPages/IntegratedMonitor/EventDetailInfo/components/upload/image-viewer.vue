<template>
  <!-- 使用过渡效果包裹图片查看器 -->
  <transition name="viewer-fade">
    <!-- 图片查看器的外层容器，设置tabindex使其可聚焦 -->
    <div tabindex="-1" ref="el-image-viewer__wrapper" class="el-image-viewer__wrapper" :style="{ 'z-index': viewerZIndex }">
      <!-- 遮罩层，点击时触发handleMaskClick方法 -->
      <div class="el-image-viewer__mask" @click.self="handleMaskClick"></div>
      <!-- 关闭按钮，点击时触发hide方法 -->
      <span class="el-image-viewer__btn el-image-viewer__close" @click="hide">
        <i class="el-icon-close"></i>
      </span>
      <!-- 左右箭头按钮，用于切换图片 -->
      <template v-if="!isSingle">
        <!-- 上一张图片按钮，点击时触发prev方法 -->
        <span
          class="el-image-viewer__btn el-image-viewer__prev"
          :class="{ 'is-disabled': !infinite && isFirst }"
          @click="prev">
          <i class="el-icon-arrow-left"/>
        </span>
        <!-- 下一张图片按钮，点击时触发next方法 -->
        <span
          class="el-image-viewer__btn el-image-viewer__next"
          :class="{ 'is-disabled': !infinite && isLast }"
          @click="next">
          <i class="el-icon-arrow-right"/>
        </span>
      </template>
      <!-- 操作按钮区域 -->
      <div class="el-image-viewer__btn el-image-viewer__actions">
        <div class="el-image-viewer__actions__inner">
          <!-- 显示当前图片索引 -->
          <span>{{ index + 1 }}</span>
          <!-- 缩小按钮，点击时触发handleActions方法 -->
          <i class="el-icon-zoom-out" @click="handleActions('zoomOut')"></i>
          <!-- 放大按钮，点击时触发handleActions方法 -->
          <i class="el-icon-zoom-in" @click="handleActions('zoomIn')"></i>
          <!-- 模式切换按钮，点击时触发toggleMode方法 -->
          <i :class="mode.icon" @click="toggleMode"></i>
          <!-- 逆时针旋转按钮，点击时触发handleActions方法 -->
          <i class="el-icon-refresh-left" @click="handleActions('anticlocelise')"></i>
          <!-- 顺时针旋转按钮，点击时触发handleActions方法 -->
          <i class="el-icon-refresh-right" @click="handleActions('clocelise')"></i>
        </div>
      </div>
      <!-- 图片或视频展示区域 -->
      <div class="el-image-viewer__canvas">
        <template v-for="(url, i) in urlList">
          <!-- 当前图片展示 -->
          <img
            v-if="i === index && isImage"
            alt="图片"
            ref="img"
            class="el-image-viewer__img"
            :src="url.fileUrl"
            :style="imgStyle"
            referrerpolicy='no-referrer'
            @load="handleImgLoad"
            @error="handleImgError"
            @mousedown="handleMouseDown">
          <!-- 当前视频展示 -->
          <video
            controls
            autoplay
            v-else-if='i === index && isVideo'
            ref="video"
            :src="url.fileUrl"
            :style="imgStyle"
            class="el-image-viewer__video"
            referrerpolicy='no-referrer'
            @load="handleImgLoad"
            @error="handleImgError"
            @mousedown="handleMouseDown"
          />
          <!-- 非图片或视频文件的展示 -->
          <img
            v-else
            alt='下载'
            :src='require(`@/assets/images/alarmEvent/alarm/download_doc.png`)'
            class='file-image'
            @click='downloadFile(url.fileUrl,url.fileName)'
            :title='url.fileName'
          />
        </template>
      </div>
    </div>
  </transition>
</template>
<script>
import { downloadFile } from '@/api/service/common';
import { off, on } from 'element-ui/src/utils/dom';
import { PopupManager } from 'element-ui/src/utils/popup';
import { isFirefox, rafThrottle } from 'element-ui/src/utils/util';

// 定义查看模式
const Mode = {
  CONTAIN: {
    name: 'contain',
    icon: 'el-icon-full-screen'
  },
  ORIGINAL: {
    name: 'original',
    icon: 'el-icon-c-scale-to-original'
  }
};

// 根据浏览器类型设置鼠标滚轮事件名称
const mousewheelEventName = isFirefox() ? 'DOMMouseScroll' : 'mousewheel';
export default {
  name: 'image-viewer',
  props: {
    // 图片或视频的URL列表
    urlList: {
      type: Array,
      default: () => []
    },
    // 设置组件的z-index
    zIndex: {
      type: Number,
      default: 30000
    },
    // 切换图片时的回调函数
    onSwitch: {
      type: Function,
      default: () => {
        console.log('switch');
      }
    },
    // 关闭组件时的回调函数
    onClose: {
      type: Function,
      default: () => {
        console.log('close');
      }
    },
    // 初始显示的图片索引
    initialIndex: {
      type: Number,
      default: 0
    },
    // 是否将组件添加到body中
    appendToBody: {
      type: Boolean,
      default: true
    },
    // 是否允许通过点击遮罩层关闭组件
    maskClosable: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      index: this.initialIndex, // 当前显示的图片索引
      isShow: false, // 是否显示组件
      infinite: true, // 是否无限循环切换图片
      loading: false, // 图片是否正在加载
      mode: Mode.CONTAIN, // 当前查看模式
      transform: { // 图片变换属性
        scale: 1,
        deg: 0,
        offsetX: 0,
        offsetY: 0,
        enableTransition: false
      }
    };
  },
  computed: {
    // 是否只有一张图片
    isSingle() {
      return this.urlList.length <= 1;
    },
    // 是否为第一张图片
    isFirst() {
      return this.index === 0;
    },
    // 是否为最后一张图片
    isLast() {
      return this.index === this.urlList.length - 1;
    },
    // 当前图片的URL
    currentImg() {
      return this.urlList[this.index].fileUrl;
    },
    // 图片样式
    imgStyle() {
      const { scale, deg, offsetX, offsetY, enableTransition } = this.transform;
      const style = {
        transform: `scale(${scale}) rotate(${deg}deg)`,
        transition: enableTransition ? 'transform .3s' : '',
        'margin-left': `${offsetX}px`,
        'margin-top': `${offsetY}px`
      };
      if (this.mode === Mode.CONTAIN) {
        style.maxWidth = style.maxHeight = '100%';
      }
      return style;
    },
    // 计算图片查看器的z-index
    viewerZIndex() {
      const nextZIndex = PopupManager.nextZIndex();
      return this.zIndex > nextZIndex ? this.zIndex : nextZIndex;
    },
    // 当前文件是否为图片
    isImage() {
      return this.urlList[this.index].type === '1'
    },
    // 当前文件是否为视频
    isVideo() {
      return this.urlList[this.index].type === '2'
    }
  },
  watch: {
    // 监听图片索引变化
    index: {
      handler: function(val) {
        this.reset(); // 重置图片变换状态
        this.onSwitch(val); // 调用切换回调函数
      }
    },
    // 监听当前图片URL变化
    currentImg(val) {
      if(!this.isImage) {
        return false;
      }
      this.$nextTick(_ => {
        const $img = this.$refs.img[0];
        if ($img && !$img.complete) {
          this.loading = true; // 设置加载状态
        }
      });
    }
  },
  methods: {
    downloadFile, // 下载文件方法
    /**
     * 隐藏组件实例
     * @summary 调用设备卸载支持函数并触发关闭事件
     */
    hide() {
      this.deviceSupportUninstall();
      this.onClose();
    },
    /**
     * 设置设备安装支持
     * @summary 初始化键盘和鼠标滚轮的事件监听器
     */
    deviceSupportInstall() {
      /* 设置键盘事件处理函数 */
      this._keyDownHandler = e => {
        e.stopPropagation();
        const keyCode = e.keyCode;
        /* 根据按键执行相应操作 */
        switch (keyCode) {
          // ESC
          case 27:
            this.hide();
            break;
          // SPACE
          case 32:
            this.toggleMode();
            break;
          // LEFT_ARROW
          case 37:
            this.prev();
            break;
          // UP_ARROW
          case 38:
            this.handleActions('zoomIn');
            break;
          // RIGHT_ARROW
          case 39:
            this.next();
            break;
          // DOWN_ARROW
          case 40:
            this.handleActions('zoomOut');
            break;
        }
      };
      /* 设置鼠标滚轮事件处理函数 */
      this._mouseWheelHandler = rafThrottle(e => {
        const delta = e.wheelDelta ? e.wheelDelta : -e.detail;
        /* 根据滚动方向执行相应操作 */
        if (delta > 0) {
          this.handleActions('zoomIn', {
            zoomRate: 0.015,
            enableTransition: false
          });
        } else {
          this.handleActions('zoomOut', {
            zoomRate: 0.015,
            enableTransition: false
          });
        }
      });
      /* 添加事件监听器 */
      on(document, 'keydown', this._keyDownHandler);
      on(document, mousewheelEventName, this._mouseWheelHandler);
    },
    /**
     * 移除设备支持
     * @summary 取消键盘和鼠标滚轮的事件监听器并清除处理函数
     */
    deviceSupportUninstall() {
      /* 取消事件监听器 */
      off(document, 'keydown', this._keyDownHandler);
      off(document, mousewheelEventName, this._mouseWheelHandler);
      /* 清除处理函数引用 */
      this._keyDownHandler = null;
      this._mouseWheelHandler = null;
    },
    /**
     * 图片加载完成的事件处理函数
     * @param {Event} e - 加载完成事件对象
     */
    handleImgLoad(e) {
      this.loading = false;
    },
    /**
     * 图片加载失败的事件处理函数
     * @param {Event} e - 加载失败事件对象
     */
    handleImgError(e) {
      this.loading = false;
      /* 设置图片 alt 属性为“加载失败” */
      e.target.alt = '加载失败';
    },
    /**
     * 鼠标按下事件的处理函数
     * @param {Event} e - 鼠标按下事件对象
     */
    handleMouseDown(e) {
      /* 如果图片正在加载或非左键按下，则不执行操作 */
      if (this.loading || e.button !== 0) {
        return;
      }
      /* 获取当前变换的偏移量 */
      const {
        offsetX,
        offsetY
      } = this.transform;
      /* 记录鼠标按下的页面坐标 */
      const startX = e.pageX;
      const startY = e.pageY;
      /* 设置拖动处理函数 */
      this._dragHandler = rafThrottle(ev => {
        this.transform.offsetX = offsetX + ev.pageX - startX;
        this.transform.offsetY = offsetY + ev.pageY - startY;
      });
      /* 添加鼠标移动和松开的事件监听器 */
      on(document, 'mousemove', this._dragHandler);
      on(document, 'mouseup', ev => {
        off(document, 'mousemove', this._dragHandler);
      });
      e.preventDefault();
    },
    /**
     * 遮罩层点击事件的处理函数
     * @summary 如果允许通过遮罩层关闭，则隐藏组件实例
     */
    handleMaskClick() {
      if (this.maskClosable) {
        this.hide();
      }
    },
    /**
     * 重置图片变换状态
     * @summary 将变换属性重置为初始状态
     */
    reset() {
      /* 设置初始变换状态 */
      this.transform = {
        scale: 1,
        deg: 0,
        offsetX: 0,
        offsetY: 0,
        enableTransition: false
      };
      /* 如果是视频，则重置视频控制的样式 */
      if (this.isVideo) {
        this.$nextTick(() => {
          const $video = this.$refs.video[0];
          $video.style.removeProperty('--controls-rotate');
          $video.style.removeProperty('--controls-width');
          $video.style.removeProperty('--controls-height');
        });
      }
    },
    /**
     * 切换模式
     * @summary 根据当前模式切换到下一个模式，并重置图片变换状态
     */
    toggleMode() {
      if (this.loading) {
        return;
      }
      const modeNames = Object.keys(Mode);
      const modeValues = Object.values(Mode);
      const index = modeValues.indexOf(this.mode);
      const nextIndex = (index + 1) % modeNames.length;
      this.mode = Mode[modeNames[nextIndex]];
      this.reset();
    },
    /**
     * 显示上一张图片
     * @summary 如果不是第一张图片或设置为无限滚动，则切换到上一张图片
     */
    prev() {
      if (this.isFirst && !this.infinite) {
        return;
      }
      const len = this.urlList.length;
      this.index = (this.index - 1 + len) % len;
      this.$emit('update:initialIndex', this.index);
    },
    /**
     * 显示下一张图片
     * @summary 如果不是最后一张图片或设置为无限滚动，则切换到下一张图片
     */
    next() {
      if (this.isLast && !this.infinite) {
        return;
      }
      const len = this.urlList.length;
      this.index = (this.index + 1) % len;
      this.$emit('update:initialIndex', this.index);
    },
    /**
     * 执行图片变换操作
     * @param {string} action - 变换操作类型
     * @param {Object} options - 变换操作的可选参数
     * @summary 根据操作类型和参数更新图片的变换状态
     */
    handleActions(action, options = {}) {
      if (this.loading) {
        return;
      }
      /* 合并默认参数和传入参数 */
      const {
        zoomRate,
        rotateDeg,
        enableTransition
      } = {
        zoomRate: 0.2,
        rotateDeg: 90,
        enableTransition: true,
        ...options
      };
      const { transform } = this;
      /* 根据操作类型执行相应的变换操作 */
      switch (action) {
        case 'zoomOut':
          if (transform.scale > 0.2) {
            transform.scale = parseFloat((transform.scale - zoomRate).toFixed(3));
          }
          break;
        case 'zoomIn':
          transform.scale = parseFloat((transform.scale + zoomRate).toFixed(3));
          break;
        case 'clocelise':
          transform.deg += rotateDeg;
          this.rotateVideo();
          break;
        case 'anticlocelise':
          transform.deg -= rotateDeg;
          this.rotateVideo();
          break;
      }
      transform.enableTransition = enableTransition;
    },
    /**
     * 旋转视频
     * @summary 根据图片的旋转角度更新视频控制的样式
     */
    rotateVideo() {
      if (!this.isVideo) {
        return false;
      }
      const $video = this.$refs.video[0];
      const width = $video.clientWidth;
      const height = $video.clientHeight;
      /* 根据旋转角度更新视频控制的样式 */
      $video.style.setProperty('--controls-rotate', -this.transform.deg + 'deg');
      if (this.transform.deg % 180 === 0) {
        $video.style.setProperty('--controls-width', width + 'px');
        $video.style.setProperty('--controls-height', height + 'px');
      } else {
        $video.style.setProperty('--controls-width', height + 'px');
        $video.style.setProperty('--controls-height', width + 'px');
      }
    }
  },
  mounted() {
    this.deviceSupportInstall();
    if (this.appendToBody) {
      document.body.appendChild(this.$el);
    }
    // add tabindex then wrapper can be focusable via Javascript
    // focus wrapper so arrow key can't cause inner scroll behavior underneath
    this.$refs['el-image-viewer__wrapper'].focus();
  },
  destroyed() {
    // if appendToBody is true, remove DOM node after destroy
    if (this.appendToBody && this.$el && this.$el.parentNode) {
      this.$el.parentNode.removeChild(this.$el);
    }
  }
};
</script>
<style lang="less" scoped>
.el-image-viewer__actions {
  border-radius: 22px;
}
.el-image-viewer__close{
  border-radius: 50%;
  background: #606266;
  font-size: 24px;
  &:hover{
    opacity: 0.6;
  }
  &:active{
    opacity: 0.8;
  }
}
.el-image-viewer__prev, .el-image-viewer__next{
  width: 44px;
  height: 44px;
  border-radius: 50%;
  background: #606266;
  font-size: 24px;
  &:hover{
    opacity: 0.6;
  }
  &:active{
    opacity: 0.8;
  }
}
video::-webkit-media-controls-enclosure{
  position: relative;
}
video::-webkit-media-controls-panel{
  width: var(--controls-width, 100%);
  height: var(--controls-height, 100%);
  transform: translate(-50%, -50%) rotate(var(--controls-rotate, 0));
  position: absolute;
  left: 50%;
  top: 50%;
  transition: all;
}
</style>
