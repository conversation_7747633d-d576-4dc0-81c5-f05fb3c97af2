<!--
 * @Description  : 处置
 * <AUTHOR> wnj
 * @Date         : 2023-12-11 10:58:52
 * @LastEditors  : wnj
 * @LastEditTime : 2023-12-26 14:50:20
 * @FilePath     :  / src / components / page / alarmEvent / components / alarmRight / alarmDisposeDialog.vue
-->
<!--  告警各环节处理弹窗  -->
<template>
  <!-- 使用 CtlPanel 组件包裹内容，设置关闭事件 -->
  <CtlPanel class='evt-handle-wrap alarm-dispatch-outter false-alarm-wrap' :close='toClose'>
    <!-- 设置弹窗标题 -->
    <template v-slot:title>
      {{ alarmDisposeData.title }}
    </template>
    <!-- 设置弹窗内容 -->
    <template v-slot:content>
      <div class='dispatch-con evt-handle-form el-form'>
        <!-- 环节说明 -->
        <div class='dispose-item-box'>
          <div class='dispose-item-left'>
            <div class='dispose-item-label'>
              误报说明
            </div>
          </div>
          <div class='dispose-item-right'>
            <!-- 使用 AlarmPhrases 组件，绑定处理说明文本 -->
            <AlarmPhrases
              v-model='remarksText'
              placeholder='请输入内容'
              :orderStatus='alarmDisposeData.alarmInfo.order.orderStatus'
              :maxLength='200'
              @update='isPhrasesUnfold'
              :isUnfold.sync='isUnfold'
            />
          </div>
        </div>
        <!-- 告警压制条件：误报且告警来源为 Ai 或摄像机 -->
        <div class='dispose-item-box' style='flex-direction: column'
             v-if="(alarmDisposeData.alarmInfo.order.warningSource === '1' || alarmDisposeData.alarmInfo.order.warningSource === '4')">
          <div class='dispose-item-left-suppress'>
            <div class='dispose-item-label'>是否告警压制</div>
            <div class='dispose-tip'>
              <!-- 提示信息 -->
              <div c-tip='如开启告警压制，将按照同一设备同一告警类型在规定时间内进行压制处理，压制的告警将放到回收站中。'
                   c-tip-placement='top' class='text-source'></div>
            </div>
            <div class='suppress-switch-box'>
              <!-- 使用 el-switch 组件控制告警压制开关 -->
              <el-switch
                v-model='isSuppress'
                active-color='#3A77E5'
                inactive-color='#a2a3a3'>
              </el-switch>
            </div>
          </div>
          <!-- 使用 AlarmSuppress 组件，控制告警压制 -->
          <AlarmSuppress
            ref='AlarmSuppress'
            v-if='isSuppress'
          />
        </div>
      </div>
      <!-- 提交和取消按钮 -->
      <div class='dispatch-buttons'>
        <div class='dispatch-button dispatch-button-blue' @click='toSubmit'><span>确定</span></div>
        <div class='dispatch-button' @click='toClose'><span>取消</span></div>
      </div>
    </template>
  </CtlPanel>
</template>
<script>
import api from '@/api';
import CtlPanel from '@/components/page/ScreenPages/IntegratedMonitor/CtlPanel/index.vue';
import AlarmPhrases from '../components/alarm-phrases.vue';
import AlarmSuppress from '../components/alarm-suppress.vue';
export default {
  components: {
    CtlPanel,
    AlarmSuppress,
    AlarmPhrases
  },
  props: {
    propData: {
      type: Object,
      default: {}
    },
    close: {
      type: Function,
      default: () => {
        console.log('close');
      }
    },
    okCalBack: {
      type: Function,
      default: () => {
        console.log('okCalBack');
      }
    }
  },
  data() {
    return {
      alarmDisposeData: this.propData, //告警处理数据
      linkId: '', //下一环节id
      linkName: '', //下一环节名称
      realLinkType: '', //真实环节id
      msgNotifyType: '', //通知方式
      nextEnableAcceptanceMode: '', //下一环节是否开启受理
      timeLimitAcceptance: '', //下一环节受理时间
      processDTOList: [],//处理人入参
      remarksText: '',//流程处理时的处理说明
      isUnfold: false,//展开常用语 弹窗变大
      isSuppress: false//是否开启压制
    };
  },
  methods: {
    /**
     * 确认提交
     *
     */
    toSubmit() {
      let alarmInfo = this.alarmDisposeData.alarmInfo;
      this.toJudegeFalse(alarmInfo);
    },
    /**
     * 常用语判断弹窗高度
     */
    isPhrasesUnfold(val) {
      this.isUnfold = val;
    },
    /**
     * 取消提交
     *
     */
    toClose() {
      this.close();
    },
    /**
     * 误报流转
     *
     */
    toJudegeFalse(alarmInfo) {
      let params = {
        orderId: alarmInfo.order.warningOrderId,
        warningOrderId: alarmInfo.order.warningOrderId,
        flowId: alarmInfo.order.flowId,//流程ID
        currentLink: alarmInfo.order.linkId,//当前环节ID
        // isSign: '0',//是否会签
        msgNotifyType: this.msgNotifyType,//通知方式
        isNextLink: this.isNextLink,//是否流转1：流转下个环节  0：不流转 isNextLink除了需要完成会签的时候是0  其他时候都是1
        remark: this.remarksText,//说明
        realLinkType: this.realLinkType,// 真实环节类型(1研判2调度4处置5核实6办结)
        linkId: '0',//下一环节
        linkName: '假警完结',
        orderStatus: '0',//订单状态（与linkId相同）
        enableAcceptanceMode: alarmInfo.order.enableAcceptanceMode, //受理状态（取详情接口返回的事件该字段（0：不开启 1：开启（待受理）2：已受理 3：受理完成）
        timeLimitAcceptance: this.timeLimitAcceptance, //受理时限
        timeLimit: this.timeLimit,
        attrId: this.linkId, //流程环节id
        nextEnableAcceptanceMode: this.nextEnableAcceptanceMode, //下一环节是否开启受理状态（（0:不开启 1:开启））
        judgeResult: '0',
        flowLinkType: '0',//工单流转类型 0 默认 1转派 2回退
        processDTOList: this.processDTOList, //人员入参
        warningTypeId: alarmInfo.order.warningTypeId//告警类型
      };
      if (this.isSuppress) {//开启告警压制
        this.doSuppress(alarmInfo);
      }
      this.doSubmitProcess(params);
    },
    /**
     *
     * 处理压制数据提交
     */
    doSuppress(alarmInfo) {
      let interceptTime = 0;
      let suppressToday = false;
      let type = this.$refs.AlarmSuppress.suppressTypeValue;
      let day = this.$refs.AlarmSuppress.dayValue;
      let hour = this.$refs.AlarmSuppress.hourValue;
      let minute = this.$refs.AlarmSuppress.minuteModel;
      if (type === 1) {//5分钟压制
        interceptTime = 5;
      } else if (type === 2) {//1小时压制
        interceptTime = 60;
      } else if (type === 3) {//今日不告警
        interceptTime = this.$refs.AlarmSuppress.todaySuppressTime();
        suppressToday = true;
      } else {//自定义压制
        interceptTime = Number(day) * 24 * 60 + Number(hour) * 60 + Number(minute);
      }
      if (this.isSuppress) {
        this.goSuppress(alarmInfo.order.deviceCode, alarmInfo.order.warningTypeId, interceptTime, suppressToday);
      }
    },
    /**
     * 提交压制
     *
     */
    goSuppress(deviceCode, warningTypeId, interceptTime, noAlarmToday) {
      if (!deviceCode) {
        return false;
      }
      const params = {};
      params.deviceCode = deviceCode;
      params.warningTypeId = warningTypeId;
      params.interceptTime = interceptTime;
      params.noAlarmToday = noAlarmToday;
      api.post(
        `${this.$env.VUE_APP_REQ_PREFIX_PLAT}/event/createIntercept`,
        params
      ).then(res => {
        if (res.code !== 200) {
          this.$message({
            type: 'error',
            message: res.msg || '提交压制失败！'
          });
        }
      });
    },
    /**
     * 提交工单流转
     *
     */
    doSubmitProcess(params) {
      api.post(
        `${this.$env.VUE_APP_REQ_PREFIX_PLAT}/event/submitProcess`,
        params
      ).then(res => {
        if (res.code === 200) {
          this.$message({
            type: 'success',
            message: res.msg
          });
          this.okCalBack();
        } else {
          this.$message({
            type: 'error',
            message: res.msg || '提交工单流转失败！'
          });
        }
      });
    }
  }
};
</script>
<style lang='less' src='./index.less' />
