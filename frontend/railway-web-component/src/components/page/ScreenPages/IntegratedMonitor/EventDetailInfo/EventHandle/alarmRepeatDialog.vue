<!--
 * @Description  : 重复告警处理弹窗
 * <AUTHOR> wnj
 * @Date         : 2023-12-12 15:40:52
 * @LastEditors  : wnj
 * @LastEditTime : 2023-12-22 10:56:56
 * @FilePath     :  / src / components / page / alarmEvent / components / alarmRight / alarmRepeatDialog.vue
-->
<template>
  <!-- 使用 CtlPanel 组件作为弹窗的外层容器 -->
  <CtlPanel
    class="evt-handle-wrap .alarm-dispatch-outter alarm-repeat-outter el-form"
    :close="toClose"
  >
    <!-- 弹窗标题 -->
    <template v-slot:title>{{ alarmRepeatData.title }}</template>
    <!-- 弹窗内容 -->
    <template v-slot:content>
      <div class="dispatch-con" v-loading="dataLoading">
        <!-- 筛选条件 -->
        <el-row class="repeat-sift-box">
          <!-- 事件编号输入框 -->
          <div class="repreat-sift-item">
            <div class="sift-label">事件编号</div>
            <div class="sift-input">
              <el-input
                class="c-input"
                placeholder="请输入"
                clearable
                v-model="repeatParams.alarmId"
                @input="evtIdInputChange"
              ></el-input>
            </div>
          </div>
          <!-- 事件类型选择框 -->
          <div class="repreat-sift-item">
            <div class="sift-label">事件类型</div>
            <div class="sift-input">
              <el-select
                class="c-select"
                popper-class="c-select-dropdown"
                v-model="repeatParams.alarmType"
                clearable
                multiple
                collapse-tags
                placeholder="请选择"
                @change="alarmTypeChange"
              >
                <el-option
                  v-for="(item, index) in alarmTypeList"
                  :key="index"
                  :label="item.typeName"
                  :value="item.typeValue"
                ></el-option>
              </el-select>
            </div>
          </div>
          <!-- 时间范围选择器 -->
          <div class="repreat-sift-item">
            <div class="sift-label">时间范围</div>
            <div class="sift-date-picker">
              <el-date-picker
                :picker-options="pickerOptions"
                ref="alarmDataPicker"
                prefix-icon="iconfont icon-riqiicon-jp"
                class="c-date-editor dt-date-editor"
                popper-class="c-date-editor-picker dt-date-editor-picker"
                v-model="repeatParams.alarmDate"
                value-format="yyyy-MM-dd"
                type="daterange"
                :clearable="true"
                format="yyyy-MM-dd"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                @change="alarmRepeatDataChange"
              ></el-date-picker>
            </div>
          </div>
        </el-row>
        <!-- 表格数据展示 -->
        <el-row class="table-box-contain">
          <CommonTable
            ref="table"
            :height="190"
            :table-data="repeatTableData.rows"
            :highlightCurrentRow="true"
            :row-click="tableRowData"
          >
            <template v-slot:column>
              <!-- 表格列定义 -->
              <el-table-column prop="orderId" label="事件编号" show-overflow-tooltip width="120"></el-table-column>
              <el-table-column prop="alarmBody" label="事件标题" show-overflow-tooltip width="120"></el-table-column>
              <el-table-column prop="alarmTime" label="事发时间" width="160"></el-table-column>
              <el-table-column
                prop="warningTypeName"
                label="事件类型"
                show-overflow-tooltip
                width="100"
              ></el-table-column>
              <el-table-column prop="address" label="事发地点" show-overflow-tooltip width="150"></el-table-column>
              <el-table-column prop="orderStatusName" label="事件状态" width="80"></el-table-column>
            </template>
          </CommonTable>
        </el-row>
        <!-- 告警压制选项 -->
        <el-row
          class="dispose-item-left-suppress"
          v-if="alarmRepeatData.alarmInfo.order.warningSource === '1' || alarmRepeatData.alarmInfo.order.warningSource === '4'"
        >
          <div class="dispose-item-label">是否告警压制</div>
          <div class="dispose-tip">
            <div
              c-tip="如开启告警压制，将按照同一设备同一告警类型在规定时间内进行压制处理，压制的告警将放到回收站中。"
              c-tip-placement="top"
              class="text-source"
            ></div>
          </div>
          <div class="suppress-switch-box">
            <el-switch v-model="isSuppress" active-color="#3A77E5" inactive-color="#a2a3a3"></el-switch>
          </div>
        </el-row>
        <!-- 告警压制组件 -->
        <el-row v-if="isSuppress" class="dispose-item-left-suppress">
          <AlarmSuppress ref="alarmSuppressCp" />
        </el-row>
        <!-- 分页组件 -->
        <el-row class="repeat-suppress-box">
          <div class="dispose-item-right-page">
            <div class="list-page">
              <el-pagination
                class="im-page"
                @current-change="rightHandleCurrentChange"
                :current-page="repeatParams.pageNum"
                :page-size="repeatParams.pageSize"
                :page-sizes="[10, 20, 30, 40]"
                layout="total, prev, pager, next"
                :total="repeatTableData.total"
                :pager-count="5"
              ></el-pagination>
            </div>
          </div>
        </el-row>
      </div>
      <!-- 弹窗底部按钮 -->
      <div class="dispatch-buttons">
        <div class="dispatch-button dispatch-button-blue" @click="toSubmit">
          <span>确定</span>
        </div>
        <div class="dispatch-button" @click="toClose">
          <span>取消</span>
        </div>
      </div>
    </template>
  </CtlPanel>
</template>
<script>
import api from '@/api'
import ConstEnum from '@/components/common/ConstEnum'
import CommonTable from '@/components/page/ScreenPages/IntegratedMonitor/CommonTable/index.vue'
import CtlPanel from '@/components/page/ScreenPages/IntegratedMonitor/CtlPanel/index.vue'
import dayjs from 'dayjs'
import AlarmSuppress from '../components/alarm-suppress.vue'
export default {
  name: 'alarm-repeat-dialog',
  components: {
    CtlPanel,
    AlarmSuppress,
    CommonTable,
  },
  props: {
    propData: {
      type: Object,
      default: {},
    },
    close: {
      type: Function,
      default: () => {
        console.log('close')
      },
    },
    okCalBack: {
      type: Function,
      default: () => {
        console.log('okCalBack')
      },
    },
  },
  data() {
    const pickerRange = 3600 * 1000 * 24 * 30 // 可选择日期范围（30天）
    const datepickerOption = {
      disabledDate: nowDate => {
        if (nowDate > new Date()) {
          // 不允许选择未来时间
          return true
        }
        if (this.pickerMinDate) {
          // 超过区间的时间也不能
          return (
            nowDate.getTime() > this.pickerMinDate + pickerRange ||
            nowDate.getTime() < this.pickerMinDate - pickerRange
          )
        }
        return false
      },
      onPick: ({ maxDate, minDate }) => {
        if (minDate) {
          this.pickerMinDate = minDate.getTime()
        } else {
          this.pickerMinDate = null
        }
      },
      timeReset: false,
    }
    return {
      alarmRepeatData: this.propData,
      dataLoading: true,
      repeatParams: {
        //重复告警入参
        alarmId: '', //事件编号
        alarmType: [], //告警类型
        alarmDate: [], // 时间范围
        spaceRange: '', //空间范围
        prange: '',
        trange: '',
        pageNum: 1,
        pageSize: 5,
      },
      alarmTypeList: [], // 告警类型
      pickerOptions: datepickerOption, //时间范围规范
      repeatTableData: {
        rows: [],
      },
      repeatOrderId: '', //列表选中的重复告警id
      isSuppress: false,
    }
  },
  created() {
    // 初始化时获取默认告警时间、告警类型和重复告警列表
    this.getDefaultWarnTime()
    this.getWarningType()
    this.getReapeatAlarm()
  },
  methods: {
    /**
     * 获取默认告警时间
     */
    getDefaultWarnTime() {
      let warnTime = []
      let sixHours = 6 * 60 * 60 * 1000 // 6个小时的毫秒数
      let nowDate = new Date().getTime()
      let beforeDate = nowDate - sixHours
      warnTime.push(new Date(beforeDate).toLocaleString().replace(/\//g, '-'))
      warnTime.push(new Date(nowDate).toLocaleString().replace(/\//g, '-'))
      this.repeatParams.alarmDate = warnTime
      this.repeatParams = JSON.parse(JSON.stringify(this.repeatParams))
    },
    /**
     * 查询告警类型
     */
    getWarningType() {
      api
        .post(
          `${this.$env.VUE_APP_REQ_PREFIX_BIZ}/adsEvtTypeConfig/findOrderAlarm`,
          {
            isFocus: '',
            isOpen: 'Y',
          }
        )
        .then(res => {
          if (res.code == 200) {
            this.alarmTypeList = res.data
          } else {
            this.$message({
              type: 'error',
              message: res.msg || '告警类型查询失败！',
            })
          }
        })
    },
    /**
     * 查询重复告警列表
     */
    getReapeatAlarm() {
      const params = {
        alarmTimeStart: dayjs(this.repeatParams.alarmDate[0]).format(
          ConstEnum.DATE_FORMAT.zero
        ),
        alarmTimeEnd: dayjs(this.repeatParams.alarmDate[1]).format(
          ConstEnum.DATE_FORMAT.H24
        ),
        orderId: this.repeatParams.alarmId,
        warningTypeIdList: this.repeatParams.alarmType,
        alarmRangeP: this.repeatParams.prange,
        alarmRangeT: this.repeatParams.trange,
        spaceRange: this.repeatParams.spaceRange,
        pageNum: this.repeatParams.pageNum,
        pageSize: 5,
      }
      api
        .post(
          `${this.$env.VUE_APP_REQ_PREFIX_PLAT}/event/getOrderListForDuplication`,
          params
        )
        .then(res => {
          if (res.code == 200) {
            this.repeatTableData = res
            this.dataLoading = false
          } else {
            this.dataLoading = false
            this.$message({
              type: 'error',
              message: res.msg || '告警类型查询失败！',
            })
          }
        })
    },
    /**
     * 选中列表行内数据
     */
    tableRowData(row) {
      this.repeatOrderId = row.orderId
    },
    /**
     * 右侧告警页码改变
     */
    rightHandleCurrentChange(num) {
      this.repeatParams.pageNum = num
      this.getReapeatAlarm()
    },
    /**
     * 确认提交
     */
    toSubmit() {
      if (this.dataLoading) {
        return false
      } else {
        this.doSubmitReapeat()
      }
    },
    /**
     * 处理重复告警入参数据
     */
    doSubmitReapeat() {
      let alarmInfo = this.alarmRepeatData.alarmInfo
      if (this.repeatOrderId === '') {
        this.$message({
          type: 'error',
          message: '请选择重复告警事件',
        })
        return false
      }
      let params = {
        orderId: alarmInfo.order.warningOrderId,
        warningOrderId: alarmInfo.order.warningOrderId,
        flowId: alarmInfo.order.flowId, //流程ID
        currentLink: alarmInfo.order.linkId, //当前环节ID
        // isSign: '0',//是否会签
        msgNotifyType: this.msgNotifyType, //通知方式
        isNextLink: this.isNextLink, //是否流转1：流转下个环节  0：不流转 isNextLink除了需要完成会签的时候是0  其他时候都是1
        remark: this.remarksText, //说明
        realLinkType: this.realLinkType, // 真实环节类型(1研判2调度4处置5核实6办结)
        linkId: '7', //下一环节
        linkName: '重复告警完结',
        orderStatus: '7', //订单状态（与linkId相同）
        enableAcceptanceMode: alarmInfo.order.enableAcceptanceMode, //受理状态（取详情接口返回的事件该字段（0：不开启 1：开启（待受理）2：已受理 3：受理完成）
        timeLimitAcceptance: this.timeLimitAcceptance, //受理时限
        attrId: this.linkId, //流程环节id
        nextEnableAcceptanceMode: this.nextEnableAcceptanceMode, //下一环节是否开启受理状态（（0:不开启 1:开启））
        timeLimit: this.timeLimit,
        judgeResult: '2',
        flowLinkType: '0', //工单流转类型 0 默认 1转派 2回退
        processDTOList: this.processDTOList, //人员入参
        warningTypeId: alarmInfo.order.warningTypeId, //告警类型
        duplicateOrderId: this.repeatOrderId, //重复告警id
      }
      if (alarmInfo.order?.changeAddCodeName?.isChange) {
        params = this.toAddressData(params, alarmInfo)
      }
      if (this.isSuppress) {
        //开启告警压制
        this.doSuppress(alarmInfo)
      }
      this.doSubmitProcess(params)
    },
    /**
     * 修改地址时处理地址数据
     */
    toAddressData(params, alarmInfo) {
      // 修改数据处理
      params.gridId = alarmInfo.order.gridId //网格ID
      params.gridName = alarmInfo.order.gridName //网格名称
      params.checkGridFlag = 1 //是否修改网格信息和水平方位角
      if (
        alarmInfo.order.warningSource === '1' ||
        alarmInfo.order.warningSource === '4'
      ) {
        params.horiAzimuthAngle = alarmInfo.order.horiAzimuthAngle //水平方位角
      }
      params.address = alarmInfo.order.changeAddCodeName.address //详细地址名称
      params.longitude = alarmInfo.order.changeAddCodeName.longitude //经度名称
      params.latitude = alarmInfo.order.changeAddCodeName.latitude //维度名称
      params.provinceId = alarmInfo.order.changeAddCodeName.provinceId //省份id
      params.provinceName = alarmInfo.order.changeAddCodeName.provinceName //省份名称
      params.cityId = alarmInfo.order.changeAddCodeName.cityId //地市名称
      params.cityName = alarmInfo.order.changeAddCodeName.cityName //地市名称
      params.countyId = alarmInfo.order.changeAddCodeName.countyId //区县编码
      params.countyName = alarmInfo.order.changeAddCodeName.countyName //区县名称
      return params
    },
    /**
     * 处理压制数据提交
     */
    doSuppress(alarmInfo) {
      let interceptTime = 0
      let suppressToday = false
      let type = this.$refs.alarmSuppressCp.suppressTypeValue
      let day = this.$refs.alarmSuppressCp.dayValue
      let hour = this.$refs.alarmSuppressCp.hourValue
      let minute = this.$refs.alarmSuppressCp.minuteModel
      if (type === 1) {
        //5分钟压制
        interceptTime = 5
      } else if (type === 2) {
        //1小时压制
        interceptTime = 60
      } else if (type === 3) {
        //今日不告警
        interceptTime = this.$refs.alarmSuppressCp.todaySuppressTime()
        suppressToday = true
      } else {
        //自定义压制
        interceptTime =
          Number(day) * 24 * 60 + Number(hour) * 60 + Number(minute)
      }
      if (this.isSuppress) {
        this.goSuppress(
          alarmInfo.order.deviceCode,
          alarmInfo.order.warningTypeId,
          interceptTime,
          suppressToday
        )
      }
    },
    /**
     * 提交压制
     */
    goSuppress(deviceCode, warningTypeId, interceptTime, noAlarmToday) {
      if (!deviceCode) {
        return false
      }
      const params = {}
      params.deviceCode = deviceCode
      params.warningTypeId = warningTypeId
      params.interceptTime = interceptTime
      params.noAlarmToday = noAlarmToday
      api
        .post(
          `${this.$env.VUE_APP_REQ_PREFIX_PLAT}/event/createIntercept`,
          params
        )
        .then(res => {
          if (res.code != 200) {
            this.$message({
              type: 'error',
              message: res.msg || '提交压制失败！',
            })
          }
        })
    },
    /**
     * 提交工单流转
     */
    doSubmitProcess(params) {
      api
        .post(
          `${this.$env.VUE_APP_REQ_PREFIX_PLAT}/event/submitProcess`,
          params
        )
        .then(res => {
          if (res.code == 200) {
            this.$message({
              type: 'success',
              message: res.msg,
            })
            this.okCalBack()
          } else {
            this.$message({
              type: 'error',
              message: res.msg || '提交工单流转失败！',
            })
          }
        })
    },
    /**
     * 取消提交
     */
    toClose() {
      this.close()
    },
    evtIdInputChange() {
      this.getReapeatAlarm()
    },
    alarmTypeChange() {
      this.getReapeatAlarm()
    },
    alarmRepeatDataChange() {
      this.getReapeatAlarm()
    },
  },
}
</script>
<style lang='less' src='./index.less' />
<style lang='less' scoped>
@import '../../common';
.alarm-repeat-outter {
  width: 764px;
  min-height: 372px;
  padding: 0 12px;
  border-radius: 8px;
  box-sizing: border-box;
  color: #fff;
  font-size: 14px;
  font-family: PingFangSC-Regular;
  .evt-handle-wrap .ctl-content {
    height: auto;
  }
  .dispatch-title {
    width: 100%;
    height: 36px;
    background-image: linear-gradient(
      180deg,
      rgb(103 157 228 / 45%) 1%,
      rgb(65 94 145 / 45%) 98%
    );
    border-radius: 8px 8px 0px 0px;
    display: flex;
    justify-content: center;
    align-items: center;
    .alarm-close {
      position: absolute;
      right: -0.5rem;
      top: -0.5rem;
      cursor: pointer;
      font-size: 1rem;
    }
  }
  .dispatch-con {
    .el-table :deep .el-table__body tr.current-row > td.el-table__cell,
    .el-table__body tr.selection-row > td.el-table__cell {
      background-color: @theme-color;
    }
    .repeat-sift-box {
      width: 100%;
      margin: 10px 0;
      display: flex;
      .sift-btn-box {
        margin-top: -7px;
      }
      .repreat-sift-item {
        display: flex;
        text-align: center;
        .sift-label {
          height: 32px;
          display: flex;
          font-size: 14px;
          font-weight: 400;
          color: #ffffff;
          line-height: 32px;
        }
        .sift-input {
          width: 110px;
          height: 32px;
          margin: 0 10px;
          /deep/ .el-select {
            .el-select__tags .el-tag {
              background-color: rgba(25, 137, 250, 0.2);
              color: #1989fa;
              border: none;
            }
            .el-select__tags .el-tag.el-tag--info .el-tag__close.el-icon-close {
              color: #1989fa;
              background-color: transparent;
              &::before {
                color: #1989fa;
              }
            }
          }
        }
        .sift-date-picker {
          height: 32px;
          .el-range-editor.el-input__inner {
            padding: 0;
          }
          .el-range-editor--small .el-range-input {
            font-size: 12px;
          }
          /deep/ .el-date-editor {
            width: 300px;
            .el-range-input {
              color: #ffffff;
              background: transparent;
            }
            .el-range-separator {
              color: #ffffff;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }
          /deep/ .c-date-editor {
            .el-input__icon {
              /* 时间前侧icon */
              margin: 0;
              font-weight: 400;
            }
          }
        }
        .sift-label-space {
          height: 32px;
          font-size: 14px;
          font-weight: 400;
          color: #ffffff;
          line-height: 32px;
          display: flex;
          .text-source {
            float: left;
            margin-left: 6px;
            margin-top: 6px;
            background-size: 100% 100%;
            background-repeat: no-repeat;
            width: 14px;
            height: 14px;
            background-image: url('~@/assets/images/alarmEvent/weather/zhushi_icon.svg');
          }
        }
        .sift-label-space-pt {
          height: 32px;
          font-size: 14px;
          font-weight: 400;
          color: #ffffff;
          line-height: 32px;
          display: flex;
          .text-source {
            float: left;
            margin-left: 6px;
            margin-top: 6px;
            background-size: 100% 100%;
            background-repeat: no-repeat;
            height: 14px;
            background-image: url('~@/assets/images/alarmEvent/weather/zhushi_icon.svg');
          }
        }
        .sift-date-picker-space {
          height: 32px;
        }
        .sift-date-picker-space-pt {
          height: 32px;
          display: flex;
          .c-input {
            /deep/ .el-input-group__prepend {
              height: 32px;
              background: transparent;
              border: 0;
              margin: 0 -10px;
              line-height: 32px;
            }
          }
          .pt-btn-left {
            margin-right: 11px;
          }
        }
        .sift-date-picker-btn {
          display: flex;
          flex-wrap: wrap;
          justify-content: right;
          .bottom-btn {
            width: 80px;
            margin: 4px 0;
          }
          .sift-submit {
            margin-right: 10px;
          }
        }
      }
    }
  }
  .dispatch-buttons {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
  }
  .dispatch-button {
    width: 80px;
    height: 32px;
    // background-image: linear-gradient(90deg, #2FB1EC 8%, #155BD4 100%);
    background: #2f4360;
    border-radius: 4px;
    line-height: 32px;
    text-align: center;
    margin-left: 5px;
    margin-bottom: 5px;
    font-size: 12px;
    cursor: pointer;
  }
  .dispatch-button-blue {
    background-image: linear-gradient(90deg, #2fb1ec 8%, #155bd4 100%);
    background-image: linear-gradient(90deg, #2fb1ec 8%, #155bd4 100%);
  }
  /deep/ .el-loading-mask {
    background-color: rgb(0 0 0 / 47%);
  }
}

//压制
.repeat-suppress-box {
  height: 40px;
  display: flex;
  flex-wrap: wrap;
  align-content: center;
}
.dispose-item-left-suppress {
  display: flex;
  .dispose-item-label {
    height: 32px;
    font-size: 14px;
    font-weight: 400;
    color: #ffffff;
    line-height: 32px;
  }
  .dispose-tip {
    display: flex;
    align-content: center;
    flex-wrap: wrap;
    .text-source {
      float: left;
      margin: 0 3px 0 3px;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      width: 18px;
      height: 18px;
      background-image: url('~@/assets/images/alarmEvent/weather/zhushi_icon.svg');
    }
  }
  .suppress-switch-box {
    display: flex;
    flex-wrap: wrap;
    align-content: center;
  }
}
.dispose-item-right-page {
  margin-left: auto;
  .page-tool {
    position: absolute;
    bottom: 12px;
    right: 12px;
    display: flex;
    .page-number {
      height: 26px;
      font-size: 12px;
      font-weight: 400;
      color: rgba(232, 242, 254, 0.9);
      line-height: 26px;
      text-align: left;
      width: 80px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    /deep/ .el-pagination .el-pager li,
    /deep/ .el-pagination .btn-next,
    /deep/ .el-pagination .btn-prev {
      color: #fff !important;
      background: transparent !important;
    }
    /deep/ .el-pagination .el-pager li.active {
      background: #1989fa !important;
      border-radius: 4px !important;
      color: #fff !important;
    }
  }
}
.dispose-item-right-suppress {
  width: 100%;
}
.repeat-dialog-body {
  /deep/ .el-input__inner {
    margin-top: -2px;
  }
}
</style>
