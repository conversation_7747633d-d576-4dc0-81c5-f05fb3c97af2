<!--
 * @Description  : 处置
 * <AUTHOR> wnj
 * @Date         : 2023-12-11 10:58:52
 * @LastEditors  : wnj
 * @LastEditTime : 2023-12-26 14:50:20
 * @FilePath     :  / src / components / page / alarmEvent / components / alarmRight / alarmDisposeDialog.vue
-->
<!--  告警各环节处理弹窗  -->
<template>
  <!-- 使用 CtlPanel 组件包裹整个弹窗 -->
  <CtlPanel class='evt-handle-wrap alarm-dispatch-outter' :close='toClose'>
    <!-- 弹窗标题插槽 -->
    <template v-slot:title>
      {{ alarmDisposeData.title }}
    </template>
    <!-- 弹窗内容插槽 -->
    <template v-slot:content>
      <!-- 处理表单，加载时显示 loading -->
      <div class='dispatch-con evt-handle-form' v-loading='dataLoading'>
        <!-- 选择下一环节 -->
        <div class='dispose-item-box'>
          <div class='dispose-item-left' :class="{'chuZhi-left' : isChuZhi}">
            <div class='dispose-xinghao'>*</div>
            <div class='dispose-item-label'>
              下一环节
            </div>
          </div>
          <div class='dispose-item-right' :class="{'chuZhi-right' : isChuZhi}">
            <!-- 下拉选择组件 -->
            <el-select
              class='c-select'
              popper-class='c-select-dropdown'
              placeholder='请选择'
              v-model='nextLink'
              :disabled='nextLinkList && nextLinkList.length === 1'
              @change='changeNextLink'
            >
              <!-- 遍历环节列表生成选项 -->
              <el-option
                v-for='item in nextLinkList'
                :key='item.curLink'
                :label='item.linkName'
                :value='item.curLink'>
              </el-option>
            </el-select>
          </div>
        </div>
        <!-- 处理人输入框 -->
        <div class='dispose-item-box'>
          <div class='dispose-item-left' :class="{'chuZhi-left' : isChuZhi}">
            <div class='dispose-xinghao'>*</div>
            <div class='dispose-item-label'>责任人</div>
          </div>
          <div class='dispose-item-right' :class="{'chuZhi-right' : isChuZhi}">
            <el-input v-model='personInCharge' placeholder='请输入责任人'></el-input>
          </div>
        </div>
        <!-- 环节说明 -->
        <div class='dispose-item-box'>
          <div class='dispose-item-left' :class="{'chuZhi-left' : isChuZhi}">
            <div class='dispose-item-label'>
              整改方案
            </div>
          </div>
          <div class='dispose-item-right' :class="{'chuZhi-right' : isChuZhi}">
            <!-- 使用 AlarmPhrases 组件 -->
            <AlarmPhrases
              v-model='remarksText'
              placeholder='请输入内容'
              :orderStatus='alarmDisposeData.alarmInfo.order.orderStatus'
              :maxLength='200'
              @update='isPhrasesUnfold'
              :isUnfold.sync='isUnfold'
            />
          </div>
        </div>
        <!-- 附件上传 -->
        <div class='dispose-item-box'>
          <div class='dispose-item-left chuZhi-left'>
            <div class='dispose-xinghao is-chuzhi'>*</div>
            <div class='dispose-tip'>
              <!-- 上传格式提示 -->
              <div c-tip='上传格式支持png、jpg、jpeg、mp4、txt、doc、docx、pdf、ppt、pptx，单个文件上传不得超过10M，最多可上传9个' c-tip-placement='top'
                   class='text-source'>?
              </div>
            </div>
            <div class='dispose-item-label'>上传附件</div>
          </div>
          <div class='dispose-item-right' :class="{'chuZhi-right' : isChuZhi}" @contextmenu='disableRightClick'>
            <!-- 使用 UploadFiles 组件 -->
            <UploadFiles
              class='dispose-upload'
              :accept='accept'
              :isEdit='true'
              :maxSize='10'
              :maxLength='9'
              @change='changeFile'
              :unAcceptErrMsg='`只支持上传${accept}格式的文件`'
            />
          </div>
        </div>
        <!-- 责任段长选择树 -->
        <div class='dispose-item-box'>
          <div class='dispose-item-left' :class="{'chuZhi-left' : isChuZhi}">
            <div class='dispose-xinghao'>*</div>
            <div class='dispose-item-label'>责任段长</div>
          </div>
          <div class='dispose-item-right' :class="{'chuZhi-right' : isChuZhi}">
            <!-- 使用 PeopleSelectTree 组件 -->
            <PeopleSelectTree
              ref='rightPeopleSelect'
              :alarmInfo='alarmDisposeData?.alarmInfo'
              :getFlowUserParams='getFlowUserParams'
              :showAllTreeBtn='showAllTreeBtn'
              :dataFilterPermiss='dataFilterPermiss'
              @dispatchCheckChange='dispatchCheckChange'
              @dataLoading='changeLoading'
            />
          </div>
        </div>
      </div>
      <!-- 提交和取消按钮 -->
      <div class='dispatch-buttons'>
        <div class='dispatch-button dispatch-button-blue' @click='toSubmit'><span>确定</span></div>
        <div class='dispatch-button' @click='toClose'><span>取消</span></div>
      </div>
    </template>
  </CtlPanel>
</template>
<script>
import api from '@/api';
import { isNotEmptyString } from '@/components/common/utils';
import CtlPanel from '@/components/page/ScreenPages/IntegratedMonitor/CtlPanel/index.vue';
import AlarmPhrases from '../components/alarm-phrases.vue';
import AlarmSuppress from '../components/alarm-suppress.vue';
import PeopleSelectTree from '../components/peopleSelectTree.vue';
import UploadFiles from '../components/upload/UploadFiles.vue';
export default {
  components: {
    CtlPanel,
    PeopleSelectTree,
    AlarmPhrases,
    UploadFiles,
    AlarmSuppress
  },
  props: {
    propData: {
      type: Object,
      default: {}
    },
    close: {
      type: Function,
      default: () => {
        console.log('close');
      }
    },
    okCalBack: {
      type: Function,
      default: () => {
        console.log('okCalBack');
      }
    }
  },
  data() {
    return {
      alarmDisposeData: this.propData, // 告警处置数据
      accept: 'png,jpg,jpeg,mp4,txt,doc,docx,pdf,ppt,pptx', // 可接受的文件格式
      personInCharge: '', // 责任人
      dataLoading: true, // 数据加载状态
      isChuZhi: false, // 是否处置
      nextLinkList: [], // 所有的环节
      nextLink: '', // 选中的下一环节
      showAllTree: false, // 是否展示全部处理人树
      showAllTreeBtn: false, // 是否展示全部处理人按钮
      linkId: '', // 下一环节id
      linkName: '', // 下一环节名称
      realLinkType: '', // 真实环节id
      msgNotifyType: '', // 通知方式
      nextEnableAcceptanceMode: '', // 下一环节是否开启受理
      timeLimit: '', // 下一环节超期时间
      timeLimitAcceptance: '', // 下一环节受理时间
      dataFilterPermiss: '', // 是否开启过滤权限 0 否 1是
      alarmFlowId: '', // 告警流程id
      userType: '', // 用户类型
      getFlowUserParams: { // 查询处置人员入参
        item: {}, // 下一环节数据
        recordsInfo: [] // 回退时需要的环节信息
      },
      dispatchNameValue: [], // 处置人名称值
      processDTOList: [], // 处理人入参
      remarksText: '', // 流程处理时的处理说明
      isUnfold: false, // 展开常用语 弹窗变大
      imgsList: [], // 图片列表
      videosList: [], // 视频列表
      isSuppress: false // 是否开启压制
    };
  },
  created() {
    // 初始化时获取下一环节
    this.getNextLink(this.alarmDisposeData.alarmInfo.order);
  },
  methods: {
    /**
     * 禁用鼠标右键
     */
    disableRightClick(e) {
      e.preventDefault();
    },
    /**
     * 查询下一环节
     */
    getNextLink(item) {
      const params = {
        flowId: item.flowId,
        curLink: item.linkId,
        templateId: item.flowTemplate,  // 模板id
        warningSource: item.warningSource // 告警来源
      };
      api.post(
        `${this.$env.VUE_APP_REQ_PREFIX_PLAT}/event/queryNextLink`,
        params
      ).then(res => {
        if (res.code == 200) {
          this.nextLinkList = res.data;
          if (res.data.length < 2) { // 只有一个环节时直接赋值
            this.nextLink = this.nextLinkList[0].curLink;
            this.changeNextLink(this.nextLink);
            if (this.alarmDisposeData.title === '核实') {
              this.dataLoading = false;
            }
          } else {
            this.dataLoading = false;
          }
        } else {
          this.dataLoading = false;
          this.$message({
            type: 'error',
            message: res.msg || '下一环节查询失败！'
          });
        }
      });
    },
    /**
     * 用户选中下一环节
     */
    changeNextLink(val) {
      let choosed;
      choosed = this.nextLinkList.find(item => item.curLink === val);
      console.log('%c █░░░░░░░░░░░░█ ,注释 用户选中下一环节', 'color: #FAC800', choosed);
      this.linkId = val;
      this.linkName = choosed.linkName;
      this.realLinkType = choosed.realLinkType;
      this.msgNotifyType = choosed.msgNotifyType;
      this.nextEnableAcceptanceMode = choosed.enableAcceptanceMode;
      this.timeLimitAcceptance = choosed.timeLimitAcceptance;
      this.timeLimit = choosed.timeLimit;
      this.dataFilterPermiss = choosed.dataFilterPermiss;
      // 当“是否过滤数据权限dataFilterPermiss”配置为否， “是否显示客户管理员customAdmin” 配置为是时，下拉列表底部不显示“显示全部处理人”按钮。
      this.showAllTreeBtn = !(choosed.dataFilterPermiss === '0' && choosed.customAdmin === '1');
      this.alarmFlowId = choosed.flowId;
      this.userType = choosed.userType;
      let a = {};
      a.flowId = choosed.flowId;
      a.linkId = val;
      a.orderId = this.alarmDisposeData.alarmInfo.order.warningOrderId;
      // 赋值获取人员数的参数
      this.getFlowUserParams = {
        item: a,
        recordsInfo: []
      };
    },
    /**
     * 赋值处置人员树返回的数据
     */
    dispatchCheckChange(param) {
      this.processDTOList = param.processDTOList;
      this.dispatchNameValue = param.dispatchNameValue;
    },
    /**
     * 常用语判断弹窗高度
     */
    isPhrasesUnfold(val) {
      this.isUnfold = val;
    },
    /**
     * 上传组件回调
     */
    changeFile(files, flag) {
      if (flag === 1) {
        this.$message({
          type: 'success',
          message: '上传成功'
        });
      }
      if (flag === -1) {
        this.$message({
          type: 'success',
          message: '删除成功'
        });
      }
      this.fileList = files.map((it) => {
        return {
          fileId: it.fileId,
          fileName: it.fileName,
          fileUrl: it.fileUrl,
          coverUrl: it.coverUrl,
          fileType: it.type // 1:图片 2:视频 3:文件
        };
      });
    },
    /**
     * 改变加载等待状态
     */
    changeLoading() {
      this.dataLoading = false;
    },
    /**
     * 确认提交
     */
    toSubmit() {
      if (this.dataLoading) {
        return false;
      }
      this.toSubmitProcess(this.alarmDisposeData.title);
    },
    /**
     * 取消提交
     */
    toClose() {
      this.close();
    },
    /**
     * 正常工单流转 真警、调度、处置、核实 转派
     */
    toSubmitProcess(type) {
      if (this.dispatchNameValue.length === 0) { // 校验处置人
        this.$message({
          type: 'error',
          message: '请选择处理人'
        });
        return false;
      }
      if (this.fileList?.length < 1) { // 校验处置环节附件上传
        this.$message({
          type: 'error',
          message: '请先完善附件上传信息'
        });
        return false;
      }
      // 判断不为空以及空字符串
      if (!isNotEmptyString(this.personInCharge)) {
        this.$message({
          type: 'error',
          message: '请先完善责任人'
        });
        return false;
      }
      let alarmInfo = this.alarmDisposeData.alarmInfo;
      this.toDispatch(alarmInfo);
    },
    /**
     * 调度入参处理
     */
    toDispatch(alarmInfo) {
      let params = {
        orderId: alarmInfo.order.warningOrderId,
        warningOrderId: alarmInfo.order.warningOrderId,
        flowId: alarmInfo.order.flowId, // 流程ID
        currentLink: alarmInfo.order.linkId, // 当前环节ID
        // isSign: '0', // 是否会签
        msgNotifyType: this.msgNotifyType, // 通知方式
        isNextLink: this.isNextLink, // 是否流转1：流转下个环节  0：不流转 isNextLink除了需要完成会签的时候是0  其他时候都是1
        remark: this.remarksText, // 说明
        realLinkType: this.realLinkType, // 真实环节类型(1研判2调度4处置5核实6办结)
        linkId: this.linkId, // 下一环节
        linkName: this.linkName,
        orderStatus: this.linkId, // 订单状态（与linkId相同）
        enableAcceptanceMode: alarmInfo.order.enableAcceptanceMode, // 受理状态（取详情接口返回的事件该字段（0：不开启 1：开启（待受理）2：已受理 3：受理完成）
        timeLimitAcceptance: this.timeLimitAcceptance, // 受理时限
        timeLimit: this.timeLimit,
        attrId: this.linkId, // 流程环节id
        nextEnableAcceptanceMode: this.nextEnableAcceptanceMode, // 下一环节是否开启受理状态（（0:不开启 1:开启））
        judgeResult: '1',
        flowLinkType: '0', // 工单流转类型 0 默认 1转派 2回退
        processDTOList: this.processDTOList, // 人员入参
        warningTypeId: alarmInfo.order.warningTypeId // 告警类型
      };
      if (alarmInfo.order?.changeAddCodeName?.isChange) {
        params = this.toAddressData(params, alarmInfo);
      }
      let fileList = [];
      this.fileList.forEach((item) => {
        let a = {};
        a.resourceId = item.fileId;
        a.resourceUrl = item.fileUrl;
        a.fileName = item.fileName;
        a.resourceType = item.fileType; // 1:图片 2:视频 3:文件
        fileList.push(a);
      });
      params.orderFileDTOList = fileList;
      this.doSubmitProcess(params);
    },
    /**
     * 修改地址时处理地址数据
     */
    toAddressData(params, alarmInfo) { // 修改数据处理
      params.gridId = alarmInfo.order.gridId; // 网格ID
      params.gridName = alarmInfo.order.gridName; // 网格名称
      params.checkGridFlag = 1; // 是否修改网格信息和水平方位角
      if (alarmInfo.order.warningSource === '1' || alarmInfo.order.warningSource === '4') {
        params.horiAzimuthAngle = alarmInfo.order.horiAzimuthAngle; // 水平方位角
      }
      params.address = alarmInfo.order.changeAddCodeName.address; // 详细地址名称
      params.longitude = alarmInfo.order.changeAddCodeName.longitude; // 经度名称
      params.latitude = alarmInfo.order.changeAddCodeName.latitude; // 维度名称
      params.provinceId = alarmInfo.order.changeAddCodeName.provinceId; // 省份id
      params.provinceName = alarmInfo.order.changeAddCodeName.provinceName; // 省份名称
      params.cityId = alarmInfo.order.changeAddCodeName.cityId; // 地市名称
      params.cityName = alarmInfo.order.changeAddCodeName.cityName; // 地市名称
      params.countyId = alarmInfo.order.changeAddCodeName.countyId; // 区县编码
      params.countyName = alarmInfo.order.changeAddCodeName.countyName; // 区县名称
      return params;
    },
    /**
     * 提交工单流转
     */
    doSubmitProcess(params) {
      api.post(
        `${this.$env.VUE_APP_REQ_PREFIX_PLAT}/event/submitProcess`,
        params
      ).then(res => {
        if (res.code == 200) {
          this.$message({
            type: 'success',
            message: res.msg
          });
          this.okCalBack();
        } else {
          this.$message({
            type: 'error',
            message: res.msg || '提交工单流转失败！'
          });
        }
      });
    }
  }
};
</script>
<style lang='less' src='./index.less' />
