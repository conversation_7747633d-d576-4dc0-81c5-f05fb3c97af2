<!--
 * @Description  : 告警事件详情
 * <AUTHOR> wnj
 * @Date         : 2023-12-06 10:55:00
 * @LastEditors: liu.yongli
 * @LastEditTime: 2025-06-12 22:09:57
 * @FilePath     :  / src / components / page / alarmEvent / components / alarmRight / alarmDetail.vue
-->
<template>
  <!-- 外层容器 -->
  <!-- propData.isAutoOpen 演示大屏时，模拟右上角提示时，打开事件详情时，节点上添加一个标记 -->
  <div
    class="alarm-detail-outter"
    v-bind:data-custom-open-mode="`${propData.isAutoOpen}`"
    v-loading="infoLoading"
  >
    <!-- 标题部分，只有在showMainTitle为true时显示 -->
    <div class="titleWrap" v-if="showMainTitle">
      <div class="title">
        <div class="titlName">事件详情</div>
      </div>
      <!-- 标题图标部分 -->
      <div class="titleIcons">
        <!-- 关闭按钮，只有在propData.closeable为true时显示 -->
        <img
          alt="关闭"
          v-if="propData.closeable"
          class="alarm-close"
          :src="require(`@/assets/images/alarmEvent/alarm/close_icon.svg`)"
          @click="onCloseDetail"
        />
      </div>
    </div>
    <!-- 视频图片轮播 -->
    <!-- <div class="video-img-carousel-box">
      <AlarmFileCarouselNew :alarmInfo="alarmInfo" />
    </div>-->
    <div class="video-img-carousel-box">
      <AlarmFileCarouselNew :alarmInfo="imgVideoInfo" />
    </div>
    <!-- <div class="pageer" v-if="alarmInfo?.alarmList?.length > 1">
      <el-pagination
        class="common-pagination"
        background
        layout="prev, pager, next"
        :hide-on-single-page="relAlarmIds.length < 2"
        :total="pageInfo.total"
        :page-size="pageInfo.pageSize"
        :current-page="pageInfo.pageIndex"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>-->
    <!-- v-if="alarmInfo?.alarmList?.length > 1" -->
    <div class="time-line-wraper">
      <TimeLineCarouselCom :list="progressTimer" @change="timeChange" />
    </div>
    <!-- 工具栏 -->
    <div class="detail-tools">
      <!-- 实时视频按钮，只有在告警来源为1、3或4时显示 -->
      <div
        v-if="alarmInfo?.order?.warningSource === '1' || alarmInfo?.order?.warningSource === '3' || alarmInfo?.order?.warningSource === '4'"
        c-tip="实时视频"
        c-tip-placement="top"
        :class="openDialog['1']?.selected ? 'iconfont icon-icon_shishishipin_30_s fun-btn-class fun-btn-active-class' : 'iconfont icon-icon_shishishipin_30_n fun-btn-class'"
        @click.stop="clickFunBtn('1')"
      ></div>
      <!-- 一键看向告警点按钮，只有在告警来源为1、3或4时显示 -->
      <div
        v-if="alarmInfo?.order?.warningSource === '1' || alarmInfo?.order?.warningSource === '3' || alarmInfo?.order?.warningSource === '4'"
        c-tip="看这里"
        c-tip-placement="top"
        :class="openDialog['2']?.selected ? 'iconfont icon-yijiankanxianggaojingdian_30_s fun-btn-class fun-btn-active-class' : 'iconfont icon-icon_yijiankanxianggaojingdian_30_n fun-btn-class'"
        @click.stop="clickFunBtn('2')"
      ></div>
      <!-- 转领导批示按钮 -->
      <!-- <Authority
        :authPoint="HandleEvent.leaderGuidance.name"
        :alarmInfo="alarmInfo"
        :linkDetail="linkDetail"
        c-tip="转领导批示"
        c-tip-placement="top"
        @click.native="clickFunBtn('4')"
        :class="
          openDialog[HandleEvent.leaderGuidance.desc]?.selected
            ? 'iconfont icon-icon_zhuanlingdaopishi_30_s fun-btn-class fun-btn-active-class'
            : 'iconfont icon-icon_zhuanlingdaopishi_30_n fun-btn-class'
        "
      ></Authority>-->
      <!-- 实时喊话按钮，只有在告警来源为1、3或4时显示 -->
      <div
        v-if="alarmInfo?.order?.warningSource === '1' || alarmInfo?.order?.warningSource === '3' || alarmInfo?.order?.warningSource === '4'"
        c-tip="实时喊话"
        c-tip-placement="top"
        :class="openDialog['6']?.selected ? 'iconfont icon-icon_hanhua_20_s fun-btn-class fun-btn-active-class' : 'iconfont icon-icon_hanhua_20_n fun-btn-class'"
        @click.stop="clickFunBtn('6')"
      ></div>
      <!-- 指点飞行 -->
      <div
        c-tip="指点飞行"
        c-tip-placement="top"
        :class="
            openDialog['13']
                ? 'iconfont icon_wurenjijichang_18_s fun-btn-class fun-btn-active-class2'
                : 'iconfont icon_wurenjijichang_18 fun-btn-class'
        "
        @click.stop="clickFunBtn('13')"
      ></div>
      <!-- 收藏按钮 -->
      <div c-tip="收藏" c-tip-placement="top">
        <!-- 未收藏状态 -->
        <img
          alt="收藏"
          v-if="alarmInfo?.isCollecltion !== '1'"
          style="cursor: pointer"
          class="collection-icon"
          :src="require('@/assets/images/alarmEvent/alarm/shoucang_nor.svg')"
          @click.stop="collEvent(alarmInfo, '1')"
        />
        <!-- 已收藏状态 -->
        <img
          alt="已收藏"
          v-else
          class="collection-icon"
          style="cursor: pointer"
          :src="require('@/assets/images/alarmEvent/alarm/shoucang_huang_sel.svg')"
          @click.stop="collEvent(alarmInfo, '0')"
        />
      </div>
    </div>
    <!-- 详情内容 -->
    <div class="detail-con">
      <!-- 告警信息内容组件 -->
      <AlarmInfoContent
        ref="AlarmInfoContent"
        :mapId="null"
        v-if="alarmInfo"
        :alarmInfo="alarmInfo"
        :recordsInfo="recordsInfo"
        :showRelAlarm="showRelAlarm"
      />
    </div>
    <!-- 底部按钮盒子 -->
    <div class="detail-buttons">
      <!-- 不是关联事件且有处理权限时显示 -->
      <div class="alarm-bottom-btn-div" v-if="!infoLoading">
        <!-- 真警按钮 -->
        <Authority
          :authPoint="HandleEvent.trueAlarm.name"
          :alarmInfo="alarmInfo"
          :linkDetail="linkDetail"
        >
          <div
            :class="`detail-button detail-button-blue ${
              openDialog['真警']?.selected ? 'selected' : ''
            }`"
            @click="toAlarmDispose('真警')"
          >
            <span>真警</span>
          </div>
        </Authority>
        <!-- 误报按钮 -->
        <Authority
          :authPoint="HandleEvent.falseAlarm.name"
          :alarmInfo="alarmInfo"
          :linkDetail="linkDetail"
        >
          <div class="detail-button detail-button-red" @click="toAlarmDispose('误报')">
            <span>误报</span>
          </div>
        </Authority>
        <!-- 重复告警按钮 暂不显示-->
        <!-- <Authority
          :authPoint="HandleEvent.repeatAlarm.name"
          :alarmInfo="alarmInfo"
          :linkDetail="linkDetail"
        >
          <div
            :class="`detail-button detail-button-blue ${
              openDialog[HandleEvent.repeatAlarm.desc]?.selected
                ? 'selected'
                : ''
            }`"
            @click="toAlarmDispose(HandleEvent.repeatAlarm.desc)"
          >
            <span>重复告警</span>
          </div>
        </Authority>-->
        <!-- 调度按钮 -->
        <Authority
          :authPoint="HandleEvent.scheduling.name"
          :alarmInfo="alarmInfo"
          :linkDetail="linkDetail"
        >
          <div
            :class="`detail-button detail-button-blue ${
              openDialog['调度']?.selected ? 'selected' : ''
            }`"
            @click="toAlarmDispose('调度')"
          >
            <span>调度</span>
          </div>
        </Authority>
        <!-- 处置按钮 -->
        <Authority
          :authPoint="HandleEvent.disposition.name"
          :alarmInfo="alarmInfo"
          :linkDetail="linkDetail"
        >
          <div
            :class="`detail-button detail-button-blue ${
              openDialog['处置']?.selected ? 'selected' : ''
            }`"
            @click="toAlarmDispose('处置')"
          >
            <span>处置</span>
          </div>
        </Authority>
        <!-- 核实按钮 -->
        <Authority
          :authPoint="HandleEvent.verification.name"
          :alarmInfo="alarmInfo"
          :linkDetail="linkDetail"
        >
          <div
            :class="`detail-button detail-button-blue ${
              openDialog['核实']?.selected ? 'selected' : ''
            }`"
            @click="toAlarmDispose('核实')"
          >
            <span>核实</span>
          </div>
        </Authority>
        <!-- 受理按钮 -->
        <Authority
          :authPoint="HandleEvent.acceptance.name"
          :alarmInfo="alarmInfo"
          :linkDetail="linkDetail"
        >
          <div
            :class="`detail-button detail-button-blue ${
              openDialog['受理']?.selected ? 'selected' : ''
            }`"
            @click="toAlarmDispose('受理')"
          >
            <span>受理</span>
          </div>
        </Authority>
        <!-- 回退按钮 -->
        <Authority
          :authPoint="HandleEvent.rollback.name"
          :alarmInfo="alarmInfo"
          :linkDetail="linkDetail"
        >
          <div
            :class="`detail-button detail-button-blue ${
              openDialog['回退']?.selected ? 'selected' : ''
            }`"
            @click="toAlarmDispose('回退')"
          >
            <span>回退</span>
          </div>
        </Authority>
        <!-- 转派按钮 -->
        <Authority
          :authPoint="HandleEvent.transfer.name"
          :alarmInfo="alarmInfo"
          :linkDetail="linkDetail"
        >
          <div
            :class="`detail-button detail-button-blue ${
              openDialog['转派']?.selected ? 'selected' : ''
            }`"
            @click="toAlarmDispose('转派')"
          >
            <span>转派</span>
          </div>
        </Authority>
        <!-- 关闭按钮 -->
        <Authority
          :authPoint="HandleEvent.close.name"
          :alarmInfo="alarmInfo"
          :linkDetail="linkDetail"
        >
          <div
            :class="`detail-button detail-button-blue ${
              openDialog['关闭']?.selected ? 'selected' : ''
            }`"
            @click="toAlarmDispose('关闭')"
          >
            <span>关闭</span>
          </div>
        </Authority>
      </div>
    </div>
  </div>
</template>
<script>
import api from '@/api'
import {
  lookHere,
  // queryEvtCtlRole,
  queryEvtDetail,
  queryOrderInfo,
} from '@/api/service/imScreenService'
import bus from '@/components/common/bus.js'
// import {
//   drawMultiLine,
//   removeLayer,
// } from '@/components/common/Map/CommonCtMapOl'
import CommonMap from '@/components/common/Map/CommonMap'
import BroadCastCtl from '@/components/page/ScreenPages/IntegratedMonitor/BroadCastCtl'
import Authority from '@/components/page/ScreenPages/IntegratedMonitor/EventDetailInfo/Authority.vue'
import Station from '@/components/page/ScreenPages/IntegratedMonitor/PassRoute/Station'
import { $playerFit } from '@/utils/playerFit'
import AlarmInfoContent from './AlarmInfoContent/index.vue'
import AlarmFileCarouselNew from './components/alarm-file-carousel-new.vue'
import HandleWinCfg, { HandleEvent } from './HandleWinCfg'
import HornDeviceList from './hornDeviceList'
import { transformPointToMercator } from '@/utils'
import TimeLineCarouselCom from '@/components/common/TimeLineCarousel/TimeLineCarouselCom'
import dayjs from 'dayjs'
import PassRouteLine from '@/components/page/ScreenPages/IntegratedMonitor/PassRoute/PassRouteLine'

// let evtRoute = null // 事件路径
// 线路渲染实例
let passRouteLineIns = null

export default {
  props: {
    provinceId: {
      // 省份编码
      type: String,
      default: '110000',
    },
    cityId: {
      // 地市编码
      type: String,
      default: '',
    },
    orderId: {
      type: String,
      default: '',
    },
    propData: {
      type: Object,
      default: {
        closeable: true,
        // 演示大屏使用
        isAutoOpen: false,
      },
    },
    close: {
      type: Function,
      default: () => {
        console.log('关闭')
      },
    },
    showMainTitle: {
      type: Boolean,
      default: true,
    },
  },
  computed: {
    getMapRef() {
      return this.$store.state.map.mapStoreRef
    },
  },
  watch: {
    propData: {
      //监听事件变更
      handler() {
        this.getAlarmInfo()
      },
      deep: true, //json 深度匹配
      immediate: true, //初始化时执行
    },
  },
  components: {
    Authority,
    AlarmFileCarouselNew, //告警详情轮播窗
    AlarmInfoContent, //告警环节信息
    HornDeviceList, //大喇叭列表/实时喊话
    BroadCastCtl,
    TimeLineCarouselCom,
  },
  data: function () {
    return {
      HandleEvent,
      tools: [
        'zhoubianshipin',
        'bianji',
        'zldps',
        'shishihanhua',
        'zhoubian',
        'juhe',
      ],
      activeNames: ['2'],
      infoLoading: true,
      alarmInfo: {
        order: {},
      }, //告警详情
      openDialog: {}, //打开弹窗的标记 0:分享 1:实时视频 2:看向告警点 3:周边视频 4:转领导批示 5:告警标签 6:实时喊话 7:周边分析 8:历史告警 9:图片对比 10:应急预案 11:态势标绘
      showRelAlarm: false, //是否展示关联事件
      leaderFlag: true, //是否可转领导批示
      rollbackFlag: false, //是否可回退
      turnFlag: false, //是否可转派
      recordsInfo: [], //流程信息
      judgeOverInfo: {}, //研判信息
      dispatchOverInfo: {}, //调度信息
      handleOverInfo: {}, //处置信息
      verifyOverInfo: {}, //核实信息
      commandInfo: {}, //指挥调度信息
      linkDetail: [], // 流程节点详情
      alarmDisposeData: {
        //告警各环节处理弹窗数据 不含重复告警
        showDialog: false,
        title: '', //真警 误报 调度 处置 核实 转派 回退 受理 关闭
        alarmInfo: {}, //告警详情信息
        recordsInfo: [], //回退时所需要的环节信息
      },
      currentRolePerms: false, //当前角色权限
      alarmRepeatData: {
        showDialog: false,
        title: '重复告警',
        alarmInfo: {}, //告警详情信息
      },
      alarmDisposalProcess: {
        showDialog: false,
      },
      // evtRoute: null, //事件路径
      isShowAround: false, //是否开启周边分析
      ctlClick: {},
      relAlarmIds: [], //关联的告警id列表
      pageInfo: {
        pageIndex: 1,
        pageSize: 1,
        total: 0,
      }, // 关联的告警图片分页
      imgVideoInfo: { imgs: [], video: null }, //图片视频信息
      progressTimer: [],
      isShowFlight: false, // 是否开启指点飞行
    }
  },
  beforeDestroy() {
    // 组件销毁前移除事件监听和清理缓存
    window.removeEventListener('message', this.playerClosedBack)
    bottomWrap.closeByIdPrefix(`bottomHorn_`)
    bottomWrap.closeByIdPrefix(HandleWinCfg.winCfg.idPrefix)
    Station.clearCache()
    // 删除虚线线路
    // evtRoute && removeLayer(this.getMapRef.mapInstance, evtRoute)
    // evtRoute = null
    // 删除发光线路
    passRouteLineIns && passRouteLineIns.removePassRouteLine()
    passRouteLineIns = null
    $playerFit.close()
    this.$EventBus.$off('closeEvtDetailModal')
    this.$globalEventBus.$off('common-comp-guide-flight__isClose')
  },
  mounted() {
    // 监听指点飞行状态
    this.$globalEventBus.$on(`common-comp-guide-flight__isClose`, () => {
      this.isShowFlight = false
      if (this.openDialog['13']) {
        this.openDialog['13'] = false
      }
    })
    // 组件挂载后添加事件监听
    window.addEventListener('message', this.closeRealTimeVideo)
    this.$EventBus.$on('closeEvtDetailModal', this.onCloseDetail)
  },
  methods: {
    /**
     * 时间切换
     */
    timeChange(key) {
      this.handleCurrentChange(key)
    },
    /**
     * 关闭详情弹窗
     */
    onCloseDetail() {
      // 清空弹窗状态
      this.close()
    },
    // onFreshOrderStatus() {},
    /**
     * 查询事件详情
     */
    async getAlarmInfo(freshOrderStatus = false) {
      this.infoLoading = true
      const params = {
        id: this.propData.orderId,
      }
      // 事件详情
      const [, resData] = await queryEvtDetail(params)
      // 测试数据 resData ?? evtDetailData.data
      let data = resData
      // 取alarmList最后一个值
      const warningOrderId = data.alarmList?.length
        ? data.alarmList[data.alarmList.length - 1].alarmId
        : ''
      // 告警详情,取来源信息、处置信息
      const resOrder = await queryOrderInfo({ warningOrderId })
      const resOrderData = { data: resOrder?.[1] || {} }

      if (data) {
        // data.order = resOrderData?.data?.order
        // data.records = resOrderData?.data?.records
        // data.imgs = resOrderData?.data?.imgs
        // data.video = resOrderData?.data?.video
        // data.users = resOrderData?.data?.users
        // 把告警信息与事件详情放一起
        data = { ...data, ...resOrderData.data }
        this.alarmInfo = data

        // 事详操作按钮中的弹窗面板确定时刷新状态
        if (freshOrderStatus) {
          // 刷新状态
          try {
            await api.post(
              `${this.$env.VUE_APP_REQ_PREFIX_BIZ}/fusionEvent/updateEvent`,
              {
                id: this.alarmInfo?.id,
                alarmStatus: this.alarmInfo.order?.orderStatus,
              }
            )
          } catch (e) {
            console.log('/fusionEvent/updateEvent--', e)
          }
        }

        // 设置图片、视频
        this.getMediaInfo()

        // 根据参数查询当前事件控制角色
        // const linkId = data?.order?.linkId || ''
        // const realLinkType = data?.order?.realLinkType || ''
        // const [roleSuccess, roleData] = await queryEvtCtlRole({
        //   linkId,
        //   realLinkType,
        //   orderId: this.propData.orderId,
        // })
        // if (roleSuccess) {
        //   const { ifCurrentLink } = roleData
        //   this.currentRolePerms = ifCurrentLink === '1' // 当前角色权限
        // }
        // 对接流程后调试
        this.handleOrderData(data) //查到详情后 数据处理
        // 绘制事件上下游站点
        this.drawEvtStation()
      } else {
        this.infoLoading = false
      }
    },
    /**
     * 获取图片、视频
     */
    getMediaInfo() {
      const relAlarmList = []
      this.progressTimer = (this.alarmInfo?.alarmList || []).map(v => {
        relAlarmList.push(v.alarmId)
        return {
          ...v,
          date: dayjs(v.time).format('MM-DD'),
          clock: dayjs(v.time).format('HH:mm'),
        }
      })

      this.relAlarmIds = relAlarmList
      this.pageInfo.total = relAlarmList.length || 0
      this.pageInfo.pageIndex = 1
      // 如果res.data.dataSource有值，且值为1/2/3则直接把res.data.fileIconUrl放入this.imgVideoInfo
      if (
        this.alarmInfo.dataSource &&
        ['1', '2', '3'].includes(this.alarmInfo.dataSource)
      ) {
        this.imgVideoInfo.imgs = [
          {
            fileUrl: this.alarmInfo.fileImgUrlIcon
              ? this.alarmInfo.fileImgUrlIcon
              : require('@/assets/images/alarmEvent/alarm/alarmNull.png'),
          },
        ]
        this.imgVideoInfo.video = null
      } else {
        relAlarmList.length ? this.getAlarmImgVideo(relAlarmList[0]) : null
      }
    },
    // 查询告警的图片
    handleCurrentChange(item) {
      !['1', '2', '3'].includes(this.alarmInfo?.dataSource) &&
        this.getAlarmImgVideo(this.relAlarmIds[item - 1])
    },
    /**
     * 查询视频、图片
     */
    async getAlarmImgVideo(alarmId) {
      const params = {
        warningOrderId: alarmId,
      }
      const res = await queryOrderInfo(params)
      const resOrderData = { data: res?.[1] || {} }
      this.imgVideoInfo = resOrderData.data
      // 如果alarmId是this.relAlarmIds的第一个元素，则把res.data.order放入this.alarmInfo
      // if (alarmId === this.relAlarmIds[0]) {
      //   this.alarmInfo = { ...this.alarmInfo, order: res.data.order }
      // }
    },
    /**
     * 绘制事件上下游站点
     */
    drawEvtStation() {
      if (!this.alarmInfo) {
        return
      }
      const {
        stations,
        segmentLink,
        // eventTypeName,
        // startLng,
        // startLat,
        // endLng,
        // endLat,
      } = this.alarmInfo
      const [startpoint, endpoint] = stations || []
      // const stationNames = linkName?.split('-') || []
      // const startpoint = {
      //   lon: pointArray?.[0]?.[0]?.[0],
      //   lat: pointArray?.[0]?.[0]?.[1],
      //   stationName: stationNames?.[0] ?? eventTypeName,
      // }
      // const lastPoint = pointArray[0][pointArray[0].length - 1]
      // const endpoint = {
      //   lon: lastPoint[0],
      //   lat: lastPoint[1],
      //   stationName: stationNames?.[1] ?? eventTypeName,
      // }

      // 清除
      Station.clearStation()

      // 上下流站点
      if (startpoint) {
        const { lon, lat, stationName } = startpoint
        const startL = transformPointToMercator([Number(lon), Number(lat)])
        console.log('startpoint--', startpoint)
        new Station({
          id: 'startpoint-evt',
          props: {
            name: stationName,
          },
          lng: startL[0],
          lat: startL[1],
          map: this.getMapRef.mapInstance,
        }).drawSideRight()
      }
      if (endpoint) {
        const { lon, lat, stationName } = endpoint
        const endL = transformPointToMercator([Number(lon), Number(lat)])
        console.log('endpoint--', endpoint)
        new Station({
          id: 'endpoint-evt',
          props: {
            name: stationName,
          },
          lng: endL[0],
          lat: endL[1],
          map: this.getMapRef.mapInstance,
        }).drawSideLeft()
      }

      // 绘制事件上下游站点、绘制事件上下游站点连线
      if (segmentLink && segmentLink.shape) {
        const { shape } = segmentLink
        // 虚线线路
        // const pointArray = CommonMap.wktToPoint(shape, true)
        // // 绘制线路
        // evtRoute = drawMultiLine({
        //   paths: pointArray,
        //   layer: evtRoute,
        //   options: {
        //     strokeColor: '#1F90FF',
        //     strokeStyle: 'dashed',
        //     strokeWeight: 15,
        //   },
        //   mapIns: this.getMapRef.mapInstance,
        // })
        this.drawRoute(shape) // 绘制路线
      }
    },
    /**
     * 发光线路
     * 绘制路线的函数。
     * 根据当前点的信息，解析形状数据并绘制多边形路线。
     */
    drawRoute(shape) {
      const pointArray = CommonMap.wktToPoint(shape)[0]
      passRouteLineIns = new PassRouteLine({
        props: {},
        pointArray,
        map: this.getMapRef.mapInstance,
      })
    },

    /**
     * 详情后 数据处理
     */
    handleOrderData(item) {
      if (item.order?.gridId) {
        this.alarmInfo.order.gridKeeperName = item.order.gridInfo.gridKeeperName //获取网格长
      }
      this.getRecords() //获取各个环节信息
      if (
        item.order?.realLinkType !== '0' &&
        item.order?.realLinkType !== '6' &&
        this.alarmInfo?.order?.realLinkType !== '7'
      ) {
        this.getFlowLink(item.order) //查询当前环节
      } else {
        this.infoLoading = false
      }
    },
    /**
     * 查询当前环节
     */
    getFlowLink(item) {
      const params = {
        flowId: item?.flowId,
        curLink: item?.linkId,
        templateId: item?.flowTemplate, //模板id
        warningSource: item?.warningSource, //告警来源
      }
      api
        .post(
          `${this.$env.VUE_APP_REQ_PREFIX_PLAT}/event/queryFlowLink`,
          params
        )
        .then(res => {
          if (res.code == 200) {
            const flowLink = res.data
            // flowLink.forEach(linkItem => {
            //   if (String(item.linkId) === String(linkItem.linkId)) {
            //     this.rollbackFlag = linkItem.rollbackFlag === '1'
            //     this.leaderFlag = linkItem.leaderFlag === '1'
            //     this.turnFlag = linkItem.turnFlag === '1'
            //   }
            // })
            this.linkDetail = flowLink
          } else {
            this.$message({
              type: 'error',
              message: res.msg || '当前环节查询失败！',
              customClass: 'evt-file-mes-wrap',
            })
          }
          this.infoLoading = false
        })
    },
    /**
     * 处理环节信息
     */
    getRecords() {
      const _this = this
      let records = this.alarmInfo.records || []
      records.forEach(recordItem => {
        switch (recordItem.linkId) {
          case '1':
            if (!_this.judgeOverInfo.linkId) {
              _this.judgeOverInfo = recordItem //研判信息
            }
            break
          case '2':
            if (!_this.dispatchOverInfo.linkId) {
              _this.dispatchOverInfo = recordItem //调度信息
            }
            break
          case '4':
            if (!_this.handleOverInfo.linkId) {
              _this.handleOverInfo = recordItem //处置信息
            }
            break
          case '5':
            if (!_this.verifyOverInfo.linkId) {
              _this.verifyOverInfo = recordItem //核实信息
            }
            break
          // case "8":
          //   if (!_this.commandInfo.linkId) {
          //     _this.commandInfo = recordItem;//指挥调度信息
          //     _this.showCommandInfo = true;
          //   }
          //   break
          default:
        }
      })
      this.recordsInfo = this.sortRecords(records)
    },
    /**
     * 排序环节信息
     */
    sortRecords(records) {
      //环节信息 按照结束时间排序重新排序 倒叙排序
      let copyRecords = records
      let recordsInfo = []
      copyRecords.forEach((item, index) => {
        item.selected = index >= copyRecords.length - 2
        item.id = item.linkId + index
        item.orderFileList = this.mergeFiles(item)
        recordsInfo.push(item)
      })
      for (let i = recordsInfo.length - 1; i > 0; i--) {
        // 合并平台上传附件以及用户上传附件
        if (
          recordsInfo[i].realLinkType === recordsInfo[i - 1].realLinkType &&
          recordsInfo[i - 1].enableAcceptanceMode === '2'
        ) {
          recordsInfo.splice(i - 1, 1)
        }
      }
      return recordsInfo
    },
    /**
     * 合并平台上传附件以及用户上传附件
     * @param record
     */
    mergeFiles(record) {
      let files = []
      const recordFiles =
        record.files && record.files.length > 0 ? record.files : []
      const orderFileList =
        record.orderFileList && record.orderFileList.length > 0
          ? record.orderFileList
          : []
      files.push(
        ...[...recordFiles, ...orderFileList].map(file => {
          return {
            ...file,
            resourceType: file.resourceType,
            resourceUrl: file.resourceUrl,
            fileName: file.fileName,
          }
        })
      )
      return files
    },
    /**
     * 收藏事件
     */
    collEvent(item, isCollecltion) {
      const params = {
        operationType: isCollecltion, // 1-新增收藏； 0-取消收藏
        eventId: this.propData.orderId ?? item.id ?? item.orderId,
        // 原接口入参
        // warningOrderId: item.order.warningOrderId,
        // alarmTime: item.order.alarmTime,
      }
      // ${this.$env.VUE_APP_REQ_PREFIX_PLAT}/event/warningOrderCollection
      api
        .post(
          `${this.$env.VUE_APP_REQ_PREFIX_BIZ}/fusionEvent/favorite`,
          params
        )
        .then(res => {
          if (res.code == 200) {
            this.$message({
              type: 'success',
              message: res.msg,
            })
            this.refreshInfoAndList()
          } else {
            this.$message({
              type: 'error',
              message: res.msg || '收藏操作失败！',
            })
          }
        })
    },
    /**
     * 打开弹窗
     */
    async clickFunBtn(type) {
      // 开启按钮功能
      switch (type) {
        case '1': // 实时视频
          this.openRealTimeVideo()
          break
        case '2': // 一键看向告警点
          this.lookAlarmPoint()
          break
        case '3': // 周边视频
          this.openPerimeter()
          break
        case '4': // 转领导批示
          this.toAlarmDispose(HandleEvent.leaderGuidance.desc)
          break
        case '6': // 实时喊话
          this.openHorn()
          break
        case '7': // 周边分析
          this.showAround()
          break
        case '13': // 指点飞行
          this.openGuideFlight()
          break
        default:
      }
    },
    /**
     * 打开指点飞行界面
     */
    openGuideFlight() {
      const data = {
        address: this.alarmInfo?.order?.address || '',
        longitude:
          Number(this.alarmInfo?.order?.deviceLongitude || '') ||
          Number(this.alarmInfo?.order?.longitude || '') ||
          Number(this.alarmInfo.startLng || ''),
        latitude:
          Number(this.alarmInfo?.order?.deviceLatitude || '') ||
          Number(this.alarmInfo?.order?.latitude || '') ||
          Number(this.alarmInfo.startLat || ''),
        deviceCode: this.alarmInfo?.order?.deviceCode || '',
      }
      const params = {
        data,
        from: 'alarmDetail',
        showTable: true,
      }
      // 关闭指点飞行
      if (this.isShowFlight) {
        params.openStatus = false
      } else {
        params.openStatus = true
      }
      this.isShowFlight = !this.isShowFlight
      this.$globalEventBus.$emit(`common-comp-guide-flight__change`, params)
    },
    /**
     * 工单流转操作
     */
    toAlarmDispose(type) {
      if (this.openDialog[type] && this.openDialog[type].selected) {
        this.setOpenDialog(type, false)
        bottomWrap.closeByIdPrefix(HandleWinCfg.winCfg.idPrefix)
        return
      }
      this.setOpenDialog(type, true)
      const winCfg = HandleWinCfg[type]
      bottomWrap.closeByIdPrefix(HandleWinCfg.winCfg.idPrefix)
      const { order } = this.alarmInfo
      const { warningOrderId } = order
      const windId = `${HandleWinCfg.winCfg.idPrefix}_${warningOrderId}`
      bottomWrap.addContent({
        component: winCfg.component(this.alarmInfo),
        props: winCfg.props(this.alarmInfo),
        key: windId,
        onok: () => {
          winCfg.onok(this)
          bottomWrap.closeContent(windId)
        },
        onclose: () => {
          winCfg.onclose && winCfg.onclose(this)
          this.setOpenDialog(type, false)
          bottomWrap.closeContent(windId)
        },
      })
    },
    /**
     * 刷新详情 刷新列表
     */
    async refreshInfoAndList(freshOrderStatus = false) {
      //刷新详情
      await this.getAlarmInfo(freshOrderStatus)
      // 当事件详情底部按钮操作时需要上面方法中 fusionEvent/updateEvent 调用完成
      //刷新右侧面板列表
      this.$EventBus.$emit('refreshAlarmList')
    },
    /**
     * 关闭详情 刷新列表
     */
    async closeInfoAndList() {
      //刷新详情
      await this.onCloseDetail()
      // 当事件详情底部按钮操作时需要上面方法中 fusionEvent/updateEvent 调用完成
      //刷新右侧面板列表
      this.$EventBus.$emit('refreshAlarmList')
    },
    /**
     * 功能弹窗关闭
     */
    closeFunDialog(data) {
      this.setOpenDialog(data, false)
    },
    /**
     * 打开实时视频
     */
    openRealTimeVideo() {
      const type = '1'
      const list = [
        {
          deviceCode: this.alarmInfo?.deviceCode, //设备编号
          channelCode: this.alarmInfo?.channelCode, //通道编号
          longitude: this.alarmInfo?.startLat, //经度
          latitude: this.alarmInfo?.startLng, //维度l
          showClose: true, //是否显示视频关闭按钮
        },
      ]
      if (this.openDialog[type] && this.openDialog[type].selected) {
        // 这个必须在弹窗关闭之前关闭，否则重置之后list 会丢失
        $playerFit.close(list)
        this.setOpenDialog(type, false)
      } else {
        // 清空看这里视频
        $playerFit.close([])
        this.setOpenDialog('2', false)
        this.setOpenDialog(type, true, { list })
        $playerFit.appendLeftSmall(list, 'append')
      }
    },
    /**
     * 关闭实时视频
     */
    closeRealTimeVideo(e) {
      // 监听关闭事件，如果关闭的视频窗口的channelCode和当前告警点的channelCode一致，则关闭弹窗
      if (
        e.data &&
        e.data.callBackMethod === 'bigScreenPlayerClose' &&
        this.alarmInfo.order.channelCode == e.data.videoData?.channelCode
      ) {
        $playerFit.close(this.openDialog['1'].list)
        this.setOpenDialog('1', false)
      }
      if (
        e.data &&
        e.data.callBackMethod === 'bigScreenPlayerClose' &&
        e.data.videoData?.playVideoNum == 0
      ) {
        $playerFit.close([])
        this.setOpenDialog('1', false)
        this.setOpenDialog('2', false)
      }
    },
    /**
     * 一键看向告警点
     */
    async lookAlarmPoint() {
      const type = '2'
      if (this.openDialog[type] && this.openDialog[type].selected) {
        $playerFit.close(this.openDialog[type].list)
        this.setOpenDialog(type, false)
      } else {
        // 清空实时视频
        $playerFit.close([])
        this.setOpenDialog('1', false)
        //  deviceLongitude, deviceLatitude
        const { startLat, startLng } = this.alarmInfo || {}
        const [success, data] = await lookHere({
          longitude: startLng,
          latitude: startLat,
          urlNum: 5,
          distance: 5000,
        })
        if (!success || !data || !data.urlList || data.urlList.length < 1) {
          this.$message.warning('未查询到附近的摄像机')
          return
        }
        const { urlList } = data
        const list = urlList.map(item => {
          return {
            deviceCode: item.deviceCode, //设备编号
            channelCode: item.channelCode, //通道编号
            longitude: Number(item.longitude), //经度
            latitude: Number(item.latitude), //维度
            showClose: true, //是否显示视频关闭按钮
            lookHere: {
              //转动ptz
              turnCallBack: 'turnCallBackMethod',
              ptzInfo: {
                deviceCode: item.deviceCode, //设备编号
                channelCode: item.channelCode, //通道编号
                cameraLon: Number(item.longitude), //摄像机经纬度
                cameraLat: Number(item.latitude),
                pointLon: this.alarmInfo?.order?.longitude, //选点经纬度
                pointLat: this.alarmInfo?.order?.latitude,
              },
            },
          }
        })
        this.setOpenDialog(type, true, {
          list,
        })
        $playerFit.appendLeftSmall(list, 'append')
      }
    },
    /**
     * 周边视频
     */
    openPerimeter() {
      const type = '3'
      if (this.openDialog[type] && this.openDialog[type].selected) {
        this.setOpenDialog(type, false)
        $playerFit.close(this.openDialog[type].list)
      } else {
        let urlList = []
        const params = {
          longitude: this.alarmInfo?.order?.longitude,
          latitude: this.alarmInfo?.order?.latitude,
          deviceCode: this.alarmInfo?.order?.deviceCode,
          channelCode: this.alarmInfo?.order?.channelCode,
        }
        api
          .post(
            `${this.$env.VUE_APP_REQ_PREFIX_PLAT}/device/getVideoAndUrl`,
            params
          )
          .then(res => {
            if (res.code == 200) {
              res.data.urlList.forEach(item => {
                //将摄像机看向选点
                item.lookHere = {
                  turnCallBack: 'turnCallBackMethod',
                  ptzInfo: {
                    deviceCode: item.deviceCode, //设备编号
                    channelCode: item.channelCode, //通道编号
                    cameraLon: Number(item.longitude), //摄像机经纬度
                    cameraLat: Number(item.latitude),
                    pointLon: this.alarmInfo?.order?.longitude, //选点经纬度
                    pointLat: this.alarmInfo?.order?.latitude,
                  },
                }
              })
              urlList = res.data.urlList
              this.setOpenDialog(type, false, { list: urlList })
              $playerFit.right(urlList, 'append')
            } else {
              this.$message({
                type: 'error',
                message: res.msg || '周边视频查询失败！',
              })
            }
          })
      }
    },
    setOpenDialog(type, open, props = {}) {
      Object.keys(this.openDialog).forEach(key => {
        if (type == key) {
          this.openDialog[key].selected = open
        }
      })
      this.openDialog = {
        ...this.openDialog,
        [type]: {
          selected: open,
          ...props,
        },
      }
    },
    /**
     * 打开实时喊话列表
     */
    async openHorn() {
      bottomWrap.closeByIdPrefix(`bottomHorn_`)
      const type = '6'
      if (this.openDialog[type] && this.openDialog[type].selected) {
        this.setOpenDialog(type, false)
        return
      }
      this.setOpenDialog(type, true)
      const { code, data } = await api.get(
        `${this.$env.VUE_APP_REQ_PREFIX_BIZ}/deviceFacilities/getHornDeviceListByDeviceCode`,
        { deviceCode: this.alarmInfo?.deviceCode }
      )
      if (code === 200 && data && data.length > 0) {
        bottomWrap.addContent({
          component: BroadCastCtl,
          props: { devices: data },
          key: `bottomHorn_${this.alarmInfo?.deviceCode}`,
          onclose: () => {
            bottomWrap.closeContent(`bottomHorn_${this.alarmInfo?.deviceCode}`)
            this.setOpenDialog(type, false)
          },
        })
      } else {
        this.$message({
          type: 'error',
          message: '摄像机未绑定云广播设备',
        })
      }
    },
    /**
     * 唤起周边分析
     */
    showAround() {
      this.isShowAround = !this.isShowAround
      const type = '7'
      if (this.openDialog[type] && this.openDialog[type].selected) {
        this.setOpenDialog(type, false)
      } else {
        this.setOpenDialog(type, true)
      }
      if (this.isShowAround) {
        //开启监听周边分析关闭
        bus.$on('aroundAnaClose', () => {
          console.log(
            '%c █░░░░░░░░░░░░█ ,注释 监听到周边分析关闭',
            'color: #FAC800'
          )
          this.setOpenDialog(type, false)
        })
        //唤起周边分析
        const orderInfo = {
          longitude: this.alarmInfo?.order?.longitude,
          latitude: this.alarmInfo?.order?.latitude,
        }
        bus.$emit('callAroundAna', orderInfo)
      } else {
        //关闭
        bus.$emit('closeAroundAny')
      }
    },
  },
}
</script>
<style lang='less' src='./index.less' />
<style scoped lang="scss">
@import '~@/assets/styles/px-to-rem';
.pageer {
  height: px-to-rem(33);
  line-height: px-to-rem(33);
  display: flex;
  align-items: center;
  justify-content: center;

  :deep {
    .el-pagination {
      line-height: px-to-rem(14);
    }
    .el-pagination.is-background .btn-next.is-disabled,
    .el-pagination.is-background .btn-next:disabled,
    .el-pagination.is-background .btn-prev.is-disabled,
    .el-pagination.is-background .btn-prev:disabled,
    .el-pagination.is-background .el-pager li.is-disabled,
    .el-pagination.is-background .el-pager li:disabled {
      background: transparent;
    }
    .el-pagination.is-background .btn-next,
    .el-pagination.is-background .btn-prev,
    .el-pagination.is-background .el-pager li {
      background: transparent;
      color: #ffffff;
      min-width: px-to-rem(14);
      height: px-to-rem(14);
      line-height: px-to-rem(14);
    }

    .el-pagination.is-background .el-pager li:not(.disabled).active {
      background-color: #1989fa !important;
    }
  }
}
.time-line-wraper {
  padding: px-to-rem(17) 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}
</style>
