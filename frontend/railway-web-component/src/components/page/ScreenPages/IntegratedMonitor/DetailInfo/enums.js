/**
 * 导入一个用于检查值是否为空或未定义的实用函数。
 */
import { isEmptyOrUndefined } from '@/components/common/utils';

/**
 * 枚举对象，包含各种设备和状态的枚举定义。
 */
export const enums = {
  /**
   * 相机设备相关枚举。
   */
  CAMERA: {
    /**
     * 相机开关状态枚举。
     */
    ON: '0', // 相机开启状态
    OFF: '1', // 相机关闭状态
    /**
     * 相机设备状态定义。
     */
    deviceStatus: {
      0: {
        name: '在线', // 在线状态名称
        color: 'green', // 在线状态颜色
      },
      1: {
        name: '离线', // 离线状态名称
        color: 'gray', // 离线状态颜色
      },
    },
    /**
     * 相机设备状态枚举，包含更详细的设备状态。
     */
    deviceStatusEnum: {
      onLine: {
        name: '在线', // 在线状态名称
        code: '0', // 在线状态代码
      },
      offLine: {
        name: '离线', // 离线状态名称
        code: '1', // 离线状态代码
      },
      fault: {
        name: '故障', // 故障状态名称
        code: '2', // 故障状态代码
      },
    },
  },
  /**
   * 扬声器设备相关枚举。
   */
  LOUDSPEAKER: {
    /**
     * 扬声器设备类型定义。
     */
    deviceType: {
      // 音柱
      soundColumn: {
        code: '4001', // 音柱设备代码
        name: '音柱', // 音柱设备名称
      },
      // 扬声器
      speaker: {
        code: '4002', // 扬声器设备代码
        name: '扬声器', // 扬声器设备名称
      },
    },
    /**
     * 扬声器设备状态分组定义。
     */
    deviceStatusGroup: {
      0: {
        name: '离线', // 离线状态名称
        group: '异常', // 离线状态分组
        color: 'gray', // 离线状态颜色
      },
      1: {
        name: '空闲', // 空闲状态名称
        group: '正常', // 空闲状态分组
        color: 'green', // 空闲状态颜色
      },
      2: {
        name: '工作中', // 工作中状态名称
        group: '正常', // 工作中状态分组
        color: 'green', // 工作中状态颜色
      },
      3: {
        name: '故障', // 故障状态名称
        group: '异常', // 故障状态分组
        color: 'red', // 故障状态颜色
      },
      4: {
        name: '从未连接', // 从未连接状态名称
        group: '异常', // 从未连接状态分组
        color: 'red', // 从未连接状态颜色
      },
      5: {
        name: '空闲',
        group: '正常',
        color: 'green',
      },
      /**
       * 根据状态获取设备状态名称，如果状态未知，则返回'未知'。
       * @param {any} status 设备状态码。
       * @returns {string} 设备状态名称。
       */
      get(status) {
        if (isEmptyOrUndefined(status)) {
          return '未知'; // 如果状态为空或未定义，返回'未知'
        }
        return this[status] ? this[status].name : '未知'; // 返回对应状态名称或'未知'
      },
    },
    /**
     * 扬声器设备状态定义。
     */
    deviceStatus: {
      playing: {
        code: '2', // 播放中状态代码
        name: '播放中', // 播放中状态名称
      },
      idle: {
        code: '1', // 空闲状态代码
        name: '空闲', // 空闲状态名称
      },
      fault: {
        code: '3', // 故障状态代码
        name: '故障', // 故障状态名称
      },
      offline: {
        code: '0', // 离线状态代码
        name: '离线', // 离线状态名称
      },
      unconnect: {
        code: '4', // 从未连接状态代码
        name: '从未连接', // 从未连接状态名称
      },
      todayExistsFail: {
        code: '5', // 当天存在播放失败状态代码
        name: '当天存在播放失败', // 当天存在播放失败状态名称
      },
    },
  },
  /**
   * 报警事件相关枚举。
   */
  ALARM_EVENT: {
    /**
     * 报警事件处理状态枚举。
     */
    orderStatus: {
      end: ['0', '6', '7'], // 结束状态代码列表
      pending: ['1'], // 待处理状态代码列表
      /**
       * 根据状态返回对应的颜色。
       * @param {any} status 报警事件处理状态。
       * @returns {string} 颜色代码。
       */
      color(status) {
        if (this.end.includes(status)) {
          return '#E8F3FF'; // 结束状态颜色
        }
        if (this.pending.includes(status)) {
          return '#FB913C'; // 待处理状态颜色
        }
        return '#4F9FFF'; // 默认状态颜色
      },
    },
  },
  /**
   * 畜牧设备相关枚举。
   */
  ANIMAL: {
    /**
     * 畜牧设备状态定义。
     */
    state: {
      0: {
        name: '在线', // 在线状态名称
        color: 'green', // 在线状态颜色
      },
      1: {
        name: '离线', // 离线状态名称
        color: 'gray', // 离线状态颜色
      },
    },
    // deviceStatus
    deviceStatus: {
      0: {
        name: '未启用', // 未启用状态名称
        color: 'white', // 未启用状态颜色
      },
      1: {
        name: '运动', // 运动状态名称
        color: 'green', // 运动状态颜色
      },
      2: {
        name: '静止', // 静止状态名称
        color: 'blue', // 静止状态颜色
      },
      3: {
        name: '离线', // 离线状态名称
        color: 'gray', // 离线状态颜色
      },
      4: {
        name: '欠费', // 欠费状态名称
        color: 'yellow', // 欠费状态颜色
      },
    },
    /**
     * 相机设备状态枚举，包含更详细的设备状态。
     */
    deviceStatusEnum: {
      noUse: {
        name: '未启用', // 未启用状态名称
        code: '0', // 未启用状态代码
      },
      move: {
        name: '运动', // 运动状态名称
        code: '1', // 运动状态代码
      },
      stop: {
        name: '静止', // 静止状态名称
        code: '2', // 静止状态代码
      },
      offline: {
        name: '离线', // 离线状态名称
        code: '3', // 离线状态代码
      },
      arrears: {
        name: '欠费', // 欠费状态名称
        code: '4', // 欠费状态代码
      },
    },
  },
};
