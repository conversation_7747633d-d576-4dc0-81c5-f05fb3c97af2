@import "../common";

@content-width: 360px;
.detail-info-wrap {
  height: auto;
  background-color: @background-color;
  padding: 12px 12px 0 12px;
  border-radius: 8px;
  box-sizing: border-box;

  > div {
    width: 100%;
  }

  .detail-info-header {
    font-family: PingFangSC-Medium;
    font-size: 14px;
    color: #e8f3ff;
    letter-spacing: 0;
    font-weight: 500;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;

    .el-icon-error {
      font-size: 18px;
      margin-left: auto;
    }
  }

  .detail-info-content {
    padding-bottom: 12px;
    overflow: hidden;
    overflow-y: scroll;

    > div {
      width: 100%;
    }

    .detail-info-sub-title {
      font-family: PingFangSC-Regular;
      font-size: 14px;
      height: 30px;
      color: #e8f3ff;
      letter-spacing: 0;
      font-weight: 400;
      display: flex;
      align-items: center;
      justify-content: flex-start;
    }

    .detail-info-sub-content {
      height: auto;

      &.single-head {
        margin-left: 20px;
        background: #192b41;
        border: 1px dashed #254368;
        width: auto;
        margin-top: 10px;
        border-radius: 4px;
        position: relative;
        cursor: pointer;

        &.clickable {
          &:hover,
          &.selected {
            .detail-info-item .detail-info-item-label,
            .detail-info-item-value {
              color: #4f9fff !important;
            }

            border: 1px solid #254368;
          }
        }

        .item-index {
          position: absolute;
          left: -1px;
          top: 0;
          width: 15px;
          height: 15px;
          font-family: PingFangSC-Regular;
          font-style: normal;
          border-radius: 2px;
          font-size: 10px;
          color: #fefefe;
          background: #4f9fff;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .detail-info-item {
          padding-left: 10px;
        }
      }
    }

    .detail-info-item {
      padding-left: 30px;
      display: flex;
      align-items: flex-start;
      justify-content: flex-start;
      margin: 6px 0;

      .detail-info-item-label {
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: rgba(232, 243, 255, 0.7);
        letter-spacing: 0;
        font-weight: 400;
        display: inline-block;
      }

      .detail-info-item-value {
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #e8f3ff;
        letter-spacing: 0;
        font-weight: 400;
        display: flex;
        align-items: flex-start;
        justify-content: flex-start;
        max-width: 90%;
        flex: 1;
        .value {
          word-break: break-all; /* 指定超过容器宽度后将不再按字符断开而直接换行 */
        }

        &[stat="CAMERA-deviceStatus-0"] {
          .value {
            color: #15bd94;
          }
        }

        &[stat="CAMERA-deviceStatus-1"] {
          .value {
            color: gray;
          }
        }

        &[stat="LOUDSPEAKER-deviceStatus-0"] {
          .value {
            color: gray;
          }
        }

        &[stat="LOUDSPEAKER-deviceStatus-1"] {
          .value {
            color: #15bd94;
          }
        }

        &[stat="LOUDSPEAKER-deviceStatus-5"] {
          .value {
            color: #15bd94;
          }
        }

        &[stat="LOUDSPEAKER-deviceStatus-2"] {
          .value {
            color: green;
          }
        }

        &[stat="LOUDSPEAKER-deviceStatus-3"] {
          .value {
            color: red;
          }
        }
        &[stat="LOUDSPEAKER-deviceStatus-4"] {
          .value {
            color: red;
          }
        }

        &[stat="ANIMAL-devcStatusName-0"] {
          .value {
            color: #aab6c3;
          }
        }

        &[stat="ANIMAL-devcStatusName-1"] {
          .value {
            color: #15987d;
          }
        }

        &[stat="ANIMAL-devcStatusName-2"] {
          .value {
            color: #3b75ba;
          }
        }

        &[stat="ANIMAL-devcStatusName-3"] {
          .value {
            color: #6d747d;
          }
        }
        &[stat="ANIMAL-devcStatusName-4"] {
          .value {
            color: #d08e3e;
          }
        }

        &[stat="ANIMAL-isStopName-0"] {
          .value {
            color: #15987d;
          }
        }
        &[stat="ANIMAL-isStopName-1"] {
          .value {
            color: red;
          }
        }

        .el-icon-document-copy {
          margin-left: 6px;
        }
      }
    }
  }

  .detail-bottom-content {
    width: 100%;
    height: 40px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: flex-start;

    .img-wrap {
      margin-left: 10px;
      width: 18px;
      height: 18px;
      cursor: pointer;
    }
  }
}

.main-title {
  width: 100%;
  height: 40px;
  margin: 0 10px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  background: url("./img/bg_main_title_bg.svg") 0 100% no-repeat;

  .main-title-icon {
    height: 40px;
    width: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: url("./img/icon_main_title_icon.svg") 100% 100% no-repeat;
    background-size: 100% 100%;
    margin: 0 10px;
    .icon {
      margin-left: -6px;
    }
    /deep/.svg-icon {
      margin-right: 0px !important;
    }
  }
  .el-icon-error {
    cursor: pointer;
    align-self: flex-start;
  }
}

.sub-title {
  width: 100%;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  background: url("./img/title_sub_title_bg.png") 100% 100% no-repeat;
  background-size: 100% 50%;

  .sub-title-icon {
    height: 20px;
    width: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: url("./img/icon_sub_title_icon.svg") 100% 100% no-repeat;
    background-size: 100% 100%;
    margin: 0 10px;
  }
}
