<!--
 * @Description:
 * @Autor: he.chao
 * @Date: 2022-10-19 15:07:55
 * @LastEditors: liu.yongli
 * @LastEditTime: 2024-07-18 19:38:57
-->
<template>
  <!-- 只有在contentCfg中存在对应的markerType时才渲染 -->
  <div
    class="detail-info-wrap"
    v-if="contentCfg[propData.markerType]"
    :style="{
      width:propData.width ? pxToRem(+propData.width): '3.6rem',
    }"
  >
    <!-- 显示主标题 -->
    <div class="detail-info-header" v-if="showMainTitle">
      <MainTitle>
        <template v-slot:icon>
          <icon-title-detail />
        </template>
        <template v-slot:text>{{ contentCfg[propData.markerType].title }}</template>
        <template v-slot:close>
          <!-- 点击关闭图标触发关闭事件 -->
          <i class="el-icon-error" @click="close" />
        </template>
      </MainTitle>
    </div>
    <template>
      <!-- 详情内容区域 -->
      <div
        class="detail-info-content"
        :style="{
          height: pxToRem(propData.height - 40 - 12),
        }"
      >
        <!-- 遍历分组信息 -->
        <div
          v-for="(group,i) in contentCfg[propData.markerType].groups"
          :key="`${group.groupName || ''}_${i}`"
        >
          <!-- 如果分组有路径且detailInfo中有对应数据 -->
          <template v-if="group.path && detailInfo[group.path]">
            <!-- 如果分组没有单独的头部 -->
            <template v-if="!group.singleHead">
              <div
                v-for="(cycleInfo,idx) in detailInfo[group.path]"
                :key="`${group.groupName}_${idx}`"
              >
                <!-- 显示子标题 -->
                <div class="detail-info-sub-title">
                  <SubTitle>
                    <template v-slot:text>{{ group.groupName }}</template>
                  </SubTitle>
                </div>
                <!-- 显示子内容 -->
                <div class="detail-info-sub-content">
                  <!-- 遍历内容项 -->
                  <div
                    v-for="(content, contentIndex) in group.content"
                    class="detail-info-item"
                    :key="`${content.key}-${contentIndex}`"
                  >
                    <div class="detail-info-item-label">{{ content.label }}</div>
                    <div
                      class="detail-info-item-value"
                      :stat="`${propData.markerType}-${content.key}-${
                        cycleInfo[content.code] || ''
                      }`"
                    >
                      <!-- 如果有渲染函数则使用渲染函数，否则显示默认值 -->
                      <span v-if="content.render" class="value">
                        {{
                        content.render(cycleInfo[content.key]) || '-'
                        }}
                      </span>
                      <span v-else class="value">
                        {{
                        cycleInfo[content.key] || '-'
                        }}
                      </span>
                      <!-- 如果有复制图标则显示复制图标 -->
                      <icon-copy
                        v-if="
                          content.icon && content.icon.includes(iconCode.copy)
                        "
                        @click.native="copy(cycleInfo[content.key], $event)"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </template>
            <!-- 如果分组有单独的头部 -->
            <template v-if="group.singleHead">
              <div class="detail-info-sub-title">
                <SubTitle>
                  <template v-slot:text>{{ group.groupName }}</template>
                </SubTitle>
              </div>
              <div
                :class="`detail-info-sub-content single-head ${
                  group.clickable && 'clickable'
                } ${
                  cycleInfo[group.dataKey] === selected[propData.markerType]
                    ? 'selected'
                    : ''
                }`"
                v-for="(cycleInfo, index) in detailInfo[group.path]"
                @click="group.onclick && group.onclick(cycleInfo, groupOnclick)"
                :key="index"
              >
                <i class="item-index">{{ index + 1 }}</i>
                <div
                  v-for="(content, contentIndex) in group.content"
                  class="detail-info-item"
                  :key="`${content.key}-${contentIndex}`"
                >
                  <div class="detail-info-item-label">{{ content.label }}</div>
                  <div
                    class="detail-info-item-value"
                    :stat="`${propData.markerType}-${content.key}-${
                      cycleInfo[content.code] || ''
                    }`"
                  >
                    <span class="value">
                      {{
                      content.render
                      ? content.render(cycleInfo[content.key])
                      : cycleInfo[content.key] || '-'
                      }}
                    </span>
                    <icon-copy
                      v-if="
                        content.icon && content.icon.includes(iconCode.copy)
                      "
                      @click.native="copy(cycleInfo[content.key], $event)"
                    />
                  </div>
                </div>
              </div>
            </template>
          </template>
          <!-- 如果分组没有路径或detailInfo中没有对应数据 -->
          <template v-else>
            <div class="detail-info-sub-title">
              <SubTitle>
                <template v-slot:text>{{ group.groupName }}</template>
              </SubTitle>
            </div>
            <div class="detail-info-sub-content">
              <div
                v-for="(content, contentIndex) in group.content"
                class="detail-info-item"
                :key="`${content.key}-${contentIndex}`"
              >
                <!-- 如果输入类型是图片 -->
                <template v-if="content.inputType && content.inputType === 'image'">
                  <img alt="图片" :src="detailInfo[content.key]" width="270px" height="150px" />
                </template>
                <!-- 如果输入类型是视频图片 -->
                <template v-if="content.inputType && content.inputType === 'videoImage'">
                  <AlarmFileCarousel :alarmInfo="getAlarmInfo(content.key)" />
                </template>
                <!-- 其他情况 -->
                <template v-else>
                  <div class="detail-info-item-label">{{ content.label }}</div>
                  <div
                    class="detail-info-item-value"
                    :stat="`${propData.markerType}-${content.key}-${
                      content.code && detailInfo[content.code]
                    }`"
                  >
                    <span v-if="content.render" class="value">
                      {{
                      content.render(detailInfo[content.key]) || ''
                      }}
                    </span>
                    <span v-else class="value">
                      {{
                      detailInfo[content.key] || '-'
                      }}
                    </span>
                    <icon-copy
                      v-if="
                        content.icon && content.icon.includes(iconCode.copy)
                      "
                      @click.native="copy(detailInfo[content.key], $event)"
                    />
                  </div>
                </template>
              </div>
            </div>
          </template>
        </div>
      </div>
      <!-- 底部内容区域 -->
      <div class="detail-bottom-content">
        <!-- 播放视频图标 -->
        <icon-meta-play-video
          v-if="ctlCfg.includes(iconCode.playVideo)"
          :class="`${iconActiveStat[iconCode.playVideo] ? 'active' : ''}`"
          @click.native="iconClick(iconCode.playVideo)"
        />
        <!-- 查看区域图标 -->
        <icon-video-view-area
          v-if="ctlCfg.includes(iconCode.viewArea)"
          :class="`${iconActiveStat[iconCode.viewArea] ? 'active' : ''}`"
          @click.native="iconClick(iconCode.viewArea)"
        />
        <!-- 广播列表图标 -->
        <icon-broadcast-list
          v-if="ctlCfg.includes(iconCode.broadcast)"
          :class="`${iconActiveStat[iconCode.broadcast] ? 'active' : ''}`"
          @click.native="iconClick(iconCode.broadcast)"
        />
        <!-- 实时广播图标 -->
        <icon-reltime-broadcast
          v-if="ctlCfg.includes(iconCode.reltimeBroadcast)"
          :class="`${
            iconActiveStat[iconCode.reltimeBroadcast] ? 'active' : ''
          }`"
          @click.native="iconClick(iconCode.reltimeBroadcast)"
        />
        <!-- 历史轨迹图标 -->
        <img
          alt="历史轨迹"
          title="历史轨迹"
          :src="
            require(`./img/icon_locus_${
              iconActiveStat[iconCode.historyLocus] ? 's' : 'n'
            }.svg`)
          "
          v-if="ctlCfg.includes(iconCode.historyLocus)"
          @click="iconClick(iconCode.historyLocus)"
          class="img-wrap"
        />
        <!-- 过车信息 -->
        <img
          alt="过车信息"
          title="过车记录"
          :src="
            require(`../InfoWindow/img/${
              iconActiveStat[iconCode.passRoute]
                ? 'icon_pass_route_18_s'
                : 'icon_pass_route_12_n'
            }.svg`)
          "
          v-if="ctlCfg.includes(iconCode.passRoute)"
          @click="iconClick(iconCode.passRoute)"
          class="img-wrap"
        />
      </div>
    </template>
  </div>
</template>
<script>
import CommonMap from '@/components/common/Map/CommonMap'
import BroadCastCtl from '@/components/page/ScreenPages/IntegratedMonitor/BroadCastCtl'
import AnimalLocusCtl from '@/components/page/ScreenPages/IntegratedMonitor/AnimalLocus'
import BroadList from '@/components/page/ScreenPages/IntegratedMonitor/BroadList'
import MainTitle from '@/components/page/ScreenPages/IntegratedMonitor/DetailInfo/MainTitle'
import SubTitle from '@/components/page/ScreenPages/IntegratedMonitor/DetailInfo/SubTitle.vue'
import { Events } from '@/components/page/ScreenPages/IntegratedMonitor/enum/EventsEnum'
import AlarmFileCarousel from '@/components/page/ScreenPages/IntegratedMonitor/EventDetailInfo/components/alarm-file-carousel.vue'
import iconBroadcastList from '@/components/page/ScreenPages/IntegratedMonitor/Icons/icon-broadcast-list.vue'
import IconCopy from '@/components/page/ScreenPages/IntegratedMonitor/Icons/icon-copy.vue'
import IconMetaPlayVideo from '@/components/page/ScreenPages/IntegratedMonitor/Icons/icon-meta-play-video.vue'
import iconReltimeBroadcast from '@/components/page/ScreenPages/IntegratedMonitor/Icons/icon-reltime-broadcast.vue'
import IconTitleDetail from '@/components/page/ScreenPages/IntegratedMonitor/Icons/icon-title-detail.vue'
import IconVideoViewArea from '@/components/page/ScreenPages/IntegratedMonitor/Icons/icon-video-view-area.vue'
import IconPassRoute from '@/components/page/ScreenPages/IntegratedMonitor/Icons/icon-pass-route.vue'
import LayerConst from '@/components/page/ScreenPages/IntegratedMonitor/Map/LayerConst'
import { $playerFit } from '@/utils/playerFit'
import { iconCode } from './ContentConfig'
import {
  tunnelDetailData,
  cameraDetailData,
} from '@/components/page/ScreenPages/IntegratedMonitor/mockData'
import api from '@/api'
import { transformPointToMercator } from '@/utils'

const layerCache = {}
// 解决 sonar
const eachFunc = (resData, defaultViewShed) => {
  const views = resData.map(item => {
    return {
      visualRange:
        Number(
          item?.visualRange && item?.visualRange !== '0'
            ? item.visualRange
            : '0.5'
        ) * 1000,
      // 可视域方向
      horizViewRange: Number(
        item?.horizViewRange && item?.horizViewRange !== '0'
          ? item.horizViewRange
          : defaultViewShed.horizViewRange // this.defaultViewShed.horizViewRange
      ),
      // 可视域角度
      horizRange: Number(
        item?.horizRange && item?.horizRange !== '0'
          ? item.horizRange
          : defaultViewShed.horizRange // this.defaultViewShed.horizRange
      ),
    }
  })
  return views
}

export default {
  computed: {
    getMapRef() {
      return this.$store.state.map.mapStoreRef
    },
    getAlarmInfo() {
      return key => this.detailInfo?.[key] || {}
    },
  },
  props: {
    propData: {
      type: Object,
      default: {},
    },
    close: {
      type: Function,
      default: () => {
        console.log('close')
      },
    },
    showMainTitle: {
      type: Boolean,
      default: true,
    },
  },
  components: {
    AlarmFileCarousel,
    BroadList,
    BroadCastCtl,
    IconVideoViewArea,
    IconMetaPlayVideo,
    SubTitle,
    IconTitleDetail,
    MainTitle,
    IconCopy,
    iconBroadcastList,
    iconReltimeBroadcast,
    AnimalLocusCtl,
    IconPassRoute,
  },
  data: function () {
    return {
      contentCfg: this.propData.contentCfg, // 内容配置
      iconCode, // 图标代码
      ctlCfg: [], // 控制配置
      detailInfo: {}, // 详细信息
      iconActiveStat: {}, // 图标激活状态
      iconHandle: {}, // 图标处理函数
      viewAreaLayer: '', // 视域层
      selected: {}, // 选中状态
      defaultViewShed: {
        // 默认视域设置
        visualRange: 3000,
        height: '10',
        horizViewRange: '157.79',
        vertiViewRange: '-2.90',
        horizRange: '36.52',
        vertiRange: '27.00',
      },
    }
  },
  mounted() {
    this.iconHandle = this.getIconHandle() // 获取图标处理函数
    this.getDetailInfo() // 获取详细信息
    window.addEventListener('message', this.closeRealTimeVideo) // 添加消息事件监听
    this.$EventBus.$on(Events.DEVICE_DETAIL.GROUP_CLICK, this.handleGroupClick) // 监听分组点击事件
    this.$EventBus.$on(
      Events.PassRouteEvt.CHANGE_PASS_ROUTE_OPEN_STATUS,
      this.initPassRouteIconState
    )
  },
  beforeDestroy() {
    Object.keys(this.iconActiveStat).forEach(key => {
      if (this.iconActiveStat[key]) {
        this.iconHandle[key].cancel() // 取消激活状态
      }
    })
    this[iconCode.playVideo] &&
      $playerFit.close(this[iconCode.playVideo].bizParam) // 关闭视频播放
    window.removeEventListener('message', this.closeRealTimeVideo) // 移除消息事件监听
    this.$EventBus.$off(Events.DEVICE_DETAIL.GROUP_CLICK) // 移除分组点击事件监听
    this.clearViewArea() // 清除视域分析
    this.close({ destroy: true }) // 关闭组件
    this.$EventBus.$off(
      Events.PassRouteEvt.CHANGE_PASS_ROUTE_OPEN_STATUS,
      this.initPassRouteIconState
    )
  },
  methods: {
    /**
     * 初始化过车弹窗图标状态
     */
    initPassRouteIconState({ open }) {
      if (this.ctlCfg.includes(iconCode.passRoute)) {
        if (!open && this.iconActiveStat[iconCode.passRoute]) {
          this.iconActiveStat[iconCode.passRoute] = false
        }
        if (open && !this.iconActiveStat[iconCode.passRoute]) {
          this.iconActiveStat[iconCode.passRoute] = true
        }
      }
    },
    /**
     * 处理分组点击事件
     * 根据不同的markerType执行不同的操作
     * @returns {Object} 返回一个包含不同markerType处理函数的对象
     */
    groupClickHandle() {
      const _this = this
      return {
        [LayerConst.CAMERA]: {
          handle: data => {
            // 打开视频播放器
            _this.iconHandle[iconCode.playVideo].handle()
          },
        },
      }
    },
    /**
     * 获取图标的处理函数
     * 根据不同的图标代码(iconCode)返回对应的处理函数和键值(key)
     * @returns {Object} 返回一个包含不同图标处理函数和键值的对象
     */
    getIconHandle() {
      const that = this
      return {
        [iconCode.broadcast]: {
          key: `${iconCode.broadcast}-${that.propData.id}`,
          handle: () => {
            // 添加广播设备列表组件到底部包装器
            bottomWrap.addContent({
              component: BroadList,
              props: {
                devices: that.getBroadCaseDevice(),
              },
              key: this.iconHandle[iconCode.broadcast].key,
              onclose: () => {
                // 关闭底部包装器中的广播设备列表组件
                bottomWrap.closeContent(this.iconHandle[iconCode.broadcast].key)
                that.iconActiveStat = {
                  ...that.iconActiveStat,
                  [iconCode.broadcast]: false,
                }
              },
            })
          },
          cancel: () => {
            // 直接关闭底部包装器中的广播设备列表组件
            bottomWrap.closeContent(this.iconHandle[iconCode.broadcast].key)
          },
        },
        [iconCode.reltimeBroadcast]: {
          key: `${iconCode.reltimeBroadcast}-${that.propData.id}`,
          handle: async () => {
            const devices = that.getBroadCaseDevice()
            if (
              that.propData.markerType === LayerConst.CAMERA &&
              (!devices || devices.length < 1)
            ) {
              that.$message.error('摄像机未绑定云广播设备')
              return
            }
            // 添加实时广播控制组件到底部包装器
            bottomWrap.addContent({
              component: BroadCastCtl,
              props: {
                devices: that.getBroadCaseDevice(),
              },
              key: this.iconHandle[iconCode.reltimeBroadcast].key,
              onclose: () => {
                // 关闭底部包装器中的实时广播控制组件
                bottomWrap.closeContent(
                  this.iconHandle[iconCode.reltimeBroadcast].key
                )
                that.iconActiveStat = {
                  ...that.iconActiveStat,
                  [iconCode.reltimeBroadcast]: false,
                }
              },
            })
          },
          cancel: () => {
            // 直接关闭底部包装器中的实时广播控制组件
            bottomWrap.closeContent(
              this.iconHandle[iconCode.reltimeBroadcast].key
            )
          },
        },
        [iconCode.playVideo]: {
          key: `${iconCode.playVideo}-${that.propData.id}`,
          handle: () => {
            // 处理视频播放
            const { latitude, longitude, deviceCode, channels } =
              that.detailInfo
            let channelCodes
            if (this.selected[this.propData.markerType]) {
              channelCodes = [this.selected[this.propData.markerType]]
            } else if (channels && channels.length > 0) {
              channelCodes = channels.map(i => i.channelCode)
            } else {
              channelCodes = ['']
            }
            const list = channelCodes.map(channelCode => {
              return {
                deviceCode: deviceCode, //设备编号
                channelCode, //通道编号
                longitude: longitude, //经度
                latitude: latitude, //纬度
                showClose: true, //是否显示视频关闭按钮
              }
            })
            that[iconCode.playVideo] = {
              bizParam: list,
            }
            console.log('that[iconCode.playVideo]', list)
            $playerFit.appendLeftSmall(list, 'append')
          },
          cancel: () => {
            // 关闭视频播放
            that[iconCode.playVideo] &&
              $playerFit.close(that[iconCode.playVideo].bizParam)
            that[iconCode.playVideo] = null
          },
        },
        [iconCode.viewArea]: {
          handle: async () => {
            // 处理视域分析  channels
            const { latitude, longitude, deviceCode } = this.detailInfo
            // 测试数据
            // const deviceCode = '36011200031327000001'
            // const latitude = '40.225892812'
            // const longitude = '116.249465430'

            // 请求接口获取可视域数据
            if (!deviceCode) return
            const res = await api.post(
              `${this.$env.VUE_APP_REQ_PREFIX_PLAT}/device/getDeviceVisibleBySite`,
              {
                deviceCodes: [deviceCode],
                // screenId: '1001684',
                // screenType: '1',
              }
            )
            console.log('处理视域分析', this.detailInfo, res)
            const resData = res?.data || []
            if (resData?.length > 0) {
              const views = eachFunc(resData, this.defaultViewShed)
              // console.table(views)
              views.push({
                visualRange:
                  Number(
                    resData[0].visualRange && resData[0].visualRange !== '0'
                      ? resData[0].visualRange
                      : '0.5'
                  ) * 1000,
                // 可视域方向
                horizViewRange: 0,
                // 可视域角度
                horizRange: 360,
              })
              const _point = transformPointToMercator([
                Number(longitude),
                Number(latitude),
              ])
              CommonMap.setViewshedList(deviceCode, {
                map: this.getMapRef.mapInstance,
                point: {
                  lng: _point[0],
                  lat: _point[1],
                },
                views,
                style: {
                  viewDistanceColor: 'rgba(109,212,0,0.01)',
                  viewAngleColor: 'rgba(109,212,0,0.3)',
                },
              })
            }
          },
          cancel: () => {
            // 清除视域分析
            this.clearViewArea()
          },
        },
        [iconCode.historyLocus]: {
          key: `${iconCode.historyLocus}-${that.propData.id}`,
          handle: async () => {
            console.info(
              '------------打开/关闭 历史轨迹查询窗口-------',
              that.propData
            )
            const deviceCode = this.propData?.ds?.id
            if (!deviceCode) {
              that.$message.error('畜牧设备设备code不能为空')
              return
            }
            // 添加实时广播控制组件到底部包装器
            bottomWrap.addContent({
              component: AnimalLocusCtl, //BroadCastCtl,
              props: {
                devices: deviceCode,
                deviceCode: deviceCode,
              },
              key: this.iconHandle[iconCode.historyLocus].key,
              onclose: () => {
                // 关闭底部包装器中的实时广播控制组件
                bottomWrap.closeContent(
                  this.iconHandle[iconCode.historyLocus].key
                )
                that.iconActiveStat = {
                  ...that.iconActiveStat,
                  [iconCode.historyLocus]: false,
                }
              },
            })
          },
          cancel: () => {
            // 直接关闭底部包装器中的实时广播控制组件
            bottomWrap.closeContent(this.iconHandle[iconCode.historyLocus].key)
          },
        },
        [iconCode.passRoute]: {
          key: `${iconCode.passRoute}-${that.propData.deviceCode}`,
          handle: async () => {
            this.$EventBus.$emit(
              Events.PassRouteEvt.CHANGE_PASS_ROUTE_OPEN_STATUS,
              { open: true, deviceInfo: this.detailInfo }
            )
          },
          cancel: () => {
            this.$EventBus.$emit(
              Events.PassRouteEvt.CHANGE_PASS_ROUTE_OPEN_STATUS,
              { open: false }
            )
          },
        },
      }
    },
    /**
     * 点击图标触发的事件处理函数
     * 根据图标状态执行不同的操作：激活或取消激活
     * @param {string} code 图标代码
     */
    async iconClick(code) {
      // 切换图标状态
      this.iconActiveStat[code] = !this.iconActiveStat[code]
      if (this.iconActiveStat[code]) {
        // 如果图标被激活，调用对应的处理函数
        await this.iconHandle[code].handle()
      } else {
        // 如果图标被取消激活，调用对应的取消函数
        this.iconHandle[code].cancel()
      }
      // 更新图标状态对象
      this.iconActiveStat = {
        ...this.iconActiveStat,
      }
    },
    /**
     * 关闭实时视频的回调函数
     * 主要用于处理大屏幕播放器关闭事件
     * @param {Object} e 事件对象
     */
    closeRealTimeVideo(e) {
      // 检查回调方法是否为'bigScreenPlayerClose'
      if (e.data && e.data.callBackMethod === 'bigScreenPlayerClose') {
        // 检查视频播放数量，若为0，则重置选中状态和图标状态
        if (e.data.videoData.playVideoNum === 0) {
          this.selected[this.propData.markerType] = null
          this.iconActiveStat[iconCode.playVideo] = false
          this.iconActiveStat = {
            ...this.iconActiveStat,
          }
          this[iconCode.playVideo] = null
        }
      }
    },
    /**
     * 复制文本到剪贴板的函数
     * @param {string} value 需要复制的文本
     * @param {Event} event 事件对象
     */
    copy(value, event) {
      // 阻止事件冒泡
      event.stopPropagation()
      // 创建临时input元素用于复制文本
      let tempInput = document.createElement('input')
      document.body.appendChild(tempInput)
      tempInput.value = value
      tempInput.select()
      // 执行复制命令
      document.execCommand('copy')
      // 移除临时input元素
      document.body.removeChild(tempInput)
      // 显示复制成功的提示信息
      this.$message({
        type: 'success',
        message: '文本已复制到剪切板',
      })
    },
    /**
     * 异步获取设备详细信息
     * 根据设备代码(deviceCode)、设施代码(facilityCode)或ID获取设备详细信息
     * @returns {Promise} 返回一个Promise对象，包含设备详细信息
     */
    async getDetailInfo() {
      const { deviceCode, facilityCode, id } = this.propData.ds
      console.info(
        'getDetailInfo------this.contentCfg------',
        this.contentCfg,
        this.propData
      )
      this.ctlCfg = this.contentCfg[this.propData.markerType].ctrl
      const [, detailInfo] = await this.contentCfg[
        this.propData.markerType
      ].getDetail(deviceCode || facilityCode || id, this.propData.resUrl)
      let data = detailInfo
      // 测试数据
      if (!data) {
        // 隧道数据
        if (this.propData.markerType === 'R:202406041411449843')
          data = tunnelDetailData.data
        else if (this.propData.markerType === 'CAMERA')
          data = cameraDetailData.data
        else {
          // todo
        }
      }
      if (data) {
        this.detailInfo = data
      }
    },
    /**
     * 获取广播设备列表
     * 根据markerType决定返回的设备列表
     * @returns {Array} 返回一个设备列表
     */
    getBroadCaseDevice() {
      const { hornDevices } = this.detailInfo
      const type = this.propData.markerType
      if (type === LayerConst.CAMERA) {
        if (hornDevices && hornDevices.length > 0) {
          return hornDevices
        }
        return []
      }
      return [{ ...this.detailInfo }]
    },
    /**
     * 处理分组点击事件
     * 根据propData.markerType调用不同处理函数
     * @param {Object} data 点击事件数据
     */
    handleGroupClick(data) {
      const handle = this.groupClickHandle()
      handle[this.propData.markerType].handle(data)
    },
    /**
     * 处理分组点击事件中的选项点击
     * 更新选中状态
     * @param {Object} data 点击事件数据
     * @param {string} key 选项键值
     */
    groupOnclick(data, key) {
      this.selected[this.propData.markerType] = key
      this.selected = {
        ...this.selected,
      }
    },
    /**
     * 清除视域分析
     * 主要用于清除地图上的视域分析效果
     */
    clearViewArea() {
      const { deviceCode } = this.propData.ds
      CommonMap.removeViewshed(deviceCode)
      if (layerCache.circleLayer) {
        CommonMap.removeLayer(
          this.getMapRef.mapInstance,
          layerCache.circleLayer
        )
      }
    },
  },
}
</script>
<style lang="less" src="./index.less" scoped />
