<template>
  <div class='detail-info-wrap iframe'>
    <!-- 当showMainTitle为true时显示标题栏 -->
    <div class='detail-info-header' v-if='showMainTitle'>
      <MainTitle>
        <!-- 标题栏图标插槽 -->
        <template v-slot:icon>
          <icon-title-detail />
        </template>
        <!-- 标题栏文本插槽 -->
        <template v-slot:text>
          {{ `${propData.layerName || ''}详情` }}
        </template>
        <!-- 标题栏关闭按钮插槽 -->
        <template v-slot:close>
          <i class='el-icon-error' @click='close' />
        </template>
      </MainTitle>
    </div>
    <!-- 内容区域，包含iframe和底部内容区 -->
    <template>
      <div class='detail-info-content' v-loading='loading'>
        <!-- 加载iframe，根据propData设定宽度和高度 -->
        <iframe
          ref='contentIf'
          style='border:none;'
          :src='iframeUrl'
          :width='`${propData.width}px`'
          :height='`${propData.height}px`'
        />
      </div>
      <div class='detail-bottom-content'>
      </div>
    </template>
  </div>
</template>
<script>
import { getUserInfo } from '@/api/service/common';
import { getUrlDomain } from '@/components/common/utils';
import MainTitle from '@/components/page/ScreenPages/IntegratedMonitor/DetailInfo/MainTitle';
import SubTitle from '@/components/page/ScreenPages/IntegratedMonitor/DetailInfo/SubTitle.vue';
import IconTitleDetail from '@/components/page/ScreenPages/IntegratedMonitor/Icons/icon-title-detail.vue';

/* 设置允许跨域访问的域名 */
const targetDomain = '*';
/* 用于缓存层信息的对象 */
const layerCache = {};

/**
 * 细节信息组件。
 * 用于显示带有标题和内容区域的详细信息，其中内容区域通过iframe加载。
 * @prop {Object} propData - 组件的属性数据，包含各种配置和iframe的源URL。
 * @prop {Function} close - 关闭组件的回调函数。
 * @prop {Boolean} showMainTitle - 是否显示标题栏。
 * @component SubTitle - 子标题组件。
 * @component MainTitle - 主标题组件。
 * @component IconTitleDetail - 标题图标组件。
 */
export default {
  computed: {},
  props: {
    propData: {
      type: Object,
      default: {}
    },
    close: {
      type: Function,
      default: () => {
        console.log('close');
      }
    },
    showMainTitle: {
      type: Boolean,
      default: true
    }
  },
  components: {
    SubTitle,
    MainTitle,
    IconTitleDetail
  },
  data() {
    return {
      /* 内容配置，从propData中获取 */
      contentCfg: this.propData.contentCfg,
      ctlCfg: [],
      detailInfo: {},
      iconActiveStat: {},
      iconHandle: {},
      viewAreaLayer: '',
      selected: {},
      /* 加载状态 */
      loading: true,
      /* iframe的URL */
      iframeUrl: ''
    };
  },
  mounted() {
    this.init();
    this.initIframe();
  },
  beforeDestroy() {
    this.close({ destroy: true });
  },
  methods: {
    /**
     * 初始化组件，包括设置iframe的URL和监听窗口消息。
     * 主要用于处理跨域通信。
     */
    init() {
      const $this = this;
      /* 监听窗口消息，用于接收iframe发送的数据 */
      window.addEventListener('message', async function(event) {
        // 检查消息来源是否安全
        if (event.origin !== document.referrer) {
          return;
        }
        if (event.data && event.data.type === 'HJ-IFRAME') {
          /* 获取用户信息并将其添加到iframe发送的数据中 */
          const data = await getUserInfo();
          const res = {
            ...event.data,
            data: data
          };
          const { resUrl } = this.propData;
          const domain = getUrlDomain(resUrl)
          $this.$refs.contentIf.contentWindow.postMessage(res, domain);
        }
      });
      this.loading = false;
    },
    /**
     * 初始化iframe的URL。
     * 根据propData中的设备码、设施码或ID构造iframe的src。
     */
    initIframe() {
      const {
        deviceCode,
        facilityCode,
        id
      } = this.propData.ds;
      const { resUrl } = this.propData;
      this.iframeUrl = `${resUrl}?resourceId=${deviceCode || facilityCode || id}`;
    }
  }
};
</script>

<style lang='less' src='../index.less' />
