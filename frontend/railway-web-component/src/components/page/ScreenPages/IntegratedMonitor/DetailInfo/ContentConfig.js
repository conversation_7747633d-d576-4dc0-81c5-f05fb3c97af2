/**
 * 导入设备、设施和资源的详细信息接口。
 */
import { postProxy } from '@/api/service/common';
import dayjs from 'dayjs';
import {
  getDeviceDetail,
  getFacilityDetail,
  getAnimalDetail,
} from '@/api/service/imScreenService';
import { $getUrlParam } from '@/utils/common.js';

/**
 * 导入空值检查工具。
 */
import { isEmptyOrUndefined } from '@/components/common/utils';

/**
 * 导入设备状态枚举。
 */
import { enums } from '@/components/page/ScreenPages/IntegratedMonitor/DetailInfo/enums';

/**
 * 导入配置枚举。
 */
import { CfgEnum } from '@/components/page/ScreenPages/IntegratedMonitor/enum/CfgEnum';

/**
 * 导入事件枚举。
 */
import { Events } from '@/components/page/ScreenPages/IntegratedMonitor/enum/EventsEnum';

/**
 * 导入图层常量。
 */
import LayerConst from '@/components/page/ScreenPages/IntegratedMonitor/Map/LayerConst';

/**
 * 导入Vue实例。
 */
import vue from '@/main';

/**
 * 设备图标代码对象。
 */
export const iconCode = {
  broadcast: 'broadcast',
  reltimeBroadcast: 'reltimeBroadcast',
  playVideo: 'playVideo',
  viewArea: 'viewArea',
  copy: 'copy',
  historyLocus: 'historyLocus',
  passRoute: 'passRoute',
};

/**
 * 获取设备内容配置。
 * @returns {Object} 设备内容配置对象。
 */
export const contentCfg = () => ({
  CAMERA: {
    groups: [
      {
        groupName: '基础信息',
        content: [
          {
            key: 'deviceStatus',
            render: (code) => {
              // 如果设备状态码为空或未定义，则返回'-'
              if (isEmptyOrUndefined(code)) {
                return '-';
              }
              // 根据设备状态码返回对应的状态名称
              return enums.CAMERA.deviceStatus[code].name;
            },
          },
        ],
      },
      {
        groupName: '通道信息',
        onclick: (data, calback) => {
          // 触发回调函数并传递数据和通道代码
          calback && calback(data, data.channelCode);
          // 通过事件总线触发设备详情组点击事件
          vue.$EventBus.$emit(Events.DEVICE_DETAIL.GROUP_CLICK, data);
        },
      },
    ],
    getDetail: async (deviceCode) => {
      // 获取当前视图ID
      const viewId = $getUrlParam('viewId');
      // 异步获取设备详细信息
      return await getDeviceDetail({
        deviceCode: deviceCode,
        layerCode: LayerConst.CAMERA,
        modelCode: viewId,
      });
    },
  },
  LOUDSPEAKER: {
    groups: [
      {
        groupName: '基础信息',
        content: [
          {
            key: 'deviceStatus',
            render: (code) => {
              // 如果设备状态码为空或未定义，则返回'-'
              if (isEmptyOrUndefined(code)) {
                return '-';
              }
              // 根据设备状态码返回对应的状态名称
              return enums.LOUDSPEAKER.deviceStatusGroup[code].name;
            },
          },
        ],
      },
    ],
    getDetail: async (deviceCode) => {
      // 获取当前视图ID
      const viewId = $getUrlParam('viewId');
      // 异步获取设备详细信息
      return await getDeviceDetail({
        deviceCode: deviceCode,
        layerCode: LayerConst.LOUDSPEAKER,
        modelCode: viewId,
      });
    },
  },
  BRIDGE: {
    getDetail: async (deviceCode) => {
      // 异步获取设施详细信息
      return await getFacilityDetail({
        facilityCode: deviceCode,
        layerCode: LayerConst.BRIDGE,
        modelCode: CfgEnum.MODEL_CODE.modelCode,
      });
    },
  },
  TUNNEL: {
    getDetail: async (deviceCode) => {
      // 异步获取设施详细信息
      return await getFacilityDetail({
        facilityCode: deviceCode,
        layerCode: LayerConst.TUNNEL,
        modelCode: CfgEnum.MODEL_CODE.modelCode,
      });
    },
  },
  ANIMAL: {
    getDetail: async (deviceCode) => {
      // 异步获取动物详细信息，时间范围为过去一天
      return await getAnimalDetail({
        deviceCode: deviceCode,
        startTime: dayjs().subtract(1, 'days').format('YYYY-MM-DD HH:mm:ss'),
        endTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      });
    },
  },
  COMM_LAYER: {
    getDetail: async (deviceCode, resUrl = '') => {
      // 如果资源URL为空，则使用默认URL
      let uri = resUrl;
      if (isEmptyOrUndefined(resUrl)) {
        uri = `${vue.$env.VUE_APP_REQ_PREFIX_BIZ}/resource/detail`;
      }
      // 异步获取资源详细信息
      const [success, data] = await postProxy({
        url: uri,
        params: {
          resourceId: deviceCode,
        },
      });
      if (success && data) {
        // 初始化视频和图片URL对象
        const url = {
          video: {
            videoList: [],
          },
          imgs: [],
        };
        const { videoUrl, imgUrl } = data;
        // 处理视频URL
        if (videoUrl) {
          videoUrl.split(',').forEach((item) => {
            url.video.videoList.push({
              src: item,
            });
          });
        }
        // 处理图片URL
        if (imgUrl) {
          imgUrl.split(',').forEach((item) => {
            url.imgs.push({
              fileUrl: item,
            });
          });
        }
        // 返回成功状态和数据
        return [
          success,
          {
            ...data,
            url,
          },
        ];
      }
      // 返回成功状态和数据
      return [success, data];
    },
  },
});

/**
 * 合并数据库配置（dbCfg）和内容配置（contentCfg）到一个静态和动态配置的统一结果中。
 * @returns {Object} 包含静态和动态配置信息的合并后的对象。
 */
export const mergeCfg = (dbCfg) => {
  // 初始化结果对象
  const result = {};
  // 将数据库配置复制到结果对象中
  Object.assign(result, dbCfg);
  // 获取结果对象中的所有键
  const keys = Object.keys(result);
  // 获取内容配置
  const staticCfg = contentCfg();
  // 遍历结果对象的每个层
  keys.forEach((layer) => {
    // 尝试获取该层的静态配置
    let cfg = staticCfg[layer];
    // 如果该层没有静态配置，则使用通用层配置
    if (!staticCfg[layer]) {
      cfg = {
        ...staticCfg.COMM_LAYER,
      };
    }
    // 如果该层有静态配置的分组
    const staticCfgGroups = cfg.groups;
    if (!staticCfgGroups) {
      // 直接将静态配置合并到结果对象的该层
      Object.assign(result[layer], cfg);
      return;
    }
    // 获取该层在结果对象中的动态配置分组
    const dynamicCfgGroups = result[layer].groups;
    // 如果该层没有动态配置分组，则跳过
    if (!dynamicCfgGroups) {
      return;
    }
    // 遍历每个动态配置分组
    dynamicCfgGroups.map((group, index) => {
      // 尝试在静态配置中找到对应的分组
      const staticGroup = staticCfgGroups.find(
        (item) => item.groupName === group.groupName
      );
      // 如果找不到对应的静态配置分组，则跳过
      if (!staticGroup) {
        return;
      }
      // 获取静态配置分组的内容
      const staticContent = staticGroup.content;
      // 获取动态配置分组的内容
      const dynamicContent = group.content;
      // 如果动态配置分组没有内容，则跳过
      if (!dynamicContent) {
        return;
      }
      // 遍历动态配置分组的每个内容项
      dynamicContent.map((groupContent, contentIndex) => {
        // 尝试在静态配置内容中找到对应的内容项
        if (!staticContent) {
          return;
        }
        const staticContentItem = staticContent.find(
          (item) => item.key === groupContent.key
        );
        // 如果找不到对应的内容项，则跳过
        if (!staticContentItem) {
          return;
        }
        // 将静态配置内容项合并到动态配置内容项中
        Object.assign(dynamicContent[contentIndex], staticContentItem);
      });
      // 删除静态配置分组中的内容，以避免重复
      delete staticGroup.content;
      // 将静态配置分组合并到动态配置分组中
      Object.assign(dynamicCfgGroups[index], staticGroup);
    });
    // 删除静态配置层中的分组，以避免重复
    delete staticCfg[layer].groups;
    // 将静态配置层剩余的部分合并到结果对象的该层中
    Object.assign(result[layer], staticCfg[layer]);
  });
  // 返回合并后的配置结果
  return result;
};

/**
 * 根据路径获取对象值。
 * @param {Object} obj 对象。
 * @param {string} path 路径字符串。
 * @returns {*} 对象路径对应的值。
 */
export const getValueByPath = (obj, path) => {
  // 将路径字符串分割成键数组
  const keys = path.split('.');
  // 初始化结果为输入对象
  let result = obj;
  // 遍历键数组
  for (let i = 0; i < keys.length; i++) {
    // 如果结果为null或undefined，则返回结果
    if (result === null || result === undefined) {
      return result;
    }
    // 更新结果为当前键对应的值
    result = result[keys[i]];
  }
  // 返回最终结果
  return result;
};
