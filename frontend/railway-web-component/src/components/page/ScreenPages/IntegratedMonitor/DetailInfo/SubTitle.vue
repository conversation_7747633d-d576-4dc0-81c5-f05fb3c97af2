<!--
 * @Description: 提供一个可定制的子标题组件，支持通过插槽添加文本和关闭按钮。
 * @Autor: he.chao
 * @Date: 2022-10-19 15:07:55
 * @LastEditors: liu.yongli
 * @LastEditTime: 2023-12-13 14:36:14
-->
<template>
  <!-- 子标题组件容器，包含图标、文本和关闭按钮插槽 -->
  <span class='sub-title'>
    <!-- 子标题图标占位符，可通过CSS定制 -->
    <span class='sub-title-icon'>
    </span>
    <!-- 文本内容插槽，用于插入子标题的文本 -->
    <slot name='text'></slot>
    <!-- 关闭按钮插槽，用于插入关闭功能的按钮 -->
    <slot name='close'></slot>
  </span>
</template>

<script>
export default {
  // 组件数据定义
  data: function() {
    return {};
  },
  // 组件方法定义
  methods: {}
};
</script>

<!-- 组件样式，使用less语言，并从相对路径的index.less文件引入 -->
<style scoped lang='less' src='./index.less' />
