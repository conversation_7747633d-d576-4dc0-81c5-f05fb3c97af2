<?xml version="1.0" encoding="UTF-8"?>
<svg width="342px" height="20px" viewBox="0 0 342 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>bg_标题_342_n</title>
    <defs>
        <linearGradient x1="87.5410483%" y1="50.0468908%" x2="1.31711231%" y2="50%" id="linearGradient-1">
            <stop stop-color="#4F9FFF" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#4F9FFF" stop-opacity="0.24728799" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="87.5410483%" y1="50.008513%" x2="1.31711231%" y2="50%" id="linearGradient-2">
            <stop stop-color="#4F9FFF" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#4F9FFF" stop-opacity="0.5" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="87.5410483%" y1="50.0004689%" x2="1.31711231%" y2="50%" id="linearGradient-3">
            <stop stop-color="#4F9FFF" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#4F9FFF" stop-opacity="0.5" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="综合监测-大屏" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="1-1综合监测-宏观" transform="translate(-1554.000000, -118.000000)">
            <g id="重点关注事件" transform="translate(1548.000000, 92.000000)">
                <g id="bg_标题_342_n" transform="translate(6.000000, 12.000000)">
                    <g transform="translate(0.000000, 14.000000)" id="矩形">
                        <rect fill="url(#linearGradient-1)" x="6" y="0" width="336" height="20"></rect>
                        <rect fill="#4F9FFF" x="3" y="4" width="4" height="4"></rect>
                        <rect fill="#4F9FFF" x="0" y="11" width="2" height="2"></rect>
                        <rect fill="#4F9FFF" x="6" y="13" width="3" height="3"></rect>
                        <rect fill="#4F9FFF" x="8" y="8" width="3" height="3"></rect>
                        <rect fill="#0C1E33" x="13" y="5" width="3" height="3"></rect>
                        <rect fill="#4F9FFF" x="5" y="10" width="2" height="2"></rect>
                        <polygon fill="url(#linearGradient-2)" points="34 5 136.07803 5 141.200326 9 310 12 34 12"></polygon>
                        <rect fill="url(#linearGradient-3)" x="6" y="18" width="336" height="2"></rect>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>