<!--
 * @Description:
 * @Autor: he.chao
 * @Date: 2022-10-19 15:07:55
 * @LastEditors: liu.yongli
 * @LastEditTime: 2024-05-10 16:54:55
-->
<template>
  <!-- 外层容器，包含站点信息和指针 -->
  <div class='station-info-wrap'>
    <!-- 信息边框容器 -->
    <div class='info-border'>
      <!-- 站点信息显示区域 -->
      <div class='station-info'>
        <!-- 显示传入的站点名称 -->
        {{ propData.name }}
        <!-- 图标组件，用于显示信息点 -->
        <icon-info-win-point />
      </div>
    </div>
    <!-- 信息指针 -->
    <div class='info-pointer'></div>
  </div>
</template>
<script>
import IconInfoWinPoint from '@/components/page/ScreenPages/IntegratedMonitor/Icons/icon-info-win-point.vue';

/**
 * 该组件是基于Vue的默认导出配置，旨在提供一个特定功能的UI组件。
 * 它使用了IconInfoWinPoint作为子组件，以展示特定的图标信息。
 *
 * @component
 * @example
 * <MyComponent :propData="data"/>
 *
 * 其中，propData是一个对象属性，用于传递给组件以供内部使用。
 */
export default {
  // 注册子组件IconInfoWinPoint
  components: { IconInfoWinPoint },
  // 定义组件接收的外部属性
  props: {
    propData: {
      type: Object, // propData属性类型为对象
      default: {}   // 默认值为空对象
    }
  },
  // 初始化组件的内部数据
  data: function() {
    return {}; // 返回一个空对象作为组件的内部数据
  },
  // 定义组件的方法
  methods: {}
};
</script>
<style lang='less' scoped>
.station-info-wrap {
  position: relative; // 设置相对定位
  .info-border {
    height: 25px; // 设置高度
    background: url("./img/bg_info_border.png") 100% 100% no-repeat; // 背景图片设置
    background-size: 100% 100%; // 背景图片大小设置
    display: flex; // 使用flex布局
    align-items: center; // 垂直居中
    justify-content: center; // 水平居中
    position: absolute; // 绝对定位
    right: 25px; // 距离右侧25px
    top: -13px; // 距离顶部-13px
    padding: 0 5px; // 内边距设置
    .station-info {
      height: 18px; // 设置高度
      min-width: 80px; // 最小宽度
      font-family: PingFangSC-Medium, sans-serif; // 字体设置
      font-size: 12px; // 字体大小
      color: #E8F3FF; // 字体颜色
      letter-spacing: 0; // 字间距
      font-weight: 500; // 字体粗细
      background: url("./img/bg_station_info.png") 100% 100% no-repeat; // 背景图片设置
      background-size: 100% 100%; // 背景图片大小设置
      display: flex; // 使用flex布局
      align-items: center; // 垂直居中
      justify-content: flex-end; // 水平靠右
      .icon {
        margin: 0 5px; // 图标的左右边距
      }
    }
  }
  .info-pointer {
    width: 30px; // 设置宽度
    height: 30px; // 设置高度
    background: url("./img/site_pointer.png") 100% 100% no-repeat; // 背景图片设置
    background-size: 100% 100%; // 背景图片大小设置
  }
  &.right {
    .info-border {
      right: -83px; // 右侧定位调整
      top: -13px; // 顶部定位调整
      .station-info {
        flex-direction: row-reverse; // 反转布局方向
      }
    }
    .info-pointer {
      transform: scaleX(-1); // 水平翻转指针
    }
  }
}
</style>
