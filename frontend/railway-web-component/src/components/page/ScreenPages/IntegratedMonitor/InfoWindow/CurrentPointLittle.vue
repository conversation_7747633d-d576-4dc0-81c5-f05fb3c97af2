<!--
 * @Description: 这是一个Vue组件的模板文件，包含HTML模板、JavaScript逻辑和样式。
 * @Autor: he.chao
 * @Date: 2022-10-19 15:07:55
 * @LastEditors: liu.yongli
 * @LastEditTime: 2024-05-10 15:49:11
-->
<template>
  <!-- 定义一个名为'pass-current-point'的div容器 -->
  <div class='pass-current-point'>
    <!-- 在容器内嵌入一个图片，图片路径为'./img/lan-dingwei.gif' -->
    <img alt="定位" src='./img/lan-dingwei.gif' />
  </div>
</template>
<script>
/**
 * 此处为组件的JavaScript部分，它定义了组件的行为和逻辑。
 *
 * @export 默认导出这个对象，使得它可以被其他地方引入并使用。
 * @returns {Object} 返回一个包含组件数据和方法的对象。
 */
export default {
  /**
   * 定义组件的数据属性。
   *
   * 这里使用了一个空对象，表示组件没有初始数据。在实际开发中，这里会根据需要定义组件的状态数据。
   *
   * @returns {Object} 返回一个空对象，表示组件的初始数据。
   */
  data: function() {
    return {};
  },
  /**
   * 定义组件的方法。
   *
   * 这里使用了一个空对象，表示组件没有额外的方法。在实际开发中，这里会根据需要定义处理各种交互事件的方法。
   *
   * @returns {Object} 返回一个空对象，表示组件的方法集合。
   */
  methods: {}
};
</script>
<style lang='less' scoped>
/* 定义'pass-current-point'类的样式 */
.pass-current-point {
  /* 设置容器的宽度和高度为100px */
  width: 100px;
  height: 100px;
  /* 设置背景图片为'./img/bg_point.png'，并且不重复，背景大小为100% */
  background: url("./img/bg_point.png") 100% 100% no-repeat;
  background-size: 100% 100%;
  /* 设置容器的定位为相对定位 */
  position: relative;
  /* 定义img标签的样式 */
  img {
    /* 设置图片的定位为绝对定位 */
    position: absolute;
    /* 设置图片距离顶部22px，距离左边0px */
    top: 22px;
    left: 0px;
    /* 设置图片的宽度为100px，高度为50px */
    width: 100px;
    height: 50px;
  }
}
</style>
