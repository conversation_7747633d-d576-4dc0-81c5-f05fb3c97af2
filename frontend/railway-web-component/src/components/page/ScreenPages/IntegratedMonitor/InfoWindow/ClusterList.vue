<template>
  <!-- 集群列表包装组件，用于显示集群的列表信息 -->
  <div class="cluster-list-wrap">
    <!-- 使用CommonTable组件显示集群数据，具备选中行的功能 -->
    <CommonTable
      ref="commonTable"
      :height="propData.length < 6 ? (propData.length + 1) * 33 : 200"
      :highlight-current-row="true"
      :handle-current-change="onSelected"
      :table-data="propData"
    >
      <!-- 定义表格列，包括类型、名称和状态 -->
      <template v-slot:column>
        <el-table-column prop="type" label="事件类型" width="200"></el-table-column>
        <el-table-column prop="name" label="事件名称" width="200"></el-table-column>
        <el-table-column prop="statusName" label="事件状态" width="100">
          <!-- 自定义状态列的显示，通过颜色区分状态 -->
          <template slot-scope="scope">
            <span :style="`color: ${scope.row.color}`">{{ scope.row.statusName }}</span>
          </template>
        </el-table-column>
      </template>
    </CommonTable>
  </div>
</template>

<script>
import CommonTable from '../CommonTable'

/**
 * 集群列表组件，用于展示集群信息列表。
 *
 * @component CommonTable
 * @prop {Array} propData - 集群数据数组，每个元素包含集群的详细信息。
 * @prop {Function} onSelected - 当选中行时触发的回调函数。
 */
export default {
  components: {
    CommonTable,
  },
  props: {
    propData: {
      type: Array,
      default: [],
    },
    onSelected: {
      type: Function,
      default: () => {
        console.log('onSelected')
      },
    },
  },
  data() {
    return {}
  },
  methods: {
    /**
     * 清除选中状态。
     *
     * 用于在需要的时候清除表格中的选中行状态。
     */
    clear() {
      this.$refs.commonTable.clearSelection()
    },
  },
}
</script>

<style lang='less' src='./index.less' />
