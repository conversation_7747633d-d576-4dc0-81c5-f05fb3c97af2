<!--
 * @Description:
 * @Autor: he.chao
 * @Date: 2022-10-19 15:07:55
 * @LastEditors: liu.yongli
 * @LastEditTime: 2023-12-13 14:36:14
-->
<template>
  <!-- 当前点提示的容器 -->
  <div class='current-point-tip'>
    <!-- 提示的角标 -->
    <i class='tip-angle' />
    <!-- 提示内容的容器 -->
    <div class='tip-content'>
      <!-- 显示设备名称 -->
      <div class='tip-info'>
        名称：{{ propData.deviceName }}
      </div>
      <!-- 显示设备编号 -->
      <div class='tip-info'>
        编号：{{ propData.deviceCode }}
      </div>
    </div>
  </div>
</template>
<script>

/**
 * 组件说明：这里写组件的通用介绍
 *
 * @prop {Object} propData - 传入的属性数据对象，默认为空对象
 */
export default {
  props: {
    // 定义传入的属性数据对象，类型为Object，默认值为空对象
    propData: {
      type: Object,
      default: {}
    }
  },
  data() {
    // 组件数据初始化
    return {};
  },
  methods: {
    // 组件方法
  }
};
</script>
<!-- 引入外部样式文件 -->
<style lang='less' src='./index.less' />
