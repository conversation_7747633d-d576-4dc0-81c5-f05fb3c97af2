<template>
  <!-- 监控设备列表区域 -->
  <div class="cameras-wrap">
    <!-- 使用CommonTable组件展示监控设备列表 -->
    <CommonTable
      ref="commonTable"
      :height="propData.length < 6 ? (propData.length + 1) * 33 : 200" 
      :highlight-current-row="true"
      :handle-current-change="onSelected"
      :table-data="propData" 
    >
      <!-- 定义表格列模板 -->
      <template v-slot:column>
        <!-- 设备名称列 -->
        <el-table-column prop="deviceName" label="设备名称" width="180">
        </el-table-column>
        <!-- 设备状态列 -->
        <el-table-column prop="deviceStatus" label="设备状态" width="80">
          <template slot-scope="scope">
            <!-- 根据设备状态显示不同颜色 -->
            <span
              :style="`color: ${
                CAMERA.deviceStatus[scope.row.deviceStatus] &&
                CAMERA.deviceStatus[scope.row.deviceStatus].color
              }`"
            >
              {{
                CAMERA.deviceStatus[scope.row.deviceStatus] &&
                CAMERA.deviceStatus[scope.row.deviceStatus].name
              }}
            </span>
          </template>
        </el-table-column>
      </template>
    </CommonTable>
  </div>
</template>
<script>
import { enums } from '@/components/page/ScreenPages/IntegratedMonitor/DetailInfo/enums'; // 导入枚举
import CommonTable from '../CommonTable'; // 导入CommonTable组件
const { CAMERA } = enums; // 解构出CAMERA枚举
/**
 * 监控设备列表组件
 * @description 展示监控设备的名称和状态
 * @prop {Array} propData - 监控设备数据数组
 * @prop {Function} onSelected - 当选择设备时的回调函数
 */
export default {
  components: {
    CommonTable, // 注册CommonTable组件
  },
  props: {
    propData: {
      type: Array, // propData属性类型为数组
      default: [], // 默认值为空数组
    },
    onSelected: {
      type: Function, // onSelected属性类型为函数
      default: () => {
        console.log('onSelected'); // 默认函数输出日志
      },
    },
  },
  data() {
    return {
      CAMERA, // 将CAMERA枚举添加到组件数据中
    };
  },
  mounted() {
    // 组件挂载后的逻辑（此处为空）
  },
  methods: {
    /**
     * 清除选中的设备
     * @description 取消选中任何已选中的监控设备
     */
    clear() {
      this.$refs.commonTable.clearSelection(); // 调用CommonTable组件的clearSelection方法
    },
  },
};
</script>
<style lang='less' src='./index.less' /> <!-- 引入样式文件 -->
