<!--
 * @Description:
 * @Autor: he.chao
 * @Date: 2022-10-19 15:07:55
 * @LastEditors: liu.yongli
 * @LastEditTime: 2023-12-13 14:36:14
-->
<template>
    <div :class="`station-info-wrap right ${propData.className || ''}`">
      <div class="info-border-right">
        <div class="station-info">
          <icon-info-win-point />
          <span class="station-label">{{ propData.name }}</span>
        </div>
      </div>
      <div class="info-pointer"></div>
      <div class="right-railway-station"></div>
    </div>
  </template>

  <script>
  import IconInfoWinPoint from '@/components/page/ScreenPages/IntegratedMonitor/Icons/icon-info-win-point.vue';

  /**
   * 该组件是基于Vue的默认导出配置，旨在提供一个特定功能的UI组件。
   * 它使用了IconInfoWinPoint作为子组件，以展示特定的图标信息。
   *
   * @component
   * @example
   * <MyComponent :propData="data"/>
   *
   * 其中，propData是一个对象属性，用于传递给组件以供内部使用。
   */
  export default {
    // 注册子组件IconInfoWinPoint
    components: { IconInfoWinPoint },
    // 定义组件接收的外部属性
    props: {
      propData: {
        type: Object,
        default: {},
      },
    },
    // 初始化组件的内部数据
    data: function () {
      return {};
    },
    // 定义组件的方法
    methods: {},
  };
  </script>
  <style lang="less">
  @import './index.less';
  .station-info-wrap {
    &.right {
      .info-border-right {
        height: 50px;
        background: url('./img/bg_info_border.png') 100% 100% no-repeat;
        background-size: 100% 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        position: absolute;
        left: 50px;
        top: -25px;
        padding: 0 10px;
        .station-info {
          height: 35px;
          font-family: PingFangSC-Medium, sans-serif;
          font-size: 14px;
          color: #e8f3ff;
          letter-spacing: 0;
          font-weight: 500;
          background: url('./img/bg_station_info.png') 100% 100% no-repeat;
          background-size: 100% 100%;
          display: flex;
          align-items: center;
          justify-content: flex-end;

          .station-label {
            display: inline-block;
            white-space: nowrap;
            margin-right: 15px;
          }

          .icon {
            padding: 0;
            margin: 0;
          }
        }
      }

      .info-pointer {
        background: url('./img/site_pointer.png') 100% 100% no-repeat;
        background-size: 100%;
        transform: rotate(270deg);
      }
      .right-railway-station {
        width: 58px;
        height: 40px;
        background: url('../PassRoute/img/railway_station.gif') 100% 100%
          no-repeat;
        background-size: 100% 100%;
        position: absolute;
        left: -28px;
        bottom: -20px;
      }
    }
  }
  </style>
