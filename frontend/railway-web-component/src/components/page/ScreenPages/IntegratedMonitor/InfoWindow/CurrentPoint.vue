<!--
 * @Description:
 * @Autor: he.chao
 * @Date: 2022-10-19 15:07:55
 * @LastEditors: liu.yongli
 * @LastEditTime: 2023-12-13 14:36:14
-->
<template>
    <div :class='`pass-current-point ${propData.className || ""}`'>
      <img alt="定位" src='./img/lan-dingwei.gif' />
    </div>
  </template>

  <script>
  /**
   * 此处为组件的JavaScript部分，它定义了组件的行为和逻辑。
   *
   * @export 默认导出这个对象，使得它可以被其他地方引入并使用。
   * @returns {Object} 返回一个包含组件数据和方法的对象。
   */
  export default {
    props: {
      // 定义propData属性，类型为对象，如果没有提供则默认为空对象
      propData: {
        type: Object,
        default: {}
      }
    },
    /**
     * 定义组件的数据属性。
     *
     * 这里使用了一个空对象，表示组件没有初始数据。在实际开发中，这里会根据需要定义组件的状态数据。
     *
     * @returns {Object} 返回一个空对象，表示组件的初始数据。
     */
    data: function() {
      return {};
    },

    /**
     * 定义组件的方法。
     *
     * 这里使用了一个空对象，表示组件没有额外的方法。在实际开发中，这里会根据需要定义处理各种交互事件的方法。
     *
     * @returns {Object} 返回一个空对象，表示组件的方法集合。
     */
    methods: {}
  };
  </script>

  <style lang='less' src='./index.less' />
