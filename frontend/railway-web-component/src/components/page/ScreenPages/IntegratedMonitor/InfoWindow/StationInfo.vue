<!--
 * @Description:
 * @Autor: he.chao
 * @Date: 2022-10-19 15:07:55
 * @LastEditors: liu.yongli
 * @LastEditTime: 2023-12-13 14:36:14
-->
<template>
    <div :class='`station-info-wrap ${propData.className || ""}`'>
      <div class='info-border'>
        <div class='station-info'>
          <span class='station-label'> {{ propData.name }}</span>
          <icon-info-win-point />
        </div>
      </div>
      <div class='info-pointer'></div>
      <div class="left-railway-station"></div>
    </div>
  </template>

  <script>
  import IconInfoWinPoint from '@/components/page/ScreenPages/IntegratedMonitor/Icons/icon-info-win-point.vue';

  /**
   * 该组件是基于Vue的默认导出配置，旨在提供一个特定功能的UI组件。
   * 它使用了IconInfoWinPoint作为子组件，以展示特定的图标信息。
   *
   * @component
   * @example
   * <MyComponent :propData="data"/>
   *
   * 其中，propData是一个对象属性，用于传递给组件以供内部使用。
   */
  export default {
    // 注册子组件IconInfoWinPoint
    components: { IconInfoWinPoint },
    // 定义组件接收的外部属性
    props: {
      propData: {
        type: Object,
        default: {}
      }
    },
    // 初始化组件的内部数据
    data: function() {
      return {};
    },
    // 定义组件的方法
    methods: {}
  };
  </script>

  <style lang='less' src='./index.less' />
