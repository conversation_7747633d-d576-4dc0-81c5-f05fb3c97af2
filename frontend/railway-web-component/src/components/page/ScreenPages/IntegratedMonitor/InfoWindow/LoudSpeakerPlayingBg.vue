<template>
  <!-- 定义组件的模板部分 -->
  <div class='loud-speaker-playing-bg'>
  </div>
</template>
<script>
export default {
  // 定义组件接收的外部属性
  props: {},
  // 初始化组件的内部数据
  data: function() {
    // 返回组件内部的数据对象
    return {};
  },
  // 定义组件的方法
  methods: {}
};
</script>
<style lang='less'>
/* 定义组件的样式 */
.loud-speaker-playing-bg {
    width: 42px; /* 设置背景宽度 */
    height: 42px; /* 设置背景高度 */
    background: url("./img/loud_speaker_playing_wave_42.gif") 100% 100% no-repeat; /* 设置背景图片及其显示方式 */
    background-size: 100% 100%; /* 设置背景图片的大小 */
}
</style>
