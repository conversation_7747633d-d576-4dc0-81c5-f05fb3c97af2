@import "../common.less";

.pass-current-win {
  width: 548px;
  height: 141px;
  display: flex;
  justify-content: flex-start;
  pointer-events: none;

  .pass-info {
    width: 300px;
    height: 120px;
    background: url("./img/bg_pass_info_win.png") 100% 100% no-repeat;
    background-size: 100% 100%;
    position: absolute;
    right: 530px;
    top: -50px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-end;

    .info-wrap {
      width: 300px;
      height: 100px;
      display: flex;
      align-items: center;
      justify-content: flex-start;
    }

    .info-icon {
      width: 40px;
      height: 40px;
      margin: 0 12px;

      svg {
        width: 30px !important;
        height: 30px !important;
      }
    }

    .info-container {
      width: calc(100% - 40px - 12px - 12px);
      display: flex;
      align-items: flex-start;
      flex-direction: column;
      //   pointer-events: auto;
      .info-route-seg {
        font-family: PingFangSC-Medium;
        font-size: 24px;
        width: 100%;
        color: #e8f3ff;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        font-weight: 500;
        white-space: nowrap; //强制文本在一行内输出
        overflow: hidden; //隐藏溢出部分
        text-overflow: ellipsis; //对溢出部分加上...
      }

      .info-route-seg-null {
        font-family: PingFangSC-Medium;
        width: 100%;
        font-size: 24px;
        font-weight: 900;
        letter-spacing: 1px;
        background: linear-gradient(
          to right,
          #42d5f7 0%,
          #44eefe 50%,
          #42d5f7 100%
        );
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      .info-route-location {
        font-family: PingFangSC-Medium;
        font-size: 14px;
        color: #e8f3ff;
        letter-spacing: 0;
        font-weight: 500;
        display: -webkit-box;
        -webkit-line-clamp: 2; /* 控制显示的行数 */
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }

  .pass-dir {
    position: absolute;
    height: 141px;
    width: 326px;
    background: url("./img/line_pass_side.png") 100% 100% no-repeat;
    background-size: 100% 100%;
  }
}

.fadeInStyle {
  animation: fadeIn 3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.fadeOutStyle {
  animation: fadeOut 3s ease-in-out;
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

.station-info-wrap {
  position: relative;

  .info-border {
    height: 50px;
    background: url("./img/bg_info_border.png") 100% 100% no-repeat;
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    right: 50px;
    top: -25px;
    padding: 0 10px;

    .station-info {
      height: 35px;
      font-family: PingFangSC-Medium, sans-serif;
      font-size: 14px;
      color: #e8f3ff;
      letter-spacing: 0;
      font-weight: 500;
      background: url("./img/bg_station_info.png") 100% 100% no-repeat;
      background-size: 100% 100%;
      display: flex;
      align-items: center;
      justify-content: flex-end;

      .station-label {
        display: inline-block;
        white-space: nowrap;
        margin-left: 15px;
      }

      .icon {
        padding: 0;
        margin: 0;
      }
    }
  }

  .info-pointer {
    width: 60px;
    height: 60px;
    background: url("./img/site_pointer.png") 100% 100% no-repeat;
    background-size: 100% 100%;
  }

  .left-railway-station {
    width: 58px;
    height: 40px;
    background: url("../PassRoute/img/railway_station.gif") 100% 100% no-repeat;
    background-size: 100% 100%;
    position: absolute;
    right: -28px;
    bottom: -20px;
  }

  &.right {
    .info-border {
      right: -280px;
      top: -25px;

      .station-info {
        flex-direction: row-reverse;

        .station-label {
          margin-left: 0;
          margin-right: 12px;
        }

        .icon {
          margin-left: 0px;
        }
      }
    }

    .info-pointer {
      transform: scaleX(-1);
    }
  }
}

.pass-current-point {
  width: 200px;
  height: 200px;
  background: url("./img/bg_point.gif") 100% 100% no-repeat;
  background-size: 100% 100%;
  position: relative;
  pointer-events: none;

  img {
    position: absolute;
    top: 55px;
    left: 0px;
    width: 200px;
    height: 100px;
  }
}

.current-point-tip {
  min-width: 209px;
  height: 52px;
  background: #172537;
  border: 1px solid #00a0e9;
  position: relative;
  border-radius: 4px;

  .tip-angle {
    width: 8px;
    height: 8px;
    background: #172537;
    border-top: 1px solid #00a0e9;
    border-left: 1px solid #00a0e9;
    position: absolute;
    left: -6px;
    top: 20%;
    transform: rotate(-45deg);
  }

  .tip-content {
    height: 52px;
    display: flex;
    align-items: flex-start;
    flex-direction: column;
    justify-content: center;
    box-sizing: border-box;
    padding: 10px;

    .tip-info {
      font-family: PingFangSC-Regular;
      font-size: 14px;
      color: #ffffff;
      font-weight: 400;
    }
  }
}

.cameras-wrap,
.cluster-list-wrap {
  background-color: @background-color;
  border-radius: 5px;
  position: fixed;
  padding: 7px;
  pointer-events: auto;
}

#mainMap .ol-overlay-container.ol-selectable {
  // 会影响通用地图公共点位图层的详情面板点击
  // pointer-events: none !important;

  #CAMERA-camera-tip {
    pointer-events: auto !important;
  }
}
