<!--
 * @Description: 抓拍信息地图渲染组件
-->
<template>
  <div class="captureInfoWin-wrap">
    <div class="captureInfoWin-content">
      <video-player
        v-if="curPlayData.videoUrl"
        class="video video-player vjs-custom-skin infoVideo-player lunboVideo"
        :playsinline="true"
        :options="playerOptions"
        @ended="onVideoEnded"
        @error="onVideoError"
      />
      <img
        v-else
        alt="图片"
        :src="curPlayData.fileImgUrlIcon || defaultImg"
        fit="contain"
        class="img-style"
      />
      <div title="放大" class="common-button full-screen-img" @click="openFullScreen"></div>
    </div>
    <div class="captureInfoWin-buttons">
      <div
        :class="[
          'common-button',
          'video-play-img',
          openVideo && 'video-play-active',
        ]"
        title="实时视频"
        @click.stop="videoHandle"
      ></div>
      <div
        :class="[
          'common-button',
          'pass-route-img',
          openPassRouteWin && 'pass-route-active',
        ]"
        @click.stop="passRouteHandle"
        title="过车记录"
      ></div>
    </div>
  </div>
</template>
<script>
import { mapActions } from 'vuex'
import { Events } from '@/components/page/ScreenPages/IntegratedMonitor/enum/EventsEnum'
import { $playerFit } from '@/utils/playerFit'

export default {
  // 定义组件接收的外部属性
  props: {
    propData: {
      type: Object,
      required: true,
    },
  },
  // 初始化组件的内部数据
  data: function () {
    // 返回组件内部的数据对象
    return {
      captureIdx: 0, // 弹窗数据下标
      curPlayData: {}, // 当前播放的数据
      videoParam: null, // 实时视频参数
      openVideo: false, // 是否打开实时视频
      openPassRouteWin: false, // 是否打开过车路线窗口
      defaultImg: require('@/assets/images/comm/nonImg.png'), // 默认图片
      endTimer: null, // 定时器
      infoWinShowTime:
        (window?.sysConfig?.MAP_CAPTURE_INFO_SHOW_TIME || 10) * 1000, // 信息窗口显示时间，配置读不到就默认10s
      playerOptions: {
        // 小视频播放参数
        autoplay: true, // 不自动播放
        loop: false, // 循环播放
        muted: true, // 静音
        preload: 'auto', // 自动加载
        language: 'zh-CN',
        fluid: true, // 流体布局
        hls: false,
        sources: [
          {
            type: 'video/mp4',
            src: '', // 视频源
          },
        ],
        aspectRatio: '16:9',
        poster: '', // 封面地址
        choosed: false, // 被选中的
        notSupportedMessage: '视频录像服务维护中', // 不支持时的提示信息
        controls: false, // 关闭所有工具按钮
      },
    }
  },
  computed: {
    captureList() {
      const deviceCode = this.propData.deviceCode
      return this.$store.getters['captureInfo/getDeviceCaptureList'](deviceCode)
    },
  },
  mounted() {
    // 组件挂载完成后执行的函数
    window.addEventListener('message', this.closeRealTimeVideo) // 添加消息事件监听
    this.$EventBus.$on(
      Events.PassRouteEvt.CHANGE_PASS_ROUTE_OPEN_STATUS,
      this.initOpenPassRouteWin
    )
    // 开始播放
    this.startPlay()
  },
  beforeDestroy() {
    window.removeEventListener('message', this.closeRealTimeVideo) // 移除消息事件监听
    this.$EventBus.$off(
      Events.PassRouteEvt.CHANGE_PASS_ROUTE_OPEN_STATUS,
      this.initOpenPassRouteWin
    )
    // 组件销毁前移除视频播放逻辑
    if (this.openVideo) {
      this.closeRtVideo()
    }
  },
  // 定义组件的方法
  methods: {
    ...mapActions('captureInfo', ['loadedCaptureTask', 'stopRenderCaptureWin']),
    // 初始化打开过车路线窗口按钮
    initOpenPassRouteWin({ open }) {
      if (!open && this.openPassRouteWin) {
        this.openPassRouteWin = false
      }
    },
    // 打开全屏
    openFullScreen() {
      this.$EventBus.$emit(
        Events.PassRouteEvt.CHANGE_CAPTURE_FULL_SCREEN_OPEN_STATUS,
        { open: true, captureInfo: { ...this.curPlayData } }
      )
    },
    startPlay() {
      this.curPlayData = this.captureList?.[this.captureIdx] || {}
      if (this.curPlayData?.videoUrl) {
        this.playerOptions.sources = [
          {
            type: 'video/mp4',
            src: this.curPlayData.videoUrl, // 'https://www.runoob.com/try/demo_source/movie.mp4', //
          },
        ]
      }
      this.loadedCaptureTask(this.propData.deviceCode)
      // 开始播放
      console.log('开始播放')
      // 播放的时候，就把队列中此过车信息给移除掉
      // 定时任务，播放10秒后停止播放
      // return;
      // 如果是图片指定时间后消散
      if (this.curPlayData?.fileImgUrlIcon) {
        this.endTimer = setTimeout(() => {
          this.stopPlay()
        }, this.infoWinShowTime)
      }
      // }, 60 * 1000); // 临时把时间调长，测试
    },
    stopPlay() {
      // 停止播放
      console.log('停止播放')
      // 查看过车队列中是否还有下一个，没有的话，则关闭当前窗口，并且通知地图去重新绘制点位图标
      if (
        this.captureList &&
        this.captureList.length > 0 &&
        this.captureIdx < this.captureList.length
      ) {
        // 接着播放下一个
        this.captureIdx += 1
        this.startPlay()
        return
      }
      // 通知地图可以移除渲染了
      this.stopRenderCaptureWin(this.propData.deviceCode)
    },

    onVideoEnded() {
      this.stopPlay()
    },
    onVideoError() {
      setTimeout(() => {
        this.stopPlay()
      }, this.infoWinShowTime)
    },

    /**
     * 关闭实时视频的回调函数
     * 主要用于处理大屏幕播放器关闭事件
     * @param {Object} e 事件对象
     */
    closeRealTimeVideo(e) {
      // 检查回调方法是否为'bigScreenPlayerClose'
      if (e.data && e.data.callBackMethod === 'bigScreenPlayerClose') {
        // 检查视频播放数量，若为0，则重置选中状态和图标状态
        if (e.data.videoData.playVideoNum === 0) {
          this.videoParam = null
          this.openVideo = false
        }
      }
    },
    startRtVideo() {
      // 处理视频播放
      // latitude, longitude,
      const { deviceCode, channelCode } = this.curPlayData
      const channelCodes = [channelCode]
      const list = channelCodes.map(item => {
        return {
          deviceCode: deviceCode, //设备编号
          channelCode: item, //通道编号
          //   longitude: longitude, //经度
          //   latitude: latitude, //纬度
          showClose: true, //是否显示视频关闭按钮
        }
      })
      this.videoParam = list
      console.log('that[iconCode.playVideo]', list)
      $playerFit.right(list, 'append', { right: 10 })
    },
    closeRtVideo() {
      $playerFit.close(this.videoParam)
      this.videoParam = null
    },
    videoHandle() {
      this.openVideo = !this.openVideo
      if (this.openVideo) {
        // 打开实时视频
        this.startRtVideo()
      } else {
        // 关闭实时视频
        this.closeRtVideo()
      }
    },
    passRouteHandle() {
      this.openPassRouteWin = !this.openPassRouteWin
      if (this.openPassRouteWin) {
        // 打开过车路线窗口
        this.$EventBus.$emit(
          Events.PassRouteEvt.CHANGE_PASS_ROUTE_OPEN_STATUS,
          { open: true, deviceInfo: this.curPlayData }
        )
      } else {
        // 关闭过车路线窗口
        this.$EventBus.$emit(
          Events.PassRouteEvt.CHANGE_PASS_ROUTE_OPEN_STATUS,
          { open: false }
        )
      }
    },
  },
}
</script>
<style lang="less" scoped>
/* 定义组件的样式 */
.captureInfoWin-wrap {
  position: relative;
  width: 184px;
  height: 112px;
  background: url(./img/bg_capture_win_184.gif) no-repeat 100% 100%;
  background-size: 100% 100%;
  padding: 6px;
  box-sizing: border-box;
  &::after {
    content: '';
    position: absolute;
    bottom: -12px;
    right: -12px;
    width: 23px;
    height: 23px;
    background: url(./img/capture_win_jiaobiao.svg) no-repeat 100% 100%;
    background-size: 100% 100%;
  }
  .common-button {
    background: rgb(28, 44, 64, 0.9);
    border-radius: 2px;
    cursor: pointer;
    width: 20px;
    height: 20px;
    position: relative;
    pointer-events: all;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #e8f3ff;
    &::after {
      content: '';
      position: absolute;
      top: 4px;
      left: 4px;
      width: 12px;
      height: 12px;
    }
  }
  .captureInfoWin-content {
    width: 172px;
    height: 100px;
    position: relative;
    pointer-events: all;
    .video-player {
      height: 100%;
      width: 100%;
      /deep/ video {
        object-fit: cover;
      }
      /deep/ .vjs-big-play-button {
        top: 50% !important;
        left: 50% !important;
        height: 30px !important;
        line-height: 30px !important;
        width: 30px;
        border-radius: 50%;
        background-color: rgba(8, 0, 0, 0.4);
        border: none;
        margin: 0 !important;
        transform: translate(-50%, -50%);
      }
      /deep/ .video-js {
        height: 100% !important;
        border-radius: 4px;
      }
      /deep/ .vjs-poster {
        border-radius: 4px;
        background-size: cover;
      }
      /deep/ .vjs-modal-dialog-content {
        display: none;
      }
      /deep/ .vjs-icon-placeholder:before {
        font-size: 24px !important;
        color: #fffdef !important;
      }
    }
    .img-style {
      width: 100%;
      height: 100%;
    }
    .full-screen-img {
      position: absolute;
      right: 0;
      bottom: 0;
      &::after {
        background: url(./img/icon_full_screen_12_n.svg) no-repeat 100% 100%;
        background-size: 100% 100%;
      }
    }
  }
  .captureInfoWin-buttons {
    position: absolute;
    bottom: 0;
    left: 188px;
    width: 20px;
    height: 40px;
    .video-play-img {
      &::after {
        background: url(./img/icon_video_play_12_n.svg) no-repeat 100% 100%;
        background-size: 100% 100%;
      }
    }
    .video-play-active {
      &::after {
        background: url(./img/icon_video_play_12_s.svg) no-repeat 100% 100%;
        background-size: 100% 100%;
      }
    }
    .pass-route-img {
      &::after {
        background: url(./img/icon_pass_route_12_n.svg) no-repeat 100% 100%;
        background-size: 100% 100%;
      }
    }
    .pass-route-active {
      &::before {
        content: '';
        position: absolute;
        top: 1px;
        left: 1px;
        width: 18px;
        height: 18px;
        background-image: linear-gradient(180deg, #19feb3 1%, #000000 97%);
        border-radius: 2px;
      }
    }
  }
}
</style>
