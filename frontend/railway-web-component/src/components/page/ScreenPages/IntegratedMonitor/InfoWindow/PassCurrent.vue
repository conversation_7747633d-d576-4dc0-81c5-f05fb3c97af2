<!--
 * @Description:
 * @Autor: he.chao
 * @Date: 2022-10-19 15:07:55
 * @LastEditors: liu.yongli
 * @LastEditTime: 2023-12-13 14:36:14
-->
<template>
    <div :class='`pass-current-win ${propData.className || ""}`'>
      <div class='pass-info'>
        <div class='info-wrap'>
          <icon-route class='info-icon' style='color: #fefefe' />
          <div class='info-container'>
            <span class='info-route-seg' v-if='propData.segName' :c-tip='propData.segName'>{{ propData.segName }}</span>
            <span class='info-route-seg-null' v-else>{{ '>>>>>><<<<<<' }}</span>
            <span class='info-route-location' :c-tip='propData.address'>{{ propData.address }}</span>
          </div>
        </div>
      </div>
      <div class='pass-dir'></div>
    </div>
  </template>

  <script>
  import IconRoute from '@/components/page/ScreenPages/IntegratedMonitor/Icons/icon-route.vue';

  /**
   * 导出的默认对象定义了Vue组件的配置。
   * 该组件使用了IconRoute作为子组件，并接收一个名为propData的属性。
   * 它不维护任何内部状态，并且没有定义任何方法。
   */
  export default {
    // 注册IconRoute组件，用于UI展示
    components: { IconRoute },
    props: {
      // 定义propData属性，类型为对象，如果没有提供则默认为空对象
      propData: {
        type: Object,
        default: {}
      }
    },
    data() {
      // 组件不维护任何内部状态
      return {};
    },
    methods: {
      // 组件没有定义任何方法
    }
  };
  </script>

  <style lang='less' src='./index.less' />
