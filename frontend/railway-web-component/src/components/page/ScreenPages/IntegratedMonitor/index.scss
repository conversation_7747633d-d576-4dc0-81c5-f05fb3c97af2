@import "~@/assets/styles/px-to-rem";

.main-map {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 0;
  top: 0;
}

.left-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 366px;
  height: 100%;
  padding-top: 28px;
  margin-left: 22px;
  transition: all 0.3s;

  .left-wrapper-son {
    position: relative;
    height: calc(100% - 28px);
    overflow-y: auto;
    overflow-x: hidden;
    width: 100%;
    z-index: 1;

      &::-webkit-scrollbar {
          width: 4px !important; /* remove scrollbar space */
          background: #262e42 !important; /* optional: just make scrollbar invisible */
          height: 6px !important;
      }
      &::-webkit-scrollbar-thumb {
          background: #23559a !important; /* optional: just make scrollbar invisible */
      }
      &::-webkit-scrollbar-track {
          background-color: #262e42 !important; /* 滚动条轨道的颜色 */
      }
  }

  .leftbtn {
    pointer-events: none;
    display: block;
    width: 70px;
    height: 100%;
    text-align: center;
    font-size: 20px;
    position: absolute;
    top: 0;
    right: -65px;
    z-index: 88;
    background: url('../IntegratedMonitor/img/left_hide.png') no-repeat left
      center/100% 100%;
    &::after {
      content: '';
      position: absolute;
      right: 10px;
      top: 45%;
      width: 30px;
      height: 240px;
      pointer-events: auto;
      cursor: pointer;
    }
  }

  &.hide {
    transform: translateX(-400px);
    &::before {
      left: -400px;
    }
    > div {
      overflow: hidden;
    }
    .leftbtn {
      right: -108px;
      background: url('../IntegratedMonitor/img/left_show.png') no-repeat left
        center/100% 100%;
    }
  }
}

.right-background {
  position: absolute;
  width: px-to-rem(360);
  height: 100%;
  right: px-to-rem(12);
  display: flex;
  justify-content: center;
  pointer-events: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  z-index: 99;

  .map_legend_main_outter{
    z-index: -1;
  }

  &.open {
    right: px-to-rem(12);
    transition: right 1s cubic-bezier(0, 0.11, 0.18, 0.98);
  }

  &.close {
    right: px-to-rem(-360);
    transition: right 1s cubic-bezier(0, 0.11, 0.18, 0.98);
  }

  .btn-handle {
    height: 100%;
    width: 70px;
    position: absolute;
    left: -65px;
    cursor: pointer;
    pointer-events: auto;

    &.close {
    background: url("../IntegratedMonitor/img/right_show.png") 100% 100% no-repeat;
    background-size: 100% 100%;
    }

    &.open {
    background: url("../IntegratedMonitor/img/right_hide.png") 100% 100% no-repeat;
    background-size: 100% 100%;
    }
}

  .right-container {
    width: px-to-rem(366);
    height: calc(100% - px-to-rem(40));
    overflow-y: auto;
    position: absolute;
    top: px-to-rem(40);
    right: 0;
    pointer-events: auto;
    transition: all 0.5s;

    .show-hide-button {
      position: absolute;
      left: px-to-rem(-386);
      font-size: px-to-rem(88);
      display: flex;
      flex-direction: column;
      color: #96aabc;
      justify-content: flex-start;
      align-items: center;
      cursor: pointer;
      padding-right: px-to-rem(5);

      .button-box {
        width: px-to-rem(40);
        height: px-to-rem(40);
        background-image: linear-gradient(
          180deg,
          rgba(66, 95, 130, 0.4) 1%,
          rgba(72, 161, 231, 0.4) 97%
        );
        border: px-to-rem(2) solid #425f82;
        border-radius: px-to-rem(4);
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .selected {
        background-image: linear-gradient(
          180deg,
          rgba(234, 166, 123, 0.8) 1%,
          rgba(118, 89, 74, 0.8) 97%
        );
        border: px-to-rem(2) solid rgba(234, 166, 123, 1);
      }
    }

    .toolbar-button {
      position: absolute;
      left: px-to-rem(-386);
      top: px-to-rem(60);;
    }
  }

  .hidden {
    right: px-to-rem(238);
  }
}

// preload元素是一个不可见元素，它的目的就是用来预加载一些资源
.preload {
  background: url('~@/assets/images/common/left_show.png') no-repeat left
      center/100% 100%,
    url('~@/assets/images/common/right_show.png') no-repeat left center/100%
      100%;
}

.screen-container {
  position: relative;
  width: 100%;
  height: 100%;

  // 工具箱-点位测量
  ::v-deep .ctmap-union-measure-pop {
    .small-box-measureTip,
    .ctm-space-ground-measure {
      font-size: px-to-rem(16);
      .unit,
      .distance {
        font-weight: 600;
      }
      .trash,
      .close {
        font-size: px-to-rem(20);
        margin-left: px-to-rem(8);
      }
      .trash::before {
        content: '\e6d7';
        font-family: element-icons !important; // 必须使用
        position: absolute;
        background: #ffffff;
        right: px-to-rem(2);
        top: px-to-rem(4);
      }
    }
  }
  ::v-deep .ctm-space-ground-measure-cur {
    font-size: px-to-rem(16) !important;
    font-weight: 600;
  }

  // ::v-deep {
  //   i[c-tip~='到这里'],
  //   .uav-tree-dialog-footer:has(.icon-tongyong_icon_daozheli_s_30),
  //   .footerIconArea:has(.icon-icon_daozheli_30_n),
  //   .icon-icon_guanlian_20_n,
  //   .footerIconArea:has(.icon-icon_zhoubianfenxi_30_n),
  //   .footer .f_cont .icon-icon_zhoubianfenxi_30_n {
  //     display: none !important;
  //   }
  // }
  ::v-deep .common-comp-alarm-detail {
    .alarm-info-box.common-comp-alarm-detail-module {
      z-index: 100 !important;
    }
  }
  ::v-deep .common-comp-footer__footer_func_area {
    z-index: 1;
  }
  .maptooltele {
    width: 100%;
    height: 100%;
    pointer-events: none;
    position: relative;
    z-index: 2;
    ::v-deep .map-tools {
      pointer-events: all;
      transition: all 0.53s;
      position: absolute;
      bottom: 0;
      right: px-to-rem(10) !important;

      .tile-control,
      .compass-tool,
      .zoom-tool,
      .scale-line {
        z-index: 0;
      }

      .tile-control {
        position: absolute;
        right: px-to-rem(56);
        bottom: px-to-rem(68);

        .ctmap-union-layer-switcher {
          width: px-to-rem(88) !important;

          &:hover {
            // px-to-rem(339)
            width: px-to-rem(256) !important;
          }
        }

        // .ctmap-union-layer-switcher__layerlist {
        //   // 修改常规
        //   // 设置图层控制中替换为深色地图样式
        //   // div[data-index='0'] {
        //   //   background-image: url('../IntegratedMonitor/Map/MapTool/img/2D-dark-map.png') !important;
        //   //   background-repeat: no-repeat;

        //   //   .map-tile-name::before {
        //   //     content: '深色地图';
        //   //     position: absolute;
        //   //     background: #fff;
        //   //   }
        //   // }
        //   // // 悬浮时的样式
        //   // div[data-index='0'].map-tile-item__active,
        //   // div[data-index='0']:hover {
        //   //   .map-tile-name::before {
        //   //     content: '深色地图';
        //   //     position: absolute;
        //   //     color: #ffffff !important;
        //   //     background: #1373e6 !important;
        //   //   }
        //   // }

        //   // 修改地形
        //   // 设置图层控制中替换为深色地图样式
        //   // div[data-index='2'] {
        //   //   background-image: url('../IntegratedMonitor/Map/MapTool/img/2D-map.jpeg') !important;
        //   //   background-repeat: no-repeat;

        //   //   .map-tile-name::before {
        //   //     content: '常规地图';
        //   //     position: absolute;
        //   //     background: #fff;
        //   //   }
        //   // }
        //   // // 悬浮时的样式
        //   // div[data-index='2'].map-tile-item__active,
        //   // div[data-index='2']:hover {
        //   //   .map-tile-name::before {
        //   //     content: '常规地图';
        //   //     position: absolute;
        //   //     color: #ffffff !important;
        //   //     background: #1373e6 !important;
        //   //   }
        //   // }

        //   .map-tile-name {
        //     color: #172537;
        //   }

        //   .map-tile-item:hover,
        //   .map-tile-item__active {
        //     .map-tile-name {
        //       color: #e8f3fe;
        //     }
        //   }
        // }
      }
    }
    &.fix-map-tool {
      ::v-deep .map-tools {
        // transition: all 0.53s;
        // right: px-to-rem(383) !important; // 用于控制地图控制工具的位置
        right: px-to-rem(372) !important;
      }
    }
  }
  .tool-box {
    top: px-to-rem(48);
    right: px-to-rem(18);
    transition: all 0.3s;
    &.fix-map-tool {
      right: px-to-rem(416);
    }

    // :deep {
    //   // .tool-box-trigger,
    //   // .tool-box-trigger__icon {
    //   //   display: none;
    //   //   // background-image: url('../../assets/images/common/icon_gongju.png') !important;
    //   // }
    // }
  }

  .mapsearch {
    position: absolute;
    top: px-to-rem(48);
    left: px-to-rem(416);
    transition: all 0.3s;
    z-index: 99; // 左wrapper为1
    &.fix-mapsearch {
      left: px-to-rem(24);
    }
  }

  .chartmodule {
    + .chartmodule {
      margin-top: px-to-rem(12);
    }
  }

  .aroundanalysis {
    top: px-to-rem(88);
    right: px-to-rem(481);
    &.hide {
      right: px-to-rem(83);
    }
  }

  .tree-wrapper {
    position: absolute;
    flex: 1;
    top: px-to-rem(108);
    left: px-to-rem(24);
    height: calc(100% - px-to-rem(182));
    .dtreebox {
      ::v-deep .treeBox {
        left: px-to-rem(-2);
      }
    }
  }
  // 告警筛选定位
  ::v-deep .alarm-filte {
    top: px-to-rem(94);
    left: calc(50% - px-to-rem(438));
    .common-iw-s.el-picker-panel.el-popper[x-placement^='bottom'] {
      margin-top: px-to-rem(12);
    }
  }
}
.top-background {
  width: 100%;
  height: px-to-rem(200);
  top: px-to-rem(-51);
  transition: all 0.5s;
  pointer-events: none;
  position: absolute;
  display: flex;
  justify-content: center;
  background: url('~@/assets/images/common/dataCenterTopBg.png') no-repeat 0 0 /
    100% px-to-rem(166);
}

.searchmapp {
  &.navipotision {
    ::v-deep .d-search-map__navigation-popup__wrapper {
      top: 0;
      right: px-to-rem(-258);
    }
  }
}

.toolsubdialog {
  ::v-deep .SpotSearch,
  ::v-deep .SpotSpace,
  ::v-deep .map-swiper-container {
    top: px-to-rem(107);
    right: px-to-rem(475);
  }

  &.hide {
    ::v-deep .SpotSearch,
    ::v-deep .SpotSpace,
    ::v-deep .map-swiper-container {
      right: px-to-rem(85);
    }
  }

  &.max-width {
    ::v-deep .tool_spot_detail {
      .innercomp-abcontainer {
        width: calc(100vw - px-to-rem(65)) !important;
      }
    }
  }
  &.min-width {
    ::v-deep .tool_spot_detail {
      .innercomp-abcontainer {
        width: calc(100vw - px-to-rem(865)) !important;
      }
    }
  }
  &.hide-left-width {
    ::v-deep .tool_spot_detail {
      .innercomp-abcontainer {
        width: calc(100vw - px-to-rem(475)) !important;
      }
    }
  }
  &.hide-right-width {
    ::v-deep .tool_spot_detail {
      .innercomp-abcontainer {
        width: calc(100vw - px-to-rem(465)) !important;
      }
    }
  }
  ::v-deep .tool_spot_detail {
    .innercomp-abcontainer {
      position: fixed;
    }
  }
}
::v-deep .tool-box-container {
  display: none;
}