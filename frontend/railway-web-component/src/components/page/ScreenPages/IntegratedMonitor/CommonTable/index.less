@import "../common";
.common-table-wrap {
  width: 100%;

  &.el-table {
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #e8f3ff;
    letter-spacing: 0;
    font-weight: 400;

    :deep .ascending .sort-caret.ascending {
      border-bottom-color: @theme-color;
    }

    :deep .descending .sort-caret.descending {
      border-top-color: @theme-color;
    }
    :deep .sound-wrap {
      display: flex;
      align-items: center;
      justify-content: flex-start;

      .sound-info {
        height: 20px;
        width: 54px;
        background: @theme-color;
        border-radius: 4px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        &.onplay {
          border: 1px solid #fefefe;
        }
        .sound-icon {
          width: 20px;
          height: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-left: 4px;
          margin-right: 4px;
          border-right: 1px solid #f3f3f34d;
          i {
            svg {
              width: 10px;
              height: 10px;
            }
          }

          img {
            width: 6px;
            height: 8px;
          }
        }

        .sound-icon-playing {
          margin-left: 0;
          img {
            width: 100%;
            height: 100%;
          }
        }

        .info-wrap {
          width: 34px;
          font-size: 10px;
          color: #ffffff;
          letter-spacing: 0;
          line-height: 14px;
          font-weight: 400;
          text-align: center;
        }
      }

      .sound-ctl {
        width: 20px;
        height: 20px;
        background: @theme-color;
        border-radius: 4px;
        cursor: pointer;
        margin-left: 1px;
        display: flex;
        align-items: center;
        justify-content: center;

        img {
          width: 8px;
          height: 8px;
        }
      }
    }

    :deep .el-table__header {
      width: 100% !important;
      th.el-table__cell > .cell {
        display: flex;
        align-items: center;
        justify-content: flex-start;

        .el-table__column-filter-trigger {
          opacity: 0;
        }

        &.highlight {
          .icon {
            svg {
              fill: @theme-color;
            }
          }
        }
      }
    }

    :deep .cell {
      line-height: 16px;
      font-size: 14px;
      // height: 32px;
      display: flex;
      align-items: center;

      .el-button.el-button--text:focus,
      .el-button.el-button--text:hover {
        color: #66b1ff;
      }

      .el-button.el-button--text:focus,
      .el-button.el-button--text:hover {
        color: #66b1ff !important;
      }
    }
  }

  :deep .el-table__body {
    width: 100% !important;

    tr.current-row > td.el-table__cell,
    .el-table__body tr.selection-row > td.el-table__cell {
      color: @theme-color;
      background: #15447d !important;
    }
  }
  &.el-table--striped
    :deep
    .el-table__body
    tr.el-table__row--striped
    td.el-table__cell {
    background: transparent;
  }

  &.el-table :deep tr {
    background-color: transparent;
  }

  &.el-table :deep td.el-table__cell,
  &.el-table :deep th.el-table__cell.is-leaf {
    border-bottom: 1px solid transparent;
  }

  &.el-table
    :deep
    .el-table__header-wrapper
    .el-table__header
    th.el-table__cell.is-leaf {
    &:not(:last-child) {
      border-right: 1px solid #fefefe2e;
    }
  }

  &.el-table :deep td.el-table__cell,
  .common-table-wrap .el-table th.el-table__cell.is-leaf {
    border-bottom: 1px solid transparent;
  }

  &.el-table--enable-row-hover
    :deep
    .el-table__body
    tr:hover
    > td.el-table__cell {
    background-color: transparent;
  }

  &.el-table :deep tr:hover {
    background: #15447d;
  }

  :deep .el-table--border::after,
  :deep .el-table--group::after,
  &.el-table::before {
    background: transparent;
  }

  &.el-table--enable-row-hover
    :deep
    .el-table__body
    tr:hover
    > td.el-table__cell {
    background-color: #15447d;
  }

  &.el-table--small :deep .el-table__cell {
    padding: 0;
  }

  &.el-table,
  .el-table__expanded-cell {
    background-color: #172537;
  }

  &.el-table :deep th.el-table__cell {
    background: rgba(79, 159, 255, 0.2);
  }

  &.el-table :deep thead {
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #e8f3ff;
    letter-spacing: 0;
    font-weight: 400;
  }

  &.el-table :deep td.el-table__cell,
  .el-table th.el-table__cell.is-leaf {
    border-bottom: 1px solid transparent;
  }

  &.el-table :deep td.el-table__cell {
    border-bottom: 1px solid hsla(0, 0%, 100%, 0.1);
  }
}
