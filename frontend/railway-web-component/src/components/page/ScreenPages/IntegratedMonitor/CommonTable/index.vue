<!--
 * @Description: 基于Element UI的表格组件，用于展示数据表格。
 * @Autor: he.chao
 * @Date: 2022-10-19 15:07:55
 * @LastEditors: liu.yongli
 * @LastEditTime: 2023-12-13 14:36:14
-->
<template>
  <!-- 使用Element UI的el-table组件 -->
  <el-table
   ref='singleTable'
    stripe
    :data='tableData' 
    :height='height'
    class='common-table-wrap' 
    :highlight-current-row='highlightCurrentRow' 
    @current-change='onCurrentChange' 
    @sort-change='sortChange' 
    @filter-change='filterChange' 
    @row-click='rowClick' 
  >
    <!-- 插槽，用于自定义列 -->
    <slot name='column'></slot>
  </el-table>
</template>
<script>
/**
 * 表格组件，基于Element UI实现，用于展示数据表格并提供交互功能。
 *
 * @prop {Number} height 表格的高度。
 * @prop {Array} tableData 表格的数据源。
 * @prop {Boolean} highlightCurrentRow 是否高亮当前行。
 * @prop {Function} handleCurrentChange 当当前行改变时的回调函数。
 * @prop {Function} sortChange 当排序改变时的回调函数。
 * @prop {Function} filterChange 当过滤条件改变时的回调函数。
 * @prop {Function} clearFilter 清除过滤条件的回调函数。
 * @prop {Function} rowClick 当行被点击时的回调函数。
 */
export default {
  props: {
    height: {
      type: Number, // 高度的类型为数字
      default: 300 // 默认高度为300
    },
    tableData: {
      type: Array, // 数据源的类型为数组
      default: () => [] // 默认数据源为空数组
    },
    highlightCurrentRow: {
      type: Boolean, // 是否高亮当前行的类型为布尔值
      default: false // 默认不高亮
    },
    handleCurrentChange: {
      type: Function, // 当前行改变时的回调函数类型为函数
      default: () => console.log('current-change') // 默认打印'current-change'
    },
    sortChange: {
      type: Function, // 排序改变时的回调函数类型为函数
      default: () => console.log('sort-change') // 默认打印'sort-change'
    },
    filterChange: {
      type: Function, // 过滤条件改变时的回调函数类型为函数
      default: () => console.log('filter-change') // 默认打印'filter-change'
    },
    clearFilter: {
      type: Function, // 清除过滤条件的回调函数类型为函数
      default: () => console.log('clear-filter') // 默认打印'clear-filter'
    },
    rowClick: {
      type: Function, // 行被点击时的回调函数类型为函数
      default: () => console.log('row-click') // 默认打印'row-click'
    }
  },
  computed: {},
  data() {
    return {
      currentRow: null // 当前选中的行数据
    };
  },
  methods: {
    /**
     * 清除选中状态，即清空当前选中行。
     */
    clearSelection() {
      this.currentRow = null; // 清空当前选中行的数据
      this.$refs.singleTable.setCurrentRow(null); // 清除表格中选中的行
    },
    /**
     * 当前行改变时的处理函数，更新currentRow并触发父组件的回调。
     *
     * @param {Object} val 当前行的数据。
     */
    onCurrentChange(val) {
      this.currentRow = val; // 更新当前选中行的数据
      this.handleCurrentChange(val); // 调用父组件传入的回调函数
    },
    /**
     * 清除所有列的过滤条件。
     */
    clearAllFilter() {
      this.$refs.singleTable.clearFilter(); // 调用Element UI的方法清除过滤条件
    },
    /**
     * 清除排序。
     */
    clearSort() {
      this.$refs.singleTable.clearSort(); // 调用Element UI的方法清除排序
    },
    /**
     * 设置列的排序。
     *
     * @param {String} columnKey 列的属性键。
     * @param {String} order 排序顺序，可以是'ascending'或'descending'。
     */
    setColumnSort(columnKey, order) {
      let columns = this.$refs.singleTable.columns; // 获取表格的列
      columns.forEach((column) => {
        if (column.sortable === 'custom' && column.property === columnKey) {
          column.multiOrder = order; // 设置列的排序顺序
        }
      });
    }
  }
};
</script>
<style scoped lang='less' src='./index.less' />
