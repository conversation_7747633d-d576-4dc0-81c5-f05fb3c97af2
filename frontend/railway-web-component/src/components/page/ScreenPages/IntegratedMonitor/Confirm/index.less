@import "../common.less";

.confirm-pop {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;

  .confirm-pop-bg {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
  }

  @pop-content-width: 320px;
  .confirm-pop-content {
    position: absolute;
    width: @pop-content-width;
    font-family: PingFangSC-Regular;
    color: #E8F3FF;
    height: 114px;
    top: 0;
    left: calc((100% - @pop-content-width - 20px) / 2);
    background-color: @background-color;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: space-between;
    padding: 10px;
    border-radius: 4px;
    box-shadow: 0 3px 3px #000;

    .el-icon-error {
      position: absolute;
      top: -8px;
      right: -8px;
      cursor: pointer;
    }

    > div {
      width: 100%;
    }

    .pop-title {
      height: 30px;
      font-family: PingFangSC-Medium;
      font-size: 16px;
      letter-spacing: 0;
      font-weight: 500;
    }

    .pop-body {
      font-size: 14px;
      letter-spacing: 0;
      font-weight: 400;
    }

    .pop-footer {
      display: flex;
      align-items: center;
      justify-content: flex-end;

      span {
        font-size: 12px;
        margin-left: 12px;
        color: #FFFFFF;
        letter-spacing: 0;
        font-weight: 500;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 48px;
        height: 24px;
        border-radius: 4px;
        cursor: pointer;
      }

      .btn-cancel {
        background: rgba(19, 115, 231, 0.20);
      }

      .btn-confirm {
        background-image: linear-gradient(90deg, #2FB1EC 8%, #155BD4 100%);

      }
    }
  }
}
