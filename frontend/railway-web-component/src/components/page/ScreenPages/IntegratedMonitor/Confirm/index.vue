<!--
 * @Description: 弹出确认框组件，用于显示简单的确认或取消操作的提示信息。
 * @Autor: he.chao
 * @Date: 2022-10-19 15:07:55
 * @LastEditors: liu.yongli
 * @LastEditTime: 2023-12-13 14:36:14
-->
<template>
  <!-- 当 popShow 为 true 时显示弹窗 -->
  <div v-if='popShow' class='confirm-pop'>
    <!-- 点击背景关闭弹窗 -->
    <div class='confirm-pop-bg' @click='closeClick'></div>
    <div class='confirm-pop-content'>
      <!-- 点击图标关闭弹窗 -->
      <i class='el-icon-error' @click='closeClick'></i>
      <div class='pop-title'>
        {{ popTitle }} <!-- 显示弹窗标题 -->
      </div>
      <div class='pop-body'>
        {{ popMsg }} <!-- 显示弹窗消息 -->
      </div>
      <div class='pop-footer'>
        <!-- 确认按钮，点击执行确认操作 -->
        <span class='btn-confirm' @click='okClick'>确认</span>
        <!-- 取消按钮，点击执行关闭操作 -->
        <span class='btn-cancel' @click='closeClick'>取消</span>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  components: {},
  data() {
    return {
      popTitle: '', // 确认框标题
      popShow: false, // 控制弹窗是否显示
      popMsg: '', // 弹窗显示的消息
      okHandle: null, // 确认操作的回调函数
      close: null // 关闭操作的回调函数
    };
  },
  methods: {
    /**
     * 显示确认框
     * @param {Object} options - 弹窗配置项
     * @param {string} options.msg - 弹窗显示的消息
     * @param {string} options.title - 弹窗标题，默认为'提示'
     * @param {Function} options.okHandle - 确认操作的回调函数
     * @param {Function} options.close - 关闭操作的回调函数
     */
    show(options) {
      this.popMsg = options.msg; // 设置弹窗消息
      this.okHandle = options.okHandle; // 设置确认操作回调
      this.popTitle = options.title || '提示'; // 设置弹窗标题，默认为'提示'
      this.close = options.close; // 设置关闭操作回调
      this.popShow = true; // 显示弹窗
    },
    /**
     * 点击确认按钮时执行的操作
     */
    okClick() {
      this.okHandle && this.okHandle(); // 如果确认回调存在，则执行
      this.popShow = false; // 隐藏弹窗
    },
    /**
     * 点击取消按钮或背景时执行的操作
     */
    closeClick() {
      this.close && this.close(); // 如果关闭回调存在，则执行
      this.popShow = false; // 隐藏弹窗
    }
  }
};
</script>
<style scoped lang='less' src='./index.less' />
