@import "../common";

.broad-cast-list.ctl-panel {
  width: 765px;
  height: 440px;
  padding: 0 12px;
  border-radius: 8px;
  box-sizing: border-box;

  .el-radio__input.is-checked + .el-radio__label {
    color: #409eff !important;
  }

  .el-radio__input.is-checked .el-radio__inner {
    border-color: #409eff !important;
    background: #409eff !important;
  }

  .el-radio-group {
    font-size: 0;
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }

  .el-radio {
    margin: 0;
  }

  .el-radio__label {
    padding: 0 12px 0 6px;
  }

  .el-select__tags {
    max-width: 100% !important;
  }

  .ctl-header {
    justify-content: space-between;
  }

  .list-ctl {
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: flex-start;

    .ctl-label {
      font-size: 14px;
      color: #e8f3ff;
      letter-spacing: 0;
      font-weight: 400;
    }

    .el-select {
      width: 160px;
      // height: 32px;
      margin: 0 12px;

      .el-input__inner {
        background-color: rgba(79, 159, 255, 0.2);
        border: none;
      }
    }

    .el-date-editor {
      width: 290px;
      margin-left: 12px;

      &.el-input__inner {
        background-color: rgba(79, 159, 255, 0.2);
        border: none;
      }

      .el-range-separator {
        color: #fefefe;
      }

      .el-range-input {
        background-color: transparent;
        color: #fefefe;
      }
    }
  }

  .list-content {
    height: 290px;

    .common-table-wrap {
      height: 100% !important;

      .el-table__cell {
        padding: 5px 0 !important;
      }
    }
  }

  .list-page {
    margin-top: @unit;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }

  .common-table-wrap.el-table {
    .el-table__cell {
      .cell-play-stat {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: flex-start;

        &.playing {
          color: #fb913c;
        }

        .icon {
          width: 42px;
          height: 16px;
        }
      }

      .cell-play-res {
        &.res-C {
          color: #15bd94;
        }

        &.res-E {
          color: #ed5158;
        }

        &.res-U {
          color: #909399;
        }
      }
    }
  }
}

.el-table-filter__list-item:hover,
.el-table-filter__list-item.is-active {
  background-color: #f5f7fa !important;
  color: black !important;
}
