<!--
 * @Description:
 * @Autor: he.chao
 * @Date: 2022-10-19 15:07:55
 * @LastEditors: liu.yongli
 * @LastEditTime: 2024-03-20 15:40:08
-->
<template>
  <!-- 控制面板组件，包含关闭功能 -->
  <CtlPanel class="broad-cast-list" :close="close">
    <!-- 标题插槽 -->
    <template v-slot:title>播放列表</template>
    <!-- 内容插槽 -->
    <template v-slot:content>
      <div class="list-ctl">
        <span class="ctl-label">广播类型</span>
        <!-- 下拉选择组件，用于选择广播类型 -->
        <el-select
          v-model="selectedBroadCastType"
          multiple
          collapse-tags
          clearable
          @change="broadCastTypeChange"
          placeholder="请选择"
        >
          <!-- 遍历广播类型选项 -->
          <el-option
            v-for="item in broadCastTypes"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
        <!-- 如果播放状态为已播放，显示播放时间选择器 -->
        <template v-if="selectedPlayStat ===  playStatCode.palyed">
          <span class="ctl-label">播放时间</span>
          <!-- 日期范围选择器 -->
          <el-date-picker
            v-model="selectedPlayTime"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            align="right"
            @change="playTimeChange"
          ></el-date-picker>
        </template>
      </div>
      <div class="list-content" style="position: relative">
        <!-- 通用表格组件 -->
        <CommonTable
          ref="table"
          :height="190"
          :table-data="meta"
          :sort-change="sortChange"
          :filter-change="filterHandler"
        >
          <template v-slot:column>
            <!-- 如果播放状态为未播放，显示相关列 -->
            <template v-if="selectedPlayStat === playStatCode.unplay">
              <!-- 广播类型列 -->
              <el-table-column
                v-if="!broadTypeColumnAttr.sortable"
                :key="Math.random() + '__broad_type'"
                columnKey="broad_type"
                prop="broad_type"
                label="广播类型"
                :filter-multiple="false"
                :filtered-value="filterValue.broad_type"
                :filters="broadTypeColumnAttr.filters"
              >
                <!--  slot-scope="scope" -->
                <template slot="header">
                  广播类型
                  <icon-table-header-filter />
                </template>
                <template slot-scope="scope">{{ scope.row.broadType }}</template>
              </el-table-column>
              <!-- 广播类型列（可排序） -->
              <el-table-column v-else prop="broad_type" label="广播类型" sortable="custom">
                <template slot-scope="scope">{{ scope.row.broadType }}</template>
              </el-table-column>
              <!-- 执行时段列 -->
              <el-table-column
                key="column-playTimePeriod"
                prop="playTimePeriod"
                label="执行时段"
                show-overflow-tooltip
                width="175"
              ></el-table-column>
              <!-- 循环列 -->
              <el-table-column
                key="column-cycle"
                prop="cycle"
                label="循环"
                show-overflow-tooltip
                width="175"
              >
                <template slot-scope="scope">{{ formatInterVal(scope.row.cycle) }}</template>
              </el-table-column>
            </template>
            <!-- 如果播放状态为已播放，显示相关列 -->
            <template v-if="selectedPlayStat === playStatCode.palyed">
              <!-- 播放结果列 -->
              <el-table-column
                v-if="!playResColumnAttr.sortable"
                :key="Math.random() + '__broad_type'"
                column-key="play_state"
                prop="play_state"
                label="播放结果"
                :filter-multiple="false"
                :filtered-value="filterValue.play_state"
                :filters="playResColumnAttr.filters"
                width="120"
              >
                <!--  slot-scope="scope" -->
                <template slot="header">
                  播放结果
                  <icon-table-header-filter />
                </template>
                <template slot-scope="scope">
                  <div
                    v-if="scope.row.playResCode"
                    :class="`cell-play-res res-${scope.row.playResCode}`"
                  >{{ scope.row.playRes }}</div>
                  <div v-else class="cell-play-res">-</div>
                </template>
              </el-table-column>
              <!-- 播放结果列（可排序） -->
              <el-table-column
                v-else
                column-key="play_state"
                key="play_state"
                prop="play_state"
                label="播放结果"
                sortable="custom"
              >
                <template slot-scope="scope">
                  <div
                    v-if="scope.row.playResCode"
                    :class="`cell-play-res res-${scope.row.playResCode}`"
                  >{{ scope.row.playRes }}</div>
                  <div v-else class="cell-play-res">-</div>
                </template>
              </el-table-column>
              <!-- 广播类型列 -->
              <el-table-column
                v-if="!broadTypeColumnAttr.sortable"
                :key="Math.random() + '__broad_type'"
                columnKey="broad_type"
                prop="broad_type"
                label="广播类型"
                :filter-multiple="false"
                :filtered-value="filterValue.broad_type"
                :filters="broadTypeColumnAttr.filters"
              >
                <!--  slot-scope="scope" -->
                <template slot="header">
                  广播类型
                  <icon-table-header-filter />
                </template>
                <template slot-scope="scope">{{ scope.row.broadType }}</template>
              </el-table-column>
              <!-- 广播类型列（可排序） -->
              <el-table-column v-else prop="broad_type" label="广播类型" sortable="custom">
                <template slot-scope="scope">{{ scope.row.broadType }}</template>
              </el-table-column>
              <!-- 播放时间列 -->
              <el-table-column
                key="column-playTime"
                prop="playTime"
                label="播放时间"
                show-overflow-tooltip
                width="160"
              >
                <template slot-scope="scope">{{ formatTime(scope.row.playTime) }}</template>
              </el-table-column>
            </template>
            <!-- 操作列 -->
            <el-table-column label="操作" width="146">
              <template slot-scope="scope">
                <!-- 如果播放状态为未播放，显示相关操作按钮 -->
                <template v-if="selectedPlayStat === playStatCode.unplay">
                  <el-button type="text" @click="goDetail(scope.row)">详情</el-button>
                  <el-button type="text" @click="goEdit(scope.row)">编辑</el-button>
                  <el-button type="text" @click="cancelPlay(scope.row)">取消</el-button>
                </template>
                <!-- 如果播放状态为已播放，显示相关操作按钮 -->
                <template v-if="selectedPlayStat === playStatCode.palyed">
                  <el-button type="text" @click="goHisDetail(scope.row)">详情</el-button>
                </template>
              </template>
            </el-table-column>
          </template>
        </CommonTable>
        <!-- 确认弹窗组件 -->
        <confirm ref="confirmPop" />
      </div>
      <div class="list-page">
        <!-- 分页组件 -->
        <el-pagination
          class="im-page"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        ></el-pagination>
      </div>
      <!-- 音频播放器组件 -->
      <AudioPlayer ref="audioPlayer" :audioSrc="audioSrc" audioId="subscribeAudioList" />
    </template>
  </CtlPanel>
</template>
<script>
import commonService from '@/api/service/common'
import {
  cancelSub,
  getBroadCastHistoryGroup,
  getBroadcastList,
} from '@/api/service/imScreenService'
import ConstEnum from '@/components/common/ConstEnum'
import AudioPlayer from '@/components/page/ScreenPages/IntegratedMonitor/AudioPlayer/index.vue'
import CommonTable from '@/components/page/ScreenPages/IntegratedMonitor/CommonTable/index.vue'
import Confirm from '@/components/page/ScreenPages/IntegratedMonitor/Confirm'
import CtlPanel from '@/components/page/ScreenPages/IntegratedMonitor/CtlPanel'
import IconCopy from '@/components/page/ScreenPages/IntegratedMonitor/Icons/icon-copy.vue'
import IconMetaPlay from '@/components/page/ScreenPages/IntegratedMonitor/Icons/icon-meta-play.vue'
import IconMetaStop from '@/components/page/ScreenPages/IntegratedMonitor/Icons/icon-meta-stop.vue'
import IconPlayingStat from '@/components/page/ScreenPages/IntegratedMonitor/Icons/icon-playing-stat.vue'
import IconTableHeaderFilter from '@/components/page/ScreenPages/IntegratedMonitor/Icons/icon-table-header-filter.vue'
import { toHump } from '@/utils/common'
import dayjs from 'dayjs'

// 播放状态代码
const playStatCode = {
  unplay: 2, // 未播放
  playing: 3, // 播放中
  palyed: 1, // 已播放
}

// 表头操作类型
const headOptType = {
  filter: 'filter', // 过滤
  sort: 'sort', // 排序
}

// 播放结果代码
const playResCode = {
  success: {
    code: 'C',
    name: '成功',
  },
  error: {
    code: 'E',
    name: '失败',
  },
  unknown: {
    code: 'U',
    name: '未知',
  },
}
export default {
  props: {
    propData: {
      type: Object,
      default: {},
    },
    close: {
      type: Function,
      default: {},
    },
  },
  components: {
    AudioPlayer,
    Confirm,
    IconPlayingStat,
    IconTableHeaderFilter,
    IconCopy,
    IconMetaPlay,
    IconMetaStop,
    CommonTable,
    CtlPanel,
  },
  data: function () {
    return {
      localPlayingMeta: '', // 当前播放的音频元数据
      playResCode, // 播放结果代码
      playStatCode, // 播放状态代码
      audioSrc: '', // 音频源
      selectedDateTime: '', // 选中的日期时间
      selectedPlayStat: playStatCode.palyed, // 选中的播放状态
      broadCastTypes: [], // 广播类型列表
      selectedBroadCastType: null, // 选中的广播类型
      selectedPlayTime: [], // 选中的播放时间
      broadTypeColumnAttr: {}, // 广播类型列属性
      playResColumnAttr: {}, // 播放结果列属性
      pickerOptions: {
        // 日期选择器选项
        shortcuts: [
          {
            text: '最近一周',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: '最近三个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
              picker.$emit('pick', [start, end])
            },
          },
        ],
      },
      headerOptions: {}, // 表头选项
      filterValue: {}, // 过滤值
      currentPage: 1, // 当前页码
      total: 0, // 总记录数
      pageSize: 10, // 每页记录数
      meta: [], // 表格数据
    }
  },
  mounted() {
    // 获取广播类型和广播列表
    this.getBroadCastType()
    this.getBroadCastList(true)
  },
  methods: {
    /**
     * 跳转到详情页面
     * @param {Object} row 行数据
     */
    goDetail(row) {
      // 获取设备代码列表
      const deviceCodes = this.propData.devices.map(item => item.deviceCode)
      // 触发消息工具，跳转到详情页面
      this.$postMsgUtil.trigger(null, 'redirectTo', {
        isOpener: true,
        url: `/subscribeBroadcast?type=detail&bookId=${
          row.key
        }&deviceCodes=${deviceCodes.join(',')}`,
      })
    },
    /**
     * 跳转到历史详情页面
     * @param {Object} row 行数据
     */
    goHisDetail(row) {
      // 获取设备代码列表
      const deviceCodes = this.propData.devices.map(item => item.deviceCode)
      // 触发消息工具，跳转到历史详情页面
      this.$postMsgUtil.trigger(null, 'redirectTo', {
        isOpener: true,
        url: `/BroadcastHis?type=detail&bookId=${
          row.key
        }&deviceCodes=${deviceCodes.join(',')}`,
      })
    },
    /**
     * 跳转到编辑页面
     * @param {Object} row 行数据
     */
    goEdit(row) {
      // 获取设备代码列表
      const deviceCodes = this.propData.devices.map(item => item.deviceCode)
      // 触发消息工具，跳转到编辑页面
      this.$postMsgUtil.trigger(null, 'redirectTo', {
        isOpener: true,
        url: `/subscribeBroadcast?type=edit&bookId=${
          row.key
        }&deviceCodes=${deviceCodes.join(',')}`,
      })
    },
    /**
     * 播放MP3
     * @param {string} audioUrl 音频URL
     * @param {string} key 数据唯一键
     */
    // 试听，下载播放mp3
    playMp3(audioUrl, key) {
      this.$refs.audioPlayer.stop()
      if (!this.localPlayingMeta || this.localPlayingMeta !== key) {
        this.audioSrc = audioUrl
        this.localPlayingMeta = key
        return
      }
      this.audioSrc = null
      this.localPlayingMeta = null
    },
    /**
     * 停止播放MP3
     */
    stopMp3() {
      const audio = document.getElementById('subscribeAudioList')
      if (!audio) {
        return
      }
      this.localPlayingMeta = ''
      audio.pause()
      audio.currentTime = 0
    },
    /**
     * 格式化时间
     * @param {string} time 时间戳
     * @returns {string} 格式化后的时间
     */
    formatTime(time) {
      return dayjs(time).format(ConstEnum.DATE_FORMAT.yyyy_mm_dd_hh_mm_ss)
    },
    /**
     * 清空头部选项
     */
    clearHeaderOptions() {
      this.headerOptions = {}
      this.filterValue = {}
    },
    /**
     * 选择播放时间后触发的函数
     * @param {Array} dateTimes 选择的播放时间
     */
    playTimeChange(dateTimes) {
      this.selectedPlayTime = dateTimes || []
      this.resetTable()
    },
    /**
     * 重置表格
     */
    resetTable() {
      this.resetPage()
      this.clearHeaderOptions()
      this.clearTableHandleAll()
      this.clearTableSort()
      this.getBroadCastList(true)
    },
    /**
     * 过滤操作处理
     * @param {Object} value 过滤条件
     */
    filterHandler(value) {
      this.clearTableSort()
      this.clearTableHandleAll()
      const keys = Object.keys(value)
      const filterKey = keys[0]
      this.headerOptions = {
        type: headOptType.filter,
        key: filterKey,
        value: value[filterKey][0],
      }
      keys.forEach(key => {
        this.filterValue = {
          [key]: value[key],
        }
      })
      this.resetPage()
      this.getBroadCastList()
    },
    /**
     * 排序变化时触发的函数
     * @param {Object} param 排序参数
     */
    // 排序
    sortChange(param) {
      const { column, order } = param
      this.clearTableHandleAll()
      this.headerOptions = {
        type: headOptType.sort,
        key: column.property,
        value: order,
      }
      this.resetPage()
      this.getBroadCastList()
    },
    /**
     * 播放状态变化时触发的函数
     * @param {string} value 播放状态
     */
    playStatChange(value) {
      this.selectedPlayStat = value
      this.selectedPlayTime = []
      this.resetPage()
      this.clearHeaderOptions()
      this.getBroadCastList(true)
    },
    /**
     * 清除表格所有处理（筛选、排序等）
     */
    clearTableHandleAll() {
      this.$refs.table.clearAllFilter()
    },
    /**
     * 清除表格排序
     */
    clearTableSort() {
      this.$refs.table.clearSort()
    },
    /**
     * 取消云播放
     * @param {Object} row 行数据
     */
    // 取消云播放
    cancelPlay(row) {
      this.$refs.confirmPop.show({
        msg: '确认取消该广播？',
        okHandle: async () => {
          const [success] = await cancelSub({
            bookingId: row.key,
            version: row.version,
          })
          if (success) {
            this.$message.success({
              duration: 100,
              customClass: 'cust-message',
              message: '取消成功',
            }).showClose = true
            this.resetTable()
          } else {
            this.$message.error('取消失败').showClose = true
          }
        },
      })
    },
    /**
     * 格式化播放间隔
     * @param {string} code 播放间隔代码
     * @returns {string} 格式化后的播放间隔
     */
    // 格式化播放时间
    formatInterVal(code) {
      if (!code) {
        return '永不'
      }
      const codes = code.split(',')
      if (codes.length === 7) {
        return '永久'
      }
      return codes.map(item => ConstEnum.PERIOD_W[item].txt).join(',')
    },
    /**
     * 获取广播列表
     * @param {boolean} reloadHeader 是否重新加载表格头部
     */
    async getBroadCastList(reloadHeader = false) {
      this.stopMp3()
      let result = []
      const params = this.getTableParams()
      const { type, key, value } = this.headerOptions
      if (type === headOptType.sort && value) {
        params.orderColumn = key
        params.isAsc = value === 'ascending'
      }
      if (type === headOptType.filter && value) {
        params[toHump(key)] = value
      }
      const [success, data] = await getBroadcastList(params)
      if (success) {
        result = data.list
        this.total = data.total
      }
      this.meta = result.map(this.formatData(this.selectedPlayStat))
      if (reloadHeader) {
        await this.handleTableHead()
      }
    },
    /**
     * 获取表格查询参数
     * 根据当前选中的状态、广播类型、分页信息和设备码等条件，构造用于查询表格数据的参数对象
     * @returns {Object} 查询参数对象
     */
    getTableParams() {
      // 提取所有设备的设备码，以逗号分隔
      const deviceCodes = this.propData.devices.map(item => item.deviceCode)
      // 构造查询参数对象
      const params = {
        type: this.selectedPlayStat,
        broadType: this.selectedBroadCastType.join(','),
        pageSize: this.pageSize,
        pageNum: this.currentPage,
        deviceCode: deviceCodes.join(','),
      }
      // 如果选定了播放时间范围，则添加开始和结束时间到参数对象
      if (this.selectedPlayTime.length > 1) {
        Object.assign(params, {
          playStartTime: dayjs(this.selectedPlayTime[0]).format(
            ConstEnum.DATE_FORMAT.zero
          ),
          playEndTime: dayjs(this.selectedPlayTime[1]).format(
            ConstEnum.DATE_FORMAT.H24
          ),
        })
      }
      return params
    },
    /**
     * 处理表格头部，包括获取广播历史分组数据并根据数据初始化表格列属性
     * @async
     */
    async handleTableHead() {
      const params = this.getTableParams()
      // 异步获取广播历史分组数据，表格头部功能按钮
      const [groupSuccess, groupData] = await getBroadCastHistoryGroup(params)
      if (groupSuccess) {
        // 初始化选项列表和状态列表
        let optList = []
        let stateList = []
        // 遍历分组数据，根据分组列类型分别处理，构造选项或状态列表
        groupData.forEach(valGroup => {
          if (valGroup.groupColumn === 'broad_type') {
            optList = valGroup.columnList.map(val => ({
              value: val[Object.keys(val)[0]],
              text: Object.keys(val)[0],
            }))
          }
          if (valGroup.groupColumn === 'play_state') {
            stateList = valGroup.columnList.map(val => ({
              value: val[Object.keys(val)[0]],
              text: Object.keys(val)[0],
            }))
          }
        })
        // 根据选项或状态列表的长度，决定是否启用自定义排序，然后设置表格列的属性
        this.getBroadTypeColumnAttr(optList.length <= 2, optList)
        this.getPlayResColumnAttr(stateList.length <= 2, stateList)
      }
    },
    /**
     * 根据播放状态格式化数据
     * 根据不同的播放状态，返回一个函数，该函数用于格式化表格数据项
     * @param {number} playStat 播放状态码
     * @returns {Function} 格式化数据项的函数
     */
    formatData(playStat) {
      if (playStat === playStatCode.unplay) {
        // 未播放状态下的数据格式化函数
        return (item, index) => ({
          id: index,
          key: item.booking_id,
          playTimePeriod: item.play_periods,
          cycle: item.play_days,
          broadType: item.broad_type_str,
          broad_type: item.broad_type,
          audioUrl: item.audio_url,
          content: item.content,
          duration: item.duration,
          deviceCode: item.device_codes,
          materialId: item.material_id,
          version: item.version,
        })
      }
      // 已播放状态下的数据格式化函数
      return (item, index) => ({
        id: index,
        key: item.record_id,
        playRes: item.play_state_str,
        playResCode: item.play_state,
        play_state: item.play_state,
        broad_type: item.broad_type,
        broadType: item.broad_type_str,
        audioUrl: item.audio_url,
        content: item.content,
        deviceCode: item.device_codes,
        duration: item.duration,
        playTime: item.play_time,
        materialId: item.material_id,
        version: item.version,
      })
    },
    /**
     * 广播类型改变时的处理函数
     * 重置相关状态并刷新广播列表
     */
    broadCastTypeChange() {
      // 重置播放时间、分页信息、头部选项、表格全选状态和排序状态
      this.selectedPlayTime = []
      this.resetPage()
      this.clearHeaderOptions()
      this.clearTableHandleAll()
      this.clearTableSort()
      // 刷新广播列表
      this.getBroadCastList()
    },
    /**
     * 分页大小改变时的处理函数
     * 更新分页大小并刷新广播列表
     * @param {number} size 新的分页大小
     */
    handleSizeChange(size) {
      this.pageSize = size
      this.resetPage()
      this.getBroadCastList()
    },
    /**
     * 分页改变时的处理函数
     * 更新当前页码并刷新广播列表
     * @param {number} pageIndex 新的页码
     */
    handleCurrentChange(pageIndex) {
      this.currentPage = pageIndex
      this.getBroadCastList()
    },
    /**
     * 获取广播类型列表
     * 异步调用服务获取广播类型的字典数据，并更新广播类型选项列表
     */
    getBroadCastType() {
      commonService.getDictListByCatCode('BROADCAST_TYPE').then(res => {
        const { code, data } = res
        if (code === 200) {
          this.broadCastTypes = data?.map(item => ({
            label: item.codeName,
            value: item.code,
          }))
        }
      })
    },
    /**
     * 重置分页信息
     * 将当前页码重置为第1页
     */
    resetPage() {
      this.currentPage = 1
    },
    /**
     * 设置广播类型列的属性
     * 根据广播类型选项的数量，决定是否启用自定义排序，然后设置列的过滤器属性
     * @param {boolean} sorted 是否启用自定义排序
     * @param {Array} filters 列的过滤器选项列表
     */
    getBroadTypeColumnAttr(sorted, filters) {
      const _this = this
      if (sorted) {
        _this.broadTypeColumnAttr = {
          sortable: 'custom',
        }
        return
      }
      _this.broadTypeColumnAttr = {
        filterMultiple: false,
        filters,
      }
    },
    /**
     * 设置播放结果列的属性
     * 根据播放结果选项的数量，决定是否启用自定义排序，然后设置列的过滤器属性
     * @param {boolean} sorted 是否启用自定义排序
     * @param {Array} filters 列的过滤器选项列表
     */
    getPlayResColumnAttr(sorted, filters) {
      const _this = this
      if (sorted) {
        _this.playResColumnAttr = {
          sortable: 'custom',
        }
        return
      }
      _this.playResColumnAttr = {
        filterMultiple: false,
        filters,
      }
    },
  },
}
</script>
<style lang='less' src='./index.less' />
