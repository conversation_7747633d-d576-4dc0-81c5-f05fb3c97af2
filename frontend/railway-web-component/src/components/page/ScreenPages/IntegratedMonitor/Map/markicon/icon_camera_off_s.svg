<?xml version="1.0" encoding="UTF-8"?>
<svg width="48px" height="63px" viewBox="0 0 60 79" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>icon_摄像机_离线_70_n</title>
    <defs>
        <linearGradient x1="50%" y1="0.258654101%" x2="50%" y2="98.890959%" id="linearGradient-1">
            <stop stop-color="#E3E3E3" stop-opacity="0.147385817" offset="0%"></stop>
            <stop stop-color="#D5D5D5" stop-opacity="0.5" offset="47.171522%"></stop>
            <stop stop-color="#F1F1F1" stop-opacity="0.5" offset="100%"></stop>
        </linearGradient>
        <filter x="-75.0%" y="-8.3%" width="250.0%" height="116.7%" filterUnits="objectBoundingBox" id="filter-2">
            <feGaussianBlur stdDeviation="1" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="96.7802461%" id="linearGradient-3">
            <stop stop-color="#D5D5D5" stop-opacity="0.8" offset="0%"></stop>
            <stop stop-color="#BEBEBE" stop-opacity="0.8" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="97.6304764%" x2="50%" y2="-5.11180773%" id="linearGradient-4">
            <stop stop-color="#D5D5D5" offset="0%"></stop>
            <stop stop-color="#D5D5D5" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <ellipse id="path-5" cx="26" cy="67" rx="7" ry="3"></ellipse>
        <filter x="-53.6%" y="-91.7%" width="207.1%" height="350.0%" filterUnits="objectBoundingBox" id="filter-6">
            <feMorphology radius="0.5" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
            <feOffset dx="0" dy="2" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.3 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-7">
            <stop stop-color="#D5D5D5" stop-opacity="0.4" offset="0%"></stop>
            <stop stop-color="#D5D5D5" offset="100%"></stop>
        </linearGradient>
        <circle id="path-8" cx="26" cy="26" r="26"></circle>
        <filter x="-13.5%" y="-9.6%" width="126.9%" height="126.9%" filterUnits="objectBoundingBox" id="filter-9">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.835294118   0 0 0 0 0.835294118   0 0 0 0 0.835294118  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="5.08795696%" x2="50%" y2="100%" id="linearGradient-10">
            <stop stop-color="#D5D5D5" offset="0%"></stop>
            <stop stop-color="#D5D5D5" stop-opacity="0.276665961" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-11">
            <stop stop-color="#FFFFFF" stop-opacity="0.167855216" offset="0%"></stop>
            <stop stop-color="#D5D5D5" offset="100%"></stop>
        </linearGradient>
        <circle id="path-12" cx="26" cy="26" r="23"></circle>
        <filter x="-17.4%" y="-13.0%" width="134.8%" height="134.8%" filterUnits="objectBoundingBox" id="filter-13">
            <feMorphology radius="1" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
            <feOffset dx="0" dy="2" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.5 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-14">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#424242" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="44.4796397%" y1="17.8031512%" x2="44.4796397%" y2="78.3504117%" id="linearGradient-15">
            <stop stop-color="#D5D5D5" stop-opacity="0.2" offset="0%"></stop>
            <stop stop-color="#D5D5D5" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="综合监测-大屏" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="1-6综合监测-弹框" transform="translate(-1209.000000, -912.000000)">
            <g id="icon_摄像机_离线_70_n" transform="translate(1213.000000, 914.000000)">
                <rect id="矩形" fill="url(#linearGradient-1)" filter="url(#filter-2)" x="24" y="31" width="4" height="36" rx="1.5"></rect>
                <g id="椭圆形">
                    <use fill="black" fill-opacity="1" filter="url(#filter-6)" xlink:href="#path-5"></use>
                    <use stroke="url(#linearGradient-4)" stroke-width="1" fill-opacity="0.8" fill="url(#linearGradient-3)" fill-rule="evenodd" xlink:href="#path-5"></use>
                </g>
                <g id="编组-9">
                    <g id="椭圆形" opacity="0.469350179">
                        <use fill="black" fill-opacity="1" filter="url(#filter-9)" xlink:href="#path-8"></use>
                        <use fill="url(#linearGradient-7)" fill-rule="evenodd" xlink:href="#path-8"></use>
                    </g>
                    <g id="椭圆形">
                        <use fill="black" fill-opacity="1" filter="url(#filter-13)" xlink:href="#path-12"></use>
                        <use stroke="url(#linearGradient-11)" stroke-width="2" fill="url(#linearGradient-10)" fill-rule="evenodd" xlink:href="#path-12"></use>
                    </g>
                    <rect id="矩形" stroke="url(#linearGradient-15)" fill="url(#linearGradient-14)" x="6" y="6" width="40" height="40" rx="20"></rect>
                </g>
                <path d="M18.912733,29.492 L18.912733,26.792 L17,26.4307143 L17,35 L18.912733,34.2684286 L18.912733,31.3292857 L21.9489395,31.3292857 L23.2986503,29.1242857 L21.6108691,28.0224286 L20.8216096,29.492 L18.912733,29.492 Z M35,26.792 L21.2740841,17 L19.7019924,17 L17.5643076,20.6745714 L17.5643076,21.8985714 L29.8209669,30.4717143 L30.8338927,30.3431428 L35,26.792 Z M17.5591659,22.634 L17.5591659,24.5857143 L29.9340856,33.0367143 L31.0588445,33.0367143 L31.6218667,32.4285714 L32.1848889,30.5912857 C32.1848889,30.5912857 30.6102264,31.8165714 30.4971078,31.8165714 C29.935371,31.3292857 17.5617368,22.634 17.5617368,22.634 L17.5591659,22.634 Z" id="icon_摄像机_18_n" fill="#FFFFFF" fill-rule="nonzero"></path>
            </g>
        </g>
    </g>
</svg>