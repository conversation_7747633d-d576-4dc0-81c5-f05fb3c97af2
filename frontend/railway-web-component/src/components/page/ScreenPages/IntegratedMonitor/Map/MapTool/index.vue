<!--
 * @Description:
 * @Autor: he.chao
 * @Date: 2022-10-19 15:07:55
 * @LastEditors: liu.yongli
 * @LastEditTime: 2025-02-21 12:05:52
-->
<template>
  <!-- 地图控制组件 -->
  <div
    class="map-control-tool"
    :style="{
      right: useType === 'eventFile' ? pxToRem(80) : useType === 'fullView' ? pxToRem(55) : pxToRem(390),
      bottom: useType === 'eventFile' ? pxToRem(100) : useType === 'fullView' ? pxToRem(22) : pxToRem(22)
    }"
  >
    <div class="map-control-tool-main">
      <!-- 缩放控制工具 -->
      <div class="zoom-control-tool">
        <!-- 显示当前缩放级别 -->
        <div class="button">{{ zoomLevel }}</div>
        <!-- 放大按钮 -->
        <div class="button" @click="zoomMap('zoomIn')">
          <img alt="放大" :src="require('@/assets/images/home/<USER>')" />
        </div>
        <!-- 缩小按钮 -->
        <div class="button" @click="zoomMap('zoomOut')">
          <img alt="缩小" :src="require('@/assets/images/home/<USER>')" />
        </div>
      </div>
      <!-- 比例尺工具 -->
      <div class="scale-tool">
        <div class="scale-container">
          <div class="left"></div>
          <div class="line"></div>
          <div class="right"></div>
        </div>
        <!-- 显示当前比例尺 -->
        <div class="scale-info">{{ scaleValue }}</div>
      </div>
      <!-- 地图切换工具 -->
      <div class="map-switch-tool">
        <div
          class="map-switch-tool-inner"
          :class="{ 'map-switch-third': this.$store.state.map.mapType == 2,'map-switch-second': this.$store.state.map.mapType == 1, }"
        >
          <!-- 常规地图选项 -->
          <div
            class="map-item"
            :class="{ selected: this.$store.state.map.mapType == 0, }"
            @click="changeMapType(0)"
          >
            <img alt="常规地图" class="img-style" :src="require('./img/2D-map.jpeg')" />
            <div class="text-style">常规地图</div>
          </div>
          <!-- 深色地图选项 -->
          <div
            class="map-item"
            :class="{ selected: this.$store.state.map.mapType == 2, }"
            @click="changeMapType(2)"
          >
            <img alt="深色地图" class="img-style" :src="require('./img/2D-dark-map.png')" />
            <div class="text-style">深色地图</div>
          </div>
          <!-- 卫星地图选项 -->
          <div
            class="map-item"
            :class="{ selected: this.$store.state.map.mapType == 1, }"
            @click="changeMapType(1)"
          >
            <img alt="卫星地图" class="img-style" :src="require('./img/satellite-map.jpeg')" />
            <div class="text-style">卫星地图</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { Events } from '@/components/page/ScreenPages/IntegratedMonitor/enum/EventsEnum'
import CTMapOl from '@ct/ct_map_ol'
import { mapGetters, mapMutations } from 'vuex'
let _map = null

export default {
  inject: ['mapRef'],
  props: {
    useType: {
      type: String,
      default: () => {
        return ''
      },
    },
    mapId: {
      type: String,
    },
  },
  data: function () {
    return {
      zoomLevel: '-', // 当前缩放级别
      scaleValue: '50公里', // 当前比例尺
    }
  },
  mounted() {
    // 监听地图移动结束事件，更新比例尺
    _map = this.mapRef.getMapRef(this.mapId).mapRef.mapInstance
    _map.on('moveend', () => this.getScaleNew())
    this.getScaleNew() // 初始化比例尺
  },
  beforeDestroy() {
    // 组件销毁前移除事件监听
    _map.un('moveend', () => this.getScaleNew())
  },
  computed: {
    ...mapGetters('map', ['getLocationLnglat']), // 从Vuex获取当前位置经纬度
  },
  methods: {
    ...mapMutations('map', [
      'setMapType', // 设置地图类型
      'setPickLnglat', // 设置选择的经纬度
      'setLocationLnglat', // 设置当前位置的经纬度
      'setLookHere', // 设置是否聚焦到某个位置
      'setAlarmFilter', // 设置报警过滤条件
    ]),
    /**
     * 根据给定的键调整地图的缩放级别。
     * @param {string} key - 表示缩放操作的键，可以是'zoomIn'（放大）或其它（缩小）。
     */
    zoomMap(key) {
      if (!_map) {
        return
      }
      if (key === 'zoomIn') {
        CTMapOl.api.fixZoomin(_map)
      } else {
        CTMapOl.api.fixZoomout(_map)
      }
    },
    /**
     * 计算并更新当前地图的刻度值。
     */
    getScaleNew() {
      // 获取地图层级
      this.zoomLevel = Math.round(_map.getView().getZoom())
      // 默认dpi
      const DEFAULT_DPI = 25.4 / 0.28
      // 每米多少英寸
      const inchesPerMeter = 1000 / 25.4
      let currentScale = 1
      // 如果是度分秒的话需要转换为米
      if (_map.getView().getProjection().getUnits() != 'metric') {
        currentScale =
          _map.getView().getResolution() *
          _map.getView().getProjection().getMetersPerUnit() *
          inchesPerMeter *
          DEFAULT_DPI
      } else {
        currentScale =
          _map.getView().getResolution() * inchesPerMeter * DEFAULT_DPI
      }
      const scaleNum = Math.round(currentScale / 100)
      if (scaleNum < 1000) {
        this.scaleValue = (scaleNum / 1).toFixed(0) + '米'
      } else {
        this.scaleValue = (scaleNum / 1000).toFixed(1) + '公里'
      }
    },
    /**
     * 改变地图类型。
     * @param {any} newval - 新的地图类型。
     */
    changeMapType(newval) {
      this.setMapType(newval)
      this.$EventBus.$emit(Events.MAP_TOOL_EVT.MAP_LAYER_EXCHANGE, newval)
    },
  },
}
</script>
<style lang='scss' src='./index.scss' />
