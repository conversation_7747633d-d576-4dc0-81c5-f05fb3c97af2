<?xml version="1.0" encoding="UTF-8"?>
<svg width="48px" height="63px" viewBox="0 0 48 63" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>icon_云广播_播放中_56_n</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="98.890959%" id="linearGradient-1">
            <stop stop-color="#1BF98E" stop-opacity="0.147385817" offset="0%"></stop>
            <stop stop-color="#29D980" stop-opacity="0.623991853" offset="45.4628257%"></stop>
            <stop stop-color="#90FFD6" stop-opacity="0.663027036" offset="100%"></stop>
        </linearGradient>
        <filter x="-74.3%" y="-8.4%" width="248.6%" height="116.7%" filterUnits="objectBoundingBox" id="filter-2">
            <feGaussianBlur stdDeviation="0.8" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="93.6372478%" id="linearGradient-3">
            <stop stop-color="#3AE58B" stop-opacity="0.37038523" offset="0%"></stop>
            <stop stop-color="#0AE0E7" stop-opacity="0.8" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="-3.1396872%" id="linearGradient-4">
            <stop stop-color="#3FDD6B" offset="0%"></stop>
            <stop stop-color="#AB9B19" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <ellipse id="path-5" cx="20.8205128" cy="53.6" rx="5.5" ry="2.4"></ellipse>
        <filter x="-53.6%" y="-81.3%" width="207.3%" height="345.8%" filterUnits="objectBoundingBox" id="filter-6">
            <feMorphology radius="0.4" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
            <feOffset dx="0" dy="2" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.3 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-7">
            <stop stop-color="#3ACFE5" stop-opacity="0.4" offset="0%"></stop>
            <stop stop-color="#00313F" offset="100%"></stop>
        </linearGradient>
        <ellipse id="path-8" cx="21" cy="20.7257143" rx="21" ry="20.7257143"></ellipse>
        <filter x="-13.1%" y="-8.4%" width="126.2%" height="126.5%" filterUnits="objectBoundingBox" id="filter-9">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.22745098   0 0 0 0 0.806306856   0 0 0 0 0.898039216  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="5.08795696%" x2="50%" y2="100%" id="linearGradient-10">
            <stop stop-color="#3AE57B" offset="0%"></stop>
            <stop stop-color="#3AE568" stop-opacity="0.276665961" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-11">
            <stop stop-color="#FFFFFF" stop-opacity="0.167855216" offset="0%"></stop>
            <stop stop-color="#3ACAE5" offset="100%"></stop>
        </linearGradient>
        <ellipse id="path-12" cx="21" cy="20.7257143" rx="18.5769231" ry="18.3342857"></ellipse>
        <filter x="-17.0%" y="-11.7%" width="133.9%" height="134.4%" filterUnits="objectBoundingBox" id="filter-13">
            <feMorphology radius="0.8" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
            <feOffset dx="0" dy="2" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.5 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="1.41712536%" x2="50%" y2="96.2653678%" id="linearGradient-14">
            <stop stop-color="#21993D" offset="0%"></stop>
            <stop stop-color="#32D785" offset="62.4393063%"></stop>
            <stop stop-color="#32D2D6" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="44.4796397%" y1="17.8031512%" x2="44.4796397%" y2="78.3504117%" id="linearGradient-15">
            <stop stop-color="#E8F3FE" stop-opacity="0.2" offset="0%"></stop>
            <stop stop-color="#3AE5DC" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="综合监测-大屏" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="icon_云广播_播放中_56_n" transform="translate(3.000000, 1.000000)">
            <rect id="矩形" fill="url(#linearGradient-1)" filter="url(#filter-2)" x="19.3846154" y="24.7114286" width="3.23076923" height="28.6971429" rx="1.2"></rect>
            <g id="椭圆形">
                <use fill="black" fill-opacity="1" filter="url(#filter-6)" xlink:href="#path-5"></use>
                <use stroke="url(#linearGradient-4)" stroke-width="0.8" fill-opacity="0.8" fill="url(#linearGradient-3)" fill-rule="evenodd" xlink:href="#path-5"></use>
            </g>
            <g id="椭圆形" opacity="0.469350179">
                <use fill="black" fill-opacity="1" filter="url(#filter-9)" xlink:href="#path-8"></use>
                <use fill="url(#linearGradient-7)" fill-rule="evenodd" xlink:href="#path-8"></use>
            </g>
            <g id="椭圆形">
                <use fill="black" fill-opacity="1" filter="url(#filter-13)" xlink:href="#path-12"></use>
                <use stroke="url(#linearGradient-11)" stroke-width="1.6" fill="url(#linearGradient-10)" fill-rule="evenodd" xlink:href="#path-12"></use>
            </g>
            <rect id="矩形" stroke="url(#linearGradient-15)" stroke-width="0.8" fill="url(#linearGradient-14)" x="5" y="5" width="32.3076923" height="31.8857143" rx="15.9428571"></rect>
            <path d="M26.3813494,12.5228127 L26.5780701,12.7341566 L26.6854771,12.8563953 C27.349258,13.625288 27.9461175,14.5997702 28.3957,15.7150432 C29.7001148,18.9441965 29.2790222,22.1331225 27.4577779,22.8360027 L27.2817059,22.8923033 C27.0815541,22.9492956 26.8733911,22.9750192 26.6605016,22.9710802 C26.3472725,22.9912119 25.9926724,22.9929969 25.6381261,22.9886715 L24.4632143,22.9680132 L24.4632143,22.9680132 L24.0576233,22.9727755 C23.9545032,22.9819288 23.8431181,22.9674557 23.7254191,22.9316642 C22.7113116,22.9673034 21.7453325,23.060793 20.819011,23.1952162 C21.7217704,24.3413306 22.5488854,25.5975727 22.8999697,26.5445581 L22.9844883,26.7929683 C23.1140682,27.1188821 23.2004791,27.4213638 23.2311448,27.6888751 L23.2780709,28.1492319 L23.3052693,28.5456577 C23.3567436,29.6441805 23.1347513,29.9490466 22.5750135,30.0849451 L22.375837,30.1248434 L22.1511133,30.1573672 L21.8299075,30.1896542 C20.9891469,30.2488095 20.2957091,30.0725232 20.2540237,29.1833756 L20.2535787,29.0178381 C20.2665138,28.6546941 20.2035008,28.0599938 20.0666557,27.3654876 L20.007505,27.4021644 C19.9887683,27.1517582 19.9352425,26.7966247 19.8370891,26.3788276 C19.6361734,25.628704 19.3645229,24.840757 19.0237936,24.1403949 C18.9271283,23.9539045 18.8233466,23.7662525 18.7109716,23.581035 L18.1496115,23.7078375 C17.2954591,23.3223871 16.3395863,22.1499448 15.7150529,20.6052173 C15.0927632,19.0604899 14.9723442,17.5669608 15.3253739,16.7156053 C16.9632519,15.8100557 18.6746066,14.677737 20.3506203,13.2047266 C20.3734902,13.1767977 20.3972663,13.1542951 20.4218662,13.1353776 L21.0951355,12.4690075 C21.2950929,12.2730223 21.5107829,12.0664069 21.7242113,11.8748434 L21.9582246,11.6684154 L21.9798341,11.646237 C22.1486001,11.4692725 22.3473547,11.3228498 22.5668715,11.2131772 L22.7360057,11.1379105 C23.8339808,10.7146054 25.1912938,11.2908269 26.3813494,12.5228127 Z M14.4861804,17.1661508 C14.2797478,18.1923121 14.4293367,19.5380974 14.9768319,20.8926595 C15.525075,22.2486845 16.3552931,23.3311639 17.2229085,23.9433499 L16.6574626,24.0954822 C15.6178201,24.4962921 14.1742876,23.3289697 13.4323269,21.4880232 C12.6888703,19.6470767 12.9282125,17.8302666 13.9663592,17.4294566 L14.4861804,17.1661508 Z M23.041167,11.8949147 C22.0060121,12.2935304 21.8152863,15.4349151 22.799581,17.9780104 C23.8272564,20.6308165 26.1039988,22.4841969 27.1511208,22.0797299 C28.5737108,21.5311761 28.8055735,18.8052295 27.670942,15.9944399 C26.5355626,13.1821876 24.4622611,11.3463609 23.041167,11.8949147 Z M23.6627087,15.801349 C24.5878912,15.8023233 25.3664583,16.4791035 25.4758851,17.3774776 C25.5853118,18.2758517 24.9910921,19.1125313 24.0920287,19.3259899 C23.7292828,18.8399655 23.4347181,18.3086331 23.2161859,17.746155 C23.0046627,17.1902186 22.8595952,16.6122262 22.7838742,16.0236962 C23.0449067,15.8803408 23.3448324,15.801349 23.6627087,15.801349 Z" id="icon_云广播_24_n" fill="#FFFFFF"></path>
        </g>
    </g>
</svg>