import { getMetaByDicCode } from '@/api/service/imScreenService';
import { isNotEmpty } from '@/components/common/utils';
import {
  enums
} from '@/components/page/ScreenPages/IntegratedMonitor/DetailInfo/enums';
import vue from '@/main';
import LayerConst
  from '@/components/page/ScreenPages/IntegratedMonitor/Map/LayerConst';
import {
  baseMarker, calMakerStyle, closeLeftSideWindow, closeLeftSideWindowByIdPrefix,
  clusterTip, openLeftSideWidow
} from '@/components/page/ScreenPages/IntegratedMonitor/Map/MapLayerHandle/CommonHandle';
import {
  broadcastIcon
} from '@/components/page/ScreenPages/IntegratedMonitor/Map/marker';
import { createSource, useClusterLayer } from '@/utils/map3.0';
import LoudSpeakerPlayingBg from '@/components/page/ScreenPages/IntegratedMonitor/InfoWindow/LoudSpeakerPlayingBg.vue';
import CommonMap from '@/components/common/Map/CommonMap';
import {
  transformPointToMercator,
} from '@/utils/index.js'

const LOUDSPEAKER = {
  largeAnchor: [30, 70], // 大图标的锚点位置
  smallAnchor: [25, 55], // 小图标的锚点位置
  cfg: {
    showFault: false, // 是否显示故障标记
    showLoudSpeakerPlayingBg: true, // 是否显示扬声器播放背景
    loudSpeakerPlayingBgIds: [], // 播放背景的ID集合
    loudSpeakerPlayingBgOverlays: {}, // 播放背景的覆盖物集合
  },
  /**
   * 获取原始选中项的ID
   *
   * @param refresh 是否刷新
   * @returns 原始选中项的ID
   */
  getOriSelectId(refresh) {
    let oriSelectId = null
    if (refresh) {
      const oriMarkers = this.getMarker();
      oriSelectId = oriMarkers.find(item => item.values_.attributes.selected)?.values_.attributes.ds.deviceCode;
    }
    return oriSelectId;
  },
  /**
   * 绘制地图图标
   * @param markersLayer
   * @param onclick
   * @param initData
   * @returns {{markersLayer: CTMapOl.layer.Vector, markers: any[]}}
   */
  async draw({
    onclick = () => {
      console.log('default implement'); // 默认点击事件处理
    },
    initData = null, // 初始化数据
    region = {}, // 区域信息
    refresh = false,
    mapInstance // 地图实例
  }) {
    const layerData = await this.getData(initData, region);  // 获取图层数据
    // 正在播放中的
    const playingData = layerData.filter(device => device.deviceStatus === enums.LOUDSPEAKER.deviceStatus.playing.code)
    let oriSelectId = this.getOriSelectId(refresh);

    const markers = []; // 存储正常状态的标记
    const faultMarkers = []; // 存储故障状态的标记
    layerData.forEach((item, index) => {
      // 转座标
      const point = transformPointToMercator([Number(item.longitude), Number(item.latitude)]);

      // 格式化部分属性
      const attrs = {
        lng: point[0],
        lat: point[1],
        deviceCode: item.deviceCode,
        deviceName: item.deviceName,
        location: item.location,
        deviceStatus: item.deviceStatus,
        height: item.height,
        ...item,
      };
      const [small, large] = this.getIcon(attrs); // 获取设备图标
      const isSelected = oriSelectId && oriSelectId === item.deviceCode
      const marker = baseMarker({
        point: [attrs.lng, attrs.lat], // 标记位置
        markerAttr: {
          markerType: LayerConst.LOUDSPEAKER, // 标记类型
          ds: attrs, // 标记属性
          selected: isSelected
        },
        initIcon: isSelected ? large : small, // 初始图标
        selectedIcon: large, // 选中图标
        anchorCfg: this, // 锚点配置
        onclick: (attr, markerFeature, feature) => {
          onclick(attr, markerFeature); // 执行点击事件
          const {
            ds,
            selected
          } = attr;
          const markerId = `${LayerConst.LOUDSPEAKER}-${item.deviceStatus}-${ds.deviceCode}`; // 构造标记ID
          if (selected) {
            // 必须在清除窗体之后，否则窗体关闭会出问题
            // 窗体关闭会回调onclose方法（leftWrap.closeAllContent()）
            this.onSelected(marker); // 处理选中状态
            this.openDetail(item, (key, param) => {
              if (param && param.destroy) {
                return;
              }
              this.cancelSelected(); // 取消选中状态
            });
            return;
          }
          closeLeftSideWindow(markerId, LayerConst.LOUDSPEAKER); // 关闭左侧窗口
        }
      });
      // 分组故障状态的marker
      if ([
        enums.LOUDSPEAKER.deviceStatus.fault.code,
        enums.LOUDSPEAKER.deviceStatus.offline.code
      ].includes(item.deviceStatus)) {
        faultMarkers.push(marker); // 故障标记
        return;
      }
      markers.push(marker); // 正常标记
    });

    this.cfg.faultMarkers = faultMarkers; // 更新故障标记配置
    this.cfg.otherMarkers = markers; // 更新其他标记配置
    const showMarkers = this.getMarker(); // 获取显示的标记
    if (!refresh) {
      this.cfg.markersLayer = useClusterLayer({
        markers: showMarkers, // 标记集合
        map: mapInstance, // 地图实例
        type: LayerConst.LOUDSPEAKER, // 标记类型
        onclick: this.onClusterClick // 集群点击事件处理
      });
    }
    // 渲染完了以后渲染播放中的动效
    this.renderPlayingLoudSpeaker(playingData); // 渲染播放中的动效
    return {
      markersLayer: this.cfg.markersLayer, // 返回标记图层
      markers // 返回标记集合
    };
  },
  /**
   * 获取数据
   *
   * @param {any} initData 初始数据
   * @param {any} region 数据
   * @returns {*} 返回数据
   */
  async getData(initData, region) {
    let layerData = initData; // 初始化图层数据
    if (!layerData) {
      // 获取图层数据
      const [success, data] = await getMetaByDicCode({
        layerCode: LayerConst.LOUDSPEAKER,
        checkMode: '1', ...region
      });
      if (success) {
        layerData = data;
      }
    }
    if (!layerData || layerData.length < 1) {
      return [];
    }
    return layerData;
  },
  /**
   * 广播播放中/结束播放图标切换
   */
  afterPlayingChangeIcon(playingDeviceCodes = [], endPlayingDeviceCodes = []) {
    const markers = this.getMarker(); // 获取标记集合
    playingDeviceCodes.forEach(deviceCode => {
      const marker = markers.find(item => item.values_.attributes.ds.deviceCode === deviceCode); // 查找播放中的设备
      const selected = marker.values_.attributes.selected; // 获取选中状态
      const icon = selected ? broadcastIcon.iconBroadcastPlayingL : broadcastIcon.iconBroadcastPlayingS; // 根据选中状态选择图标
      const anchor = selected ? this.largeAnchor : this.smallAnchor; // 根据选中状态选择锚点
      marker.style_ = calMakerStyle(icon, anchor); // 更新标记样式
    });
    endPlayingDeviceCodes.forEach(deviceCode => {
      const marker = markers.find(item => item.values_.attributes.ds.deviceCode === deviceCode); // 查找结束播放的设备
      const selected = marker.values_.attributes.selected; // 获取选中状态
      const icon = selected ? broadcastIcon.iconBroadcastOnL : broadcastIcon.iconBroadcastOnS; // 根据选中状态选择图标
      const anchor = selected ? this.largeAnchor : this.smallAnchor; // 根据选中状态选择锚点
      marker.style_ = calMakerStyle(icon, anchor); // 更新标记样式
    });
    // 更新标记图层的源数据
    this.cfg.markersLayer.setSource(createSource({
      markers, // 更新标记集合
      type: LayerConst.LOUDSPEAKER, // 标记类型
      onclick: this.onClusterClick // 集群点击事件处理
    }));
  },
  /**
   * 渲染正在播放的扬声器动效
   *
   * @param {Array} devices 设备列表
   */
  renderPlayingLoudSpeaker(devices) {
    return; // 设计要求不显示播放动效，但是代码先保留
    const markers = this.getMarker();
    // 选中的设备
    const selectedDeviceCodes = markers.filter(marker => marker.values_.attributes.selected).map(marker => marker.values_.attributes.ds.deviceCode);
    // 移除所有播放动效
    this.cfg.loudSpeakerPlayingBgIds.forEach(id => {
      const content = document.getElementById(id);
      content && content.remove();
    })
    this.cfg.loudSpeakerPlayingBgIds = [];
    this.cfg.loudSpeakerPlayingBgOverlays = {};
    // 渲染新的
    devices.forEach(device => {
      const { deviceCode, longitude, latitude } = device;
      const playingBgId = `LoudSpeakerPlayingBg_${deviceCode}`;
      this.cfg.loudSpeakerPlayingBgIds.push(playingBgId);
      // 渲染动效
      const offsetX = selectedDeviceCodes.includes(deviceCode) ? 25 : 20;
      const offsetY = selectedDeviceCodes.includes(deviceCode) ? -45 : -35;
      const overlayIns = CommonMap.infoWindow(longitude * 1, latitude * 1,
        CommonMap.componentToHtml({
          component: LoudSpeakerPlayingBg,
          props: {},
          onClose: () => {
            return null;
          }
        }), 'popup',
        offsetX, offsetY, playingBgId, vue.$store.state.map.mapStoreRef.mapInstance,
        false, 'mainMap', () => {
          console.log('default implement');
        }, false);
      this.cfg.loudSpeakerPlayingBgOverlays[playingBgId] = overlayIns;
    });
  },
  /**
   * 渲染正在播放的扬声器背景图层
   *
   * @param {Array} devices 设备列表
   */
  renderPlayingLoudSpeakerBak(devices) {
    const markers = this.getMarker(); // 获取标记集合
    // 选中的设备
    const selectedDeviceCodes = markers.filter(marker => marker.values_.attributes.selected).map(marker => marker.values_.attributes.ds.deviceCode);
    const playingDeviceCodes = []; // 播放中的设备代码集合
    const endPlayingDeviceCodes = []; // 结束播放的设备代码集合
    // 首先移除不在播放的设备
    const newPlayingBgIds = devices.map(i => `LoudSpeakerPlayingBg_${i.deviceCode}`); // 新的播放背景ID集合
    const setNewPlayingBgIds = new Set(newPlayingBgIds); // 转换为集合
    const removeIds = this.cfg.loudSpeakerPlayingBgIds.filter(id => !setNewPlayingBgIds.has(id)); // 需要移除的ID集合
    removeIds.forEach(id => {
      const content = document.getElementById(id);
      content && content.remove();
      // 设备code
      endPlayingDeviceCodes.push(id.replace('LoudSpeakerPlayingBg_', ''));
      delete this.cfg.loudSpeakerPlayingBgOverlays[id];
    });
    this.cfg.loudSpeakerPlayingBgIds = this.cfg.loudSpeakerPlayingBgIds.filter(id => setNewPlayingBgIds.has(id));
    // 渲染新的
    devices.forEach(device => {
      const { deviceCode, longitude, latitude } = device;
      const playingBgId = `LoudSpeakerPlayingBg_${deviceCode}`;
      // 如果地图上有了就不渲染了
      if (!this.cfg.loudSpeakerPlayingBgIds.includes(playingBgId)) {
        this.cfg.loudSpeakerPlayingBgIds.push(playingBgId);
        playingDeviceCodes.push(deviceCode);
        // 渲染动效
        const offsetX = selectedDeviceCodes.includes(deviceCode) ? 25 : 20;
        const offsetY = selectedDeviceCodes.includes(deviceCode) ? -45 : -35;
        const overlayIns = CommonMap.infoWindow(longitude * 1, latitude * 1,
          CommonMap.componentToHtml({
            component: LoudSpeakerPlayingBg,
            props: {},
            onClose: () => {
              return null;
            }
          }), 'popup',
          offsetX, offsetY, playingBgId, vue.$store.state.map.mapStoreRef.mapInstance,
          false, 'mainMap', () => {
            console.log('default implement');
          }, false);
        this.cfg.loudSpeakerPlayingBgOverlays[playingBgId] = overlayIns;
      }
    });
    this.afterPlayingChangeIcon(playingDeviceCodes, endPlayingDeviceCodes)
  },
  /**
   * 隐藏所有正在播放的背景图
   */
  hiddenAllPlayingBg() {
    // 本身就隐藏了
    if (!this.cfg.showLoudSpeakerPlayingBg) {
      return;
    }
    Object.keys(this.cfg.loudSpeakerPlayingBgOverlays).forEach(id => {
      this.cfg.loudSpeakerPlayingBgOverlays[id].getElement().style.display = 'none';
    });
    this.cfg.showLoudSpeakerPlayingBg = false;
  },
  /**
   * 显示所有正在播放的背景
   */
  showAllPlayingBg() {
    // 本身就显示了
    if (this.cfg.showLoudSpeakerPlayingBg) {
      return;
    }
    Object.keys(this.cfg.loudSpeakerPlayingBgOverlays).forEach(id => {
      this.cfg.loudSpeakerPlayingBgOverlays[id].getElement().style.display = 'block';
    });
    this.cfg.showLoudSpeakerPlayingBg = true;
  },
  removeAllPlayingBg() {
    this.cfg.loudSpeakerPlayingBgIds.forEach(id => {
      const content = document.getElementById(id);
      content && content.remove();
      delete this.cfg.loudSpeakerPlayingBgOverlays[id];
    });
    this.cfg.loudSpeakerPlayingBgIds = [];
  },
  /**
   * 当标记被选中时触发的函数。
   * @param {Object} marker 被选中的标记对象。
   */
  onSelected(marker) {
    // 根据设备状态获取大图标
    const [, large] = this.getIcon(marker.values_.attributes.ds);
    // 获取所有标记
    const markers = this.getMarker();
    // 查找被选中的标记
    const selectedMarker = markers.find(
      item => item.values_.attributes.ds.deviceCode ===
        marker.values_.attributes.ds.deviceCode);
    // 设置选中标记的样式
    selectedMarker.style_ = calMakerStyle(large, this.largeAnchor);
    // 标记为已选中
    selectedMarker.values_.attributes.selected = true;
    // 更新标记图层的源数据
    this.cfg.markersLayer.setSource(createSource({
      markers,
      type: LayerConst.LOUDSPEAKER,
      onclick: this.onClusterClick
    }));
    const { deviceCode } = selectedMarker.values_.attributes.ds;
    // 调整播放动效的偏移
    if (this.cfg.loudSpeakerPlayingBgOverlays[`LoudSpeakerPlayingBg_${deviceCode}`]) {
      this.cfg.loudSpeakerPlayingBgOverlays[`LoudSpeakerPlayingBg_${deviceCode}`].setOffset([25, -45]);
    }
  },

  /**
   * 取消选中状态的函数。
   */
  cancelSelected() {
    const markers = this.getMarker();
    // 如果没有标记则返回
    if (!isNotEmpty(markers)) {
      return;
    }
    // 重新初始化标记样式
    this.initMarkers(markers);
    // 更新标记图层的源数据
    this.cfg.markersLayer.setSource(createSource({
      markers,
      type: LayerConst.LOUDSPEAKER,
      onclick: this.onClusterClick
    }));
  }, // 初始化marker点

  /**
   * 初始化标记的函数。
   * @param {Array} markers 标记数组。
   */
  initMarkers(markers) {
    // 遍历所有标记，设置样式和选中状态
    markers.forEach(marker => {
      const [small] = this.getIcon(marker.values_.attributes.ds);
      const { deviceCode } = marker.values_.attributes.ds;
      // 调整播放动效的偏移
      if (this.cfg.loudSpeakerPlayingBgOverlays[`LoudSpeakerPlayingBg_${deviceCode}`]) {
        this.cfg.loudSpeakerPlayingBgOverlays[`LoudSpeakerPlayingBg_${deviceCode}`].setOffset([20, -35]);
      }
      // 设置为未选中
      marker.values_.attributes.selected = false;
      // 设置标记样式
      marker.style_ = calMakerStyle(small, this.smallAnchor);
    });
  },

  /**
   * 获取标记数组的函数。
   * @returns {Array} 标记数组。
   */
  getMarker() {
    // 根据显示故障的配置，返回相应的标记数组
    const {
      faultMarkers,
      otherMarkers,
      showFault
    } = this.cfg;
    if (showFault) {
      return [...faultMarkers, ...otherMarkers];
    }
    return otherMarkers;
  },

  /**
   * 清除所有标记的函数。
   */
  clear() {
    // 关闭所有LOUDSPEAKER类型的左侧窗口
    closeLeftSideWindowByIdPrefix(`${LayerConst.LOUDSPEAKER}-`,
      LayerConst.LOUDSPEAKER);
  },

  /**
   * 根据设备状态获取图标的函数。
   * @param {Object} data 设备状态数据。
   * @returns {Array} 图标URL数组。
   */
  getIcon(data) {
    // 根据设备状态返回相应的图标
    const { deviceStatus } = data;
    if (deviceStatus === enums.LOUDSPEAKER.deviceStatus.idle.code) {
      return [
        broadcastIcon.iconBroadcastOnS, broadcastIcon.iconBroadcastOnL
      ];
    }
    if (deviceStatus === enums.LOUDSPEAKER.deviceStatus.playing.code) {
      return [
        broadcastIcon.iconBroadcastPlayingS, broadcastIcon.iconBroadcastPlayingL
      ];
    }
    if (deviceStatus === enums.LOUDSPEAKER.deviceStatus.todayExistsFail.code) {
      return [
        broadcastIcon.iconBroadcastErrS, broadcastIcon.iconBroadcastErrL
      ];
    }
    return [
      broadcastIcon.iconBroadcastOffS, broadcastIcon.iconBroadcastOffL
    ];
  }, // 故障检测

  /**
   * 过滤故障标记的函数。
   * @param {Object} param0 过滤配置。
   * @param {Object} param0.layer 标记图层。
   * @param {boolean} param0.show 是否显示故障。
   */
  filterFault({
    layer,
    show
  }) {
    // 更新显示故障的配置
    this.cfg.showFault = show;
    const markers = this.getMarker();
    // 重新初始化标记样式
    this.initMarkers(markers);
    // 更新标记图层的源数据
    layer.setSource(createSource({
      markers,
      type: LayerConst.LOUDSPEAKER,
      onclick: this.onClusterClick
    }));
    // 关闭所有LOUDSPEAKER类型的左侧窗口
    closeLeftSideWindowByIdPrefix(`${LayerConst.LOUDSPEAKER}-`,
      LayerConst.LOUDSPEAKER);
  },

  /**
   * 打开详情页面的函数。
   * @param {Object} selectedData 选中的设备数据。
   * @param {Function} onclose 关闭详情页面的回调函数。
   */
  openDetail(selectedData, onclose) {
    // 构造详情页面的窗口ID
    const markerId = `${LayerConst.LOUDSPEAKER}-${selectedData.deviceStatus}-${selectedData.deviceCode}`;
    // 打开详情窗口
    openLeftSideWidow({
      windowId: markerId,
      props: {
        ds: { ...selectedData },
        markerType: LayerConst.LOUDSPEAKER
      },
      group: LayerConst.LOUDSPEAKER, // 详情页面关闭回调
      onclose
    });
  },

  /**
   * 集群点击事件的处理函数。
   * @param {Object} event 点击事件对象。
   * @param {Array} event.features 点击的特征数组。
   * @param {Object} event.geometry 点击的几何对象。
   */
  onClusterClick({
    features,
    geometry
  }) {
    // 如果没有特征则返回
    if (!features || features.length < 1) {
      return;
    }
    // 构造设备数据数组
    const ds = features.map(feature => {
      const data = feature.values_.attributes.ds;
      return {
        key: data.deviceCode,
        type: '云广播',
        name: data.deviceName,
        status: data.deviceStatus,
        statusName: enums.LOUDSPEAKER.deviceStatusGroup[data.deviceStatus]?.name,
        color: enums.LOUDSPEAKER.deviceStatusGroup[data.deviceStatus]?.color, ...data
      };
    });
    // 获取几何对象的坐标
    const [lng, lat] = geometry.getCoordinates();
    // 显示集群提示信息
    clusterTip({
      ds,
      markerType: LayerConst.LOUDSPEAKER,
      lng,
      lat
    });
  }
};
export default LOUDSPEAKER;
