@import "../../common";
.region-select-wrap {
  display: flex;
  position: absolute;
  top: 40px;
  right: 440px;
  pointer-events: auto;
  width: 200px;

  .el-cascader {
    width: 100%;
    background: rgba(23, 37, 55, 0.9);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    line-height: 37px !important;

    .el-input {
      // margin-right: 20px;

      .el-input__inner {
        font-family: PingFangSC-Regular;
        padding: 0 25px 0 0;
        font-size: 14px;
        color: rgba(255, 255, 255, 0.65);
        letter-spacing: 0;
        font-weight: 400;
        background: transparent;
        border: none;
      }
    }

    &::before {
      display: flex;
      width: 20px;
      height: 24px;
      margin: 0 6px;
      content: url("./img/icon_search.svg");
    }
  }
}

.el-cascader__dropdown {
  background: rgba(23, 37, 55, 0.9);
  border-radius: 4px;
  border: none;

  .el-radio__input.is-checked .el-radio__inner {
    border-color: #409eff !important;
    background: #409eff !important;
  }

  .el-cascader-node.is-active {
    .el-cascader-node__label {
      color: @theme-color;
    }
  }

  .el-radio__input .el-radio__inner {
    background-color: transparent;
    border-color: @theme-color;
  }
  .el-radio__input.is-disabled .el-radio__inner {
    background-color: #f5f7fa2e;
    border-color: #409eff73;
  }
  .el-cascader-menu {
    color: #fefefe;

    &:not(:last-child) {
      border-right: solid 1px @border-color;
    }

    .el-cascader-node.is-active,
    .el-cascader-node.is-selectable.in-checked-path {
      color: #fefefe !important;
    }

    .el-cascader-node:not(.is-disabled):focus,
    .el-cascader-node:not(.is-disabled):hover {
      background: @focus-color !important;
      color: #fefefe;
    }

    .el-cascader-node.is-selectable.in-active-path {
      color: @theme-color;
    }

    .in-active-path .in-checked-path .is-active {
      background: @theme-color;
      color: #fefefe;
    }
  }
}
