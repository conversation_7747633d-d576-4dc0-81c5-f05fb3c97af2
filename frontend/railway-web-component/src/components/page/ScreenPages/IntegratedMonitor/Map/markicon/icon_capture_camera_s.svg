<?xml version="1.0" encoding="UTF-8"?>
<svg width="48px" height="63px" viewBox="0 0 48 63" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>icon_抓拍摄像机_56_n</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="98.890959%" id="linearGradient-1">
            <stop stop-color="#1BF98E" stop-opacity="0.147385817" offset="0%"></stop>
            <stop stop-color="#29D980" stop-opacity="0.623991853" offset="45.4628257%"></stop>
            <stop stop-color="#90FFD6" stop-opacity="0.663027036" offset="100%"></stop>
        </linearGradient>
        <filter x="-74.3%" y="-8.4%" width="248.6%" height="116.7%" filterUnits="objectBoundingBox" id="filter-2">
            <feGaussianBlur stdDeviation="0.8" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="93.6372478%" id="linearGradient-3">
            <stop stop-color="#3AE58B" stop-opacity="0.37038523" offset="0%"></stop>
            <stop stop-color="#0AE0E7" stop-opacity="0.8" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="-3.1396872%" id="linearGradient-4">
            <stop stop-color="#3FDD6B" offset="0%"></stop>
            <stop stop-color="#AB9B19" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <ellipse id="path-5" cx="20.8205128" cy="53.6" rx="5.5" ry="2.4"></ellipse>
        <filter x="-53.6%" y="-81.3%" width="207.3%" height="345.8%" filterUnits="objectBoundingBox" id="filter-6">
            <feMorphology radius="0.4" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
            <feOffset dx="0" dy="2" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.3 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-7">
            <stop stop-color="#3ACFE5" stop-opacity="0.4" offset="0%"></stop>
            <stop stop-color="#00313F" offset="100%"></stop>
        </linearGradient>
        <ellipse id="path-8" cx="21" cy="20.7257143" rx="21" ry="20.7257143"></ellipse>
        <filter x="-13.1%" y="-8.4%" width="126.2%" height="126.5%" filterUnits="objectBoundingBox" id="filter-9">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.22745098   0 0 0 0 0.806306856   0 0 0 0 0.898039216  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="5.08795696%" x2="50%" y2="100%" id="linearGradient-10">
            <stop stop-color="#3AE57B" offset="0%"></stop>
            <stop stop-color="#3AE568" stop-opacity="0.276665961" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-11">
            <stop stop-color="#FFFFFF" stop-opacity="0.167855216" offset="0%"></stop>
            <stop stop-color="#3ACAE5" offset="100%"></stop>
        </linearGradient>
        <ellipse id="path-12" cx="21" cy="20.7257143" rx="18.5769231" ry="18.3342857"></ellipse>
        <filter x="-17.0%" y="-11.7%" width="133.9%" height="134.4%" filterUnits="objectBoundingBox" id="filter-13">
            <feMorphology radius="0.8" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
            <feOffset dx="0" dy="2" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.5 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="1.41712536%" x2="50%" y2="96.2653678%" id="linearGradient-14">
            <stop stop-color="#21993D" offset="0%"></stop>
            <stop stop-color="#32D785" offset="62.4393063%"></stop>
            <stop stop-color="#32D2D6" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="44.4796397%" y1="17.8031512%" x2="44.4796397%" y2="78.3504117%" id="linearGradient-15">
            <stop stop-color="#E8F3FE" stop-opacity="0.2" offset="0%"></stop>
            <stop stop-color="#3AE5DC" offset="100%"></stop>
        </linearGradient>
        <path d="M16.2751553,23.2446857 L16.2751553,21.4511143 L15,21.2111173 L15,26.9035714 L16.2751553,26.417599 L16.2751553,24.4651684 L18.299293,24.4651684 L19.1991002,23.0004184 L18.0739127,22.2684704 L17.5477398,23.2446857 L16.2751553,23.2446857 Z M27,21.4511143 L17.8493894,14.9464286 L16.8013283,14.9464286 L15.3762051,17.3873939 L15.3762051,18.2004796 L23.5473113,23.8954959 L24.2225952,23.8100878 L27,21.4511143 Z M15.3727773,18.6890143 L15.3727773,19.9855102 L23.6227237,25.5993888 L24.372563,25.5993888 L24.7479112,25.1954082 L25.1232593,23.9749255 C25.1232593,23.9749255 24.0734842,24.7888653 23.9980718,24.7888653 C23.6235807,24.4651684 15.3744912,18.6890143 15.3744912,18.6890143 L15.3727773,18.6890143 Z" id="path-16"></path>
        <filter x="-25.0%" y="-25.1%" width="150.0%" height="150.2%" filterUnits="objectBoundingBox" id="filter-17">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.309803922   0 0 0 0 0.623529412   0 0 0 0 1  0 0 0 0.7 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="综合监测-大屏" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="6-综合监测-实时过车-过车记录" transform="translate(-1037.000000, -672.000000)">
            <g id="icon_抓拍摄像机_56_n" transform="translate(1040.000000, 673.000000)">
                <rect id="矩形" fill="url(#linearGradient-1)" filter="url(#filter-2)" x="19.3846154" y="24.7114286" width="3.23076923" height="28.6971429" rx="1.2"></rect>
                <g id="椭圆形">
                    <use fill="black" fill-opacity="1" filter="url(#filter-6)" xlink:href="#path-5"></use>
                    <use stroke="url(#linearGradient-4)" stroke-width="0.8" fill-opacity="0.8" fill="url(#linearGradient-3)" fill-rule="evenodd" xlink:href="#path-5"></use>
                </g>
                <g id="椭圆形" opacity="0.469350179">
                    <use fill="black" fill-opacity="1" filter="url(#filter-9)" xlink:href="#path-8"></use>
                    <use fill="url(#linearGradient-7)" fill-rule="evenodd" xlink:href="#path-8"></use>
                </g>
                <g id="椭圆形">
                    <use fill="black" fill-opacity="1" filter="url(#filter-13)" xlink:href="#path-12"></use>
                    <use stroke="url(#linearGradient-11)" stroke-width="1.6" fill="url(#linearGradient-10)" fill-rule="evenodd" xlink:href="#path-12"></use>
                </g>
                <rect id="矩形" stroke="url(#linearGradient-15)" stroke-width="0.8" fill="url(#linearGradient-14)" x="5" y="4.98214286" width="32.3076923" height="31.8857143" rx="15.9428571"></rect>
                <path d="M28.7402109,30.8892507 L24.1301579,30.8892507 C23.7195922,30.8798202 23.388707,30.550998 23.3781492,30.1419306 C23.3781492,29.7433599 23.7281533,29.3946105 24.1301579,29.3946105 L28.7402109,29.3946105 C29.1412155,29.3946105 29.4922195,29.0458611 29.4922195,28.6472904 L29.4922195,24.8110471 C29.4922195,24.4124764 29.8422235,24.063727 30.2442281,24.063727 C30.6442327,24.063727 30.9952368,24.4124764 30.9952368,24.8110471 L30.9952368,28.6472904 C31.0452373,29.8430026 29.9932253,30.8892507 28.7402109,30.8892507 Z M17.1640778,30.8892684 L13.3050335,30.8892684 C12.6938377,30.8916389 12.1069108,30.6511109 11.6742578,30.2209439 C11.2416048,29.790777 10.9989375,29.2064777 11.0000035,28.5974691 L11.0000035,24.7612258 C11.0000035,24.362655 11.350011,24.0139057 11.7520157,24.0139057 C12.1520203,24.0139057 12.5030243,24.3626551 12.5030243,24.7612258 L12.5030243,28.5974691 C12.5030243,28.9960398 12.8540283,29.3447892 13.2550329,29.3447892 L17.1140773,29.3447892 C17.5140819,29.3447892 17.8660859,29.6935386 17.8660859,30.0921093 C17.9160865,30.5405013 17.5650825,30.8394294 17.1640778,30.8892684 Z M11.7520157,17.8858807 C11.34145,17.8764502 11.0105648,17.547628 11.000007,17.1385606 L11.000007,13.252496 C11.000007,12.0069624 12.0520191,10.9607143 13.3050335,10.9607143 L17.1640778,10.9607143 C17.5640824,10.9607143 17.9160865,11.3094637 17.9160865,11.7080344 C17.9160865,12.1066051 17.5650825,12.4553545 17.1640778,12.4553545 L13.3050335,12.4553545 C12.8948581,12.4653076 12.5645603,12.7939832 12.5540249,13.2026746 L12.5540249,17.0389179 C12.5540249,17.48731 12.2030208,17.8360594 11.7520157,17.8858807 Z M30.2442281,17.8858807 C29.8336624,17.8764502 29.5027773,17.547628 29.4922195,17.1385606 L29.4922195,13.252496 C29.4922195,12.8539252 29.1412155,12.5051759 28.7402109,12.5051759 L24.1301579,12.5051759 C23.7195922,12.4957453 23.3887071,12.1669232 23.3781492,11.7578558 C23.3781492,11.359285 23.6781527,10.9607143 24.0801573,10.9607143 L28.6902103,10.9607143 C29.9932253,10.9607143 30.9952368,11.9571411 30.9952368,13.252496 L30.9952368,17.0887393 C31.0452373,17.48731 30.6952333,17.8360594 30.2442281,17.8858807 L30.2442281,17.8858807 Z" id="形状" fill="#FFFFFF" fill-rule="nonzero"></path>
                <g id="icon_摄像机_18_n" fill-rule="nonzero">
                    <use fill="black" fill-opacity="1" filter="url(#filter-17)" xlink:href="#path-16"></use>
                    <use fill="#FFFFFF" xlink:href="#path-16"></use>
                </g>
            </g>
        </g>
    </g>
</svg>