<!--
 * @Description: 区域选择组件，用于选择和显示区域信息，并根据选择的区域高亮显示地图区域。
 * @Autor: he.chao
 * @Date: 2022-10-19 15:07:55
 * @LastEditors: liu.yongli
 * @LastEditTime: 2023-12-13 14:36:14
-->
<template>
  <div class="region-select-wrap">
    <!-- 使用Element UI的级联选择器，用于展示和选择区域选项 -->
    <el-cascader
      ref="cascader"
      expandTrigger="hover"
      :options="addressOptions"
      @change="addressChange"
      v-model="newAddress"
      :clearable="false"
      :props="{ checkStrictly: true }"
    />
  </div>
</template>

<script>
import { setUserLocalStorage } from '@/components/common/utils'
import { CfgEnum } from '@/components/page/ScreenPages/IntegratedMonitor/enum/CfgEnum'
import {
  drawPoly,
  highLightArea,
  removeHighLightArea,
  highLightArea30,
} from '@/utils/map3.0'
// import { polyArrowLine } from '@/utils/map3.0'
import { transformPointToMercator } from '@/utils'
import CTMapOl from '@ct/ct_map_ol'
import ConstEnum from '@/components/common/ConstEnum'
// 用于缓存地图层的变量
const layerCache = {}
let maskByRegionCodeObj = null
export default {
  inject: ['mapRef'],
  props: {
    // 定义了一个自定义属性 onRegionChange，用于在区域选择变化时触发外部的回调函数
    onRegionChange: {
      type: Function,
      default: () => {
        console.log('onRegionChange')
      },
    },
    mapId: {
      type: String,
    },
  },
  data() {
    return {
      // 存储级联选择器的选项数据
      addressOptions: [],
      // 存储当前选择的区域信息
      newAddress: [],
    }
  },
  mounted() {
    // 组件挂载后初始化用户省份城市数据
    this.initUserProvinceCityData()
  },
  methods: {
    /**
     * 2.0根据区域名称改变地图上区域的高亮显示
     * @param {string} areaName - 区域的名称，默认为北京
     */
    changeCityPolygon(areaName = '北京') {
      const _map = this.mapRef.getMapRef(this.mapId).mapInstance
      // 根据区域名称获取区域的多边形信息
      let region = new CTMapOl.netApi.regionInfo({ keyWord: areaName })
      region.then(({ code, data }) => {
        // 如果已有高亮区域，则先移除
        if (layerCache.hightLight) {
          removeHighLightArea(_map, layerCache.hightLight)
        }
        // 如果已有区域层，则移除其所有图形
        if (layerCache.regionLayer) {
          layerCache.regionLayer.exts.remove()
        }
        // 如果返回码为200且返回了数据，则根据多边形数据绘制区域和高亮区域
        if (code == 200 && data) {
          let polygon = data.polygon
          if (polygon) {
            const list = []
            const polys = []
            // 处理多边形数据，转换为绘制函数需要的格式
            polygon.split('|').map(a => {
              const coordinates = a.split(';').map(sub => {
                const [longitude, latitude] = sub.split(',')
                // return [parseFloat(longitude), parseFloat(latitude)]
                // 3.0的地图经纬度要转成墨卡托
                return transformPointToMercator([
                  parseFloat(longitude),
                  parseFloat(latitude),
                ])
              })
              list.push([coordinates])
              polys.push(coordinates)
            })
            // 绘制区域
            layerCache.regionLayer = drawPoly({
              map: _map,
              polys,
              option: {
                fillColor: 'rgba(255, 255, 255, 0)',
                strokeColor: '#409EFF',
                strokeWidth: 2,
              },
            })
            // 高亮区域
            layerCache.hightLight = highLightArea(_map, list)
          }
        }
      })
    },
    /**
     * 使用 3.0 方式
     * 根据区域名称改变地图上区域的高亮显示
     * @param {string} regionCode - 区域的id，默认为北京
     * @param {*} key - 默认为北京
     */
    async changeCityPolygon30(regionCode = '110000', key = 0) {
      // 如果已有高亮区域，则先移除
      if (maskByRegionCodeObj) {
        maskByRegionCodeObj.cleanup()
      }
      // 如果返回码为200且返回了数据，则根据多边形数据绘制区域和高亮区域
      maskByRegionCodeObj = await highLightArea30(
        this.mapRef.getMapRef(this.mapId),
        regionCode,
        key
      )
      // 根据区域名称获取区域的多边形信息
      // let region = new CTMapOl.netApi.regionInfo({ keyWord: areaName })
      // region.then(async ({ code, data }) => {
      //   // 如果已有高亮区域，则先移除
      //   if (maskByRegionCodeObj) {
      //     maskByRegionCodeObj.cleanup()
      //   }
      //   // 如果返回码为200且返回了数据，则根据多边形数据绘制区域和高亮区域
      //   if (code == 200 && data.cityCode) {
      //     maskByRegionCodeObj = await highLightArea30(
      //       this.mapRef.getMapRef(this.mapId),
      //       data.cityCode
      //     )
      //   }
      // })
    },
    /**
     * 区域选择变化时的处理函数
     * @param {Array} nodes - 选择的区域节点数组
     */
    addressChange(nodes) {
      // 更新当前选择的区域信息
      this.newAddress = nodes
      let nodesInfo = this.$refs['cascader'].getCheckedNodes()
      this.cityName = nodesInfo[0] ? nodesInfo[0].label : ''
      let selectedData

      // 如果选择了区域，则处理并存储选择的区域数据
      if (nodesInfo[0]) {
        selectedData = {
          regionCode: nodesInfo[0].value,
          regionName: nodesInfo[0].label,
          regionLevel: nodesInfo[0].data.regionLevel,
        }
        // 将选择的区域信息存储到本地存储中
        setUserLocalStorage(`${CfgEnum.STORAGE_KEY.SELECT_REGION}`, {
          nodes,
          cityName: this.cityName,
          regionCode: nodesInfo[0].value,
          regionLevel: nodesInfo[0].data.regionLevel,
        })
      }
      // 触发外部定义的区域变化回调函数
      this.onRegionChange(selectedData)
      // 根据当前选择的城市更新地图上的高亮区域 this.cityName
      const idx = nodes?.length - 1 || 0
      this.changeCityPolygon30(nodes?.[idx] || '', idx)
    },
    /**
     * 初始化用户省份城市数据
     * @description 根据用户信息设置默认选择的区域和区域选项
     */
    initUserProvinceCityData() {
      // 将选择的区域信息存储到本地存储中
      const _userInfo = window.userInfo
      // 获取最后一个地市展示
      const idx = _userInfo?.defArea?.length - 1 || 0
      const _regionCode = _userInfo?.defArea[idx] || ''
      const regionLevelArr = [
        ConstEnum.REGION_LEVEL.province,
        ConstEnum.REGION_LEVEL.city,
        ConstEnum.REGION_LEVEL.district,
      ]
      setUserLocalStorage(`${CfgEnum.STORAGE_KEY.SELECT_REGION}`, {
        nodes: _userInfo.defArea,
        cityName: _userInfo.defAreaName,
        regionCode: _regionCode,
        regionLevel: regionLevelArr[idx],
      })
      // 设置当前选择的区域为用户默认区域
      this.newAddress = _userInfo.defArea
      // 设置区域选项为用户可选择的区域树
      this.addressOptions = _userInfo.roleAreaTree
      // 根据用户默认区域名更新地图上的高亮区域 _userInfo.defAreaName
      this.changeCityPolygon30(_regionCode, idx)
    },
  },
}
</script>

<style lang='less' src='./index.less' />