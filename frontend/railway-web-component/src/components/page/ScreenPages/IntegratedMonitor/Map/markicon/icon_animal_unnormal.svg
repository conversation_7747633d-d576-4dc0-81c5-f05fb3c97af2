<?xml version="1.0" encoding="UTF-8"?>
<svg width="48px" height="63px" viewBox="0 0 48 63" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>icon_畜牧_故障_56_n</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="98.890959%" id="linearGradient-1">
            <stop stop-color="#1BA9F9" stop-opacity="0.147385817" offset="0%"></stop>
            <stop stop-color="#72C1CF" stop-opacity="0.5" offset="47.171522%"></stop>
            <stop stop-color="#3A77E5" stop-opacity="0.5" offset="100%"></stop>
        </linearGradient>
        <filter x="-74.3%" y="-8.3%" width="248.6%" height="116.7%" filterUnits="objectBoundingBox" id="filter-2">
            <feGaussianBlur stdDeviation="0.8" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-3">
            <stop stop-color="#3A77E5" stop-opacity="0.8" offset="0%"></stop>
            <stop stop-color="#0A92E7" stop-opacity="0.8" offset="96.7802461%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="-5.11180773%" id="linearGradient-4">
            <stop stop-color="#3A77E5" offset="0%"></stop>
            <stop stop-color="#194DAB" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <ellipse id="path-5" cx="21" cy="53.6" rx="5.65384615" ry="2.4"></ellipse>
        <filter x="-52.2%" y="-81.3%" width="204.4%" height="345.8%" filterUnits="objectBoundingBox" id="filter-6">
            <feMorphology radius="0.4" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
            <feOffset dx="0" dy="2" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.3 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-7">
            <stop stop-color="#3A77E5" stop-opacity="0.4" offset="0%"></stop>
            <stop stop-color="#00233F" offset="100%"></stop>
        </linearGradient>
        <ellipse id="path-8" cx="21" cy="20.8" rx="21" ry="20.8"></ellipse>
        <filter x="-13.1%" y="-8.4%" width="126.2%" height="126.4%" filterUnits="objectBoundingBox" id="filter-9">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.22745098   0 0 0 0 0.466666667   0 0 0 0 0.898039216  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="5.08795696%" x2="50%" y2="100%" id="linearGradient-10">
            <stop stop-color="#3A77E5" offset="0%"></stop>
            <stop stop-color="#3A77E5" stop-opacity="0.276665961" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-11">
            <stop stop-color="#FFFFFF" stop-opacity="0.167855216" offset="0%"></stop>
            <stop stop-color="#3A77E5" offset="100%"></stop>
        </linearGradient>
        <ellipse id="path-12" cx="21" cy="20.8" rx="18.5769231" ry="18.4"></ellipse>
        <filter x="-17.0%" y="-11.7%" width="133.9%" height="134.2%" filterUnits="objectBoundingBox" id="filter-13">
            <feMorphology radius="0.8" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
            <feOffset dx="0" dy="2" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.5 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="1.41712536%" x2="50%" y2="97.1209752%" id="linearGradient-14">
            <stop stop-color="#3A77E5" offset="0%"></stop>
            <stop stop-color="#4891F6" offset="64.9389745%"></stop>
            <stop stop-color="#4F9FFF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="44.4796397%" y1="17.8031512%" x2="44.4796397%" y2="78.3504117%" id="linearGradient-15">
            <stop stop-color="#E8F3FE" stop-opacity="0.2" offset="0%"></stop>
            <stop stop-color="#3A77E5" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-16">
            <stop stop-color="#EF5058" offset="0%"></stop>
            <stop stop-color="#8B1414" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-17">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#DF4542" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="综合监测-大屏" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="3-2综合监测-畜牧-详情" transform="translate(-572.000000, -412.000000)">
            <g id="icon_畜牧_故障_56_n" transform="translate(575.000000, 413.000000)">
                <g id="icon_畜牧_56_n">
                    <rect id="矩形" fill="url(#linearGradient-1)" filter="url(#filter-2)" x="19.3846154" y="24.8" width="3.23076923" height="28.8" rx="1.2"></rect>
                    <g id="椭圆形">
                        <use fill="black" fill-opacity="1" filter="url(#filter-6)" xlink:href="#path-5"></use>
                        <use stroke="url(#linearGradient-4)" stroke-width="0.8" fill-opacity="0.8" fill="url(#linearGradient-3)" fill-rule="evenodd" xlink:href="#path-5"></use>
                    </g>
                    <g id="椭圆形" opacity="0.469350179">
                        <use fill="black" fill-opacity="1" filter="url(#filter-9)" xlink:href="#path-8"></use>
                        <use fill="url(#linearGradient-7)" fill-rule="evenodd" xlink:href="#path-8"></use>
                    </g>
                    <g id="椭圆形">
                        <use fill="black" fill-opacity="1" filter="url(#filter-13)" xlink:href="#path-12"></use>
                        <use stroke="url(#linearGradient-11)" stroke-width="1.6" fill="url(#linearGradient-10)" fill-rule="evenodd" xlink:href="#path-12"></use>
                    </g>
                    <rect id="矩形" stroke="url(#linearGradient-15)" stroke-width="0.8" fill="url(#linearGradient-14)" x="5" y="5" width="32.3076923" height="32" rx="16"></rect>
                    <path d="M24.9766082,24.1834458 C25.2535673,23.5671545 25.4100117,23.2590088 25.4444444,23.2590088 C25.4788772,23.2590088 25.6349474,23.5671545 25.9122807,24.1834458 L29,24.1834458 L29,29 L21.8888889,29 L21.8888889,24.1834458 L24.9766082,24.1834458 Z M23.9009591,27.3486551 C24.3089123,27.3486551 24.6390175,27.0096949 24.6390175,26.5917229 C24.6390175,26.1737509 24.308538,25.8347907 23.9009591,25.8347907 C23.4933801,25.8347907 23.1632749,26.1737509 23.1632749,26.5917229 C23.1632749,27.0096949 23.4933801,27.3486551 23.9009591,27.3486551 Z M26.2491228,25.8347907 L26.2491228,27.3486551 L26.7188304,27.3486551 L26.7188304,25.8347907 L26.2491228,25.8347907 Z M27.188538,25.8347907 L27.188538,27.3486551 L27.6578713,27.3486551 L27.6578713,25.8347907 L27.188538,25.8347907 Z M27.4311111,13 C27.3555556,13.2802155 27.3177778,13.5204002 27.3177778,13.7605849 C27.6577778,13.8006157 27.8844444,13.4403386 28.1111111,13.560431 C28.0355556,13.7205541 27.9977778,13.8406465 27.8844444,14.0007696 C27.7333333,14.2009236 27.5066667,14.4010775 27.3555556,14.5211699 C27.0911111,14.681293 27.2422222,14.9615085 27.3555556,15.1616624 C27.5444444,15.4819087 27.5822222,15.8822166 27.8088889,16.2024629 C27.9222222,16.362586 28.1488889,16.6828323 27.96,16.8829862 C27.6577778,17.2032325 27.1666667,16.8429555 26.8266667,16.8429555 C26.4488889,16.8029247 26.1088889,16.7228631 25.7311111,16.8029247 C25.4288889,16.8829862 25.1266667,16.9630478 24.9,17.2032325 C24.4088889,17.8036943 24.4088889,18.6443408 23.9555556,19.2448026 C23.8422222,19.4049258 23.2755556,19.8852952 23.2755556,20.0854491 C23.2755556,20.4457262 23.1622222,20.8060033 23.0866667,21.1662803 C22.9733333,21.5265574 22.9733333,21.7667421 23.0111111,22.1270192 C23.0111111,22.3271731 23.0488889,22.4872963 23.0866667,22.6474194 C23.1244444,22.727481 23.4644444,23.5280967 23.4266667,23.5681275 C23.2377778,23.7682814 22.7466667,23.6081583 22.4822222,23.5681275 C22.4822222,23.4080044 22.5577778,23.3279428 22.52,23.1678196 C22.4444444,23.1277889 22.3311111,23.1678196 22.2933333,23.0877581 C22.2933333,23.0477273 22.3311111,22.7675118 22.3311111,22.6874502 C22.3311111,22.5673579 22.2933333,22.4072347 22.2933333,22.2871423 C22.2933333,22.0069268 22.2933333,21.7267113 22.2555556,21.4865266 C22.18,21.1662803 22.0288889,20.8860648 21.9911111,20.5658186 C21.9155556,20.1254799 21.9155556,19.6851413 21.7644444,19.3248642 C21.6133333,18.9645871 20.8955556,18.9245563 20.5933333,18.9245563 C20.0644444,18.9245563 19.5355556,19.0046179 19.0066667,19.0446487 C18.7422222,19.0846795 18.4777778,19.164741 18.2133333,19.2047718 C17.9111111,19.2448026 17.6088889,19.0846795 17.3444444,19.0046179 C17.0422222,18.9645871 16.9288889,19.1247103 16.6644444,19.2448026 C16.5511111,19.2848334 16.3244444,19.364895 16.2111111,19.4449565 C15.9844444,19.6451105 16.06,20.1655107 16.0977778,20.4056954 C16.1355556,20.7659725 16.2111111,21.2863727 16.3622222,21.6466498 C16.5511111,22.0069268 16.9666667,22.1270192 17.0044444,22.5673579 C16.7777778,22.6874502 16.4,22.6073886 16.0977778,22.5273271 C16.06,22.4072347 16.06,22.2871423 16.0222222,22.1270192 C15.3422222,21.8067729 15.4555556,20.6058493 14.9266667,20.0854491 C14.7,20.3256338 14.6244444,20.6859109 14.5111111,20.9661264 C14.3977778,21.2863727 14.2844444,21.6466498 14.3977778,22.0069268 C14.4355556,22.0869884 14.8888889,23.2478812 14.8133333,23.2478812 C14.3977778,23.287912 13.8688889,23.2478812 13.9066667,22.6874502 C13.8877778,22.6574271 13.8641667,22.6349098 13.8381944,22.6161454 L13.6729167,22.5185703 L13.6729167,22.5185703 L13.6044444,22.4472655 C13.68,22.16705 13.6044444,21.9668961 13.6044444,21.8067729 C13.6422222,21.3264035 13.5666667,20.8860648 13.68,20.4056954 C13.6941667,20.3556569 13.7095139,20.3056184 13.7253776,20.2555018 L13.8214453,19.9532381 C13.8978125,19.6995273 13.9538889,19.4399527 13.9066667,19.164741 C13.8311111,18.6443408 13.7933333,17.9638175 13.3777778,17.6035404 C13.2266667,18.0839098 13.4911111,18.7244024 13.4911111,19.2448026 C13.4911111,19.6451105 13.7177778,20.2055415 13.4155556,20.5658186 C13.34,20.5257878 13.34,20.4056954 13.3022222,20.3256338 C13.1888889,19.8452644 13,19.4849873 13,18.9645871 C13,18.4842177 13.1511111,18.0038482 13.1888889,17.483448 C13.2644444,16.7628939 13.1888889,16.0023089 13.2266667,15.2817548 C13.2644444,14.9214777 13.2644444,14.3610467 13.4533333,14.0408004 C14.0955556,13.5204002 14.9644444,13.7205541 15.6822222,13.6805234 C16.06,13.6805234 16.4377778,13.7205541 16.8155556,13.7605849 C17.4577778,13.8406465 18.1,13.7605849 18.7422222,13.7605849 C19.4222222,13.7605849 20.0644444,13.8006157 20.7444444,13.8006157 C21.2355556,13.8406465 21.6888889,13.8406465 22.1422222,13.7205541 C22.5955556,13.6404926 22.9733333,13.5204002 23.4644444,13.560431 C23.5777778,13.560431 24.8622222,14.0007696 25.4666667,13.8806773 C25.7311111,13.8406465 25.9955556,13.4403386 26.3733333,13.6004618 L26.3733333,13.6004618 L26.4866667,13.2401847 C26.6755556,13.2802155 26.6755556,13.5204002 26.9022222,13.5204002 C27.1288889,13.4003079 27.2044444,13.1200924 27.4311111,13 Z M27.1967719,22.4274105 C27.2529123,22.4534843 27.2693801,22.6608901 27.1967719,22.8161481 C27.1267836,22.9662704 26.9695906,23.0658251 26.9160702,23.0389611 C25.8381754,22.3235101 25.0447251,22.3341766 24.0682573,23.0389611 C23.9851696,23.085183 23.7707135,23.024344 23.6883743,22.8686909 C23.6116491,22.7225192 23.6610526,22.4839038 23.7377778,22.438077 C24.8096842,21.7076137 26.0163275,21.7076137 27.1967719,22.4274105 Z" id="icon_畜牧_18_s" fill="#FFFFFF" fill-rule="nonzero"></path>
                </g>
                <g id="icon_云广播告警_20_n" transform="translate(26.000000, 0.000000)">
                    <circle id="椭圆形" stroke="url(#linearGradient-17)" stroke-width="0.8" fill="url(#linearGradient-16)" cx="8" cy="8" r="8"></circle>
                    <g id="编组" transform="translate(7.200000, 3.200000)" fill="#FFFFFF">
                        <path d="M0.0199262278,0.810985365 C0.0199262278,0.363090516 0.376777381,0 0.816975587,0 C1.24937292,0.0110023609 1.59423389,0.370890257 1.59409874,0.810985365 L1.59409874,4.98755997 C1.51866714,5.36553169 1.19204813,5.63716126 0.812990337,5.63716126 C0.433932543,5.63716126 0.10731353,5.36553169 0.0318819699,4.98755997 L0.0199262278,0.810985365 Z" id="路径"></path>
                        <polygon id="路径" points="1.5940987 9.11547546 0 9.11547546 0 7.49350473 1.5940987 7.49350473"></polygon>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>