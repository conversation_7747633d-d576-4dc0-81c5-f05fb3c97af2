<?xml version="1.0" encoding="UTF-8"?>
<svg width="62px" height="87px" viewBox="0 0 62 87" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>icon_畜牧_故障_80_s</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="97.230474%" id="linearGradient-1">
            <stop stop-color="#1BA9F9" stop-opacity="0.147385817" offset="0%"></stop>
            <stop stop-color="#6CB0D1" offset="52.6941686%"></stop>
            <stop stop-color="#3A77E5" stop-opacity="0.898221529" offset="100%"></stop>
        </linearGradient>
        <filter x="-76.3%" y="-8.5%" width="252.5%" height="117.0%" filterUnits="objectBoundingBox" id="filter-2">
            <feGaussianBlur stdDeviation="1.056" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="0%" id="linearGradient-3">
            <stop stop-color="#058FC5" offset="0%"></stop>
            <stop stop-color="#02B7FF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="0.871394231%" id="linearGradient-4">
            <stop stop-color="#0CBAFF" offset="0%"></stop>
            <stop stop-color="#00B7FF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <filter x="-11.9%" y="-25.5%" width="123.7%" height="151.0%" filterUnits="objectBoundingBox" id="filter-5">
            <feGaussianBlur stdDeviation="1.408" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="0%" id="linearGradient-6">
            <stop stop-color="#058FC5" offset="0%"></stop>
            <stop stop-color="#02B7FF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="0.871394231%" id="linearGradient-7">
            <stop stop-color="#0CBAFF" offset="0%"></stop>
            <stop stop-color="#00B7FF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-8">
            <stop stop-color="#3A77E5" stop-opacity="0.4" offset="0%"></stop>
            <stop stop-color="#00233F" offset="100%"></stop>
        </linearGradient>
        <ellipse id="path-9" cx="27" cy="26.8965517" rx="27" ry="26.8965517"></ellipse>
        <filter x="-13.0%" y="-9.3%" width="125.9%" height="126.0%" filterUnits="objectBoundingBox" id="filter-10">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.22745098   0 0 0 0 0.466666667   0 0 0 0 0.898039216  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="5.08795696%" x2="50%" y2="100%" id="linearGradient-11">
            <stop stop-color="#3A77E5" offset="0%"></stop>
            <stop stop-color="#3A77E5" stop-opacity="0.276665961" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-12">
            <stop stop-color="#FFFFFF" stop-opacity="0.167855216" offset="0%"></stop>
            <stop stop-color="#3A77E5" offset="100%"></stop>
        </linearGradient>
        <ellipse id="path-13" cx="27" cy="26.8965517" rx="23.8846154" ry="23.7931034"></ellipse>
        <filter x="-16.9%" y="-12.7%" width="133.7%" height="133.9%" filterUnits="objectBoundingBox" id="filter-14">
            <feMorphology radius="1.056" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
            <feOffset dx="0" dy="2" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.5 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="1.41712536%" x2="50%" y2="97.1209752%" id="linearGradient-15">
            <stop stop-color="#3A77E5" offset="0%"></stop>
            <stop stop-color="#4891F6" offset="64.9389745%"></stop>
            <stop stop-color="#4F9FFF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="44.4796397%" y1="17.8031512%" x2="44.4796397%" y2="78.3504117%" id="linearGradient-16">
            <stop stop-color="#E8F3FE" stop-opacity="0.2" offset="0%"></stop>
            <stop stop-color="#3A77E5" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-17">
            <stop stop-color="#EF5058" offset="0%"></stop>
            <stop stop-color="#8B1414" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-18">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#DF4542" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="综合监测-大屏" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="3-2综合监测-畜牧-详情" transform="translate(-1326.000000, -488.000000)">
            <g id="icon_畜牧_故障_80_s" transform="translate(1330.000000, 490.000000)">
                <rect id="矩形" fill="url(#linearGradient-1)" filter="url(#filter-2)" x="24.9230769" y="32.0689655" width="4.15384615" height="37.2413793" rx="1.584"></rect>
                <path d="M47.7692308,70.3448276 C47.7692308,75.6771476 38.4715938,80 27,80 C15.5284062,80 6.23076923,75.6771476 6.23076923,70.3448276 C6.23076923,65.0125076 15.5284062,60.6896552 27,60.6896552 C38.4715938,60.6896552 47.7692308,65.0125076 47.7692308,70.3448276 C47.7692308,70.3448276 47.7692308,70.3448276 47.7692308,70.3448276 Z" id="椭圆_7_拷贝" stroke="url(#linearGradient-4)" stroke-width="1.408" fill="url(#linearGradient-3)" opacity="0.5" filter="url(#filter-5)"></path>
                <path d="M27,62.0689655 C34.6470092,62.0689655 40.8461538,64.8478924 40.8461538,68.2758621 C40.8461538,71.7038317 34.6470092,74.4827586 27,74.4827586 C19.3529908,74.4827586 13.1538462,71.7038317 13.1538462,68.2758621 C13.1538462,64.8478924 19.3529908,62.0689655 27,62.0689655 C27,62.0689655 27,62.0689655 27,62.0689655 Z" id="椭圆_7" stroke="url(#linearGradient-7)" stroke-width="1.408" fill="url(#linearGradient-6)"></path>
                <g id="椭圆形" opacity="0.469350179">
                    <use fill="black" fill-opacity="1" filter="url(#filter-10)" xlink:href="#path-9"></use>
                    <use fill="url(#linearGradient-8)" fill-rule="evenodd" xlink:href="#path-9"></use>
                </g>
                <g id="椭圆形">
                    <use fill="black" fill-opacity="1" filter="url(#filter-14)" xlink:href="#path-13"></use>
                    <use stroke="url(#linearGradient-12)" stroke-width="2.112" fill="url(#linearGradient-11)" fill-rule="evenodd" xlink:href="#path-13"></use>
                </g>
                <rect id="矩形" stroke="url(#linearGradient-16)" stroke-width="1.056" fill="url(#linearGradient-15)" x="6" y="6" width="41.5384615" height="41.3793103" rx="20.6896552"></rect>
                <path d="M31.9707602,30.9793073 C32.3169591,30.2089431 32.5125146,29.823761 32.5555556,29.823761 C32.5985965,29.823761 32.7936842,30.2089431 33.1403509,30.9793073 L37,30.9793073 L37,37 L28.1111111,37 L28.1111111,30.9793073 L31.9707602,30.9793073 Z M30.6261988,34.9358189 C31.1361404,34.9358189 31.5487719,34.5121186 31.5487719,33.9896536 C31.5487719,33.4671887 31.1356725,33.0434884 30.6261988,33.0434884 C30.1167252,33.0434884 29.7040936,33.4671887 29.7040936,33.9896536 C29.7040936,34.5121186 30.1167251,34.9358189 30.6261988,34.9358189 Z M33.5614035,33.0434884 L33.5614035,34.9358189 L34.148538,34.9358189 L34.148538,33.0434884 L33.5614035,33.0434884 Z M34.7356725,33.0434884 L34.7356725,34.9358189 L35.3223392,34.9358189 L35.3223392,33.0434884 L34.7356725,33.0434884 Z M35.0388889,17 C34.9444444,17.3502694 34.8972222,17.6505003 34.8972222,17.9507312 C35.3222222,18.0007696 35.6055556,17.5504233 35.8888889,17.7005388 C35.7944444,17.9006927 35.7472222,18.0508081 35.6055556,18.2509621 C35.4166667,18.5011545 35.1333333,18.7513469 34.9444444,18.9014623 C34.6138889,19.1016163 34.8027778,19.4518856 34.9444444,19.7020781 C35.1805556,20.1023859 35.2277778,20.6027707 35.5111111,21.0030786 C35.6527778,21.2032325 35.9361111,21.6035404 35.7,21.8537328 C35.3222222,22.2540407 34.7083333,21.8036943 34.2833333,21.8036943 C33.8111111,21.7536558 33.3861111,21.6535789 32.9138889,21.7536558 C32.5361111,21.8537328 32.1583333,21.9538098 31.875,22.2540407 C31.2611111,23.0046179 31.2611111,24.055426 30.6944444,24.8060033 C30.5527778,25.0061572 29.8444444,25.606619 29.8444444,25.8568114 C29.8444444,26.3071577 29.7027778,26.7575041 29.6083333,27.2078504 C29.4666667,27.6581968 29.4666667,27.9584277 29.5138889,28.408774 C29.5138889,28.6589664 29.5611111,28.8591203 29.6083333,29.0592743 C29.6555556,29.1593512 30.0805556,30.1601209 30.0333333,30.2101594 C29.7972222,30.4603518 29.1833333,30.2601979 28.8527778,30.2101594 C28.8527778,30.0100054 28.9472222,29.9099285 28.9,29.7097746 C28.8055556,29.6597361 28.6638889,29.7097746 28.6166667,29.6096976 C28.6166667,29.5596591 28.6638889,29.2093897 28.6638889,29.1093128 C28.6638889,28.9591973 28.6166667,28.7590434 28.6166667,28.6089279 C28.6166667,28.2586586 28.6166667,27.9083892 28.5694444,27.6081583 C28.475,27.2078504 28.2861111,26.857581 28.2388889,26.4572732 C28.1444444,25.9068499 28.1444444,25.3564266 27.9555556,24.9060802 C27.7666667,24.4557339 26.8694444,24.4056954 26.4916667,24.4056954 C25.8305556,24.4056954 25.1694444,24.5057724 24.5083333,24.5558109 C24.1777778,24.6058493 23.8472222,24.7059263 23.5166667,24.7559648 C23.1388889,24.8060033 22.7611111,24.6058493 22.4305556,24.5057724 C22.0527778,24.4557339 21.9111111,24.6558878 21.5805556,24.8060033 C21.4388889,24.8560418 21.1555556,24.9561187 21.0138889,25.0561957 C20.7305556,25.3063881 20.825,25.9568884 20.8722222,26.2571193 C20.9194444,26.7074656 21.0138889,27.3579659 21.2027778,27.8083122 C21.4388889,28.2586586 21.9583333,28.408774 22.0055556,28.9591973 C21.7222222,29.1093128 21.25,29.0092358 20.8722222,28.9091588 C20.825,28.7590434 20.825,28.6089279 20.7777778,28.408774 C20.3527778,28.2086201 20.1756944,27.7332545 20.0222222,27.2266149 L19.9080404,26.8445828 C19.791276,26.4643099 19.65625,26.100749 19.4083333,25.8568114 C19.125,26.1570423 19.0305556,26.6073886 18.8888889,26.957658 C18.7472222,27.3579659 18.6055556,27.8083122 18.7472222,28.2586586 C18.7944444,28.3587355 19.3611111,29.8098515 19.2666667,29.8098515 C18.7472222,29.85989 18.0861111,29.8098515 18.1333333,29.1093128 C18.0388889,28.9591973 17.85,28.9591973 17.7555556,28.8090819 C17.8095238,28.6089279 17.8018141,28.4414522 17.7852932,28.297318 L17.7605118,28.0974559 L17.7605118,28.0974559 L17.7555556,28.0084661 C17.8027778,27.4080044 17.7083333,26.857581 17.85,26.2571193 C17.9916667,25.7567344 18.2277778,25.2563496 18.1333333,24.7059263 C18.0388889,24.055426 17.9916667,23.2047718 17.4722222,22.7544255 C17.2833333,23.3548873 17.6138889,24.155503 17.6138889,24.8060033 C17.6138889,25.3063881 17.8972222,26.0069268 17.5194444,26.4572732 C17.425,26.4072347 17.425,26.2571193 17.3777778,26.1570423 C17.2361111,25.5565805 17,25.1062342 17,24.4557339 C17,23.8552721 17.1888889,23.2548103 17.2361111,22.60431 C17.3305556,21.7036174 17.2361111,20.7528862 17.2833333,19.8521935 C17.3305556,19.4018472 17.3305556,18.7013084 17.5666667,18.3010005 C18.3694444,17.6505003 19.4555556,17.9006927 20.3527778,17.8506542 C20.825,17.8506542 21.2972222,17.9006927 21.7694444,17.9507312 C22.5722222,18.0508081 23.375,17.9507312 24.1777778,17.9507312 C25.0277778,17.9507312 25.8305556,18.0007696 26.6805556,18.0007696 C27.2944444,18.0508081 27.8611111,18.0508081 28.4277778,17.9006927 C28.9944444,17.8006157 29.4666667,17.6505003 30.0805556,17.7005388 C30.2222222,17.7005388 31.8277778,18.2509621 32.5833333,18.1008466 C32.9138889,18.0508081 33.2444444,17.5504233 33.7166667,17.7505772 L33.7166667,17.7505772 L33.8583333,17.3002309 C34.0944444,17.3502694 34.0944444,17.6505003 34.3777778,17.6505003 C34.4722222,17.6004618 34.545679,17.528184 34.6121399,17.4484931 L34.8045267,17.2020072 C34.8709877,17.1223163 34.9444444,17.0500385 35.0388889,17 Z M34.7459649,28.7842631 C34.8161403,28.8168554 34.8367251,29.0761126 34.7459649,29.2701851 C34.6584795,29.457838 34.4619883,29.5822814 34.3950877,29.5487014 C33.0477193,28.6543876 32.0559064,28.6677208 30.8353216,29.5487014 C30.731462,29.6064787 30.4633918,29.53043 30.3604678,29.3358636 C30.2645614,29.153149 30.3263158,28.8548798 30.4222222,28.7975963 C31.7621053,27.8845172 33.2704094,27.8845172 34.7459649,28.7842631 Z" id="icon_畜牧_18_s" fill="#FFFFFF" fill-rule="nonzero"></path>
                <g id="icon_云广播告警_20_n" transform="translate(33.230769, 1.379310)">
                    <ellipse id="椭圆形" stroke="url(#linearGradient-18)" stroke-width="0.9856" fill="url(#linearGradient-17)" cx="9.69230769" cy="9.65517241" rx="9.69230769" ry="9.65517241"></ellipse>
                    <g id="编组" transform="translate(8.723077, 3.862069)" fill="#FFFFFF">
                        <path d="M0.0241413913,0.978775441 C0.0241413913,0.438212691 0.456480289,0 0.989797346,0 C1.51366334,0.0132787115 1.93147568,0.447626173 1.93131194,0.978775441 L1.93131194,6.01946893 C1.83992366,6.47564169 1.44421216,6.80347049 0.984969062,6.80347049 C0.525725966,6.80347049 0.130014468,6.47564169 0.0386262328,6.01946893 L0.0241413913,0.978775441 Z" id="路径"></path>
                        <polygon id="路径" points="1.93131189 11.0014359 0 11.0014359 0 9.04388502 1.93131189 9.04388502"></polygon>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>