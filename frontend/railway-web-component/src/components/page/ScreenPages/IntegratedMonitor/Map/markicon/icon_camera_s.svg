<?xml version="1.0" encoding="UTF-8"?>
<svg width="48px" height="63px" viewBox="0 0 48 63" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>icon_摄像机_56_n</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="98.890959%" id="linearGradient-1">
            <stop stop-color="#1BA9F9" stop-opacity="0.147385817" offset="0%"></stop>
            <stop stop-color="#72C1CF" stop-opacity="0.5" offset="47.171522%"></stop>
            <stop stop-color="#3A77E5" stop-opacity="0.5" offset="100%"></stop>
        </linearGradient>
        <filter x="-74.3%" y="-8.3%" width="248.6%" height="116.7%" filterUnits="objectBoundingBox" id="filter-2">
            <feGaussianBlur stdDeviation="0.8" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-3">
            <stop stop-color="#3A77E5" stop-opacity="0.8" offset="0%"></stop>
            <stop stop-color="#0A92E7" stop-opacity="0.8" offset="96.7802461%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="-5.11180773%" id="linearGradient-4">
            <stop stop-color="#3A77E5" offset="0%"></stop>
            <stop stop-color="#194DAB" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <ellipse id="path-5" cx="21" cy="53.6" rx="5.65384615" ry="2.4"></ellipse>
        <filter x="-52.2%" y="-81.3%" width="204.4%" height="345.8%" filterUnits="objectBoundingBox" id="filter-6">
            <feMorphology radius="0.4" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
            <feOffset dx="0" dy="2" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.3 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-7">
            <stop stop-color="#3A77E5" stop-opacity="0.4" offset="0%"></stop>
            <stop stop-color="#00233F" offset="100%"></stop>
        </linearGradient>
        <ellipse id="path-8" cx="21" cy="20.8" rx="21" ry="20.8"></ellipse>
        <filter x="-13.1%" y="-8.4%" width="126.2%" height="126.4%" filterUnits="objectBoundingBox" id="filter-9">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.22745098   0 0 0 0 0.466666667   0 0 0 0 0.898039216  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="5.08795696%" x2="50%" y2="100%" id="linearGradient-10">
            <stop stop-color="#3A77E5" offset="0%"></stop>
            <stop stop-color="#3A77E5" stop-opacity="0.276665961" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-11">
            <stop stop-color="#FFFFFF" stop-opacity="0.167855216" offset="0%"></stop>
            <stop stop-color="#3A77E5" offset="100%"></stop>
        </linearGradient>
        <ellipse id="path-12" cx="21" cy="20.8" rx="18.5769231" ry="18.4"></ellipse>
        <filter x="-17.0%" y="-11.7%" width="133.9%" height="134.2%" filterUnits="objectBoundingBox" id="filter-13">
            <feMorphology radius="0.8" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
            <feOffset dx="0" dy="2" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.5 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="1.41712536%" x2="50%" y2="97.1209752%" id="linearGradient-14">
            <stop stop-color="#3A77E5" offset="0%"></stop>
            <stop stop-color="#4891F6" offset="64.9389745%"></stop>
            <stop stop-color="#4F9FFF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="44.4796397%" y1="17.8031512%" x2="44.4796397%" y2="78.3504117%" id="linearGradient-15">
            <stop stop-color="#E8F3FE" stop-opacity="0.2" offset="0%"></stop>
            <stop stop-color="#3A77E5" offset="100%"></stop>
        </linearGradient>
        <path d="M15.2756689,23.5936 L15.2756689,21.4336 L13.7307692,21.1445714 L13.7307692,28 L15.2756689,27.4147429 L15.2756689,25.0634286 L17.7279896,25.0634286 L18.8181406,23.2994286 L17.4549327,22.4179429 L16.8174539,23.5936 L15.2756689,23.5936 Z M28.2692308,21.4336 L17.1829141,13.6 L15.9131477,13.6 L14.1865562,16.5396571 L14.1865562,17.5188571 L24.0861656,24.3773714 L24.904298,24.2745143 L28.2692308,21.4336 Z M14.1824032,18.1072 L14.1824032,19.6685714 L24.1775306,26.4293714 L25.0859898,26.4293714 L25.5407385,25.9428571 L25.9954872,24.4730286 C25.9954872,24.4730286 24.7236444,25.4532571 24.6322793,25.4532571 C24.1785689,25.0634286 14.1844797,18.1072 14.1844797,18.1072 L14.1824032,18.1072 Z" id="path-16"></path>
        <filter x="-31.0%" y="-31.2%" width="161.9%" height="162.5%" filterUnits="objectBoundingBox" id="filter-17">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.309803922   0 0 0 0 0.623529412   0 0 0 0 1  0 0 0 0.7 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="综合监测-大屏" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="1-6综合监测-弹框" transform="translate(-905.000000, -919.000000)">
            <g id="icon_摄像机_56_n" transform="translate(908.000000, 920.000000)">
                <rect id="矩形" fill="url(#linearGradient-1)" filter="url(#filter-2)" x="19.3846154" y="24.8" width="3.23076923" height="28.8" rx="1.2"></rect>
                <g id="椭圆形">
                    <use fill="black" fill-opacity="1" filter="url(#filter-6)" xlink:href="#path-5"></use>
                    <use stroke="url(#linearGradient-4)" stroke-width="0.8" fill-opacity="0.8" fill="url(#linearGradient-3)" fill-rule="evenodd" xlink:href="#path-5"></use>
                </g>
                <g id="椭圆形" opacity="0.469350179">
                    <use fill="black" fill-opacity="1" filter="url(#filter-9)" xlink:href="#path-8"></use>
                    <use fill="url(#linearGradient-7)" fill-rule="evenodd" xlink:href="#path-8"></use>
                </g>
                <g id="椭圆形">
                    <use fill="black" fill-opacity="1" filter="url(#filter-13)" xlink:href="#path-12"></use>
                    <use stroke="url(#linearGradient-11)" stroke-width="1.6" fill="url(#linearGradient-10)" fill-rule="evenodd" xlink:href="#path-12"></use>
                </g>
                <rect id="矩形" stroke="url(#linearGradient-15)" stroke-width="0.8" fill="url(#linearGradient-14)" x="4.84615385" y="4.8" width="32.3076923" height="32" rx="16"></rect>
                <g id="icon_摄像机_18_n" fill-rule="nonzero">
                    <use fill="black" fill-opacity="1" filter="url(#filter-17)" xlink:href="#path-16"></use>
                    <use fill="#FFFFFF" xlink:href="#path-16"></use>
                </g>
            </g>
        </g>
    </g>
</svg>