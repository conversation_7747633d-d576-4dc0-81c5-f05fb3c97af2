<?xml version="1.0" encoding="UTF-8"?>
<svg width="62px" height="87px" viewBox="0 0 62 87" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>icon_摄像机_离线_80_s</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="97.230474%" id="linearGradient-1">
            <stop stop-color="#D5D5D5" stop-opacity="0.147385817" offset="0%"></stop>
            <stop stop-color="#D5D5D5" offset="51.4224179%"></stop>
            <stop stop-color="#D5D5D5" stop-opacity="0.898221529" offset="100%"></stop>
        </linearGradient>
        <filter x="-69.3%" y="-7.7%" width="238.7%" height="115.5%" filterUnits="objectBoundingBox" id="filter-2">
            <feGaussianBlur stdDeviation="0.96" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="0%" id="linearGradient-3">
            <stop stop-color="#D5D5D5" offset="0%"></stop>
            <stop stop-color="#D5D5D5" stop-opacity="0" offset="97.5113022%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="0.871394231%" id="linearGradient-4">
            <stop stop-color="#D5D5D5" offset="0%"></stop>
            <stop stop-color="#00B7FF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <filter x="-10.8%" y="-23.2%" width="121.6%" height="146.4%" filterUnits="objectBoundingBox" id="filter-5">
            <feGaussianBlur stdDeviation="1.28" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="0%" id="linearGradient-6">
            <stop stop-color="#D5D5D5" offset="0%"></stop>
            <stop stop-color="#D5D5D5" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="0.871394231%" id="linearGradient-7">
            <stop stop-color="#D5D5D5" offset="0%"></stop>
            <stop stop-color="#D5D5D5" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-8">
            <stop stop-color="#D5D5D5" stop-opacity="0.4" offset="0%"></stop>
            <stop stop-color="#D5D5D5" offset="100%"></stop>
        </linearGradient>
        <ellipse id="path-9" cx="27" cy="26.8965517" rx="27" ry="26.8965517"></ellipse>
        <filter x="-13.0%" y="-9.3%" width="125.9%" height="126.0%" filterUnits="objectBoundingBox" id="filter-10">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.835294118   0 0 0 0 0.835294118   0 0 0 0 0.835294118  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="5.08795696%" x2="50%" y2="100%" id="linearGradient-11">
            <stop stop-color="#D5D5D5" offset="0%"></stop>
            <stop stop-color="#D5D5D5" stop-opacity="0.276665961" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-12">
            <stop stop-color="#FFFFFF" stop-opacity="0.167855216" offset="0%"></stop>
            <stop stop-color="#D5D5D5" offset="100%"></stop>
        </linearGradient>
        <ellipse id="path-13" cx="27" cy="26.8965517" rx="23.8846154" ry="23.7931034"></ellipse>
        <filter x="-16.7%" y="-12.5%" width="133.3%" height="133.5%" filterUnits="objectBoundingBox" id="filter-14">
            <feMorphology radius="0.96" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
            <feOffset dx="0" dy="2" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.5 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-15">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#424242" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="44.4796397%" y1="17.8031512%" x2="44.4796397%" y2="78.3504117%" id="linearGradient-16">
            <stop stop-color="#D5D5D5" stop-opacity="0.2" offset="0%"></stop>
            <stop stop-color="#D5D5D5" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="综合监测-大屏" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="1-6综合监测-弹框" transform="translate(-1296.000000, -876.000000)">
            <g id="icon_摄像机_离线_80_s" transform="translate(1300.000000, 878.000000)">
                <rect id="矩形" fill="url(#linearGradient-1)" filter="url(#filter-2)" x="24.9230769" y="32.0689655" width="4.15384615" height="37.2413793" rx="1.44"></rect>
                <path d="M27,80 C15.5284062,80 6.23076923,75.6771476 6.23076923,70.3448276 C6.23076923,65.0125076 15.5284062,60.6896552 27,60.6896552 C38.4715938,60.6896552 47.7692308,65.0125076 47.7692308,70.3448276 C47.7692308,75.6771476 38.4715938,80 27,80 Z" id="椭圆_7_拷贝" stroke="url(#linearGradient-4)" stroke-width="1.28" fill="url(#linearGradient-3)" opacity="0.5" filter="url(#filter-5)"></path>
                <path d="M27,62.0689655 C34.6470092,62.0689655 40.8461538,64.8478924 40.8461538,68.2758621 C40.8461538,71.7038317 34.6470092,74.4827586 27,74.4827586 C19.3529908,74.4827586 13.1538462,71.7038317 13.1538462,68.2758621 C13.1538462,64.8478924 19.3529908,62.0689655 27,62.0689655 C27,62.0689655 27,62.0689655 27,62.0689655 Z" id="椭圆_7" stroke="url(#linearGradient-7)" stroke-width="1.28" fill="url(#linearGradient-6)"></path>
                <g id="编组-9">
                    <g id="椭圆形" opacity="0.469350179">
                        <use fill="black" fill-opacity="1" filter="url(#filter-10)" xlink:href="#path-9"></use>
                        <use fill="url(#linearGradient-8)" fill-rule="evenodd" xlink:href="#path-9"></use>
                    </g>
                    <g id="椭圆形">
                        <use fill="black" fill-opacity="1" filter="url(#filter-14)" xlink:href="#path-13"></use>
                        <use stroke="url(#linearGradient-12)" stroke-width="1.92" fill="url(#linearGradient-11)" fill-rule="evenodd" xlink:href="#path-13"></use>
                    </g>
                    <rect id="矩形" stroke="url(#linearGradient-16)" stroke-width="0.96" fill="url(#linearGradient-15)" x="6.23076923" y="6.20689655" width="41.5384615" height="41.3793103" rx="19.2"></rect>
                </g>
                <path d="M18.8223842,30.9103448 L18.8223842,27.8068966 L16.6153846,27.3916256 L16.6153846,37.2413793 L18.8223842,36.4004926 L18.8223842,33.0221675 L22.3256994,33.0221675 L23.883058,30.4876847 L21.9356182,29.2211823 L21.0249342,30.9103448 L18.8223842,30.9103448 Z M37.3846154,27.8068966 L21.5470201,16.5517241 L19.7330682,16.5517241 L17.2665088,20.7753695 L17.2665088,22.182266 L31.408808,32.0364532 L32.5775685,31.8886699 L37.3846154,27.8068966 Z M17.260576,23.0275862 L17.260576,25.270936 L31.5393295,34.9847291 L32.8371283,34.9847291 L33.4867693,34.2857143 L34.1364103,32.1738916 C34.1364103,32.1738916 32.319492,33.582266 32.1889705,33.582266 C31.5408127,33.0221675 17.2635424,23.0275862 17.2635424,23.0275862 L17.260576,23.0275862 Z" id="icon_摄像机_18_n" fill="#FFFFFF" fill-rule="nonzero"></path>
            </g>
        </g>
    </g>
</svg>