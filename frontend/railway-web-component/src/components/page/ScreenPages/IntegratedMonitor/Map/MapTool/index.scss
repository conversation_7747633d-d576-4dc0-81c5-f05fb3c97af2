@import "~@/assets/styles/px-to-rem";

.map-control-tool {
  position: absolute;
  width: px-to-rem(173);
  height: px-to-rem(125);
  right: px-to-rem(390);
  bottom: px-to-rem(22);
  pointer-events: auto;
  z-index: 1;

  .map-switch-tool {
    width: px-to-rem(124);
    height: px-to-rem(96);
    background: #1c2c40;
    border-radius: px-to-rem(4);
    overflow: hidden;
    position: absolute;
    bottom: px-to-rem(30);
    right: px-to-rem(47);
    padding: px-to-rem(5);
    box-sizing: border-box;
    transition: all 0.3s;

    &:hover {
      width: px-to-rem(347);

      .map-switch-second {
        left: px-to-rem(5);
      }

      .map-switch-third {
        left: px-to-rem(5);
      }
    }

    .map-switch-tool-inner {
      width: px-to-rem(337);
      height: 100%;
      display: flex;
      position: absolute;

      .map-item {
        flex:1;
        height: 100%;
        display: flex;
        flex-flow: column;
        cursor: pointer;

        .img-style {
          width: 100%;
          height: px-to-rem(65);
        }

        .text-style {
          height: px-to-rem(28);
          line-height: px-to-rem(28);
          text-align: center;
          width: 100%;
          font-family: PingFangSC-Regular;
          font-size: px-to-rem(12);
          color: #ffffff;
          letter-spacing: 0;
          font-weight: 400;
        }

        &:not(:first-child) {
          margin-left: px-to-rem(5);
        }

        &:hover {
          background: rgba(5, 41, 92, 0.9);
        }
      }

      .selected {
        background: #05295c;
      }
    }

    .map-switch-second {
      left: px-to-rem(-218);
    }
    .map-switch-third {
      left: px-to-rem(-109);
    }
  }

  .map-control-tool-main {
    position: relative;
    height: 100%;

    .zoom-control-tool {
      position: absolute;
      right: 0;
      top: 0;
      width: px-to-rem(40);
      height: px-to-rem(96);
      background: #1c2c40;
      border-radius: px-to-rem(4);
      display: flex;
      flex-flow: column;
      align-items: center;

      .button {
        height: calc(px-to-rem(68) / 3);
        color: #fefefe;
        flex: 1;
        font-weight: 500;
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: px-to-rem(24);
      }
    }

    .scale-tool {
      position: absolute;
      right: 0;
      bottom: 0;
      width: 100%;
      height: px-to-rem(22);
      background: #1c2c40;
      border-radius: px-to-rem(4);

      .scale-container {
        height: px-to-rem(8);
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: px-to-rem(4) px-to-rem(6) 0 px-to-rem(6);

        .left,
        .right {
          width: px-to-rem(1);
          height: px-to-rem(8);
          background: #d8d8d8;
        }

        .line {
          flex:1;
          height: px-to-rem(1);
          background: #d8d8d8;
        }
      }

      .scale-info {
        font-family: PingFangSC-Regular;
        font-size: px-to-rem(12);
        color: #ffffff;
        letter-spacing: 0;
        font-weight: 400;
        text-align: center;
        position: absolute;
        bottom: px-to-rem(-2);
        width: 100%;
      }
    }
  }
}

.toolbox-toolbox {
  // 工具提示覆写
  &.el-tooltip__popper.is-dark {
    background: rgba(23, 37, 55, 0.9) !important;

    color: #fefefe !important;
    line-height: px-to-rem(6.5);

    .popper__arrow {
      border-left-color: rgba(23, 37, 55, 0.9);
      border-right-color: rgba(23, 37, 55, 0.9);

      &::after {
        border-left-color: transparent;
        border-right-color: transparent;
      }
    }
  }
}
