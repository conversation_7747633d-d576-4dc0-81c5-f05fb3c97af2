<?xml version="1.0" encoding="UTF-8"?>
<svg width="48px" height="63px" viewBox="0 0 48 63" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>icon_桥梁_56_n</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="98.890959%" id="linearGradient-1">
            <stop stop-color="#1BA9F9" stop-opacity="0.147385817" offset="0%"></stop>
            <stop stop-color="#72C1CF" stop-opacity="0.5" offset="47.171522%"></stop>
            <stop stop-color="#3A77E5" stop-opacity="0.5" offset="100%"></stop>
        </linearGradient>
        <filter x="-74.3%" y="-8.3%" width="248.6%" height="116.7%" filterUnits="objectBoundingBox" id="filter-2">
            <feGaussianBlur stdDeviation="0.8" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-3">
            <stop stop-color="#3A77E5" stop-opacity="0.8" offset="0%"></stop>
            <stop stop-color="#0A92E7" stop-opacity="0.8" offset="96.7802461%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="-5.11180773%" id="linearGradient-4">
            <stop stop-color="#3A77E5" offset="0%"></stop>
            <stop stop-color="#194DAB" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <ellipse id="path-5" cx="21" cy="53.6" rx="5.65384615" ry="2.4"></ellipse>
        <filter x="-52.2%" y="-81.3%" width="204.4%" height="345.8%" filterUnits="objectBoundingBox" id="filter-6">
            <feMorphology radius="0.4" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
            <feOffset dx="0" dy="2" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.3 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-7">
            <stop stop-color="#3A77E5" stop-opacity="0.4" offset="0%"></stop>
            <stop stop-color="#00233F" offset="100%"></stop>
        </linearGradient>
        <ellipse id="path-8" cx="21" cy="20.8" rx="21" ry="20.8"></ellipse>
        <filter x="-13.1%" y="-8.4%" width="126.2%" height="126.4%" filterUnits="objectBoundingBox" id="filter-9">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.22745098   0 0 0 0 0.466666667   0 0 0 0 0.898039216  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="5.08795696%" x2="50%" y2="100%" id="linearGradient-10">
            <stop stop-color="#3A77E5" offset="0%"></stop>
            <stop stop-color="#3A77E5" stop-opacity="0.276665961" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-11">
            <stop stop-color="#FFFFFF" stop-opacity="0.167855216" offset="0%"></stop>
            <stop stop-color="#3A77E5" offset="100%"></stop>
        </linearGradient>
        <ellipse id="path-12" cx="21" cy="20.8" rx="18.5769231" ry="18.4"></ellipse>
        <filter x="-17.0%" y="-11.7%" width="133.9%" height="134.2%" filterUnits="objectBoundingBox" id="filter-13">
            <feMorphology radius="0.8" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
            <feOffset dx="0" dy="2" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.5 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="1.41712536%" x2="50%" y2="97.1209752%" id="linearGradient-14">
            <stop stop-color="#3A77E5" offset="0%"></stop>
            <stop stop-color="#4891F6" offset="64.9389745%"></stop>
            <stop stop-color="#4F9FFF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="44.4796397%" y1="17.8031512%" x2="44.4796397%" y2="78.3504117%" id="linearGradient-15">
            <stop stop-color="#E8F3FE" stop-opacity="0.2" offset="0%"></stop>
            <stop stop-color="#3A77E5" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="综合监测-大屏" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="1-6综合监测-弹框" transform="translate(-1058.000000, -919.000000)">
            <g id="icon_桥梁_56_n" transform="translate(1061.000000, 920.000000)">
                <rect id="矩形" fill="url(#linearGradient-1)" filter="url(#filter-2)" x="19.3846154" y="24.8" width="3.23076923" height="28.8" rx="1.2"></rect>
                <g id="椭圆形">
                    <use fill="black" fill-opacity="1" filter="url(#filter-6)" xlink:href="#path-5"></use>
                    <use stroke="url(#linearGradient-4)" stroke-width="0.8" fill-opacity="0.8" fill="url(#linearGradient-3)" fill-rule="evenodd" xlink:href="#path-5"></use>
                </g>
                <g id="椭圆形" opacity="0.469350179">
                    <use fill="black" fill-opacity="1" filter="url(#filter-9)" xlink:href="#path-8"></use>
                    <use fill="url(#linearGradient-7)" fill-rule="evenodd" xlink:href="#path-8"></use>
                </g>
                <g id="椭圆形">
                    <use fill="black" fill-opacity="1" filter="url(#filter-13)" xlink:href="#path-12"></use>
                    <use stroke="url(#linearGradient-11)" stroke-width="1.6" fill="url(#linearGradient-10)" fill-rule="evenodd" xlink:href="#path-12"></use>
                </g>
                <rect id="矩形" stroke="url(#linearGradient-15)" stroke-width="0.8" fill="url(#linearGradient-14)" x="4.84615385" y="4.8" width="32.3076923" height="32" rx="16"></rect>
                <path d="M28.2691917,19.7823329 L28.2691917,25.4548886 C28.2737183,25.9692029 27.8842098,26.3915067 27.3969816,26.4 L25.6430517,26.4 C25.6521844,23.672719 23.5790675,21.4455968 20.9945209,21.407849 C18.4113442,21.4470124 16.338684,23.6731909 16.3482733,26.4 L14.6144352,26.4 C14.1267504,26.3915067 13.7367853,25.9692028 13.7422078,25.4544168 L13.7422078,19.7828048 C13.7372419,19.2680187 14.1267504,18.8457148 14.6144352,18.8372215 L27.3969816,18.8372215 C27.8842098,18.8457148 28.2737183,19.2680187 28.2691917,19.7823329 Z M27.9677737,16.9446395 L14.0217247,16.9446395 C13.9431527,16.9432636 13.8684277,16.9092483 13.814413,16.85027 C13.7596527,16.7906158 13.7296592,16.7111861 13.7308079,16.6289733 L13.7308079,16.3156663 C13.7295392,16.233291 13.7595413,16.1536693 13.814413,16.0938977 C13.8685048,16.0350934 13.9432199,16.0012527 14.0217247,16 L27.9673171,16 C28.0457297,16.001502 28.1202677,16.0355071 28.1741722,16.0943696 C28.2289682,16.1533506 28.2586494,16.2330929 28.2577892,16.3156663 L28.2577892,16.6308607 C28.2607989,16.800319 28.1308349,16.9405162 27.9668605,16.9446395 L27.9677737,16.9446395 Z M21.4424785,18.8367497 L20.5703082,18.8367497 L20.5703082,16.9446395 L21.4424785,16.9446395 L21.4424785,18.8367497 Z M24.2028291,18.8367497 L23.3306588,18.8367497 L23.3306588,16.9446395 L24.2028291,16.9446395 L24.2028291,18.8367497 Z M26.962723,18.8367497 L26.0905527,18.8367497 L26.0905527,16.9446395 L26.962723,16.9446395 L26.962723,18.8367497 Z M18.6825846,18.8367497 L17.8104143,18.8367497 L17.8104143,16.9446395 L18.6825846,16.9446395 L18.6825846,18.8367497 Z M15.9226907,18.8367497 L15.0505204,18.8367497 L15.0505204,16.9446395 L15.9226907,16.9446395 L15.9226907,18.8367497 Z" id="icon_桥梁_18_n" fill="#FFFFFF" fill-rule="nonzero"></path>
            </g>
        </g>
    </g>
</svg>