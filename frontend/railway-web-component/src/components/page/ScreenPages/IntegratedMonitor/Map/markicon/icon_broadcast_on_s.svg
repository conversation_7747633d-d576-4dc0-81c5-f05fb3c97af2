<?xml version="1.0" encoding="UTF-8"?>
<svg width="48px" height="63px" viewBox="0 0 48 63" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>icon_云广播_在线_56_n</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="98.890959%" id="linearGradient-1">
            <stop stop-color="#1BA9F9" stop-opacity="0.147385817" offset="0%"></stop>
            <stop stop-color="#72C1CF" stop-opacity="0.5" offset="47.171522%"></stop>
            <stop stop-color="#3A77E5" stop-opacity="0.5" offset="100%"></stop>
        </linearGradient>
        <filter x="-74.3%" y="-8.3%" width="248.6%" height="116.7%" filterUnits="objectBoundingBox" id="filter-2">
            <feGaussianBlur stdDeviation="0.8" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-3">
            <stop stop-color="#3A77E5" stop-opacity="0.8" offset="0%"></stop>
            <stop stop-color="#0A92E7" stop-opacity="0.8" offset="96.7802461%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="-5.11180773%" id="linearGradient-4">
            <stop stop-color="#3A77E5" offset="0%"></stop>
            <stop stop-color="#194DAB" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <ellipse id="path-5" cx="21" cy="53.6" rx="5.65384615" ry="2.4"></ellipse>
        <filter x="-52.2%" y="-81.3%" width="204.4%" height="345.8%" filterUnits="objectBoundingBox" id="filter-6">
            <feMorphology radius="0.4" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
            <feOffset dx="0" dy="2" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.3 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-7">
            <stop stop-color="#3A77E5" stop-opacity="0.4" offset="0%"></stop>
            <stop stop-color="#00233F" offset="100%"></stop>
        </linearGradient>
        <ellipse id="path-8" cx="21" cy="20.8" rx="21" ry="20.8"></ellipse>
        <filter x="-13.1%" y="-8.4%" width="126.2%" height="126.4%" filterUnits="objectBoundingBox" id="filter-9">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.22745098   0 0 0 0 0.466666667   0 0 0 0 0.898039216  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="5.08795696%" x2="50%" y2="100%" id="linearGradient-10">
            <stop stop-color="#3A77E5" offset="0%"></stop>
            <stop stop-color="#3A77E5" stop-opacity="0.276665961" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-11">
            <stop stop-color="#FFFFFF" stop-opacity="0.167855216" offset="0%"></stop>
            <stop stop-color="#3A77E5" offset="100%"></stop>
        </linearGradient>
        <ellipse id="path-12" cx="21" cy="20.8" rx="18.5769231" ry="18.4"></ellipse>
        <filter x="-17.0%" y="-11.7%" width="133.9%" height="134.2%" filterUnits="objectBoundingBox" id="filter-13">
            <feMorphology radius="0.8" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
            <feOffset dx="0" dy="2" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.5 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="1.41712536%" x2="50%" y2="97.1209752%" id="linearGradient-14">
            <stop stop-color="#3A77E5" offset="0%"></stop>
            <stop stop-color="#4891F6" offset="64.9389745%"></stop>
            <stop stop-color="#4F9FFF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="44.4796397%" y1="17.8031512%" x2="44.4796397%" y2="78.3504117%" id="linearGradient-15">
            <stop stop-color="#E8F3FE" stop-opacity="0.2" offset="0%"></stop>
            <stop stop-color="#3A77E5" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="综合监测-大屏" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="1-6综合监测-弹框" transform="translate(-905.000000, -818.000000)">
            <g id="icon_云广播_在线_56_n" transform="translate(908.000000, 819.000000)">
                <rect id="矩形" fill="url(#linearGradient-1)" filter="url(#filter-2)" x="19.3846154" y="24.8" width="3.23076923" height="28.8" rx="1.2"></rect>
                <g id="椭圆形">
                    <use fill="black" fill-opacity="1" filter="url(#filter-6)" xlink:href="#path-5"></use>
                    <use stroke="url(#linearGradient-4)" stroke-width="0.8" fill-opacity="0.8" fill="url(#linearGradient-3)" fill-rule="evenodd" xlink:href="#path-5"></use>
                </g>
                <g id="椭圆形" opacity="0.469350179">
                    <use fill="black" fill-opacity="1" filter="url(#filter-9)" xlink:href="#path-8"></use>
                    <use fill="url(#linearGradient-7)" fill-rule="evenodd" xlink:href="#path-8"></use>
                </g>
                <g id="椭圆形">
                    <use fill="black" fill-opacity="1" filter="url(#filter-13)" xlink:href="#path-12"></use>
                    <use stroke="url(#linearGradient-11)" stroke-width="1.6" fill="url(#linearGradient-10)" fill-rule="evenodd" xlink:href="#path-12"></use>
                </g>
                <rect id="矩形" stroke="url(#linearGradient-15)" stroke-width="0.8" fill="url(#linearGradient-14)" x="4.84615385" y="4.8" width="32.3076923" height="32" rx="16"></rect>
                <path d="M26.3044263,12.7228127 L26.5011471,12.9341566 L26.608554,13.0563953 C27.2723349,13.825288 27.8691944,14.7997702 28.3187769,15.9150432 C29.6231917,19.1441965 29.2020991,22.3331225 27.3808548,23.0360027 L27.2047828,23.0923033 C27.004631,23.1492956 26.796468,23.1750192 26.5835786,23.1710802 C26.2703494,23.1912119 25.9157493,23.1929969 25.561203,23.1886715 L24.3862913,23.1680132 L24.3862913,23.1680132 L23.9807002,23.1727755 C23.8775801,23.1819288 23.766195,23.1674557 23.648496,23.1316642 C22.6343885,23.1673034 21.6684095,23.260793 20.7420879,23.3952162 C21.6448474,24.5413306 22.4719623,25.7975727 22.8230466,26.7445581 L22.9075652,26.9929683 C23.0371451,27.3188821 23.1235561,27.6213638 23.1542218,27.8888751 L23.2011479,28.3492319 L23.2283462,28.7456577 C23.2798206,29.8441805 23.0578282,30.1490466 22.4980904,30.2849451 L22.2989139,30.3248434 L22.0741902,30.3573672 L21.7529845,30.3896542 C20.9122239,30.4488095 20.2187861,30.2725232 20.1771006,29.3833756 L20.1766556,29.2178381 C20.1895907,28.8546941 20.1265777,28.2599938 19.9897326,27.5654876 L19.9305819,27.6021644 C19.9118452,27.3517582 19.8583195,26.9966247 19.760166,26.5788276 C19.5592503,25.828704 19.2875998,25.040757 18.9468705,24.3403949 C18.8502053,24.1539045 18.7464235,23.9662525 18.6340485,23.781035 L18.0726884,23.9078375 C17.218536,23.5223871 16.2626632,22.3499448 15.6381298,20.8052173 C15.0158402,19.2604899 14.8954211,17.7669608 15.2484508,16.9156053 C16.8863289,16.0100557 18.5976835,14.877737 20.2736973,13.4047266 C20.2965671,13.3767977 20.3203432,13.3542951 20.3449431,13.3353776 L21.0182124,12.6690075 C21.2181698,12.4730223 21.4338599,12.2664069 21.6472882,12.0748434 L21.8813015,11.8684154 L21.902911,11.846237 C22.071677,11.6692725 22.2704316,11.5228498 22.4899484,11.4131772 L22.6590826,11.3379105 C23.7570577,10.9146054 25.1143707,11.4908269 26.3044263,12.7228127 Z M14.4092573,17.3661508 C14.2028247,18.3923121 14.3524136,19.7380974 14.8999088,21.0926595 C15.4481519,22.4486845 16.2783701,23.5311639 17.1459854,24.1433499 L16.5805396,24.2954822 C15.540897,24.6962921 14.0973646,23.5289697 13.3554039,21.6880232 C12.6119473,19.8470767 12.8512894,18.0302666 13.8894361,17.6294566 L14.4092573,17.3661508 Z M22.9642439,12.0949147 C21.929089,12.4935304 21.7383633,15.6349151 22.7226579,18.1780104 C23.7503333,20.8308165 26.0270757,22.6841969 27.0741977,22.2797299 C28.4967877,21.7311761 28.7286504,19.0052295 27.5940189,16.1944399 C26.4586395,13.3821876 24.385338,11.5463609 22.9642439,12.0949147 Z M23.5857856,16.001349 C24.5109681,16.0023233 25.2895352,16.6791035 25.398962,17.5774776 C25.5083887,18.4758517 24.9141691,19.3125313 24.0151056,19.5259899 C23.6523598,19.0399655 23.3577951,18.5086331 23.1392629,17.946155 C22.9277397,17.3902186 22.7826721,16.8122262 22.7069511,16.2236962 C22.9679836,16.0803408 23.2679093,16.001349 23.5857856,16.001349 Z" id="icon_云广播_24_n" fill="#FFFFFF"></path>
            </g>
        </g>
    </g>
</svg>