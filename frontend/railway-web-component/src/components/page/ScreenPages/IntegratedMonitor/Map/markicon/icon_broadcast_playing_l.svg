<?xml version="1.0" encoding="UTF-8"?>
<svg width="62px" height="93px" viewBox="0 0 62 93" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>icon_云广播_播放中_80_s</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="98.890959%" id="linearGradient-1">
            <stop stop-color="#1BF98E" stop-opacity="0.147385817" offset="0%"></stop>
            <stop stop-color="#29D980" stop-opacity="0.623991853" offset="45.4628257%"></stop>
            <stop stop-color="#90FFD6" stop-opacity="0.663027036" offset="100%"></stop>
        </linearGradient>
        <filter x="-74.3%" y="-8.3%" width="248.6%" height="116.7%" filterUnits="objectBoundingBox" id="filter-2">
            <feGaussianBlur stdDeviation="1.02857143" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="93.6372478%" id="linearGradient-3">
            <stop stop-color="#3AE58B" stop-opacity="0.37038523" offset="0%"></stop>
            <stop stop-color="#0AE0E7" stop-opacity="0.8" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="-3.1396872%" id="linearGradient-4">
            <stop stop-color="#3FDD6B" offset="0%"></stop>
            <stop stop-color="#AB9B19" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <ellipse id="path-5" cx="26.7692308" cy="70.5" rx="20.5" ry="9.5"></ellipse>
        <filter x="-21.3%" y="-30.2%" width="142.7%" height="192.0%" filterUnits="objectBoundingBox" id="filter-6">
            <feGaussianBlur stdDeviation="1.41" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <filter x="-29.9%" y="-48.7%" width="159.7%" height="228.9%" filterUnits="objectBoundingBox" id="filter-7">
            <feMorphology radius="0.514285714" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
            <feOffset dx="0" dy="3" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.3 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="93.6372478%" id="linearGradient-8">
            <stop stop-color="#3AE58B" stop-opacity="0.37038523" offset="0%"></stop>
            <stop stop-color="#0AE0E7" stop-opacity="0.8" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="-3.1396872%" id="linearGradient-9">
            <stop stop-color="#3FDD6B" offset="0%"></stop>
            <stop stop-color="#AB9B19" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <ellipse id="path-10" cx="27.2692308" cy="67.8285714" rx="14" ry="6"></ellipse>
        <filter x="-28.6%" y="-41.8%" width="157.2%" height="233.6%" filterUnits="objectBoundingBox" id="filter-11">
            <feMorphology radius="0.514285714" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
            <feOffset dx="0" dy="3" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.3 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-12">
            <stop stop-color="#3ACFE5" stop-opacity="0.4" offset="0%"></stop>
            <stop stop-color="#00313F" offset="100%"></stop>
        </linearGradient>
        <ellipse id="path-13" cx="27" cy="26.7428571" rx="27" ry="26.7428571"></ellipse>
        <filter x="-13.9%" y="-8.4%" width="127.8%" height="128.0%" filterUnits="objectBoundingBox" id="filter-14">
            <feOffset dx="0" dy="3" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.22745098   0 0 0 0 0.806306856   0 0 0 0 0.898039216  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="5.08795696%" x2="50%" y2="100%" id="linearGradient-15">
            <stop stop-color="#3AE57B" offset="0%"></stop>
            <stop stop-color="#3AE568" stop-opacity="0.276665961" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-16">
            <stop stop-color="#FFFFFF" stop-opacity="0.167855216" offset="0%"></stop>
            <stop stop-color="#3ACAE5" offset="100%"></stop>
        </linearGradient>
        <ellipse id="path-17" cx="27" cy="26.7428571" rx="23.8846154" ry="23.6571429"></ellipse>
        <filter x="-17.9%" y="-11.7%" width="135.7%" height="136.1%" filterUnits="objectBoundingBox" id="filter-18">
            <feMorphology radius="1.02857143" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
            <feOffset dx="0" dy="3" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.5 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="1.41712536%" x2="50%" y2="96.2653678%" id="linearGradient-19">
            <stop stop-color="#21993D" offset="0%"></stop>
            <stop stop-color="#32D785" offset="62.4393063%"></stop>
            <stop stop-color="#32D2D6" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="44.4796397%" y1="17.8031512%" x2="44.4796397%" y2="78.3504117%" id="linearGradient-20">
            <stop stop-color="#E8F3FE" stop-opacity="0.2" offset="0%"></stop>
            <stop stop-color="#3AE5DC" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="综合监测-大屏" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="icon_云广播_播放中_80_s" transform="translate(4.000000, 1.000000)">
            <rect id="矩形" fill="url(#linearGradient-1)" filter="url(#filter-2)" x="24.9230769" y="31.8857143" width="4.15384615" height="37.0285714" rx="1.54285714"></rect>
            <g id="椭圆形" opacity="0.5" filter="url(#filter-6)">
                <use fill="black" fill-opacity="1" filter="url(#filter-7)" xlink:href="#path-5"></use>
                <use stroke="url(#linearGradient-4)" stroke-width="1.02857143" fill-opacity="0.8" fill="url(#linearGradient-3)" fill-rule="evenodd" xlink:href="#path-5"></use>
            </g>
            <g id="椭圆形">
                <use fill="black" fill-opacity="1" filter="url(#filter-11)" xlink:href="#path-10"></use>
                <use stroke="url(#linearGradient-9)" stroke-width="1.02857143" fill-opacity="0.8" fill="url(#linearGradient-8)" fill-rule="evenodd" xlink:href="#path-10"></use>
            </g>
            <g id="椭圆形" opacity="0.469350179">
                <use fill="black" fill-opacity="1" filter="url(#filter-14)" xlink:href="#path-13"></use>
                <use fill="url(#linearGradient-12)" fill-rule="evenodd" xlink:href="#path-13"></use>
            </g>
            <g id="椭圆形">
                <use fill="black" fill-opacity="1" filter="url(#filter-18)" xlink:href="#path-17"></use>
                <use stroke="url(#linearGradient-16)" stroke-width="2.05714286" fill="url(#linearGradient-15)" fill-rule="evenodd" xlink:href="#path-17"></use>
            </g>
            <rect id="矩形" stroke="url(#linearGradient-20)" stroke-width="1.02857143" fill="url(#linearGradient-19)" x="6" y="6" width="41.5384615" height="41.1428571" rx="20.5714286"></rect>
            <path d="M34.204592,15.9691544 L34.4575187,16.2424439 L34.5956134,16.4005112 C35.449046,17.3947689 36.2164368,18.6548753 36.7944715,20.0970387 C38.4715762,24.2726679 37.9301714,28.396279 35.5885716,29.3051759 L35.3621932,29.3779784 C35.1048552,29.4516754 34.8372171,29.4849386 34.5635021,29.4798451 C34.1607789,29.5058775 33.7048645,29.5081856 33.2490193,29.5025925 L31.7384184,29.4758792 L31.7384184,29.4758792 L31.2169442,29.4820372 C31.0843613,29.4938735 30.9411518,29.4751583 30.7898246,29.4288761 C29.485972,29.4749613 28.243999,29.595853 27.0530141,29.7696762 C28.2137049,31.2517206 29.2771383,32.8761716 29.7285324,34.1007216 L29.8371993,34.4219417 C30.003802,34.843382 30.1149017,35.2345221 30.1543291,35.580442 L30.2146626,36.1757309 L30.2496319,36.6883505 C30.3158132,38.1088541 30.0303945,38.5030775 29.3107317,38.6788083 L29.0546476,38.730401 L28.7657171,38.7724575 L28.3527383,38.8142081 C27.2717604,38.8907019 26.3801975,38.6627455 26.3266019,37.5129856 L26.3260298,37.2989286 C26.3426606,36.8293459 26.2616439,36.0603368 26.0857002,35.1622685 L26.0096493,35.2096953 C25.9855592,34.8858943 25.9167404,34.4266698 25.7905431,33.886415 C25.532223,32.9164275 25.182958,31.8975306 24.7448775,30.99189 C24.6205936,30.7507385 24.4871599,30.5080851 24.3426778,30.2685798 L23.620929,30.4325485 C22.5227331,29.9341212 21.2937538,28.418032 20.4907822,26.4205397 C19.6906956,24.4230473 19.5358711,22.4917596 19.9897664,21.3908689 C22.0956096,20.2198997 24.2959227,18.7556944 26.4507976,16.8509396 C26.4802017,16.8148246 26.5107709,16.7857264 26.5423993,16.7612642 L27.4080313,15.8995787 C27.6651195,15.6461496 27.9424352,15.3789745 28.2168431,15.131263 L28.5177173,14.8643303 L28.5455009,14.8356513 C28.7624859,14.6068179 29.0180275,14.4174781 29.3002633,14.2756602 L29.5177216,14.1783325 C30.9294038,13.6309553 32.6745206,14.3760693 34.204592,15.9691544 Z M18.9108034,21.9734709 C18.64539,23.3004036 18.8377186,25.0406432 19.541641,26.7922321 C20.246525,28.5457127 21.3139483,29.9454706 22.4294538,30.7370904 L21.702452,30.9338132 C20.3657687,31.4521019 18.5097984,29.9426332 17.5558489,27.562099 C16.5999762,25.1815647 16.9077018,22.8322413 18.2424618,22.3139525 L18.9108034,21.9734709 Z M29.9100718,15.1572173 C28.5791584,15.6726687 28.3339396,19.734804 29.5994613,23.0232893 C30.9207582,26.453642 33.8479984,28.8502546 35.1942981,28.3272369 C37.0233424,27.6179002 37.3214516,24.0929692 35.8626397,20.4583275 C34.4028662,16.8217942 31.7371928,14.4478805 29.9100718,15.1572173 Z M30.7091969,20.208641 C31.8987173,20.2099008 32.8997321,21.0850476 33.0404236,22.2467382 C33.1811152,23.4084289 32.4171185,24.4903421 31.2611797,24.7663663 C30.7947922,24.1378864 30.4160662,23.4508187 30.1350962,22.7234763 C29.8631378,22.004593 29.6766223,21.257189 29.5792668,20.4961588 C29.91488,20.3107855 30.3004987,20.208641 30.7091969,20.208641 Z" id="icon_云广播_24_n" fill="#FFFFFF"></path>
        </g>
    </g>
</svg>