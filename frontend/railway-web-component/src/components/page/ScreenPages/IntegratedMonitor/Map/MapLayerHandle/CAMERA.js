import { getMetaByDicCode } from '@/api/service/imScreenService';
import CommonMap from '@/components/common/Map/CommonMap';
import { isNotEmpty } from '@/components/common/utils';
import {
  enums
} from '@/components/page/ScreenPages/IntegratedMonitor/DetailInfo/enums';
import CamerasList
  from '@/components/page/ScreenPages/IntegratedMonitor/InfoWindow/CamerasList.vue';
import LayerConst
  from '@/components/page/ScreenPages/IntegratedMonitor/Map/LayerConst';
import {
  baseMarker, calMakerStyle, closeLeftSideWindowByIdPrefix, clusterTip,
  openLeftSideWidow
} from '@/components/page/ScreenPages/IntegratedMonitor/Map/MapLayerHandle/CommonHandle';
import {
  cameraIcon
} from '@/components/page/ScreenPages/IntegratedMonitor/Map/marker';
import { createSource, useClusterLayer } from '@/utils/map3.0';
import CaptureInfoWin from '@/components/page/ScreenPages/IntegratedMonitor/InfoWindow/CaptureInfoWin.vue';
import vue from '@/main'; // 导入Vue实例
// import { cameraData } from '@/components/page/ScreenPages/IntegratedMonitor/mockData'
import {
  transformPointToMercator,
} from '@/utils/index.js'

const CAMERA = {
  largeAnchor: [30, 70], // 大图标的锚点位置
  smallAnchor: [23, 55], // 小图标的点位置
  overlayOffset: [-85, -55], // 过车信息小弹窗的偏移量
  selOverlayOffset: [-120, -70], // 过车信息小弹窗的偏移量
  cfg: {
    markers: [],
    tipIdSuffix: 'camera-tip',
    showFault: false,
    showCaptureWin: true,
    captureWinOverlayIns: {}, // key-id,value-{overlayIns-overlay的实例,componentIns-组件实例}
  },
  /**
   * 绘制地图图标
   * @param markersLayer
   * @param onclick
   * @param initData
   * @returns {{markersLayer: CTMapOl.layer.Vector, markers: any[]}}
   */
  async draw({
    onclick = () => {
      console.log('default implement');
    },
    initData = null,
    region = {},
    mapInstance
  }) {
    let layerData = initData;
    if (!layerData) {
      const [, resData] = await getMetaByDicCode({
        layerCode: LayerConst.CAMERA,
        checkMode: '1',
        ...region
      });
      // 测试数据 resData?.length ? resData : cameraData.data
      const data = resData
      if (data) {
        layerData = data;
      }
    }
    if (!layerData || layerData.length < 1) {
      return;
    }
    const points = layerData.map((item) => {
      const result = {
        id: item.siteCode,
        lng: item.longitude * 1,
        lat: item.latitude * 1,
        location: item.location,
        list: item.list
      };
      const { list } = item;
      if (!list || list.length < 1) {
        return result;
      }
      const {
        deviceCode,
        deviceName,
        height,
        deviceStatus,
        channelInfoVOList
      } = list[0];
      result.deviceCode = deviceCode;
      result.deviceName = deviceName;
      result.height = height;
      result.deviceStatus = deviceStatus;
      if (channelInfoVOList && channelInfoVOList.length > 0) {
        const {
          channelCode,
          channelName,
          channelType,
          visualRange,
          horizRange,
          vertiRange
        } = channelInfoVOList[0];
        result.channelCode = channelCode;
        result.channelName = channelName;
        result.channelType = channelType;
        result.visualRange = visualRange;
        result.horizRange = horizRange;
        result.vertiRange = vertiRange;
      }
      return result;
    });
    const markers = [];
    const faultMarkers = [];
    points.map((item, index) => {
      const [small, large] = this.getIcon(item);
      // console.log('transformPointToMercator([item.lng, item.lat])--', transformPointToMercator([item.lng, item.lat]))
      // 转换座标
      const point = transformPointToMercator([item.lng, item.lat]);
      const marker = baseMarker({
        point,
        markerAttr: {
          id: item.id,
          lng: point[0],
          lat: point[1],
          markerType: LayerConst.CAMERA,
          deviceCode: item.deviceCode,
          ds: item.list
        },
        initIcon: small,
        selectedIcon: large,
        anchorCfg: this,
        onclick: (attr, markerFeature) => {
          onclick(attr, markerFeature);
          this.onclick(attr, marker);
        }
      });
      // 分组故障状态的marker
      if ([
        enums.CAMERA.deviceStatusEnum.onLine.code
      ].includes(item.deviceStatus)) {
        markers.push(marker);
        return;
      }
      faultMarkers
        .push(marker);
      return null;
    });
    this.cfg.faultMarkers = faultMarkers;
    this.cfg.otherMarkers = markers;
    const showMarkers = this.getMarker();
    this.cfg.markersLayer = useClusterLayer({
      markers: showMarkers,
      map: mapInstance,
      type: LayerConst.CAMERA,
      onclick: this.onClusterClick
    });
    return {
      markersLayer: this.cfg.markersLayer,
      markers
    };
  },
  /**
   * 更新icon，当打开过车信息时，会调用此方法，替换图标
   * 当存在过车数据时，点位上方增加overlay的渲染，展示过车视频/图片
   */
  updateCaptureMarkers(needOpenDevice, needCloseDevice) {
    // 先处理关闭
    needCloseDevice.forEach(deviceCode => {
      const overlayId = `CaptureInfoWin_${deviceCode}`;
      // 过车小弹窗是否已经关闭了，是否存在
      const { overlayIns, componentIns } = this.cfg.captureWinOverlayIns?.[overlayId] || {};
      // 关闭
      if (overlayIns && componentIns) {
        vue.$store.state.map.mapStoreRef.mapInstance.removeOverlay(overlayIns);
        componentIns.$destroy();
        delete this.cfg.captureWinOverlayIns[overlayId];
      }
    });
    // 初始图标，同时补充需要打开过车弹窗的数据
    const openDeviceArr = [];
    const selectedDeviceCodes = [];
    // 获取所有标记
    const markers = this.getMarker();
    // 初始化marker点
    markers.forEach(marker => {
      const [small, large] = this.getIcon(marker.values_.attributes.ds[0]);
      let icon = small;
      let anchor = this.smallAnchor;
      if (marker.values_.attributes.selected) {
        icon = large;
        anchor = this.largeAnchor;
        selectedDeviceCodes.push(marker.values_.attributes.deviceCode);
      }
      marker.style_ = calMakerStyle(icon, anchor);
      if (needOpenDevice.includes(marker.values_.attributes.deviceCode)) {
        openDeviceArr.push(marker.values_.attributes);
      }
    });
    this.cfg.markersLayer.setSource(createSource({
      markers,
      type: LayerConst.CAMERA,
      onclick: this.onClusterClick
    }));
    // 打开过车弹窗
    openDeviceArr.forEach(device => {
      const { deviceCode, lng, lat } = device;
      const overlayId = `CaptureInfoWin_${deviceCode}`;
      // 渲染动效
      const offsetX = selectedDeviceCodes.includes(deviceCode) ? this.selOverlayOffset[0] : this.overlayOffset[0];
      const offsetY = selectedDeviceCodes.includes(deviceCode) ? this.selOverlayOffset[1] : this.overlayOffset[1];
      const compInsArr = CommonMap.componentToHtml({
        component: CaptureInfoWin,
        props: {
          deviceCode
        },
        onClose: () => {
          return null;
        }
      });
      // 绘制过车视频、图片小弹窗
      const overlayIns = CommonMap.infoWindow(
        lng * 1,
        lat * 1,
        compInsArr,
        'popup',
        offsetX,
        offsetY,
        overlayId,
        vue.$store.state.map.mapStoreRef.mapInstance,
        false,
        'mainMap',
        () => {
          console.log('default implement');
        },
        false
      );
      this.cfg.captureWinOverlayIns[overlayId] = {
        overlayIns,
        componentIns: compInsArr[0]
      };
    })

  },
  /**
   * 隐藏所有的抓拍信息弹窗
   * @returns
   */
  hiddenAllCaptureWin() {
    // 本身就隐藏了
    if (!this.cfg.showCaptureWin) {
      return;
    }
    Object.keys(this.cfg.captureWinOverlayIns).forEach(id => {
      this.cfg.captureWinOverlayIns[id].overlayIns.getElement().style.display = 'none';
    });
    this.cfg.showCaptureWin = false;
  },
  /**
   * 展示所有的抓拍信息弹窗
   * @returns
   */
  showAllCaptureWin() {
    // 本身就显示了
    if (this.cfg.showCaptureWin) {
      return;
    }
    Object.keys(this.cfg.captureWinOverlayIns).forEach(id => {
      this.cfg.captureWinOverlayIns[id].overlayIns.getElement().style.display = 'block';
    });
    this.cfg.showCaptureWin = true;
  },
  removeAllCaptureWin() {
    Object.keys(this.cfg.captureWinOverlayIns).forEach(id => {
      const { overlayIns, componentIns } = this.cfg.captureWinOverlayIns[id];
      vue.$store.state.map.mapStoreRef.mapInstance.removeOverlay(overlayIns);
      componentIns.$destroy();
      delete this.cfg.captureWinOverlayIns[id];
    });
  },
  onclick(attr, marker) {
    const {
      selected
    } = attr;
    if (selected) {
      // 必须在清除窗体之后，否则窗体关闭会出问题
      this.onSelected(marker);
      return;
    }
    this.cancelSelected();
  },
  /**
   * 初始化标记点
   * @param {Object[]} markers - 标记点数组
   */
  initMarkers(markers) {
    // 初始化marker点
    markers.forEach(marker => {
      const [small] = this.getIcon(marker.values_.attributes.ds[0]);
      const { deviceCode } = marker.values_.attributes;
      // 调整播放动效的偏移
      if (this.cfg.captureWinOverlayIns[`CaptureInfoWin_${deviceCode}`]) {
        this.cfg.captureWinOverlayIns[`CaptureInfoWin_${deviceCode}`].overlayIns.setOffset(this.overlayOffset);
      }
      marker.values_.attributes.selected = false;
      marker.style_ = calMakerStyle(small, this.smallAnchor);
    });
  },
  /**
   * 标记点被选中时的处理
   * @param {Object} marker - 被选中的标记点
   */
  onSelected(marker) {
    const [, large] = this.getIcon(marker.values_.attributes.ds[0]);
    const markers = this.getMarker();
    const selectedMarker = markers.find(
      item => item.values_.attributes.id === marker.values_.attributes.id);
    selectedMarker.style_ = calMakerStyle(large, this.largeAnchor);
    this.cfg.markersLayer.setSource(createSource({
      markers,
      type: LayerConst.CAMERA,
      onclick: this.onClusterClick
    }));
    const {
      lng,
      lat
    } = marker.values_.attributes;
    this.openTip(marker.values_.attributes.ds, lng, lat);
    const { deviceCode } = selectedMarker.values_.attributes;
    // 调整播放动效的偏移
    if (this.cfg.captureWinOverlayIns[`CaptureInfoWin_${deviceCode}`]) {
      this.cfg.captureWinOverlayIns[`CaptureInfoWin_${deviceCode}`].overlayIns.setOffset(this.selOverlayOffset);
    }
  },
  /**
   * 取消选中状态
   */
  cancelSelected() {
    const markers = this.getMarker();
    if (!isNotEmpty(markers)) {
      return;
    }
    this.initMarkers(markers);
    this.cfg.markersLayer.setSource(createSource({
      markers,
      type: LayerConst.CAMERA,
      onclick: this.onClusterClick
    }));
    closeLeftSideWindowByIdPrefix(LayerConst.CAMERA, LayerConst.CAMERA);
  },
  /**
   * 获取标记点数组
   * @returns {Object[]} - 返回标记点数组
   */
  getMarker() {
    const {
      faultMarkers,
      otherMarkers,
      showFault
    } = this.cfg;
    if (showFault) {
      return [...faultMarkers, ...otherMarkers];
    }
    return otherMarkers;
  },
  /**
   * 清除所有标记点和相关信息
   */
  clear() {
    closeLeftSideWindowByIdPrefix(`${LayerConst.CAMERA}-`, LayerConst.CAMERA);
  },
  /**
   * 根据设备状态获取标记点图标
   * @param {Object} data - 设备数据
   * @returns {Object[]} - 返回图标信息数组
   */
  getIcon(data) {
    const { deviceCode, deviceStatus } = data;
    // 当前渲染的抓拍点位
    const captureDevcs = vue.$store.state.captureInfo.curRenderCaptureDevc;
    if (captureDevcs.includes(deviceCode)) {
      return [cameraIcon.iconCaptureCameraS, cameraIcon.iconCaptureCameraL]
    }
    if (deviceStatus === enums.CAMERA.deviceStatusEnum.onLine.code) {
      return [cameraIcon.iconCameraS, cameraIcon.iconCameraL];
    }
    if (deviceStatus === enums.CAMERA.deviceStatusEnum.offLine.code) {
      return [cameraIcon.iconCameraOffS, cameraIcon.iconCameraOffL];
    }
    return [cameraIcon.iconCameraOffS, cameraIcon.iconCameraOffL];
  },
  /**
   * 打开提示框
   * @param {Object[]} ds - 设备数据数组
   * @param {number} lng - 经度
   * @param {number} lat - 纬度
   */
  openTip(ds, lng, lat) {
    if (ds.length === 1) {
      const selectedData = ds[0];
      this.openDetail(selectedData, (key, param) => {
        if (param && param.destroy) {
          return;
        }
        this.cancelSelected();
      });
      return;
    }
    const winComponent = CommonMap.componentToHtml({
      component: CamerasList,
      props: ds,
      onSelected: (seletedData) => {
        const markerId = `${LayerConst.CAMERA}-${seletedData.deviceCode}`;
        // 打开详情页面
        openLeftSideWidow({
          windowId: markerId,
          props: {
            ds: { ...seletedData },
            markerType: LayerConst.CAMERA
          },
          group: LayerConst.CAMERA, // 详情页面关闭回调
          onclose: (key, param) => {
            if (param && param.destroy) {
              return;
            }
            const [, , contentRef] = winComponent;
            contentRef.clear();
          }
        });
      }
    });
    CommonMap.mapTip({
      lng,
      lat,
      content: winComponent,
      mapinstance: vue.$store.state.map.mapStoreRef.mapInstance
    });
  }, // 故障检测
  /**
   * 过滤故障标记点
   * @param {Object} layer - 层信息
   * @param {boolean} show - 是否显示故障点
   */
  filterFault({
    layer,
    show
  }) {
    this.cfg.showFault = show;
    const markers = this.getMarker();
    this.initMarkers(markers);
    this.cfg.markersLayer.setSource(createSource({
      markers,
      type: LayerConst.CAMERA,
      onclick: this.onClusterClick
    }));
    closeLeftSideWindowByIdPrefix(`${LayerConst.CAMERA}-`, LayerConst.CAMERA);
  },
  /**
   * 打开设备详情页面
   * @param {Object} selectedData - 选中的设备数据
   * @param {Function} onclose - 关闭详情页面的回调函数
   */
  openDetail(selectedData, onclose) {
    const markerId = `${LayerConst.CAMERA}-${selectedData.deviceCode}`;
    openLeftSideWidow({
      windowId: markerId,
      props: {
        ds: { ...selectedData },
        markerType: LayerConst.CAMERA
      },
      group: LayerConst.CAMERA, // 详情页面关闭回调
      onclose
    });
  },
  /**
   * 处理集群点击事件
   * @param {Object} event - 点击事件对象
   */
  onClusterClick({
    features,
    geometry
  }) {
    if (!features || features.length < 1) {
      return;
    }
    const ds = features.map(feature => {
      const data = feature.values_.attributes.ds[0];
      return {
        key: data.deviceCode,
        type: '摄像机',
        name: data.deviceName,
        status: data.deviceStatus,
        statusName: enums.CAMERA.deviceStatus[data.deviceStatus]?.name,
        color: enums.CAMERA.deviceStatus[data.deviceStatus]?.color, ...data
      };
    });
    const [lng, lat] = geometry.getCoordinates();
    clusterTip({
      ds,
      markerType: LayerConst.CAMERA,
      lng,
      lat
    });
  }
};
export default CAMERA;
