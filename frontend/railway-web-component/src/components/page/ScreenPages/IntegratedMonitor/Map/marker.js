/**
 * 导入各种标记图标的SVG和GIF文件。
 * 这些图标用于表示不同的广播状态、相机状态、事件类型和路线标记。
 * 使用命名导出将这些图标组织成不同的类别，以便在应用程序中根据需要导入和使用。
 */
// 导入表示广播错误状态的大型和小型图标
import iconBroadcastErrL from './markicon/icon_broadcast_err_l.svg';
import iconBroadcastErrS from './markicon/icon_broadcast_err_s.svg';

// 导入表示广播关闭状态的大型和小型图标
import iconBroadcastOffL from './markicon/icon_broadcast_off_l.svg';
import iconBroadcastOffS from './markicon/icon_broadcast_off_s.svg';

// 导入表示广播开启状态的大型和小型图标
import iconBroadcastOnL from './markicon/icon_broadcast_on_l.svg';
import iconBroadcastOnS from './markicon/icon_broadcast_on_s.svg';

// 导入表示广播播放状态的大型和小型图标，以及播放波纹效果的GIF图标
import iconBroadcastPlayingL from './markicon/icon_broadcast_playing_l.svg';
import iconBroadcastPlayingS from './markicon/icon_broadcast_playing_s.svg';
import iconBroadcastPlayingWave from './markicon/icon_broadcast_playing_wave.gif';

// 导入表示相机状态的大型和小型图标
import iconCameraL from './markicon/icon_camera_l.svg';
import iconCameraOffL from './markicon/icon_camera_off_l.svg';
import iconCameraOffS from './markicon/icon_camera_off_s.svg';
import iconCameraS from './markicon/icon_camera_s.svg';
// 抓拍相机
import iconCaptureCameraL from './markicon/icon_capture_camera_l.svg';
import iconCaptureCameraS from './markicon/icon_capture_camera_s.svg';

// 导入表示当前点和通过路线站的图标
import iconCurrentPoint from './markicon/icon_current_point.png';
import iconEvtAlarmL from './markicon/icon_evt_alarm_l.svg';
import iconEvtAlarmS from './markicon/icon_evt_alarm_s.svg';
import iconEvtHiddenL from './markicon/icon_evt_hidden_l.svg';
import iconEvtHiddenS from './markicon/icon_evt_hidden_s.svg';
import iconPassRouteStation from './markicon/icon_pass_route_station.svg';

// 不同类型的事件图标
import iconEvtFFSRL from './markicon/icon_非法闯入_80_n.svg';
import iconEvtFFSRS from './markicon/icon_非法闯入_40_n.svg';
import iconEvtPFWL from './markicon/icon_漂浮物_80_n.svg';
import iconEvtPFWS from './markicon/icon_漂浮物_40_n.svg';
import iconEvtWFPFDFL from './markicon/icon_违法排放堆放_80_n.svg';
import iconEvtWFPFDFS from './markicon/icon_违法排放堆放_40_n.svg';
import iconEvtWFSGL from './markicon/icon_违法施工_80_n.svg';
import iconEvtWFSGS from './markicon/icon_违法施工_40_n.svg';
import iconEvtQTL from './markicon/icon_其他_80_n.svg'; // icon_evt_alarm_l
import iconEvtQTS from './markicon/icon_其他_40_n.svg';

// 导入表示畜牧相关的图标
import iconAnimalNormal from './markicon/icon_animal_normal.svg';
import iconAnimalNormalSelected from './markicon/icon_animal_normal_s.svg';
import iconAnimalUnnormal from './markicon/icon_animal_unnormal.svg';
import iconAnimalUnnormalSelected from './markicon/icon_animal_unnormal_s.svg';

export const broadcastIcon = {
  iconBroadcastErrL,
  iconBroadcastErrS,
  iconBroadcastOnL,
  iconBroadcastOnS,
  iconBroadcastOffS,
  iconBroadcastOffL,
  iconBroadcastPlayingS,
  iconBroadcastPlayingL,
  iconBroadcastPlayingWave,
};

// 导出相机图标对象，包含各种相机状态的图标
export const cameraIcon = {
  iconCameraL,
  iconCameraS,
  iconCameraOffS,
  iconCameraOffL,
  iconCaptureCameraL,
  iconCaptureCameraS
};

// 导出事件图标对象，包含不同类型的事件图标
export const evtIcon = {
  iconEvtAlarmL,
  iconEvtHiddenL,
  iconEvtHiddenS,
  iconEvtAlarmS,
};

// 不同类型的事件
export const evtTypesIcon = {
  '非法闯入': [iconEvtFFSRS, iconEvtFFSRL],
  '漂浮物': [iconEvtPFWS, iconEvtPFWL],
  '违法排放堆放': [iconEvtWFPFDFS, iconEvtWFPFDFL],
  '违法施工': [iconEvtWFSGS, iconEvtWFSGL],
  '其他': [iconEvtQTS, iconEvtQTL],
};

// 导出路线图标对象，包含当前点和通过路线站的图标
export const passRouteIcon = {
  iconPassRouteStation,
  iconCurrentPoint,
};

// 导出畜牧图标对象，包含各种畜牧状态的图标
export const animalIcon = {
  iconAnimalNormal,
  iconAnimalNormalSelected,
  iconAnimalUnnormal,
  iconAnimalUnnormalSelected,
};
