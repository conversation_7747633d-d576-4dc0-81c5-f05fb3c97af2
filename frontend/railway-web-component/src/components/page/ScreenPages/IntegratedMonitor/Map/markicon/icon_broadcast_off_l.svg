<?xml version="1.0" encoding="UTF-8"?>
<svg width="62px" height="87px" viewBox="0 0 62 87" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>icon_云广播_离线_80_s</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="97.230474%" id="linearGradient-1">
            <stop stop-color="#D5D5D5" stop-opacity="0.147385817" offset="0%"></stop>
            <stop stop-color="#D5D5D5" offset="51.4224179%"></stop>
            <stop stop-color="#D5D5D5" stop-opacity="0.898221529" offset="100%"></stop>
        </linearGradient>
        <filter x="-76.3%" y="-8.5%" width="252.5%" height="117.0%" filterUnits="objectBoundingBox" id="filter-2">
            <feGaussianBlur stdDeviation="1.056" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="0%" id="linearGradient-3">
            <stop stop-color="#D5D5D5" offset="0%"></stop>
            <stop stop-color="#D5D5D5" stop-opacity="0" offset="97.5113022%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="0.871394231%" id="linearGradient-4">
            <stop stop-color="#D5D5D5" offset="0%"></stop>
            <stop stop-color="#00B7FF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <filter x="-11.9%" y="-25.5%" width="123.7%" height="151.0%" filterUnits="objectBoundingBox" id="filter-5">
            <feGaussianBlur stdDeviation="1.408" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="0%" id="linearGradient-6">
            <stop stop-color="#D5D5D5" offset="0%"></stop>
            <stop stop-color="#D5D5D5" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="0.871394231%" id="linearGradient-7">
            <stop stop-color="#D5D5D5" offset="0%"></stop>
            <stop stop-color="#D5D5D5" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-8">
            <stop stop-color="#D5D5D5" stop-opacity="0.4" offset="0%"></stop>
            <stop stop-color="#D5D5D5" offset="100%"></stop>
        </linearGradient>
        <ellipse id="path-9" cx="27" cy="26.8965517" rx="27" ry="26.8965517"></ellipse>
        <filter x="-13.0%" y="-9.3%" width="125.9%" height="126.0%" filterUnits="objectBoundingBox" id="filter-10">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.835294118   0 0 0 0 0.835294118   0 0 0 0 0.835294118  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="5.08795696%" x2="50%" y2="100%" id="linearGradient-11">
            <stop stop-color="#D5D5D5" offset="0%"></stop>
            <stop stop-color="#D5D5D5" stop-opacity="0.276665961" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-12">
            <stop stop-color="#FFFFFF" stop-opacity="0.167855216" offset="0%"></stop>
            <stop stop-color="#D5D5D5" offset="100%"></stop>
        </linearGradient>
        <ellipse id="path-13" cx="27" cy="26.8965517" rx="23.8846154" ry="23.7931034"></ellipse>
        <filter x="-16.9%" y="-12.7%" width="133.7%" height="133.9%" filterUnits="objectBoundingBox" id="filter-14">
            <feMorphology radius="1.056" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
            <feOffset dx="0" dy="2" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.5 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-15">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#424242" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="44.4796397%" y1="17.8031512%" x2="44.4796397%" y2="78.3504117%" id="linearGradient-16">
            <stop stop-color="#D5D5D5" stop-opacity="0.2" offset="0%"></stop>
            <stop stop-color="#D5D5D5" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="综合监测-大屏" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="1-6综合监测-弹框" transform="translate(-1193.000000, -786.000000)">
            <g id="icon_云广播_离线_80_s" transform="translate(1197.000000, 788.000000)">
                <rect id="矩形" fill="url(#linearGradient-1)" filter="url(#filter-2)" x="24.9230769" y="32.0689655" width="4.15384615" height="37.2413793" rx="1.584"></rect>
                <path d="M27,80 C15.5284062,80 6.23076923,75.6771476 6.23076923,70.3448276 C6.23076923,65.0125076 15.5284062,60.6896552 27,60.6896552 C38.4715938,60.6896552 47.7692308,65.0125076 47.7692308,70.3448276 C47.7692308,75.6771476 38.4715938,80 27,80 Z" id="椭圆_7_拷贝" stroke="url(#linearGradient-4)" stroke-width="1.408" fill="url(#linearGradient-3)" opacity="0.5" filter="url(#filter-5)"></path>
                <path d="M27,62.0689655 C34.6470092,62.0689655 40.8461538,64.8478924 40.8461538,68.2758621 C40.8461538,71.7038317 34.6470092,74.4827586 27,74.4827586 C19.3529908,74.4827586 13.1538462,71.7038317 13.1538462,68.2758621 C13.1538462,64.8478924 19.3529908,62.0689655 27,62.0689655 C27,62.0689655 27,62.0689655 27,62.0689655 Z" id="椭圆_7" stroke="url(#linearGradient-7)" stroke-width="1.408" fill="url(#linearGradient-6)"></path>
                <g id="编组-9">
                    <g id="椭圆形" opacity="0.469350179">
                        <use fill="black" fill-opacity="1" filter="url(#filter-10)" xlink:href="#path-9"></use>
                        <use fill="url(#linearGradient-8)" fill-rule="evenodd" xlink:href="#path-9"></use>
                    </g>
                    <g id="椭圆形">
                        <use fill="black" fill-opacity="1" filter="url(#filter-14)" xlink:href="#path-13"></use>
                        <use stroke="url(#linearGradient-12)" stroke-width="2.112" fill="url(#linearGradient-11)" fill-rule="evenodd" xlink:href="#path-13"></use>
                    </g>
                    <rect id="矩形" stroke="url(#linearGradient-16)" stroke-width="1.056" fill="url(#linearGradient-15)" x="6.23076923" y="6.20689655" width="41.5384615" height="41.3793103" rx="20.6896552"></rect>
                </g>
                <path d="M33.8199767,16.451913 L34.0729034,16.7252026 L34.210998,16.8832698 C35.0644306,17.8775275 35.8318214,19.1376339 36.4098561,20.5797973 C38.0869608,24.7554265 37.545556,28.8790377 35.2039562,29.7879345 L34.9775779,29.8607371 C34.7202398,29.934434 34.4526017,29.9676972 34.1788867,29.9626037 C33.7761635,29.9886361 33.3202491,29.9909442 32.8644039,29.9853511 L31.3538031,29.9586378 L31.3538031,29.9586378 L30.8323288,29.9647958 C30.6997459,29.9766321 30.5565364,29.9579169 30.4052092,29.9116347 C29.1013567,29.9577199 27.8593836,30.0786116 26.6683987,30.2524348 C27.8290895,31.7344792 28.8925229,33.3589303 29.343917,34.5834802 L29.4525839,34.9047003 C29.6191866,35.3261406 29.7302864,35.7172807 29.7697137,36.0632006 L29.8300473,36.6584895 L29.8650165,37.1711091 C29.9311979,38.5916127 29.6457791,38.9858361 28.9261163,39.1615669 L28.6700322,39.2131596 L28.3811017,39.2552162 L27.9681229,39.2969667 C26.887145,39.3734605 25.9955821,39.1455041 25.9419865,37.9957443 L25.9414144,37.7816872 C25.9580452,37.3121045 25.8770285,36.5430954 25.7010848,35.6450271 L25.6250339,35.6924539 C25.6009439,35.3686529 25.532125,34.9094285 25.4059277,34.3691736 C25.1476076,33.3991861 24.7983426,32.3802892 24.3602621,31.4746486 C24.2359782,31.2334971 24.1025446,30.9908437 23.9580624,30.7513384 L23.2363136,30.9153071 C22.1381177,30.4168798 20.9091384,28.9007907 20.1061669,26.9032983 C19.3060802,24.9058059 19.1512557,22.9745183 19.605151,21.8736276 C21.7109943,20.7026583 23.9113073,19.238453 26.0661822,17.3336982 C26.0955863,17.2975832 26.1261555,17.2684851 26.157784,17.2440228 L27.023416,16.3823373 C27.2805041,16.1289082 27.5578198,15.8617331 27.8322277,15.6140216 L28.1331019,15.3470889 L28.1608856,15.3184099 C28.3778705,15.0895765 28.6334121,14.9002368 28.9156479,14.7584188 L29.1331063,14.6610911 C30.5447885,14.1137139 32.2899052,14.8588279 33.8199767,16.451913 Z M18.526188,22.4562295 C18.2607746,23.7831622 18.4531032,25.5234018 19.1570256,27.2749908 C19.8619096,29.0284713 20.9293329,30.4282292 22.0448384,31.2198491 L21.3178366,31.4165718 C19.9811533,31.9348605 18.125183,30.4253919 17.1712335,28.0448576 C16.2153608,25.6643233 16.5230864,23.3149999 17.8578464,22.7967112 L18.526188,22.4562295 Z M29.5254565,15.6399759 C28.1945431,16.1554273 27.9493242,20.2175626 29.2148459,23.5060479 C30.5361429,26.9364006 33.463383,29.3330132 34.8096827,28.8099956 C36.638727,28.1006588 36.9368362,24.5757278 35.4780244,20.9410861 C34.0182508,17.3045529 31.3525775,14.9306391 29.5254565,15.6399759 Z M30.3245815,20.6913996 C31.5141019,20.6926594 32.5151167,21.5678062 32.6558083,22.7294969 C32.7964998,23.8911875 32.0325031,24.9731008 30.8765644,25.2491249 C30.4101768,24.620645 30.0314508,23.9335773 29.7504808,23.206235 C29.4785224,22.4873517 29.292007,21.7399476 29.1946514,20.9789174 C29.5302647,20.7935441 29.9158834,20.6913996 30.3245815,20.6913996 Z" id="icon_云广播_24_n" fill="#FFFFFF"></path>
            </g>
        </g>
    </g>
</svg>