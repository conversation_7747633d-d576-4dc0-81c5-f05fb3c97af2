<?xml version="1.0" encoding="UTF-8"?>
<svg width="62px" height="93px" viewBox="0 0 62 93" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>icon_抓拍摄像机_80_n</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="98.890959%" id="linearGradient-1">
            <stop stop-color="#1BF98E" stop-opacity="0.147385817" offset="0%"></stop>
            <stop stop-color="#29D980" stop-opacity="0.623991853" offset="45.4628257%"></stop>
            <stop stop-color="#90FFD6" stop-opacity="0.663027036" offset="100%"></stop>
        </linearGradient>
        <filter x="-74.3%" y="-8.3%" width="248.6%" height="116.7%" filterUnits="objectBoundingBox" id="filter-2">
            <feGaussianBlur stdDeviation="1.02857143" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="93.6372478%" id="linearGradient-3">
            <stop stop-color="#3AE58B" stop-opacity="0.37038523" offset="0%"></stop>
            <stop stop-color="#0AE0E7" stop-opacity="0.8" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="-3.1396872%" id="linearGradient-4">
            <stop stop-color="#3FDD6B" offset="0%"></stop>
            <stop stop-color="#AB9B19" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <ellipse id="path-5" cx="26.7692308" cy="70.5" rx="20.5" ry="9.5"></ellipse>
        <filter x="-21.3%" y="-30.2%" width="142.7%" height="192.0%" filterUnits="objectBoundingBox" id="filter-6">
            <feGaussianBlur stdDeviation="1.41" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <filter x="-29.9%" y="-48.7%" width="159.7%" height="228.9%" filterUnits="objectBoundingBox" id="filter-7">
            <feMorphology radius="0.514285714" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
            <feOffset dx="0" dy="3" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.3 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="93.6372478%" id="linearGradient-8">
            <stop stop-color="#3AE58B" stop-opacity="0.37038523" offset="0%"></stop>
            <stop stop-color="#0AE0E7" stop-opacity="0.8" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="-3.1396872%" id="linearGradient-9">
            <stop stop-color="#3FDD6B" offset="0%"></stop>
            <stop stop-color="#AB9B19" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <ellipse id="path-10" cx="27.2692308" cy="67.8285714" rx="14" ry="6"></ellipse>
        <filter x="-28.6%" y="-41.8%" width="157.2%" height="233.6%" filterUnits="objectBoundingBox" id="filter-11">
            <feMorphology radius="0.514285714" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
            <feOffset dx="0" dy="3" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.3 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-12">
            <stop stop-color="#3ACFE5" stop-opacity="0.4" offset="0%"></stop>
            <stop stop-color="#00313F" offset="100%"></stop>
        </linearGradient>
        <ellipse id="path-13" cx="27" cy="26.7428571" rx="27" ry="26.7428571"></ellipse>
        <filter x="-13.9%" y="-8.4%" width="127.8%" height="128.0%" filterUnits="objectBoundingBox" id="filter-14">
            <feOffset dx="0" dy="3" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.22745098   0 0 0 0 0.806306856   0 0 0 0 0.898039216  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="5.08795696%" x2="50%" y2="100%" id="linearGradient-15">
            <stop stop-color="#3AE57B" offset="0%"></stop>
            <stop stop-color="#3AE568" stop-opacity="0.276665961" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-16">
            <stop stop-color="#FFFFFF" stop-opacity="0.167855216" offset="0%"></stop>
            <stop stop-color="#3ACAE5" offset="100%"></stop>
        </linearGradient>
        <ellipse id="path-17" cx="27" cy="26.7428571" rx="23.8846154" ry="23.6571429"></ellipse>
        <filter x="-17.9%" y="-11.7%" width="135.7%" height="136.1%" filterUnits="objectBoundingBox" id="filter-18">
            <feMorphology radius="1.02857143" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
            <feOffset dx="0" dy="3" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.5 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="1.41712536%" x2="50%" y2="96.2653678%" id="linearGradient-19">
            <stop stop-color="#21993D" offset="0%"></stop>
            <stop stop-color="#32D785" offset="62.4393063%"></stop>
            <stop stop-color="#32D2D6" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="44.4796397%" y1="17.8031512%" x2="44.4796397%" y2="78.3504117%" id="linearGradient-20">
            <stop stop-color="#E8F3FE" stop-opacity="0.2" offset="0%"></stop>
            <stop stop-color="#3AE5DC" offset="100%"></stop>
        </linearGradient>
        <path d="M20.9251997,29.9931429 L20.9251997,27.6788571 L19.2857143,27.3691837 L19.2857143,34.7142857 L20.9251997,34.0872245 L20.9251997,31.5679592 L23.5276624,31.5679592 L24.6845574,29.6779592 L23.2378878,28.7335102 L22.5613797,29.9931429 L20.9251997,29.9931429 Z M34.7142857,27.6788571 L22.949215,19.2857143 L21.6017078,19.2857143 L19.7694066,22.4353469 L19.7694066,23.4844898 L30.2751145,30.832898 L31.1433366,30.7226939 L34.7142857,27.6788571 Z M19.7649993,24.1148571 L19.7649993,25.7877551 L30.3720733,33.0314694 L31.3361525,33.0314694 L31.8187429,32.5102041 L32.3013334,30.9353878 C32.3013334,30.9353878 30.9516226,31.9856327 30.8546638,31.9856327 C30.3731751,31.5679592 19.767203,24.1148571 19.767203,24.1148571 L19.7649993,24.1148571 Z" id="path-21"></path>
        <filter x="-29.2%" y="-29.2%" width="158.3%" height="158.3%" filterUnits="objectBoundingBox" id="filter-22">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.309803922   0 0 0 0 0.623529412   0 0 0 0 1  0 0 0 0.7 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="综合监测-大屏" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="3-综合监测-过车信息+-摄像机详情" transform="translate(-1072.000000, -810.000000)">
            <g id="icon_抓拍摄像机_80_n" transform="translate(1076.000000, 811.000000)">
                <rect id="矩形" fill="url(#linearGradient-1)" filter="url(#filter-2)" x="24.9230769" y="31.8857143" width="4.15384615" height="37.0285714" rx="1.54285714"></rect>
                <g id="椭圆形" opacity="0.5" filter="url(#filter-6)">
                    <use fill="black" fill-opacity="1" filter="url(#filter-7)" xlink:href="#path-5"></use>
                    <use stroke="url(#linearGradient-4)" stroke-width="1.02857143" fill-opacity="0.8" fill="url(#linearGradient-3)" fill-rule="evenodd" xlink:href="#path-5"></use>
                </g>
                <g id="椭圆形">
                    <use fill="black" fill-opacity="1" filter="url(#filter-11)" xlink:href="#path-10"></use>
                    <use stroke="url(#linearGradient-9)" stroke-width="1.02857143" fill-opacity="0.8" fill="url(#linearGradient-8)" fill-rule="evenodd" xlink:href="#path-10"></use>
                </g>
                <g id="椭圆形" opacity="0.469350179">
                    <use fill="black" fill-opacity="1" filter="url(#filter-14)" xlink:href="#path-13"></use>
                    <use fill="url(#linearGradient-12)" fill-rule="evenodd" xlink:href="#path-13"></use>
                </g>
                <g id="椭圆形">
                    <use fill="black" fill-opacity="1" filter="url(#filter-18)" xlink:href="#path-17"></use>
                    <use stroke="url(#linearGradient-16)" stroke-width="2.05714286" fill="url(#linearGradient-15)" fill-rule="evenodd" xlink:href="#path-17"></use>
                </g>
                <rect id="矩形" stroke="url(#linearGradient-20)" stroke-width="1.02857143" fill="url(#linearGradient-19)" x="6.42857143" y="6.42857143" width="41.5384615" height="41.1428571" rx="20.5714286"></rect>
                <path d="M36.9516997,39.8570977 L31.0244887,39.8570977 C30.4966185,39.8449293 30.0711948,39.4206426 30.0576205,38.8928137 C30.0576205,38.3785289 30.5076256,37.9285297 31.0244887,37.9285297 L36.9516997,37.9285297 C37.467277,37.9285297 37.9185679,37.4785305 37.9185679,36.9642457 L37.9185679,32.0142543 C37.9185679,31.4999695 38.3685731,31.0499703 38.8854362,31.0499703 C39.3997278,31.0499703 39.8510187,31.4999695 39.8510187,32.0142543 L39.8510187,36.9642457 C39.9153052,38.5071001 38.5627182,39.8570977 36.9516997,39.8570977 Z M22.0681001,39.8571205 L17.1064716,39.8571205 C16.3206485,39.8601792 15.5660282,39.5498205 15.00976,38.9947664 C14.4534919,38.4397122 14.1414911,37.6857776 14.1428616,36.8999601 L14.1428616,31.9499687 C14.1428616,31.4356839 14.5928713,30.9856847 15.1097344,30.9856847 C15.6240261,30.9856847 16.075317,31.4356839 16.075317,31.9499687 L16.075317,36.8999601 C16.075317,37.4142449 16.5266079,37.8642441 17.0421852,37.8642441 L22.0038136,37.8642441 C22.5181052,37.8642441 22.9706819,38.3142433 22.9706819,38.8285281 C23.0349683,39.4070985 22.5836774,39.7928121 22.0681001,39.8571205 Z M15.1097344,23.0785557 C14.5818642,23.0663873 14.1564405,22.6421007 14.1428662,22.1142717 L14.1428662,17.0999948 C14.1428662,15.4928548 15.4954531,14.1428571 17.1064716,14.1428571 L22.0681001,14.1428571 C22.5823917,14.1428571 23.0349683,14.5928564 23.0349683,15.1071412 C23.0349683,15.621426 22.5836774,16.0714252 22.0681001,16.0714252 L17.1064716,16.0714252 C16.5791033,16.0842679 16.1544347,16.5083654 16.1408891,17.0357092 L16.1408891,21.9857005 C16.1408891,22.5642709 15.6895982,23.0142702 15.1097344,23.0785557 Z M38.8854362,23.0785557 C38.357566,23.0663873 37.9321422,22.6421006 37.9185679,22.1142717 L37.9185679,17.0999948 C37.9185679,16.58571 37.467277,16.1357108 36.9516997,16.1357108 L31.0244887,16.1357108 C30.4966185,16.1235424 30.0711948,15.6992557 30.0576205,15.1714268 C30.0576205,14.657142 30.4433392,14.1428571 30.9602023,14.1428571 L36.8874132,14.1428571 C38.5627182,14.1428571 39.8510187,15.4285692 39.8510187,17.0999948 L39.8510187,22.0499861 C39.9153052,22.564271 39.4653,23.0142702 38.8854362,23.0785557 L38.8854362,23.0785557 Z" id="形状" fill="#FFFFFF" fill-rule="nonzero"></path>
                <g id="icon_摄像机_18_n" fill-rule="nonzero">
                    <use fill="black" fill-opacity="1" filter="url(#filter-22)" xlink:href="#path-21"></use>
                    <use fill="#FFFFFF" xlink:href="#path-21"></use>
                </g>
            </g>
        </g>
    </g>
</svg>