import mainVue from '@/main'

const RAILWAY_LINE = {
  /**
   * 绘制隐藏事件地图图标
   * @param markersLayer
   * @param onclick
   * @returns {{markersLayer: CTMapOl.layer.Vector, markers: any[]}}
   */
  async draw({
    markersLayer,
    onclick = () => {
      console.log('default implement');
    },
    initData = null,
    stopClick = false,
    mapInstance
  }) {
    mapInstance.railwayRouteLayer(true, mainVue.$store.state.map.mapModeId);
    return {
      markersLayer: 'NON',
      markers: []
    };
  },
  /**
   * 取消选中的标记
   * 该函数用于在地图上取消选中的标记，目前实现为打印一条日志信息。
   * @param {Array} markers - 标记数组，代表当前选中的标记集合。
   */
  cancelSelected(markers) {
    console.log('default implement');
  },

  /**
   * 清空标记图层
   * 该函数用于清空地图上的标记图层，以清除所有已添加的标记。
   * @param {*} mapInstance 地图实例
   * @param {Object} markersLayer - 标记图层对象，代表地图上用于展示标记的图层 : RAILWAY_LINE
   */
  clear(markersLayer, mapInstance) {
    mapInstance.railwayRouteLayer(false);
  }
};
export default RAILWAY_LINE;
