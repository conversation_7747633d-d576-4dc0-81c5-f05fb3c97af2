<?xml version="1.0" encoding="UTF-8"?>
<svg width="86px" height="105px" viewBox="0 0 86 105" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>icon_隐患_80_s</title>
    <defs>
        <linearGradient x1="4.81907389%" y1="59.5998027%" x2="91.6145815%" y2="78.9009571%" id="linearGradient-1">
            <stop stop-color="#FB913C" stop-opacity="0.498743444" offset="0%"></stop>
            <stop stop-color="#FF7100" stop-opacity="0.810000002" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#E97E29" stop-opacity="0.811764717" offset="0%"></stop>
            <stop stop-color="#FD9C4E" stop-opacity="0.811764717" offset="98.8059304%"></stop>
        </linearGradient>
        <linearGradient x1="57.5794637%" y1="25.1865715%" x2="57.5794637%" y2="79.8783958%" id="linearGradient-3">
            <stop stop-color="#FF9946" stop-opacity="0.811764717" offset="0%"></stop>
            <stop stop-color="#BE661F" stop-opacity="0.811764717" offset="100%"></stop>
        </linearGradient>
        <ellipse id="path-4" cx="19.1999994" cy="19.2687189" rx="15.9999994" ry="16.0687189"></ellipse>
        <filter x="-12.5%" y="-12.4%" width="125.0%" height="124.9%" filterUnits="objectBoundingBox" id="filter-5">
            <feMorphology radius="4" operator="erode" in="SourceAlpha" result="shadowSpreadInner1"></feMorphology>
            <feGaussianBlur stdDeviation="2" in="shadowSpreadInner1" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="0" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.5 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <path d="M19.1999994,34.1316168 C27.3738134,34.1316168 33.9999988,27.4759677 33.9999988,19.2658084 C33.9999988,11.0556491 27.3738134,4.4 19.1999994,4.4 C11.0261854,4.4 4.4,11.0556491 4.4,19.2658084 C4.4,27.4759677 11.0261854,34.1316168 19.1999994,34.1316168 Z" id="path-6"></path>
        <mask id="mask-7" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="29.5999988" height="29.7316168" fill="white">
            <use xlink:href="#path-6"></use>
        </mask>
        <path d="M19.1999995,31.7186488 C26.0483301,31.7186488 31.599999,26.1404192 31.599999,19.2593244 C31.599999,12.3782295 26.0483301,6.8 19.1999995,6.8 C12.3516689,6.8 6.8,12.3782295 6.8,19.2593244 C6.8,26.1404192 12.3516689,31.7186488 19.1999995,31.7186488 Z" id="path-8"></path>
        <mask id="mask-9" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="24.799999" height="24.9186488" fill="white">
            <use xlink:href="#path-8"></use>
        </mask>
        <linearGradient x1="50%" y1="3.62191685%" x2="50%" y2="100%" id="linearGradient-10">
            <stop stop-color="#FB913C" offset="0%"></stop>
            <stop stop-color="#FB913C" stop-opacity="0.129999995" offset="100%"></stop>
        </linearGradient>
        <filter x="-37.5%" y="-3.0%" width="175.0%" height="106.0%" filterUnits="objectBoundingBox" id="filter-11">
            <feGaussianBlur stdDeviation="0.400000004" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <filter x="-2.8%" y="-6.0%" width="105.5%" height="111.9%" filterUnits="objectBoundingBox" id="filter-12">
            <feGaussianBlur stdDeviation="0.735294118" in="SourceGraphic"></feGaussianBlur>
        </filter>
    </defs>
    <g id="综合监测-大屏" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="1-6综合监测-弹框" transform="translate(-1590.000000, -917.000000)" fill-rule="nonzero">
            <g id="icon_隐患_80_s" transform="translate(1593.000000, 917.000000)">
                <g id="icon_隐患_104_n" transform="translate(20.800000, 0.000000)">
                    <g id="编组-3">
                        <path d="M32.8876619,5.75825612 C33.3990694,6.28013944 33.8810884,6.83113511 34.3310304,7.40854078 C34.650879,7.81900015 34.9545167,8.24280512 35.2409791,8.67898543 C37.2378205,11.7194722 38.4,15.3612761 38.4,19.2755904 C38.4,29.9212061 29.8038669,38.5511811 19.1999991,38.5511811 C10.9868044,38.5511811 3.97809587,33.3738588 1.23529792,26.0926867 L1.23529792,26.0926867 Z M19.1999993,0 C24.0300513,0 28.4435415,1.79054283 31.8178181,4.74652908 L31.8178181,4.74652908 L0,19.2755907 C0,18.5083577 0.0446494296,17.7515952 0.131481965,17.007777 C1.2494803,7.43087109 9.36035658,0 19.1999993,0 Z" id="形状" fill="url(#linearGradient-1)"></path>
                        <g id="椭圆形">
                            <use fill="url(#linearGradient-2)" xlink:href="#path-4"></use>
                            <use fill="black" fill-opacity="1" filter="url(#filter-5)" xlink:href="#path-4"></use>
                            <ellipse stroke="url(#linearGradient-3)" stroke-width="3.2" stroke-linejoin="square" cx="19.1999994" cy="19.2687189" rx="14.3999994" ry="14.4687189"></ellipse>
                        </g>
                        <use id="椭圆形" stroke="#FFFFFF" mask="url(#mask-7)" stroke-width="1.6" fill-opacity="0.00999999978" fill="#FFFFFF" stroke-dasharray="1.6" xlink:href="#path-6"></use>
                        <use id="椭圆形" stroke="#FFFFFF" mask="url(#mask-9)" fill-opacity="0.00999999978" fill="#FFFFFF" stroke-dasharray="1.6" xlink:href="#path-8"></use>
                        <path d="M26.98854,23.2789105 L23.7871535,17.6735232 L20.5579892,12.039796 C20.2766245,11.5471132 19.7598299,11.2440945 19.2009301,11.2440945 C18.6420303,11.2440945 18.1252356,11.5471132 17.8438709,12.039796 L14.6170215,17.674704 L11.4144775,23.2800914 C10.8079965,24.3558251 11.5591534,25.7007874 12.7721154,25.7007874 L25.6193281,25.7007874 C26.184284,25.699819 26.7059319,25.3918058 26.9882053,24.8925176 C27.2704786,24.3932295 27.2706062,23.7783206 26.98854,23.2789105 Z M21.7859976,18.6949388 L18.5429445,23.4017166 C18.3982686,23.6166272 18.0684078,23.4678429 18.1204911,23.2127842 L18.6876203,20.5653693 C18.7130832,20.457914 18.6216481,20.3492779 18.5174815,20.3492779 L16.8045198,20.3634479 C16.6204922,20.3634479 16.5012793,20.1473565 16.6077607,19.999753 L19.6644714,15.4016114 C19.8103046,15.1867008 20.1251192,15.3201343 20.0869248,15.575193 L19.6239622,18.1316842 C19.5973418,18.2391395 19.6887769,18.3324249 19.7941009,18.3324249 L21.5869237,18.3064466 C21.7859976,18.3194357 21.8901642,18.5485163 21.7859976,18.6949388 L21.7859976,18.6949388 Z" id="icon_隐患事件_18_n" fill="#FFFFFF"></path>
                    </g>
                    <rect id="矩形" fill="url(#linearGradient-10)" filter="url(#filter-11)" x="17.6" y="43.2" width="3.2" height="40.3275591" rx="1.6"></rect>
                </g>
                <g id="编组-10" transform="translate(0.000000, 65.055118)" fill="#FB913C" fill-opacity="0.270000011" stroke="#FB913C" stroke-width="0.5">
                    <ellipse id="圆形-3" opacity="0.600000024" filter="url(#filter-12)" cx="40" cy="18.4724409" rx="39.75" ry="18.2224409"></ellipse>
                    <ellipse id="圆形-4" cx="40" cy="18.4655418" rx="32.55" ry="15.0155418"></ellipse>
                    <ellipse id="圆形-4" cx="40" cy="18.4496738" rx="20.55" ry="9.39967379"></ellipse>
                </g>
            </g>
        </g>
    </g>
</svg>