<?xml version="1.0" encoding="UTF-8"?>
<svg width="80px" height="102px" viewBox="0 0 80 102" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>icon_告警_80_n</title>
    <defs>
        <linearGradient x1="4.46261826%" y1="59.5998027%" x2="91.9429004%" y2="78.9009571%" id="linearGradient-1">
            <stop stop-color="#ED5158" stop-opacity="0.498743444" offset="0%"></stop>
            <stop stop-color="#CF2E2A" stop-opacity="0.810000002" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#EE3632" stop-opacity="0.811764717" offset="0%"></stop>
            <stop stop-color="#FC514D" stop-opacity="0.811764717" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="57.5794637%" y1="25.1865715%" x2="57.5794637%" y2="79.8783958%" id="linearGradient-3">
            <stop stop-color="#FC514D" stop-opacity="0.811764717" offset="0%"></stop>
            <stop stop-color="#CF2E2A" stop-opacity="0.811764717" offset="100%"></stop>
        </linearGradient>
        <ellipse id="path-4" cx="19.0181812" cy="19.018182" rx="15.8181812" ry="15.818182"></ellipse>
        <filter x="-12.6%" y="-12.6%" width="125.3%" height="125.3%" filterUnits="objectBoundingBox" id="filter-5">
            <feMorphology radius="4" operator="erode" in="SourceAlpha" result="shadowSpreadInner1"></feMorphology>
            <feGaussianBlur stdDeviation="2" in="shadowSpreadInner1" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="0" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.5 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <path d="M19.0258818,33.651765 C27.1035332,33.651765 33.6517636,27.1035344 33.6517636,19.0258825 C33.6517636,10.9482307 27.1035332,4.4 19.0258818,4.4 C10.9482303,4.4 4.4,10.9482307 4.4,19.0258825 C4.4,27.1035344 10.9482303,33.651765 19.0258818,33.651765 Z" id="path-6"></path>
        <mask id="mask-7" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="29.2517636" height="29.251765" fill="white">
            <use xlink:href="#path-6"></use>
        </mask>
        <path d="M19.0430375,31.2860762 C25.8046803,31.2860762 31.2860749,25.8046814 31.2860749,19.0430381 C31.2860749,12.2813949 25.8046803,6.8 19.0430375,6.8 C12.2813946,6.8 6.8,12.2813949 6.8,19.0430381 C6.8,25.8046814 12.2813946,31.2860762 19.0430375,31.2860762 Z" id="path-8"></path>
        <mask id="mask-9" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="24.4860749" height="24.4860762" fill="white">
            <use xlink:href="#path-8"></use>
        </mask>
        <linearGradient x1="50%" y1="3.62191685%" x2="50%" y2="100%" id="linearGradient-10">
            <stop stop-color="#FF2A2A" offset="0%"></stop>
            <stop stop-color="#CF2E2A" stop-opacity="0.129999995" offset="100%"></stop>
        </linearGradient>
        <filter x="-40.0%" y="-3.0%" width="180.0%" height="106.0%" filterUnits="objectBoundingBox" id="filter-11">
            <feGaussianBlur stdDeviation="0.400000004" in="SourceGraphic"></feGaussianBlur>
        </filter>
    </defs>
    <g id="综合监测-大屏" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="1-6综合监测-弹框" transform="translate(-1687.000000, -917.000000)" fill-rule="nonzero">
            <g id="icon_告警_80_n" transform="translate(1687.000000, 917.000000)">
                <g id="icon_告警_104_n" transform="translate(21.000000, 0.000000)">
                    <g id="编组-3">
                        <path d="M32.5450821,5.67592811 C33.0511624,6.19034986 33.5281604,6.73346774 33.9734155,7.30261802 C34.2899324,7.70720889 34.5904072,8.12495455 34.8738856,8.55489863 C36.8499265,11.5519144 38,15.1416501 38,18.9999998 C38,29.4934111 29.4934099,38 18.9999992,38 C10.8723585,38 3.93665737,32.8966999 1.22243024,25.7196295 L1.22243024,25.7196295 Z M19,0 C23.7797383,0 28.1472546,1.76494275 31.4863825,4.67866612 L31.4863825,4.67866612 L0,19 C0,18.2437366 0.0441843313,17.4977938 0.130112361,16.7646103 C1.23646488,7.32462906 9.26285286,0 19,0 Z" id="形状" fill="url(#linearGradient-1)"></path>
                        <g id="椭圆形">
                            <use fill="url(#linearGradient-2)" xlink:href="#path-4"></use>
                            <use fill="black" fill-opacity="1" filter="url(#filter-5)" xlink:href="#path-4"></use>
                            <ellipse stroke="url(#linearGradient-3)" stroke-width="3.2" stroke-linejoin="square" cx="19.0181812" cy="19.018182" rx="14.2181812" ry="14.218182"></ellipse>
                        </g>
                        <use id="椭圆形" stroke="#FFFFFF" mask="url(#mask-7)" stroke-width="1.6" fill-opacity="0.00999999978" fill="#FFFFFF" stroke-dasharray="1.6" xlink:href="#path-6"></use>
                        <use id="椭圆形" stroke="#FFFFFF" mask="url(#mask-9)" fill-opacity="0.00999999978" fill="#FFFFFF" stroke-dasharray="1.6" xlink:href="#path-8"></use>
                        <g id="警告" transform="translate(11.875000, 11.083333)">
                            <rect id="矩形" fill="#000000" opacity="0" x="0" y="0" width="14.2499995" height="14.2500001"></rect>
                            <path d="M13.9584195,11.2491426 L8.36766889,1.30741963 C7.68391712,0.0919712405 6.56640334,0.0919712405 5.88255432,1.30741963 L0.291817648,11.2491426 C-0.391850754,12.465837 0.167482726,13.4583335 1.53374971,13.4583335 L12.7164874,13.4583335 C14.0827544,13.4583335 14.641546,12.465837 13.9584195,11.2491426 Z M7.56700145,11.6761462 L6.68267991,11.6761462 L6.68267991,10.7850526 L7.56700145,10.7850526 L7.56700145,11.6761462 Z M7.56700145,9.00287939 L6.68267991,9.00287939 L6.68267991,3.6563317 L7.56700145,3.6563317 L7.56700145,9.00287939 Z" id="形状" fill="#FFFFFF"></path>
                        </g>
                    </g>
                    <rect id="矩形" fill="url(#linearGradient-10)" filter="url(#filter-11)" x="18" y="42" width="3" height="40" rx="1.5"></rect>
                </g>
            </g>
        </g>
    </g>
</svg>