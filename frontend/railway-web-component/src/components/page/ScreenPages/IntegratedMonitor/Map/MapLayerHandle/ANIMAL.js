/*
 * @Description:
 * @Author: liu.yongli
 * @Date: 2024-07-18 16:02:25
 * @LastEditTime: 2024-07-23 20:08:25
 * @LastEditors: liu.yongli
 */
/*
 * @Description:
 * @Author: liu.yongli
 * @Date: 2024-07-18 16:02:25
 * @LastEditTime: 2024-07-18 16:25:33
 * @LastEditors: liu.yongli
 */
import { getMetaByDicCode } from '@/api/service/imScreenService';
import CommonMap from '@/components/common/Map/CommonMap';
import { isNotEmpty } from '@/components/common/utils';
import { enums } from '@/components/page/ScreenPages/IntegratedMonitor/DetailInfo/enums';
import CamerasList from '@/components/page/ScreenPages/IntegratedMonitor/InfoWindow/CamerasList.vue';
import LayerConst from '@/components/page/ScreenPages/IntegratedMonitor/Map/LayerConst';
import {
  baseMarker,
  calMakerStyle,
  closeLeftSideWindowByIdPrefix,
  clusterTip,
  openLeftSideWidow,
} from '@/components/page/ScreenPages/IntegratedMonitor/Map/MapLayerHandle/CommonHandle';
// import { animalIcon } from '@/components/page/ScreenPages/IntegratedMonitor/Map/marker';
import { createSource, useClusterLayer } from '@/utils/map3.0';
import vue from '@/main'; // 导入Vue实例
import { transformPointToMercator } from '@/utils'

const ANIMAL = {
  largeAnchor: [30, 70], // 大图标的锚点位置
  smallAnchor: [23, 55], // 小图标的点位置
  cfg: {
    markers: [], // 标记点数组
    tipIdSuffix: 'camera-tip', // 提示框ID后缀
    showFault: false, // 是否显示故障
    selectedId: null, // 选中的标记点ID
  },
  /**
   * 绘制地图图标
   * @param markersLayer
   * @param onclick
   * @param initData
   * @returns {{markersLayer: CTMapOl.layer.Vector, markers: any[]}}
   */
  async draw({
    onclick = () => {
      console.log('default implement'); // 默认点击事件处理
    },
    initData = null, // 初始化数据
    region = {}, // 区域信息
    isCheckMode = false, // 是否检查模式
    refresh = false, // 是否刷新
    mapInstance, // 地图实例
  }) {
    let layerData = initData; // 图层数据
    if (!layerData) {
      // 查询所有畜牧设备列表
      const [success, data] = await getMetaByDicCode({
        layerCode: LayerConst.ANIMAL,
        checkMode: '1',
        ...region,
      });
      if (success) {
        layerData = data.filter((item) => item.devcStatusName && item.lon); // 过滤有效数据
      }
    }
    if (!layerData || layerData.length < 1) {
      return; // 如果没有数据则返回
    }
    const normalStatusArr = [
      enums.ANIMAL.deviceStatusEnum.move.code, // 设备运动状态代码
      enums.ANIMAL.deviceStatusEnum.stop.code, // 设备停止状态代码
    ];
    layerData = isCheckMode
      ? layerData
      : layerData.filter(
        (item) => normalStatusArr.indexOf(item.devcStatus) > -1 // 过滤正常状态的设备
      );

    const points = layerData.map((item) => {
      // 3.0 地图转成墨卡托
      const lngLat = transformPointToMercator([Number(item.lon), Number(item.lat)])
      const result = {
        id: item.deviceCode, // 设备代码
        devcStatus: item.devcStatus, // 设备状态
        ...item,
        lng: lngLat[0], // item.lon * 1, // 经度
        lat: lngLat[1], // item.lat * 1, // 纬度
        // location: item.location, // 地址无
        // list: item.list // list 无
      };
      return result; // 返回设备信息
    });
    const markers = []; // 标记点数组
    const faultMarkers = []; // 故障标记点数组
    points.map(item => {
      const [small, large] = this.getIcon(item); // 获取图标
      console.info('draw animal item=======', small); // 输出图标信息
      const marker = baseMarker({
        point: [item.lng, item.lat], // 标记点位置
        markerAttr: {
          id: item.id, // 标记点ID
          lng: item.lng, // 经度
          lat: item.lat, // 纬度
          markerType: LayerConst.ANIMAL, // 标记点类型
          ds: [item], // 设备数据
        },
        // 测试图标 iconEvtWFPFDFL
        initIcon:
          this.cfg.selectedId && item.id === this.cfg.selectedId
            ? large
            : small, // 初始化图标
        selectedIcon: large, // 选中图标 iconEvtWFPFDFL
        anchorCfg: this, // 锚点配置
        // 点击锚点
        onclick: (attr, markerFeature) => {
          onclick(attr, markerFeature); // 执行传入的点击事件
          this.onclick(attr, marker); // 执行内部点击事件
        },
      });
      markers.push(marker); // 添加标记点到数组
      return null;
    });
    this.cfg.faultMarkers = faultMarkers; // 更新故障标记点
    this.cfg.otherMarkers = markers; // 更新其他标记点
    const showMarkers = this.getMarker(); // 获取显示的标记点
    if (!refresh) {
      this.cfg.markersLayer = useClusterLayer({
        markers: showMarkers, // 标记点
        map: mapInstance, // 地图实例
        type: LayerConst.ANIMAL, // 标记点类型
        onclick: this.onClusterClick, // 集群点击事件处理
      });
    }
    return {
      markersLayer: this.cfg.markersLayer, // 返回标记点图层
      markers, // 返回标记点数组
    };
  },
  // 打开详情信息或关闭详情
  onclick(attr, marker) {
    console.info('onClick------------', attr); // 输出点击信息
    const { selected, id } = attr; // 获取选中状态和ID
    if (selected) {
      // 必须在清除窗体之后，否则窗体关闭会出问题
      this.onSelected(marker); // 处理选中状态
      this.cfg.selectedId = id; // 更新选中ID
      return;
    }
    this.cfg.selectedId = null; // 清除选中ID
    this.cancelSelected(); // 取消选中状态
  },
  /**
   * 初始化标记点
   * @param {Object[]} markers - 标记点数组
   */
  initMarkers(markers) {
    // 初始化marker点
    markers.forEach((marker) => {
      const [small] = this.getIcon(marker.values_.attributes.ds[0]); // 获取小图标
      marker.values_.attributes.selected = false; // 设置未选中状态
      marker.style_ = calMakerStyle(small, this.smallAnchor); // 设置样式
    });
  },
  /**
   * 标记点被选中时的处理
   * @param {Object} marker - 被选中的标记点
   */
  onSelected(marker) {
    const [, large] = this.getIcon(marker.values_.attributes.ds[0]); // 获取大图标
    const markers = this.getMarker(); // 获取标记点数组
    const selectedMarker = markers.find(
      (item) => item.values_.attributes.id === marker.values_.attributes.id // 找到选中的标记点
    );
    selectedMarker.style_ = calMakerStyle(large, this.largeAnchor); // 更新样式
    this.cfg.markersLayer.setSource(
      createSource({
        markers, // 更新标记点源
        type: LayerConst.ANIMAL, // 标记点类型
        onclick: this.onClusterClick, // 集群点击事件处理
      })
    );
    console.info('onSelected----------------', marker.values_.attributes); // 输出选中信息
    const { lng, lat } = marker.values_.attributes; // 获取经纬度
    this.openTip(marker.values_.attributes.ds, lng, lat); // 打开提示框
  },
  /**
   * 刷新原来 标记点被选中时的处理
   * @param {Object} marker - 被选中的标记点
   */
  onRefreshSelected(marker) {
    console.info(
      'onSelected----refresh------------',
      marker.values_.attributes // 输出刷新选中信息
    );
    const [, large] = this.getIcon(marker.values_.attributes.ds[0]); // 获取大图标
    const markers = this.getMarker(); // 获取标记点数组
    const selectedMarker = markers.find(
      (item) => item.values_.attributes.id === marker.values_.attributes.id // 找到选中的标记点
    );
    selectedMarker.style_ = calMakerStyle(large, this.largeAnchor); // 更新样式
    this.cfg.markersLayer.setSource(
      createSource({
        markers, // 更新标记点源
        type: LayerConst.ANIMAL, // 标记点类型
        onclick: this.onClusterClick, // 集群点击事件处理
      })
    );
  },
  /**
   * 取消选中状态
   */
  cancelSelected() {
    const markers = this.getMarker(); // 获取标记点数组
    if (!isNotEmpty(markers)) {
      return; // 如果标记点为空则返回
    }
    this.initMarkers(markers); // 初始化标记点
    this.cfg.markersLayer.setSource(
      createSource({
        markers, // 更新标记点源
        type: LayerConst.ANIMAL, // 标记点类型
        onclick: this.onClusterClick, // 集群点击事件处理
      })
    );
    closeLeftSideWindowByIdPrefix(LayerConst.ANIMAL, LayerConst.ANIMAL); // 关闭侧边窗体
  },
  /**
   * 获取标记点数组
   * @returns {Object[]} - 返回标记点数组
   */
  getMarker() {
    const { faultMarkers, otherMarkers, showFault } = this.cfg; // 获取故障标记点、其他标记点和显示故障状态
    if (showFault) {
      return [...faultMarkers, ...otherMarkers]; // 如果显示故障则返回所有标记点
    }
    return otherMarkers; // 否则返回其他标记点
  },
  /**
   * 清除所有标记点和相关信息
   */
  clear() {
    closeLeftSideWindowByIdPrefix(`${LayerConst.ANIMAL}-`, LayerConst.ANIMAL); // 关闭侧边窗体
  },
  /**
   * 根据设备状态获取标记点图标
   * @param {Object} data - 设备数据
   * @returns {Object[]} - 返回图标信息数组
   */
  getIcon(data) {
    // console.info('getIcon----------', sysConfig.poiIconCfg);
    const { devcStatus } = data; // 获取设备状态
    // 如果状态是运动和静止则显示正常图标，否则展示异常图标
    // if (
    //   devcStatus == enums.ANIMAL.deviceStatusEnum.move.code ||
    //   devcStatus == enums.ANIMAL.deviceStatusEnum.stop.code
    // ) {
    //   return [animalIcon.iconAnimalNormal, animalIcon.iconAnimalNormalSelected];
    // }
    // return [
    //   animalIcon.iconAnimalUnnormal,
    //   animalIcon.iconAnimalUnnormalSelected,
    // ];
    const { mapIcon, mapExceptionIcon } = sysConfig.poiIconCfg['ANIMAL']; // 获取图标配置
    if (
      devcStatus == enums.ANIMAL.deviceStatusEnum.move.code ||
      devcStatus == enums.ANIMAL.deviceStatusEnum.stop.code
    ) {
      return [mapIcon.normal, mapIcon.select]; // 返回正常图标
    }
    return [mapExceptionIcon.normal, mapExceptionIcon.select]; // 返回异常图标
  },
  /**
   * 打开提示框
   * @param {Object[]} ds - 设备数据数组
   * @param {number} lng - 经度
   * @param {number} lat - 纬度
   */
  openTip(ds, lng, lat) {
    if (ds.length === 1) {
      const selectedData = ds[0]; // 获取选中的设备数据
      this.openDetail(selectedData, param => {
        if (param && param.destroy) {
          return; // 如果参数中包含销毁标记则返回
        }
        this.cancelSelected(); // 取消选中状态
      });
      return;
    }
    const winComponent = CommonMap.componentToHtml({
      component: CamerasList, // 使用摄像头列表组件
      props: ds, // 传递设备数据
      onSelected: (seletedData) => {
        const markerId = `${LayerConst.ANIMAL}-${seletedData.deviceCode}`; // 生成标记点ID
        // 打开详情页面
        openLeftSideWidow({
          windowId: markerId, // 窗体ID
          props: {
            ds: { ...seletedData }, // 设备数据
            markerType: LayerConst.ANIMAL, // 标记点类型
          },
          group: LayerConst.ANIMAL, // 详情页面关闭回调
          onclose: param => {
            if (param && param.destroy) {
              return; // 如果参数中包含销毁标记则返回
            }
            const [, , contentRef] = winComponent; // 获取组件引用
            contentRef.clear(); // 清除组件内容
          },
        });
      },
    });
    CommonMap.mapTip({
      lng, // 经度
      lat, // 纬度
      content: winComponent, // 提示框内容
      mapinstance: vue.$store.state.map.mapStoreRef.mapInstance, // 地图实例
    });
  }, // 故障检测
  /**
   * 过滤故障标记点
   * @param {Object} layer - 层信息
   * @param {boolean} show - 是否显示故障点
   */
  filterFault({ layer, show }) {
    this.cfg.showFault = show; // 更新显示故障状态
    const markers = this.getMarker(); // 获取标记点数组
    console.info('filterFault--------------', markers); // 输出过滤故障信息
    this.initMarkers(markers); // 初始化标记点
    this.cfg.markersLayer.setSource(
      createSource({
        markers, // 更新标记点源
        type: LayerConst.ANIMAL, // 标记点类型
        onclick: this.onClusterClick, // 集群点击事件处理
      })
    );
    closeLeftSideWindowByIdPrefix(`${LayerConst.ANIMAL}-`, LayerConst.ANIMAL); // 关闭侧边窗体
  },
  /**
   * 打开设备详情页面
   * @param {Object} selectedData - 选中的设备数据
   * @param {Function} onclose - 关闭详情页面的回调函数
   */
  openDetail(selectedData, onclose) {
    const markerId = `${LayerConst.ANIMAL}-${selectedData.deviceCode}`; // 生成标记点ID
    openLeftSideWidow({
      windowId: markerId, // 窗体ID
      props: {
        ds: { ...selectedData }, // 设备数据
        markerType: LayerConst.ANIMAL, // 标记点类型
      },
      group: LayerConst.ANIMAL, // 详情页面关闭回调
      onclose, // 关闭回调函数
    });
  },
  /**
   * 处理集群点击事件
   * @param {Object} event - 点击事件对象
   */
  onClusterClick({ features, geometry }) {
    if (!features || features.length < 1) {
      return; // 如果没有特征则返回
    }
    const ds = features.map((feature) => {
      const data = feature.values_.attributes.ds[0]; // 获取设备数据
      return {
        key: data.deviceCode, // 设备代码
        type: '畜牧设备', // 设备类型
        name: data.deviceName, // 设备名称
        status: data.devcStatus, // 设备状态
        statusName: enums.ANIMAL.deviceStatus[data.devcStatus]?.name, // 状态名称
        color: enums.ANIMAL.deviceStatus[data.devcStatus]?.color, // 状态颜色
        ...data,
      };
    });
    const [lng, lat] = geometry.getCoordinates(); // 获取经纬度
    clusterTip({
      ds, // 设备数据
      markerType: LayerConst.ANIMAL, // 标记点类型
      lng, // 经度
      lat, // 纬度
    });
  },
};
export default ANIMAL; // 导出动物图层配置
