/**
 * 该模块定义了用于处理和显示警报事件地图标记的函数和属性。
 * 它包括绘制标记、处理点击事件、选择和取消选择标记等功能。
 */
import { getMapEvts } from '@/api/service/imScreenService';
// import ConstEnum from '@/components/common/ConstEnum';
import CommonMap from '@/components/common/Map/CommonMap';
import { isNotEmpty } from '@/components/common/utils';
// import {
//   enums
// } from '@/components/page/ScreenPages/IntegratedMonitor/DetailInfo/enums';
import {
  Events
} from '@/components/page/ScreenPages/IntegratedMonitor/enum/EventsEnum';
import LayerConst
  from '@/components/page/ScreenPages/IntegratedMonitor/Map/LayerConst';
import {
  baseMarker, calMakerStyle, closeLeftSideWindowByIdPrefix, clusterTip
} from '@/components/page/ScreenPages/IntegratedMonitor/Map/MapLayerHandle/CommonHandle';
import {
  evtIcon
  , evtTypesIcon
} from '@/components/page/ScreenPages/IntegratedMonitor/Map/marker';
import vue from '@/main';
import { createSource, useClusterLayer } from '@/utils/map3.0';
// import moment from 'moment';
import { getEvtParams } from '@/components/common/utils';
import { transformPointToMercator } from '@/utils'
// import {
//   queryOrderInfo,
// } from '@/api/service/imScreenService'
// import { postProxy } from '@/api/service/common';
// import api from '@/api';
// import { 
//   dealStateEnm,
// } from '@/components/page/ScreenPages/IntegratedMonitor/EventFilter/typeDatas'

const {
  EVT_LIST,
  // MapEvent
} = Events;
const ALARM_EVENT = {
  largeAnchor: [40, 80],
  smallAnchor: [15, 80], // [50, 100],
  cfg: {
    markers: []
  },
  /**
   * 在地图上绘制警报事件标记。
   * @param {Object} options - 绘制选项。
   * @param {Function} options.onclick - 点击标记时的回调函数。
   * @param {Object|null} options.initData - 初始化数据，用于直接渲染标记。
   * @param {boolean} options.stopClick - 是否阻止标记的点击事件。
   * @param {Object} options.region - 地域筛选条件。
   * @returns {Object|null} 返回包含标记图层和标记数组的对象，如果没有数据则返回null。
   * @returns {string} 筛选组件的操作状态
   */
  async draw({
    onclick = () => {
      console.log("default implement")
    },
    initData = null,
    stopClick = false,
    region = {},
    filterState = '',
    mapInstance
  }) {
    let layerData = initData;
    if (!layerData) {
      // 原接口参数
      let params = {
        isFocus: 'Y',
        fusionStatusList: ['1', '2'], // 未消散的
        ...getEvtParams(filterState)
      }
      const [, resData] = await getMapEvts(params);
      // 测试数据
      const data = resData
      if (data?.length) {
        layerData = data.map(v => ({
          ...v,
          latitude: v.startLat,
          longitude: v.startLng,
        }));
      }
    }
    if (!layerData || layerData.length < 1) {
      return;
    }
    const points = layerData.map((item) => {
      // 3.0 地图转成墨卡托
      const lngLat = transformPointToMercator([Number(item.longitude), Number(item.latitude)])
      return {
        ...item,
        lng: lngLat[0], // longitude
        lat: lngLat[1], // latitude
        orderId: item.warningOrderId,
        location: item.eventAddress, // address
      };
    });
    const markers = [];
    points.map((item) => {
      const [small, large] = this.getTypesIcon(item.eventTypeName);
      const marker = baseMarker({
        point: [item.lng, item.lat],
        markerAttr: {
          markerType: LayerConst.ALARM_EVENT,
          ds: item,
          selected: !!initData
        },
        initIcon: initData ? large : small,
        selectedIcon: large,
        anchorCfg: this,
        onclick: (attr, markerFeature) => {
          if (stopClick) {
            return;
          }
          onclick(attr, markerFeature);
          const {
            selected,
          } = attr;
          if (selected) {
            this.onSelected(marker, item);
          } else {
            vue.$EventBus.$emit(EVT_LIST.ON_DETAIL_CLOSE, { resetLayer: true });
          }
        }
      });
      markers.push(marker);
    });
    this.cfg.markers = markers;
    this.cfg.markersLayer = useClusterLayer({
      markers: markers,
      map: mapInstance,
      type: LayerConst.ALARM_EVENT,
      onclick: this.onClusterClick
    });
    return {
      markersLayer: this.cfg.markersLayer,
      markers
    };
  }, // 初始化marker点
  /**
   * 初始化标记符号。
   * 此函数为给定的标记数组中的每个标记设置初始状态和样式。
   * @param {Array} markers - 包含标记信息的数组。
   */
  initMarkers(markers) {
    // 遍历标记数组
    markers.forEach(marker => {
      // 根据标记的属性获取图标
      const [small] = this.getTypesIcon(marker.values_.attributes?.ds?.eventTypeName);
      // 设置标记的选中状态为未选中
      marker.values_.attributes.selected = false;
      // 根据小图标和锚点位置计算并设置标记的样式
      marker.style_ = calMakerStyle(small, this.smallAnchor);
    });
  },
  /**
   * 当标记被选中时触发的事件处理函数。
   * @param {Object} marker - 被选中的标记对象。
   * @param {Object} item - 与标记相关联的数据项。
   */
  onSelected(marker, item) {
    vue.$EventBus.$emit(EVT_LIST.ON_CLICK, item);
  },

  /**
   * 取消当前选中的标记。
   */
  cancelSelected() {
    const markers = this.getMarker();
    if (!isNotEmpty(markers)) {
      return;
    }
    this.initMarkers(markers);
    this.cfg.markersLayer.setSource(createSource({
      markers,
      type: LayerConst.ALARM_EVENT,
      onclick: this.onClusterClick
    }));
  },

  /**
   * 获取配置中的标记数组。
   * @returns {Array} 标记数组。
   */
  getMarker() {
    const {
      markers
    } = this.cfg;
    return markers;
  },

  /**
   * 清除地图上的所有标记。
   */
  clear() {
    closeLeftSideWindowByIdPrefix(`${LayerConst.ALARM_EVENT}-`,
      LayerConst.ALARM_EVENT);
  },

  /**
   * 根据数据项生成标记图标。
   * @param {Object} data - 数据项。
   * @returns {Array} 图标URL数组。
   */
  getIcon(data) {
    return [evtIcon.iconEvtAlarmS, evtIcon.iconEvtAlarmL];
  },

  /**
   * 获取不同类型的事件图标
   * @param {String} type - 类型。
   * @returns {Array} 图标URL数组。
   */
  getTypesIcon(type) {
    return evtTypesIcon[type];
  },

  /**
   * 打开详细信息窗口。
   * @param {Object} selectedData - 被选中的数据项。
   */
  openDetail(selectedData) {
    CommonMap.closeTip(vue.$store.state.map.mapStoreRef.mapInstance);
    vue.$EventBus.$emit(EVT_LIST.ON_CLICK, selectedData);
  },

  /**
   * 当标记聚合成簇时触发的事件处理函数。
   * @param {Object} event - 包含聚合成的簇的信息的对象。
   */
  async onClusterClick({
    features,
    geometry
  }) {
    if (!features || !Array.isArray(features) || features.length < 1) {
      return;
    }
    const getStatus = (_key) => {
      if (_key === '3') return '已消散'
      else if (['1', '2'].includes(_key)) return '未消散'
      else ''
    }
    // let res = []
    // 等待所有请求完成
    // promise.all 发到线上环境获取不到返回值，所以使用for循环
    // 取告警状态
    // for (const feature of features) {
    //   try {
    //     const data = feature.values_.attributes.ds;
    //     const response = await queryOrderInfo({ warningOrderId: data.lastEventId });
    //     res.push(response);
    //   } catch (error) {
    //     console.error('请求失败:', error);
    //   }
    // }
    // console.log('fetches-请求-', features)
    // 查询到对应的数据
    const ds = features.map(feature => {
      const data = feature.values_.attributes.ds;
      // const orderList = res.find(v => {
      //   const [, fetchData] = v
      //   return fetchData?.order?.warningOrderId === data.lastEventId
      // })
      // const orderData = orderList?.[1]?.order

      return {
        key: data.warningOrderId ?? data.id,
        type: ['其他', '其它'].includes(data.eventTypeName) ? data.warningTypeName : data.eventTypeName, // 事件类型,
        name: data.title ?? data.alarmBody,
        status: data.fusionStatus, // orderData?.orderStatus, // 直接展示事件状态,不展示告警状态了 
        statusName: getStatus(data.fusionStatus), // orderData?.orderStatusName,
        // enums.ALARM_EVENT.orderStatus.color(data.orderStatus)
        color: '#4f9fff', // 展示默认颜色,
        ...data
      };
    });
    const [lng, lat] = geometry.getCoordinates();
    clusterTip({
      ds,
      markerType: LayerConst.ALARM_EVENT,
      lng,
      lat
    });
  }
};
export default ALARM_EVENT;

