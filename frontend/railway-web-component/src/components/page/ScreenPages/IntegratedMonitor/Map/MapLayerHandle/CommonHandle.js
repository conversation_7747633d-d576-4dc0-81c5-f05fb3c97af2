import CommonMap from '@/components/common/Map/CommonMap';
import { uuid } from '@/components/common/utils';
import ClusterList
  from '@/components/page/ScreenPages/IntegratedMonitor/InfoWindow/ClusterList.vue';
import {
  maplayer
} from '@/components/page/ScreenPages/IntegratedMonitor/Map/maplayer';
import {
  changeImageMarker, getMarkerStyle, imageMarkerWidthBg
} from '@/utils/map3.0';
import CTMapOl from '@ct/ct_map_ol';
import vue from '@/main';
/**
 * 创建一个基础标记，用于地图上标注特定位置。
 *
 * @param {Object} point - 地图上的坐标点对象。
 * @param {Object} markerAttr - 标记的属性配置，包括选中状态等。
 * @param {string} initIcon - 未选中状态时的图标。
 * @param {string} selectedIcon - 选中状态时的图标。
 * @param {Object} anchorCfg - 锚点配置，包括大锚点和小锚点。
 * @param {Function} onclick - 点击事件的回调函数。
 * @returns {Object} 返回创建的标记对象。
 */
export const baseMarker = ({
  point,
  markerAttr = {},
  initIcon,
  selectedIcon,
  anchorCfg,
  onclick
}) => {
  /**
   * 使用给定的配置创建一个图像标记。
   *
   * @param {Object} options - 标记的配置选项，包括位置、图标和锚点等。
   * @param {Object} attributes - 标记的属性，包括id和onclick事件处理函数等。
   * @returns {Object} 返回创建的图像标记对象。
   */
  const marker = imageMarkerWidthBg({
    lnglat: point,
    option: {
      icon: initIcon,
      anchor: markerAttr.selected
        ? anchorCfg.largeAnchor
        : anchorCfg.smallAnchor,
      text: '定位'
    },
    attributes: {
      id: `meta-icon-${uuid()}`, ...markerAttr,
      onclick: (attr, feature) => {
        let select = !attr.selected;
        const newAttr = {
          ...attr,
          selected: select,
          onclick: attr.onclick
        };
        onclick && onclick(newAttr, marker, feature);
        changeImageMarker({
          feature,
          attr: {
            attributes: newAttr
          },
          style: {
            src: select ? selectedIcon : initIcon,
            anchor: select ? anchorCfg.largeAnchor : anchorCfg.smallAnchor
          }
        });
      }
    }
  });
  return marker;
};
/**
 * 打开左侧窗口
 * @param {Object} config 窗口配置对象
 * @param {string} config.windowId 窗口ID
 * @param {Object} config.props 窗口属性
 * @param {string} config.group 窗口分组
 * @param {Function} config.onclose 关闭窗口的回调函数
 */
export const openLeftSideWidow = ({
  windowId,
  props,
  group,
  onclose
}) => {
  window.leftWrap?.addContent?.({
    props: props,
    group,
    key: windowId,
    onclose
  });
};

/**
 * 关闭左侧窗口
 * @param {string} windowId 窗口ID
 * @param {string} group 窗口分组
 */
export const closeLeftSideWindow = (windowId, group) => {
  window.leftWrap.closeContent(windowId, group);
};

/**
 * 通过前缀关闭左侧窗口
 * @param {string} windowId 前缀ID
 * @param {string} group 窗口分组
 */
export const closeLeftSideWindowByIdPrefix = (windowId, group) => {
  window.leftWrap?.closeByIdPrefix?.(windowId, group);
};

/**
 * 集群提示信息处理
 * @param {Object} config 集群提示配置对象
 * @param {Object[]} config.ds 数据源
 * @param {string} config.markerType 标记类型
 * @param {number} config.lng 经度
 * @param {number} config.lat 纬度
 */
export const clusterTip = ({
  ds,
  markerType,
  lng,
  lat
}) => {
  const winComponent = CommonMap.componentToHtml({
    component: ClusterList,
    props: ds,
    onSelected: (seletedData) => {
      if (!seletedData) {
        return;
      }
      // 打开详情页面
      maplayer.getLayer(markerType).openDetail(seletedData, (key, param) => {
        if (param && param.destroy) {
          return;
        }
        const [, , contentRef] = winComponent;
        contentRef.clear(markerType);
      });
    }
  });
  CommonMap.mapTip({
    lng,
    lat,
    content: winComponent,
    mapinstance: vue.$store.state.map.mapStoreRef.mapInstance
  });
};

/**
 * 计算标记样式
 * @param {string} initIcon 初始图标
 * @param {string} anchor 锚点位置
 * @returns {Object} 标记样式对象
 */
export const calMakerStyle = (initIcon, anchor) => {
  return getMarkerStyle({
    icon: initIcon,
    anchor: anchor
  });
};

/**
 * 初始化图层
 * @param {Object} markersLayer 标记图层对象
 * @param {Object} mapIns 地图实例
 * @returns {Object} 初始化后的标记图层对象
 */
export const initLayer = (markersLayer, mapIns) => {
  const map = mapIns; // || window.map;
  if (!markersLayer) {
    markersLayer = new CTMapOl.layer.Vector({
      source: new CTMapOl.source.Vector(),
      zIndex: 5
    });
    map.addLayer(markersLayer);
  }
  return markersLayer;
};
