import { getMetaByDicCode } from '@/api/service/imScreenService';
import { isNotEmpty } from '@/components/common/utils';
import {
  baseMarker, calMakerStyle, closeLeftSideWindow, closeLeftSideWindowByIdPrefix,
  clusterTip, openLeftSideWidow
} from '@/components/page/ScreenPages/IntegratedMonitor/Map/MapLayerHandle/CommonHandle';
import { createSource, useClusterLayer } from '@/utils/map3.0';
import {
  transformPointToMercator,
} from '@/utils/index.js'
// 测试图标
// import iconEvtWFPFDFL from '../markicon/icon_违法排放堆放_80_n.svg';

const COMM_LAYER = {
  largeAnchor: [30, 70],
  smallAnchor: [23, 55],
  cfg: {
    markers: {},
    markersLayer: {}
  },
  /**
   * 绘制地图图标
   * @param markersLayer
   * @param onclick
   * @param initData
   * @returns {{markersLayer: CTMapOl.layer.Vector, markers: any[]}}
   */
  async draw({
    layerCode,
    onclick = () => {
      console.log('default implement');
    },
    initData = null,
    region = {},
    mapInstance
  }) {
    let layerData = initData;
    if (!layerData) {
      const [, resData] = await getMetaByDicCode({
        layerCode: layerCode,
        checkMode: '1',
        ...region
      });
      let data = resData
      // 测试数据
      // if (!data?.length) {
      //   // 随道数据
      //   if (layerCode === 'R:202406041411449843') {
      //     data = tunnelListData.data
      //   }
      // }

      if (data?.length) {
        layerData = data;
      }
    }
    if (!layerData || layerData.length < 1) {
      return;
    }
    const points = layerData.map((item) => {
      const point = transformPointToMercator([Number(item.longitude), Number(item.latitude)]);
      return {
        id: item.resourceId,
        lng: point[0],
        lat: point[1],
        markerType: layerCode,
        ds: {
          ...item,
          id: item.resourceId,
          markerType: layerCode
        }
      };
    });
    const markers = [];
    points.map((item, index) => {
      const [small, large] = this.getIcon(item);
      const marker = baseMarker({
        point: [item.lng, item.lat],
        markerAttr: {
          id: item.id,
          lng: item.lng,
          lat: item.lat,
          markerType: item.markerType,
          ds: item.ds
        },
        // 公共图层的图标都是从接口中取，接口图标地址不展示时使用下面替换测试
        initIcon: small,// iconEvtWFPFDFL, // 测试数据图标 
        selectedIcon: large,// iconEvtWFPFDFL,
        anchorCfg: this,
        onclick: (attr, markerFeature) => {
          onclick(attr, markerFeature);
          const {
            id,
            markerType,
            selected
          } = attr;
          const markerId = `${markerType}-${id}`;
          if (selected) {
            this.onSelected(marker);
            // 打开详情页面
            this.openDetail(item.ds, (key, param) => {
              this.cancelSelected(markerType);
            });
          } else {
            closeLeftSideWindow(markerId, markerType);
          }
        }
      });
      markers.push(marker);
      if (marker.bg) {
        markers.push(marker.bg);
      }
    });

    this.cfg.markers[layerCode] = markers;
    this.cfg.markersLayer[layerCode] = useClusterLayer({
      markers: markers,
      map: mapInstance,
      type: layerCode,
      onclick: this.onClusterClick
    });
    return {
      markersLayer: this.cfg.markersLayer[layerCode],
      markers
    };
  }, // 初始化marker点
  /**
   * 初始化标记符号
   * @param {Array} markers 标记数组
   */
  initMarkers(markers) {
    markers.forEach((marker) => {
      const [small] = this.getIcon(marker.values_.attributes.ds);
      marker.values_.attributes.selected = false;
      marker.style_ = calMakerStyle(small, this.smallAnchor);
    });
  },

  /**
   * 当标记被选中时的处理函数
   * @param {Object} marker 选中的标记对象
   */
  onSelected(marker) {
    const {
      markerType,
    } = marker.values_.attributes.ds;
    const [, large] = this.getIcon(marker.values_.attributes.ds);
    const markers = this.getMarker(markerType);
    const selectedMarker = markers.find(
      (item) => item.values_.attributes.id === marker.values_.attributes.id
    );
    selectedMarker.values_.attributes.selected = true;
    selectedMarker.style_ = calMakerStyle(large, this.largeAnchor);
    this.cfg.markersLayer[markerType].setSource(
      createSource({
        markers,
        type: markerType,
        onclick: this.onClusterClick
      })
    );
  },

  /**
   * 取消标记选中状态
   * @param {String} markerType 标记类型
   */
  cancelSelected(markerType) {
    const markers = this.getMarker(markerType);
    if (!isNotEmpty(markers)) {
      return;
    }
    this.initMarkers(markers);
    this.cfg.markersLayer[markerType].setSource(
      createSource({
        markers,
        type: markerType,
        onclick: this.onClusterClick
      })
    );
  },

  /**
   * 根据标记类型获取标记数组
   * @param {String} markerType 标记类型
   * @return {Array} 标记数组
   */
  getMarker(markerType) {
    return this.cfg.markers[markerType] || [];
  },

  /**
   * 清除指定类型的标记
   * @param {String} markerType 标记类型
   */
  clear(markerType) {
    closeLeftSideWindowByIdPrefix(`${markerType}-`, markerType);
  },

  /**
   * 根据数据获取标记的图标
   * @param {Object} data 标记的数据
   * @return {Array} 图标正常状态和选中状态的图标对象
   */
  getIcon(data) {
    const { markerType } = data;
    const {
      mapIcon,
    } = sysConfig.poiIconCfg[markerType];
    return [mapIcon.normal, mapIcon.select];
  },

  /**
   * 打开详情窗口
   * @param {Object} selectedData 选中的数据对象
   * @param {Function} onclose 关闭窗口的回调函数
   */
  openDetail(selectedData, onclose) {
    const {
      id,
      markerType
    } = selectedData;
    const markerId = `${markerType}-${id}`;
    openLeftSideWidow({
      windowId: markerId,
      props: {
        ds: { ...selectedData },
        markerType
      },
      group: markerType, // 详情页面关闭回调
      onclose
    });
  },

  /**
   * 当标记聚集成簇时的点击事件处理函数
   * @param {Object} event 点击事件对象，包含特征和几何形状信息
   */
  onClusterClick({
    features,
    geometry
  }) {
    if (!features || features.length < 1) {
      return;
    }
    let markerType;
    const ds = features.map((feature) => {
      const data = feature.values_.attributes.ds;
      markerType = data.markerType;
      return {
        key: data.id,
        type: data.resourceTypeName,
        name: data.resourceName,
        status: data.status,
        statusName: data.status,
        ...data
      };
    });
    const [lng, lat] = geometry.getCoordinates();
    clusterTip({
      ds,
      markerType,
      lng,
      lat
    });
  }
};
export default COMM_LAYER;
