<?xml version="1.0" encoding="UTF-8"?>
<svg width="40px" height="88px" viewBox="0 0 40 88" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>icon_其他_40_n</title>
    <defs>
        <linearGradient x1="4.46261826%" y1="59.5998027%" x2="91.9429004%" y2="78.9009571%" id="linearGradient-1">
            <stop stop-color="#ED5158" stop-opacity="0.498743444" offset="0%"></stop>
            <stop stop-color="#CF2E2A" stop-opacity="0.810000002" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#EE3632" stop-opacity="0.811764717" offset="0%"></stop>
            <stop stop-color="#FC514D" stop-opacity="0.811764717" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="57.5794637%" y1="25.1865715%" x2="57.5794637%" y2="79.8783958%" id="linearGradient-3">
            <stop stop-color="#FC514D" stop-opacity="0.811764717" offset="0%"></stop>
            <stop stop-color="#CF2E2A" stop-opacity="0.811764717" offset="100%"></stop>
        </linearGradient>
        <ellipse id="path-4" cx="20.0191381" cy="20.0191389" rx="16.6507171" ry="16.6507179"></ellipse>
        <filter x="-12.0%" y="-12.0%" width="124.0%" height="124.0%" filterUnits="objectBoundingBox" id="filter-5">
            <feMorphology radius="4" operator="erode" in="SourceAlpha" result="shadowSpreadInner1"></feMorphology>
            <feGaussianBlur stdDeviation="2" in="shadowSpreadInner1" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="0" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.5 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <path d="M20.027244,35.4229106 C28.530035,35.4229106 35.422909,28.5300362 35.422909,20.0272448 C35.422909,11.5244533 28.530035,4.63157895 20.027244,4.63157895 C11.524453,4.63157895 4.63157895,11.5244533 4.63157895,20.0272448 C4.63157895,28.5300362 11.524453,35.4229106 20.027244,35.4229106 Z" id="path-6"></path>
        <mask id="mask-7" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="30.7913301" height="30.7913316" fill="white">
            <use xlink:href="#path-6"></use>
        </mask>
        <path d="M20.0453026,32.9327118 C27.1628214,32.9327118 32.9327104,27.1628225 32.9327104,20.0453033 C32.9327104,12.9277841 27.1628214,7.15789474 20.0453026,7.15789474 C12.9277838,7.15789474 7.15789474,12.9277841 7.15789474,20.0453033 C7.15789474,27.1628225 12.9277838,32.9327118 20.0453026,32.9327118 Z" id="path-8"></path>
        <mask id="mask-9" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="25.7748157" height="25.7748171" fill="white">
            <use xlink:href="#path-8"></use>
        </mask>
        <linearGradient x1="50%" y1="3.62191685%" x2="50%" y2="100%" id="linearGradient-10">
            <stop stop-color="#FF2A2A" offset="0%"></stop>
            <stop stop-color="#CF2E2A" stop-opacity="0.129999995" offset="100%"></stop>
        </linearGradient>
        <filter x="-31.6%" y="-3.2%" width="163.2%" height="106.3%" filterUnits="objectBoundingBox" id="filter-11">
            <feGaussianBlur stdDeviation="0.421052636" in="SourceGraphic"></feGaussianBlur>
        </filter>
    </defs>
    <g id="综合监测-大屏" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="1-2综合监测-收起" transform="translate(-700.000000, -393.000000)" fill-rule="nonzero">
            <g id="icon_其他_40_n" transform="translate(700.000000, 393.000000)">
                <g id="编组-3">
                    <path d="M34.2579812,5.97466117 C34.7906973,6.51615775 35.2928004,7.08786077 35.76149,7.68696634 C36.0946657,8.11285146 36.4109549,8.55258374 36.7093532,9.00515645 C38.7893963,12.1599099 40,15.938579 40,19.9999998 C40,31.0456959 31.0456947,40 19.9999991,40 C11.4445879,40 4.14384986,34.6281051 1.28676867,27.0732942 L1.28676867,27.0732942 Z M20,0 C25.0313035,0 29.6286891,1.85783448 33.1435605,4.92491171 L33.1435605,4.92491171 L0,20 C0,19.2039333 0.0465098225,18.4187303 0.13696038,17.6469582 C1.30154198,7.71013585 9.75037144,0 20,0 Z" id="形状" fill="url(#linearGradient-1)"></path>
                    <g id="椭圆形">
                        <use fill="url(#linearGradient-2)" xlink:href="#path-4"></use>
                        <use fill="black" fill-opacity="1" filter="url(#filter-5)" xlink:href="#path-4"></use>
                        <ellipse stroke="url(#linearGradient-3)" stroke-width="3.36842105" stroke-linejoin="square" cx="20.0191381" cy="20.0191389" rx="14.9665065" ry="14.9665074"></ellipse>
                    </g>
                    <use id="椭圆形" stroke="#FFFFFF" mask="url(#mask-7)" stroke-width="1.68421053" fill-opacity="0.00999999978" fill="#FFFFFF" stroke-dasharray="1.684210551412482" xlink:href="#path-6"></use>
                    <use id="椭圆形" stroke="#FFFFFF" mask="url(#mask-9)" stroke-width="1.05263158" fill-opacity="0.00999999978" fill="#FFFFFF" stroke-dasharray="1.684210551412482" xlink:href="#path-8"></use>
                    <g id="警告" transform="translate(12.500000, 11.666667)">
                        <rect id="矩形" fill="#000000" opacity="0" x="0" y="0" width="14.9999995" height="15.0000001"></rect>
                        <path d="M14.6930731,11.8412027 L8.80807251,1.37623119 C8.08833381,0.0968118321 6.91200352,0.0968118321 6.19216244,1.37623119 L0.307176472,11.8412027 C-0.412474478,13.1219337 0.176297606,14.1666668 1.61447338,14.1666668 L13.3857762,14.1666668 C14.823952,14.1666668 15.4121537,13.1219337 14.6930731,11.8412027 Z M7.96526468,12.2906802 L7.0343999,12.2906802 L7.0343999,11.352687 L7.96526468,11.352687 L7.96526468,12.2906802 Z M7.96526468,9.47671515 L7.0343999,9.47671515 L7.0343999,3.84877021 L7.96526468,3.84877021 L7.96526468,9.47671515 Z" id="形状" fill="#FFFFFF"></path>
                    </g>
                </g>
                <rect id="矩形" fill="url(#linearGradient-10)" filter="url(#filter-11)" x="18" y="46" width="4" height="40" rx="1.68421054"></rect>
            </g>
        </g>
    </g>
</svg>