<template></template>
<script>
// import { getMapTiles } from '@/api/service/common' // 导入获取地图瓦片的服务
import CTMapOl from '@ct/ct_map_ol' // 导入地图库
import CommonMap from '@/components/common/Map/CommonMap' // 导入通用地图组件
import { maplayer } from '@/components/page/ScreenPages/IntegratedMonitor/Map/maplayer' // 导入地图图层管理
import {
  imageMarker,
  polyline,
  createMap,
  // mouseAreaTool,
  // mouseLineTool,
} from '@/utils/map3.0' // 导入地图工具函数
import { mapGetters, mapMutations } from 'vuex' // 导入Vuex的getter和mutation
import { Events } from '../enum/EventsEnum' // 导入事件枚举
import LayerConst from '@/components/page/ScreenPages/IntegratedMonitor/Map/LayerConst' // 导入图层常量
import dayjs from 'dayjs' // 导入时间处理库
import { debounce, difference } from 'lodash' // 导入防抖函数
// import MapOptions from '@/components/common/Map/CommonMap'
import { transformPointToMercator } from '@/utils'

// 缓存图层对象，用于快速访问和操作图层
const layerCache = {
  getLayer: layer => {
    return layerCache[layer] || {}
  },
}

// 地图事件常量
const MapEvent = Events.MapEvent
let _map = null // 地图
let locusLayer = null // 轨迹图层

/**
 * 地图组件
 * 用于在页面中展示地图，并提供地图相关的操作功能
 */
export default {
  inject: ['mapRef'],
  props: {
    onMapLoad: {
      type: Function,
      default: () => {
        console.log('onMapLoad')
      },
    },
    center: {
      type: Array,
      default: () => {
        return [116.38813, 39.89446] // 默认地图中心坐标
      },
    },
    mapId: {
      type: String,
    },
  },
  computed: {
    curRenderCaptureDevc() {
      return this.$store.state.captureInfo.curRenderCaptureDevc
    },
    ...mapGetters('map', ['formatSelectedRegion', 'getPassRouteSwitch']), // 从Vuex中获取格式化的选中区域
  },
  watch: {
    curRenderCaptureDevc: {
      handler(newDevices, oldDevices) {
        console.log('数组发生变化')
        console.log('新值:', newDevices)
        console.log('旧值:', oldDevices)
        // 如果这时候视频图层还没打开，不做处理，然后在图层初始化的时候渲染这些抓拍设备
        if (!layerCache[LayerConst.CAMERA]) {
          return
        }
        // 这个时候如果是已经关闭的弹窗，则需要关闭
        const needOpenDevice = difference(newDevices, oldDevices)
        const needCloseDevice = difference(oldDevices, newDevices)
        this.renderCaptureCamera(needOpenDevice, needCloseDevice)
      },
      deep: true,
    },
  },
  data() {
    return {
      // mapLoaded: false, // 地图是否加载完成
    }
  },
  mounted() {
    this.initMap() // 初始化地图
    // 注册地图相关的事件监听
    this.$EventBus.$on(MapEvent.ADD_MARKER, this.onAddMarker)
    this.$EventBus.$on(MapEvent.CLEAR_ALL, this.clearAll)
    this.$EventBus.$on(MapEvent.REMOVE_MARKER, this.onRemoveMarker)
    this.$EventBus.$on(MapEvent.DETECT_FAILURE, this.onDetectFailure)
    this.$EventBus.$on(MapEvent.EVENT_MAP_ZOOM_CHANGE, this.onMapZoomChange)
    this.$EventBus.$on(
      MapEvent.REFRESH_LOUD_SPEAKER_LAYER,
      this.dealRefreshLayer
    )
    this.$EventBus.$on(
      Events.MAP_TOOL_EVT.MAP_LAYER_EXCHANGE,
      this.onMaLayerExchange
    )
    // 执法仪轨迹渲染
    this.$EventBus.$on(
      MapEvent.ANIMAL_GPS_HISTORY_LOCUS,
      ({ type, locusList, currentTime }) => {
        this.locusRefresh(type, locusList, currentTime)
      }
    )
    this.updateZoomLevel = this.updateZoomLevel.bind(this)
    // 事件筛选回调
    this.$EventBus.$on('onEventFilterChange', this.onEventFilterChange)
  },
  beforeDestroy() {
    // 取消注册的地图事件监听
    this.$EventBus.$off(MapEvent.ADD_MARKER)
    this.$EventBus.$off(MapEvent.REMOVE_MARKER)
    this.$EventBus.$off(MapEvent.CLEAR_ALL)
    this.$EventBus.$off(MapEvent.DETECT_FAILURE)
    this.$EventBus.$off(MapEvent.CLUSTER_CLICK)
    this.$EventBus.$off(MapEvent.EVENT_MAP_ZOOM_CHANGE)
    this.$EventBus.$off(MapEvent.REFRESH_LOUD_SPEAKER_LAYER)
    this.$EventBus.$off(Events.MAP_TOOL_EVT.MAP_LAYER_EXCHANGE)
    this.$EventBus.$off(Events.MAP_TOOL_EVT.MAP_LAYER_EXCHANGE)
    this.$EventBus.$off('onEventFilterChange', this.onEventFilterChange)
    _map = null
    locusLayer = null
  },
  methods: {
    ...mapMutations('map', ['setPickLnglat', 'setPassRouteSwitch']), // 从Vuex中获取设置经纬度的mutation
    ...mapMutations('captureInfo', ['setCameraLayerReady']),
    ...mapMutations('event', ['setEvtListFilterState']),
    renderCaptureCamera(needOpenDevice, needCloseDevice) {
      layerCache[LayerConst.CAMERA].layerObj.updateCaptureMarkers(
        needOpenDevice,
        needCloseDevice
      )
    },
    /**
     * 事件筛选回调
     * @param {*} e
     */
    onEventFilterChange(e) {
      this.setEvtListFilterState(e.filterState)
      const params = {
        filterState: e.filterState,
        dicName: '事件',
        dicValue: 'ALARM_EVENT',
        isDefault: 'Y',
        layerIconUrl: ' ',
        layerIconUrlS: undefined,
      }
      // 先清除图层
      this.onRemoveMarker(params)
      // 关闭详情弹窗
      this.$EventBus.$emit('closeEvtDetailModal', {})
      setTimeout(() => {
        this.onAddMarker(params)
      }, 50)
    },
    /**
     * 切换地图图层
     * @param {number} newval 切换的图层类型
     */
    onMaLayerExchange(newval) {
      if (newval === 0) {
        _map.changeLayersHj('vector', 0)
      }
      if (newval === 1) {
        _map.changeLayersHj('satellite', 1)
      }
      if (newval === 2) {
        _map.changeLayersHj('vector', 2)
      }
    },
    /**
     * 添加标记到地图
     * @param {object} param 添加标记的参数
     */
    async onAddMarker(param) {
      const { dicValue, initData, stopClick } = param
      const layer = maplayer.getLayer(dicValue)
      const layers = await layer.draw({
        ...param,
        filterState: param.filterState ?? this.$store.state.event.filterState,
        layerCode: dicValue,
        onclick: (attr, markerFeature) => {
          // 取消marker点选中
          layer.cancelSelected(dicValue)
        },
        initData: initData || null,
        stopClick: stopClick || false,
        region: this.formatSelectedRegion,
        isCheckMode: this.$store.state.map.checkMode,
        refresh: false,
        mapInstance: _map,
      })
      if (!layers) {
        return
      }
      // 摄像机图层的话需设置已准备好，用于抓拍数据监听启动
      if (dicValue === LayerConst.CAMERA) {
        this.setCameraLayerReady(true)
      }
      layerCache[dicValue] = {
        layer: layers.markersLayer,
        objs: layers.markers,
        layerObj: layer,
        initData: initData || null,
      }
      this.addInterval(dicValue)
    },
    // 如果是畜牧/云广播图层则需要定时刷新
    addInterval(layerType) {
      if (
        [LayerConst.ANIMAL, LayerConst.LOUDSPEAKER].includes(layerType) &&
        !layerCache[layerType].timer
      ) {
        layerCache[layerType].timer = setInterval(() => {
          this.dealRefreshLayer(layerType)
        }, window.sysConfig.HUS_LAYER_UPDATE * 60 * 1000)
      }
    },
    // 定时器内容
    async dealRefreshLayer(layerType) {
      layerCache?.[layerType]?.layer?.getSource?.()?.clear?.(true)
      const layer = maplayer.getLayer(layerType)
      const layers = await layer.draw({
        layerCode: layerType,
        onclick: (attr, markerFeature) => {
          // 取消marker点选中
          layer.cancelSelected(layerType)
        },
        initData: layerCache[layerType].initData,
        stopClick: null,
        region: this.formatSelectedRegion,
        isCheckMode: this.$store.state.map.checkMode,
        refresh: true,
      })
      layerCache?.[layerType]?.layer
        ?.getSource?.()
        ?.addFeatures?.(layers.markers)
    },
    /**
     * 从地图上移除标记
     * @param {object} param 移除标记的参数
     */
    onRemoveMarker(param) {
      const { dicValue, isRefresh } = param
      // 云广播删除所有的播放动效
      //   if (dicValue === LayerConst.LOUDSPEAKER) {
      //     layerCache[LayerConst.LOUDSPEAKER].layerObj.removeAllPlayingBg();
      //   }
      // 清除定时刷新任务
      if (layerCache?.[dicValue]?.timer) {
        clearInterval(layerCache[dicValue].timer)
      }
      if (dicValue == 'ANIMAL') {
        const animalLayer = maplayer.getLayer(dicValue)
        animalLayer.cfg.selectedId = null
      }
      // 摄像机图层的话需设置摄像机图层未准备好
      if (dicValue === LayerConst.CAMERA) {
        this.setCameraLayerReady(false)
        layerCache?.[LayerConst.CAMERA]?.layerObj?.removeAllCaptureWin?.()
        // 如果此时打开了抓拍窗口，则关闭
        if (this.getPassRouteSwitch) {
          this.setPassRouteSwitch(false)
        }
      }
      let layer = layerCache.getLayer(dicValue).layer
      CommonMap.removeLayer(_map, layer)
      delete layerCache[dicValue]
      // RAILWAY_LINE 需要单独处理,显隐铁路线路图层
      maplayer.getLayer(dicValue).clear(dicValue, _map)
    },
    /**
     * 处理故障检测失败的事件
     * @param {object} param 失败的参数，包含show属性
     */
    onDetectFailure({ show }) {
      // 过滤掉故障点
      Object.keys(layerCache).forEach(key => {
        if (key === 'getLayer' || !layerCache.getLayer(key)) {
          return
        }
        let layer = layerCache.getLayer(key)
        if (key == 'ANIMAL') {
          this.onRemoveMarker({ dicValue: key, isRefresh: true })
          this.onAddMarker({ dicValue: key, initData: null, stopClick: null })
          return
        }
        layer.layerObj?.filterFault &&
          layer.layerObj.filterFault({
            layer: layerCache.getLayer(key).layer,
            show,
          })
      })
    },
    /**
     * 清除地图上的所有标记和图层
     * @param {Function} callback 清除后的回调函数
     */
    clearAll(callback) {
      Object.keys(layerCache).forEach(key => {
        if (key !== 'getLayer') {
          if (layerCache[key].timer) {
            clearInterval(layerCache[key].timer)
          }
          CommonMap.removeLayer(_map, layerCache.getLayer(key).layer)
          maplayer.getLayer(key).clear(key)
          delete layerCache[key]
        }
      })
      leftWrap.closeAllContent()
      bottomWrap.closeAllContent()
    },
    /**
     * 初始化地图
     */
    async initMap() {
      // await getMapTiles()
      const { MAP_CFG } = window.hjCfg
      const _mapRef = this.mapRef.getMapRef(this.mapId)
      _map = createMap(_mapRef, {
        center: this.center || MAP_CFG.defCenter || [116.38813, 39.89446],
        zoom: MAP_CFG.defZoom || 12,
        maxZoom: MAP_CFG.maxZoom || 17,
        minZoom: MAP_CFG.minZoom || 1,
      })
      if (_mapRef.mapType === '2D') {
        // 监听层级变化
        //   _map.getView().on('change:resolution', this.updateZoomLevel);
        //畜牧GPS轨迹图层
        locusLayer = new CTMapOl.layer.Vector({
          source: new CTMapOl.source.Vector(),
        })
        _map.addLayer(locusLayer)

        // 注册地图点击事件
        _map.on('click', e => {
          const lonlat = _map.getCoordinateFromPixel(e.pixel)
          // 获取地图点位-工具箱-座标拾取
          const lnglat = transformPointToMercator(lonlat, '4326').join(',')
          this.setPickLnglat(lnglat)
        })
        // 注册地图单击事件
        _map.on('singleclick', this.singleclickHandle)
        // 注册鼠标移动事件
        _map.on('pointermove', this.pointermoveHandle)
      }
      // 加载
      this.onMapLoad(_map)
      // mouseAreaTool(_map)
      // mouseLineTool(_mapRef)
    },
    /**
     * 地图单击事件
     */
    singleclickHandle(e) {
      let pixel = _map.getEventPixel(e.originalEvent)
      let currentFeature = _map.forEachFeatureAtPixel(
        pixel,
        function (feature, layer) {
          return feature
        }
      )
      CommonMap.closeTip(_map)
      // 如果点击到地图上的锚点对象，currentFeature就不为空
      if (currentFeature) {
        if (
          currentFeature.values_.features &&
          currentFeature.values_.features.length === 1 &&
          currentFeature.values_.features[0].values_.attributes.onclick
        ) {
          // 聚合图层散落出来的点才可以点击
          currentFeature.values_.features[0].values_.attributes.onclick(
            currentFeature.values_.features[0].values_.attributes,
            currentFeature
          )
          // console.info(
          //   '-------click map02-----------',
          //   currentFeature.values_.features[0].values_.attributes
          // )
        } else if (
          currentFeature.values_.features &&
          currentFeature.values_.features.length > 1 &&
          _map.getView().getZoom() === _map.getView().getMaxZoom()
        ) {
          currentFeature.values_.attributes.onclick(
            currentFeature.values_.features,
            currentFeature.getGeometry()
          )
        } else if (
          currentFeature.values_.attributes &&
          currentFeature.values_.attributes.onclick
        ) {
          currentFeature.values_.attributes.onclick(
            currentFeature.values_.attributes,
            currentFeature
          )
        } else {
          console.warn('没有绑定onclick方法')
        }
      }
    },
    /**
     * 注册鼠标移动事件
     */
    pointermoveHandle(e) {
      let pixel = _map?.getEventPixel(e.originalEvent)
      let feature = _map?.forEachFeatureAtPixel?.(pixel, featureObj => {
        return featureObj
      })
      // 线、面要素不做鼠标移入样式修改
      if (feature == undefined || feature.getGeometry().getType() != 'Point') {
        _map.getTargetElement().style.cursor = 'auto'
      } else {
        _map.getTargetElement().style.cursor = 'pointer'
      }
    },
    updateZoomLevel: debounce(function (zoom) {
      this.changeCustomOverlayVisable(zoom)
    }, 300),
    changeCustomOverlayVisable(zoom) {
      //   if (layerCache[LayerConst.LOUDSPEAKER]) {
      //     if (zoom < 8) {
      //       layerCache[LayerConst.LOUDSPEAKER].layerObj.hiddenAllPlayingBg();
      //     } else {
      //       layerCache[LayerConst.LOUDSPEAKER].layerObj.showAllPlayingBg();
      //     }
      //   }
      if (layerCache[LayerConst.CAMERA]) {
        if (zoom < 8) {
          layerCache[LayerConst.CAMERA].layerObj.hiddenAllCaptureWin()
        } else {
          layerCache[LayerConst.CAMERA].layerObj.showAllCaptureWin()
        }
      }
    },
    onMapZoomChange(zoom) {
      CommonMap.closeTip(_map)
      this.updateZoomLevel(zoom)
    },
    // 畜牧设备轨迹渲染
    locusRefresh(type, locusList, currentTime) {
      console.info('locusRefresh---------', type, locusList, currentTime)
      // 清空以前存在的历史轨迹
      locusLayer && locusLayer.getSource().clear(true)
      // 如果轨迹数据为空则不往下执行
      if (!locusList || locusList.length == 0 || type == 'close') {
        return
      }
      let markerArr = []
      // 渲染上起点锚点
      markerArr.push(
        imageMarker(
          [locusList[0].lng * 1, locusList[0].lat * 1],
          {
            icon: require(`./markicon/locus_start.svg`),
            text: '图标',
            anchorOrigin: 'bottom-left',
            anchor: [15, 0],
          },
          {
            type: 'zhifajilu_son',
            isClick: true,
            lnglat: [locusList[0].lng * 1, locusList[0].lat * 1],
          }
        )
      )
      // 计算出轨迹的经纬度集合
      const path = locusList.map(item => {
        return [item.lng * 1, item.lat * 1]
      })
      // 把历史轨迹渲染到地图上
      markerArr.push(
        polyline(
          path,
          {
            strokeColor: type == 'realTime' ? '#09E6C8' : '#909399', //线颜色
            strokeOpacity: 1,
            strokeWeight: 6, //线宽
            zIndex: 1000,
          },
          { type: 'road', isClick: true, lnglat: path[0] }
        )
      )
      // 如果是历史轨迹播放，需要渲染结束点，和实时轨迹
      if (type == 'history') {
        // 增加结束点
        markerArr.push(
          imageMarker(
            [
              locusList[locusList.length - 1].lng * 1,
              locusList[locusList.length - 1].lat * 1,
            ],
            {
              icon: require(`./markicon/locus_end.svg`),
              text: '图标',
              anchorOrigin: 'bottom-left',
              anchor: [15, 0],
            },
            {
              type: 'zhifajilu_son',
              isClick: true,
              lnglat: [
                locusList[locusList.length - 1].lng * 1,
                locusList[locusList.length - 1].lat * 1,
              ],
            }
          )
        )
        // 实时轨迹:先计算已经播放的轨迹点集合
        let lastItemIndex = locusList.length
        let foundFlag = false
        for (let i = 0; i < locusList.length && !foundFlag; i++) {
          if (
            locusList[i].pt == currentTime ||
            dayjs(locusList[i].pt).isAfter(dayjs(currentTime))
          ) {
            lastItemIndex = i
            foundFlag = true
          }
        }
        markerArr.push(
          polyline(
            path.slice(0, lastItemIndex),
            {
              strokeColor: '#09E6C8', //线颜色
              strokeOpacity: 1,
              strokeWeight: 6, //线宽
              zIndex: 1001,
            },
            { type: 'road', isClick: true, lnglat: path[0] }
          )
        )
        if (lastItemIndex == 0) {
          lastItemIndex = 1
        }
        // 当前点位锚点
        markerArr.push(
          imageMarker(
            path[lastItemIndex - 1],
            {
              icon: require(`./markicon/icon_location.svg`),
              text: '图标',
              anchorOrigin: 'bottom-left',
              anchor: [15, 0],
            },
            {
              type: 'zhifajilu_son',
              isClick: true,
              lnglat: path[lastItemIndex - 1],
            }
          )
        )
      }
      locusLayer.getSource().addFeatures(markerArr)
    },
  },
}
</script>