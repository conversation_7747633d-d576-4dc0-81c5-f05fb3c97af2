@import "../common";

.broad-cast-ctl-wrap {
  width: 400px;
  padding: 0 12px;
  border-radius: 8px;
  box-sizing: border-box;
  overflow: hidden;

  .ctl-content {
    font-size: 16px;
  }

  :deep .ctl-header {
    .ctl-stat {
      font-size: 14px;
      color: #fb913c;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: auto;
      margin-right: @unit;

      i {
        width: 42px;
        height: 16px;
        margin-left: @unit;
      }
    }
  }
  .ctl-sound {
    width: 100%;
    height: auto;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: space-around;

    > div {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      min-height: 70px;
    }

    .sound-ctl {
      margin-left: auto;
      margin-right: 10px;
      width: 230px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      position: relative;
      .volum-val {
        position: absolute;
        bottom: 35px;
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #4f9fff;
      }
      .volum-msg {
        position: absolute;
        color: #2a5adf;
        font-size: 14px;
        top: 30px;
        width: 300px;
      }
      .el-slider {
        width: 165px;
      }
    }
  }

  .ctl-tab {
    width: 100%;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(19, 115, 231, 0.2);
    border-radius: 4px;

    :deep > span {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 50%;
      height: 100%;
      cursor: pointer;

      .icon {
        margin-right: 10px;

        svg {
          fill: #ffffff;
        }
      }

      &.selected {
        background: url("./img/tab_selected.svg") 100% 100% no-repeat;
        background-size: 100% 100%;
      }
    }
  }

  .ctl-tab-content {
    margin-top: 10px;
    height: 250px;
    width: 376px;

    :deep .common-table-wrap {
      height: 250px !important;
    }

    .tab-content {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;

      &.record {
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #4f9fff;
        font-weight: 400;

        .icon {
          height: 80px !important;
          width: 80px !important;
          margin-bottom: 10px;
          cursor: pointer;

          svg {
            height: 80px !important;
            width: 80px !important;
          }
        }
      }
    }
  }
}
