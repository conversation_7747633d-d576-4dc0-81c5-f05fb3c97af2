<template>
  <div class='sound-ctl'>
    <!-- 静音图标，点击后静音 -->
    <icon-sound-mute />
    <!-- 音量调节滑块容器 -->
    <div style='position: relative'>
      <!-- 显示当前音量值 -->
      <div class='volum-val' :style='`left: calc(${volum}% - 7px); `'>{{ volum }}</div>
      <!-- 音量滑块，用于调节音量大小 -->
      <el-slider v-model='volum' @change='volumSpeakerChange' :show-tooltip='false'>
      </el-slider>
    </div>
    <!-- 最大音量图标，点击后恢复音量 -->
    <icon-sound-loud />
    <!-- 当音量为0时，显示静音提示信息 -->
    <div class='volum-msg' v-if='volum===0'>您已设置为静音状态，将无法听到声音！</div>
  </div>
</template>
<script>
import IconSoundLoud from '@/components/page/ScreenPages/IntegratedMonitor/Icons/icon-sound-loud.vue';
import IconSoundMute from '@/components/page/ScreenPages/IntegratedMonitor/Icons/icon-sound-mute.vue';

/**
 * 音量控制组件
 * 用于在界面中显示和控制音量的大小，并提供静音功能。
 *
 * @component IconSoundLoud 最大音量图标组件
 * @component IconSoundMute 静音图标组件
 * @prop {Number} volum 音量大小，取值范围0-100，0表示静音
 * @prop {Function} volumSpeakerChange 音量改变时的回调函数
 */
export default {
  components: {
    IconSoundMute, // 引入静音图标组件
    IconSoundLoud  // 引入最大音量图标组件
  },
  data() {
    return {};
  },
  props: {
    volum: {
      type: Number, // 音量属性类型为数字
      default: 30   // 默认音量为30%
    },
    volumSpeakerChange: {
      type: Function, // 音量改变时的回调函数类型为函数
      default: () => {
        console.log('volumSpeakerChange') // 默认的音量改变回调函数，输出日志
      }
    }
  },
  mounted() {
    // 组件挂载后的逻辑（此处为空）
  },
  methods: {}
};
</script>
<style lang='less' src='./index.less' />
