<template>
  <!-- 控制面板组件，用于云广播喊话功能的控制和显示 -->
  <CtlPanel class="broad-cast-ctl-wrap" :close="doClose">
    <!-- 标题区域 -->
    <template v-slot:title>云广播-喊话</template>
    <!-- 根据设备状态显示当前播放状态图标 -->
    <template
      v-if="deviceInfo?.deviceStatus?.deviceStatus === deviceStatus.playing.code || recordStat === recordStatEnum.recording || metaRecordStat === recordStatEnum.recording"
      v-slot:self-icon
    >
      <span class="ctl-stat">
        {{ deviceStatus.playing.name }}
        <i :class="`icon icon-playing-sound`" />
      </span>
    </template>
    <!-- 控制面板的内容区域，包括音量控制和喊话类型选择 -->
    <template v-slot:content>
      <div class="ctl-sound">
        <!-- 如果设备类型包括音柱，显示音柱相关控制 -->
        <div v-if="deviceInfo.deviceTypeName.includes(deviceType.soundColumn.code)">
          {{ deviceType.soundColumn.name }}
          <SoundCtl :volum="volum" :volum-speaker-change="volumChange" />
          <el-checkbox @change="volumeMute">静音</el-checkbox>
        </div>
        <!-- 如果设备类型包括扬声器，显示扬声器相关控制 -->
        <div v-if="deviceInfo.deviceTypeName.includes(deviceType.speaker.code)">
          {{ deviceType.speaker.name }}
          <SoundCtl :volum="volumSpeaker" :volum-speaker-change="volumSpeakerChange" />
          <el-checkbox @change="volumeSpeakerMute">静音</el-checkbox>
        </div>
      </div>
      <!-- 标签页用于切换实时喊话和素材喊话功能 -->
      <div class="ctl-tab">
        <span :class="selectTab === tabs.realtime ? 'selected':''" @click="tabClick(tabs.realtime)">
          <IconReltimeBroadcast />实时喊话
        </span>
        <span :class="selectTab === tabs.meta? 'selected':''" @click="tabClick(tabs.meta)">
          <IconBroadcastList />素材喊话
        </span>
      </div>
      <!-- 根据选择的标签页显示对应内容 -->
      <div v-if="selectTab === tabs.realtime" class="ctl-tab-content" style="position: relative">
        <!-- 实时喊话的录音状态控制 -->
        <div v-if="recordStat=== recordStatEnum.recorded" class="tab-content record">
          <icon-unrecord @click.native="startSpeaking" />点击按钮可进行实时喊话！
        </div>
        <div v-if="recordStat=== recordStatEnum.recording" class="tab-content record">
          <icon-recording @click.native="stopSpeaking" />喊话中！点击按钮完成喊话！
        </div>
        <!-- 提示框组件，用于确认操作 -->
        <confirm ref="confirmPop" />
      </div>
      <!-- 素材喊话的详细内容组件 -->
      <tab-meta
        v-show="selectTab === tabs.meta"
        ref="metaPlayer"
        :device="propData.devices"
        :volum="volum"
        :volumSpeaker="volumSpeaker"
        :changePlayingStat="changePlayingStat"
      />
    </template>
  </CtlPanel>
</template>

<script>
import {
  BroadCast,
  BroadcastState,
} from '@/components/page/ScreenPages/IntegratedMonitor/BroadCastCtl/BroadCast'
import SoundCtl from '@/components/page/ScreenPages/IntegratedMonitor/BroadCastCtl/SoundCtl.vue'
import CommonTable from '@/components/page/ScreenPages/IntegratedMonitor/CommonTable/index.vue'
import Confirm from '@/components/page/ScreenPages/IntegratedMonitor/Confirm'
import CtlPanel from '@/components/page/ScreenPages/IntegratedMonitor/CtlPanel'
import { enums } from '@/components/page/ScreenPages/IntegratedMonitor/DetailInfo/enums'
import IconBroadcastList from '@/components/page/ScreenPages/IntegratedMonitor/Icons/icon-broadcast-list'
import IconRecording from '@/components/page/ScreenPages/IntegratedMonitor/Icons/icon-recording.vue'
import IconReltimeBroadcast from '@/components/page/ScreenPages/IntegratedMonitor/Icons/icon-reltime-broadcast'
import IconSoundLoud from '@/components/page/ScreenPages/IntegratedMonitor/Icons/icon-sound-loud.vue'
import IconSoundMute from '@/components/page/ScreenPages/IntegratedMonitor/Icons/icon-sound-mute.vue'
import IconUnrecord from '@/components/page/ScreenPages/IntegratedMonitor/Icons/icon-unrecord.vue'
import TabMeta from './tab-meta'
import LayerConst from '../Map/LayerConst'
import { Events } from '../enum/EventsEnum'

// 地图事件常量
const MapEvent = Events.MapEvent

const deviceStatus = enums.LOUDSPEAKER.deviceStatus
const deviceType = enums.LOUDSPEAKER.deviceType
const tabs = {
  realtime: 'realtime',
  meta: 'meta',
}
const recordStatEnum = {
  recording: '录音中',
  recorded: '录音完成',
}
export default {
  props: {
    close: {
      type: Function,
      default: () => {
        console.log('close')
      },
    },
    propData: {
      type: Object,
      default: {},
    },
  },
  components: {
    SoundCtl,
    Confirm,
    CtlPanel,
    CommonTable,
    IconUnrecord,
    IconRecording,
    IconSoundLoud,
    IconSoundMute,
    IconReltimeBroadcast,
    IconBroadcastList,
    TabMeta,
  },
  data() {
    return {
      deviceType,
      deviceStatus,
      volum: 30,
      volumHis: 30,
      volumSpeaker: 30,
      volumSpeakerHis: 30,
      selectTab: tabs.realtime,
      tabs,
      recordStatEnum,
      recordStat: recordStatEnum.recorded,
      // 素材喊话的状态
      metaRecordStat: recordStatEnum.recorded,
      deviceInfo: {
        deviceTypeName: [],
        deviceStatus: [],
      },
      broadcast: {},
    }
  },
  mounted() {
    this.getDeviceData()
  },
  beforeDestroy() {
    // 关闭控制面板前，如果正在喊话则停止喊话
    if (this.recordStat === recordStatEnum.recording) {
      this.stopSpeaking()
    }
  },
  methods: {
    /**
     * 素材喊话改变设备的播放状态
     */
    changePlayingStat(isPlaying) {
      if (isPlaying) {
        // 插队逻辑，素材喊话成功了就认为实时喊话结束了
        this.recordStat = recordStatEnum.recorded
        this.metaRecordStat = recordStatEnum.recording
      } else {
        this.metaRecordStat = recordStatEnum.recorded
      }
    },
    /**
     * 切换标签页
     * @param {string} key - 标签页的键值
     */
    tabClick(key) {
      // 切换标签页
      this.selectTab = key
    },

    /**
     * 音量变化时触发的事件处理函数
     * @param {number} val - 新的音量值
     */
    volumChange(val) {
      // 音量变化事件处理
      this.volum = val
      this.volumHis = val
    },

    /**
     * 控制静音状态
     * @param {boolean} val - 静音状态的标志
     */
    volumeMute(val) {
      // 静音控制
      if (val) {
        this.volum = 0
        return
      }
      this.volum = this.volumHis
    },

    /**
     * 扬声器音量变化时触发的事件处理函数
     * @param {number} val - 新的扬声器音量值
     */
    volumSpeakerChange(val) {
      // 扬声器音量变化事件处理
      this.volumSpeaker = val
      this.volumSpeakerHis = val
    },

    /**
     * 控制扬声器的静音状态
     * @param {boolean} val - 扬声器静音状态的标志
     */
    volumeSpeakerMute(val) {
      // 扬声器静音控制
      if (val) {
        this.volumSpeaker = 0
        return
      }
      this.volumSpeaker = this.volumSpeakerHis
    },

    /**
     * 开始喊话操作
     */
    startSpeaking() {
      const { devices } = this.propData
      if (
        devices.some(i =>
          [deviceStatus.fault.code, deviceStatus.offline.code].includes(
            i.deviceStatus
          )
        )
      ) {
        this.$refs.confirmPop.show({
          msg: '设备故障/离线，暂时无法工作。',
        })
        return
      }
      const { deviceStatus: stat } = this.deviceInfo
      // 检查是否静音或设备正在播放中
      if (this.volum === 0 || this.volumSpeaker === 0) {
        this.$refs.confirmPop.show({
          msg: '您已设置为静音状态，将无法听到声音！',
          okHandle: () => {
            this.pushAudio()
          },
        })
        return
      }
      if (stat && stat.deviceStatus === deviceStatus.playing.code) {
        this.$refs.confirmPop.show({
          msg: '广播设备工作中，请稍候再试',
        })
        return
      }
      this.pushAudio()
    },

    /**
     * 向设备推送音频
     */
    async pushAudio() {
      // 根据设备列表，初始化广播实例并启动说话功能，针对不同的设备类型设置相应的音量
      const { devices } = this.propData
      for (let i = 0; i < devices.length; i += 1) {
        let item = devices[i]
        let broadCast = new BroadCast(item.deviceCode)
        await broadCast.startTalkingV2({
          deviceCode: item.deviceCode,
          volum:
            item.deviceType === deviceType.speaker.code
              ? this.volumSpeaker
              : this.volum,
        })
        this.broadcast[item.deviceCode] = broadCast
      }
      // 检查是否有设备成功开启，如果没有则记录错误并返回
      const openStream = Object.keys(this.broadcast).find(key => {
        return (
          this.broadcast[key] &&
          this.broadcast[key].state === BroadcastState.OPEN_SUCCESS
        )
      })
      if (!openStream) {
        console.error('设备未开启成功，请检查设备状态！')
        return
      }
      // 插队逻辑，这个时候如果实时喊话成功，那么素材喊话认为已经结束了
      this.metaRecordStat = recordStatEnum.recorded
      this.$refs.metaPlayer.endTimer()
      this.recordStat = recordStatEnum.recording
      // 触发刷新云广播图层方法
      this.$EventBus.$emit(
        MapEvent.REFRESH_LOUD_SPEAKER_LAYER,
        LayerConst.LOUDSPEAKER
      )
      // 开始录音，并在录音成功时将录音流发送到所有已开启的设备
      //   Recorder.start((stream) => {
      //     this.recordStat = recordStatEnum.recording;
      //     Object.keys(this.broadcast).forEach(key => {
      //       this.broadcast[key].sendStream(stream);
      //     });
      //   }, (err) => {
      //     this.$message.error(err);
      //     this.recordStat = recordStatEnum.recorded;
      //     Object.keys(this.broadcast).forEach(key => {
      //       this.broadcast[key].stopTalkingV2(2);
      //     });
      //   });
    },

    /**
     * 停止喊话
     */
    stopSpeaking() {
      this.recordStat = recordStatEnum.recorded
      Object.keys(this.broadcast).forEach(key => {
        this.broadcast[key].stopTalkingV2(1)
      })
      // 触发刷新云广播图层方法
      this.$EventBus.$emit(
        MapEvent.REFRESH_LOUD_SPEAKER_LAYER,
        LayerConst.LOUDSPEAKER
      )
    },

    /**
     * 关闭面板前的处理函数，用于停止正在进行的喊话操作
     */
    doClose() {
      this.close()
    },

    /**
     * 获取设备数据，包括设备状态和类型
     */
    async getDeviceData() {
      // 获取设备数据，包括设备状态和类型
      const { devices } = this.propData
      const playingDeviceStatus = devices.find(
        item =>
          item.deviceStatus === enums.LOUDSPEAKER.deviceStatus.playing.code
      )
      const deviceTypes = _.uniq(_.map(devices, 'deviceType'))
      this.deviceInfo = {
        deviceTypeName: deviceTypes,
        deviceStatus: playingDeviceStatus,
      }
    },
  },
}
</script>

<style scoped lang='less' src='./index.less' />
