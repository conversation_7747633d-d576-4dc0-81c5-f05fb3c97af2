<template>
  <div class="ctl-tab-content">
    <!-- 使用CommonTable组件展示素材列表，高度固定为190px -->
    <!--  :height="190" -->
    <CommonTable :table-data="meta">
      <template v-slot:column>
        <!-- 素材类别列，显示溢出提示，固定宽度60px -->
        <el-table-column prop="metaTypes" label="素材类别" show-overflow-tooltip width="60"></el-table-column>
        <!-- 素材名称列，显示溢出提示，固定宽度105px -->
        <el-table-column prop="name" label="素材名称" show-overflow-tooltip width="100"></el-table-column>
        <!-- 素材内容列，固定宽度60px，展示声音素材的播放控件和文本素材的预览 -->
        <el-table-column label="素材内容" width="60">
          <template slot-scope="scope">
            <!-- 声音素材播放控件，包括播放图标、时长和文本素材的预览 -->
            <div class="sound-wrap">
              <div
                :class="`sound-info ${
                  localPlayingMeta === scope.row.materialId ? 'onplay' : ''
                }`"
                @click="
                  playMp3(
                    scope.row.audioUrl,
                    scope.row.materialId,
                    scope.row.duration == null ? 0 : scope.row.duration
                  )
                "
              >
                <div class="sound-icon">
                  <IconPlayingWave
                    :class="`${
                      localPlayingMeta === scope.row.materialId ? 'active' : ''
                    }`"
                  />
                </div>
                <div class="info-wrap">{{ scope.row.duration == null ? 0 : scope.row.duration }}″</div>
              </div>
              <!-- 文本素材预览，通过el-popover组件展示 -->
              <div class="sound-ctl">
                <el-popover
                  popper-class="common-table-tip"
                  placement="top"
                  :title="scope.row.name"
                  width="200"
                  trigger="hover"
                  :content="scope.row.content"
                >
                  <img
                    alt="预览"
                    slot="reference"
                    :src="
                      require(`@/assets/images/home/<USER>/icon_text.svg`)
                    "
                  />
                </el-popover>
              </div>
            </div>
          </template>
        </el-table-column>
        <!-- 操作列，固定宽度50px，展示播放控制按钮 -->
        <el-table-column label="操作" width="45">
          <template slot-scope="scope">
            <!-- 根据素材状态显示不同的播放控制图标，并绑定点击事件 -->
            <template v-if="scope.row.stat === metaStat.stop">
              <icon-meta-stop @click.native="metaPlay(scope.row)" />
            </template>
            <template v-if="scope.row.stat === metaStat.playing">
              <icon-playing style="color: #5297f3" />
            </template>
            <template v-if="scope.row.stat === metaStat.unplay">
              <icon-meta-play @click.native="metaPlay(scope.row)" />
            </template>
          </template>
        </el-table-column>
      </template>
    </CommonTable>
    <!-- 引入确认框组件，用于播放素材前的确认操作 -->
    <confirm ref="confirmPop" />
    <!-- 引入音频播放器组件，用于播放声音素材 -->
    <AudioPlayer ref="audioPlayer" :audioSrc="audioSrc" audioId="subscribeAudio" />
  </div>
</template>

<script>
import api from '@/api'
import {
  geBroadcastMetaList,
  playMeta,
  metaSpeaker,
} from '@/api/service/imScreenService'
import AudioPlayer from '@/components/page/ScreenPages/IntegratedMonitor/AudioPlayer/index.vue'
import CommonTable from '@/components/page/ScreenPages/IntegratedMonitor/CommonTable'
import Confirm from '@/components/page/ScreenPages/IntegratedMonitor/Confirm'
import { enums } from '@/components/page/ScreenPages/IntegratedMonitor/DetailInfo/enums'
import IconMetaPlay from '@/components/page/ScreenPages/IntegratedMonitor/Icons/icon-meta-play.vue'
import IconMetaStop from '@/components/page/ScreenPages/IntegratedMonitor/Icons/icon-meta-stop.vue'
import IconPlayingStat from '@/components/page/ScreenPages/IntegratedMonitor/Icons/icon-playing-stat.vue'
import IconPlayingWave from '@/components/page/ScreenPages/IntegratedMonitor/Icons/icon-playing-wave.vue'
import IconPlaying from '@/components/page/ScreenPages/IntegratedMonitor/Icons/icon-playing.vue'
import vue from '@/main'
import LayerConst from '../../Map/LayerConst'
import { Events } from '../../enum/EventsEnum'

// 地图事件常量
const MapEvent = Events.MapEvent

const metaStat = {
  unplay: 'unplay',
  stop: 'stop',
  playing: 'playing',
}
// T-文本 A-音频
const contentTypeDict = {
  T: '1',
  A: '2',
}
export default {
  props: {
    device: {
      type: Array,
      default: [],
    },
    volum: {
      type: Number,
      default: 50,
    },
    volumSpeaker: {
      type: Number,
      default: 50,
    },
    changePlayingStat: {
      type: Function,
      default: () => {
        console.log('changePlayingStat')
      },
    },
  },
  components: {
    AudioPlayer,
    IconPlayingStat,
    IconPlaying,
    Confirm,
    IconMetaStop,
    IconMetaPlay,
    CommonTable,
    IconPlayingWave,
  },
  data() {
    return {
      metaStat,
      audioSrc: null, // 语音播放资源
      playingMeta: null, // 当前播放的语音元数据
      localPlayingMeta: null, // 本地正在播放的语音元数据
      meta: [], // 素材列表数据
      voicePlayTimer: null, // 语音播放定时器
    }
  },
  mounted() {
    this.getMeta()
  },
  beforeDestroy() {
    this.endTimer()
  },
  methods: {
    /**
     * 结束定时任务
     */
    endTimer() {
      this.initMeta()
      this.clearTimer && clearTimeout(this.clearTimer)
    },
    /**
     * 播放mp3音频
     * @param {string} audioUrl 音频URL
     * @param {string} key 音频唯一标识
     */
    async playMp3(audioUrl, key, duration) {
      this.$refs.audioPlayer.stop()
      this.voicePlayTimer && clearTimeout(this.voicePlayTimer)
      if (!this.localPlayingMeta || this.localPlayingMeta !== key) {
        this.audioSrc = audioUrl
        this.localPlayingMeta = key
        // 设置定时器以在音频播放结束后暂停播放并重置状态
        this.voicePlayTimer = setTimeout(() => {
          this.$refs.audioPlayer.stop()
          this.audioSrc = null
          this.localPlayingMeta = null
        }, (duration + 1) * 1000)
        return
      }
      this.audioSrc = null
      this.localPlayingMeta = null
    },
    /**
     * 初始化素材状态
     */
    initMeta() {
      this.meta.forEach(item => {
        item.stat = 'unplay'
      })
    },
    /**
     * 素材播放
     * @param {Object} row 当前行数据
     */
    async metaPlay(row) {
      const devices = this.device.map(item => {
        return {
          deviceCode: item.deviceCode,
          paramVolume:
            item.deviceType === enums.LOUDSPEAKER.deviceType.soundColumn.code
              ? this.volum
              : this.volumSpeaker,
        }
      })
      const { contentId, inputType, materialId, duration } = row
      if (!contentId) {
        this.$message.warning('素材异常，请重新上传！')
        return
      }
      // 异步调用添加播放任务接口
      const [success] = await metaSpeaker({
        hornOptType: 0,
        vmHornTaskLists: [
          {
            contentId,
            materialId,
            contentType: contentTypeDict[inputType],
            speechRate: 50,
            xunfeiVcn: 'x2_yezi',
            playOrder: 1,
          },
        ],
        devices,
        taskType: '1',
      })
      // 调用成功
      if (success) {
        this.$message.success('广播中')
        this.initMeta()
        // 前台的播放动效
        this.meta.find(i => i.materialId === materialId).stat = 'playing'
        this.changePlayingStat(true)
        // 触发刷新云广播图层方法
        this.$EventBus.$emit(
          MapEvent.REFRESH_LOUD_SPEAKER_LAYER,
          LayerConst.LOUDSPEAKER
        )
        // 无法获取真实的播放状态，前台根据素材时长切换回原始播放图标
        setTimeout(() => {
          this.meta.find(i => i.materialId === materialId).stat = 'unplay'
        }, (duration + 1) * 1000)
        // 设备图标的切换逻辑
        this.clearTimer && clearTimeout(this.clearTimer)
        this.clearTimer = setTimeout(() => {
          this.changePlayingStat(false)
          // 触发刷新云广播图层方法
          this.$EventBus.$emit(
            MapEvent.REFRESH_LOUD_SPEAKER_LAYER,
            LayerConst.LOUDSPEAKER
          )
        }, (duration + 1) * 1000)
      } else {
        this.$message.error('广播失败')
      }
    },

    /**
     * 获取素材列表
     */
    async getMeta() {
      // 初始化一个空数组用于存放处理后的结果数据
      let result = []

      // 异步调用获取广播元数据列表的函数，根据特定条件筛选
      const [success, data] = await geBroadcastMetaList({
        state: 'C',
        orderColumns: 'seq',
        isAsc: true,
        pageIndex: 1,
        pageSize: 9999,
        isHotspot: 'Y',
      })

      // 如果请求成功
      if (success) {
        // 对返回的数据列表进行处理，转换为所需的格式
        result = data.list.map((item, index) => {
          return {
            id: index, // 使用索引作为唯一标识
            metaTypes: item.materialTypeName, // 材料类型名称
            name: item.materialName, // 材料名称
            stat: 'unplay', // 初始状态为未播放
            audioUrl: item.audioUrl, // 音频URL
            content: item.content, // 内容
            ...item, // 合并原始数据的所有其他属性
          }
        })
      }

      // 将处理后的结果赋值给组件的数据属性meta
      this.meta = result
    },

    /**
     * 推送音频到设备
     * @param {Object} options 包含音频URL、成功和失败回调的选项
     */
    async pushAudio({ audioUrl, success, fail }) {
      // 初始化一个空数组，用于存放所有设备的播放元数据请求
      const fetches = []
      // 遍历设备列表
      for (let i = 0, count = this.device.length; i < count; i += 1) {
        // 解构当前设备的设备码和设备类型
        let { deviceCode, deviceType } = this.device[i]
        // 根据设备类型和音量设置参数
        let params = {
          deviceCode: deviceCode,
          // 根据设备类型选择音量
          volume:
            deviceType === enums.LOUDSPEAKER.deviceType.soundColumn.code
              ? this.volum
              : this.volumSpeaker,
          netType: '1',
          // 根据协议类型选择协议类型
          protocolType: location.protocol === 'https:' ? '2' : '5',
          networkType: '1',
          // 根据设备类型选择采样率
          sampleRate:
            deviceType === enums.LOUDSPEAKER.deviceType.soundColumn.code
              ? this.volum
              : this.volumSpeaker,
          streamCodeId: '1',
          fileUrl: audioUrl,
        }
        // 发起播放元数据请求，并将请求放入fetches数组
        fetches.push(playMeta(params))
      }
      // 如果fetches数组为空，即没有设备需要播放，直接返回
      if (fetches.length < 1) {
        return
      }
      // 等待所有设备的播放元数据请求完成
      const res = await Promise.all(fetches)
      // 以表格形式在控制台输出请求结果
      console.table(res)
      // 查找请求结果中是否有失败的请求
      const errorInfo = res.find(item => !item[0])
      // 如果有失败的请求，调用fail函数处理错误
      if (errorInfo) {
        fail(errorInfo)
      } else {
        // 如果所有请求都成功，调用success函数处理成功逻辑
        success()
      }
    },

    /**
     * 停止播放
     */
    stopPlaying() {
      //4释放锁 this.lockId = null;
      if (this.lockId) {
        const params = {
          deviceCode: this.deviceCode,
          lockId: this.lockId,
          messageId: this.messageId,
        }
        api
          .post(
            `${vue.$env.VUE_APP_REQ_PREFIX_PLAT}/devices/horn/stopTalk`,
            params
          )
          .then(res => {
            if (res.code === 200) {
              this.lockId = null
              this.pushStreamAddress = null
              this.messageId = null
              this.second = 0
            } else {
              this.$message.error(res.data.errorMsg)
            }
          })
      }
    },
  },
}
</script>

<style lang="less" src="./index.less" />
