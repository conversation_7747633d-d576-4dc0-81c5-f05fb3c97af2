<?xml version="1.0" encoding="UTF-8"?>
<svg  viewBox="0 0 176 32" preserveAspectRatio="none meet" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#5479B4" offset="0%"></stop>
            <stop stop-color="#2E3A50" offset="100%"></stop>
        </linearGradient>
        <path d="M4,0 L176,0 L176,0 L176,32 L4,32 C1.790861,32 2.705415e-16,30.209139 0,28 L0,4 C-2.705415e-16,1.790861 1.790861,4.05812251e-16 4,0 Z" id="path-2"></path>
        <linearGradient x1="0%" y1="50%" x2="91.1132952%" y2="50%" id="linearGradient-4">
            <stop stop-color="#0095FF" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#00FFF6" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="综合监测-大屏" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="1-7综合监测-云广播" transform="translate(-572.000000, -832.000000)">
            <g id="云广播喊话" transform="translate(384.000000, 721.000000)">
                <g id="编组-11" transform="translate(12.000000, 111.000000)">
                    <g id="tab_右_172_n" transform="translate(176.000000, 0.000000)">
                        <g id="形状结合" transform="translate(88.000000, 16.000000) scale(-1, 1) translate(-88.000000, -16.000000) ">
                            <mask id="mask-3" fill="white">
                                <use xlink:href="#path-2"></use>
                            </mask>
                            <use id="蒙版" fill="url(#linearGradient-1)" xlink:href="#path-2"></use>
                            <path d="M13,-9 L-10.0769231,40 L-17,40 L6.07692308,-9 L13,-9 Z M28,-9 L4.92307692,40 L-2,40 L21.0769231,-9 L28,-9 Z M45,-9 L21.9230769,40 L15,40 L38.0769231,-9 L45,-9 Z M60,-9 L36.9230769,40 L30,40 L53.0769231,-9 L60,-9 Z M76,-9 L52.9230769,40 L46,40 L69.0769231,-9 L76,-9 Z M90,-9 L66.9230769,40 L60,40 L83.0769231,-9 L90,-9 Z M107,-9 L83.9230769,40 L77,40 L100.076923,-9 L107,-9 Z M122,-9 L98.9230769,40 L92,40 L115.076923,-9 L122,-9 Z M138,-9 L114.923077,40 L108,40 L131.076923,-9 L138,-9 Z M153,-9 L129.923077,40 L123,40 L146.076923,-9 L153,-9 Z M170,-9 L146.923077,40 L140,40 L163.076923,-9 L170,-9 Z M185,-9 L161.923077,40 L155,40 L178.076923,-9 L185,-9 Z M201,-9 L177.923077,40 L171,40 L194.076923,-9 L201,-9 Z M216,-9 L192.923077,40 L186,40 L209.076923,-9 L216,-9 Z M233,-9 L209.923077,40 L203,40 L226.076923,-9 L233,-9 Z M248,-9 L224.923077,40 L218,40 L241.076923,-9 L248,-9 Z M265,-9 L241.923077,40 L235,40 L258.076923,-9 L265,-9 Z M280,-9 L256.923077,40 L250,40 L273.076923,-9 L280,-9 Z M297,-9 L273.923077,40 L267,40 L290.076923,-9 L297,-9 Z M311,-9 L287.923077,40 L281,40 L304.076923,-9 L311,-9 Z M327,-9 L303.923077,40 L297,40 L320.076923,-9 L327,-9 Z M342,-9 L318.923077,40 L312,40 L335.076923,-9 L342,-9 Z M359,-9 L335.923077,40 L329,40 L352.076923,-9 L359,-9 Z M374,-9 L350.923077,40 L344,40 L367.076923,-9 L374,-9 Z" fill="#4F9FFF" opacity="0.05" mask="url(#mask-3)"></path>
                        </g>
                        <polygon id="路径-23" fill="url(#linearGradient-4)" transform="translate(159.000000, 16.000000) scale(-1, 1) translate(-159.000000, -16.000000) " points="154 9 164 16.2804337 154 23 156.542332 16.2804337"></polygon>
                        <polygon id="路径-23" fill="url(#linearGradient-4)" points="12 9 22 16.2804337 12 23 14.5423316 16.2804337"></polygon>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>
