/**
 * 导入api和vue实例
 */
import api from '@/api';
import vue from '@/main';
import ShoutStream from '@ct/shout-stream';

/**
 * 定义广播状态常量
 */
export const BroadcastState = {
    // 打开成功
    OPEN_SUCCESS: 1,
    // 打开失败
    OPEN_FAILED: 2,
    // 未连接
    NOT_CONNECTED: 3,
    // 连接中
    CONNECTING: 4,
};

/**
 * 广播类
 * 用于管理设备的广播状态和操作
 */
export class BroadCast {
    /**
     * 构造函数
     * @param deviceCode 设备代码
     */
    constructor(deviceCode) {
        this.deviceCode = deviceCode; // 设备代码
        this.socket = null; // WebSocket连接
        this.state = BroadcastState.NOT_CONNECTED; // 广播状态
        this.volume = 50; // 默认音量
        this.shoutStream = null; // ShoutStream实例
        this.recordId = null; // 广播记录ID
        return this;
    }
    /**
     * 开始广播-shoutStream组件版本
     */
    startTalkingV2 = async ({ deviceCode, volum }) => {
        const token = sessionStorage.getItem('Admin-Token'); // 从sessionStorage获取token
        const options = {
            devices: [{ deviceCode }], // 设备列表
            heartbeat: true, // 是否启用心跳
            innerToken: token, // 内部token
            volume: Math.ceil(volum / 10) // 音量设置
        };
        this.shoutStream = new ShoutStream(options); // 创建ShoutStream实例
        let isPlay = 0; // 播放状态，2-失败 1-成功 0-未知
        try {
            const res = await this.shoutStream.shoutStreamOpen(); // 开始喊话
            console.log('shoutStreamOpen res', res); // 打印响应结果
            if (res.errorList) {
                vue.$message.error('实时喊话出现异常，请稍候重试'); // 错误提示
                return;
            }
            if (res.isWorkingDeviceNameList) {
                vue.$message.error('喇叭设备已经有人在喊话，请稍后再试'); // 错误提示
                return;
            }
            this.state = BroadcastState.OPEN_SUCCESS; // 更新状态为打开成功
            isPlay = 1; // 播放成功
        } catch (err) {
            console.log('shoutStreamOpen err', err); // 打印错误信息
            isPlay = 2; // 播放失败
            if (err.msg) {
                vue.$message.error(err.msg); // 错误提示
            } else {
                vue.$message.error('实时喊话出现异常，请稍候重试'); // 错误提示
            }
        }
        const params = {
            broadType: 'S', // 广播类型
            deviceCode,
            volume: volum,
            isPlay,
        };
        const { code, data } = await api.post(
            `${vue.$env.VUE_APP_REQ_PREFIX_BIZ}/deviceFacilities/saveHornPlayRecord`,
            params
        );
        if (code === 200 && data && data.result) {
            this.recordId = data.recordId; // 保存记录ID
        }
    };
    /**
     * 开始广播
     * @param {Object} options 配置选项
     * @param {string} options.deviceCode 设备代码
     * @param {number} options.volum 音量
     * @param {Function} options.success 成功回调函数
     * @returns {Promise<void>}
     */
    startTalking = async ({
        deviceCode,
        volum,
        success = () => {
            console.log('default implement'); // 默认成功回调
        },
    }) => {
        this.volume = volum; // 设置音量
        await this.stopTalking(); // 停止当前广播
        const iniWebSocket = () => {
            if (this.socket && String(this.socket.readyState) === '1') {
                this.socket.close(); // 关闭现有连接
                this.socket = null;
            }
            console.log('socket连接开始'); // 打印连接开始信息
            const sp = this.pushStreamAddress.split('/'); // 解析推送流地址
            this.token = sp[sp.length - 1]; // 获取token
            this.socket = new WebSocket(this.pushStreamAddress); // 创建WebSocket实例
            this.socket.onopen = () => {
                console.log('socket连接成功', this.deviceCode); // 连接成功回调
            };
            this.socket.onerror = (e) => {
                console.log('%c FXY', 'color:#f6b2b1;font-size:50px', e); // 打印错误信息
                console.log('wss地址连接失败,请稍后重试', this.deviceCode); // 错误提示
            };
            this.socket.onclose = () => {
                console.log('socket连接关闭', this.deviceCode); // 连接关闭回调
            };
        };
        const params = {
            deviceCode: deviceCode,
            volume: volum,
            protocolType: location.protocol === 'https:' ? '2' : '5', // 协议类型
            networkType: '1', // 网络类型
        };
        const { code, data, msg } = await api.post(
            `${vue.$env.VUE_APP_REQ_PREFIX_BIZ}/deviceFacilities/startTalkAndSave`,
            params
        );
        if (code === 200) {
            this.lockId = data.data.lockId; // 保存锁定ID
            this.pushStreamAddress = data.data.pushStreamAddress; // 保存推送流地址
            this.messageId = data.data.messageId; // 保存消息ID
            iniWebSocket(); // 初始化WebSocket连接
            this.state = BroadcastState.OPEN_SUCCESS; // 更新状态为打开成功
        } else {
            if (msg && msg.indexOf('资源释放失败') > -1) {
                vue.$message.error('设备占用中，无法喊话！'); // 错误提示
            } else if (msg.indexOf('Failed to connect') > -1) {
                vue.$message.warning('设备已离线'); // 错误提示
            } else {
                vue.$message.error(msg); // 错误提示
            }
            this.state = BroadcastState.OPEN_FAILED; // 更新状态为打开失败
        }
    };
    /**
     * 发送音频流
     * @param {ArrayBuffer} stream 音频流数据
     */
    sendStream = (stream) => {
        stream[8] = (this.token >> 24) & 255; // 设置RTP头部的同步源标识符
        stream[9] = (this.token >> 16) & 255;
        stream[10] = (this.token >> 8) & 255;
        stream[11] = (this.token >> 0) & 255;
        this.socket.send(stream); // 发送音频流
    };
    /**
     * 停止广播-shoutStream组件版本
     */
    stopTalkingV2 = async () => {
        if (!this.shoutStream) {
            return; // 如果没有ShoutStream实例，则直接返回
        }
        let isPlay = 0; // 播放状态，2-失败 1-成功 0-未知
        try {
            const res = await this.shoutStream.shoutStreamClosed(); // 停止喊话
            console.log('shoutStreamClosed res', res); // 打印响应结果
            const errMsgArr = (res.data || []).filter(i => i.msg); // 过滤错误信息
            if (errMsgArr.length > 0) {
                vue.$message.error('停止喊话出现异常，请稍候重试'); // 错误提示
                return;
            }
            this.shoutStream = null; // 清空ShoutStream实例
            isPlay = 1; // 播放成功
        } catch (err) {
            console.log('shoutStreamClosed err', err); // 打印错误信息
            isPlay = 2; // 播放失败
            if (err.msg) {
                vue.$message.error(err.msg); // 错误提示
            } else {
                vue.$message.error('停止喊话出现异常，请稍候重试'); // 错误提示
            }
        }
        if (!this.recordId) {
            return; // 如果没有记录ID，则直接返回
        }
        const params = {
            recordId: this.recordId, // 广播记录ID
            isPlay,
        };
        const { code, data } = await api.post(
            `${vue.$env.VUE_APP_REQ_PREFIX_BIZ}/deviceFacilities/saveHornPlayRecord`,
            params
        );
        if (code === 200 && data && data.result) {
            console.log('update saveHornPlayRecordDO', this.recordId); // 打印更新信息
            this.recordId = null; // 清空记录ID
        }
    };
    /**
     * 停止广播
     * @param {number} isPlay 播放状态（0: 未知, 1: 成功, 2: 失败）
     * @returns {Promise<void>}
     */
    stopTalking = async (isPlay = 1) => {
        const params = {
            deviceCode: this.deviceCode, // 设备代码
            lockId: this.lockId, // 锁定ID
            messageId: this.messageId, // 消息ID
            isPlay,
            volume: this.volume, // 音量
        };
        if (!this.lockId || !this.messageId) {
            return; // 若无锁定ID或消息ID，则不执行停止操作
        }
        const { code, data } = await api
            .post(
                `${vue.$env.VUE_APP_REQ_PREFIX_BIZ}/deviceFacilities/stopTalk`,
                params
            )
            .then((res) => {
                if (code === 200) {
                    this.lockId = null; // 清空锁定ID
                    this.messageId = null; // 清空消息ID
                } else {
                    vue.$message.error(data.errorMsg); // 错误提示
                }
            });
    };
}
