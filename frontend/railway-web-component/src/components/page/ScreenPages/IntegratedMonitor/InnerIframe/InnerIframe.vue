<template>
  <div
    class="now-event-statistics-index"
    :style="`width: ${pxToRem(defaultWidth)};height: ${pxToRem(defaultHeight)};`"
  >
    <div
      class="content-container"
      :style="`width: ${pxToRem(defaultWidth)};height: ${pxToRem(defaultHeight)};`"
    >
      <iframe
        v-if="propData?.compUrl"
        ref="contentIf"
        style="border: none"
        :src="propData?.compUrl"
        width="100%"
        height="100%"
      />
    </div>
  </div>
</template>

<script>
// import { Divider } from 'element-ui'
export default {
  name: 'CommonInnerIframe',
  components: {
    // 引入的组件
  },
  props: {
    // title入参
    title: {
      type: String,
      default: '组件标题',
    },
    // 定时器时间
    intervalTime: {
      // eslint-disable-next-line vue/require-prop-type-constructor
      type: String | Number,
      default: 2 * 60 * 1000, // 默认2min
    },
    // 内嵌式组件宽度
    // defaultWidth: {
    //   type: Number,
    //   default: 366
    // },
    // // 内嵌式组件高度
    // defaultHeight: {
    //   type: Number,
    //   default: 255
    // },
    // title入参
    compUrl: {
      type: String,
      default: '',
    },
    propData: {
      type: Object,
      default: () => {
        return {}
      },
    },
  },
  data() {
    return {
      dataTimer: null, // 定时器
      defaultWidth: this.propData.defaultWidth, // 摄像机总数
      defaultHeight: this.propData.defaultHeight, // 离线摄像机数
    }
  },
  computed: {},
  watch: {
    propData: {
      handler(val) {
        console.info('innerIframe watch props=', val)
        this.defaultWidth = val.defaultWidth
        this.defaultHeight = val.defaultHeight
      },
    },
  },
  mounted() {
    console.info('innerIframe mounted props==================', this.propData)
    // 定时查询事件统计接口
    this.dataTimer = setInterval(() => {
      this.reloadIframe()
    }, this.intervalTime)
  },
  beforeDestroy() {
    // 销毁前清理定时器
    clearInterval(this.dataTimer)
  },
  methods: {
    reloadIframe() {
      // 检查浏览器兼容性，确保支持 contentWindow
      if (this.$refs.contentIf.contentWindow) {
        this.$refs.contentIf.contentWindow.location.reload()
      } else {
        // 对于不支持的情况，可以尝试重新设置 src 属性
        const src = this.$refs.contentIf.src
        this.$refs.contentIf.src = ''
        this.$refs.contentIf.src = src
      }
    },
  },
}
</script>

<style scoped lang="less">
.now-event-statistics-index {
  color: #ffffff;
  letter-spacing: 0;
  background-color: #172435;

  .content-container {
    width: 100%;
    height: 100%;
  }
}
</style>
