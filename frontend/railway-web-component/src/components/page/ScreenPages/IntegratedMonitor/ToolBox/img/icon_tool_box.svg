<?xml version="1.0" encoding="UTF-8"?>
<svg width="32px" height="31px" viewBox="0 0 32 31" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>icon_工具_20_n</title>
    <defs>
        <filter x="-40.0%" y="-42.1%" width="180.0%" height="184.2%" filterUnits="objectBoundingBox" id="filter-1">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.309803922   0 0 0 0 0.623529412   0 0 0 0 1  0 0 0 0.7 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#BCDBFF" offset="99.7149701%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="99.7149701%" id="linearGradient-3">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#BCDBFF" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="综合监测-大屏" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="1-2综合监测-工具" transform="translate(-1500.000000, -97.000000)" fill-rule="nonzero">
            <g id="工具" transform="translate(1376.000000, 92.000000)">
                <g id="icon_工具_20_n" filter="url(#filter-1)" transform="translate(130.000000, 11.000000)">
                    <path d="M11.2582026,10.1306937 C11.2586695,9.91179849 11.1995285,9.69683626 11.0870185,9.50848467 L8.94721825,9.50848467 C8.83046404,9.69502955 8.77098534,9.91121903 8.77603422,10.1306937 C8.76047527,10.4634378 8.88264089,10.7880537 9.11424949,11.029392 C9.34585808,11.2707303 9.66682766,11.4078654 10.0028531,11.4090503 C10.3403423,11.4045799 10.6622138,11.2673774 10.8976441,11.0276324 C11.1330743,10.7878873 11.2627733,10.4652433 11.2582026,10.1306937 L11.2582026,10.1306937 Z" id="路径" fill="url(#linearGradient-2)"></path>
                    <path d="M12.4907275,10.1306937 C12.5346444,11.0389376 12.0708412,11.8974552 11.2838816,12.3646122 C10.4969219,12.8317692 9.51449032,12.8317692 8.72753068,12.3646122 C7.94057105,11.8974552 7.47676789,11.0389376 7.52068473,10.1306937 C7.519216,9.92031079 7.54803712,9.7107964 7.60627674,9.50848467 L0,9.50848467 L0,16.4772254 C0.00391236303,16.5738484 0.0134383711,16.6701676 0.0285306527,16.7657041 L0.0285306527,17.8687109 C0.0285306674,18.4935046 0.539475318,19 1.16975748,19 L18.8587732,19 C19.4890553,19 20,18.4935046 20,17.8687109 L20,9.50848467 L12.4051355,9.50848467 C12.4615814,9.71116537 12.4903683,9.92043139 12.4907275,10.1306937 Z M19.9771755,4.80797856 L19.9771755,3.67668948 C19.9771755,3.05189578 19.4662308,2.54540042 18.8359486,2.54540042 L13.7574893,2.54540042 L13.7574893,0.848466806 C13.7574893,0.379871528 13.3742808,0 12.9015692,0 L7.0813124,0 C6.60860077,0 6.22539228,0.379871528 6.22539228,0.848466806 L6.22539228,2.54540042 L1.16975748,2.54540042 C0.539475318,2.54540042 0.0285306527,3.05189578 0.0285306527,3.67668948 L0.0285306527,8.26406669 L20,8.26406669 L20,5.09080084 C19.997762,4.99617633 19.9901418,4.90175228 19.9771755,4.80797856 L19.9771755,4.80797856 Z M12.5592011,2.54540042 L7.48644793,2.54540042 L7.48644793,1.85531407 C7.48644793,1.54291723 7.74192025,1.28966955 8.05706133,1.28966955 L11.9657632,1.28966955 C12.1170993,1.28966955 12.262237,1.34926407 12.3692478,1.45534299 C12.4762586,1.56142192 12.5363766,1.70529582 12.5363766,1.85531407 L12.5592011,2.54540042 Z" id="形状" fill="url(#linearGradient-3)"></path>
                </g>
            </g>
        </g>
    </g>
</svg>
