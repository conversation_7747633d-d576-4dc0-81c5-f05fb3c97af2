<!--
 * @Description:
 * @Autor: he.chao
 * @Date: 2022-10-19 15:07:55
 * @LastEditors: liu.yongli
 * @LastEditTime: 2023-12-13 14:36:14
-->
<template>
  <div class="tool-box-wrap">
    <div :class="`tool-box-icon ${selectedCtl ? 'selected': ''}`" @click.stop="itemClick">
      <span class="item-icon">
        <i :class="`iconfont icon-tool`" />
      </span>
    </div>
    <div :class="`tool-box-container ${selectedCtl ? 'selected':''}`">
      <el-tooltip popper-class="toolbox-toolbox" placement="left">
        <div slot="content" class="tooltip-content">地图控制</div>
        <span class="tool-btn btn-map" data-mode="map" @click="btnClick">
          <icon-map-tool :class="`${clickBtn['map']?'active':''}`" />
        </span>
      </el-tooltip>

      <!-- 取消过车开关与小工具联动 v-if="!$store.state.map.passRouteSwitch" -->
      <span :class="`tool-btn btn-ruler `" data-mode="ruler" @click="btnClick">
        <el-tooltip popper-class="toolbox-toolbox" placement="left">
          <div slot="content" class="tooltip-content">小工具</div>
          <icon-ruler-tool :class="`${clickBtn['ruler']?'active':''}`" />
        </el-tooltip>
      </span>

      <div
        :class="`tool-btn btn-cfg`"
        style="position: relative;"
        data-mode="cfg"
        @click="btnClick"
      >
        <el-tooltip popper-class="toolbox-toolbox" placement="left">
          <div slot="content" class="tooltip-content">图层工具箱</div>
          <!-- <icon-check-tool :class="`${clickBtn['cfg']?'active':''}`" /> -->
          <img
            :style="{width:pxToRem(18),height:pxToRem(18)}"
            :src="`${require(clickBtn['cfg'] ? '@/assets/images/alarmEvent/alarm/icon_check_s.svg' : '@/assets/images//alarmEvent/alarm/icon_check_n.svg')}`"
            alt="图片加载出错"
          />
        </el-tooltip>
        <div v-if="clickBtn['cfg']" @click.stop class="switch-wrap">
          <div>
            <el-switch
              v-model="$store.state.map.passRouteSwitch"
              active-color="#3A77E5"
              inactive-color="#a2a3a3"
              :active-value="true"
              :inactive-value="false"
              @change="passRouteSwitchChange"
            />实时过车
          </div>
          <div>
            <el-switch
              v-model="checkMode"
              active-color="#3A77E5"
              inactive-color="#a2a3a3"
              :active-value="true"
              :inactive-value="false"
              @change="checkModeChange"
            />检查模式
          </div>
          <!-- -->
          <div>
            <el-switch
              v-model="checkModForEvt"
              active-color="#3A77E5"
              inactive-color="#a2a3a3"
              :active-value="true"
              :inactive-value="false"
              @change="checkModeChangeForEvt"
            />实时事件
          </div>
        </div>
      </div>

      <el-tooltip popper-class="toolbox-toolbox" placement="left">
        <div slot="content" class="tooltip-content">事件筛选</div>
        <span class="tool-btn btn-filter" data-mode="filter" @click="btnClick">
          <!-- <icon-map-tool :class="`${clickBtn['filter']?'active':''}`" /> -->
          <img
            :style="{width:pxToRem(18),height:pxToRem(18)}"
            :src="`${require(clickBtn['filter'] ? '@/assets/images/alarmEvent/alarm/gaojing_selected.svg' : '@/assets/images//alarmEvent/alarm/gaojing_normal.svg')}`"
            alt="图片加载出错"
          />
        </span>
      </el-tooltip>
    </div>
    <Tools :mapId="mapId" v-if="clickBtn['ruler']" />
    <EventFilterDialog v-if="clickBtn['filter']" />
  </div>
</template>

<script>
import { Events } from '@/components/page/ScreenPages/IntegratedMonitor/enum/EventsEnum'
import IconCheckTool from '@/components/page/ScreenPages/IntegratedMonitor/Icons/icon-check-tool.vue'
import IconMapTool from '@/components/page/ScreenPages/IntegratedMonitor/Icons/icon-map-tool.vue'
import IconRulerTool from '@/components/page/ScreenPages/IntegratedMonitor/Icons/icon-ruler-tool.vue'
import MapTool from '@/components/page/ScreenPages/IntegratedMonitor/Map/MapTool'
import { mapMutations } from 'vuex'
import Tools from './Tools'
import EventFilterDialog from '../EventFilter/EventFilterDialog.vue'

/**
 * MapToolConst 是一个对象，用于定义地图工具相关的常量。
 * 它包含三个属性：
 * - MAP: 表示地图工具的常量。
 * - RULER: 表示尺子工具的常量。
 * - CFG: 表示配置工具的常量。
 */
const MapToolConst = {
  MAP: 'map',
  RULER: 'ruler',
  CFG: 'cfg',
}
const { MapEvent } = Events
export default {
  props: {
    mapId: {
      type: String,
    },
  },
  components: {
    IconCheckTool,
    IconMapTool,
    IconRulerTool,
    MapTool,
    Tools,
    EventFilterDialog,
  },
  data: function () {
    return {
      selectedCtl: false,
      clickBtn: { map: true },
      checkMode: false,
      passRouteModel: false,
      checkModForEvt: false, // 实时事件开关
    }
  },
  mounted() {
    this.setMapToolSwitch(this.clickBtn['map'])
    this.$EventBus.$on(Events.PassRouteEvt.OPEN, this.onPassRouteOpen)
  },
  beforeDestroy() {
    this.$EventBus.$off(Events.PassRouteEvt.OPEN)
  },
  methods: {
    ...mapMutations('map', [
      'setCheckMode', // 设置检查模式
      'setPassRouteSwitch', // 设置通过路线切换
      'setMapToolSwitch', // 设置地图工具切换
      'setEventFilterParams', // 设置事件筛选参数
    ]),
    ...mapMutations('event', [
      'setEvtListOpen', // 设置事件列表打开状态
    ]),
    /**
     * 点击项目时触发的方法，用于切换当前项的选中状态。
     */
    /**
     * itemClick 方法用于处理点击事件。
     * 当点击时，切换 selectedCtl 的值。
     */
    itemClick() {
      this.selectedCtl = !this.selectedCtl
    },
    /**
     * 当按钮被点击时触发的方法，用于处理不同模式下的按钮点击事件。
     * @param {Event} e - 事件对象，包含当前点击按钮的数据信息。
     */
    btnClick(e) {
      const mode = e.currentTarget.dataset.mode
      this.clickBtn[mode] = !this.clickBtn[mode]
      this.onClick(mode)
    },
    /**
     * 处理按钮点击后的逻辑，根据模式更新地图工具开关状态。
     * @param {string} mode - 当前点击的按钮模式。
     */
    onClick(mode) {
      if (mode === 'map') {
        this.setMapToolSwitch(this.clickBtn[mode])
      }
      this.clickBtn = {
        ...this.clickBtn,
      }
      this.clearEvtParams(mode)
    },
    /**
     * 清除事件筛选的参数
     * 关闭了事件筛选时按钮
     */
    clearEvtParams(mode) {
      if (mode === 'filter' && !this.clickBtn?.['filter']) {
        this.setEventFilterParams({})
        // 暴露事件筛选数据
        this.$EventBus.$emit('onEventFilterChange', {
          filterState: 'close',
        })
      }
    },
    /**
     * 检查模式改变时触发的方法，用于更新检查模式状态并通知相关事件。
     * @param {boolean} val - 新的检查模式状态。
     */
    checkModeChange(val) {
      this.setCheckMode(val)
      this.checkMode = val
      this.$EventBus.$emit(MapEvent.DETECT_FAILURE, {
        show: val,
      })
    },
    /**
     * 实时事件开关改变时触发的方法，用于更新实时事件开关状态并执行相关操作。
     */
    checkModeChangeForEvt(val) {
      this.checkModForEvt = val
    },
    /**
     * 通过路线切换开关改变时触发的方法，用于更新开关状态并执行相关操作。
     * @param {boolean} val - 新的通过路线切换开关状态。
     */
    passRouteSwitchChange(val) {
      this.setPassRouteSwitch(val)
      // // 过车信息切换的开关不要关联了右侧展示
      // if (val) {
      //   this.setEvtListOpen(false)
      // }
      this.passRouteModel = val
      this.$EventBus.$emit(Events.PassRouteEvt.OPEN, val)
    },

    /**
     * 处理通过的路由打开逻辑
     * @param {boolean} isOpen - 表示路由是否打开
     * 当 isOpen 为 true 时，关闭地图和标尺工具的按钮状态，并触发相应的点击事件
     */
    onPassRouteOpen(isOpen) {
      if (isOpen) {
        // 当路由打开时，设置地图和标尺工具的按钮为关闭状态，并触发点击事件
        this.clickBtn[MapToolConst.MAP] = false
        this.onClick(MapToolConst.MAP)
        this.clickBtn[MapToolConst.RULER] = false
        this.onClick(MapToolConst.RULER)
      }
    },
  },
}
</script>

<style scoped lang='scss' src='./index.scss' />
