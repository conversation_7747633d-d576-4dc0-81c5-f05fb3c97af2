<template>
  <div class="ZBDW-info-container">
    <div class="info-title">
      坐标定位
      <i class="el-icon-close info-container-close" @click="close"></i>
    </div>
    <div class="info-content">
      <div class="info-item">
        <el-input v-model="lng" clearable placeholder="请输入经度"></el-input>
        <el-input v-model="lat" clearable placeholder="请输入纬度"></el-input>
      </div>
      <div class="info-btn">
        <el-button @click="goPoint" plain>定位到</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import fullScreen from '@/utils/fullScreen'
import { imageMarker, setZoomAndCenter } from '@/utils/map3.0'
import CTMapOl from '@ct/ct_map_ol'
import { mapMutations } from 'vuex'
import { transformPointToMercator } from '@/utils'

let _map = null
let markerLayer = null

export default {
  mixins: [fullScreen],
  inject: ['mapRef'],
  props: {
    mapId: {
      type: String,
    },
  },
  data: function () {
    return {
      lng: '',
      lat: '',
    }
  },
  mounted() {
    if (!markerLayer) {
      markerLayer = new CTMapOl.layer.Vector({
        source: new CTMapOl.source.Vector(),
      })
      _map = this.mapRef.getMapRef(this.mapId).mapInstance
      _map.addLayer(markerLayer)
    }
  },
  methods: {
    ...mapMutations('map', ['setLocationLnglat']),
    /**
     * 导航到指定点位
     * 根据输入的经纬度验证其有效性，并在地图上设置新的位置
     */
    goPoint() {
      // 验证经度的有效性
      const blLng = this.validatorLongitude(this.lng)
      // 验证纬度的有效性
      const blLat = this.validatorLatitude(this.lat)
      // 如果经度或纬度无效，则显示警告消息并返回
      if (!blLng || !blLat) {
        this.$message({
          type: 'warning',
          message: `请输入正确的${!blLng ? '经' : '纬'}度`,
        })
        return
      }
      // 设置地图的中心位置为验证有效的经纬度
      this.setLocationLnglat(this.lng + ',' + this.lat)
      // 添加标记到地图上
      this.addMarker()
    },
    /**
     * 在地图上添加标记
     * 清除现有的标记后，根据当前的经纬度添加新的标记
     */
    addMarker() {
      // 清除地图上的所有标记
      markerLayer.getSource().clear(true)
      // 准备新的标记数组
      let markerArr = []
      const center = [this.lng * 1, this.lat * 1]
      // 创建并添加新的标记
      markerArr.push(
        imageMarker(
          transformPointToMercator(center),
          {
            icon: require('@/assets/images/home/<USER>/wendushiduji_dingwei_svg.svg'),
            anchor: [15, 42],
          },
          { id: `wendushiduji_dingwei_svg-${new Date().getTime()}` }
        )
      )
      // 将新的标记添加到地图上
      markerLayer.getSource().addFeatures(markerArr)
      // 转到点的位置
      setZoomAndCenter(this.mapRef.getMapRef(this.mapId), { point: center })
    },
    /**
     * 关闭当前视图或操作
     * 调用父组件的方法，传递空字符串以关闭工具包面板
     */
    close() {
      this.$parent.toolPackClick('')
    },
    /**
     * 验证经度的有效性
     * @param {number|string} value - 待验证的经度值
     * @returns {boolean} 经度是否有效的布尔值
     */
    //经度
    validatorLongitude(value) {
      let bl
      // 经度的正则表达式，验证范围为-180到180，包括小数点后最多15位
      const reg =
        /^(\-|\+)?(((\d|[1-9]\d|1[0-7]\d|0{1,3})\.\d{0,15})|(\d|[1-9]\d|1[0-7]\d|0{1,3})|180\.0{0,15}|180)$/
      // 如果值为空，则无效
      if (value === '') {
        bl = false
      } else if (!reg.test(value)) {
        // 如果值不符合正则表达式，则无效
        // '经度范围：-180~180（保留小数点后十五位）'
        bl = false
      } else {
        // 否则，值有效
        bl = true
      }
      return bl
    },
    /**
     * 验证纬度的有效性
     * @param {number|string} value - 待验证的纬度值
     * @returns {boolean} 纬度是否有效的布尔值
     */
    //纬度
    validatorLatitude(value) {
      let bl
      // 纬度的正则表达式，验证范围为-90到90，包括小数点后最多15位
      const reg = /^(\-|\+)?([0-8]?\d{1}\.\d{0,15}|90\.0{0,15}|[0-8]?\d{1}|90)$/
      // 如果值为空，则无效
      if (value === '') {
        bl = false
      } else if (!reg.test(value)) {
        // 如果值不符合正则表达式，则无效
        // "纬度范围：-90~90（保留小数点后十五位）"
        bl = false
      } else {
        // 否则，值有效
        bl = true
      }
      return bl
    },
  },
  beforeDestroy() {
    if (markerLayer) {
      markerLayer?.getSource?.()?.clear(true)
      _map?.removeLayer(markerLayer)
    }
  },
}
</script>

<style lang='scss'>
@import '~@/assets/styles/px-to-rem';

.ZBDW-info-container {
  width: px-to-rem(440);
  height: px-to-rem(260);
  background: url('~@/assets/images/home/<USER>') no-repeat;
  background-size: 100% 100%;
  color: #ffffff;
  padding: px-to-rem(20);

  .info-title {
    height: px-to-rem(40);
    position: relative;
    font-size: px-to-rem(18);
    font-weight: bold;
    margin-top: px-to-rem(5);
    display: flex;
    align-items: flex-start;
    justify-content: space-between;

    &::after {
      content: '';
      position: absolute;
      width: 100%;
      height: px-to-rem(4);
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      background: url('~@/assets/images/home/<USER>') no-repeat;
      background-size: 100% 100%;
    }

    .info-container-close {
      cursor: pointer;
      line-height: normal;
    }
  }

  .info-content {
    margin-top: px-to-rem(15);
    font-size: px-to-rem(14);
    .info-item {
      display: flex;
      flex-direction: column;
    }

    .el-input {
      background: #173e5e;

      &:nth-child(2) {
        margin-top: px-to-rem(15);
      }

      .el-input__inner {
        background: #173e5e;
        border: 0;
        color: #ffffff;
        font-size: px-to-rem(14);
      }
    }

    .info-btn {
      width: 100%;
      text-align: center;
      margin-top: px-to-rem(15);

      .el-button {
        background: url('~@/assets/images/home/<USER>') no-repeat !important;
        background-size: 100% 100% !important;
        font-family: PingFangSC-Medium, sans-serif;
        font-size: px-to-rem(14);
        color: #00d7ff !important;
        text-align: right;
        font-weight: 500;
        border: 0 !important;
        padding: px-to-rem(10) px-to-rem(15) !important;
      }
    }
  }
}
</style>