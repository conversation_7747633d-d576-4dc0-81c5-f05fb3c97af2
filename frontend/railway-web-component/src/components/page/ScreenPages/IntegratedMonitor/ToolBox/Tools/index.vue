<!--
 * @Description:
 * @Autor: he.chao
 * @Date: 2022-10-19 15:07:55
 * @LastEditors: liu.yongli
 * @LastEditTime: 2023-12-13 14:36:14
-->
<template>
  <div>
    <div class="tool-bar-wrap tool-pack-panel">
      <p
        v-for="(item, i) in toolPackList"
        :key="i"
        @click="toolPackClick(item)"
        :style="{ color: toolPackKey === item ? '#1fddff' : '#ffffff' }"
      >{{ item }}</p>
    </div>
    <div class="tool-pack-info">
      <ZBSQ-Info :mapId="mapId" v-if="toolPackKey.includes(toolPackList[0])" />
      <ZBDW-Info :mapId="mapId" v-if="toolPackKey.includes(toolPackList[1])" />
    </div>
  </div>
</template>

<script>
import ZBDWInfo from '@/components/page/ScreenPages/IntegratedMonitor/ToolBox/Tools/ZBDW.vue'
import ZBSQInfo from '@/components/page/ScreenPages/IntegratedMonitor/ToolBox/Tools/ZBSQ.vue'
import { mapGetters } from 'vuex'
import { mouseAreaTool, mouseLineTool } from '@/utils/map3.0' // 导入地图工具函数

let _mouseLineTool = null // 测距功能
let _mouseTool = null // 测距功能

export default {
  inject: ['mapRef'],
  props: {
    mapId: {
      type: String,
    },
  },
  components: {
    ZBDWInfo,
    ZBSQInfo,
  },
  data: function () {
    return {
      toolPackKey: '',
      toolPackList: [
        '坐标拾取',
        '坐标定位',
        '距离测量',
        '面积测量',
        '清除地图',
      ],
    }
  },
  computed: {
    ...mapGetters('map', ['getLocationLnglat']),
  },
  beforeDestroy() {
    this.clearDraw()
  },
  mounted() {
    // 创建
    const _mapRef = this.mapRef.getMapRef(this.mapId)
    // 测距功能
    if (!_mouseLineTool) {
      _mouseLineTool = mouseLineTool(_mapRef)
      _mouseLineTool.stop()
    }
    // 绘图
    if (!_mouseTool) {
      const _map = _mapRef.mapInstance
      _mouseTool = mouseAreaTool(_map)
      _mouseTool.stop()
    }
  },
  methods: {
    /**
     * 设置选中的经纬度坐标
     * @param {Object} state - Vuex的状态对象
     * @param {Object} item - 经纬度坐标对象
     */
    setPickLnglat(state, item) {
      state.pickLnglat = item || []
    },
    /**
     * 处理工具包点击事件
     * @param {String} key - 被点击的工具键值
     */
    toolPackClick(key) {
      this.selectedToolClick('工具', key)
      this.draw(key)
    },
    /**
     * 处理工具点击事件
     * @param {String} key - 被点击的工具键值
     * @param {String} packKey - 工具包的键值
     */
    selectedToolClick(key = '', packKey = '') {
      this.toolPackKey = packKey
    },
    /**
     * 根据不同的工具键值执行相应的绘图操作
     * @param {String} key - 工具键值
     */
    draw(key) {
      if (this.toolPackList[2] === key) {
        this.draw2('rule')
      } else if (this.toolPackList[3] === key) {
        this.draw2('measureArea')
      } else if (this.toolPackList[4] === key) {
        this.clearDraw()
      } else {
        // todo
        // this.setPickLnglat('')
      }
    },
    /**
     * 清除绘图
     */
    clearDraw() {
      _mouseTool?.clear()
      _mouseTool?.stop()
      _mouseLineTool?.clearUp()
      _mouseLineTool?.stop()
    },
    /**
     * 根据类型启动相应的绘图工具
     * @param {String} type - 绘图类型
     */
    draw2(type) {
      switch (type) {
        case 'rule': {
          _mouseLineTool?.start()
          _mouseTool?.stop()
          break
        }
        case 'measureArea': {
          _mouseTool?.start()
          _mouseLineTool?.stop()
          break
        }
      }
    },
  },
}
</script>

<style lang='less' src='./index.less' />
