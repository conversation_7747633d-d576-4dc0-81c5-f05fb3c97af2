<template>
  <div class="ZBSQ-info-container">
    <!-- 坐标拾取模块的标题，包含关闭按钮 -->
    <div class="info-title">
      坐标拾取
      <i class="el-icon-close info-container-close" @click="close"></i>
    </div>
    <!-- 坐标显示区域，包含坐标值和复制按钮 -->
    <div class="info-content">
      <div class="info-item">
        <span>坐标：</span>
        <span id="lnglat">{{ getPickLnglat }}</span>
        <span @click="copy">复制</span>
      </div>
      <!-- 输入框用于编辑和复制坐标值 -->
      <el-input class="copy-ipt" v-model="getPickLnglat" placeholder></el-input>
    </div>
  </div>
</template>

<script>
import fullScreen from '@/utils/fullScreen'
import { mapGetters } from 'vuex'

export default {
  mixins: [fullScreen],
  props: {},
  data() {
    return {
      info: {},
    }
  },
  computed: {
    // 使用vuex映射getter来获取坐标值
    ...mapGetters('map', ['getPickLnglat']),
  },
  methods: {
    // 关闭坐标拾取模块
    close() {
      this.$parent.toolPackClick('')
    },
    // 复制坐标值到剪贴板
    copy() {
      // 根据坐标值是否存在来设置提示信息的类型
      this.$message({
        type: this.getPickLnglat ? 'success' : 'warn',
        message: this.getPickLnglat ? '复制成功' : '请拾取座标',
      })
      // 选中输入框中的文本以准备复制
      document.querySelector('.copy-ipt .el-input__inner').select()
      // 执行复制命令
      document.execCommand('Copy')
    },
  },
}
</script>

<style lang='scss'>
@import '~@/assets/styles/px-to-rem';

.ZBSQ-info-container {
  width: px-to-rem(440);
  height: px-to-rem(160);
  background: url('~@/assets/images/home/<USER>') no-repeat;
  background-size: 100% 100%;
  color: #ffffff;
  padding: px-to-rem(20);

  .info-title {
    height: px-to-rem(40);
    position: relative;
    font-size: px-to-rem(18);
    font-weight: bold;
    margin-top: px-to-rem(12);
    display: flex;
    align-items: flex-start;
    justify-content: space-between;

    &::after {
      content: '';
      position: absolute;
      width: 100%;
      height: px-to-rem(4);
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      background: url('~@/assets/images/home/<USER>') no-repeat;
      background-size: 100% 100%;
    }

    .info-container-close {
      cursor: pointer;
      line-height: normal;
    }
  }

  .info-content {
    margin-top: px-to-rem(15);
    font-size: px-to-rem(14);
    position: relative;

    .info-item {
      cursor: pointer;
      display: flex;
      flex-direction: row;
      align-items: center;
      height: px-to-rem(44);
      background: #173e5e;
      border: 1px dashed rgba(5, 147, 202, 1);
      padding: 0 px-to-rem(10);
      position: absolute;
      top: 0;
      z-index: 1;
      left: 0;
      right: 0;

      // span {
      //   width: px-to-rem(300);
      // }

      span:nth-child(2) {
        flex: 1;
      }

      // span:nth-child(3) {
      //   width: px-to-rem(200);
      // }
    }
  }
}
</style>
