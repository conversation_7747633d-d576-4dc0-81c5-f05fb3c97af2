.tool-bar-wrap {
  cursor: pointer;
  padding: 10px 0;
  background: #173e5e;
  border-radius: 8px;
  text-align: center;
  position: relative;

  .el-divider--horizontal {
    height: 0.5px;
    background: rgba(216, 216, 216, 0.3);
    width: 40%;
    margin: 4px auto;
  }

  .gongju-left {
    position: absolute;
    color: #7c7c7c;
    left: 5px;
    font-size: 14px;
    top: calc(50% - 7px);
  }
}
.tool-pack-panel {
  z-index: 2;
  font-size: 16px;
  position: absolute;
  right: 51px;
  top: 46px;
  background: rgba(22, 57, 107, 0.5);
  border: 1px solid rgba(0, 145, 255, 0.58);
  border-radius: 8px;
  // backdrop-filter: blur(3px);

  p {
    width: 107px;
    padding: 10px 20px;
    text-align: left;
    margin: 0 !important;

    &:hover {
      color: #1fddff !important;
    }
  }
}
.tool-pack-info {
  position: absolute;
  right: 170px;
  top: 42px;
}

#mainMap .ol-overlay-container.ol-selectable {
  .ctmap-ol-measure-tooltip {
    pointer-events: auto !important;
  }
}
