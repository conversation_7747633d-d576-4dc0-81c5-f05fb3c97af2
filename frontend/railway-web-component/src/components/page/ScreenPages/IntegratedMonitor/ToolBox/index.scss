@import "~@/assets/styles/px-to-rem";

.tool-box-wrap {
  position: absolute;
  top: px-to-rem(40);
  right: px-to-rem(390);
  display: flex;
  align-items: center;
  flex-direction: column;
  pointer-events: auto;
  z-index: 20;

  .tool-box-icon {
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: space-around;
    cursor: pointer;

    &:not(:first-child) {
      margin-top: px-to-rem(40);
    }

    .item-icon {
      width: px-to-rem(40);
      height: px-to-rem(40);
      background: url("../../../../../assets/images/comm/ctl_icon_unselected.svg")
        100% no-repeat;
      background-size: 100% 100%;
      display: flex;
      align-items: center;
      justify-content: center;

      .iconfont {
        width: px-to-rem(24);
        height: px-to-rem(24);

        &.icon-tool {
          background: url(./img/icon_tool_box.svg) 100% no-repeat;
          background-size: 100% 100%;
        }
      }
    }

    .item-text {
      font-family: PingFangSC-Regular;
      font-size: px-to-rem(14);
      color: rgba(255, 255, 255, 0.65);
      letter-spacing: 0;
      font-weight: 400;
      margin-top: px-to-rem(9);
    }

    &.selected {
      .item-icon {
        background: url("../../../../../assets/images/comm/ctl_icon_selected.svg")
          100% no-repeat;
        background-size: 100% 100%;
      }
    }
  }

  .tool-box-container {
    width:  px-to-rem(40);
    display: none;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    background: #172537;
    border-radius: px-to-rem(4);

    &.selected {
      display: flex;
    }

    .tool-btn {
      // width: px-to-rem(18);
      // height: px-to-rem(18);
      margin:px-to-rem(10);
      cursor: pointer;
      display: flex;

      svg {
        fill: #fefefe;
      }

      .switch-wrap {
        display: flex;
        align-items: center;
        position: absolute;
        right: 41px;
        top: -12px;
        border-radius: 5px;
        width: 160px;
        padding: px-to-rem(10);
        background: #172537;
        font-size: px-to-rem(14);
        color: #e8f3ff;
        letter-spacing: 0;
        font-weight: 400;
        flex-direction: column;
        > div {
          &:not(:first-child) {
            margin-top: 12px;
          }
        }

        .el-switch {
          margin-right: px-to-rem(10);
        }
      }

      &.active {
        svg {
          fill: #3a77e5;
        }
      }
    }
  }
}
