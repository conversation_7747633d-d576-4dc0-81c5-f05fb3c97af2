<template>
  <div id="left-side-window-ins" class="left-side-window">
    <!-- 当有标签页时，显示TabWinPanel组件 -->
    <TabWinPanel
      v-if="tabs.length > 0"
      :selectedTabValue="tabNameValue"
      :onRemoveTab="doTabClose"
      :tabs="tabs"
    />
  </div>
</template>

<script>
import { getDetailCfg } from '@/api/service/imScreenService'
import BizConstEnums from '@/components/common/enums/BizConstEnums'
import { mergeCfg } from '@/components/page/ScreenPages/IntegratedMonitor/DetailInfo/ContentConfig'
import DetailInIframe from '@/components/page/ScreenPages/IntegratedMonitor/DetailInfo/DetailInIframe/index.vue'
import DetailInfo from '@/components/page/ScreenPages/IntegratedMonitor/DetailInfo/index.vue'
import ExclusiveModeHandle from '@/components/page/ScreenPages/IntegratedMonitor/WindowWrapLeftSide/ModeHandle/ExclusiveModeHandle'
import PendingModeHandle from '@/components/page/ScreenPages/IntegratedMonitor/WindowWrapLeftSide/ModeHandle/PendingModeHandle'
import TabModeHandle from '@/components/page/ScreenPages/IntegratedMonitor/WindowWrapLeftSide/ModeHandle/TabModeHandle'
import TabWinPanel from '@/components/page/ScreenPages/IntegratedMonitor/WindowWrapLeftSide/TabWinPanel'
import {
  cameraThemeConfigDetailData,
  // tunnelDetailData,
  tunnelThemeConfigDetailData,
} from '@/components/page/ScreenPages/IntegratedMonitor/mockData'

// 枚举常量，用于确定技术类型
const { techType } = BizConstEnums
// 定义窗口模式对象，根据系统配置的详细模式确定使用的模式类
const winMod = {
  // 追加模式
  pending: PendingModeHandle,
  // 独占模式
  exclusive: ExclusiveModeHandle,
  // tab模式
  tab: TabModeHandle,
}

export default {
  components: {
    TabWinPanel,
    DetailInfo,
  },
  data() {
    return {
      // 内容窗口对象，用于存储各个内容窗口的信息
      contentWidows: {},
      // 当前窗口模式对象
      mode: ExclusiveModeHandle,
      // 当前选中的标签页名称
      tabNameValue: '',
      // 标签页列表
      tabs: [],
    }
  },
  inject: ['mapRef'],
  props: {
    mapId: {
      type: String,
    },
  },
  mounted() {
    // 根据系统配置初始化窗口模式，并设置状态
    this.mode = winMod?.[window.sysConfig?.detailMode]
    this.mode?.setState(this)
  },
  methods: {
    /**
     * 添加内容窗口
     * @param {Object} config 内容窗口的配置信息
     * @param {Component} config.component 窗口组件
     * @param {Object} config.props 组件的props
     * @param {string} config.key 窗口的唯一键
     * @param {string} [config.group=default] 窗口所属的组
     * @param {Function} [config.onclose] 关闭窗口的回调函数
     */
    async addContent({ component, props, key, group = 'default', onclose }) {
      // 获取详细配置信息
      const [, resData] = await getDetailCfg({
        layerCode: props.markerType, // 原接口参数
      })
      console.log('内容窗口--1--', resData)
      let data = resData
      // 测试数据
      if (!data?.length) {
        if (props.markerType === 'CAMERA') {
          data = cameraThemeConfigDetailData.data
        } else if (props.markerType === 'R:202406041411449843') {
          data = tunnelThemeConfigDetailData.data
        } else {
          // todo
        }
      }
      // 如果没有成功获取配置或没有数据，则返回
      if (!data || data.length === 0) {
        return
      }
      // 解析配置数据
      const {
        poiDetailJson: layoutJson,
        showTech,
        poiDetailUrl,
        poiDetailWidth,
        poiDetailHeight,
        layerName,
      } = data[0]
      // 根据是否显示技术类型选择组件
      let detailWrap = component
      if (!component) {
        detailWrap =
          showTech === techType.IFRAME.value ? DetailInIframe : DetailInfo
      }
      // 设置组件的props
      props.resUrl = poiDetailUrl
      props.width = poiDetailWidth
      props.height = poiDetailHeight
      props.layerName = layerName
      // 合并配置信息
      const contentCfg = mergeCfg(layoutJson)
      console.log(
        'this.contentCfg',
        JSON.stringify(contentCfg, function (cfgProps, val) {
          if (typeof val === 'function') {
            return val + ''
          }
          return val
        })
      )
      props.contentCfg = contentCfg
      // 通过窗口模式对象添加内容窗口
      this.mode.addContent({
        component: detailWrap,
        props,
        key,
        group,
        onclose,
      })
    },
    // 关闭指定键值的内容窗口
    closeContent(key, group = 'default') {
      this.mode.closeContent(key, group)
    },
    // 关闭指定组的所有内容窗口
    closeGroupContent(group) {
      this.mode.closeGroupContent(group)
    },
    // 关闭指定前缀的所有内容窗口
    closeByIdPrefix(prefix, group) {
      this.mode.closeByIdPrefix(prefix, group)
    },
    // 关闭所有内容窗口
    closeAllContent() {
      const _mode = this.mode
      if (window.sysConfig?.detailMode === 'tab') {
        _mode.closeAllContent(this.mapRef.getMapRef(this.mapId).mapInstance)
      } else {
        _mode.closeAllContent()
      }
    },
    /**
     * 关闭标签页
     * @param {string} params 要关闭的标签页的参数
     */
    doTabClose(params) {
      // 过滤掉要关闭的标签页，更新标签页列表
      this.tabs = this.tabs.filter(item => item.name !== params)
      // 更新当前选中的标签页名称为最后一个标签页的键
      this.tabNameValue = this.tabs[this.tabs.length - 1].key
    },
  },
}
</script>

<style lang="less" src="./index.less" />
