<!--
* @Description:
* @Autor: he.chao
* @Date: 2022-10-19 15:07:55
* @LastEditors: liu.yongli
* @LastEditTime: 2024-07-23 19:43:56
-->
<template>
  <!-- 包裹整个标签面板的div -->
  <div class='tab-win-panel-wrap'>
    <!-- 错误图标，点击时触发关闭所有标签的事件 -->
    <i class='el-icon-error icon-el' style='color:#fefefe' @click='closeAllTab' />
    <!-- el-tabs组件，用于显示标签页 -->
    <el-tabs v-model='tabNameValue' type='card' closable @tab-click='onTabClick' @tab-remove='removeTab'>
      <!-- 循环渲染每个标签页 -->
      <el-tab-pane
        v-for='item in tabs'
        :key='item.key'
        :label='item.title'
        :name='item.name'
      >
        <!-- 自定义标签页的标签内容 -->
        <span class='tab-label' slot='label'>
          <!-- 标签图标 -->
          <img alt="标签图标" class='svg-icon' :src='item.icon' />
          <!-- 标签标题 -->
          <span class='label-val'>{{ item.title }}</span>
        </span>
        <!-- 动态组件，根据item.content渲染不同的组件 -->
        <component :is=item.content :propData='item.propData' :close='item.close' :showMainTitle='false'></component>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import CtlPanel from '@/components/page/ScreenPages/IntegratedMonitor/CtlPanel/index.vue';
import { Events } from '@/components/page/ScreenPages/IntegratedMonitor/enum/EventsEnum';
export default {
  components: {
    CtlPanel
  },
  props: {
    // tabs属性，包含所有标签页的信息
    tabs: {
      type: Array,
      default: () => []
    },
    // 当前选中的标签页名称
    selectedTabValue: {
      type: String,
      default: ''
    },
    // 标签页移除时的回调函数
    onRemoveTab: {
      type: Function,
      default: () => {
        console.log('onRemoveTab');
      }
    }
  },
  data: function() {
    return {
      // 当前标签页名称
      tabNameValue: ''
    };
  },
  watch: {
    // 监听selectedTabValue属性的变化
    selectedTabValue: {
      handler(newVal) {
        // 更新当前标签页名称
        this.tabNameValue = newVal;
      },
      immediate: true
    }
  },
  methods: {
    // 移除标签页的方法
    removeTab(tabName) {
      // 找到被关闭的标签页
      const closedTab = this.tabs.find((item) => (item.name === tabName));
      // 如果找到了，调用标签页的关闭方法
      closedTab && closedTab.close({ destroy: false });
      // 初始化选中的标签页
      this.initSelectedTab();
    },
    // 关闭所有标签页的方法
    closeAllTab() {
      // 调用全局对象的方法关闭所有内容
      window.leftWrap.closeAllContent();
      // 通过事件总线发送关闭详情的事件
      this.$EventBus.$emit(Events.EVT_LIST.ON_DETAIL_CLOSE, { resetLayer: true });
    },
    // 初始化选中的标签页
    initSelectedTab() {
      // 如果有标签页，选中最后一个标签页
      if (this.tabs.length > 0) {
        this.tabNameValue = this.tabs[this.tabs.length - 1]?.name;
      }
    },
    // 标签页点击事件处理
    onTabClick() {
        // 打印点击事件
        console.log('onTabClick');
    }
  }
};
</script>
<style lang='less' src='./index.less' />
