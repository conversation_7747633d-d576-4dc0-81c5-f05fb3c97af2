import CommonMap from '@/components/common/Map/CommonMap';

const TabModeHandle = {
  /**
   * 设置当前实例的状态。
   * @param {Object} $this - 包含标签页信息的对象。
   */
  setState($this) {
    this.$this = $this;
    this.tabs = $this.tabs;
  },

  /**
   * 向标签页添加内容。
   * @param {Object} param0 - 添加内容的配置对象。
   * @param {Component} param0.component - 要添加的组件。
   * @param {Object} param0.props - 组件的属性。
   * @param {string} param0.key - 标签页的唯一键。
   * @param {string} param0.group - 标签页所属的组别，默认为 'default'。
   * @param {Function} param0.onclose - 关闭标签页时的回调函数。
   */
  addContent({
    component,
    props,
    key,
    group = 'default',
    onclose
  }) {
    const that = this;
    // 检查是否已存在相同组别和键值的标签页
    this.closeGroupContent(group);
    // 解构props中的相关内容
    const {
      markerType,
      layerName
    } = props;
    // 获取标签页的显示名称
    const tabName = layerName;
    const { poiIconCfg } = window.sysConfig;
    const layerCfg = poiIconCfg[markerType];
    // 设置当前选中的标签页名称
    this.$this.tabNameValue = layerName;
    // 添加新的标签页内容
    this.$this.tabs.push({
      name: layerName,
      key: key,
      icon: layerCfg.layerIconUrl,
      group,
      title: `${tabName}详情`,
      propData: props,
      content: component, // 关闭当前标签页的函数
      close(params) {
        that.closeContent(key, group);
        onclose && onclose(key, params);
      }
    });
  },

  /**
   * 关闭指定键值和组别的标签页。
   * @param {string} key - 标签页的唯一键。
   * @param {string} group - 标签页所属的组别，默认为 'default'。
   */
  closeContent(key, group = 'default') {
    // 筛选出不匹配指定组别和键值的标签页
    const newTabs = this.$this.tabs.filter(
      item => !(item.group === group && item.key === key));
    this.$this.tabs = [...newTabs];
    this.initSelectedTab(newTabs);
  },

  /**
   * 关闭指定组别的所有标签页。
   * @param {string} group - 标签页所属的组别。
   */
  closeGroupContent(group) {
    // 筛选出不匹配指定组别的标签页
    const newTabs = this.$this.tabs.filter(item => (item.group !== group));
    this.$this.tabs = [...newTabs];
    this.initSelectedTab(newTabs);
  },

  /**
   * 关闭键值以指定前缀开头的所有标签页（属于指定组）。
   * @param {string} prefix - 键值的前缀。
   * @param {string} group - 标签页所属的组别。
   */
  closeByIdPrefix(prefix, group) {
    // 筛选出键值不以指定前缀开头的标签页（属于指定组）
    const newTabs = this.$this.tabs.filter(
      item => !(item.group === group && item.key.indexOf(prefix) >= 0));
    this.$this.tabs = [...newTabs];
    this.initSelectedTab(newTabs);
  },

  /**
   * 关闭所有标签页内容。
   */
  closeAllContent(map) {
    this.$this.tabs = [];
    CommonMap.closeTip(map);
  },
  initSelectedTab(newTabs) {
    if (newTabs.length > 0) {
      this.$this.tabNameValue = this.$this.tabs[newTabs.length - 1]?.name;
    }
  }
};

export default TabModeHandle;
