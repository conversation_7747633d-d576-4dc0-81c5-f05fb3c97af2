import ComponentHandle from '@/components/common/ComponentHandle';

// 定义一个对象，用于处理挂起模式下的组件
const PendingModeHandle = {
  // 存储不同组的组件实例
  contentWidows: {},
  // 设置当前上下文的状态
  setState($this) {
    this.$this = $this;
  },
  // 异步方法，添加一个新的组件实例
  async addContent({
    component, // 组件类
    props,     // 组件属性
    key,       // 组件的唯一标识
    group = 'default', // 组件所属的组，默认为'default'
    onclose    // 关闭组件时的回调函数
  }) {
    // 创建组件实例
    const comp = ComponentHandle.createComponent({
      component,
      props,
      key,
      group,
      onclose: (params) => {
        // 关闭组件时调用
        this.closeContent(key, group);
        onclose && onclose(key, params);
      }
    });
    // 如果该组已经存在组件，则先关闭该组的所有组件
    if (this.contentWidows[group]) {
      this.closeGroupContent(group);
      this.contentWidows[group][key] = comp;
      return;
    }
    // 如果该组不存在，则初始化该组并添加组件
    this.contentWidows[group] = {};
    this.contentWidows[group][key] = comp;
  },
  // 关闭指定组中的指定组件
  closeContent(key, group = 'default') {
    const comp = this.contentWidows[group]?.[key];
    if (comp) {
      // 销毁组件实例
      comp.$destroy();
      const div = document.getElementById('left-side-window-ins');
      // 从DOM中移除组件的元素
      this.contentWidows[group][key] && div.hasChildNodes() &&
      div.removeChild(comp.$el);
      // 从存储中删除组件
      delete this.contentWidows[group][key];
    }
  },
  // 关闭指定组中的所有组件
  closeGroupContent(group) {
    if (this.contentWidows[group] &&
      Object.keys(this.contentWidows[group]).length > 0) {
      Object.keys(this.contentWidows[group]).forEach(key => {
        this.closeContent(key, group);
      });
    }
  },
  // 根据ID前缀关闭指定组中的组件
  closeByIdPrefix(prefix, group) {
    const groupWindow = this.contentWidows[group];
    groupWindow && Object.keys(groupWindow).map(key => {
      const comp = this.contentWidows[group][key];
      if (comp && key.indexOf(prefix) === 0) {
        // 销毁组件实例
        comp.$destroy();
        const div = document.getElementById('left-side-window-ins');
        // 从DOM中移除组件的元素
        this.contentWidows[group][key] && div.hasChildNodes() && div.removeChild(comp.$el);
        // 从存储中删除组件
        delete this.contentWidows[group][key];
      }
      return null;
    });
  },
  // 关闭所有组中的所有组件
  closeAllContent() {
    for (const key in this.contentWidows) {
      this.closeGroupContent(key);
    }
  }
};
export default PendingModeHandle;
