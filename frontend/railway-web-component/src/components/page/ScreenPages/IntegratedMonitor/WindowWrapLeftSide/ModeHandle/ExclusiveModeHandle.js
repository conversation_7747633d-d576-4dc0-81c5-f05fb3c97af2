import ComponentHandle from '@/components/common/ComponentHandle';
import LayerConst from '@/components/page/ScreenPages/IntegratedMonitor/Map/LayerConst';
import { maplayer } from '@/components/page/ScreenPages/IntegratedMonitor/Map/maplayer';

/**
 * 左侧详情面板独占模式
 */
const ExclusiveModeHandle = {
  // 存储内容窗口的对象
  contentWidows: {},
  // 存储事件内容的组件
  evtContent: null,
  // 设置当前上下文的状态
  setState($this) {
    this.$this = $this;
  },
  // 添加内容窗口
  addContent({ component, props, key, group = 'default', onclose }) {
    // 创建组件
    const comp = ComponentHandle.createComponent({
      component,
      props,
      key,
      group,
      onclose: (params) => {
        // 如果是警报事件类型，关闭事件窗口，否则关闭内容窗口
        if (props.markerType === LayerConst.ALARM_EVENT) {
          this.closeEvt();
        } else {
          this.closeContent(key, group);
        }
        // 执行传入的关闭回调
        onclose && onclose(key, params);
      }
    });
    // 关闭所有内容窗口
    this.closeAllContentWindows();
    // 遍历所有图层键
    maplayer.getKeys().map(markerType => {
      // 如果不是当前的标记类型且不是警报事件，则取消选择
      if (markerType !== props.markerType && markerType !== LayerConst.ALARM_EVENT) {
        maplayer.getLayer(markerType).cancelSelected(markerType);
      }
    });
    // 如果是警报事件类型，设置事件内容并返回
    if (props.markerType === LayerConst.ALARM_EVENT) {
      this.closeEvt();
      this.evtContent = comp;
      return;
    }
    // 初始化内容窗口组
    this.contentWidows[group] = {};
    this.contentWidows[group][key] = comp;
    // 如果事件内容存在，则隐藏其元素
    if (this.evtContent) {
      this.evtContent.$el.style.display = 'none';
    }
  },
  // 关闭指定的内容窗口
  closeContent(key, group = 'default') {
    const comp = this.contentWidows[group]?.[key];
    if (comp) {
      // 销毁组件
      comp.$destroy();
      const div = document.getElementById('left-side-window-ins');
      // 如果组件存在且父节点有子节点，则移除组件的元素
      this.contentWidows[group][key] && div.hasChildNodes() && div.removeChild(comp.$el);
      // 删除该内容窗口
      delete this.contentWidows[group][key];
    }
    // 显示事件窗口
    this.showEvt();
  },
  // 关闭指定组的所有内容窗口
  closeGroupContent(group) {
    if (this.contentWidows[group] && Object.keys(this.contentWidows[group]).length > 0) {
      Object.keys(this.contentWidows[group]).forEach(key => {
        this.closeContent(key, group);
      });
    }
  },
  // 关闭指定前缀的内容窗口
  closeByIdPrefix(prefix, group) {
    const groupWindow = this.contentWidows[group];
    groupWindow && Object.keys(groupWindow).map(key => {
      const comp = this.contentWidows[group][key];
      if (comp && key.indexOf(prefix) === 0) {
        // 销毁组件
        comp.$destroy();
        const div = document.getElementById('left-side-window-ins');
        // 如果组件存在且父节点有子节点，则移除组件的元素
        this.contentWidows[group][key] && div.hasChildNodes() && div.removeChild(comp.$el);
        // 删除该内容窗口
        delete this.contentWidows[group][key];
      }
      return null;
    });
    // 显示事件窗口
    this.showEvt();
  },
  // 显示事件窗口
  showEvt() {
    const keys = Object.keys(this.contentWidows);
    // 查找是否有其他窗口存在
    const otherWin = keys.find(key => (this.contentWidows[key] && Object.keys(this.contentWidows[key]).length > 0));
    // 如果没有其他窗口且事件内容存在，则显示事件内容
    if (!otherWin && this.evtContent) {
      this.evtContent.$el.style.display = 'flex';
    }
  },
  // 关闭所有容器窗口
  closeAllContent() {
    for (const key in this.contentWidows) {
      this.closeGroupContent(key);
    }
    // 关闭事件窗口
    this.closeEvt();
  },
  // 关闭事件窗口
  closeEvt() {
    const comp = this.evtContent;
    if (comp) {
      // 销毁组件
      comp.$destroy();
      delete this.evtContent;
      const div = document.getElementById('left-side-window-ins');
      // 如果父节点有子节点，则移除组件的元素
      div.hasChildNodes() && div.removeChild(comp.$el);
    }
  },
  // 关闭除了事件以外的窗口
  closeAllContentWindows() {
    for (const key in this.contentWidows) {
      this.closeGroupContent(key);
    }
  }
};
export default ExclusiveModeHandle;
