@import "../../common";

.tab-win-panel-wrap {
  position: relative;
  background: #172537;
  padding-top: 20px;
  border-radius: 8px;
  min-width: 360px;
  //max-width: 500px;
  box-sizing: border-box;

  .icon-el {
    position: absolute;
    top: 6px;
    right: 6px;
    cursor: pointer;
  }
  .el-tabs {
    width: 100%;

    &.el-tabs--card > .el-tabs__header {
      background: url("./img/bg_main_title_bg.svg") 0 100% no-repeat;
      border: none;
      margin: 0;

      .el-tabs__nav {
        border: none;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        padding-right: 100px;

        &:first-child {
          margin-left: 12px;
        }

        .el-tabs__item {
          margin: 12px 12px 8px 0px;
          width: auto;
          border: none;
          padding: 0;
          height: auto;
          .tab-label {
            font-family: PingFangSC-Medium;
            font-size: 14px;
            color: #E8F3FF;
            letter-spacing: 0;
            font-weight: 500;
            display: flex;
            align-items: center;
            height: 24px;
            background-image: linear-gradient(180deg, #3A77E5 1%, #4F9FFF 97%);
            justify-content: flex-start;
            position: relative;
            border: 2px solid;
            border-image: linear-gradient(to top, #549bf6 50%, white 100%) 1 stretch;
            clip-path: inset(0 round 4px);

            > img {
              margin: 0 3px;
              width: 20px;
              height: 20px;
            }

            .label-val {
              width: 0;
              text-align: center;
              overflow: hidden;
              transition: width 1s;
            }
          }

          .el-icon-close {
            display: none;
            align-items: center;
            justify-content: center;
          }

          &:hover, &.is-active {
            .tab-label {
              .label-val {
                width: 70px;
                text-align: center;
                margin-right: 10px;
                overflow: hidden;
                white-space: nowrap; //强制文本在一行内输出
                text-overflow: ellipsis; //对溢出部分加上...
              }
            }
          }

          &:hover {
            .el-icon-close {
              display: flex;
              position: absolute;
              top: -8px;
              right: -8px;
              background: #fefefe;
              color: @theme-color;
            }
          }
        }
      }
    }
  }
}
