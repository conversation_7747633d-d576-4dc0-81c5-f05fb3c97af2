<!--
 * @Description:
 * @Autor: he.chao
 * @Date: 2022-10-19 15:07:55
 * @LastEditors: liu.yongli
 * @LastEditTime: 2023-12-13 14:36:14
-->
<template>
  <i class='icon' title='播放视频'>
    <svg width='18px' height='17px' viewBox='0 0 18 17' fill='#Fefefe' version='1.1' xmlns='http://www.w3.org/2000/svg'>
      <g id='综合监测-大屏' stroke='none' stroke-width='1' fill-rule='evenodd'>
        <g id='1-5综合监测-实时喊话' transform='translate(-24.000000, -395.000000)' fill-rule='nonzero'>
          <g id='告警详情' transform='translate(12.000000, 92.000000)'>
            <g id='icon_播放视频_18_s' transform='translate(12.000000, 303.000000)'>
              <path
                d='M16.7211412,0 C17.4274302,0 18,0.636163464 18,1.40921125 L18,12.7571627 C18,13.5355842 17.4214286,14.166374 16.7202874,14.166374 L10.2856892,14.166374 L10.2856892,15.5836326 L12.2134319,15.5836326 C12.5682589,15.5836326 12.8562891,15.9003644 12.8562891,16.2913838 C12.8562891,16.682377 12.5682589,17 12.2134319,17 L5.78571429,17 C5.43083705,17 5.14285714,16.682377 5.14285714,16.2913838 C5.14285714,15.9003644 5.43083705,15.5836326 5.78571429,15.5836326 L7.71428571,15.5836326 L7.71428571,14.166374 L1.2796875,14.166374 C0.572544643,14.166374 0,13.5311017 0,12.7571627 L0,1.40921125 C0,0.63168104 0.578571429,0 1.2796875,0 L16.7211412,0 Z M16.0714286,1.4163674 L1.92857143,1.4163674 C1.5728404,1.4163674 1.28571429,1.78140974 1.28571429,2.23056961 L1.28571429,11.9358306 C1.28571429,12.3867729 1.5736942,12.750924 1.92857143,12.750924 L16.0714286,12.750924 C16.4271345,12.750924 16.7142857,12.3867729 16.7142857,11.9367218 L16.7142857,2.22967837 C16.7142857,1.78051849 16.4262556,1.41547616 16.0714286,1.41547616 L16.0714286,1.4163674 Z M9,4 C11.083203,4 12.8536128,5.46640701 13.723178,6.3369036 C14.092274,6.71080818 14.092274,7.28919182 13.723178,7.6630964 C12.8536128,8.53359299 11.083203,10 9,10 C6.916797,10 5.14638725,8.53359299 4.27682202,7.6630964 C3.90772599,7.28919182 3.90772599,6.71080818 4.27682202,6.3369036 C5.14638724,5.46640701 6.91679699,4 9,4 Z M9,4.46153847 C7.10447294,4.46153847 5.45918049,5.83446933 4.63966219,6.65238559 C4.44573038,6.84518013 4.44573038,7.1489776 4.63966219,7.34177215 C5.45292461,8.15968841 7.10447294,9.53261927 9,9.53261927 C10.8955271,9.53261927 12.5408195,8.15968841 13.3603378,7.34177215 C13.5542696,7.14897761 13.5542696,6.84518014 13.3603378,6.65238559 C12.5408195,5.83446933 10.8955271,4.46153847 9,4.46153847 Z M9,5.53067186 C9.86330935,5.53067186 10.5702221,6.18500487 10.5702221,6.99707887 C10.5702221,7.80915287 9.86330935,8.46348588 9,8.46348588 C8.13669065,8.46348588 7.42977792,7.80915287 7.42977792,6.99707887 C7.42977792,6.18500487 8.13669065,5.53067186 9,5.53067186 Z M9,5.99805258 C8.4119487,5.99805258 7.9302471,6.44790651 7.9302471,6.99707887 C7.9302471,7.54625123 8.41194869,7.99610516 9,7.99610516 C9.58805131,7.99610516 10.0697529,7.54625123 10.0697529,6.99707887 C10.0697529,6.44790651 9.58805131,5.99805258 9,5.99805258 Z'
                id='形状'></path>
            </g>
          </g>
        </g>
      </g>
    </svg>
  </i>
</template>

<script>

/**
 * 组件说明：
 * 此组件是一个基本的Vue组件示例，目前尚未实现具体的业务逻辑。
 * 它展示了如何定义一个基本的Vue组件，包括props、data和methods的声明。
 *
 * @export
 * @returns {Object} 返回一个Vue组件定义对象
 */
export default {
  // 定义组件接收的外部属性，目前未定义任何props
  props: {
  },
  // 定义组件内部的状态，目前未定义任何状态
  data() {
    return {};
  },
  // 定义组件的方法，目前未定义任何方法
  methods: {}
};
</script>

<style lang='less' src='./index.less' />
