<!--
 * @Description:
 * @Autor: he.chao
 * @Date: 2022-10-19 15:07:55
 * @LastEditors: liu.yongli
 * @LastEditTime: 2023-12-13 14:36:14
-->
<template>

  <i class='icon'>
    <svg width="16px" height="16px" fill="#E8F3FF" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g id="综合监测-大屏" stroke="none" stroke-width="1" fill-rule="evenodd">
        <g id="1-7综合监测-云广播" transform="translate(-670.000000, -802.000000)" fill-rule="nonzero">
          <g id="云广播喊话" transform="translate(384.000000, 721.000000)">
            <g id="输入框备份-2" transform="translate(12.000000, 59.000000)">
              <g id="icon_小喇叭_16_n" transform="translate(274.000000, 22.000028)">
                <rect id="矩形" opacity="0" x="0" y="0" width="16" height="16"></rect>
                <path d="M8.21158455,2.05473489 L4.61010634,4.36772669 L2.81340452,4.36772669 C1.81504141,4.36772669 1.00591,5.16694481 1.00591,6.16802608 L1.00591,9.77582 C1.00591,10.7705855 1.80514413,11.5761194 2.81340452,11.5761194 L4.61010634,11.5761194 L8.21158455,13.8873044 C8.72067938,14.1080667 9.1153398,13.8719868 9.1153398,13.3709985 L9.1153398,2.57102484 C9.1153398,2.06914112 8.71076609,1.83937691 8.21158455,2.05473489 Z M11.246327,4.96151609 C11.0769218,4.7794955 10.7912915,4.76958222 10.6101983,4.93988277 C10.4281777,5.1101833 10.4191598,5.39491825 10.588565,5.57692284 C10.6723641,5.66702175 10.815627,5.86616673 10.9633988,6.1671147 C11.2156918,6.68071848 11.3679725,7.28350978 11.3679725,7.97192303 C11.3679725,8.66033628 11.2156918,9.26402298 10.9633988,9.77673138 C10.815627,10.0776633 10.6723641,10.2768083 10.588565,10.3669232 C10.4182644,10.5489278 10.4281777,10.8336628 10.6101983,11.0039633 C10.7922029,11.1742638 11.0769378,11.1643506 11.2472384,10.98233 C11.3887105,10.8309606 11.5833305,10.5606479 11.7725622,10.174094 C12.0834234,9.54154683 12.2690416,8.80449456 12.2690416,7.97102764 C12.2690416,7.13756073 12.0834234,6.40049247 11.7725622,5.76796133 C11.5824191,5.3831982 11.3877831,5.11197409 11.246327,4.96151609 L11.246327,4.96151609 Z M14.0089339,4.40376625 C13.6449088,3.78744811 13.277286,3.36034569 13.0195887,3.13058147 C12.8339546,2.96480586 12.5492356,2.98101887 12.383428,3.16753241 C12.2176524,3.35404595 12.2338495,3.63786952 12.420379,3.80366112 C12.4627183,3.84149147 12.5474288,3.92620205 12.6609519,4.05774484 C12.8555719,4.28300012 13.050192,4.55152206 13.2331239,4.86148791 C13.756625,5.74992555 14.0710998,6.78882117 14.0710998,7.97190705 C14.0710998,9.15499292 13.756625,10.1938885 13.2322285,11.0832376 C13.0493126,11.3931874 12.8537811,11.6617094 12.6600565,11.8869646 C12.5465335,12.0185075 12.4618229,12.103218 12.4194676,12.1410484 C12.2338494,12.30684 12.2176524,12.5915749 12.3825326,12.777209 C12.5474288,12.9628272 12.8330592,12.9790242 13.0186773,12.814144 C13.2763906,12.5843798 13.6440134,12.1581728 14.0080385,11.5409592 C14.6099505,10.5200834 14.9712592,9.32618884 14.9712592,7.97190705 C14.9721689,6.61855262 14.6108458,5.42465808 14.0089339,4.40376625 Z" id="形状" ></path>
              </g>
            </g>
          </g>
        </g>
      </g>
    </svg>
  </i>
</template>

<script>

/**
 * 组件说明：
 * 此组件是一个基本的Vue组件示例，目前尚未实现具体的业务逻辑。
 * 它展示了如何定义一个基本的Vue组件，包括props、data和methods的声明。
 *
 * @export
 * @returns {Object} 返回一个Vue组件定义对象
 */
export default {
  // 定义组件接收的外部属性，目前未定义任何props
  props: {
  },
  // 定义组件内部的状态，目前未定义任何状态
  data() {
    return {};
  },
  // 定义组件的方法，目前未定义任何方法
  methods: {}
};
</script>

<style scoped lang='less' src='./index.less' />
