<!--
 * @Description:
 * @Autor: he.chao
 * @Date: 2022-10-19 15:07:55
 * @LastEditors: liu.yongli
 * @LastEditTime: 2023-12-13 14:36:14
-->
<template>
  <i class='icon'>
    <svg height='20' width='20'>
      <symbol id='beats' viewBox='0 0 100 100'>
        <line class='beat' x1='15' y1='40' x2='15' y2='100' stroke='currentColor' stroke-width='10'
              stroke-linecap='round'></line>
        <line class='beat' x1='50' y1='40' x2='50' y2='100' stroke='currentColor' stroke-width='10'
              stroke-linecap='round'></line>
        <line class='beat' x1='85' y1='40' x2='85' y2='100' stroke='currentColor' stroke-width='10'
              stroke-linecap='round'></line>
      </symbol>
    </svg>
    <svg height='30' width='30'>
         <use href='#beats'></use>
    </svg>
  </i>
</template>

<script>

/**
 * 组件说明：
 * 此组件是一个基本的Vue组件示例，目前尚未实现具体的业务逻辑。
 * 它展示了如何定义一个基本的Vue组件，包括props、data和methods的声明。
 *
 * @export
 * @returns {Object} 返回一个Vue组件定义对象
 */
export default {
  // 定义组件接收的外部属性，目前未定义任何props
  props: {
  },
  // 定义组件内部的状态，目前未定义任何状态
  data() {
    return {};
  },
  // 定义组件的方法，目前未定义任何方法
  methods: {}
};
</script>

<style lang='less'>
.icon .beat {
  transform-origin: bottom; //将变换参考点设置成`<svg>元素`的底部
  animation: beat-scale 1.4s linear infinite;

  &:nth-child(1) {
    animation-delay: 0.4s;
  }

  &:nth-child(2) {
    animation-delay: 0.2s;
  }
}

@keyframes beat-scale {
  25% {
    transform: scaleY(0.3);
  }
  50% {
    transform: scaleY(1);
  }
  75% {
    transform: scaleY(0.3);
  }
}
</style>
