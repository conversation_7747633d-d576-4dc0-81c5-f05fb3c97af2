<!--
 * @Description:
 * @Autor: he.chao
 * @Date: 2022-10-19 15:07:55
 * @LastEditors: liu.yongli
 * @LastEditTime: 2023-12-13 14:36:14
-->
<template>
  <i class='icon icon-title-detail' @click='click'/>
</template>

<script>

/**
 * 组件说明：这里写组件的功能描述
 *
 * @props {Function} click - 点击事件的回调函数。当点击组件时触发此函数。
 */
export default {
  props: {
    click: {
      type: Function,
      default: () => {
        console.log('click');
      }
    }
  },
  data() {
    // 初始化数据
    return {};
  },
  methods: {}
};
</script>

<style scoped lang='less' src='./index.less' />

