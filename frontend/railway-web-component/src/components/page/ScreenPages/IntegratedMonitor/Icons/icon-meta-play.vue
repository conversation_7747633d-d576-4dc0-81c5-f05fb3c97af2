<!--
 * @Description:
 * @Autor: he.chao
 * @Date: 2022-10-19 15:07:55
 * @LastEditors: liu.yongli
 * @LastEditTime: 2023-12-13 14:36:14
-->
<template>
  <i class='icon'>
    <svg width='13px' height='18px' viewBox='0 0 13 18' fill='#0095FF' version='1.1' xmlns='http://www.w3.org/2000/svg'>
      <g id='综合监测-大屏' stroke='none' stroke-width='1' fill-rule='evenodd'>
        <g id='1-6综合监测-弹框' transform='translate(-1154.000000, -362.000000)' fill-rule='nonzero'>
          <g id='云广播喊话' transform='translate(816.000000, 116.000000)'>
            <g id='编组-7' transform='translate(24.000000, 245.000000)'>
              <g id='icon_素材播放_18_s' transform='translate(314.000000, 1.000000)'>
                <path
                  d='M0.617697098,8.0955 C1.01456437,8.03475 1.38862319,8.3025 1.45020604,8.69625 C1.83110739,11.12625 4.00247318,12.96 6.5,12.96 C8.99752682,12.96 11.1666118,11.1285 11.549794,8.6985 C11.6113768,8.307 11.9831548,8.03925 12.3823029,8.09775 C12.781451,8.1585 13.0528717,8.52525 12.9912889,8.919 C12.5305579,11.85525 10.1607585,14.04225 7.22987085,14.35725 L7.22987085,14.35725 L7.22987085,16.56 L9.41948341,16.56 C9.82319323,16.56 10.1493543,16.88175 10.1493543,17.28 C10.1493543,17.67825 9.82319323,18 9.41948341,18 L9.41948341,18 L3.58051659,18 C3.17680677,18 2.85064574,17.67825 2.85064574,17.28 C2.85064574,16.88175 3.17680677,16.56 3.58051659,16.56 L3.58051659,16.56 L5.77012915,16.56 L5.77012915,14.35725 C2.83696066,14.04225 0.469442081,11.853 0.00871110496,8.91675 C-0.0528717482,8.523 0.218548975,8.15625 0.617697098,8.0955 Z M6.5,0 L6.69305771,0.0050149374 C8.61138498,0.104890739 10.1493543,1.68387097 10.1493543,3.6 L10.1493543,3.6 L10.1493543,7.92 C10.1493543,9.9 8.50714484,11.52 6.5,11.52 L6.5,11.52 L6.30694229,11.5149851 C4.38861502,11.4151093 2.85064574,9.83612903 2.85064574,7.92 L2.85064574,7.92 L2.85064574,3.6 C2.85064574,1.62 4.49285516,0 6.5,0 L6.5,0 Z M8.09951411,9.78989244 L4.95852161,9.78989244 C4.81623718,9.78989244 4.70089286,9.90523676 4.70089286,10.0475212 C4.70089286,10.1898056 4.81623718,10.3051499 4.95852161,10.3051499 L4.95852161,10.3051499 L8.09951411,10.3051499 C8.24179854,10.3051499 8.35714286,10.1898056 8.35714286,10.0475212 C8.35714286,9.90523676 8.24179854,9.78989244 8.09951411,9.78989244 L8.09951411,9.78989244 Z M9.14415697,8.75937745 L3.91387875,8.75937745 C3.77159432,8.75937745 3.65625,8.87472177 3.65625,9.0170062 C3.65625,9.15929063 3.77159432,9.27463494 3.91387875,9.27463494 L3.91387875,9.27463494 L9.14415697,9.27463494 C9.28644139,9.27463494 9.40178571,9.15929063 9.40178571,9.0170062 C9.40178571,8.87472177 9.28644139,8.75937745 9.14415697,8.75937745 L9.14415697,8.75937745 Z M9.14415697,7.72886245 L3.91387875,7.72886245 C3.77159432,7.72886245 3.65625,7.84420677 3.65625,7.9864912 C3.65625,8.12877563 3.77159432,8.24411995 3.91387875,8.24411995 L3.91387875,8.24411995 L9.14415697,8.24411995 C9.28644139,8.24411995 9.40178571,8.12877563 9.40178571,7.9864912 C9.40178571,7.84420677 9.28644139,7.72886245 9.14415697,7.72886245 L9.14415697,7.72886245 Z M9.14415697,6.69834746 L3.91387875,6.69834746 C3.77159432,6.69834746 3.65625,6.81369178 3.65625,6.95597621 C3.65625,7.09826064 3.77159432,7.21360496 3.91387875,7.21360496 L3.91387875,7.21360496 L9.14415697,7.21360496 C9.28644139,7.21360496 9.40178571,7.09826064 9.40178571,6.95597621 C9.40178571,6.81369178 9.28644139,6.69834746 9.14415697,6.69834746 L9.14415697,6.69834746 Z M9.14415697,5.66783247 L3.91387875,5.66783247 C3.77159432,5.66783247 3.65625,5.78317679 3.65625,5.92546121 C3.65625,6.06774564 3.77159432,6.18308996 3.91387875,6.18308996 L3.91387875,6.18308996 L9.14415697,6.18308996 C9.28644139,6.18308996 9.40178571,6.06774564 9.40178571,5.92546121 C9.40178571,5.78317679 9.28644139,5.66783247 9.14415697,5.66783247 L9.14415697,5.66783247 Z'
                  id='形状结合'></path>
              </g>
            </g>
          </g>
        </g>
      </g>
    </svg>
  </i>
</template>

<script>

/**
 * 组件说明：
 * 此组件是一个基本的Vue组件示例，目前尚未实现具体的业务逻辑。
 * 它展示了如何定义一个基本的Vue组件，包括props、data和methods的声明。
 *
 * @export
 * @returns {Object} 返回一个Vue组件定义对象
 */
export default {
  // 定义组件接收的外部属性，目前未定义任何props
  props: {
  },
  // 定义组件内部的状态，目前未定义任何状态
  data() {
    return {};
  },
  // 定义组件的方法，目前未定义任何方法
  methods: {}
};
</script>

<style scoped lang='less' src='./index.less' />
