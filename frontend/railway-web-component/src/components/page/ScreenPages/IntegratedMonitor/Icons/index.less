@size: 18px;

.icon {
  display: flex;
  width: @size;
  height: @size;
  cursor: pointer;
  align-items: center;
  justify-content: center;

  svg {
    fill: #fefefe;
    width: @size !important;
    height: @size !important;
  }

  &.active {
    svg {
      fill: #4f9fff;
    }
  }

  &:not(:first-child) {
    margin-left: 10px;
  }

  &.icon-broadcast-list {
    background: url("./img/icon_broadcast_list_icon.svg") 100% 100% no-repeat;
    background-size: 100% 100%;
  }

  &.icon-reltime-broadcast {
    background: url("./img/icon_reltime_broadcast.svg") 100% 100% no-repeat;
    background-size: 100% 100%;
  }

  &.icon-title-detail {
    background: url("./img/icon_main_title_detail.svg") 100% 100% no-repeat;
    background-size: 100% 100%;
  }

  &.icon-title-evt {
    background: url("./img/icon_main_title_evt.svg") 100% 100% no-repeat;
    background-size: 100% 100%;
  }
  &.icon-playing-sound {
    background: url("./img/icon_title_play_stat.png") 100% 100% no-repeat;
    background-size: 100% 100%;
  }
}
