<!--
 * @Description:
 * @Autor: he.chao
 * @Date: 2022-10-19 15:07:55
 * @LastEditors: liu.yongli
 * @LastEditTime: 2023-12-13 14:36:14
-->
<template>
  <i class='icon'>
    <svg width="14px" height="14px" viewBox="0 0 14 14" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
      <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
          <stop stop-color="#FFFFFF" offset="0%"></stop>
          <stop stop-color="#15BD94" offset="100%"></stop>
        </linearGradient>
      </defs>
      <g id="综合监测-大屏" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="2-1综合监测-过车信息" transform="translate(-731.000000, -431.000000)">
          <g id="站点名称" transform="translate(622.000000, 416.000000)">
            <g id="编组" transform="translate(18.000000, 6.000000)">
              <g id="编组-10" transform="translate(92.000000, 10.000000)">
                <circle id="椭圆形" fill="url(#linearGradient-1)" cx="6" cy="6" r="3"></circle>
                <circle id="椭圆形" stroke="#E8F3FF" cx="6" cy="6" r="6"></circle>
              </g>
            </g>
          </g>
        </g>
      </g>
    </svg>
  </i>
</template>

<script>

/**
 * 组件说明：
 * 此组件是一个基本的Vue组件示例，目前尚未实现具体的业务逻辑。
 * 它展示了如何定义一个基本的Vue组件，包括props、data和methods的声明。
 *
 * @export
 * @returns {Object} 返回一个Vue组件定义对象
 */
export default {
  // 定义组件接收的外部属性，目前未定义任何props
  props: {
  },
  // 定义组件内部的状态，目前未定义任何状态
  data() {
    return {};
  },
  // 定义组件的方法，目前未定义任何方法
  methods: {}
};
</script>

<style lang='less'>
.icon .beat {
  transform-origin: bottom; //将变换参考点设置成`<svg>元素`的底部
  animation: beat-scale 1.4s linear infinite;

  &:nth-child(1) {
    animation-delay: 0.4s;
  }

  &:nth-child(2) {
    animation-delay: 0.2s;
  }
}

@keyframes beat-scale {
  25% {
    transform: scaleY(0.3);
  }
  50% {
    transform: scaleY(1);
  }
  75% {
    transform: scaleY(0.3);
  }
}
</style>
