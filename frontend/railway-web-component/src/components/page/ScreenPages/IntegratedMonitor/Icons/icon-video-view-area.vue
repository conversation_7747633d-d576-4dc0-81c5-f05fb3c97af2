<!--
 * @Description:
 * @Autor: he.chao
 * @Date: 2022-10-19 15:07:55
 * @LastEditors: liu.yongli
 * @LastEditTime: 2023-12-13 14:36:14
-->
<template>
  <i class='icon' title='可视域'>
    <svg width='18px' height='17px' viewBox='0 0 18 17' fill='#fefefe' version='1.1' xmlns='http://www.w3.org/2000/svg'>
      <g id='综合监测-大屏' stroke='none' stroke-width='1' fill-rule='evenodd'>
        <g id='1-8综合监测-摄像机' transform='translate(-54.000000, -459.000000)' fill-rule='nonzero'>
          <g id='告警详情' transform='translate(12.000000, 92.000000)'>
            <g id='icon_可视域_18_s' transform='translate(42.000000, 367.000000)'>
              <path
                d='M9,5.76500277 C9.85153731,5.79350224 10.6353884,5.30806982 10.9814165,4.53792815 C11.3274447,3.76778648 11.1664319,2.86699107 10.574409,2.26090018 C9.98238614,1.6548093 9.07777857,1.46466251 8.28774469,1.78024769 C7.49771081,2.09583287 6.98028529,2.85402185 6.97979799,3.69679554 C6.96060844,4.2387803 7.16494862,4.76520289 7.54590978,5.15521668 C7.92687095,5.54523047 8.45173028,5.76533505 9,5.76500277 L9,5.76500277 Z M9,7.3236517 C12.5757576,7.3236517 15.0606061,4.81582555 15.0606061,3.65683018 C15.0606061,2.4978348 12.5959596,0 9,0 C5.40404041,0 2.93939394,2.43788679 2.93939394,3.65683018 C2.93939394,4.87577356 5.53535353,7.3236517 9,7.3236517 Z M9,0.659428393 C9.79118966,0.667174357 10.5460573,0.989027823 11.0949958,1.55267327 C11.6439343,2.11631873 11.9409082,2.87448729 11.9191919,3.65683018 C11.9191919,5.25155077 10.6122252,6.54432724 9,6.54432724 C7.38777482,6.54432724 6.08080807,5.25155077 6.08080807,3.65683018 C6.06458355,2.87602303 6.36337082,2.12096678 6.91114104,1.55852091 C7.45891125,0.996075051 8.21056691,0.67254532 9,0.659428393 L9,0.659428393 Z M3.38383838,8.99220535 L2.5050505,8.50262972 L2.01010102,9.37187624 L2.8888889,9.86145187 L3.38383838,8.99220535 Z M0,12.8188883 L0.878787879,13.3184553 L1.38383838,12.4492088 L0.505050497,11.9896071 L0,12.8188883 Z M1.5050505,10.2111487 L1,11.0803953 L1.87878788,11.5799622 L2.38383838,10.7107157 L1.5050505,10.2111487 Z M4.35353535,7.21374695 L3.85858587,6.93398947 L3.60606061,6.7941107 L3.52525253,6.74415401 L3.02020201,7.61340053 L3.89898989,8.11296749 C3.94577604,8.14868474 3.99662688,8.17886398 4.05050504,8.20288954 L4.48484848,7.30366901 L4.36363636,7.24372097 L4.35353535,7.21374695 Z M11.6161616,7.91314071 L11.4040404,7.91314071 L11.6060606,8.91227465 C11.9714108,8.84359324 12.3323153,8.75351265 12.6868687,8.64250849 L12.3838384,7.64337455 C12.1313131,7.72330526 11.8787879,7.79324464 11.6161616,7.85319268 L11.6161616,7.91314071 Z M15.1111111,9.83147785 L15.989899,9.34190222 L15.4949495,8.4726557 L14.6161616,8.96223134 L15.1111111,9.83147785 Z M1.92929292,14.3175892 C2.22214323,14.5769598 2.52899059,14.8204395 2.84848485,15.046957 L3.5050505,14.1377451 C3.21271927,13.9380333 2.93275574,13.7211652 2.66666667,13.488308 L1.92929292,14.3175892 Z M5.78787879,7.77326197 L5.58585859,7.77326197 L5.41414141,7.72330526 L5.10101011,8.6724825 C5.43434344,8.78238723 5.79797981,8.88230063 6.17171716,8.97222268 L6.39393939,7.97308875 C6.19906291,7.93499462 6.00677206,7.88494118 5.81818182,7.82321866 L5.78787879,7.77326197 Z M17.4949495,11.9896071 L16.6161616,12.4891741 L17.1212121,13.3584206 L18,12.8588537 L17.4949495,11.9896071 Z M14.7575758,7.79324464 L14.989899,7.66335722 L14.4848485,6.7941107 L13.7373737,7.2237383 L13.6161616,7.28368634 L13.5252525,7.28368634 L13.3232323,7.36361704 L13.7373737,8.28282027 L14.1010101,8.09298482 L14.6464647,7.78325329 L14.7575758,7.79324464 Z M12.5959596,15.1868357 L13.010101,16.1859696 C13.3814438,16.0439699 13.7425528,15.8770595 14.0909091,15.6864027 L13.5555556,14.6872687 C13.2424242,14.9070782 12.9191919,15.0569483 12.5959596,15.1868357 Z M10.5757576,15.756342 L10.7575758,16.845398 C11.1467121,16.7881466 11.5314445,16.7046915 11.9090909,16.5956145 L11.6161616,15.5265412 C11.2675706,15.6239744 10.9134038,15.7007115 10.5555556,15.756342 L10.5757576,15.756342 Z M16.5151515,10.2111487 L15.6363636,10.7107157 L16.1414141,11.5799622 L17.020202,11.0803953 L16.5151515,10.2111487 Z M14.4949495,14.2076845 L15.1313131,15.1168963 C15.4580194,14.8913843 15.7716458,14.6478772 16.0707071,14.3875286 L15.3333333,13.5482561 C15.052142,13.7798557 14.7588059,13.9966353 14.4545455,14.1976931 L14.4949495,14.2076845 Z M9.44444444,15.8762381 L8.53535353,15.8762381 L8.37373738,16.9852768 C8.71014526,17.0049077 9.0474305,17.0049077 9.38383838,16.9852768 L9.55555556,16.9852768 L9.4949495,15.8762381 L9.44444444,15.8762381 Z M7.79797981,8.14294151 L7.38383838,8.14294151 L7.25252526,9.14207544 C7.60606061,9.14207544 7.97979799,9.22200615 8.34343435,9.24198884 L8.39393939,8.24285491 C8.16161617,8.18290687 7.92929292,8.1629242 7.70707071,8.13295018 L7.79797981,8.14294151 Z M6.05050504,16.5656405 C6.42897957,16.6766498 6.81341391,16.7667115 7.20202019,16.8354067 L7.39393939,15.7363594 C7.0531751,15.6784264 6.71594924,15.6017066 6.38383838,15.5065586 L6.05050504,16.5656405 Z M3.86868686,15.66642 C4.19564632,15.8545376 4.53286021,16.0246494 4.87878788,16.1759783 L5.31313132,15.1768444 C4.968465,15.0413882 4.63129254,14.8879728 4.3030303,14.7172428 L3.86868686,15.66642 Z M9.40404041,8.19289822 L9.40404041,9.19203213 C9.76767678,9.19203213 10.1313131,9.19203213 10.4949495,9.12209277 L10.3737374,8.12295884 C10.0808081,8.1629242 9.74747476,8.18290687 9.40404041,8.19289822 L9.40404041,8.19289822 Z'
                id='形状'></path>
            </g>
          </g>
        </g>
      </g>
    </svg>
  </i>
</template>

<script>

/**
 * 组件说明：
 * 此组件是一个基本的Vue组件示例，目前尚未实现具体的业务逻辑。
 * 它展示了如何定义一个基本的Vue组件，包括props、data和methods的声明。
 *
 * @export
 * @returns {Object} 返回一个Vue组件定义对象
 */
export default {
  // 定义组件接收的外部属性，目前未定义任何props
  props: {
  },
  // 定义组件内部的状态，目前未定义任何状态
  data() {
    return {};
  },
  // 定义组件的方法，目前未定义任何方法
  methods: {}
};
</script>

<style lang='less' src='./index.less' />
