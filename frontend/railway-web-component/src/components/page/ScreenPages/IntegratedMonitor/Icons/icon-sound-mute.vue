<!--
 * @Description:
 * @Autor: he.chao
 * @Date: 2022-10-19 15:07:55
 * @LastEditors: liu.yongli
 * @LastEditTime: 2023-12-13 14:36:14
-->
<template>

  <i class='icon'>
    <svg width="16px" height="16px" fill="#E8F3FF" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g id="综合监测-大屏" stroke="none" stroke-width="1" fill-rule="evenodd">
        <g id="1-7综合监测-云广播" transform="translate(-444.000000, -802.000000)" fill-rule="nonzero">
          <g id="云广播喊话" transform="translate(384.000000, 721.000000)">
            <g id="输入框备份-2" transform="translate(12.000000, 59.000000)">
              <g id="icon_小喇叭静音_16_n" transform="translate(48.000000, 22.000028)">
                <rect id="矩形" fill="#000000" opacity="0" x="0" y="0" width="16" height="16"></rect>
                <path d="M11.2056745,2.08078557 L7.60419634,4.39377737 L5.80749452,4.39377737 C4.80913141,4.39377737 4,5.1929955 4,6.19407676 L4,9.80187068 C4,10.7966362 4.79923412,11.6021701 5.80749452,11.6021701 L7.60419634,11.6021701 L11.2056745,13.9133551 C11.7147694,14.1341174 12.1094298,13.8980375 12.1094298,13.3970492 L12.1094298,2.59707553 C12.1094298,2.09519181 11.7048561,1.86542759 11.2056745,2.08078557 Z" id="路径" ></path>
              </g>
            </g>
          </g>
        </g>
      </g>
    </svg>
  </i>
</template>

<script>

/**
 * 组件说明：
 * 此组件是一个基本的Vue组件示例，目前尚未实现具体的业务逻辑。
 * 它展示了如何定义一个基本的Vue组件，包括props、data和methods的声明。
 *
 * @export
 * @returns {Object} 返回一个Vue组件定义对象
 */
export default {
  // 定义组件接收的外部属性，目前未定义任何props
  props: {
  },
  // 定义组件内部的状态，目前未定义任何状态
  data() {
    return {};
  },
  // 定义组件的方法，目前未定义任何方法
  methods: {}
};
</script>

<style scoped lang='less' src='./index.less' />
