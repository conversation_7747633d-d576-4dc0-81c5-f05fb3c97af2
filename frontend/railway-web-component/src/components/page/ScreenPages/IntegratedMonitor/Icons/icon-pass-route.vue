<!--
 * @Description:
 * @Autor: he.chao
 * @Date: 2022-10-19 15:07:55
 * @LastEditors: liu.yongli
 * @LastEditTime: 2023-12-13 14:36:14
-->
<template>
  <i class='icon' title='过车信息'>
    <svg width="18px" height="18px" viewBox="0 0 12 12" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
        <title>icon_过车信息_12_n</title>
        <g id="综合监测-大屏" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" opacity="0.9">
            <g id="3-综合监测-过车信息+-摄像机详情" transform="translate(-849.000000, -861.000000)" fill="#E0EAF2" fill-rule="nonzero">
                <g id="编组-3" transform="translate(657.000000, 764.000000)">
                    <g id="工具" transform="translate(188.000000, 73.000000)">
                        <g id="icon_过车信息_12_n" transform="translate(4.000000, 24.000000)">
                            <path d="M10.6440684,11.9999931 L7.87774879,11.9999931 C7.63726309,11.9999931 7.42687481,11.7899347 7.42687481,11.5499934 C7.42687481,11.3100521 7.63726309,11.0999936 7.87774879,11.0999936 L10.6440684,11.0999936 C10.884701,11.0999936 11.0950892,10.8900834 11.0950892,10.6499939 L11.0950892,8.34008311 C11.0950892,8.09999536 11.3056243,7.89008337 11.54611,7.89008337 C11.7865957,7.89008337 11.9971308,8.09999536 11.9971308,8.34008311 L11.9971308,10.6499939 C12.0272283,11.3699642 11.3951825,11.9999931 10.6440684,11.9999931 L10.6440684,11.9999931 Z M3.69831874,11.9999966 L1.38316671,11.9999966 C1.01607884,12.0008123 0.663789503,11.8556799 0.404218532,11.5966966 C0.144647561,11.3377132 -0.00081412652,10.9862214 3.42768317e-06,10.6199646 L3.42768317e-06,8.31005383 C3.42768317e-06,8.06996608 0.210541984,7.86005409 0.451027687,7.86005409 C0.691513391,7.86005409 0.90204849,8.06996608 0.90204849,8.31005383 L0.90204849,10.6199646 C0.90204849,10.8600541 1.11258359,11.0699644 1.35306929,11.0699644 L3.66836813,11.0699644 C3.90885383,11.0699644 4.11924212,11.2800228 4.11924212,11.5201106 C4.14948635,11.7899347 3.93895125,11.9699639 3.69831874,11.9999966 Z M0.451027687,4.16952886 C0.211129254,4.16952886 6.88433374e-06,3.96005633 6.88433374e-06,3.71996858 L6.88433374e-06,1.38002851 C6.88433374e-06,0.630028935 0.631318554,0 1.38316671,0 L3.69831874,0 C3.93895125,0 4.14948635,0.210058472 4.14948635,0.449999744 C4.14948635,0.689941015 3.93895125,0.899999487 3.69831874,0.899999487 L1.38316671,0.899999487 C1.14253419,0.899999487 0.932145908,1.11005796 0.932145908,1.34999923 L0.932145908,3.6600565 C0.932145908,3.93002705 0.721610809,4.14008552 0.451027687,4.16996831 L0.451027687,4.16952886 Z M11.54611,4.16952886 C11.3056243,4.16952886 11.0950892,3.95961687 11.0950892,3.71952912 L11.0950892,1.38002851 C11.0950892,1.14008724 10.884701,0.930028768 10.6440684,0.930028768 L7.87774879,0.930028768 C7.63726309,0.930028768 7.42687481,0.719970296 7.42687481,0.480029025 C7.42687481,0.240087753 7.6073125,0 7.84779819,0 L10.6141178,0 C11.3957698,0 11.9971308,0.600585595 11.9971308,1.38002851 L11.9971308,3.6899393 C12.0272283,3.93002705 11.8166932,4.14008552 11.54611,4.16996831 L11.54611,4.16952886 Z" id="形状"></path>
                            <g id="编组-7" transform="translate(2.000000, 2.000000)">
                                <path d="M4.313645,5.47428601 C4.27150861,6.14035008 4.60536972,6.76451964 5.18984687,7.13878702 C5.7709285,7.47174513 6.4855227,7.47174513 7.06660433,7.13878702 C7.65108148,6.80568175 7.98406169,6.13947119 7.9428062,5.47428601 C7.9428062,4.80807545 7.6089451,4.1839059 7.02549566,3.85080062 C6.44445405,3.51769286 5.72977982,3.51769286 5.14873821,3.85080062 C4.56514196,4.1839059 4.313645,4.80807546 4.313645,5.47428601 Z M7.06660433,5.47428601 C7.10711572,5.80998373 6.94299642,6.13742162 6.64949818,6.30646328 C6.35136443,6.47263 5.98818501,6.47263 5.69005125,6.30646328 C5.39658001,6.13740876 5.23251227,5.80995851 5.27309192,5.47428601 C5.27309192,5.14118074 5.43958202,4.85011645 5.73218764,4.64196228 C6.02391236,4.47599558 6.35777346,4.47599558 6.64949818,4.64196228 C6.90011423,4.85011646 7.06660433,5.14118074 7.06660433,5.47428601 L7.06660433,5.47428601 Z M5.60680483,5.47428601 C5.60680483,5.68229371 5.69005125,5.89044788 5.85756908,5.973358 C6.02391236,6.09845559 6.27452841,6.09845559 6.44189941,5.973358 C6.60927041,5.84826041 6.73377095,5.68229371 6.69251546,5.47428601 C6.69251546,5.26627831 6.60941722,5.10016513 6.44189941,4.97506755 C6.2743816,4.84996998 6.06692964,4.85011645 5.85756908,4.97506755 C5.69000232,5.10016514 5.6064147,5.26657129 5.60680483,5.47428601 Z M8.40190192,6.51432448 C8.44403831,6.47228348 8.44403831,6.43126789 8.40190192,6.3482113 C8.35991236,6.3061703 8.31865687,6.2651547 8.2766673,6.2651547 C8.15143267,6.2651547 8.06818762,6.3482113 8.02605125,6.4731624 C7.65108148,7.13937296 6.94122289,7.59655043 6.14929379,7.59655043 C5.35736468,7.59655043 4.64706564,7.18068152 4.27150861,6.47345537 C4.21856911,6.37398941 4.12855521,6.29927917 4.02089257,6.26544767 C3.97890301,6.26544767 3.89565794,6.30734217 3.89565796,6.34850427 C3.89565797,6.38966638 3.85352157,6.43156087 3.89565796,6.51461746 C4.18738268,7.09762493 4.68846796,7.55465593 5.31420059,7.80485109 L4.4801351,7.80485109 C4.35475366,7.80485109 4.22937224,7.92994868 4.22937224,8.05489977 L4.22937224,8.30494845 C4.22937224,8.43004604 4.3127641,8.55499715 4.4801351,8.55499715 L7.7754352,8.55499715 C7.90081664,8.55499715 8.02605125,8.43004604 8.02605125,8.30494845 L8.02605125,8.05489977 C8.02605125,7.92994867 7.9428062,7.80485109 7.7754352,7.80485109 L6.9826252,7.80485109 C7.60821102,7.5558278 8.10856221,7.09762493 8.40116784,6.51461746 L8.40190192,6.51432448 Z" id="形状"></path>
                                <g id="火车票">
                                    <path d="M3.00221874,0 C3.41646976,0 3.75247336,0.363578129 3.75247336,0.81559418 L4.80190929,0.81559418 C5.40027187,0.81559418 5.59358902,1.28234989 5.63041133,1.87193604 L5.70415094,3.07495045 C5.49445823,3.09513687 5.29233614,3.14122745 5.10102315,3.20998373 L5.10109058,1.96528718 C5.10109058,1.78349812 4.96760969,1.63610158 4.80190929,1.63610158 L1.1979254,1.63610158 C1.03222499,1.63610158 0.898744104,1.78349812 0.898744104,1.96528718 L0.898744104,3.27220316 C0.898744104,3.45399222 1.03222499,3.60138876 1.1979254,3.60138876 L4.39839151,3.60034924 C3.81871347,4.05819011 3.44667822,4.76731696 3.44667822,5.56333729 C3.44667822,5.97354879 3.54547696,6.3606846 3.72057702,6.7022473 L1.30378955,6.70162929 C1.24855608,6.70162929 1.1979254,6.69671607 1.14729472,6.68688964 L0.530520974,7.84640907 C0.452273559,7.99380561 0.277367572,8.04293779 0.143886687,7.95941309 C0.00580301298,7.87588838 -0.0402248783,7.68427288 0.0380225369,7.53687634 L0.613371178,6.45596839 C0.309587096,6.22013393 0.12547553,5.82707649 0.153092265,5.3898001 L0.369423354,1.87193604 C0.406245667,1.28234989 0.756057641,0.81559418 1.34981744,0.81559418 L2.25196411,0.81559418 C2.25196411,0.363578129 2.58796771,0 3.00221874,0 Z M4.48196421,7.5895456 L4.47312285,7.60758737 C4.44528518,7.64791507 4.40054607,7.67444644 4.35083595,7.67444644 L1.64899873,7.67444644 C1.56614853,7.67444644 1.49710669,7.60074817 1.49710669,7.51231025 L1.49710669,7.35017406 C1.49710669,7.26173614 1.56614853,7.18803787 1.64899873,7.18803787 L4.04782059,7.1895499 C4.17617617,7.33928498 4.32193591,7.47366208 4.48196421,7.5895456 Z M1.34521465,4.08779734 C1.09666404,4.08779734 0.894141315,4.30889215 0.894141315,4.57911913 C0.898744104,4.84934612 1.09666404,5.07044093 1.34521465,5.07044093 C1.59376526,5.07044093 1.79628798,4.84934612 1.79628798,4.57911913 C1.79628798,4.30889215 1.59376526,4.08779734 1.34521465,4.08779734 Z" id="形状结合"></path>
                                </g>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </svg>
  </i>
</template>

<script>

/**
 * 组件说明：
 * 此组件是一个基本的Vue组件示例，目前尚未实现具体的业务逻辑。
 * 它展示了如何定义一个基本的Vue组件，包括props、data和methods的声明。
 *
 * @export
 * @returns {Object} 返回一个Vue组件定义对象
 */
export default {
  // 定义组件接收的外部属性，目前未定义任何props
  props: {
  },
  // 定义组件内部的状态，目前未定义任何状态
  data() {
    return {};
  },
  // 定义组件的方法，目前未定义任何方法
  methods: {}
};
</script>

<style lang='less' src='./index.less' />
