<!--
 * @Description:
 * @Autor: he.chao
 * @Date: 2022-10-19 15:07:55
 * @LastEditors: liu.yongli
 * @LastEditTime: 2023-12-13 14:36:14
-->
<template>
  <i class="icon">
    <svg
      width="18px"
      height="18px"
      fill="#Fefefe"
      viewBox="0 0 18 18"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      xmlns:xlink="http://www.w3.org/1999/xlink"
    >
      <g id="综合监测-大屏" stroke="none" stroke-width="1" fill-rule="evenodd">
        <g id="1-2综合监测-工具" transform="translate(-1507.000000, -185.000000)" fill-rule="nonzero">
          <g id="工具" transform="translate(1376.000000, 92.000000)">
            <g id="编组-5" transform="translate(120.000000, 42.000000)">
              <g id="icon_工具_18_n" transform="translate(11.000000, 51.000000)">
                <path
                  d="M17.3111155,13.1677197 L14.0216635,9.87887454 L17.3825198,6.51801823 L17.382722,6.51801823 C18.205793,5.69413814 18.205793,4.35444802 17.3825198,3.53056792 L14.4711262,0.618972032 C14.0720308,0.219876666 13.5416567,0 12.9772999,0 C12.4131453,0 11.8825689,0.219876666 11.4838781,0.618972032 L8.12281954,3.97982834 L5.15094467,1.0077512 C4.59730857,0.45431738 3.8614196,0.14928149 3.07860203,0.14928149 C2.29578446,0.14928149 1.55989549,0.45431738 1.00646167,1.0077512 C0.452825579,1.56118501 0.147789689,2.29747854 0.147789689,3.08009383 C0.147789689,3.86270913 0.452825579,4.5985981 1.00646167,5.15203191 L3.97833654,8.12410906 L0.617277952,11.4851676 C-0.205793027,12.3090477 -0.205793027,13.6487379 0.617480231,14.472618 L3.52907612,17.3842138 C3.92817149,17.7833092 4.45854556,18.0031859 5.02270014,18.0031859 C5.58705699,18.0031859 6.11763334,17.7833092 6.51632415,17.3842138 L9.87738274,14.0231553 L13.1698689,17.3162483 L16.8122968,17.7527652 C16.8470887,17.7564062 16.8816783,17.7588335 16.9160657,17.7588335 C17.1507087,17.7588335 17.3740241,17.6657854 17.5411061,17.4958715 C17.732866,17.3012797 17.8222731,17.0346767 17.7862675,16.7638259 L17.3111155,13.1677197 Z M2.02250629,4.1359873 C1.74032786,3.85380887 1.58497802,3.47878464 1.58497802,3.08009383 C1.58497802,2.68099847 1.74053014,2.30597424 2.02291084,2.02379581 C2.58666086,1.45964124 3.5705432,1.45964124 4.13469778,2.02379581 L7.10677492,4.99567068 L4.99438116,7.10826672 L2.02250629,4.1359873 L2.02250629,4.1359873 Z M5.50007726,16.367967 C5.24520642,16.6234446 4.80039613,16.6234446 4.54512074,16.367967 L1.63372712,13.4563711 C1.37036059,13.1930045 1.37036059,12.7645788 1.63352484,12.5012123 L3.9036958,10.2312436 C3.93686946,10.3400694 3.98743907,10.4442428 4.07340741,10.5304134 L5.33744539,11.7944514 C5.61800559,12.0750116 6.07292981,12.0750116 6.35369228,11.7944514 C6.63425248,11.5136889 6.63425248,11.0587647 6.35369228,10.7782045 L5.0896543,9.51416651 C5.00348369,9.4279959 4.89931029,9.37762856 4.79048449,9.3444549 L6.62332945,7.51181222 C6.65650311,7.62063802 6.70707272,7.72481142 6.79304106,7.81077975 L8.76424448,9.78218545 C9.04480467,10.0627456 9.49972889,10.0627456 9.78049137,9.78218545 C10.0610516,9.50162525 10.0610516,9.04670103 9.78049137,8.76593856 L7.80928795,6.79453286 C7.72331961,6.70856452 7.61914622,6.65799491 7.51032042,6.62482125 L9.3429631,4.79217857 C9.37613676,4.90100437 9.42670637,5.00497549 9.51267471,5.0911461 L10.7767127,6.35518409 C11.0572729,6.63574428 11.5121971,6.63574428 11.7929596,6.35518409 C12.0735198,6.07462389 12.0735198,5.61969967 11.7929596,5.33893719 L10.5289216,4.07489921 C10.4429533,3.98872859 10.3387799,3.93836126 10.2299541,3.9051876 L12.500125,1.63521892 C12.6275604,1.50758123 12.797272,1.43739061 12.9772999,1.43739061 C13.1577322,1.43739061 13.3272416,1.50758123 13.4548793,1.63521892 L16.3664752,4.54681482 C16.6298417,4.81018135 16.6298417,5.23860709 16.3666774,5.50197362 L5.50007726,16.367967 Z M13.8347582,15.9484415 L10.8934274,13.0069084 L13.0056188,10.8947169 L15.9503884,13.8394864 L16.2677633,16.2397224 L13.8347582,15.9484415 L13.8347582,15.9484415 Z"
                  id="形状"
                />
              </g>
            </g>
          </g>
        </g>
      </g>
    </svg>
  </i>
</template>

<script>
/**
 * 组件说明：
 * 此组件是一个基本的Vue组件示例，目前尚未实现具体的业务逻辑。
 * 它展示了如何定义一个基本的Vue组件，包括props、data和methods的声明。
 *
 * @export
 * @returns {Object} 返回一个Vue组件定义对象
 */
export default {
  // 定义组件接收的外部属性，目前未定义任何props
  props: {},
  // 定义组件内部的状态，目前未定义任何状态
  data() {
    return {}
  },
  // 定义组件的方法，目前未定义任何方法
  methods: {},
}
</script>

<style lang='less' src='./index.less' />
