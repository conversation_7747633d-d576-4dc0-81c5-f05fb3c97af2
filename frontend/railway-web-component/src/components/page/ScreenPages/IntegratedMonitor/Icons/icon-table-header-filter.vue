<!--
 * @Description:
 * @Autor: he.chao
 * @Date: 2022-10-19 15:07:55
 * @LastEditors: liu.yongli
 * @LastEditTime: 2023-12-13 14:36:14
-->
<template>
  <i class='icon'>
    <svg width='13px' height='14px' fill='#E8F3FF' viewBox='0 0 13 14' version='1.1' xmlns='http://www.w3.org/2000/svg'>
      <g id='综合监测-大屏' stroke='none' stroke-width='1' fill-rule='evenodd'>
        <g id='1-7综合监测-云广播' transform='translate(-1077.000000, -836.000000)' fill-rule='nonzero'>
          <g id='云广播喊话' transform='translate(772.000000, 725.000000)'>
            <g id='表头' transform='translate(12.000000, 102.000000)'>
              <g id='icon_筛选_14_n' transform='translate(293.000000, 9.000000)'>
                <path
                  d='M7.75178475,14 C7.66037901,14 7.56878882,13.9781098 7.48463135,13.9340057 L5.12338996,12.696732 C4.92523049,12.5928937 4.80038915,12.3829728 4.80038979,12.1536069 L4.80038979,5.81577673 L0.172872343,1.0398239 C0.00408554277,0.865617252 -0.046405639,0.603629755 0.0449419609,0.376021637 C0.136289561,0.148413519 0.351487351,0 0.590192332,0 L12.4098077,0 C12.6485126,0 12.8637104,0.148413519 12.955058,0.376021637 C13.0464056,0.603629755 12.9959145,0.865617252 12.8271277,1.0398239 L8.34199366,5.66882668 L8.34199366,13.3908806 C8.34200834,13.5524371 8.27982992,13.7073806 8.16914057,13.8216165 C8.05845121,13.9358524 7.90832069,14 7.75178475,14 Z M5.98077072,11.7797218 L7.16161273,12.3984919 L7.16161273,5.41651801 C7.16160871,5.25496945 7.22379322,5.10003756 7.33448321,4.9858135 L10.9849588,1.21823877 L2.01504122,1.21823877 L5.80790024,5.13274452 C5.91858164,5.2469745 5.98076461,5.40190255 5.98077072,5.56344903 L5.98077072,11.7797218 Z'
                  id='形状'></path>
              </g>
            </g>
          </g>
        </g>
      </g>
    </svg>
  </i>
</template>

<script>

/**
 * 组件说明：
 * 此组件是一个基本的Vue组件示例，目前尚未实现具体的业务逻辑。
 * 它展示了如何定义一个基本的Vue组件，包括props、data和methods的声明。
 *
 * @export
 * @returns {Object} 返回一个Vue组件定义对象
 */
export default {
  // 定义组件接收的外部属性，目前未定义任何props
  props: {
  },
  // 定义组件内部的状态，目前未定义任何状态
  data() {
    return {};
  },
  // 定义组件的方法，目前未定义任何方法
  methods: {}
};
</script>

<style lang='less' src='./index.less' />
