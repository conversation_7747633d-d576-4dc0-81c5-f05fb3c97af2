<!--
 * @Description:
 * @Autor: he.chao
 * @Date: 2022-10-19 15:07:55
 * @LastEditors: liu.yongli
 * @LastEditTime: 2023-12-13 14:36:14
-->
<template>
  <i class='icon'>
    <svg width="58px" height="58px" viewBox="0 0 58 58" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
      <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
          <stop stop-color="#FFFFFF" offset="0%"></stop>
          <stop stop-color="#BCDBFF" offset="100%"></stop>
        </linearGradient>
        <path d="M31.3323885,4.44444444 L12.1757051,4.44444444 C8.50301282,4.84324903 6.66666667,7.25959751 6.66666667,11.6934899 C6.66666667,16.1273822 8.50301282,18.7984934 12.1757051,19.7068233 L31.3323885,19.7068233 C34.888574,20.9915777 36.6666667,23.5912721 36.6666667,27.5059067 C36.6666667,31.4205412 34.888574,33.7333872 31.3323885,34.4444444 L8.63515931,34.4444444" id="path-2"></path>
        <filter x="-50.6%" y="-50.6%" width="201.1%" height="201.1%" filterUnits="objectBoundingBox" id="filter-3">
          <feMorphology radius="1.66666667" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
          <feOffset dx="0" dy="0" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
          <feMorphology radius="1.66666667" operator="erode" in="SourceAlpha" result="shadowInner"></feMorphology>
          <feOffset dx="0" dy="0" in="shadowInner" result="shadowInner"></feOffset>
          <feComposite in="shadowOffsetOuter1" in2="shadowInner" operator="out" result="shadowOffsetOuter1"></feComposite>
          <feGaussianBlur stdDeviation="4.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
          <feColorMatrix values="0 0 0 0 0.309803922   0 0 0 0 0.623529412   0 0 0 0 1  0 0 0 0.7 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <ellipse id="path-4" cx="35" cy="4.92753623" rx="5" ry="4.92753623"></ellipse>
        <filter x="-135.0%" y="-137.0%" width="370.0%" height="374.0%" filterUnits="objectBoundingBox" id="filter-5">
          <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
          <feGaussianBlur stdDeviation="4.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
          <feColorMatrix values="0 0 0 0 0.309803922   0 0 0 0 0.623529412   0 0 0 0 1  0 0 0 0.7 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <ellipse id="path-6" cx="5" cy="35.0724638" rx="5" ry="4.92753623"></ellipse>
        <filter x="-135.0%" y="-137.0%" width="370.0%" height="374.0%" filterUnits="objectBoundingBox" id="filter-7">
          <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
          <feGaussianBlur stdDeviation="4.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
          <feColorMatrix values="0 0 0 0 0.309803922   0 0 0 0 0.623529412   0 0 0 0 1  0 0 0 0.7 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
      </defs>
      <g id="综合监测-大屏" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="2-1综合监测-过车信息" transform="translate(-201.000000, -239.000000)">
          <g id="编组-6" transform="translate(198.000000, 199.000000)">
            <g id="icon_线路_40" transform="translate(12.000000, 49.000000)">
              <g id="路径-3" stroke-dasharray="8.333333333333334,3.333333333333333">
                <use fill="black" fill-opacity="1" filter="url(#filter-3)" xlink:href="#path-2"></use>
                <use stroke="url(#linearGradient-1)" stroke-width="3.33333333" xlink:href="#path-2"></use>
              </g>
              <g id="椭圆形">
                <use fill="black" fill-opacity="1" filter="url(#filter-5)" xlink:href="#path-4"></use>
                <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-4"></use>
              </g>
              <g id="椭圆形">
                <use fill="black" fill-opacity="1" filter="url(#filter-7)" xlink:href="#path-6"></use>
                <use fill="#BCDBFF" fill-rule="evenodd" xlink:href="#path-6"></use>
              </g>
            </g>
          </g>
        </g>
      </g>
    </svg>
  </i>
</template>

<script>

/**
 * 组件说明：
 * 此组件是一个基本的Vue组件示例，目前尚未实现具体的业务逻辑。
 * 它展示了如何定义一个基本的Vue组件，包括props、data和methods的声明。
 *
 * @export
 * @returns {Object} 返回一个Vue组件定义对象
 */
export default {
  // 定义组件接收的外部属性，目前未定义任何props
  props: {
  },
  // 定义组件内部的状态，目前未定义任何状态
  data() {
    return {};
  },
  // 定义组件的方法，目前未定义任何方法
  methods: {}
};
</script>

<style lang='less' src='./index.less' />
