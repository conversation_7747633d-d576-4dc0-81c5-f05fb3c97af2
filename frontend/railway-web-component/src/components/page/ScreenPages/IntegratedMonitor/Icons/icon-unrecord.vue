<!--
 * @Description:
 * @Autor: he.chao
 * @Date: 2022-10-19 15:07:55
 * @LastEditors: liu.yongli
 * @LastEditTime: 2023-12-13 14:36:14
-->
<template>

  <i class='icon'>
    <svg width='82px' height='82px' viewBox='0 0 82 82' version='1.1' xmlns='http://www.w3.org/2000/svg'
         >
      <defs>
        <linearGradient x1='50%' y1='0.330528846%' x2='50%' y2='98.7703142%' id='linearGradient-10'>
          <stop stop-color='#07C0ED' offset='0%'></stop>
          <stop stop-color='#0E82E3' offset='100%'></stop>
        </linearGradient>
        <linearGradient x1='50%' y1='0%' x2='50%' y2='100%' id='linearGradient-2'>
          <stop stop-color='#0095FF' offset='0%'></stop>
          <stop stop-color='#00FFF6' offset='100%'></stop>
        </linearGradient>
        <linearGradient x1='50%' y1='0%' x2='50%' y2='100%' id='linearGradient-3'>
          <stop stop-color='#FFFFFF' offset='0%'></stop>
          <stop stop-color='#FFFFFF' offset='100%'></stop>
        </linearGradient>
      </defs>
      <g id='综合监测-大屏' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'>
        <g id='1-7综合监测-云广播' transform='translate(-143.000000, -911.000000)'>
          <g id='云广播喊话' transform='translate(-4.000000, 721.000000)'>
            <g id='编组-40' transform='translate(116.000000, 191.000000)'>
              <g id='icon_录音_80_n' transform='translate(32.000000, 0.000000)'>
                <circle id='椭圆形' fill='url(#linearGradient-10)' cx='40' cy='40' r='33.7777778'></circle>
                <circle id='椭圆形' stroke='#4F9FFF' stroke-width='0.888888889' cx='40' cy='40' r='40'></circle>
                <g id='语音' transform='translate(27.555556, 22.222222)' fill='url(#linearGradient-3)'
                   fill-rule='nonzero'>
                  <path
                    d='M12.4444444,22.3577533 L12.4444444,22.3577533 C16.2871833,22.3577533 19.4312423,19.2136942 19.4312423,15.3709554 L19.4312423,6.9867979 C19.4312423,3.14405906 16.2871833,0 12.4444444,0 L12.4444444,0 C8.6017056,0 5.45764654,3.14405906 5.45764654,6.9867979 L5.45764654,15.3709554 C5.45764654,19.2136942 8.6017056,22.3577533 12.4444444,22.3577533 Z M24.8722112,17.3097918 C24.9901134,16.5456108 24.4704703,15.8338308 23.7062893,15.7159285 C22.9421083,15.6023931 22.2303283,16.1220362 22.112426,16.8818504 C21.3788123,21.597939 17.2260343,25.1524725 12.4444444,25.1524725 C7.66285463,25.1524725 3.50570987,21.5935723 2.77646284,16.8774837 C2.65856063,16.1133027 1.94241384,15.5936596 1.18259957,15.7115618 C0.418418551,15.829464 -0.101224544,16.541244 0.016677671,17.3054251 C0.898760907,23.0040321 5.43144605,27.2528786 11.0470849,27.8642234 L11.0470849,32.1392704 L6.85500612,32.1392704 C6.0820916,32.1392704 5.45764654,32.7637154 5.45764654,33.5366299 C5.45764654,34.3095445 6.0820916,34.9339895 6.85500612,34.9339895 L18.0338828,34.9339895 C18.8067973,34.9339895 19.4312423,34.3095445 19.4312423,33.5366299 C19.4312423,32.7637154 18.8067973,32.1392704 18.0338828,32.1392704 L13.841804,32.1392704 L13.841804,27.8642234 C19.4530761,27.2528786 23.990128,23.0083989 24.8722112,17.3097918 Z'
                    id='形状'></path>
                </g>
              </g>
            </g>
          </g>
        </g>
      </g>
    </svg>
  </i>
</template>

<script>

/**
 * 组件说明：
 * 此组件是一个基本的Vue组件示例，目前尚未实现具体的业务逻辑。
 * 它展示了如何定义一个基本的Vue组件，包括props、data和methods的声明。
 *
 * @export
 * @returns {Object} 返回一个Vue组件定义对象
 */
export default {
  // 定义组件接收的外部属性，目前未定义任何props
  props: {
  },
  // 定义组件内部的状态，目前未定义任何状态
  data() {
    return {};
  },
  // 定义组件的方法，目前未定义任何方法
  methods: {}
};
</script>

<style scoped lang='less' src='./index.less' />
