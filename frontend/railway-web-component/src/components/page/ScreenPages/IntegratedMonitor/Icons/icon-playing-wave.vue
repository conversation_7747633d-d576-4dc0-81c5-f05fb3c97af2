<!--
 * @Description:
 * @Autor: he.chao
 * @Date: 2022-10-19 15:07:55
 * @LastEditors: liu.yongli
 * @LastEditTime: 2023-12-13 14:36:14
-->
<template>
    <i class='icon wave-wrap'>
      <svg width='100%' height='100%' viewBox='0 0 11 14'>
        <g id='素材库' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'>
          <g id='广播配置/3.13素材库_我的素材编辑' transform='translate(-1527.000000, -661.000000)' fill='#FFFFFF'
             fill-rule='nonzero'>
            <g id='弹框' transform='translate(990.000000, 251.000000)'>
              <g id='新增' transform='translate(526.000000, 401.000000)'>
                <g id='语音' transform='translate(11.000000, 9.000000)'>
                  <path
                    d='M4.81544651,2.81813084 C4.52781547,2.56657943 4.0927847,2.56657943 3.80515367,2.81813084 C3.54318779,3.09760213 3.54318779,3.52651476 3.80515367,3.80598605 C5.66747141,5.63645144 5.66747141,8.59415066 3.80515367,10.424616 C3.54788865,10.7058591 3.54788865,11.1312282 3.80515367,11.4124713 C4.09097273,11.6686191 4.52962745,11.6686191 4.81544651,11.4124713 C7.18928294,9.01784751 7.18928294,5.2127546 4.81544651,2.81813084 L4.81544651,2.81813084 Z M0.370157975,6.18671714 C0.133242966,6.41668922 0,6.7296091 0,7.05602974 C0,7.38245038 0.133242966,7.69537026 0.370157975,7.92534233 C0.730457759,8.28074281 1.27450466,8.38837658 1.74776465,8.19788685 C2.22102464,8.00739711 2.52991147,7.55645285 2.52991147,7.05602974 C2.52991147,6.55560662 2.22102464,6.10466236 1.74776465,5.91417262 C1.27450466,5.72368289 0.730457759,5.83131666 0.370157975,6.18671714 L0.370157975,6.18671714 Z'
                    id='形状' class='svg-elem-1'></path>
                  <path
                    d='M8.13930999,0.220071592 C8.00034096,0.0794265054 7.80891182,0 7.60890624,0 C7.40890067,0 7.21747153,0.0794265054 7.07850249,0.220071592 C6.81162817,0.497658689 6.81162817,0.930339727 7.07850249,1.20792682 C10.3297617,4.39318883 10.3297617,9.55093527 7.07850249,12.7361973 C6.78303894,13.0254586 6.78303894,13.4940625 7.07850249,13.7833238 C7.37433397,14.0722254 7.85358143,14.0722254 8.14941291,13.7833238 C11.9541307,10.0259584 11.9496213,3.97201391 8.13930999,0.220071592 L8.13930999,0.220071592 Z'
                    id='路径' class='svg-elem-2'></path>
                </g>
              </g>
            </g>
          </g>
        </g>
      </svg>
    </i>
  </template>

  <script>

  /**
   * 组件说明：
   * 此组件是一个基本的Vue组件示例，目前尚未实现具体的业务逻辑。
   * 它展示了如何定义一个基本的Vue组件，包括props、data和methods的声明。
   *
   * @export
   * @returns {Object} 返回一个Vue组件定义对象
   */
  export default {
    // 定义组件接收的外部属性，目前未定义任何props
    props: {},
    // 定义组件内部的状态，目前未定义任何状态
    data() {
      return {};
    },
    // 定义组件的方法，目前未定义任何方法
    methods: {}
  };
  </script>

  <style lang='less'>
  /***************************************************
  * Generated by SVG Artista on 7/15/2024, 1:47:02 PM
  * MIT license (https://opensource.org/licenses/MIT)
  * W. https://svgartista.net
  **************************************************/
  .icon.wave-wrap {
    svg path {
      fill: rgb(255, 255, 255);
    }

    &.active svg path:nth-child(1) {
      animation: wave 2s linear infinite;
    }

    &.active svg path:nth-child(2) {
      animation: wave1 2s linear infinite;
      animation-delay: 0.5s;
    }
  }

  @keyframes wave {
    0% {
      opacity: 0.5;
    }
    50% {
      opacity: 1;
    }
    100% {
      opacity: 1;
    }
  }

  @keyframes wave1 {
    0% {
      opacity: 0;
    }
    50% {
      opacity: 0.6;
    }
    100% {
      opacity: 0;
    }
  }
  </style>
