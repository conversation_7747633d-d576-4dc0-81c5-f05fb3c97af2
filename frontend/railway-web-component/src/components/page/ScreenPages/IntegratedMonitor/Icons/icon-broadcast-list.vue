<!--
 * @Description:
 * @Autor: he.chao
 * @Date: 2022-10-19 15:07:55
 * @LastEditors: liu.yongli
 * @LastEditTime: 2023-12-13 14:36:14
-->
<template>

  <i class='icon icon-broadcast-list'  title='播放列表'>
    <svg width='18px' height='18px' viewBox='0 0 18 18' fill='#4F9FFF' version='1.1' xmlns='http://www.w3.org/2000/svg'>
      <g id='综合监测-大屏' stroke='none' stroke-width='1' fill-rule='evenodd'>
        <g id='1-7综合监测-云广播' transform='translate(-24.000000, -440.000000)' fill-rule='nonzero'>
          <g id='告警详情' transform='translate(12.000000, 92.000000)'>
            <g id='icon_播放列表_18_s' transform='translate(12.000000, 348.000000)'>
              <path
                d='M13.4998758,3.85681676 C13.4998758,3.68620743 13.4320829,3.52258607 13.3114427,3.40194871 C13.1908025,3.28131136 13.0271799,3.21354505 12.8565713,3.21354505 L3.85742084,3.21354505 C3.51418249,3.22955887 3.24413074,3.51250474 3.24413074,3.8561178 C3.24413074,4.19973086 3.51418249,4.48267673 3.85742084,4.49869056 L12.8574029,4.49869056 C13.2118146,4.49825268 13.4991059,4.21122928 13.4998758,3.85681676 L13.4998758,3.85681676 Z M8.35669524,7.71485449 C8.35669524,7.35958533 8.06869382,7.07158277 7.71342607,7.07158277 L3.85740314,7.07158277 C3.5141648,7.0875966 3.24411304,7.37054247 3.24411304,7.71415553 C3.24411304,8.05776859 3.5141648,8.34071446 3.85740314,8.35672829 L7.71482398,8.35672829 C8.06900074,8.35595944 8.3559264,8.06903264 8.35669524,7.71485449 L8.35669524,7.71485449 Z M3.85740314,10.9298328 C3.5141648,10.9458467 3.24411304,11.2287925 3.24411304,11.5724056 C3.24411304,11.9160187 3.5141648,12.1989645 3.85740314,12.2149784 L5.78501647,12.2149784 C6.02173488,12.2260225 6.24535477,12.1060216 6.36701759,11.9026597 C6.48868041,11.6992977 6.48868041,11.4455134 6.36701759,11.2421515 C6.24535477,11.0387896 6.02173488,10.9187887 5.78501647,10.9298328 L3.85740314,10.9298328 Z'
                id='形状'></path>
              <path
                d='M6.42989589,16.7148722 L2.55499232,16.7148722 C1.85402648,16.7139947 1.28600018,16.1459662 1.28512274,15.4449976 L1.28512274,2.55500242 C1.28600018,1.85403381 1.85402648,1.28600526 2.55499232,1.28512782 L14.1576019,1.28512782 C14.8585884,1.28602449 15.4266541,1.85401326 15.4276484,2.55500242 L15.4276484,6.42992132 C15.4276484,6.78519536 15.7156538,7.07320188 16.0709264,7.07320188 C16.4261991,7.07320188 16.7142045,6.78519536 16.7142045,6.42992132 L16.7142045,2.55500242 C16.7125468,1.14460073 15.5696082,0.00165763445 14.1592121,0 L2.55499232,0 C1.14459621,0.00165763445 0.00165762789,1.14460073 0,2.55500242 L0,15.4449976 C0.00165762789,16.8553993 1.14459621,17.9983424 2.55499232,18 L6.42989589,18 C6.77305638,17.9838886 7.04299695,17.7009759 7.04299695,17.3574361 C7.04299695,17.0138962 6.77305638,16.7309836 6.42989589,16.7148722 L6.42989589,16.7148722 Z'
                id='路径'></path>
              <path
                d='M16.4934761,9.22154325 C14.7588868,7.48758042 12.0410163,7.22033915 10.0019223,8.58324533 C7.96282821,9.94615152 7.1703892,12.5596419 8.10920498,14.8254902 C9.04802077,17.0913385 11.4566353,18.3785162 13.8621101,17.8998762 C16.2675848,17.4212362 17.9999734,15.3100777 17.9999734,12.8574361 C18.0043756,11.4927426 17.4617835,10.1831809 16.4934761,9.22152556 L16.4934761,9.22154325 Z M12.8573852,16.7148722 C10.7301714,16.7148722 8.99996441,14.984853 8.99996441,12.8574361 C8.99996441,10.7300192 10.7301714,9 12.8573852,9 C14.9845991,9 16.7148061,10.7302139 16.7148061,12.8574361 C16.7148061,14.9846583 14.9848114,16.7148722 12.8573852,16.7148722 L12.8573852,16.7148722 Z'
                id='形状'></path>
              <path
                d='M11.6154681,13.9637464 L11.6154681,14.707749 L11.9232013,14.707749 L11.9232013,14.0302466 L11.6154681,13.9637464 L11.6154681,13.9637464 Z M13.7693338,14.123497 L14.6922668,14.5617485 L14.6922668,11.3479873 L13.7693338,11.7862389 L13.7693338,14.123497 Z M12.2306678,14.0909969 L12.2306678,14.8539995 C12.2309516,14.8924994 12.2148915,14.9295244 12.1860281,14.9569122 C12.1571647,14.9843 12.117868,14.9998018 12.0768012,15 L11.4616015,15 C11.4205347,14.9998018 11.381238,14.9843 11.3523746,14.9569122 C11.3235112,14.9295244 11.3074511,14.8924994 11.3077349,14.8539995 L11.3077349,13.8964962 L11.120535,13.856746 C11.0497971,13.8414539 10.9997641,13.7823024 11.0000008,13.7142455 L11.0000008,12.1954903 C11.0000008,12.12699 11.050135,12.0679898 11.120535,12.0529898 L13.7693338,11.4939879 L14.7773334,11.0154862 C14.8249114,10.9927987 14.8816966,10.995171 14.9269334,11.0217362 C14.9722667,11.0484863 15.0000008,11.0954865 15.0000008,11.1459866 L15.0000008,14.7634992 C15.0000008,14.8142494 14.9722667,14.8612495 14.9269334,14.8879996 C14.8816966,14.9145648 14.8249114,14.9169372 14.7773334,14.8942496 L13.7693338,14.415748 L12.2306678,14.0909969 Z M11.3077349,12.3064907 L11.3077349,13.6029952 L13.4616006,14.0594967 L13.4616006,11.8499891 L11.3077349,12.3064907 Z'
                id='形状'  stroke-width='0.5'></path>
            </g>
          </g>
        </g>
      </g>
    </svg>
  </i>
</template>

<script>
/**
 * 组件说明：
 * 此组件是一个基本的Vue组件示例，目前尚未实现具体的业务逻辑。
 * 它展示了如何定义一个基本的Vue组件，包括props、data和methods的声明。
 *
 * @export
 * @returns {Object} 返回一个Vue组件定义对象
 */
export default {
  // 定义组件接收的外部属性，目前未定义任何props
  props: {
  },
  // 定义组件内部的状态，目前未定义任何状态
  data() {
    return {};
  },
  // 定义组件的方法，目前未定义任何方法
  methods: {}
};
</script>

<style lang='less' src='./index.less' />
