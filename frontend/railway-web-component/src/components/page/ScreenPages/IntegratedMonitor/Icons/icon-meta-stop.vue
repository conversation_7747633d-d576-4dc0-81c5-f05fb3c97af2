<!--
 * @Description:
 * @Autor: he.chao
 * @Date: 2022-10-19 15:07:55
 * @LastEditors: liu.yongli
 * @LastEditTime: 2023-12-13 14:36:14
-->
<template>
  <!-- 定义一个图标元素，绑定点击事件 -->
  <i class='icon' @click='click'>
    <!-- SVG图标定义，设置宽度、高度、视图框和填充颜色 -->
    <svg width="17px" height="20px" viewBox="0 0 17 20" fill="#fefefe" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <!-- 定义图标的不同层级和变换 -->
      <g id="综合监测-大屏" stroke="none" stroke-width="1" fill-rule="evenodd">
        <g id="1-6综合监测-弹框" transform="translate(-1151.000000, -393.000000)" fill-rule="nonzero">
          <g id="云广播喊话" transform="translate(816.000000, 116.000000)">
            <g id="编组-7" transform="translate(24.000000, 277.000000)">
              <g id="icon_素材播放_18_n" transform="translate(311.000000, 1.000000)">
                <!-- 定义路径，描述图标的形状 -->
                <path d="M9.51647876,11.52 L9.51647876,11.52 C11.5271052,11.52 13.1721632,9.9 13.1721632,7.92 L13.1721632,3.6 C13.1721632,1.62 11.5271052,0 9.51647876,0 L9.51647876,0 C7.50585231,0 5.86079431,1.62 5.86079431,3.6 L5.86079431,7.92 C5.86079431,9.9 7.50585231,11.52 9.51647876,11.52 Z M16.0190275,8.919 C16.0807172,8.52525 15.8088256,8.1585 15.4089851,8.09775 C15.0091447,8.03925 14.6367218,8.307 14.5750321,8.6985 C14.1911853,11.1285 12.0183378,12.96 9.51647876,12.96 C7.01461971,12.96 4.83948746,11.12625 4.4579254,8.69625 C4.39623572,8.3025 4.02152806,8.03475 3.62397238,8.0955 C3.22413189,8.15625 2.95224036,8.523 3.01393004,8.91675 C3.4754602,11.853 5.84708549,14.04225 8.78534187,14.35725 L8.78534187,16.56 L6.5919312,16.56 C6.1875211,16.56 5.86079431,16.88175 5.86079431,17.28 C5.86079431,17.67825 6.1875211,18 6.5919312,18 L12.4410263,18 C12.8454364,18 13.1721632,17.67825 13.1721632,17.28 C13.1721632,16.88175 12.8454364,16.56 12.4410263,16.56 L10.2476157,16.56 L10.2476157,14.35725 C13.1835872,14.04225 15.5574973,11.85525 16.0190275,8.919 Z" id="形状结合"></path>
                <!-- 定义另一个路径，描述图标的形状 -->
                <path d="M-1.50954034,7.47574104 L18.510405,7.49308936 C19.0620129,7.49356735 19.5090601,7.94061458 19.5095381,8.49222243 L19.5095403,8.49482171 C19.5100185,9.04662829 19.0630785,9.49434354 18.5112719,9.49482171 C18.5106947,9.49482221 18.5101175,9.49482221 18.5095403,9.49482171 L-1.51040501,9.47747339 C-2.06201286,9.4769954 -2.50906009,9.02994816 -2.50953808,8.47834032 L-2.50954034,8.47574104 C-2.51001851,7.92393446 -2.06307852,7.47621921 -1.51127194,7.47574104 C-1.51069474,7.47574054 -1.51011754,7.47574054 -1.50954034,7.47574104 Z" id="矩形" transform="translate(8.500000, 8.485281) rotate(-45.000000) translate(-8.500000, -8.485281)"></path>
              </g>
            </g>
          </g>
        </g>
      </g>
    </svg>
  </i>
</template>
<script>
/**
 * 组件说明：这里写组件的功能描述
 *
 * @props {Function} click - 组件点击事件的回调函数。默认情况下，点击事件会在控制台输出'click'。
 */
export default {
  props: {
    // 定义组件的属性，click是一个可选的函数类型属性，默认值为一个输出'click'的函数
    click: {
      required: false,
      type: Function,
      default: () => {
        console.log('click');
      }
    }
  },
  data() {
    // 初始化数据，当前没有定义任何数据
    return {};
  },
  methods: {
    // 方法列表，当前没有定义任何方法
  }
};
</script>
<!-- 引入样式文件，使用Less预处理器，并且样式作用域限定在当前组件 -->
<style scoped lang='less' src='./index.less' />
