<!--
 * @Description  : 告警事件
 * <AUTHOR> wnj
 * @Date         : 2023-12-08 09:52:58
 * @LastEditors: liu.yongli
 * @LastEditTime: 2025-07-08 09:46:04
 * @FilePath     :  / src / components / page / alarmEvent / components / alarmRight / alarmList.vue
-->
<template>
  <div class="topContent">
    <div class="titleWrap">
      <div class="title">
        <div class="titlName">今日重点关注事件</div>
        <div class="unit">（起）</div>
      </div>
    </div>
    <div class="eventIndex">
      <div class="eventIndexItem todayItem">
        <img
          alt="今日新增"
          class="indexIcon"
          :src="require(`@/assets/images/alarmEvent/alarm/todayAdd_event.svg`)"
        />
        <span class="indexKey">新增</span>
        <FlipCounter class="indexValue" :value="eventCount.today" />
      </div>
      <div class="eventIndexItem pendingItem">
        <img
          alt="未消散"
          class="indexIcon"
          :src="require(`@/assets/images/alarmEvent/alarm/icon_未消散_14_n.svg`)"
        />
        <span class="indexKey">未消散</span>
        <FlipCounter class="indexValue" :value="eventCount.pending" />
      </div>
      <div class="eventIndexItem finishedItem">
        <img
          alt="已消散"
          class="indexIcon"
          :src="require(`@/assets/images/alarmEvent/alarm/icon_已消散_14_n.svg`)"
        />
        <span class="indexKey">已消散</span>
        <FlipCounter class="indexValue" :value="eventCount.finished" />
      </div>
    </div>
  </div>
</template>

<script>
import api from '@/api'
// import { Events } from '@/components/page/ScreenPages/IntegratedMonitor/enum/EventsEnum'
import dayjs from 'dayjs'
import FlipCounter from '@/components/common/FlipCounter/index.vue'
import { getEvtParams } from '@/components/common/utils'

// const { EVT_LIST } = Events
export default {
  name: 'AlarmList',
  inject: ['mapRef'],
  props: {
    provinceId: {
      // 省份编码
      type: String,
      default: '',
    },
    cityId: {
      // 地市编码
      type: String,
      default: '',
    },
  },
  components: {
    // EventListItem,
    FlipCounter,
  },
  computed: {
    dataParam() {
      // 使用计算属性承载参数。省市区编码任意一个更新才处理
      const { provinceId, cityId } = this
      return {
        provinceId,
        cityId,
      }
    },
    selectedRegion() {
      return this.$store.state.map.selectedRegion //需要监听的属性
    },
  },
  watch: {
    dataParam: {
      handler(val) {
        // 没有就不处理
        if (val?.provinceId || val?.cityId) {
          this.refreshList()
        }
      },
      immediate: true,
    },
    selectedRegion: {
      handler: function (newVal, oldVal) {
        if (newVal) {
          this.refreshList()
        }
      },
    },
  },
  data: function () {
    return {
      filterState: '', // 工具箱事件筛选的状态
      eventCount: {
        today: 0,
        pending: 0,
        finished: 0,
      },
      timer: null, // 监听新事件的定时器
    }
  },
  // created() {},
  mounted() {
    this.getAlarmCount()
    // this.$EventBus.$on('refreshAlarmList', this.refreshList)
    // 事件筛选过滤条件回调处理
    // this.$EventBus.$on('onEventFilterChange', this.onEventFilterChange)

    // 监听新事件提示
    this.timer = setInterval(() => {
      this.refreshList()
    }, 60 * 1000) // 60秒执行一次
  },
  beforeDestroy() {
    // this.$EventBus.$off('refreshAlarmList', this.refreshList)
    // this.$EventBus.$off('onEventFilterChange', this.onEventFilterChange)
    this.timer && clearInterval(this.timer)
  },
  methods: {
    /**
     * 事件筛选过滤条件回调处理
     */
    onEventFilterChange(e) {
      this.filterState = e.filterState || ''
      this.getAlarmCount()
    },
    /**
     * 获取告警数量
     * @returns {void}
     */
    async getAlarmCount() {
      const url = `${this.$env.VUE_APP_REQ_PREFIX_BIZ}/fusionEvent/count`
      const params = {
        ...getEvtParams(),
        // .subtract(1, 'day')
        //   .startOf('days')
        startDate: dayjs().format('YYYY-MM-DD 00:00:00'),
        endDate: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        isFocus: 'Y',
      }
      // 今日新增
      const res1 = await api.post(url, {
        ...params,
      })
      // 未消散
      const res2 = await api.post(url, {
        ...params,
        fusionStatusList: ['1', '2'],
      })
      // 已消散
      const res3 = await api.post(url, {
        ...params,
        fusionStatusList: ['3'],
      })
      console.log('统计--', res1, res2, res3)
      this.eventCount = {
        today: res1.data || 0,
        pending: res2.data || 0,
        finished: res3.data || 0,
      }
    },
    /**
     * 刷新
     * 重置关键字值、页码，并重新获取告警计数和列表。
     */
    /**
     * 刷新列表
     */
    refreshList() {
      // 重新获取告警计数
      this.getAlarmCount()
    },
  },
}
</script>

<style lang='less' src='./index.less' />
