 .topContent {
    width: 366px;
    height: 108px;
    background: #172537;
    border-radius: 8px;
    padding: 12px 0;
    box-sizing: border-box;

    .titleWrap {
      padding: 0 12px 0 6px;
      height: 34px;
      width: 100%;
      box-sizing: border-box;

      .title {
        width: 100%;
        height: 100%;
        background: url("~@/assets/images/alarmEvent/alarm/event_index_bg.svg")
          no-repeat 100% 100%;
        background-size: 100%;
        display: flex;

        &::before {
          content: "";
          width: 21px;
          height: 23px;
          margin-left: 21px;
          margin-top: 2px;
          background: url("~@/assets/images/alarmEvent/alarm/event_index_title_icon.svg")
            no-repeat 100% 100%;
          background-size: 100%;
        }

        .titlName {
          font-family: PingFangSC-Medium;
          font-size: 14px;
          color: #e8f3ff;
          letter-spacing: 0;
          line-height: 20px;
          font-weight: 500;
          margin-left: 24px;
          margin-top: 4px;
        }

        .unit {
          font-family: PingFangSC-Regular;
          font-size: 14px;
          color: rgba(232, 243, 255, 0.6);
          letter-spacing: 0;
          line-height: 20px;
          font-weight: 400;
          margin-top: 4px;
        }
      }
    }

    .eventIndex {
      margin-top: 10px;
      padding: 0 12px;
      display: flex;
      justify-content: space-between;
      position: relative;

      &::after {
        content: "";
        position: absolute;
        top: 23px;
        left: 12px;
        width: calc(100% - 24px);
        height: 19px;
        background: url("~@/assets/images/alarmEvent/alarm/event_index_top_bg.png")
          no-repeat 100% 100%;
        background-size: 100%;
      }

      .eventIndexItem {
        width: 104px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;

        .indexKey {
          font-family: PingFangSC-Regular;
          font-size: 12px;
          color: #e8f3ff;
          letter-spacing: 0;
          font-weight: 400;
        }

        .indexValue {
          font-family: DINAlternate-Bold;
          font-size: 20px;
          color: #e8f3ff;
          letter-spacing: 0;
          font-weight: 700;
          margin-left: 6px;
        }
      }

      .todayItem {
        background-image: linear-gradient(
          90deg,
          rgba(237, 81, 88, 0) 0%,
          rgba(237, 81, 88, 0.5) 47%,
          rgba(237, 81, 88, 0) 100%
        );

        .indexIcon {
          width: 16px;
          height: 14px;
          margin-right: 6px;
        }
      }

      .pendingItem {
        // background-image: linear-gradient(
        //   90deg,
        //   rgba(58, 119, 229, 0) 0%,
        //   rgba(58, 119, 229, 0.5) 49%,
        //   rgba(58, 119, 229, 0) 100%
        // );
        background-image: linear-gradient(
          90deg,
          rgba(229, 132, 58, 0) 0%,
          rgba(229, 132, 58, 0.5) 49%,
          rgba(229, 132, 58, 0) 100%
        );

        .indexIcon {
          width: 9px;
          height: 14px;
          margin-right: 9px;
        }
      }

      .finishedItem {
        // background-image: linear-gradient(
        //   90deg,
        //   rgba(21, 189, 148, 0) 0%,
        //   rgba(21, 189, 148, 0.5) 49%,
        //   rgba(21, 189, 148, 0) 100%
        // );
        background-image: linear-gradient(
          90deg,
          rgba(21, 189, 148, 0) 0%,
          rgba(21, 189, 148, 0.5) 49%,
          rgba(21, 189, 148, 0) 100%
        );

        .indexIcon {
          width: 16px;
          height: 14px;
          margin-right: 6px;
        }
      }
    }
  }