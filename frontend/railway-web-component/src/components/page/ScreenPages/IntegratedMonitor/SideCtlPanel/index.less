@import "../common.less";
.ctl-panel-wrap {
  position: absolute;
  width: @width;
  height: @height;
  z-index: 99;
  top: 40px;
  left: calc(12px - @width);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: left 1s ease-in-out;

  &:hover,
  &.active {
    left: 0;
    transition: left 1s ease-in-out;

    .expand-ctl {
      background: url("./img/celan_shouqi_new.png") 100% 100% no-repeat;
      background-size: 100% 100%;
    }
  }

  .expand-ctl {
    background: url("./img/celan_zhankai_new.png") 100% 100% no-repeat;
    background-size: 100% 100%;
    position: absolute;
    right: -15px;
    width: 30px;
    height: 60px;
    cursor: pointer;
    top: 10px;
  }
  .ctl-panel-container {
    height: 100%;
    width: 70px;
    background: #172537;
    border-radius: 8px;
    overflow-y: scroll;
    padding: 20px 0;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-evenly;

    .ctl-panel-item {
      display: flex;
      align-items: center;
      flex-direction: column;
      justify-content: space-around;

      &:not(:first-child) {
        margin-top: 40px;
      }

      .item-icon {
        width: 40px;
        height: 40px;
        background: url("../../../../../assets/images/comm/ctl_icon_unselected.svg")
          100% no-repeat;
        background-size: 100% 100%;
        display: flex;
        align-items: center;
        justify-content: center;

        .iconfont {
          width: 24px;
          height: 24px;
          &.icon-LOUDSPEAKER {
            background: url(./img/icon_LOUDSPEAKER.svg) 100% no-repeat;
            background-size: 100% 100%;
          }
          &.icon-CAMERA {
            background: url(./img/icon_CAMERA.svg) 100% no-repeat;
            background-size: 100% 100%;
          }
          &.icon-BRIDGE {
            background: url(./img/icon_BRIDGE.svg) 100% no-repeat;
            background-size: 100% 100%;
          }

          &.icon-ALARM_EVENT {
            background: url(./img/icon_ALARM_EVENT.svg) 100% no-repeat;
            background-size: 100% 100%;
          }
          &.icon-HIDDEN_DANGER_EVENT {
            background: url(./img/icon_HIDDEN_DANGER_EVENT.svg) 100% no-repeat;
            background-size: 100% 100%;
          }
          &.icon-RAILWAY_LINE {
            background: url(./img/icon_RAILWAY_LINE.svg) 100% no-repeat;
            background-size: 100% 100%;
          }
          &.icon-PASSING_PIC {
            background: url(./img/icon_PASSING_PIC.svg) 100% no-repeat;
            background-size: 100% 100%;
          }
          &.icon-TUNNEL {
            background: url(./img/icon_TUNNEL.svg) 100% no-repeat;
            background-size: 100% 100%;
          }
        }
      }

      .item-text {
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: rgba(255, 255, 255, 0.65);
        letter-spacing: 0;
        font-weight: 400;
        margin-top: 9px;
      }

      &.selected {
        .item-icon {
          background: url("../../../../../assets/images/comm/ctl_icon_selected.svg")
            100% no-repeat;
          background-size: 100% 100%;
        }
      }
    }
  }
}
