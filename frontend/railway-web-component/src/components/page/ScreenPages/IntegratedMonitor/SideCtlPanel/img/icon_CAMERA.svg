<?xml version="1.0" encoding="UTF-8"?>
<svg viewBox="0 0 26 26" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>icon_摄像机_18_n</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#BCDBFF" offset="100%"></stop>
        </linearGradient>
        <path d="M13.912733,23.492 L13.912733,20.792 L12,20.4307143 L12,29 L13.912733,28.2684286 L13.912733,25.3292857 L16.9489395,25.3292857 L18.2986503,23.1242857 L16.6108691,22.0224286 L15.8216096,23.492 L13.912733,23.492 Z M30,20.792 L16.2740841,11 L14.7019924,11 L12.5643076,14.6745714 L12.5643076,15.8985714 L24.8209669,24.4717143 L25.8338927,24.3431428 L30,20.792 Z M12.5591659,16.634 L12.5591659,18.5857143 L24.9340856,27.0367143 L26.0588445,27.0367143 L26.6218667,26.4285714 L27.1848889,24.5912857 C27.1848889,24.5912857 25.6102264,25.8165714 25.4971078,25.8165714 C24.935371,25.3292857 12.5617368,16.634 12.5617368,16.634 L12.5591659,16.634 Z" id="path-2"></path>
        <filter x="-33.3%" y="-33.3%" width="166.7%" height="166.7%" filterUnits="objectBoundingBox" id="filter-3">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.309803922   0 0 0 0 0.623529412   0 0 0 0 1  0 0 0 0.7 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="综合监测-大屏" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="1-1综合监测-宏观" transform="translate(-35.000000, -270.000000)" fill-rule="nonzero">
            <g id="左侧图层" transform="translate(12.000000, 112.000000)">
                <g id="icon_摄像机_18_n" transform="translate(15.000000, 151.000000)">
                    <use fill="black" fill-opacity="1" filter="url(#filter-3)" xlink:href="#path-2"></use>
                    <use fill="url(#linearGradient-1)" xlink:href="#path-2"></use>
                </g>
            </g>
        </g>
    </g>
</svg>
