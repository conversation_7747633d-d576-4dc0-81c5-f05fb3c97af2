<?xml version="1.0" encoding="UTF-8"?>
<svg viewBox="0 0 25 28" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>icon_云广播_18_s</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#BCDBFF" offset="100%"></stop>
        </linearGradient>
        <path d="M26.0822772,11.5862633 L26.2893024,11.8064132 L26.4023354,11.9337451 C27.1008858,12.734675 27.7290094,13.7497606 28.2021414,14.9115034 C29.5748827,18.2752047 29.1317329,21.5970026 27.2150901,22.3291695 L27.0297952,22.387816 C26.8191593,22.4471829 26.6000925,22.4739783 26.3760517,22.4698752 C26.0464153,22.4908457 25.673241,22.4927051 25.3001232,22.4881995 L24.0636684,22.4666804 L24.0636684,22.4666804 L23.6368321,22.4716411 C23.5283105,22.4811759 23.4110909,22.4660997 23.2872268,22.4288168 C22.2199993,22.4659411 21.2034214,22.563326 20.2285782,22.7033502 C21.1786251,23.8972194 22.0490651,25.2058049 22.4185395,26.192248 L22.5074853,26.4510086 C22.6438527,26.7905021 22.7347899,27.1055872 22.767062,27.3842449 L22.8164461,27.8637832 L22.8450691,28.2767268 C22.8992397,29.4210214 22.6656192,29.7385902 22.0765619,29.8801511 L21.8669523,29.9217119 L21.6304573,29.9555908 L21.2924265,29.9892232 C20.4076261,30.0508432 19.6778653,29.8672117 19.6339964,28.9410162 L19.6335281,28.7685814 C19.6471407,28.3903064 19.580827,27.7708268 19.4368138,27.0473829 L19.3745648,27.0855879 C19.3548466,26.8247482 19.2985172,26.4548174 19.1952223,26.0196121 C18.9837825,25.2382333 18.6979027,24.4174552 18.3393256,23.6879114 C18.237597,23.4936505 18.1283791,23.2981797 18.0101178,23.1052448 L17.419353,23.2373307 C16.5204593,22.8358198 15.514517,21.6145258 14.8572699,20.0054347 C14.2023842,18.3963436 14.0756575,16.8405842 14.4471792,15.9537555 C16.1708509,15.0104747 17.9718478,13.8309761 19.7356528,12.2965902 C19.7597206,12.2674976 19.7847421,12.2440574 19.8106306,12.2243517 L20.5191664,11.5302161 C20.7295978,11.3260649 20.9565859,11.1108406 21.1811938,10.9112952 L21.4274649,10.6962661 L21.4502063,10.6731635 C21.6278125,10.4888255 21.836978,10.3363018 22.0679933,10.2220596 L22.245987,10.1436567 C23.401475,9.702714 24.8298854,10.3029447 26.0822772,11.5862633 Z M13.564028,16.4230738 C13.3467822,17.4919918 13.5042067,18.8938514 14.0803802,20.3048537 C14.6573408,21.7173796 15.5310466,22.8449624 16.4441085,23.4826562 L15.849044,23.6411273 C14.754944,24.0586377 13.235798,22.8426768 12.4549726,20.9250242 C11.6725731,19.0073716 11.9244522,17.114861 13.016978,16.6973507 L13.564028,16.4230738 Z M22.5671329,10.9322028 C21.4777556,11.3474276 21.2770394,14.6197032 22.3128924,17.2687608 C23.3943984,20.0321005 25.7903987,21.9627051 26.8923699,21.5413853 C28.3894766,20.9699751 28.6334845,18.1304474 27.4394199,15.2025416 C26.2445683,12.273112 24.0626652,10.3607926 22.5671329,10.9322028 Z M23.2212315,15.0014052 C24.194876,15.0024201 25.0142252,15.7073994 25.1293838,16.6432058 C25.2445424,17.5790122 24.619197,18.4505534 23.6730397,18.6729062 C23.2912929,18.1666307 22.9812986,17.6131595 22.7513195,17.0272448 C22.5287165,16.4481444 22.3760501,15.8460689 22.2963628,15.2330168 C22.5710685,15.0836883 22.8867045,15.0014052 23.2212315,15.0014052 Z" id="path-2"></path>
        <filter x="-35.3%" y="-30.0%" width="170.6%" height="160.0%" filterUnits="objectBoundingBox" id="filter-3">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.309803922   0 0 0 0 0.623529412   0 0 0 0 1  0 0 0 0.7 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="综合监测-大屏" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="1-1综合监测-宏观" transform="translate(-35.000000, -162.000000)">
            <g id="左侧图层" transform="translate(12.000000, 112.000000)">
                <g id="icon_云广播_18_s" transform="translate(15.000000, 44.000000)">
                    <use fill="black" fill-opacity="1" filter="url(#filter-3)" xlink:href="#path-2"></use>
                    <use fill="url(#linearGradient-1)" fill-rule="evenodd" xlink:href="#path-2"></use>
                </g>
            </g>
        </g>
    </g>
</svg>
