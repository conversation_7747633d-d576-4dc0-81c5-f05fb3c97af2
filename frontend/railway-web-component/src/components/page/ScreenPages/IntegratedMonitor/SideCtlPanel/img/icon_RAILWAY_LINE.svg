<?xml version="1.0" encoding="UTF-8"?>
<svg width="26px" height="26px" viewBox="0 0 26 26" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>icon_铁路线路_18_n</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#BCDBFF" offset="100%"></stop>
        </linearGradient>
        <path d="M14.0995748,2 L5.4790673,2 C3.82635577,2.17946206 3,3.26681888 3,5.26207044 C3,7.257322 3.82635577,8.45932202 5.4790673,8.8680705 L14.0995748,8.8680705 C15.6998583,9.44620995 16.5,10.6160724 16.5,12.377658 C16.5,14.1392436 15.6998583,15.1800242 14.0995748,15.5 L3.88582169,15.5" id="path-2"></path>
        <filter x="-50.0%" y="-50.0%" width="200.0%" height="200.0%" filterUnits="objectBoundingBox" id="filter-3">
            <feMorphology radius="0.75" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
            <feOffset dx="0" dy="0" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
            <feMorphology radius="1" operator="erode" in="SourceAlpha" result="shadowInner"></feMorphology>
            <feOffset dx="0" dy="0" in="shadowInner" result="shadowInner"></feOffset>
            <feComposite in="shadowOffsetOuter1" in2="shadowInner" operator="out" result="shadowOffsetOuter1"></feComposite>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.309803922   0 0 0 0 0.623529412   0 0 0 0 1  0 0 0 0.7 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <ellipse id="path-4" cx="15.75" cy="2.2173913" rx="2.25" ry="2.2173913"></ellipse>
        <filter x="-133.3%" y="-135.3%" width="366.7%" height="370.6%" filterUnits="objectBoundingBox" id="filter-5">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.309803922   0 0 0 0 0.623529412   0 0 0 0 1  0 0 0 0.7 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <ellipse id="path-6" cx="2.25" cy="15.7826087" rx="2.25" ry="2.2173913"></ellipse>
        <filter x="-133.3%" y="-135.3%" width="366.7%" height="370.6%" filterUnits="objectBoundingBox" id="filter-7">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.309803922   0 0 0 0 0.623529412   0 0 0 0 1  0 0 0 0.7 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="综合监测-大屏" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="1-1综合监测-宏观" transform="translate(-35.000000, -592.000000)">
            <g id="左侧图层" transform="translate(12.000000, 112.000000)">
                <g id="编组" transform="translate(8.000000, 473.000000)">
                    <g id="icon_铁路线路_18_n" transform="translate(19.000000, 11.000000)">
                        <g id="路径-3" stroke-dasharray="3.75,1.5">
                            <use fill="black" fill-opacity="1" filter="url(#filter-3)" xlink:href="#path-2"></use>
                            <use stroke="url(#linearGradient-1)" stroke-width="1.5" xlink:href="#path-2"></use>
                        </g>
                        <g id="椭圆形">
                            <use fill="black" fill-opacity="1" filter="url(#filter-5)" xlink:href="#path-4"></use>
                            <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-4"></use>
                        </g>
                        <g id="椭圆形">
                            <use fill="black" fill-opacity="1" filter="url(#filter-7)" xlink:href="#path-6"></use>
                            <use fill="#BCDBFF" fill-rule="evenodd" xlink:href="#path-6"></use>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>