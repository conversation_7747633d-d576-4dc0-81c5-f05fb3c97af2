<?xml version="1.0" encoding="UTF-8"?>
<svg width="26px" height="30px" viewBox="0 0 26 30" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>icon_过车信息_18_n</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="99.8323454%" id="linearGradient-1">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#BCDBFF" offset="100%"></stop>
        </linearGradient>
        <path d="M27.7438407,9 C28.518731,9 29.2018225,9.03526412 29.7904151,9.1057104 C30.3790078,9.17615669 30.8947013,9.27077571 31.3368208,9.38818618 C31.7910901,9.50559665 32.1778603,9.6423453 32.5504556,9.80810126 L32.4998313,9.7880724 C32.8326021,9.93725275 33.1390483,10.0898864 33.4178198,10.2473545 C33.6965913,10.404132 33.9922376,10.5843916 34.3027337,10.7881333 C34.6125549,10.9925657 34.8913264,11.2239334 35.1397233,11.4822364 C35.3874452,11.7419207 35.5933176,12.0354469 35.7559906,12.3655775 C35.9179885,12.6950175 35.9989801,13.0638245 35.9989801,13.4713079 L35.9989801,23.7799469 C35.9989801,24.4547118 35.80189,25.0507425 35.4070199,25.5694205 C35.0391051,26.0635494 34.5251687,26.4230031 33.9416132,26.5943449 L22.0347621,26.5936542 L21.8727641,26.5411648 C21.3612574,26.3549291 20.9140868,26.019813 20.5862302,25.5770177 L20.5808302,25.5701112 C20.2023155,25.0703703 19.9975591,24.455705 19.9989801,23.8234578 L19.9989801,23.7785656 L19.9989801,23.7806376 L19.9989801,13.4719985 C19.9989801,13.0638244 20.1036112,12.6756793 20.3128586,12.3068723 C20.522106,11.9380653 20.7779278,11.6003375 21.080324,11.2950703 C21.3655413,11.0034133 21.6767086,10.7395949 22.0097874,10.5070389 C22.3277084,10.2867216 22.60378,10.1223469 22.835977,10.0125336 C23.0681741,9.90202962 23.3239959,9.7853098 23.6027675,9.65961155 C23.881539,9.53322264 24.2149848,9.42409998 24.60243,9.33017159 C24.9898751,9.2362432 25.4394195,9.1568185 25.9524131,9.09465999 C26.548485,9.02605877 27.148089,8.99469117 27.7478907,9.00073163 L27.7418157,9.00073163 L27.7438407,9 Z M25.8126898,10.5291397 L25.80324,10.5291397 C25.6099629,10.5291988 25.4250114,10.6096399 25.2908485,10.7519944 C25.1566857,10.894349 25.0848372,11.0863876 25.0917989,11.2840199 L25.0917989,11.2826386 L25.0917989,11.2964516 C25.0917989,11.4946681 25.1721229,11.6735464 25.3017212,11.8006259 C25.4414445,11.9415185 25.6115424,12.0126554 25.8126898,12.0126554 L30.2460344,12.0126554 C30.440432,12.0126554 30.6166048,11.9318494 30.7428282,11.8006259 C30.877093,11.6689074 30.9529274,11.4867746 30.9527506,11.2964516 L30.9527506,11.2819479 L30.9527506,11.2826386 L30.9534256,11.2577752 C30.9534256,10.855817 30.6348296,10.5291397 30.2413095,10.5291397 L25.8126898,10.5291397 Z M21.4407695,17.8963012 L34.5335808,17.8963012 L34.5335808,13.4713079 L21.4414445,13.4713079 L21.4414445,17.8963012 L21.4407695,17.8963012 Z M22.1846102,25.5452478 C22.4633817,25.5452478 22.7036787,25.4437223 22.9055012,25.2392899 C23.1073237,25.0348576 23.2078974,24.7917489 23.2078974,24.5092731 C23.2078974,24.211603 23.1073237,23.9602065 22.9055012,23.7571555 C22.7193327,23.5609169 22.4633712,23.4502763 22.1960851,23.4505066 L22.1852852,23.4505066 L22.1859602,23.4505066 C21.8909889,23.4505066 21.642592,23.5527231 21.4407695,23.7571555 C21.2470678,23.9485262 21.1378842,24.2123565 21.1383716,24.4878629 L21.1383716,24.5106544 L21.1383716,24.5092731 C21.1383716,24.7917489 21.238947,25.0348576 21.4407695,25.2392899 C21.6376246,25.4411335 21.9066696,25.5516972 22.1852852,25.5452478 L22.1846102,25.5452478 Z M32.7657779,24.5092731 C32.7657779,24.7917489 32.8663517,25.0348576 33.0681742,25.2392899 C33.2699966,25.4430316 33.5183935,25.5452478 33.8126899,25.5452478 C34.1069862,25.5452478 34.3553831,25.4437223 34.5572055,25.2392899 C34.759028,25.0348576 34.8596018,24.7917489 34.8596018,24.5092731 C34.8596018,24.211603 34.759028,23.9602065 34.5572055,23.7571555 C34.3664629,23.5607053 34.1069532,23.4503166 33.8363146,23.4505067 L33.8120149,23.4505067 L33.8133648,23.4505067 C33.5190685,23.4505067 33.2706716,23.5527231 33.0688492,23.7571555 C32.8751475,23.9485262 32.7659639,24.2123565 32.7664529,24.4878629 L32.7664529,24.5106544 L32.7664529,24.5092731 L32.7657779,24.5092731 Z M33.9375633,26.4955819 C33.9490382,26.4928193 33.9382383,26.4962726 33.9281134,26.4990352 L33.9065137,26.5045604 L37,31 L34.0003375,31 L33.3719204,29.5634484 L22.6280796,29.5634484 L21.9996625,31 L19,31 L22.0691866,26.5045604 L22.0165373,26.4893661 L22.038137,26.4955819 L33.9375633,26.4955819 Z M32.0462369,26.7221151 L23.9537631,26.7221151 L23.2787715,28.2049402 L32.6976038,28.2049402 L32.0462369,26.7221151 L32.0462369,26.7221151 Z" id="path-2"></path>
        <filter x="-33.3%" y="-27.3%" width="166.7%" height="154.5%" filterUnits="objectBoundingBox" id="filter-3">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.309803922   0 0 0 0 0.623529412   0 0 0 0 1  0 0 0 0.7 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="综合监测-大屏" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="1-1综合监测-宏观" transform="translate(-35.000000, -697.000000)" fill-rule="nonzero">
            <g id="左侧图层" transform="translate(12.000000, 112.000000)">
                <g id="icon_过车信息_18_n" transform="translate(8.000000, 580.000000)">
                    <use fill="black" fill-opacity="1" filter="url(#filter-3)" xlink:href="#path-2"></use>
                    <use fill="url(#linearGradient-1)" xlink:href="#path-2"></use>
                </g>
            </g>
        </g>
    </g>
</svg>