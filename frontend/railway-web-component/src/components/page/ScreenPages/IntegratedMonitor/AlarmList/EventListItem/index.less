.list-table-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 13.5px 12px 11.5px 12px;
  // margin: 0 12px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  cursor: pointer;
  height: 130px;
  box-sizing: border-box;
  .item-left {
    width: 130px;
    height: 87px;
    background-color: #07223c;
    margin-right: 12px;
    position: relative;
    border-radius: 4px;
    overflow: hidden;
    span {
      font-size: 12px;
      position: absolute;
      bottom: 2px;
      left: 5px;
    }
    .eventListImage {
      width: 100%;
      height: 100%;
    }
  }
  .item-right {
    width: calc(100% - 142px);
    min-height: 87px;
    font-family: PingFangSC-Regular;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.65);
    .item-right-title {
      display: flex;
      justify-content: space-between;
      font-size: 14px;
      color: #ffffff;
      margin-bottom: 6px;
      .title-left {
        display: flex;
        align-items: center;

        > span:not(:first-child) {
          margin-left: 6px;
        }
        .name {
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          width: 135px;
        }
        .level-tag {
          height: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          box-sizing: border-box;
          padding: 0 3px;
          border-radius: 4px;
          font-family: PingFangSC-Medium;
          font-size: 12px;
          letter-spacing: 0;
          font-weight: 500;
        }
        .level-tag1 {
          background: rgba(255, 0, 23, 0.2);
          color: #ff0000;
        }
        .level-tag2 {
          background: rgba(251, 145, 60, 0.2);
          color: #fb913c;
        }
        .level-tag3 {
          background: rgba(255, 224, 0, 0.2);
          color: #ffe000;
        }
        .level-tag4 {
          background: rgba(79, 159, 255, 0.2);
          color: #4f9fff;
        }
      }
      img {
        width: 14px;
        height: 14px;
      }
    }
    .item-span {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      // height: 23px;
      // margin-bottom: 5px;
      // margin-right: 37px;
    }
    .item-right-con {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .item-right-con-l {
        display: flex;
        flex-direction: column;
        .con-l-tip {
          width: 116px;
          height: 20px;
          line-height: 20px;
          text-align: center;
          border-radius: 8px;
          background: rgba(13, 201, 133, 0.2);
          color: #0dc985;
        }
        .eventTimeClassChaoQi {
          background: #514647;
          color: #f6d079;
        }
      }
      .item-right-con-r {
        cursor: pointer;
        color: #ffffff;
        width: 50px;
        height: 24px;
        line-height: 24px;
        text-align: center;
        background-image: linear-gradient(90deg, #2fb1ec 8%, #155bd4 100%);
        border-radius: 4px;
        cursor: pointer;
      }
    }
    .item-tags {
      display: flex;
      column-gap: 6px;
      margin-top: 6px;
      .alarm-type {
        //width: 48px;
        height: 24px;
        padding: 0 4px;
        line-height: 24px;
        border-radius: 4px;
        font-family: PingFangSC-Regular;
        font-size: 12px;
        letter-spacing: 0;
        font-weight: 400;
        text-align: center;
        background: rgba(237, 81, 88, 0.2);
        color: #ed5158;
      }
      .alarm-typeALARM {
        background: rgba(237, 81, 88, 0.2);
        color: #ed5158;
      }
      .alarm-typeDANGER {
        background: rgba(251, 145, 60, 0.2);
        color: #fb913c;
      }
      .alarm-status {
        padding: 0 12px;
        height: 24px;
        line-height: 24px;
        border-radius: 4px;
        font-family: PingFangSC-Regular;
        font-size: 12px;
        letter-spacing: 0;
        font-weight: 400;
        text-align: center;
      }
      .alarm-status1 {
        color: #fb913c;
        background: rgba(251, 145, 60, 0.2);
      }
      .alarm-status2 {
        color: #4f9fff;
        background: rgba(79, 159, 255, 0.2);
      }
      .alarm-status3 {
        color: #e8f3ff;
        background: rgba(232, 243, 255, 0.2);
      }
      .flow-node {
        height: 24px;
        padding: 0 4px;
        line-height: 24px;
        background: #1e3f63;
        border-radius: 4px;
        font-family: PingFangSC-Regular;
        font-size: 12px;
        color: rgba(232, 243, 255, 0.7);
        letter-spacing: 0;
        font-weight: 400;
        display: flex;
        align-items: center;
        justify-content: center;

        .evt-type-img {
          width: 6px;
          width: 6px;
          margin-right: 6px;
        }

        &.flow-node-end {
          background: #e8f3ff66;
          border-radius: 4px;
          font-family: PingFangSC-Regular;
          font-size: 12px;
          color: #e8f3ff;
          letter-spacing: 0;
          font-weight: 400;
        }
      }
      .fusion1 {
        background: #87420e;
        color: #fb913c;
      }
      .fusion2 {
        background: #17434a;
        color: #15bd94;
      }
    }
  }
}
.table-item-clicked {
  background-image: linear-gradient(
    90deg,
    rgba(25, 137, 250, 0.8) 0%,
    rgba(0, 0, 0, 0) 100%
  );
  border-left: 2px solid #fff;
  box-sizing: border-box;
  // margin: 0px;
  // padding: 16px 11px;
}
