.alarm-list-outter {
  width: 366px !important;
  height: 862px !important;
  z-index: 9;

  .rt-alarm-list {
    margin-top: 6px;
    width: 100% !important;
    height: 100% !important;
    background: #172537;
    border-radius: 8px;
    font-size: 12px;
    color: #ffffff;
    letter-spacing: 0;
    pointer-events: auto;

    .list-tab {
      display: flex;
      height: 47px;
      justify-content: space-around;
      align-items: center;
      position: relative;
      padding: 0 12px;
      font-family: PingFangSC-Regular;
      font-size: 14px;
      color: #ffffff;
      letter-spacing: 0;
      font-weight: 400;

      &::after {
        content: "";
        width: 320px;
        height: 1px;
        position: absolute;
        background: url("~@/assets/images/alarmEvent/alarm/gaojingliebiao_xian_chang.png")
          no-repeat;
        bottom: 0;
      }

      .tab-item {
        position: relative;
        height: 100%;
        width: 50%;
        display: flex;
        justify-content: center;
        // align-items: center;
        cursor: pointer;

        &:hover {
          color: #e8f3ff;
        }

        span {
          margin-top: 16px;
          position: relative;

          .total-num {
            position: absolute;
            right: -20px;
            top: -10px;
            width: 20px;
            height: 20px;
            border-radius: 20px 20px 20px 0;
            font-size: 14px;
            font-style: normal;
            font-weight: 700;
            display: flex;
            align-items: center;
            justify-content: center;
            // background-color: red;
            background-image: linear-gradient(180deg, #ff6a6c 0%, #fb2c2e 100%);
          }
        }
      }

      .tab-item-click {
        color: #e8f3ff;

        &::after {
          content: "";
          width: 140px;
          height: 11px;
          position: absolute;
          background: url("~@/assets/images/alarmEvent/alarm/gaojingliebiao_xian_sel.png")
            no-repeat;
          background-size: contain;
          bottom: -3px;
        }
      }
    }

    .list-table {
      .el-loading-mask {
        // background-color: transparent;
        background: rgba(23, 37, 55, 0.85)
          url("~@/assets/images/comm/loading-dark.gif") 50% 50% no-repeat !important;
        background-size: 100px 80px !important;
      }

      height: calc(100% - 47px - 28px);
      overflow-x: hidden;
      overflow-y: auto;

      // .table-body {
      //   padding: 0 12px;
      // }
    }

    .list-pagination {
      height: 28px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 12px;
      padding: 0 12px;

      .page-number {
        font-size: 12px;
        color: #e8f3ff;
        letter-spacing: 0;
        font-weight: 400;
      }

      .el-pagination {
        display: flex;
        align-items: center;
      }

      .el-pagination button:hover {
        color: #409eff;
      }

      .el-pagination button:disabled {
        background: transparent;
        color: #c0c4cc;
      }

      .el-pagination .btn-next,
      .el-pagination .btn-prev {
        background: transparent;
        color: #fff;
      }

      .el-pagination .el-pager li {
        color: #fff;
        font-weight: 400;
        background: transparent;
        line-height: 22px;
      }

      .el-pagination .el-pager li.active {
        background: #1989fa;
        border-radius: 0.02rem;
        color: #fff;
      }

      .alarm-icon {
        font-size: 16px;
        cursor: pointer;
      }
    }
  }

  //图片视频放大样式
  .alarm-max-file-box {
    width: 926px;
    height: 522px;
    border-radius: 8px;
    background: linear-gradient(180deg, rgba(0, 19, 30, 0.7) 0%, #00131e 100%);
    border-image: linear-gradient(
        360deg,
        rgba(7, 91, 74, 0.75),
        rgba(7, 91, 74, 0.3)
      )
      1 1;
    position: fixed;
    top: 58px;
    left: 475px;
    overflow: hidden;

    .carousel-img-big {
      width: 100%;
      height: 100%;
    }
  }
}
