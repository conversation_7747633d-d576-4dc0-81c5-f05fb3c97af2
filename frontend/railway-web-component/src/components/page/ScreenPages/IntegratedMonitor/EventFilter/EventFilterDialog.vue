<template>
  <!-- v-divDrag  -->
  <div :class="['alarm-filter-dialog-view', className]">
    <div class="alarm-filter-header tool-header">
      <el-form ref="form" label-width="0px">
        <div class="alarm-filter-container">
          <!-- 时间 -->
          <div class="title titleTime">
            <div :class="alarmDateShow ? 'titleSelected' : ''" class="date-hover-bg">
              <el-form-item class="dataRangeNormal">
                <el-date-picker
                  ref="alarmDataPicker"
                  v-model="alarmDate"
                  :picker-options="pickerOptions"
                  :append-to-body="false"
                  value-format="yyyy-MM-dd"
                  type="daterange"
                  format="yyyy-MM-dd"
                  range-separator="至"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  popper-class="iwhale-speciesLYstyle"
                  :clearable="false"
                  @change="changeTime"
                />
              </el-form-item>
            </div>
          </div>
          <!-- 事件状态 -->
          <div class="title alarm-title">
            <el-dropdown
              ref="statusDrop"
              trigger="click"
              placement="bottom-start"
              :class="alarmStatusShow ? 'titleSelected' : ''"
              @visible-change="dropdownChangeStatus"
            >
              <span
                class="el-dropdown-link"
                :class="alarmStatusSelect && !isStatusAllSelect ? 'title-selected' : ''"
              >
                事件状态
                <i
                  :class="`${alarmStatusShow ? 'el-icon--right el-icon-arrow-up' : 'el-icon--right el-icon-arrow-down'}`"
                />
              </span>
              <el-dropdown-menu slot="dropdown" class="drop-down-select iwhale-speciesLYstyle">
                <!--  style="min-width: 178px" -->
                <div class="select-container">
                  <el-checkbox
                    v-model="isStatusAllSelect"
                    style="width: calc(100% - 12px)"
                    class="select-item"
                    :indeterminate="
                      !isStatusAllSelect && alarmStatusListData.length > 0
                    "
                    @change="allClick(0)"
                  >全部</el-checkbox>
                  <el-checkbox-group v-model="alarmStatusListData">
                    <el-checkbox
                      v-for="(item, index) in alarmStatusListCopy"
                      :key="index"
                      class="select-item"
                      :label="item"
                      @change="itemClick(0, index, item)"
                    >{{ item.dictLabel }}</el-checkbox>
                  </el-checkbox-group>
                </div>
                <div class="judgeBtnDiv">
                  <el-button type="primary" class="judgeFalseBtn" @click="submitGoToGetAlarm(0)">确定</el-button>
                  <el-button class="judgeCloseBtn" @click="resetAlarmData(0)">重置</el-button>
                </div>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
          <!-- 处置状态 -->
          <div class="title alarm-title">
            <el-dropdown
              ref="sourceDrop"
              trigger="click"
              placement="bottom-start"
              :class="warningSourceShow ? 'titleSelected' : ''"
              @visible-change="dropdownChangeSource"
            >
              <span
                class="el-dropdown-link"
                :class="warningSourceSelect && !isSourceAllSelect ? 'title-selected' : ''"
              >
                处置状态
                <i
                  :class="`el-icon--right ${warningSourceShow ? 'el-icon-arrow-up' : 'el-icon-arrow-down'}`"
                />
              </span>
              <el-dropdown-menu
                slot="dropdown"
                class="drop-down-select iwhale-speciesLYstyle"
                placement="bottom-start"
              >
                <div class="select-container">
                  <el-checkbox
                    v-model="isSourceAllSelect"
                    style="width: calc(100% - 12px)"
                    class="select-item"
                    :indeterminate="
                      !isSourceAllSelect && warningSourceListData.length > 0
                    "
                    @change="allClick(1)"
                  >全部</el-checkbox>
                  <el-checkbox-group v-model="warningSourceListData">
                    <el-checkbox
                      v-for="(item, index) in warningSourceListCopy"
                      :key="index"
                      class="select-item"
                      :label="item"
                      @change="itemClick(1, index, item)"
                    >{{ item.dictLabel }}</el-checkbox>
                  </el-checkbox-group>
                </div>
                <div class="judgeBtnDiv">
                  <el-button type="primary" @click="submitGoToGetAlarm(1)">确定</el-button>
                  <el-button @click="resetAlarmData(1)">重置</el-button>
                </div>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
          <!-- 事件来源 -->
          <div class="title alarm-title">
            <el-dropdown
              ref="eventSourceDrop"
              trigger="click"
              placement="bottom-start"
              :class="eventSourceShow ? 'titleSelected' : ''"
              @visible-change="dropdownChangeEventSource"
            >
              <span
                class="el-dropdown-link"
                :class="eventSourceSelect && !isEventSourceAllSelect ?'title-selected' : ''"
              >
                事件来源
                <i
                  :class="`${eventSourceShow ? 'el-icon--right el-icon-arrow-up' : 'el-icon--right el-icon-arrow-down'}`"
                />
              </span>
              <el-dropdown-menu slot="dropdown" class="drop-down-select iwhale-speciesLYstyle">
                <!--  style="min-width: 178px" -->
                <div class="select-container">
                  <el-checkbox
                    v-model="isEventSourceAllSelect"
                    style="width: calc(100% - 12px)"
                    class="select-item"
                    :indeterminate="
                      !isEventSourceAllSelect && eventSourceListData.length > 0
                    "
                    @change="allClick(6)"
                  >全部</el-checkbox>
                  <el-checkbox-group v-model="eventSourceListData">
                    <el-checkbox
                      v-for="(item, index) in eventSourceListCopy"
                      :key="index"
                      class="select-item"
                      :label="item"
                      @change="itemClick(6, index, item)"
                    >{{ item.dictLabel }}</el-checkbox>
                  </el-checkbox-group>
                </div>
                <div class="judgeBtnDiv">
                  <el-button type="primary" class="judgeFalseBtn" @click="submitGoToGetAlarm(6)">确定</el-button>
                  <el-button class="judgeCloseBtn" @click="resetAlarmData(6)">重置</el-button>
                </div>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
          <!-- 事件类型 -->
          <div class="title alarm-title">
            <el-dropdown
              ref="eventTypesDrop"
              trigger="click"
              placement="bottom-start"
              :class="eventTypesShow ? 'titleSelected' : ''"
              @visible-change="dropdownChangeEventTypes"
            >
              <span
                class="el-dropdown-link"
                :class="eventTypesSelect && !isEventTypesAllSelect ?'title-selected' : ''"
              >
                事件类型
                <i
                  :class="`${eventTypesShow ? 'el-icon--right el-icon-arrow-up' : 'el-icon--right el-icon-arrow-down'}`"
                />
              </span>
              <el-dropdown-menu slot="dropdown" class="drop-down-select iwhale-speciesLYstyle">
                <!--  style="min-width: 178px" -->
                <div class="select-container">
                  <el-checkbox
                    v-model="isEventTypesAllSelect"
                    style="width: calc(100% - 12px)"
                    class="select-item"
                    :indeterminate="
                      !isEventTypesAllSelect && eventTypesListData.length > 0
                    "
                    @change="allClick(7)"
                  >全部</el-checkbox>
                  <el-checkbox-group v-model="eventTypesListData">
                    <el-checkbox
                      v-for="(item, index) in eventTypesListCopy"
                      :key="index"
                      class="select-item"
                      :label="item"
                      @change="itemClick(7, index, item)"
                    >{{ item.dictLabel }}</el-checkbox>
                  </el-checkbox-group>
                </div>
                <div class="judgeBtnDiv">
                  <el-button type="primary" class="judgeFalseBtn" @click="submitGoToGetAlarm(7)">确定</el-button>
                  <el-button class="judgeCloseBtn" @click="resetAlarmData(7)">重置</el-button>
                </div>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
          <!-- 告警类型 -->
          <!-- <div class="title alarm-title" v-if="parentName !== 'spaceTimeTrace'">
            <el-dropdown
              ref="typeDrop"
              trigger="click"
              :class="alarmTypeShow ? 'titleSelected' : ''"
              @visible-change="dropdownChangeType"
            >
              <span class="el-dropdown-link" :class="alarmTypeSelect ? 'title-selected' : ''">
                告警类型
                <i
                  :class="`el-icon--right ${
                    alarmTypeShow ? 'el-icon-arrow-up' : 'el-icon-arrow-down'
                  }`"
                />
              </span>
              <el-dropdown-menu
                slot="dropdown"
                class="drop-down-select alarm-type-dropdown-menu iwhale-speciesLYstyle"
              >
                <div class="input-bg">
                  <div class="input-box">
                    <el-tag
                      v-if="alarmTypeListCopy.find(o => o.checked === '1')"
                      closable
                      class="first-tag"
                      @close="
                        itemClick(
                          2,
                          alarmTypeListCopy.findIndex(o => o.checked === '1'),
                          alarmTypeListCopy.find(o => o.checked === '1')
                        )
                      "
                    >
                      {{
                      alarmTypeListCopy.find(o => o.checked === "1").dictLabel
                      }}
                    </el-tag>
                    <el-tag
                      v-if="
                        alarmTypeListCopy.filter(o => o.checked === '1')
                          .length > 1
                      "
                      class="more-tag"
                    >
                      +{{
                      alarmTypeListCopy.filter(o => o.checked === "1")
                      .length - 1
                      }}
                    </el-tag>
                    <div style="flex: 1">
                      <el-input
                        v-model="keywordValue"
                        class="search-input"
                        clearable
                        :placeholder="
                          alarmTypeListCopy.filter(o => o.checked === '1')
                            .length
                            ? ''
                            : '输入关键字'
                        "
                        @clear="clearTypeListShow()"
                      />
                    </div>
                    <div class="searchTypeDiv right_line" @click="searchClick()">
                      <i
                        class="el-input__icon el-icon-search"
                        style="font-size: 18px; color: aliceblue"
                      />
                    </div>
                  </div>
                </div>
                <div class="select-container" style="max-width: 300px">
                  <div
                    v-for="(item, index) in alarmTypeListShow.length > 0
                      ? alarmTypeListShow
                      : alarmTypeListCopy"
                    :key="index"
                    :class="`select-item ${
                      item.checked === '1' ? 'selectedType' : ''
                    }`"
                    @click="
                      itemClick(
                        2,
                        alarmTypeListCopy.findIndex(
                          o => o.dictValue === item.dictValue
                        ),
                        item
                      )
                    "
                  >
                    <div style="display: flex; cursor: pointer">
                      <el-checkbox
                        style="margin-right: 10px"
                        :value="item.checked === '1'"
                        @click.stop
                        @change="
                          itemClick(
                            2,
                            alarmTypeListCopy.findIndex(
                              o => o.dictValue === item.dictValue
                            ),
                            item
                          )
                        "
                      >{{ item.dictLabel }}</el-checkbox>
                    </div>
                  </div>
                </div>
                <div class="judgeBtnDiv">
                  <el-button type="primary" @click="submitGoToGetAlarm(2)">确定</el-button>
                  <el-button @click="resetAlarmData(2)">重置</el-button>
                </div>
              </el-dropdown-menu>
            </el-dropdown>
          </div>-->
          <!-- 其他 -->
          <!-- <div class="title alarm-title" style="display: none">
            <el-dropdown
              ref="otherDrop"
              trigger="click"
              :class="otherShow ? 'titleSelected' : ''"
              @visible-change="dropdownChangeOther"
            >
              <span class="el-dropdown-link" :class="otherSelect ? 'title-selected' : ''">
                其他
                <i
                  :class="`el-icon--right ${
                    otherShow ? 'el-icon-arrow-up' : 'el-icon-arrow-down'
                  }`"
                />
              </span>
              <el-dropdown-menu
                slot="dropdown"
                class="drop-down-select other-dropdown-menu iwhale-speciesLYstyle"
                :divider="false"
              >
                <div class="select-container">
                  <el-collapse v-model="activeName">
                    <div
                      v-for="(item, index) in functionList.filter(
                        itm => itm.data.length > 0
                      )"
                      :key="index"
                      class="select-input-item"
                    >
                      <div class="select-input-title">{{ item.name }}</div>
                      <styledElTreeSelect
                        ref="functionTree"
                        placeholder="全部"
                        node-key="code"
                        popper-class="custom-fn-ly-tree"
                        :value="item.selectData"
                        :default-expanded-keys="item.fristLevel"
                        :data-source="item.data"
                        :props="treeProps"
                        multiple
                        @node-expand="
                          (data, node) => {
                            clickTreeItem(data, node, index);
                          }
                        "
                        @input="changeFunctionSelectData(index, $event)"
                      >
                        <span slot-scope="{ node, data }" class="custom-tree-node">
                          <img
                            v-if="data.list.length == 0 && index == 0"
                            class="tree-img"
                            src="@/assets/images/alarmEvent/alarm/device.png"
                          />
                          <span
                            v-if="data.list.length == 0 && index == 1"
                            class="iconfont icon-guotu_icon_wulian"
                            style="padding-right: 2px"
                          />
                          <span :title="node.label">{{ node.label }}</span>
                        </span>
                      </styledElTreeSelect>
                    </div>
                  </el-collapse>
                </div>
                <div class="judgeBtnDiv">
                  <el-button type="primary" @click="submitGoToGetAlarm(3)">确定</el-button>
                  <el-button @click="resetAlarmData(3)">重置</el-button>
                </div>
              </el-dropdown-menu>
            </el-dropdown>
          </div>-->
        </div>
      </el-form>
    </div>
  </div>
</template>

<script>
// 引入依赖组件和工具
// import $ from "jquery";
// import { dragBind } from "@/utils/common.js";
// import {
//   getMonitorTree,
//   findOrderAlarm,
//   queryDevicesByAreaParam,
// } from '@/api/service/alarmFilter.js'
// import getDimValueByCatCode, {
//   qryByCatCodeCommon,
// } from '@/api/service/getDimValueByCatCode'
// import styledElTreeSelect from './styledElTreeSelect.vue'
// import bus from '@/common/bus.js'

import dayjs from 'dayjs'
import api from '@/api'
import { mapMutations } from 'vuex'
import {
  alarmStatusListDataDefault,
  // warningSourceListDataDefault,
  // eventSourceListDataDefault,
} from '@/components/page/ScreenPages/IntegratedMonitor/EventFilter/typeDatas.js'
import { mapGetters } from 'vuex'

export default {
  // 组件的依赖组件
  components: {},
  // 父组件传参
  props: {
    orderData: {
      type: Object,
      default: () => ({}),
    },
    // 需要选中的设备数据
    showDevinfo: {
      type: Object,
      default: () => ({}),
    },
    parentName: {
      type: String,
      default: () => 'monitor', // monitor: 监控页面
    },
    className: {
      type: String,
      default: '',
    },
  },
  // 组件变量区域
  data() {
    return {
      keywordValue: '', // 告警类型搜索

      alarmDate: [], // 告警时间
      alarmStatus: '', // 事件状态
      warningSource: '', // 处置状态
      alarmType: '', // 告警类型

      warningSourceListCopy: [], // 处置状态列表
      alarmStatusListCopy: [], // 事件状态列表
      eventSourceListCopy: [], // 事件来源列表
      eventTypesListCopy: [], // 事件类型列表
      alarmTypeListCopy: [], // 告警类型列表
      alarmTypeListShow: [], // 告警类型显示
      deviceCodeListCopy: [], // 摄像机选中列表
      radarListCopy: [], // 雷达选中列表
      deviceListCopy: [], // 物联设备列表

      alarmListDate: [], // 告警时间列表传值
      warningSourceListData: [], // 处置状态列表传值
      alarmStatusListData: [], // 事件状态列表传值
      eventSourceListData: [], // 事件来源列表传值
      eventTypesListData: [], // 事件类型列表传值
      alarmTypeListData: [], // 告警类型列表传值
      deviceCodeListData: [], // 摄像机选中列表传值
      radarListData: [], // 雷达选中列表传值
      deviceListData: [], // 物联设备列表传值

      otherListData: [], // 其他树所以选中数据

      alarmTypeListShowData: [], // 告警类型列表显示

      isStatusAllSelect: true, // 事件状态全部选中
      isEventSourceAllSelect: true, // 事件来源全部选中
      isEventTypesAllSelect: true, // 事件类型全部选中
      isSourceAllSelect: true, // 处置状态全部选中

      alarmDateSelect: false, // 告警时间是否选中
      warningSourceSelect: false, // 处置状态是否选中
      alarmStatusSelect: false, // 事件状态是否选中
      eventSourceSelect: false, // 事件来源是否选中
      eventTypesSelect: false, // 事件类型是否选中
      alarmTypeSelect: false, // 告警类型是否选中
      otherSelect: false, // 其他是否选中

      alarmDateShow: false, // 告警时间是否显示
      warningSourceShow: false, // 处置状态是否显示
      alarmStatusShow: false, // 事件状态是否显示
      eventSourceShow: false, // 事件来源是否显示
      eventTypesShow: false, // 事件类型是否显示
      alarmTypeShow: false, // 告警类型是否显示
      otherShow: false, // 其他是否显示

      functionList: [
        {
          name: '摄像机',
          queryFun: {}, // { 1: getMonitorTree },
          id: 1,
          data: [],
          selectData: [],
          selectFirstDevName: '',
        },
        // {
        //   name: '雷达',
        //   queryFun: {
        //     1: getRadarMonitorTree
        //   },
        //   id: 2,
        //   data: [],
        //   selectData: [],
        // },
        // {
        //     name: '物联设备',
        //     queryFun: {
        //         1: getIotMonitorTree
        //     },
        //     id: 3,
        //     data: [],
        //     selectData: [],
        //     selectFirstDevName: ''
        // }
      ],

      treeProps: { children: 'list', label: 'name' },

      pickerOptions: {
        disabledDate: time => {
          const date = new Date()
          const year = date.getFullYear()
          const month = date.getMonth() + 1
          const strDate = date.getDate()
          // 只能选择当前 6 月的时间
          const monthNum = month - 6 // 获取6个月前的月份差
          const { newYear, newMonth, newDay } = this.formatYearMonthDay(
            strDate,
            year,
            monthNum
          )
          const a = Date.parse(newYear + '-' + newMonth + '-' + newDay)
          const b = Date.parse(year + '-' + month + '-' + strDate)
          // 将两个日期都转换为毫秒格式，然后做差
          const diffDate = Math.abs(b - a) // 取相差毫秒数的绝对值
          // 注意：这是从当前日期开始往前算，如果当前日期可选的话，不要写【-8.64e7】
          return (
            time.getTime() > Date.now() ||
            time.getTime() < Date.now() - diffDate
          )
        },
        shortcuts: [
          {
            text: '今日',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              picker.$emit('pick', [start, end])
              leftDateClick()
            },
          },
          {
            text: '近3天',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 2)
              picker.$emit('pick', [start, end])
              leftDateClick()
            },
          },
          {
            text: '近7天',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 6)
              picker.$emit('pick', [start, end])
              leftDateClick()
            },
          },
          {
            text: '近30天',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 29)
              picker.$emit('pick', [start, end])
              leftDateClick()
            },
          },
        ],
        onPick: ({ maxDate, minDate }) => {
          // donothing
        },
      },

      activeName: '', // 展开的collapse

      timeStart: '', // 默认开始时间
      timeEnd: '', // 默认结束时间
    }
  },

  computed: {
    ...mapGetters('event', [
      'getEvtTypeList',
      'getEvtSourceList',
      'getEvtOrderStatusList',
    ]),
    isCust() {
      return true
    },
  },

  watch: {
    // 监听orderData变化
    orderData(val) {
      this.orderData = val
      this.setupView()
    },
    // 监听showDevinfo变化
    showDevinfo(val, ov) {
      // if (!ov || !ov.deviceCode) {
      //   // 清空当前所选的设备
      //   ;[0, 1].forEach(item => {
      //     this.functionList[item].selectData = []
      //   })
      // }
      // this.functionList.forEach((item, index) => {
      //   if (val.type && item.id == val.type) {
      //     this._setTreeOpenData(val, index)
      //   }
      // })
    },
  },
  // 销毁前钩子
  beforeDestroy() {
    // this.$globalEventBus.$off('cancelDevTreeSelected', this.cancelDevTreeSelected)
  },
  // 创建完成钩子
  created() {
    this.setupView()
  },
  // 挂载完成后钩子
  mounted() {
    window.submitGoToGetAlarm = this.submitGoToGetAlarm
    window.resetAlarmData = this.resetAlarmData
    window.leftDateClick = this.leftDateClick
  },
  methods: {
    ...mapMutations('map', ['setEventFilterParams']),
    // 默认7天时间
    getDefaultTime() {
      // const end = new Date()
      // const start = new Date()
      // 告警事件大屏查询当天的，其他大屏保持原有逻辑
      // if (this.getCurModel !== 'bigScreen') {
      //   start.setTime(start.getTime() - 3600 * 1000 * 24 * 6)
      // }
      // this.alarmDate = [
      //   dayjs(start).format('YYYY-MM-DD'),
      //   dayjs(end).format('YYYY-MM-DD'),
      // ]
      // .subtract(30, 'day').startOf('days')
      this.alarmDate = [
        dayjs().subtract(30, 'day').format('YYYY-MM-DD'),
        dayjs().format('YYYY-MM-DD'),
      ]
    },
    // 点击了快选日期
    leftDateClick() {
      setTimeout(() => {
        this.submitGoToGetAlarm(4)
        this.alarmDateSelect = true
        this.$refs.alarmDataPicker.handleClose()
      }, 100)
    },
    // 下拉内容变化
    dropdownChange(
      timeShow,
      statusShow,
      sourceShow,
      typeShow,
      otherShow,
      eventSourceShow,
      eventTypesShow
    ) {
      this.alarmDateShow = timeShow
      this.alarmStatusShow = statusShow
      this.warningSourceShow = sourceShow
      this.alarmTypeShow = typeShow
      this.otherShow = otherShow
      this.eventSourceShow = eventSourceShow
      this.eventTypesShow = eventTypesShow
      if (!timeShow) {
        this.$refs.alarmDataPicker.handleClose()
      }
      if (!statusShow) {
        this.$refs.statusDrop.hide()
      }
      if (!sourceShow) {
        this.$refs.sourceDrop.hide()
      }
      // if (!typeShow) {
      //   this.$refs.typeDrop.hide()
      // }
      // if (!otherShow) {
      //   this.$refs.otherDrop.hide()
      // }
      if (!eventSourceShow) {
        this.$refs.eventSourceDrop.hide()
      }
      if (!eventTypesShow) {
        this.$refs.eventTypesDrop.hide()
      }
    },

    /*
     * 弹窗状态改变
     */
    dropdownChangeStatus(show) {
      if (show) {
        this.dropdownChange(false, true, false, false, false, false, false)
      } else {
        this.alarmStatusShow = false
      }
    },
    dropdownChangeEventSource(show) {
      if (show) {
        this.dropdownChange(false, false, false, false, false, true, false)
      } else {
        this.eventSourceShow = false
      }
    },
    // 事件类型
    dropdownChangeEventTypes(show) {
      if (show) {
        this.dropdownChange(false, false, false, false, false, false, true)
      } else {
        this.eventTypesShow = false
      }
    },
    // 来源改变
    dropdownChangeSource(show) {
      if (show) {
        this.dropdownChange(false, false, true, false, false, false, false)
      } else {
        this.warningSourceShow = false
      }
    },
    // 类型改变
    dropdownChangeType(show) {
      if (show) {
        this.dropdownChange(false, false, false, true, false, false, false)
      } else {
        this.alarmTypeShow = false
      }
    },
    // 其他改变
    // dropdownChangeOther(show) {
    //   if (show) {
    //     this.dropdownChange(false, false, false, false, true, false, false)
    //   } else {
    //     this.otherShow = false
    //   }
    // },

    /*
     *选中树
     * flag  是否是手动触发
     */
    handleNodeClick(itemIndex, flag = false) {
      // 本方法进行了不规范的抽象，需要解耦重构
      const res = this.$refs.functionTree[itemIndex].getCheckedNodes()
      if (itemIndex === 0) {
        this.deviceCodeListCopy = res
      } else if (itemIndex === 1) {
        this.radarListCopy = res
      } else if (itemIndex === 2) {
        this.deviceListCopy = res
      } else {
        // do nothing
      }
      if (!flag) {
        // 这里混用数据类型，需要解耦重构
        this.functionList[itemIndex].selectData = res.map(
          dev => dev.deviceCode || dev.code
        )
        this.functionTreeCheck()
      }
    },
    functionTreeCheck(i) {
      if (this.showDevinfo && this.showDevinfo.deviceCode) {
        // donothing
      }
    },

    // 全选事件
    allClick(itemIndex) {
      if (itemIndex === 0) {
        this.alarmStatusListData = this.isStatusAllSelect
          ? [...this.alarmStatusListCopy]
          : []
      } else if (itemIndex === 1) {
        this.warningSourceListData = this.isSourceAllSelect
          ? [...this.warningSourceListCopy]
          : []
      } else if (itemIndex === 6) {
        this.eventSourceListData = this.isEventSourceAllSelect
          ? [...this.eventSourceListCopy]
          : []
      } else if (itemIndex === 7) {
        this.eventTypesListData = this.isEventTypesAllSelect
          ? [...this.eventTypesListCopy]
          : []
      } else {
        // do nothing
      }
    },
    // 事件状态/处置状态/告警类型
    itemClick(itemIndex, index, data) {
      if (itemIndex === 0) {
        const len = this.alarmStatusListData.length
        this.isStatusAllSelect =
          len > 0 && len === this.alarmStatusListCopy.length
        // 是否全选
        this.alarmStatusSelect = !this.isStatusAllSelect
      } else if (itemIndex === 1) {
        const len = this.warningSourceListData.length
        this.isSourceAllSelect =
          len > 0 && len === this.warningSourceListCopy.length
        // 是否全选
        this.warningSourceSelect = !this.isSourceAllSelect
      } else if (itemIndex === 2) {
        data.checked = data.checked === '0' ? '1' : '0'
        this.$set(this.alarmTypeListCopy, index, data)
        this.alarmTypeListShowData = this.alarmTypeListCopy.filter(
          item => item.checked === '1'
        )
      } else if (itemIndex === 6) {
        const len = this.eventSourceListData.length
        this.isEventSourceAllSelect =
          len > 0 && len === this.eventSourceListCopy.length
        // 是否全选
        this.eventSourceSelect = !this.isEventSourceAllSelect
      } else if (itemIndex === 7) {
        const len = this.eventTypesListData.length
        this.isEventTypesAllSelect =
          len > 0 && len === this.eventTypesListCopy.length
        // 是否全选
        this.eventTypesSelect = !this.isEventTypesAllSelect
      } else {
        // todo
      }
    },
    /**
     * 判断
     */
    submitBefore(itemIndex) {
      if (itemIndex === 0) {
        this.$refs.statusDrop.hide()
        this.alarmStatusSelect =
          this.alarmStatusListData != null &&
          this.alarmStatusListData.length > 0
        this.alarmStatusShow = false
      } else if (itemIndex === 1) {
        this.$refs.sourceDrop.hide()
        this.warningSourceSelect =
          this.warningSourceListData != null &&
          this.warningSourceListData.length > 0
        this.warningSourceShow = false
      }
      // 告警类型
      // else if (itemIndex === 2) {
      //   this.alarmTypeListData = this.alarmTypeListCopy.filter(
      //     item => item.checked === '1'
      //   )
      //   this.$refs.typeDrop.hide()
      //   this.alarmTypeSelect =
      //     this.alarmTypeListData != null && this.alarmTypeListData.length > 0
      //   this.alarmTypeShow = false
      // }
      // 其它
      // else if (itemIndex === 3) {
      //   this.$refs.otherDrop.hide()
      //   this.otherListData = []
      //   this.otherListData = this.getOtherData()
      //   this.otherSelect = this.otherListData.length > 0
      //   this.otherShow = false
      // }
      else if (itemIndex === 4) {
        this.alarmListDate = this.alarmDate
        this.alarmDateSelect =
          this.alarmListDate != null && this.alarmListDate.length > 0
        this.alarmDateShow = false
        setTimeout(() => this.$refs.alarmDataPicker.handleClose(), 100)
      }
      // else if (itemIndex === 5) {
      //   this.$refs.otherDrop.hide()
      //   this.otherListData = []
      //   this.otherListData = this.getOtherData()
      //   this.otherSelect = this.otherListData.length > 0
      // }
      else if (itemIndex === 6) {
        this.$refs.eventSourceDrop.hide()
        this.eventSourceSelect =
          this.eventSourceListData != null &&
          this.eventSourceListData.length > 0
        this.eventSourceShow = false
      } else if (itemIndex === 7) {
        this.$refs.eventTypesDrop.hide()
        this.eventTypesSelect =
          this.eventTypesListData != null && this.eventTypesListData.length > 0
        this.eventTypesShow = false
      } else {
        // do nothing
      }
    },
    // 确认
    submitGoToGetAlarm(itemIndex) {
      this.submitBefore(itemIndex)
      const params = {
        isRequest: true,
        // “是否重点关注”：Y-是，N-否,默认Y
        isFocus: 'Y',
        startDate: this.alarmDate.length ? this.alarmDate[0] + ' 00:00:00' : '',
        endDate: this.alarmDate.length ? this.alarmDate[1] + ' 23:59:59' : '',
        // warningTypeIdList: this.alarmTypeListData.map(item => item.dictValue),
        // deviceCode: this.otherListData.map(item => item.deviceCode),
        // 事件状态
        // 全选传 null
        fusionStatusList: this.isStatusAllSelect
          ? null
          : this.alarmStatusListData
              .map(item => item.dictValue)
              ?.join()
              .split(','),
        // 处置状态
        alarmStatusList: this.isSourceAllSelect
          ? null
          : this.warningSourceListData
              .map(item => item.dictValue)
              ?.join()
              .split(','),
        // 事件类型
        eventTypeList: this.isEventTypesAllSelect
          ? null
          : this.eventTypesListData.map(item => item.dictValue),
        // 事件来源
        eventSourceList: this.isEventSourceAllSelect
          ? null
          : this.eventSourceListData.map(item => item.dictValue),
      }
      console.info('事件筛选入参---', params, this.alarmDate)
      this.setEventFilterParams(params)
      // 暴露事件筛选数据
      this.$EventBus.$emit('onEventFilterChange', {
        filterState: 'confirm',
      })
    },
    // 获取其他数据
    getOtherData() {
      const data = []
      if (this.deviceCodeListCopy.length > 0) {
        for (const i in this.deviceCodeListCopy) {
          const a = {}
          a.code = this.deviceCodeListCopy[i].code
          a.channelCode = this.deviceCodeListCopy[i].channelCode
          a.deviceCode = this.deviceCodeListCopy[i].deviceCode
          a.searchType = '0'
          data.push(a)
        }
      }
      if (this.radarListCopy.length > 0) {
        for (const i in this.radarListCopy) {
          const a = {}
          a.code = this.radarListCopy[i].code
          a.channelCode = this.radarListCopy[i].channelCode
          a.deviceCode = this.radarListCopy[i].deviceCode
          a.searchType = '0'
          data.push(a)
        }
      }
      if (this.deviceListCopy.length > 0) {
        for (const i in this.deviceListCopy) {
          const a = {}
          a.code = this.deviceListCopy[i].code
          a.channelCode = this.deviceListCopy[i].channelCode
          a.deviceCode = this.deviceListCopy[i].deviceCode
          a.searchType = '0'
          data.push(a)
        }
      }
      return data
    },

    // 重置
    resetAlarmData(itemIndex) {
      if (itemIndex === 0) {
        this.alarmStatusListData = [this.alarmStatusListCopy[0]]
        this.alarmStatusSelect = true
        this.isStatusAllSelect = false
      } else if (itemIndex === 1) {
        this.warningSourceListData = [...this.warningSourceListCopy]
        this.warningSourceSelect = true
        this.isSourceAllSelect = true
      }
      // else if (itemIndex === 2) {
      //   this.alarmTypeListCopy.forEach(item => {
      //     item.checked = '0'
      //   })
      //   this.alarmTypeListData = []
      //   this.alarmTypeSelect = false
      // }
      // else if (itemIndex === 3) {
      //   this.otherSelect = false
      //   this.normalOtherSelect()
      //   this.deviceCodeListCopy = this.functionList[0].data
      //   this.deviceListCopy = this.functionList[1].data

      //   this.deviceCodeListData = []
      //   this.radarListData = []
      //   this.deviceListData = []
      //   this.otherListData = []
      // }
      else if (itemIndex === 4) {
        this.alarmDateSelect = false
        this.getDefaultTime()
        this.alarmListDate = []
        setTimeout(() => this.$refs.alarmDataPicker.handleClose(), 100)
      } else if (itemIndex === 6) {
        this.eventSourceListData = this.eventSourceListCopy
        this.eventSourceSelect = false
        this.isEventSourceAllSelect = true
      } else if (itemIndex === 7) {
        this.eventTypesListData = this.eventTypesListCopy
        this.eventTypesSelect = false
        this.isEventTypesAllSelect = true
      } else {
        // do nothing
      }

      // this.alarmTypeListData = this.alarmTypeListCopy.filter(
      //   item => item.checked === '1'
      // )
      // this.otherListData = this.getOtherData()
    },

    setupView() {
      // 默认时间
      this.getDefaultTime()
      // 初始化处置状态下拉数据
      this.initWarningSourceData()
      // 初始化事件状态下拉数据
      this.initWarningStatusData()
      // 初始化事件来源下拉数据
      this.initEventSourceData()
      // 初始化事件类型下拉数据
      this.initEventTypesData()
      // 查询告警类型
      // this.getWarningType()
      // 查询树信息
      // this.getTreeData()
    },
    // 查询树信息
    async getTreeData() {
      try {
        let params = {}
        let _flag = -1
        const _type = this.showDevinfo.type
        for (const index in this.functionList) {
          const item = this.functionList[index]
          if (index == 0) {
            params = {
              categoryCodeList: [],
              isMonitorFlag: '1',
              statusList: [],
              needDevice: true,
              orderStatus: 1,
              displayMode: 1,
            }
          }
          // const res = await item.queryFun[1](params)
          api
            .post(
              `${this.$env.VUE_APP_REQ_PREFIX_PLAT}/device/getAreaVideoTree`,
              params
            )
            .then(res => {
              console.info(
                '------alarm ----filter-------other -----data----------',
                res
              )
              this._filterTreeData(res.data, index)
              // 有出现两个中国节点情况
              if (res.length > 0) {
                res.data[0].code = '100000-root'
              }
              this.functionList[index].data = res.data
              const arr = []
              res.data.forEach(item2 => {
                arr.push(item2.code)
              })
              this.functionList[index].fristLevel = arr
              if (_type && item.id == _type) {
                _flag = index
              }
            })
        }
        if (~_flag) {
          this._setTreeOpenData(this.showDevinfo, _flag)
        } else {
          this.normalOtherSelect(_flag)
        }
      } catch (e) {
        console.error(e)
      }
    },
    // 过滤树数据，只保留设备，不要通道，并记录设备数据
    _filterTreeData(data, i) {
      ;(function fn(arr) {
        arr.find(item => {
          if (item.deviceCode && !item.channelCode) {
            item.list = []
          }
          if (item.list && item.list.length) {
            fn(item.list)
          }
        })
      })(data)
    },
    // 设置树展开的数据
    _setTreeOpenData(data, i) {
      this.activeName = data.type + ''
      const _treeCell = data2 => {
        if (data2.deviceCode) {
          if (!this.functionList[i].selectData.includes(data2.deviceCode)) {
            this.functionList[i].selectData = [
              ...this.functionList[i].selectData,
              data2.deviceCode,
            ]
          }
        } else {
          const newcode = this._getSelectCode(
            this.functionList[i].data,
            data2.deviceCode
          )
          if (!this.functionList[i].selectData.includes(newcode)) {
            this.functionList[i].selectData = [
              ...this.functionList[i].selectData,
              newcode,
            ]
          }
        }
        this.getSelectFirstDevName(i, this.functionList[i].selectData)
        this.otherSelect = true
        // 定时处置
        setTimeout(() => {
          this.handleNodeClick(+i, true)
          this.submitGoToGetAlarm(5)
        })
      }
      if (this.isCust) {
        this.$nextTick(() => {
          _treeCell(data)
        })
        return
      }
      getDeviceChannel4Tt({ deviceCode: data.deviceCode }).then(res => {
        if (res.code === 200) {
          data.provinceId = res.data.provinceId
          data.cityId = res.data.cityId
          data.countyId = res.data.countyId
          data.townCode = res.data.townCode
          this.videoTreeOpen(data, i).then(() => {
            _treeCell(data)
          })
        } else {
          $message.a(res)
          return
        }
      })
    },

    // 重置“其他”类型选择
    normalOtherSelect(_flag) {
      // 这里其实就应该是重置为空数组吧？“全部”的情况下不应该是“没有查询条件”吗？
      setTimeout(() => {
        this.functionList[0].selectData = []
      })
    },

    _getSelectCode(list, code) {
      let selectCode = ''
      ;(function fn(arr) {
        arr.find(item => {
          if (selectCode) {
            return true
          }
          if (item.deviceCode == code) {
            selectCode = item.code
            return true
          }
          if (item.list && item.list.length) {
            fn(item.list)
          }
        })
      })(list)
      return selectCode
    },

    // 时间选择
    changeTime() {
      this.alarmDateSelect = false
      this.submitGoToGetAlarm()
    },
    // 隐藏时间
    hideDate() {
      this.alarmDateShow = false
      // $(".el-picker-panel").removeClass("button-group-yy")
    },
    // 格式化日期
    formatYearMonthDay(strDate, year, monthNum) {
      let newYear
      let newMonth
      let newDay
      if (monthNum <= 0) {
        newYear = year - 1
        newMonth = 12 + monthNum
        newDay = strDate
      } else {
        newYear = year
        newMonth = monthNum
        newDay = strDate
      }
      if (
        newMonth === 4 ||
        newMonth === 6 ||
        newMonth === 9 ||
        newMonth === 11
      ) {
        if (strDate === 31) {
          newDay = 30
        }
      } else if (newMonth === 2) {
        if (strDate >= 28) {
          newDay = 28
        }
      } else {
        // Add the missing "else" clause.
      }
      return { newYear, newMonth, newDay }
    },
    // 时空追踪初始话处置状态数据
    initWarningSourceDataSpaceTimeTrace() {
      this.warningSourceListCopy = this.getEvtOrderStatusList
      this.warningSourceListData = this.warningSourceListCopy
    },
    // 初始化处置状态下拉数据
    initWarningSourceData() {
      this.initWarningSourceDataSpaceTimeTrace()
    },
    // 初始化事件来源下拉数据
    initEventSourceData() {
      this.eventSourceListCopy = this.getEvtSourceList
      this.eventSourceListData = this.eventSourceListCopy
    },
    // 初始化事件类型下拉数据
    initEventTypesData() {
      this.eventTypesListCopy = this.getEvtTypeList
      this.eventTypesListData = [...this.eventTypesListCopy]
    },
    // 初始化事件状态下拉数据
    initWarningStatusData() {
      this.alarmStatusListCopy = alarmStatusListDataDefault
      this.alarmStatusListData = [alarmStatusListDataDefault[0]]
      this.alarmStatusSelect = true
      this.isStatusAllSelect = false
    },

    /**
     * 查询告警类型
     */
    getWarningType() {
      const params = {}
      api
        .post(
          `${this.$env.VUE_APP_REQ_PREFIX_PLAT}/alarm/orderForTrade/findOrderAlarm`,
          params
        )
        .then(res => {
          console.info('---------getWarningType--------------', res)
          if (res.code == 200) {
            // 铁塔视角不能查看人工告警类型
            const alarmList = res.data.filter(
              item =>
                !(item.alarmSource == '2' && item.tenantId && item.industryCode)
            )
            // 添加过滤条件
            const alarmListSearch = alarmList.filter(item =>
              item.typeName.includes(this.keywordValue)
            )
            this.alarmTypeListCopy = []
            // 去掉多余字段 赋值告警类型
            for (const element of alarmListSearch) {
              const a = {
                dictValue: element.typeValue,
                dictLabel: element.typeName,
                checked: '0',
              }
              this.alarmTypeListCopy.push(a)
              this.alarmTypeListShow.push(a)
            }
          } else {
            this.$message({
              type: 'error',
              message: res.msg || '告警类型查询失败！',
            })
          }
        })
    },

    searchClick() {
      this.alarmTypeListShow = []
      this.alarmTypeListShow = this.alarmTypeListCopy.filter(
        o => o.dictLabel.indexOf(this.keywordValue) !== -1
      )
    },
    clearTypeListShow() {
      this.alarmTypeListShow = []
    },
    /**
     * 查询告警类型集合
     */
    async findOriginalTypeList() {
      /* 获取枚举值 */
      try {
        let res = null
        if (this.isCust) {
          res = await findOrderAlarm({})
        } else {
          // res = await findOrderAlarmTT({});
        }
        console.info('---------getWarningType--------------', res)
        if (res.code == '200') {
          // 铁塔视角不能查看人工告警类型
          const alarmList = res.data.filter(
            item =>
              !(item.alarmSource == '2' && item.tenantId && item.industryCode)
          )
          // 添加过滤条件
          const alarmListSearch = alarmList.filter(item =>
            item.typeName.includes(this.keywordValue)
          )
          this.alarmTypeListCopy = []
          // 去掉多余字段 赋值告警类型
          for (const element of alarmListSearch) {
            const a = {
              dictValue: element.typeValue,
              dictLabel: element.typeName,
              checked: '0',
            }
            this.alarmTypeListCopy.push(a)
            this.alarmTypeListShow.push(a)
          }

          // 非人工告警不允许选择人工告警类型 this.detailAlarmTypeList = res.data.filter(item =>!(item.alarmSource == '2' && item.tenantId && item.industryCode));
        } else {
          throw new Error(res.msg)
        }
      } catch (error) {
        throw new Error(error)
      }
    },
    // 点击树
    clickTreeItem(data, node, index) {
      if (index !== 0) {
        return
      }
      if (!data.firstRow && node && !data.deviceCode && !this.isCust) {
        this.videoTreeOpen(data, index)
      }
    },
    // 展开节点
    videoTreeOpen(data, index) {
      return new Promise((r, l) => {
        if (+index !== 0 || this.isCust) {
          return r()
        }
        const _fn = par => {
          queryDevicesByAreaParam(par).then(res => {
            if (res.code === 200) {
              ;(res.data || []).forEach(item => {
                // 不显示通道
                item.list = []
                const parentNode = this.$refs.functionTree[index].getNode(
                  item.parentCode
                )
                if (this.checkReturn(parentNode, item)) {
                  return
                }
                this.$refs.functionTree[index].append(item, item.parentCode)
                // const recursionFind = (dataarr, targetId) => {
                //   if (dataarr?.length > 0) {
                //     let result;
                //     dataarr.find(o => {
                //       if (o.deviceCode === targetId) {
                //         result = o;
                //         return true;
                //       }

                //       if (o.list) {
                //         result = recursionFind(o.list, targetId);
                //         if (result) {
                //           return true;
                //         }
                //       }
                //     });
                //     return result;
                //   }
                // };
                // const targetData = recursionFind(this.functionList[index].data, item.deviceCode);
                // if (targetData) {
                //   targetData.list.push(item);
                // }
                // this.functionList[index].data = [...this.functionList[index].data];
              })
              this.$nextTick(() => {
                r()
              })
            }
          })
        }
        if (data.level === '3') {
          console.log('=========', data)
          _fn({
            provinceId: data.provinceId,
            cityId: data.cityId,
            countyId: data.countyId,
            townCode: data.townCode,
          })
        }
      })
    },
    // 校验
    checkReturn(parentNode, item) {
      if (!parentNode) {
        return true
      }
      if (parentNode.data.list?.find(ii => ii.deviceCode === item.deviceCode)) {
        return true
      }
      return false
    },
    // 取消设备树的选中
    cancelDevTreeSelected(id, code) {
      let _i = 0
      const targetFunction = this.functionList.find((item, i) => {
        _i = i
        return item.id == id
      })
      if (!targetFunction) {
        return
      }
      const _arr = targetFunction.selectData || []
      _arr.findLast((item, i) => {
        if (item == code) {
          _arr.splice(i, 1)
          return true
        }
      })
      targetFunction.selectData = _arr

      setTimeout(() => {
        this.handleNodeClick(_i, true)
        this.submitGoToGetAlarm(5)
      }, 300)
    },
    // 获取第一个选中设备的名称
    getSelectFirstDevName(index, arr) {
      let _name = ''
      if (arr.length) {
        const _code = arr[0]
        ;(function fn(arr2) {
          arr2.find(item => {
            if (_name) {
              return true
            }
            if (item.deviceCode == _code) {
              _name = item.name
              return true
            }
            if (item.list && item.list.length) {
              fn(item.list)
            }
          })
        })(this.functionList[index].data)
      } else {
        _name = ''
      }
      this.functionList[index].selectFirstDevName = _name
    },
    // “其他”类型筛选数据变动方法
    changeFunctionSelectData(i, newdata) {
      this.functionList[i].selectData = newdata
      this.handleNodeClick(i)
    },
  },
}
</script>

<style lang="less" scoped>
@IWLYTooltipBgColor: rgba(0, 34, 27, 0.95);
@IWLYDisableTextColor: rgba(255, 255, 255, 0.7);
@IWLYPopBgColor: linear-gradient(
  180deg,
  rgba(33, 45, 62, 0.74) 0%,
  rgba(33, 45, 62, 0.74) 21%,
  #212d3e 100%
);

.alarm-filter-dialog-view {
  top: 0;
  width: fit-content;
  background: rgba(23, 37, 55, 0.9);
  border-radius: 8px;
  border: 1px solid transparent; // #075b4a;
  position: absolute;
  right: 260px;
  margin: 0 auto;
  align-content: center;
  text-align: center;
  flex-direction: column;
  flex-wrap: wrap;
  justify-content: center;
  font-size: 24px;
  font-weight: 400;
  line-height: 36px;
  z-index: 105;

  .alarm-filter-container {
    display: flex;
    align-items: center;
  }
}

// 树组件覆写
/deep/.el-tree {
  background: transparent !important;
  color: #fff;

  .el-tree-node:focus > .el-tree-node__content {
    background: transparent;
  }

  .el-tree-node__content:hover {
    background: transparent;
  }
}

/deep/ .el-dropdown-link {
  color: #e8f2fe;
  border-radius: 6px;
}

/deep/ .select-input-title {
  color: #e8f3fe;
  font-size: 14px;
  margin-bottom: 6px;
}

/deep/ .select-input-content {
  padding: 5px;
  width: 100%;
  height: 36px;
  line-height: 36px;
  background: lightslategrey;
}

/deep/ .el-dropdown-menu {
  border-radius: 6px;
  background: #172537;
  margin-top: 30px;
}

/deep/ .select-container {
  max-height: 400px;
  overflow: auto;
  border: none;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  display: flex;
  flex-flow: column;
}

/deep/ .select-container .select-item {
  padding: 6px 6px;
  color: white;

  .el-checkbox {
    color: white;
  }
}

/deep/ .select-container .select-item:hover {
  background: rgba(130, 181, 255, 0.1);
}

/deep/ .select-container .select-input-item {
  width: 240px;
  padding: 0px 6px;
  margin-top: 10px;

  .el-input__inner {
    background-color: #3b5474 !important;
  }
}

.select-container .collapseTitle {
  display: flex;
  align-items: center;
  height: 20px;
  line-height: 20px;
  flex-grow: 1;
  width: 0;
  cursor: default;

  > span {
    display: flex;
    align-items: center;
    padding: 0 5px;
    color: #e8f3fe;
    background: #1373e6 100%;
    border-radius: 4px;

    + span {
      margin-left: 6px;
    }
  }

  .collapseTitleName {
    max-width: 70%;

    span {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .collapseTitleClose {
      cursor: pointer;
      flex-shrink: 0;
      height: 18px;
      width: 18px;
      background: url('~@/assets/images/alarmEvent/alarm/del.png');
    }
  }
}

/deep/ .select-container .selectedType,
/deep/ .select-container .selected {
  color: #409eff;
}

/deep/ .title {
  padding: 5px;
  position: relative;
  flex: 1;
  justify-content: center;
  display: flex;
  align-items: center;

  .title {
    &::before {
      content: '';
      width: 1px;
      height: 10px;
      background: rgba(232, 243, 254, 0.35);
      position: absolute;
      left: 0;
      top: calc(50% - 5px);
    }
  }
}

.alarm-title {
  width: 150px;
  .el-dropdown {
    line-height: 28px;
    flex: 1;
    border-radius: 5px;

    &:hover {
      background: rgba(43, 68, 100, 0.8);
    }

    // 覆盖dropdown的箭头
    .el-icon-arrow-up {
      transform: rotate(180deg);
      background-image: url('~@/assets/images/alarmEvent/alarm/arrow_up2.png');
      background-size: 18px;
      background-position: center;
      background-repeat: no-repeat;
      width: 18px;
      height: 18px;
      margin-left: 0;
      vertical-align: sub;

      &::before {
        content: '';
      }
    }

    .el-icon-arrow-down {
      background-image: url('~@/assets/images/alarmEvent/alarm/arrow_up2.png');
      background-size: 18px;
      background-position: center;
      background-repeat: no-repeat;
      width: 18px;
      height: 18px;
      margin-left: 0;
      vertical-align: sub;

      &::before {
        content: '';
      }
    }
  }
}

.alarm-title /deep/ .title-selected {
  color: #409eff !important;

  .el-icon-arrow-up,
  .el-icon-arrow-down {
    background-image: url('~@/assets/images/alarmEvent/alarm/arrow_up.png');
  }
}

.titleTime {
  width: 280px;
  flex-shrink: 0;
  flex-basis: auto;
  flex-grow: 0;
  border-radius: 5px;

  .date-hover-bg {
    border-radius: 5px;

    // background: #2a4364;
    &:hover {
      background: rgba(134, 176, 227, 0.24);
    }
  }

  // 时间选择器覆写
  /deep/.el-date-editor {
    .el-range-input {
      color: #fff;
      background: transparent;
    }
    .el-range__icon {
      line-height: 20px;
    }
    .el-range-separator {
      color: #fff;
      width: 20px;
    }

    &.el-input__inner {
      border-color: transparent;
      background: rgba(43, 68, 100, 0.8);
      color: rgba(255, 255, 255, 0.7);
      vertical-align: top;
    }

    // 设计师好像要求的是这个图标，对使用这个图标的日期选择器做统一处理
    i.iconfont_tools.icon-tongyong-shaixuanriqi {
      font-size: 18px;
      line-height: 26px;
      margin-right: 12px;

      &::before {
        // vertical-align: middle;
        // margin-top: 4px;
        margin-left: 12px;
      }
    }
  }

  // 时间弹出面板覆写
  /deep/.el-date-range-picker.el-picker-panel {
    width: 490px;
    background: @IWLYPopBgColor;
    border-color: transparent;
    color: #fff;

    &.has-sidebar {
      width: 576px;

      .el-picker-panel__content.el-date-range-picker__content {
        padding-top: 0;
        padding-bottom: 6px;
      }
    }

    .popper__arrow {
      display: none;
    }

    .el-date-range-picker__time-header {
      border-color: #3d4756;

      .el-icon-arrow-right {
        color: #e8f3fe;
      }
    }

    .el-picker-panel__sidebar {
      background: transparent;

      .el-picker-panel__shortcut {
        color: #fff;
      }
    }

    .el-picker-panel__body {
      min-width: auto;
    }

    .el-date-range-picker__header {
      color: #fff;
      padding: 0 12px;

      div {
        font-size: 12px;
        margin-left: 40px;
        margin-right: 40px;
      }

      .el-picker-panel__icon-btn {
        color: #fff;
      }
    }

    .el-date-table {
      tr {
        th {
          color: #fff;
          text-align: center;
        }
      }

      th {
        padding: 0;
      }

      td {
        padding: 0;

        div {
          height: 36px;
        }

        span {
          width: 32px;
          height: 32px;
          line-height: 32px;
        }

        &.today {
          span {
            color: rgba(43, 68, 100, 1);
          }
        }

        &.today.in-range {
          span {
            color: #fff;
          }
        }
      }
    }

    .el-date-table td.available:hover {
      color: #ffffff;

      span {
        background-color: #4f9fff;
      }
    }

    .el-date-table td.disabled div {
      background-color: transparent;
    }

    .el-date-table td.in-range div {
      background-color: rgba(79, 159, 255, 0.4);
    }

    .el-date-table td.start-date span,
    .el-date-table td.end-date span {
      background: rgba(79, 159, 255, 1);
    }
  }

  /deep/ .el-date-editor {
    height: 26px;
    width: 250px;
    // background: #2a4364;

    i.iconfont_tools.icon-tongyong-shaixuanriqi {
      line-height: 20px;
    }

    span.el-range-separator {
      line-height: 20px;
      color: #ffffff;
    }

    .el-range-input {
      background: none;
      color: #ffffff;
    }
  }

  /deep/ .el-input__inner {
    background-color: transparent !important;
    border: none;
  }
}

/deep/ .titleSelected {
  // background: rgba(134, 176, 227, 0.24);
  background: rgba(43, 68, 100, 0.8);
}

/deep/ .selected:after {
  position: absolute;
  right: 20px;
  font-family: element-icons, sans-serif;
  content: '\e6da';
  font-size: 12px;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/deep/ .el-range__close-icon {
  line-height: 26px !important;
}

/deep/ .el-form-item {
  margin-bottom: 0px !important;
  height: auto;
}

/deep/ .el-form-item .el-form-item__content {
  padding: 0 !important;
  height: 26px;
}

.drop-down-select {
  padding: 0 !important;
  margin: 9px 0 0 0 !important;
  background: linear-gradient(
    180deg,
    rgba(0, 19, 30, 0.95) 0%,
    rgba(0, 19, 30, 0.74) 21%,
    #00131e 100%
  );
  border-radius: 8px;
  border: 1px solid transparent; // #075b4a;
  min-width: 130px;

  /deep/ .popper__arrow {
    display: none;
  }
}

.other-dropdown-menu {
  transform: translate(6px, 0); // 对齐最右侧。要求的位置不在dropdown范围内

  .select-container {
    overflow: hidden;
  }
}

.alarm-type-dropdown-menu {
  transform: translate(120px, 0); // 对齐最右侧。要求的位置不在dropdown范围内
}

//搜索
.input-bg {
  width: 300px;
  padding: 6px 6px 0 6px;
  border-radius: 8px 8px 0 0;

  .input-box {
    padding: 3px;
    background: rgba(43, 68, 100, 0.8);
    display: flex;
    flex-wrap: nowrap;
    justify-content: left;
    align-items: center;
    border-radius: 3px;
    font-size: 14px;

    /deep/ .el-input__inner {
      height: 24px;
    }
  }

  .select-container {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    max-height: 367px;
  }

  .more-tag,
  .first-tag {
    border: none;
    max-width: 100px;
    position: relative;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 1px 8px;

    /deep/ .el-tag__close.el-icon-close {
      height: 18px;
      width: 18px;
      margin: 0px auto;
      position: absolute;
      top: 3px;
      right: 1px;
    }
  }

  .more-tag {
    margin-left: 4px;
  }

  .first-tag {
    padding-right: 11px !important;
  }

  input:disabled::-webkit-input-placeholder {
    -webkit-text-fill-color: rgba(255, 255, 255, 0);
  }
}

//搜索清除
.input-bg .search-input /deep/ .el-input__clear {
  line-height: 24px;
  color: #e8f3fe;
}

//搜索
.input-bg .search-input /deep/ .el-input__inner {
  color: #e8f3fe;
  text-align: left;
  border-radius: 3px;
  font-size: 14px;
  padding-right: 10px !important;
  border: none;
  background: transparent;
}

//搜索按钮
.input-bg .el-button--mini {
  width: 30px;
  height: 30px;
  color: #e8f3fe;
  font-size: 14px;
  outline: none;
  cursor: pointer;
  padding: 0;
  position: absolute;
  right: 0;
  top: 0;
  background: none;
  border: none;
}

.searchTypeDiv {
  cursor: pointer;

  /deep/.el-input__icon {
    line-height: 20px;
  }
}

.searchTypeIcon {
  font-size: 18px;
  color: #e8f3fe;
}

//确定--取消
.judgeBtnDiv {
  display: flex;
  justify-content: flex-end;
  flex-wrap: nowrap;
  padding: 6px;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;

  /deep/ .el-button {
    padding: 4px 10px;
    height: 24px;
    color: #fff;
    background-color: #212d3d;
    border-color: #212d3d;
  }

  /deep/.el-button--primary {
    background-color: #409eff !important;
    border-color: #409eff !important;
  }
}

/deep/ .el-collapse-item__header {
  display: flex;
  align-items: center;
  height: 30px;
  background: rgba(130, 181, 255, 0.2);
  color: #e8f2fe;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  border-radius: 3px;
  border-bottom: none !important;
  padding-left: 10px;
}

/deep/ .el-tree-node__content {
  display: flex;
  align-items: center;
  padding-right: 8px;
  height: 30px;
  cursor: pointer;
  background: #0f1926;
  color: #e8f2fe;
  padding-bottom: 0 !important;
}

/deep/ .el-collapse-item__content {
  padding-bottom: 0 !important;
  font-size: 13px;
  color: #303133;
  line-height: 1.769230769230769;
}

/deep/ .el-tree-node__children .el-tree-node__content {
  padding-right: 0;
}

/deep/ .el-tree-node__content:hover {
  background: #0f1926;
}

/deep/ .el-tree-node:focus > .el-tree-node__content {
  background: none;
}

/deep/ .el-tree-node.is-current > .el-tree-node__content {
  background: #0f1926;
}

/deep/ .el-tree-node__children::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  border-radius: 3px;
}

/deep/ .el-tree-node__children::-webkit-scrollbar-thumb {
  border-radius: 3px;
  background-color: #0f1926;
  border: 0;
}

/deep/ .el-tree-node__children::-webkit-scrollbar-thumb:hover {
  background-color: #0f1926;
}

/deep/ .tree-fixed-first-level > .el-tree-node > .el-tree-node__children {
  overflow-y: scroll !important;
}

/deep/ .el-collapse {
  border-top: none !important;
  border-bottom: none !important;
}

.el-checkbox-group {
  display: flex;
  flex-direction: column;
}

.el-checkbox {
  margin-right: 0;
}

.custom-fn-ly-tree {
  .custom-tree-node {
    display: flex;
    align-items: center;
    overflow: hidden;

    .tree-img {
      height: 16px;
      padding-right: 2px;
    }

    .custom-tree-right {
      display: none;
    }

    &:hover {
      .custom-tree-right {
        display: block;
      }
    }

    span {
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}

// 赤土黄样式
.theme-terracotta {
  .alarm-filter-dialog-view {
    opacity: 0.9;
    border: 1px solid rgba(110, 103, 78, 1);
    background: linear-gradient(
      155.66deg,
      rgba(66, 59, 29, 1) 13.68%,
      rgba(40, 33, 14, 1) 51.69%,
      rgba(0, 0, 0, 1) 89.25%
    );
    .titleTime {
      .date-hover-bg:hover {
        background: rgba(100, 86, 46, 0.4);
      }
      /deep/ .el-date-range-picker,
      .el-picker-panel {
        background: linear-gradient(
          179.79deg,
          rgba(66, 59, 29, 1) 0.2%,
          rgba(40, 33, 14, 1) 50.33%,
          rgba(0, 0, 0, 1) 98.65%
        );
        .el-date-table td.start-date span,
        .el-date-table td.end-date span {
          background: rgba(159, 152, 83, 1);
        }
        .el-date-table td.in-range div {
          background: rgba(122, 113, 61, 1);
        }
        .el-date-table td.available:hover {
          color: #ffffff;

          span {
            background-color: rgba(159, 152, 83, 1);
          }
        }
        .el-date-table {
          td {
            &.today {
              span {
                color: rgba(255, 250, 40, 1);
              }
            }
          }
        }
      }
    }
    .alarm-title {
      .el-dropdown:hover {
        background: rgba(100, 86, 46, 0.4);
      }
    }
    .alarm-title /deep/ .title-selected {
      color: rgb(255, 250, 40) !important;

      .el-icon-arrow-up,
      .el-icon-arrow-down {
        background-image: url('~@/assets/images/alarmEvent/alarm/arrow_up2.png');
      }
    }
    .titleSelected {
      background: rgba(100, 86, 46, 0.4);
    }
  }
  .drop-down-select {
    border: 1px solid rgba(110, 103, 78, 1);
    background: linear-gradient(
      155.66deg,
      rgba(66, 59, 29, 1) 13.68%,
      rgba(40, 33, 14, 1) 51.69%,
      rgba(0, 0, 0, 1) 89.25%
    );
  }

  //确定--取消
  .judgeBtnDiv {
    /deep/ .el-button {
      border: 0.5px solid rgba(110, 103, 78, 1);
      background: rgba(100, 86, 46, 1);
      opacity: 0.71;
      color: rgba(228, 231, 193, 1);
      font-family: 'PingFang SC', sans-serif;
      font-weight: 400;
      font-size: 12px;
    }

    /deep/.el-button--primary {
      border-width: 0.5px;
      border-style: solid;
      border-image-source: linear-gradient(
        180deg,
        rgba(255, 238, 177, 0.39) 0%,
        rgba(255, 248, 217, 0) 100%
      );
      border-image-slice: 1;
      box-sizing: border-box;
      background: radial-gradient(
          58.33% 58.33% at 50% 4.17%,
          rgba(255, 224, 72, 0.34) 0%,
          rgba(255, 241, 126, 0.1) 100%
        ),
        linear-gradient(
          180deg,
          rgba(255, 219, 117, 0.57) 0%,
          rgba(0, 0, 0, 0) 100%
        ) !important;
    }
  }
  //搜索
  .input-bg {
    .input-box {
      background: rgba(100, 86, 46, 0.4);
    }
  }

  //搜索清除
  .input-bg .search-input /deep/ .el-input__clear {
    line-height: 24px;
    color: #e8f3fe;
  }

  //搜索
  .input-bg .search-input /deep/ .el-input__inner {
    color: #e8f3fe;
    text-align: left;
    border-radius: 3px;
    font-size: 14px;
    padding-right: 10px !important;
    border: none;
    background: transparent;
  }

  //搜索按钮
  .input-bg .el-button--mini {
    width: 30px;
    height: 30px;
    color: #e8f3fe;
    font-size: 14px;
    outline: none;
    cursor: pointer;
    padding: 0;
    position: absolute;
    right: 0;
    top: 0;
    background: none;
    border: none;
  }
}
</style>
