<template>
  <!--
    封装的树形选择器组件。这个树形选择器不支持完整的树功能，请注意参考其props
    只接受叶子节点勾选数据。呈现方式一定是multiple/collapse-tags
    插槽透传，可以当作直接对el-tree里塞东西来控制树的节点数据
  -->
  <el-select
    ref="select"
    class="iwmiTreeSelect"
    :popper-class="'iwmiTreeSelect iwhale-speciesLYstyle ' + popperClass"
    :value="nowValue"
    :value-key="nodeKey"
    :placeholder="placeholder"
    size="mini"
    :clearable="clearable"
    :multiple="multiple"
    :collapse-tags="multiple"
    :disabled="disabled"
    :filterable="filterable"
    :filter-method="filterMethod"
    :popper-append-to-body="popperAppendToBody"
    style="width: 100%;"
    @clear="clear"
    @remove-tag="onRemoveTag"
    @visible-change="visibleChange"
  >
    <el-option
      ref="option"
      class="tree-select__option"
      :value="optionData.value"
      :label="optionData.label"
    >
      <el-tree
        ref="tree"
        :class="['tree-select__tree', `tree-select__tree--${multiple ? 'checked' : 'radio'}`]"
        :node-key="nodeKey"
        :data="dataSource"
        :props="props"
        :indent="indent"
        :default-expanded-keys="defaultExpandedKeys"
        :show-checkbox="multiple"
        :highlight-current="!multiple"
        :expand-on-click-node="multiple"
        :filter-node-method="filterNode"
        @node-expand="onNodeExpand"
        @node-click="handleNodeClick"
        @check-change="handleCheckChange"
      >
        <template v-for="(slot, slotName) in $scopedSlots" #[slotName]="slotProps">
          <slot :name="slotName" v-bind="slotProps" />
        </template>
      </el-tree>
    </el-option>
  </el-select>
</template>

<script>
// 引入依赖组件和工具
import $ from 'jquery'
export default {
  name: 'StyledTreeSelect',
  // 父组件传参
  props: {
    // v-model绑定
    value: {
      type: [String, Number, Array],
      default: '',
    },
    defaultExpandedKeys: Array,
    placeholder: String,
    popperClass: {
      type: String,
      default: '',
    },
    popperAppendToBody: {
      type: Boolean,
      default: true,
    },
    clearable: {
      type: Boolean,
      default: false,
    },
    indent: {
      type: Number,
      default: 10,
    },
    equalWidth: Boolean, // 下拉框浮层和输入框的部分是否等宽
    multiple: {
      type: Boolean,
      default: false,
    },
    dataSource: {
      type: Array,
      default: function () {
        return []
      },
    },
    // 每个树节点用来作为唯一标识的属性
    nodeKey: {
      type: [String, Number],
      default: 'id',
    },
    filterable: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    // tree的props配置
    props: {
      type: Object,
      default: function () {
        return {
          label: 'label',
          children: 'children',
        }
      },
    },
  },
  // 组件变量区域
  data() {
    return {
      nowValue: null, // 树选中的值
      optionData: {
        // 表示被选中的第一个选项，这是一个虚拟选项用于回显，没有业务数据上的意义
        value: '',
        label: '',
      },
      filterFlag: false, // 是否正在过滤
    }
  },
  // 监听器
  watch: {
    // 监听dataSource变化
    dataSource() {
      this._findFirstOptionData()
    },
    // 监听value变化
    value(v) {
      const newV = Array.isArray(v) ? v.map(o => o?.[this.nodeKey] || o) : v
      this.nowValue = newV
      if (newV) {
        this.$refs.tree.setCheckedKeys(newV)
        this._findFirstOptionData()
      }
    },
  },
  // 组件的方法区域
  methods: {
    // 以下为提供el-tree相同的透传方法
    onNodeExpand(data, node) {
      this.$emit('node-expand', data, node)
    },
    // 加进树
    append(item, parentCode) {
      return this.$refs.tree.append(item, parentCode)
    },
    // 获取节点
    getNode(key) {
      return this.$refs.tree.getNode(key)
    },
    // 获取选中的节点
    getCheckedNodes() {
      return this.$refs.tree.getCheckedNodes(true, false)
    },
    // 以上为提供el-tree相同的透传方法
    _findFirstOptionData() {
      const v = this.nowValue

      const firstId = Array.isArray(v) ? v[0]?.[this.nodeKey] || v[0] : v
      const target = this.recursionFind(this.dataSource, firstId)
      if (target) {
        this.optionData.value = firstId
        this.optionData.label = target[this.props.label]
      }
    },
    recursionFind(dataarr, targetId) {
      if (dataarr?.length > 0) {
        let result
        dataarr.find(o => {
          if (o[this.nodeKey] === targetId) {
            result = o
            return true
          }

          if (o[this.props.children]) {
            result = this.recursionFind(o[this.props.children], targetId)
            if (result) {
              return true
            }
          }
        })
        return result
      }
    },
    // 点击树节点
    handleNodeClick() {
      if (this.multiple) {
        return
      }
      this.nowValue = data[this.nodeKey]
      this.optionData.value = this.nowValue
      this.optionData.label = data[this.props.label]
      this.$emit('input', data[this.nodeKey])
      this.$refs.select.visible = false
    },
    // 点击树节点
    handleCheckChange() {
      const nodes = this.$refs.tree.getCheckedNodes(true, false)
      const value = nodes.map(o => o[this.nodeKey])
      this.nowValue = value
      this._findFirstOptionData()

      this.$emit('input', value)
    },
    // 树过滤
    visibleChange(e) {
      if (e) {
        // 获取树对象
        const tree = this.$refs.tree
        this.filterFlag && tree.filter('')
        // filterFlag置为false
        this.filterFlag = false
        let selectDom = null
        if (this.multiple) {
          selectDom = tree.$el.querySelector('.el-tree-node.is-checked')
        } else {
          selectDom = tree.$el.querySelector('.is-current')
        }
        setTimeout(() => {
          // 滚动到对应元素
          this.$refs.select.scrollToOption({ $el: selectDom })
        }, 0)

        // 阻止下拉框浮层这个层级的事件冒泡
        if (!$(this.$refs.option.$el).attr('flag')) {
          $(this.$refs.option.$el)
            .parents('.el-select-dropdown')
            .mousedown(function (e2) {
              return false
            })
          $(this.$refs.option.$el).attr('flag', true)
        }

        if (this.equalWidth) {
          // 如果要求和输入框等宽，那么用js设置宽度
          $(this.$refs.option.$el)
            .parents('.el-select-dropdown')
            .width($(this.$refs.select.$el).width())
        }
      }
    },
    // 移除tag
    onRemoveTag() {
      // 多选模式下移除单个tag
      // 在collapse-tag下一定是移除第一个选项，直接切就行
      if (Array.isArray(this.nowValue)) {
        const newnodes = this.$refs.tree.getCheckedNodes(true, false).slice(1)
        this.$refs.tree.setCheckedNodes(newnodes)
        this.nowValue = newnodes.map(o => o[this.nodeKey])
        this._findFirstOptionData()
        this.$emit('input', this.nowValue)
      }
    },
    // 清空
    clear() {
      const clearVal = this.multiple ? [] : ''
      this.nowValue = clearVal
      this.$emit('input', clearVal)
    },
    // 树过滤
    filterMethod(val) {
      this.filterFlag = true
      this.$refs.tree.filter(val)
    },
    // 树过滤
    filterNode(value, data) {
      if (!value) {
        return true
      }
      const label = this.props.label || 'name'
      return data[label].indexOf(value) !== -1
    },
  },
}
</script>

<style lang="less" scoped>
// 树组件覆写
/deep/.el-tree {
  background: transparent;
  color: #fff;

  .el-tree-node:focus > .el-tree-node__content {
    background: transparent;
  }

  .el-tree-node__content:hover {
    background: transparent;
  }
}

.iwmiTreeSelect {
  .el-select__tags .el-tag {
    max-width: 70%;

    // 设计要求标签要与上面的title对齐
    &:first-of-type {
      margin-left: 0;
    }
  }

  .popper__arrow,
  .el-scrollbar__bar {
    display: none;
  }

  &.el-popper[x-placement^='bottom'] {
    margin-top: 4px;
  }
}

.iwmiTreeSelect.iwhale-speciesLYstyle.el-select-dropdown {
  transition: none; // 因为el-tree里有元素对transform动画会产生撕裂，这里为了偷懒直接把动画关闭来规避这个问题
  max-width: 320px;

  .el-select-dropdown__item {
    height: auto;
    padding-left: 0px;
    padding-right: 10px;
  }

  .el-select-dropdown__item.hover,
  .el-select-dropdown__item.selected.hover {
    background-color: transparent;
  }

  .el-tree-node.is-focusable > .el-tree-node__content:hover {
    background: rgba(134, 176, 227, 0.1);
  }
}
</style>
