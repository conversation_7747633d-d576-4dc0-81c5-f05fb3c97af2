<template>
  <div v-loading="sysLoading" class="screen-container">
    <div class="preload" />
    <!-- 
      showMouseLocation 右下角显示当前鼠标经纬度位置，默认不开启 
      showMapControl 初始化完成后，是否在右下角显示地图切换、比例尺工具。默认开启
      tileModes 定义可供切换的地图类型。0、1、2、3 分别代表 常规地图、卫星地图、三维地形图、二维地形图。默认启用全部
    -->
    <!-- chooseMapMemoryKey="integratedMonitor" -->
    <RemoteComponentSyncLoader
      v-if="components['common-comp-map']"
      showMapControl
      mapToolElement=".screen-container .maptooltele"
      :class="['main-map', $store.state.event.evtListOpen && 'fix-map-tool']"
      :config="components['common-comp-map']"
      :mapId="mapId"
      chooseMapMemoryKey
      :defaultTileMode="5"
      :tileModes="tileModes"
    />
    <!-- 切换到3D地形图时使用 -->
    <!-- <div :class="['maptooltele', 'fix-map-tool']" /> -->
    <!-- 内容 -->
    <template v-if="mapComLoaded && loaded && !sysLoading">
      <WindowWrapLeftSide :mapId="mapId" ref="leftWrap" />
      <WindowWrapBottomSide ref="bottomWrap" />
      <Map :mapId="mapId" :onMapLoad="onMapLoad" />
      <template v-if="mapLoaded">
        <SideCtlPanel :mapId="mapId" />
        <!-- 左侧 -->
        <div :class="['left-wrapper', leftHide && 'hide']" v-if="showLeft">
          <div id="comp-left-container" class="left-wrapper-son">
            <template v-for="(item, index) in componentConfig.left">
              <RemoteComponentSyncLoader
                v-if="item.monitorComp"
                :key="`leftCompenent_${index}`"
                :config="getleftRightComponents(item.compCode)"
                :provinceId="provinceId"
                :cityId="cityId"
                :intervalTime="120000"
                :title="item.compName"
                :outerWidth="item.defaultWidth ? Number(item.defaultWidth) : 0"
                :outerHeight="item.defaultHeight ? Number(item.defaultHeight) : 0"
                :compUrl="item.compUrl"
              />
              <template v-else-if="!item.monitorComp && !alreadyInitFullViewComp[item.compCode]">
                <component
                  :key="`leftCompenent_${index}`"
                  :is="getComponentByCode(item)"
                  v-bind="getProps(item)"
                />
              </template>
              <!-- {{ !item.monitorComp && !alreadyInitFullViewComp[item.compCode] ? initFullViewComp(item, 'comp-left-container') : null }} -->
            </template>
          </div>
          <div class="leftbtn" @click="onHideBtnClick" />
          <div :class="['left-background', leftHide && 'leftHide']" />
        </div>
        <!-- 右侧 -->
        <div
          :class="`right-background  ${
            $store.state.event.evtListOpen ? 'open' : 'close'
          }`"
        >
          <RegionSelect :mapId="mapId" :onRegionChange="onRegionChange" />
          <ToolBox :mapId="mapId" />
          <i
            :class="`btn-handle ${ $store.state.event.evtListOpen? 'open' : 'close'}`"
            @click.stop="extendHandle"
          />
          <div class="right-container" id="comp-right-container">
            <template v-for="(item, index) in componentConfig.right">
              <RemoteComponentSyncLoader
                v-if="item.monitorComp"
                :key="`rightCompenent_${index}`"
                :config="getleftRightComponents(item.compCode)"
                :provinceId="provinceId"
                :cityId="cityId"
                :intervalTime="120000"
                :title="item.compName"
                :defaultWidth="item.defaultWidth ? Number(item.defaultWidth) : 0"
                :defaultHeight="item.defaultHeight ? Number(item.defaultHeight) : 0"
                :compUrl="item.compUrl"
              />
              <template v-else-if="!item.monitorComp && !alreadyInitFullViewComp[item.compCode]">
                <component
                  :key="`rightCompenent_${index}`"
                  :is="getComponentByCode(item)"
                  v-bind="getProps(item)"
                />
              </template>
              <!-- {{ !item.monitorComp && !alreadyInitFullViewComp[item.compCode] ? initFullViewComp(item, 'comp-right-container') : null }} -->
            </template>
            <!-- 如果右侧没有组件配置，展示默认配置 -->
            <template v-if="componentConfig.right.length === 0">
              <EventStatistics :provinceId="provinceId" :cityId="cityId" :orderId="alarmInfoId" />
              <AlarmList
                ref="rightAlarm"
                :provinceId="provinceId"
                :cityId="cityId"
                :orderId="alarmInfoId"
                :propData="defaultAlarmListProps"
              />
            </template>
          </div>
          <!-- <MapTool :mapId="mapId" v-if="$store.state.map.mapToolSwitch" /> -->
          <!-- 地图控制组件 $store.state.event.evtListOpen && 'fix-map-tool' -->
          <!-- 通过 common-comp-map 组件自动生成 -->
          <div v-if="$store.state.map.mapToolSwitch" :class="['maptooltele', 'fix-map-tool']" />
          <!-- 地图类型切换铁路线路 -->
          <ChangeRailwayType :mapId="mapId" :tileModes="tileModes" />
          <!-- 图例 -->
          <Legend screenType="monitor" />
        </div>
        <CaptureInfoWS v-if="$store.state.map.passRouteSwitch" />
        <PassRoutePanel
          v-if="openPassRouteWin"
          :propsComponents="components"
          :tileModes="tileModes"
        />
      </template>
      <CaptureInfoFullScreenWin v-if="openCaptureInfoWin" :curPlayData="curCaptureInfo" />
    </template>
    <!-- 地图工具 
      configItemMemoryNameMenu="paranomaSettingMenu"
    configItemMemoryNameMap="paranomaSettingMap"-->
    <RemoteComponentSyncLoader
      v-if="components['common-comp-tool-box']"
      :class="['tool-box', !rightHide && 'fix-map-tool']"
      :toolsList="toolsList"
      :overlayOptions="{
        use3DHeight: false
      }"
      :config="components['common-comp-tool-box']"
      :mapId="mapId"
      :defaultCheckedIds="[4]"
      :customLayers="customLayers"
      alarmFilteTeleport=".screen-container"
    />
    <!-- 周边分析 -->
    <RemoteComponentSyncLoader
      v-if="components['common-comp-around-analysis']"
      :class="['aroundanalysis', rightHide && 'hide']"
      :config="components['common-comp-around-analysis']"
      :mapId="mapId"
    />
    <!-- 指点飞行组件 -->
    <RemoteComponentSyncLoader
      v-if="flycomponents['common-comp-guide-flight']"
      :config="flycomponents['common-comp-guide-flight']"
      :mapId="mapId"
      :right="460"
      :top="180"
    />
    <RemoteComponentSyncLoader
      v-if="components['common-comp-track-popup']"
      :config="components['common-comp-track-popup']"
      :mapId="mapId"
    />
    <RemoteComponentSyncLoader
      v-if="components['common-comp-dialog-tool']"
      :config="components['common-comp-dialog-tool']"
      :mapId="mapId"
    />
    <RemoteComponentSyncLoader
      v-if="components['common-comp-layers-tool']"
      :config="components['common-comp-layers-tool']"
      :mapId="mapId"
    />
    <RemoteComponentSyncLoader
      v-if="components['common-comp-uav-check-auth']"
      :config="components['common-comp-uav-check-auth']"
      :mapId="mapId"
    />
    <div class="tree-wrapper">
      <RemoteComponentSyncLoader
        v-if="components['common-comp-uav-tree']"
        :class="['dtreebox']"
        :config="components['common-comp-uav-tree']"
        :mapId="mapId"
      />
      <RemoteComponentSyncLoader
        v-if="components['common-comp-tree-recorder']"
        :class="['dtreebox']"
        :config="components['common-comp-tree-recorder']"
        :mapId="mapId"
      />
    </div>
  </div>
</template>
<script>
import api from '@/api' // 接口API
import RemoteComponentSyncLoader from '@ct/remote-page-sync-loader/remote-page-sync-loader.umd.js' // 远程组件
import { parseQueryString } from '@/common/parse-qs.js' // 解析URL参数
import { getInfo, getFisUrl, getBusinessFilesAsync } from '@/utils/index.js' // 获取url参数
import commonService, {
  // getComponentLayout,
  initUserProvinceCityData,
} from '@/api/service/common' // 公共方法
import { promiseAll } from '@/api/service/imScreenService' // 公共方法
import { getUserLocalStorage } from '@/components/common/utils' // 获取本地存储
// import DetailInfo from '@/components/page/ScreenPages/IntegratedMonitor/DetailInfo'
import { CfgEnum } from '@/components/page/ScreenPages/IntegratedMonitor/enum/CfgEnum' // 枚举
import { Events } from '@/components/page/ScreenPages/IntegratedMonitor/enum/EventsEnum' // 枚举
// import AlarmList from '@/components/page/ScreenPages/IntegratedMonitor/EventList'
import AlarmList from '@/components/page/ScreenPages/IntegratedMonitor/AlarmList' // 事件列表
import EventStatistics from '@/components/page/ScreenPages/IntegratedMonitor/EventStatistics' // 事件统计
import Map from '@/components/page/ScreenPages/IntegratedMonitor/Map' // 地图
import MapTool from '@/components/page/ScreenPages/IntegratedMonitor/Map/MapTool/index.vue' // 地图工具
import RegionSelect from '@/components/page/ScreenPages/IntegratedMonitor/Map/RegionSelect' // 区域选择
import SideCtlPanel from '@/components/page/ScreenPages/IntegratedMonitor/SideCtlPanel' // 侧边栏
import ToolBox from '@/components/page/ScreenPages/IntegratedMonitor/ToolBox' // 工具箱
import WindowWrapBottomSide from '@/components/page/ScreenPages/IntegratedMonitor/WindowWrapBottomSide' // 底部侧边栏
import WindowWrapLeftSide from '@/components/page/ScreenPages/IntegratedMonitor/WindowWrapLeftSide' // 左侧侧边栏
import Legend from '@/components/common/legend.vue' // 图例
import { mapMutations, mapActions } from 'vuex' // vuex
import PassRoutePanel from './PassRoutePanel/index.vue' // 巡航路线
import CaptureInfoWS from './captureInfoWS.vue' // 事件推送信息
import CaptureInfoFullScreenWin from './InfoWindow/CaptureInfoFullScreenWin.vue' // 过车信息
import ClearVideo from '@/utils/mixins/clearVideo' // 清除视频
import CompCfg from '@/components/page/ScreenPages/FullViewMonitor/CompCfg' // 组件配置
import ManageAreaInfo from '@/components/page/ScreenPages/FullViewMonitor/ManageAreaInfo/index.vue'
import ss from '@/components/page/ScreenPages/IntegratedMonitor/Map/MapTool/img/2D-dark-map.png'
import cg from '@/components/page/ScreenPages/IntegratedMonitor/Map/MapTool/img/2D-map.jpeg'
import wx from '@/components/page/ScreenPages/IntegratedMonitor/Map/MapTool/img/satellite-map.jpeg'
import ChangeRailwayType from '@/components/common/Map/ChangeRailwayType' // 切换铁路线路
import { getMapTiles } from '@/api/service/common' // 导入获取地图瓦片的服务
import { setMapService } from '@/utils/common'

// 使用到的远程组件定义
// key是组件内部名称，应当与uniqueId指代的具体组件完全对应，用于维护者自己区分和填写在RemoteComponentSyncLoader内
const remoteComp = {
  'common-comp-map': { uniqueId: '2024042465120', version: '1.7.65' },
  'common-comp-tool-box': { uniqueId: '2024042422835', version: '1.7.65' },
  'common-comp-dialog-tool': { uniqueId: '2024082000537', version: '1.7.00' },
  'common-comp-layers-tool': { uniqueId: '2024082008622', version: '1.7.00' },
  'common-comp-around-analysis': {
    uniqueId: '2024042467840',
    version: '1.6.90',
  },
  'common-comp-footer': { uniqueId: '2024042461157', version: '1.6.80' },
  'common-comp-alarm-detail': { uniqueId: '2024042421530', version: '1.7.65' },
  'common-comp-alarm-detail-large': {
    uniqueId: '2024072609683',
    version: '1.7.65',
  },
  'common-comp-tree': { uniqueId: '2024042483471', version: '1.7.20' },
  'common-comp-iot-tree': { uniqueId: '2024042457412', version: '1.6.90' },
  'common-comp-radar-tree': { uniqueId: '2024042467191', version: '1.6.90' },
  'common-comp-horn-tree': { uniqueId: '2024042488249', version: '1.6.90' },
  'common-comp-uav-tree': { uniqueId: '2024042495052', version: '1.7.50' },
  'common-comp-source-tree': { uniqueId: '2024042494149', version: '1.6.90' },
  'common-comp-grid-tree': { uniqueId: '2024042446567', version: '1.6.90' },
  'common-comp-grid-operator-tree': {
    uniqueId: '2024042477521',
    version: '1.6.90',
  },
  'common-comp-track-popup': { uniqueId: '2024042449828', version: '1.7.10' },
  'common-comp-tree-recorder': { uniqueId: '2024052417174', version: '1.7.20' },
  'common-comp-tool-spot': { uniqueId: '2024042413387', version: '1.6.80' },
  'common-comp-tool-space': { uniqueId: '2024042408209', version: '1.6.80' },
  'common-comp-tool-compound': { uniqueId: '2024042436593', version: '1.6.80' },
  'common-comp-tool-complex': { uniqueId: '2024042424703', version: '1.6.80' },
  'common-comp-tool-swiper': { uniqueId: '2024042410687', version: '1.6.70' },
  'common-comp-uav-check-auth': {
    uniqueId: '2025052195256',
    version: '1.7.50',
  },
}
// 指点飞行
const flyComp = {
  'common-comp-guide-flight': {
    uniqueId: '2025012119577',
    version: '1.7.50',
  },
}
// 监听footer设备树出现事件中的对象
// const footerStatus = {}

export default {
  name: 'RemoteComponentLayout', // 组件名称
  inject: ['mapFlag', 'mapRef'], // 需要注入的属性
  mixins: [ClearVideo], // 混入的mixin
  components: {
    ManageAreaInfo,
    RemoteComponentSyncLoader, // 远程组件同步加载器
    RegionSelect, // 区域选择
    MapTool, // 地图工具
    SideCtlPanel, // 侧边栏
    ToolBox, // 工具箱
    Map, // 地图
    WindowWrapLeftSide, // 左侧侧边栏
    WindowWrapBottomSide, // 底部侧边栏
    // DetailInfo,
    AlarmList, // 事件列表
    PassRoutePanel, // 巡航路线
    Legend, // 图例
    CaptureInfoWS, // 事件推送信息
    CaptureInfoFullScreenWin, // 过车信息
    EventStatistics, // 事件统计
    ChangeRailwayType, // 切换铁路线路
  },
  data() {
    return {
      // 地图瓦片
      tileModes: [
        {
          modeId: 5,
          name: '深色地图',
          mapType: '2D',
          imgUrl: ss,
          tileType: 'vector',
          layerUrls: [
            {
              url: '',
              // url: 'http://10.43.82.110:9239/geoserver/railway_space/gwc/service/wmts?layer=railway_space:railway-project-dark&style=&tilematrixset=WebMercatorQuad&Service=WMTS&Request=GetTile&Version=1.0.0&Format=image/png&TileMatrix={z}&TileCol={x}&TileRow={y}',
              // url: 'http://webst01.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}',
            },
          ],
        },
        {
          modeId: 1,
          name: '卫星地图',
          imgUrl: wx,
          tileType: 'satellite',
          mapType: '2D',
        },
        {
          modeId: 6,
          name: '常规地图',
          imgUrl: cg,
          tileType: 'vector',
          mapType: '2D',
          layerUrls: [
            {
              url: '',
              // url: 'http://10.43.82.110:9239/geoserver/railway_space/gwc/service/wmts?layer=railway_space:railway-project&style=&tilematrixset=WebMercatorQuad&Service=WMTS&Request=GetTile&Version=1.0.0&Format=image/png&TileMatrix={z}&TileCol={x}&TileRow={y}',
            },
          ],
        },
      ],
      rightHide: false, // 右侧隐藏状态
      // 地图是否加载完成
      mapComLoaded: false,
      mapLoaded: false, // 地图是否加载完成
      mapId: 'mainMap', // 地图id
      provinceId: '', // 110000
      cityId: '', // 110100
      cityName: '',
      //是否展示告警详情
      // showAlarmInfo: false,
      //告警详情id
      // alarmInfoId: '',
      sysLoading: false,
      // 告警信息全屏弹窗相关
      openCaptureInfoWin: false,
      curCaptureInfo: null,
      // 是否打开过车记录弹窗
      openPassRouteWin: false,
      mapControl: true, // 地图控制
      // userInfo: null,
      // 默认图层 1摄像机和4无人机
      customLayers: [
        {
          id: 4,
          footerBtns: [
            {
              key: 'fxkz', // 飞行控制
            },
            {
              key: 'zdfx', // 指点飞行
            },
            {
              key: 'sssp', // 实时视频
            },
          ],
        },
      ],
      initedFlyComp: false, // 指点飞行
      flycomponents: {}, // 指点飞行配置项
      mapInitFlag: this.mapFlag(), // 初始化函数返回值
      loaded: false,
      components: {}, // 远程组件使用的配置项们
      // naviRightPosition: false, // 监听footer设备树
      // alarmDetailOpen: false, // 告警详情是否打开
      // tableLeft: '', // 地块查询弹窗左偏移
      // 远程组件使用的配置项们
      // 工具箱开启的功能列表
      toolsList: [
        {
          key: 'mapControl',
        },
        // {
        //   key: 'ToolSpot',
        // },
        // {
        //   key: 'ToolSpace',
        // },
        // {
        //   key: 'ToolCompound',
        // },
        // {
        //   key: 'ToolComplex',
        // },
        // {
        //   key: 'ToolSwiper',
        // },
        {
          key: 'smallTools',
        },
        {
          key: 'lookHere',
        },
        // {
        //   key: 'aroundAnalyse',
        // },
        {
          key: 'layersControl',
        },
        // {
        //   key: 'cameraFilte',
        // },
        // {
        //   key: 'alarmFilte',
        // },
        // {
        //   key: 'settings',
        // },
      ],
      alarmInfoId: '', // 告警详情id
      leftHide: false, // 左侧隐藏状态
      leftRightComponents: {}, // 左右两侧的组件对象
      showLeft: true, // 是否展示左侧
      // 配置组件
      componentConfigTemp: {
        left: [],
        right: [],
      },
      // 配置组件
      componentConfig: {
        left: [],
        right: [],
      },
      // 事件列表默认的props
      defaultAlarmListProps: { pageType: 'IntegratedMonitor' },
      fisConfig: null, // 配置信息
      alreadyInitFullViewComp: {}, // 已经初始化的全景展示组件
    }
  },
  computed: {
    // 是否开启巡航路线
    passRouteSwitch() {
      return this.$store.state.map.passRouteSwitch
    },
    // 获取数据
    getleftRightComponents() {
      return key => this.leftRightComponents[key]
    },
    getComponentByCode() {
      return item => CompCfg.getComponentByCode(item)
    },
    getProps() {
      return value => {
        const _props =
          value.compCode === 'import_focus_event_list'
            ? { ...value, ...this.defaultAlarmListProps }
            : value
        return { propData: _props }
      }
    },
  },
  watch: {
    mapInitFlag: {
      handler(val) {
        // 地图类型切换后会再进入运行，这里要判断 this.mapComLoaded ，不然整个页面会重新加载
        if (val && !this.mapComLoaded) {
          this.setMapStoreRef(this.mapRef.getMapRef(this.mapId))
          this.mapComLoaded = true
          // 指点飞行
          if (!this.initedFlyComp) {
            this.getFlyComp()
          }
          // 初始化页面上悬浮面板方法
          this.initcom()
        }
      },
      immediate: true,
    },
  },
  beforeCreate() {
    // 默认值
    if (window.sysConfig) {
      window.sysConfig.detailMode = 'exclusive'
    } else {
      window.sysConfig = { detailMode: 'exclusive' }
    }
  },
  created() {
    // 监听基本类型（通过函数）
    this.$watch(
      () => this.mapFlag(), // 监听函数返回值
      newVal => {
        this.mapInitFlag = newVal
      }
    )
    // 解析url参数，获取viewId
    const qsObj = parseQueryString(window.location.search)
    sessionStorage.setItem('VIEW_ID', qsObj.viewId) // 测试id  '1001684'

    // 视频初期偏移量
    window._remoteMetadata = window._remoteMetadata || {}
    const videoPositionRight = Math.ceil((window.innerHeight / 1032) * 100 * 5)
    window._remoteMetadata.videoPositionRight = videoPositionRight
  },
  async mounted() {
    try {
      // 导入获取地图瓦片的服务
      await getMapTiles()
      // 设置地图瓦片图层
      this.tileModes = setMapService(this.tileModes)
      // console.log('this.tileModes--', this.tileModes, window.hjCfg)
    } catch {
      console.log('地图瓦片服务加载失败')
    }
    // 获取用户信息
    await getInfo()
    // const _info = await getInfo()
    // this.userInfo = _info?.user || {}
    // 获取配置项
    const { data } = await getFisUrl()
    const { configValue } = data || {}
    console.log('当前配置远程域名为---：', configValue)
    // 获取远程组件
    const { components, loaded } = await this.getRemoteComps(
      configValue,
      getBusinessFilesAsync
    )
    // 赋值
    this.components = components
    this.loaded = loaded

    // 执行下面的操作
    this.changeTheme()
    this.afterRemoteLoad()
  },
  methods: {
    // 地图相关
    ...mapMutations('map', [
      'setSelectionRange',
      'setPassRouteSwitch',
      'setMapStoreRef',
    ]),
    // 事件相关
    ...mapMutations('event', [
      'setEvtListOpen',
      'setEvtTypeList',
      'setEvtSourceList',
      'setEvtOrderStatusList',
    ]),
    ...mapMutations('captureInfo', ['setCurSelectCamera']),
    ...mapActions('event', [
      'fetchEventSourceData',
      'fetchEventData',
      'fetchOrderStatusData',
    ]),
    /**
     * 初始化页面上悬浮面板组件需要的方法
     */
    async initcom() {
      // 事件类型
      this.fetchEventData()
      // 事件来源
      this.fetchEventSourceData()
      // 查询处置状态
      this.fetchOrderStatusData()
      // 初始化系统配置
      await this.iniSysConfig()

      this.$nextTick(() => {
        window.leftWrap = this.$refs.leftWrap
        window.bottomWrap = this.$refs.bottomWrap
      })
    },
    afterRemoteLoad() {
      // 监听footer设备树出现事件，以此为契机处理导航弹窗的偏移量
      // this.$globalEventBus.$on(`common-comp-footer__click`, options => {
      //   const { currentSelectData } = options
      //   const { id, isChecked } = currentSelectData
      //   footerStatus[id] = !!isChecked
      //   // isChecked表示启用还是关闭
      //   this.naviRightPosition = Object.values(footerStatus).find(o => o)
      // })

      // 监听告警详情是否打开，以此为契机处理导航弹窗的偏移量
      // this.$globalEventBus.$on(
      //   `common-comp-alarm-detail__alarm-detail-open-status`,
      //   ({ opened }) => {
      //     this.alarmDetailOpen = opened
      //   }
      // )

      // 切换地图模式 2D 切换 3D 才会触发
      // this.$globalEventBus.$on('common-comp-map__init-map-resolve', e => {
      //   console.log('切换地图模式', e)
      //   // if (e.status) {
      //   //   this.mapComLoaded = false
      //   // }
      // })

      // 地图控制按钮选中状态改变
      this.$globalEventBus.$on('common-comp-tool-box__map-control-show', e => {
        this.mapControl = e.status
      })
      // 过车记录
      this.$EventBus.$on(
        Events.PassRouteEvt.CHANGE_PASS_ROUTE_OPEN_STATUS,
        this.changePassRouteWin
      )
      // 首页事件推送
      this.$EventBus.$on(
        Events.PassRouteEvt.CHANGE_CAPTURE_FULL_SCREEN_OPEN_STATUS,
        this.changeCaptureInfoWin
      )
    },
    /**
     * 地图加载完成后执行的初始化操作
     */
    async onMapLoad() {
      // 初始化用户省市数据
      await this.initUserProvinceCityData()
      this.mapLoaded = true
      await this.qryComponentConfigInfo() // 获取组件配置信息
    },
    /**
     * 过车记录
     * @param {*} param0
     */
    changePassRouteWin({ open, deviceInfo = null }) {
      this.setCurSelectCamera(deviceInfo)
      this.openPassRouteWin = open
    },
    /**
     * 首页事件推送
     * @param {*} param0
     */
    changeCaptureInfoWin({ open, captureInfo = null }) {
      this.curCaptureInfo = captureInfo
      this.openCaptureInfoWin = open
    },
    /**
     * 初始化用户可选的省市数据
     * @returns {void}
     */
    async initUserProvinceCityData() {
      // 获取用户权限城市
      const [success, data] = await initUserProvinceCityData()
      if (!success) {
        this.$message.error('查询用户权限城市失败！')
        return
      }
      // 从本地存储中获取用户特定数据
      const userHandleCache = getUserLocalStorage(
        CfgEnum.STORAGE_KEY.SELECT_REGION
      )
      // 获取区域数据
      let { regionCode, regionLevel } = data
      // 获取区域数据
      if (userHandleCache) {
        const {
          nodes,
          cityName,
          regionLevel: cacheRegionLevel,
          regionCode: cacheRegionCode,
        } = userHandleCache
        window.userInfo.defArea = nodes
        window.userInfo.defAreaName = cityName
        regionCode = cacheRegionCode
        regionLevel = cacheRegionLevel
      }
      // 设置
      this.setSelectionRange({
        regionCode,
        regionLevel,
      })
    },
    /**
     * 初始化系统配置和布局配置
     * @returns {void}
     */
    async iniSysConfig() {
      this.sysLoading = true
      // ,await getComponentLayout(CfgEnum.MODEL_CODE),
      const res = await promiseAll([
        await commonService.getDictListByCatCode(CfgEnum.SYS_CFG.Key),
      ])
      if (res.length < 1) {
        console.error('!!!!获取系统配置失败')
        return
      }
      const { code: cfgCode, data: cfgData } = res[0]
      if (cfgCode !== 200 || !cfgData || cfgData.length < 1) {
        console.error('!!!!获取系统配置失败', CfgEnum.SYS_CFG.Key)
        return
      }
      // 测试数据
      // {
      //   dicValue:
      //     '{"EVENT_RES_RADIUS":5000,"IS_SHOW_CAPTURE_TIME":[1,5],"CAPTURE_IMG_SHOW_TIME":3,"CAPTURE_VIDEO_SHOW_TIME":10,"MAP_CAPTURE_INFO_SHOW_TIME":10,"HUS_LAYER_UPDATE":5,"HUS_HIS_TRA":[1,168]}',
      // }
      const { dicValue } = cfgData[0]
      // detailMode 需要一个默认值，用于打开地图点位详情时
      window.sysConfig = { detailMode: 'exclusive', ...JSON.parse(dicValue) }
      // const [success, layoutCfg] = res[1]
      // if (!success || !layoutCfg || !layoutCfg.layoutJsonObj) {
      //   console.error('!!!!获取布局配置失败，启动默认配置')
      //   window.sysConfig.detailMode = 'exclusive'
      // } else {
      //   Object.keys(layoutCfg.layoutJsonObj).forEach(key => {
      //     layoutCfg.layoutJsonObj[key].map(item => {
      //       // exclusive: 互斥模式，只显示一个详情,tab 页签模式，多个详情面板，pending 模式，多个详情面板
      //       if (item.compCode === 'COMMON_LAYER_DETAIL') {
      //         window.sysConfig.detailMode = item.param.showMode || 'exclusive'
      //       }
      //     })
      //   })
      // }
      this.sysLoading = false
      console.log('performance iniSysConfig end', new Date().getTime())
    },
    // 获取组件配置信息
    async qryComponentConfigInfo() {
      // 使用api调用后端接口
      await api
        .get(
          `${this.$env.VUE_APP_REQ_PREFIX_BIZ}/adsLayoutTheme/getThemeByCode`, // 接口地址
          {
            modelCode: sessionStorage.getItem('VIEW_ID'), // 模型代码常量 '1001676'
            isDefault: 'Y',
          }
        )
        .then(res => {
          console.log('大屏组件配置信息------------', res)
          if (res.code === 200 && res.data) {
            // 如果配置为空，则打开默认配置
            if (!res.data.layoutJsonObj) {
              this.showLeft = false
              //   this.getConfigComponent(this.defaultComponentList)
            } else {
              this.showLeft = res.data.layoutJsonObj?.left.length > 0
              this.componentConfigTemp = res.data.layoutJsonObj
              // 遍历组件配置的左右组件信息，查询组件的配置信息
              this.getCompDetailConfig()
            }
          } else {
            this.showLeft = false
            // window.sysConfig.detailMode = 'exclusive'
          }
        })
    },
    // 查询组件的详情配置
    async getCompDetailConfig() {
      const compCodeArr = []
      this.componentConfigTemp.left.forEach(item => {
        compCodeArr.push(
          api.get(
            `${this.$env.VUE_APP_REQ_PREFIX_BIZ}/adsLayoutThemeComp/getThemeCompByCode`, // 接口地址
            {
              compCode: item.compCode, // 模型代码常量
            }
          )
        )
      })
      this.componentConfigTemp.right.forEach(item => {
        compCodeArr.push(
          api.get(
            `${this.$env.VUE_APP_REQ_PREFIX_BIZ}/adsLayoutThemeComp/getThemeCompByCode`, // 接口地址
            {
              compCode: item.compCode, // 模型代码常量
            }
          )
        )
      })

      const res = await Promise.all(compCodeArr)
      if (res.length < 1) {
        console.error('!!!!系统配置信息获取失败')
        return
      }
      const componentList = {}
      // this.componentConfig.left.forEach(item => {
      //   componentList[item.compCode] = { uniqueId: item.id, version: item.version }
      // })
      // this.componentConfig.right.forEach(item => {
      //   componentList[item.compCode] = { uniqueId: item.id, version: item.version }
      // })
      console.info('res------', res)
      res.forEach((item, index) => {
        if (item.code === 200 && item.data) {
          componentList[item.data.compCode] = {
            uniqueId: item.data.compParamList?.find(
              item2 => item2.paramName === 'comp_id'
            )?.paramCode,
            version: item.data.compParamList?.find(
              item2 => item2.paramName === 'comp_version'
            )?.paramCode,
          }
          // 如果是内嵌式组件，需要把内嵌属性和compUrl赋值给 componentConfig
          if (this.componentConfigTemp.left.length > index) {
            this.componentConfigTemp.left[index].compUrl = item.data.compUrl
            this.componentConfigTemp.left[index].showTech = item.data.showTech
            this.componentConfigTemp.left[index].defaultWidth =
              item.data.defaultWidth
            this.componentConfigTemp.left[index].defaultHeight =
              item.data.defaultHeight
            // 如果是自带组件
            this.componentConfigTemp.left[index].monitorComp =
              item.data.compParamList?.find(
                item2 => item2.paramName === 'comp_id'
              )?.paramCode
          } else {
            this.componentConfigTemp.right[
              index - this.componentConfigTemp.left.length
            ].compUrl = item.data.compUrl
            this.componentConfigTemp.right[
              index - this.componentConfigTemp.left.length
            ].showTech = item.data.showTech
            this.componentConfigTemp.right[
              index - this.componentConfigTemp.left.length
            ].defaultWidth = item.data.defaultWidth
            this.componentConfigTemp.right[
              index - this.componentConfigTemp.left.length
            ].defaultHeight = item.data.defaultHeight
            // 如果是自带组件
            this.componentConfigTemp.right[
              index - this.componentConfigTemp.left.length
            ].monitorComp = item.data.compParamList?.find(
              item2 => item2.paramName === 'comp_id'
            )?.paramCode
          }
        }
      })

      // 删除组件componentList中不是内嵌组件，而且uniqueId和version为空，则删除该组件
      const keys = Object.keys(componentList)
      keys.forEach(key => {
        if (
          componentList[key].uniqueId == null &&
          componentList[key].version == null
        ) {
          delete componentList[key]
        }
      })

      await this.getConfigComponent(componentList)
      this.componentConfig = this.componentConfigTemp
      console.info(
        '当前配置的组件列表------',
        componentList,
        this.componentConfig
      )
    },
    // 获取配置的组件
    async getConfigComponent(componentList) {
      console.info(
        'getConfigComponent------',
        componentList,
        this.componentConfig
      )
      let configValue = this.fisConfig
      if (!this.fisConfig) {
        const { data } = await getFisUrl()
        configValue = data || {}
        this.fisConfig = configValue
      }
      const param = {
        components: Object.values(componentList),
      }
      let _configValue = configValue
      // 如果是一个对象时
      if (typeof configValue === 'object' && configValue !== null) {
        _configValue = configValue.configValue
      }
      const { components, loaded } = await getBusinessFilesAsync(
        _configValue,
        param
      )
      if (loaded) {
        this.leftRightComponents = components
        // setTimeout(() => {
        //   this.$forceUpdate()
        // }, 10000)
      }
    },
    /**
     * 区域选择变化时的处理函数
     * @param {Object} region 选中的区域对象
     */
    onRegionChange(region) {
      this.setSelectionRange(region)
    },
    /**
     * 指点飞行组件初始化
     * 获取配置
     */
    async getFlyComp() {
      const { data } = await getFisUrl()
      const { configValue } = data || {}
      const param = {
        components: Object.values(flyComp),
      }
      const { components, loaded } = await getBusinessFilesAsync(
        configValue,
        param
      )
      if (loaded) {
        this.flycomponents = components
        this.initedFlyComp = true
      }
    },
    /**
     * 原页面右侧面板显隐时的操作
     * 视频初期偏移量修改
     */
    onRightClick() {
      this.rightHide = !this.rightHide

      // 视频初期偏移量修改
      window._remoteMetadata = window._remoteMetadata || {}
      const alarmListWidth = !this.rightHide ? 5 : 1.1
      const videoPositionRight = Math.ceil(
        (window.innerHeight / 1032) * 100 * alarmListWidth
      )
      window._remoteMetadata.videoPositionRight = videoPositionRight
      this.$nextTick(() => {
        this.$globalEventBus.$emit(`screenview__updateVideoOffset`)
      })
    },
    /**
     * 获取远程组件
     * 配置项
     */
    async getRemoteComps(configValue, callback) {
      const param = {
        components: Object.values(remoteComp),
      }
      const { components, loaded } = await callback(configValue, param)
      return { components, loaded }
    },
    /**
     * 切换主题
     * 行业主题
     */
    changeTheme() {
      document.documentElement.setAttribute('data-theme', `theme-wiseblue`)
      this.$globalEventBus.$emit('data-theme', 'theme-wiseblue')
      console.log('通用', 2)
    },
    /**
     * 扩展操作处理
     * @returns {void}
     */
    extendHandle() {
      // 切换事件列表的打开状态
      const isOpen = !this.$store.state.event.evtListOpen
      this.setEvtListOpen(isOpen)
      // 过车信息切换的开关不要关联了右侧展示
      // if (isOpen) {
      //   // 如果打开事件列表，则关闭导航路线切换
      //   this.setPassRouteSwitch(false)
      // }
    },
    onHideBtnClick() {
      this.leftHide = !this.leftHide
      // this.$globalEventBus.$emit('common-comp-layer-tool-check-layer', {
      //   layerTypeChecked: { wrj: false, sxj: false, yd: false }
      // })
    },
    // 初始化全景展示组件到dom上
    initFullViewComp(item, domId) {
      console.log('initFullViewComp==================', item, domId)
      // 综合监测大屏里的原组件是与地图组件一起使用的，需要特殊处理
      let _props =
        item.compCode === 'import_focus_event_list'
          ? { ...item, ...this.defaultAlarmListProps }
          : item
      CompCfg.getComponent({
        code: item.compCode,
        wrapper: domId,
        props: _props,
        mapRef: this.mapRef,
      })
      this.alreadyInitFullViewComp[item.compCode] = true
    },
    /**
     * 右侧面板点击事件处理
     */
    rightPanelClick() {
      console.log('rightPanelClick')
    },
  },
  beforeDestroy() {
    this.$globalEventBus.$off('common-comp-tool-box__map-control-show')
    this.$EventBus.$off(
      Events.PassRouteEvt.CHANGE_PASS_ROUTE_OPEN_STATUS,
      this.changePassRouteWin
    )
    this.$EventBus.$off(
      Events.PassRouteEvt.CHANGE_CAPTURE_FULL_SCREEN_OPEN_STATUS,
      this.changeCaptureInfoWin
    )
  },
}
</script>
<style lang="scss" scoped src="./index.scss" />