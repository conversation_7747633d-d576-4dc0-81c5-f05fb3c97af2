@width: 94px;
@height: calc(100% - 100px);
@padding: 12px;
@top: 40px;
@background-color: #172537;
@unit: 12px;
@border-color: #fefefe2e;
@theme-color: #409eff;
@active-color: #409eff;
@focus-color: rgba(64, 158, 255, 0.55);
@confirm-btn-bg: linear-gradient(90deg, #2fb1ec 8%, #155bd4 100%);
@input-inner: rgba(79, 159, 255, 0.2);
@cancel-btn-bg: #2f4360;
.cust-message {
  &.el-message--error {
    background-color: #ed515887;
    font-size: 16px;
    color: #ed5158;
    font-weight: 400;
    border: none;
  }

  &.el-message--success {
    background: #15bd9463;
    border-radius: 4px;
    border: none;
    font-size: 16px;
    color: #01f5ba;
    font-weight: 400;
  }
}

.common-table-tip.el-popover {
  background: @background-color;
  border: none;
  color: #fefefe;

  .el-popover__title {
    font-family: PingFangSC-Medium;
    font-size: 14px;
    color: #e8f3ff;
    letter-spacing: 0;
    font-weight: 500;
  }

  &[x-placement^="top"] .popper__arrow {
    border-top-color: @background-color;

    &::after {
      border-top-color: @background-color;
    }
  }
}

.btn {
  font-size: 12px;
  margin-left: 12px;
  color: #ffffff;
  letter-spacing: 0;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 75px;
  height: 24px;
  border-radius: 4px;
  cursor: pointer;
  &.btn-cancel {
    color: #fefefe;
    border: none;
    background: @cancel-btn-bg;
  }

  &.btn-confirm {
    color: #fefefe;
    border: none;
    background-image: linear-gradient(90deg, #2fb1ec 8%, #155bd4 100%);
  }
}

.im-page {
  display: flex;
  align-items: center;
  justify-content: center;

  &.el-pagination {
    font-family: PingFangSC-Regular;
    font-size: 12px;
    color: #e8f3ff;
    letter-spacing: 0;
    font-weight: 400;

    .el-pagination__total,
    .el-pagination__jump {
      color: #e8f3ffb0;
    }

    & button {
      background: rgba(19, 115, 231, 0.2);
      border-radius: 4px;
      color: #e8f3ffb0;
      min-width: 22px;
      height: 22px;
      line-height: 22px;

      &:hover {
        color: #409eff;
      }
    }

    .el-pager {
      li {
        margin-left: 6px;
        background: rgba(19, 115, 231, 0.2);
        border-radius: 4px;
        color: #fff;
        min-width: 22px;
        height: 22px;
        line-height: 22px;

        &:last-child {
          margin-right: 6px;
        }

        &.active {
          background: #409eff;
        }
      }

      .el-icon.more.btn-quicknext {
        background: rgba(19, 115, 231, 0.2);
        border-radius: 4px;
        color: #fff;
      }
    }

    .el-pagination__editor.el-input .el-input__inner {
      height: 22px;
    }

    .el-input__inner {
      color: #e8f3ffb0;
      background: transparent;
      border: none;

      &[type="number"] {
        background: #223e5f;
      }
    }
  }
}

.el-form {
  .el-form-item__label {
    color: #e8f3ff;
  }

  .el-radio__input {
    .el-radio__inner {
      background-color: transparent;
      border: 1px solid rgba(232, 243, 255, 0.4);
    }

    &.is-checked {
      .el-radio__inner {
        border-color: @active-color !important;
        background: @active-color !important;
      }
    }
  }

  .el-radio.is-checked {
    .el-radio__label {
      color: @active-color !important;
    }
  }

  .el-input__inner,
  .el-textarea__inner {
    color: #fefefe;
    background: rgba(79, 159, 255, 0.2);
    border: none;
  }
  .image-video .file-content .file-upload .el-upload--picture-card {
    background: @input-inner;
  }

  .el-tag.el-tag--info {
    color: @active-color;
  }
}

.el-select-dropdown {
  background-color: @background-color;
  border: 1px solid #0000003d;
  box-shadow: 3px 3px 3px;
}
.el-select-dropdown__item.selected.hover,
.el-select-dropdown__item.selected {
  color: #409eff !important;
}

.el-select-dropdown__item.selected.hover,
.el-select-dropdown__item.selected {
  color: @theme-color !important;
  background: rgba(79, 159, 255, 0.4) !important;
}

.el-select-dropdown__item.hover {
  color: #fefefe;
  background: rgba(79, 159, 255, 0.4) !important;
}

.el-select-dropdown__item {
  color: #d2dfee;
  padding: 0 12px;
}

.el-select {
  width: 100%;

  .el-select__tags .el-tag {
    background-color: rgba(25, 137, 250, 0.2);
    color: #1989fa;
    border: none;

    .el-select__tags-text {
      max-width: 135px;
    }
  }

  .el-select__tags .el-tag.el-tag--info .el-tag__close.el-icon-close {
    color: #1989fa;
    background-color: transparent;

    &::before {
      color: #1989fa;
    }
  }
}

.el-date-editor .el-range-separator {
  display: flex;
  align-items: center;
  justify-content: center;
}
.el-popper[x-placement^="bottom"] .popper__arrow::after {
  border-bottom-color: @background-color;
}

.el-popper[x-placement^="bottom"] .popper__arrow {
  border-bottom-color: #000;
}

::-webkit-scrollbar {
  width: 0; /* remove scrollbar space */
  background: transparent; /* optional: just make scrollbar invisible */
  height: 0;
}

.el-popover {
  background-color: @background-color;
  border: none;
  color: #fefefe;
  box-shadow: 3px 3px 3px rgba(0, 0, 0, 0.5);
  .el-button.el-button--primary {
    color: #fefefe;
    border: none;
    background-image: @confirm-btn-bg;
    &:hover {
      color: #fefefe;
      background-image: @confirm-btn-bg !important;
    }
  }
}
.el-popconfirm .el-popconfirm__action .el-button {
  background-color: @cancel-btn-bg;
  color: #fefefe;
}

.el-button.el-button--default:focus,
.el-button.el-button--default:hover {
  // background: #fff !important;
  border-color: #409eff !important;
  // color: #409eff !important;
}

.el-button.el-button--text:focus,
.el-button.el-button--text:hover {
  color: #409eff !important;
}

.el-picker-panel.el-date-range-picker.el-popper {
  color: #e8f3ff;
  border: 1px solid transparent;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  background: @background-color;
  border-radius: 4px;
  line-height: 30px;
  margin: 5px 0;

  .el-date-table td.in-range div {
    background-color: @focus-color !important;
    color: #e8f3ff !important;
  }

  .el-date-range-picker__content.is-left {
    border-color: @border-color;
  }

  .el-picker-panel__icon-btn {
    color: #fefefe;

    &:hover {
      color: @theme-color;
    }
  }
  .el-date-table {
    th {
      color: #e8f3ff;
      border-bottom: solid 1px @border-color;
    }

    td.next-month,
    td.prev-month {
      color: #c0c4cc91;
    }

    td.disabled div {
      background-color: transparent;
      opacity: 1;
      cursor: not-allowed;
      color: #c0c4cc91;
    }
    td.available:hover {
      span {
        color: #e8f3ff;
        background: @focus-color;
        border-radius: 24px;
      }
    }
  }
  .el-picker-panel__icon-btn {
    color: #ffffff;
  }
}

.el-checkbox {
  .el-checkbox__inner {
    background: transparent;
    border-color: @theme-color;
  }
  .el-checkbox__label {
    color: #b1b1b1;
  }
}

.el-popper .popper__arrow {
  border-top-color: @background-color !important;

  &::after {
    border-top-color: @background-color !important;
  }
}

.el-loading-mask {
  background: rgba(23, 37, 55, 0.85)
    url("../../../../assets/images/comm/loading-dark.gif") 50% 50% no-repeat !important;
  background-size: 100px 80px !important;

  .el-loading-spinner {
    display: none;
  }
}

.el-select-dropdown__item.selected {
  font-weight: unset;
}
