/**
 * 配置枚举对象，用于定义系统中各种配置项的键值。
 * 这些键值常用于存储和检索特定的配置信息，确保配置信息的一致性和易用性。
 */
export const CfgEnum = {
  /**
   * 事件响应半径配置项。
   * 该配置项用于定义事件监测的响应半径，键值为'EVENT_RES_RADIUS'。
   */
  EVENT_RES_RADIUS:{
    Key:"EVENT_RES_RADIUS",
  },
  /**
   * 存储键配置对象，包含多个用于存储不同信息的键值。
   * 这些键值用于在本地存储或会话存储中保存特定的数据，如侧边栏状态和选择的区域。
   */
  STORAGE_KEY: {
    /**
     * 侧边栏键配置项。
     * 该配置项用于保存侧边栏的状态（如展开或隐藏），键值为'SIDEBAR_KEY'。
     */
    SIDEBAR_KEY: 'SIDEBAR_KEY',
    /**
     * 选择区域配置项。
     * 该配置项用于保存用户选择的区域信息，键值为'SELECT_REGION'。
     */
    SELECT_REGION: 'SELECT_REGION',
  },
  /**
   * 地图配置项。
   * 该配置项用于定义地图相关的配置，如地图地址，键值为'MAP_ADDR'。
   */
  MAP_CFG: {
    Key: 'MAP_ADDR'
  },
  /**
   * 左窗格组配置项。
   * 该配置项用于定义左窗格组的状态，如是否隐藏，键值为'HIDDEN'。
   */
  LEFT_WIN_GROUP: {
    HIDDEN: 'HIDDEN'
  },
  /**
   * 系统配置项。
   * 该配置项用于保存系统的全局参数配置，键值为'SYS_PARAMETER_CFG'。
   */
  SYS_CFG: {
    Key: 'SYS_PARAMETER_CFG'
  },
  /**
   * 模型代码配置项。
   * 该配置项用于定义监控模型的代码，键值为'MONITOR'。
   */
  MODEL_CODE:{
    modelCode: 'MONITOR'
  }
}

