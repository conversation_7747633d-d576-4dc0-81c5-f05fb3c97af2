/**
 * 事件常量对象，用于在不同的地图和设备事件中传递事件类型。
 * 这样做可以避免在代码中硬编码字符串，提高代码的可维护性和可读性。
 */
export const Events = {
  /**
   * 地图事件常量。
   * 这些常量用于标识地图上发生的各种事件，如添加标记、移除标记等。
   */
  MapEvent: {
    // add marker
    ADD_MARKER: 'ADD_MARKER',
    // remove marker
    REMOVE_MARKER: 'REMOVE_MARKER',
    // 清空所有元素
    CLEAR_ALL: 'CLEAR_ALL',
    // 故障检测
    DETECT_FAILURE: 'DETECT_FAILURE',
    // cluster click
    CLUSTER_CLICK: 'CLUSTER_CLICK',
    // cluster move end
    EVENT_MAP_ZOOM_CHANGE: 'EVENT_MAP_ZOOM_CHANGE',
    // 畜牧GPS历史轨迹
    ANIMAL_GPS_HISTORY_LOCUS: 'ANIMAL_GPS_HISTORY_LOCUS',
    // 刷新云广播图层
    REFRESH_LOUD_SPEAKER_LAYER: 'REFRESH_LOUD_SPEAKER_LAYER',
  },
  /**
   * 设备详情事件常量。
   * 这些常量用于设备详情页面中的特定事件，如分组点击。
   */
  DEVICE_DETAIL: {
    GROUP_CLICK: 'GROUP_CLICK',
  },
  /**
   * 事件列表事件常量。
   * 这些常量用于事件列表页面中的各种事件，如查询周围事件、点击事件等。
   */
  EVT_LIST: {
    EVENT_AROUND: 'event_around',
    ON_CLICK: 'EVT_LIST_ON_CLICK',
    EVENT_DETAIL_CLEAR_AROUND: 'event_detail_clear_around',
    ON_DETAIL_CLOSE: 'ON_DETAIL_CLOSE',
  },
  /**
   * 通过路线事件常量。
   * 这些常量用于标识通过路线相关的事件，如打开通过路线。
   */
  PassRouteEvt: {
    OPEN: 'PassRouteEvtOPEN',
    CHANGE_PASS_ROUTE_OPEN_STATUS: 'CHANGE_PASS_ROUTE_OPEN_STATUS',
    CHANGE_CAPTURE_FULL_SCREEN_OPEN_STATUS: 'CHANGE_CAPTURE_FULL_SCREEN_OPEN_STATUS',
  },
  /**
   * 地图工具事件常量。
   * 这些常量用于标识地图工具相关的事件，如图层交换。
   */
  MAP_TOOL_EVT: {
    MAP_LAYER_EXCHANGE: 'MAP_LAYER_EXCHANGE',
  },
};
