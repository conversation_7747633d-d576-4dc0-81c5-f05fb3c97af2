<!--
 * @Description: 控制面板组件，用于畜牧详情-历史轨迹查询
 * @Author: liu.yongli
 * @Date: 2024-07-18 18:30:53
 * @LastEditTime: 2024-07-20 12:38:14
 * @LastEditors: liu.yongli
-->
<template>
  <!-- 控制面板组件，用于畜牧详情-历史轨迹查询 -->
  <CtlPanel class="animal_locus_main" :close="doClose">
    <!-- 标题区域 -->
    <template v-slot:title>轨迹查询</template>
    <template v-slot:content>
      <div class="animal_locus_main_content">
        <div class="animal_locus_item">
          <div class="animal_locus_item_title">
            <!-- 查询时间标题，红色星号表示必填项 -->
            <span :style="{ color: '#ff0000' }">*</span>查询时间
          </div>
          <div class="animal_locus_item_value">
            <!-- 日期选择器，用于选择查询时间范围 -->
            <el-date-picker
              v-model="selecteTimeArr"
              type="datetimerange"
              :popper-class="'screen_animal_locus'"
              :picker-options="pickerOptions"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              :clearable="false"
              @change="timeChange"
            ></el-date-picker>
          </div>
        </div>
        <div class="animal_locus_item">
          <div class="animal_locus_item_title">轨迹播放</div>
          <div class="animal_locus_item_value">
            <!-- 滑动条，用于控制轨迹播放进度 -->
            <el-slider
              v-model="currentIndex"
              :format-tooltip="formatTooltip"
              :max="maxIndex"
              @change="onSliderChange"
            ></el-slider>
          </div>
        </div>
      </div>
      <div class="animal_locus_main_btns">
        <!-- 开始按钮，点击后查询历史轨迹 -->
        <div class="animal_locus_main_btns_start" v-if="!isPlay" @click="queryHistoryTrack">开始</div>
        <!-- 暂停按钮，点击后暂停播放 -->
        <div class="animal_locus_main_btns_start" v-if="isPlay" @click="onStop">暂停</div>
        <!-- 重置按钮，点击后重置播放状态 -->
        <div class="animal_locus_main_btns_reset" @click="onReset">重置</div>
      </div>
    </template>
  </CtlPanel>
</template>
<script>
import api from '@/api'
import dayjs from 'dayjs'
import duration from 'dayjs/plugin/duration'
// 扩展 duration 功能
dayjs.extend(duration)
import CtlPanel from '@/components/page/ScreenPages/IntegratedMonitor/CtlPanel'
import { Events } from '../enum/EventsEnum'
import { transformPointToMercator } from '@/utils'

// 地图事件常量
const MapEvent = Events.MapEvent
export default {
  props: {
    close: {
      type: Function,
      default: () => {
        console.log('close')
      },
    },
    propData: {
      type: Object,
      default: {},
    },
  },
  components: {
    CtlPanel,
  },
  // 组件销毁前的钩子函数
  beforeDestroy() {
    // 清空定时器
    this.timer && clearInterval(this.timer)
    // 清空地图历史轨迹
    this.$EventBus.$emit(MapEvent.ANIMAL_GPS_HISTORY_LOCUS, {
      locusList: [],
      type: 'close',
      currentTime: '',
    })
  },
  data() {
    return {
      // 默认选择的时间范围
      selecteTimeArr: [
        dayjs().subtract(window.sysConfig.HUS_HIS_TRA[0], 'hours'),
        dayjs(),
      ],
      isPlay: false, // 是否正在播放
      currentIndex: 0, // 当前播放索引
      maxIndex: 12, // 最大播放索引
      locusList: [], // 轨迹列表
      timer: null, // 历史轨迹播放定时器
      pickerOptions: {
        // 禁用未来日期选择
        disabledDate(time) {
          return time.getTime() > Date.now()
        },
      },
    }
  },
  methods: {
    // 查询历史轨迹
    queryHistoryTrack() {
      // 校验时间范围是否超过限制
      if (
        dayjs(this.selecteTimeArr[1]).diff(
          dayjs(this.selecteTimeArr[0]),
          'hours'
        ) > window.sysConfig.HUS_HIS_TRA[1]
      ) {
        this.$message({
          message: `时间范围不能超过${window.sysConfig.HUS_HIS_TRA[1]}小时`,
          type: 'warning',
        })
        return
      }
      console.info('selecteTimeArr==============', this.selecteTimeArr)
      // 调用API查询轨迹数据
      api
        .post(`${this.$env.VUE_APP_REQ_PREFIX_PLAT}/farm/getPlayback`, {
          sn: this.propData.deviceCode,
          mapType: 'baidu',
          startTime: dayjs(this.selecteTimeArr[0]).format(
            'YYYY-MM-DD HH:mm:ss'
          ),
          endTime: dayjs(this.selecteTimeArr[1]).format('YYYY-MM-DD HH:mm:ss'),
          showLBS: 1,
        })
        .then(res => {
          if (res.code == 200) {
            this.locusList = res.data?.length
              ? res.data.map(v => {
                  // 3.0 地图转成墨卡托
                  const lngLat = transformPointToMercator([
                    Number(v.lng),
                    Number(v.lat),
                  ])
                  return {
                    ...v,
                    lng: lngLat[0], //  经度
                    lat: lngLat[1], //  纬度
                  }
                })
              : []
            // 如果有数据则开始播放
            if (this.locusList.length > 0) {
              this.onPlay()
            } else {
              this.$message({
                message: '暂无数据',
                type: 'warning',
              })
              this.$EventBus.$emit(MapEvent.ANIMAL_GPS_HISTORY_LOCUS, {
                locusList: [],
                type: 'close',
                currentTime: '',
              })
              return
            }
          } else {
            this.$message({
              type: 'error',
              message: '查询详情失败：' + res.msg,
            })
          }
        })
    },
    // 开始播放
    onPlay() {
      // 设置定时器，每秒更新播放进度
      this.timer = setInterval(() => {
        this.currentIndex++
        if (this.currentIndex > this.maxIndex) {
          // 播放结束，清空定时器
          clearInterval(this.timer)
          this.isPlay = false
          this.currentIndex = 0
        } else {
          // 计算当前播放时间并发送事件
          const currentTime = dayjs(
            dayjs(this.selecteTimeArr[0]).format('YYYY-MM-DD HH:mm:ss')
          )
            .add(this.currentIndex * 5, 'minutes')
            .format('YYYY-MM-DD HH:mm:ss')
          this.$EventBus.$emit(MapEvent.ANIMAL_GPS_HISTORY_LOCUS, {
            locusList: this.locusList,
            type: 'history',
            currentTime: currentTime,
          })
        }
      }, 1000)
      this.isPlay = true
    },
    // 暂停播放
    onStop() {
      this.isPlay = false
      this.timer && clearInterval(this.timer)
    },
    // 重置播放状态
    onReset() {
      this.isPlay = false
      this.currentIndex = 0
      this.timer && clearInterval(this.timer)
      this.selecteTimeArr = [dayjs().subtract(1, 'hours'), dayjs()]
      this.$EventBus.$emit(MapEvent.ANIMAL_GPS_HISTORY_LOCUS, {
        locusList: [],
        type: 'close',
        currentTime: '',
      })
    },
    // 滑动条拖动事件处理
    onSliderChange(value) {
      this.currentIndex = value
      const currentTime = dayjs(
        dayjs(this.selecteTimeArr[0]).format('YYYY-MM-DD HH:mm:ss')
      )
        .add(this.currentIndex * 5, 'minutes')
        .format('YYYY-MM-DD HH:mm:ss')
      this.$EventBus.$emit(MapEvent.ANIMAL_GPS_HISTORY_LOCUS, {
        locusList: this.locusList,
        type: 'history',
        currentTime: currentTime,
      })
    },
    // 关闭面板前的处理函数
    doClose() {
      // 如果正在播放则停止播放
      if (this.isPlay) {
        this.onReset()
      }
      this.$EventBus.$emit(MapEvent.ANIMAL_GPS_HISTORY_LOCUS, {
        locusList: [],
        type: 'close',
        currentTime: '',
      })
      this.close()
    },
    // 格式化进度条tooltip时间
    formatTooltip(value) {
      if (this.selecteTimeArr && this.selecteTimeArr.length == 2) {
        return dayjs(
          dayjs(this.selecteTimeArr[0]).format('YYYY-MM-DD HH:mm:ss')
        )
          .add(value * 5, 'minutes')
          .format('YYYY-MM-DD HH:mm:ss')
      } else {
        return '--:--:--'
      }
    },
    // 时间范围修改事件处理
    timeChange(value) {
      console.info('timechange -------', value)
      // 如果清空了时间则提示
      if (!value) {
        this.$message({
          message: '查询时间不能为空',
          type: 'warning',
        })
        this.selecteTimeArr = [dayjs().subtract(1, 'hours'), dayjs()]
        return
      }
      // 校验时间范围是否超过限制
      if (
        dayjs(value[1]).diff(dayjs(value[0]), 'hours') >
        window.sysConfig.HUS_HIS_TRA[1]
      ) {
        this.$message({
          message: `时间范围不能超过${window.sysConfig.HUS_HIS_TRA[1]}小时`,
          type: 'warning',
        })
        return
      }
      this.timer && clearInterval(this.timer)
      // 重置播放状态
      this.currentIndex = 0
      this.isPlay = false
      // 设置最大播放索引
      this.maxIndex = Math.ceil(
        dayjs(value[1]).diff(dayjs(value[0]), 'minutes') / 5
      )
      this.$EventBus.$emit(MapEvent.ANIMAL_GPS_HISTORY_LOCUS, {
        locusList: [],
        type: 'close',
        currentTime: '',
      })
    },
  },
}
</script>
<style lang="less" scoped src="./index.less" />
