<!--
 * @Description: 控制面板组件
 * @Autor: he.chao
 * @Date: 2022-10-19 15:07:55
 * @LastEditors: liu.yongli
 * @LastEditTime: 2023-12-13 14:36:14
-->
<template>
  <!-- 定义控制面板的外层容器，使用动态绑定的属性 'cc' 设置类名 -->
  <div class='ctl-panel' :cc='name'>
    <div class='ctl-header'>
      <!-- 用于插入选定的标题内容 -->
      <slot name='title' />
      <!-- 用于插入选定的自定义图标内容 -->
      <slot name='self-icon' />
      <!-- 点击关闭按钮时触发关闭操作 -->
      <span class='ctl-close' @click='onclose' />
    </div>
    <div class='ctl-content'>
      <!-- 用于插入内容区域的自定义内容 -->
      <slot name='content' />
    </div>
  </div>
</template>
<script>
/**
 * 控制面板组件
 * 该组件提供了一个基本的控制面板结构，包括标题、自定义图标和关闭功能。
 * 通过插槽机制，用户可以自定义标题、自定义图标和内容区域。
 *
 * @prop {String} name - 控制面板的名称，用作CSS类名的一部分。
 * @prop {Function} close - 关闭控制面板的回调函数。
 */
export default {
  props: {
    // 控制面板的名称，用于动态绑定类名
    name,
    close: {
      type: Function,
      // 默认关闭函数，打印 'close' 到控制台
      default: () => {
        console.log('close');
      }
    }
  },
  data() {
    /* 组件数据初始化 */
    return {};
  },
  mounted() {
    /* 组件挂载后的操作 */
  },
  methods: {
    /**
     * 执行关闭操作的方法。
     * 调用传入的close属性函数，实现控制面板的关闭功能。
     */
    onclose() {
      // 调用关闭回调函数
      this.close();
    }
  }
};
</script>
<!-- 引入样式文件，使用less预处理器 -->
<style lang='less' src='./index.less' />
