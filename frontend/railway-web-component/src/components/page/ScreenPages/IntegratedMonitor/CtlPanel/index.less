@import "../common";

.ctl-panel {
  background-color: @background-color;
  font-family: PingFangSC-Medium;
  color: #E8F3FF;

  .ctl-header {
    height: 45px;
    width: 100%;
    font-size: 16px;
    letter-spacing: 0;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    background: url("./img/title_border.png") 0% 100% no-repeat, url("./img/title_border_length.png") 0% 100% no-repeat;
    background-size: 80px 5px, 100% 1px;

    :first-child {
      margin-left: auto;
    }
    .ctl-close {
      display: flex;
      width: 10px;
      height: 10px;
      background: url("./img/close.png") 100% 100% no-repeat;
      background-size: 100% 100%;
      cursor: pointer;
    }
  }

  .ctl-content {
    padding-bottom: 12px;
  }
  .el-select {
    width: 100%;
    .el-select__tags .el-tag {
      background-color: rgba(25, 137, 250, 0.2);
      color: #1989FA;
      border: none;
      .el-select__tags-text{
        max-width: 135px;
      }
    }
    .el-select__tags .el-tag.el-tag--info .el-tag__close.el-icon-close {
      color: #1989FA;
      background-color: transparent;
      &::before {
        color: #1989FA;
      }
    }
  }
  .el-select-dropdown{
    box-shadow: -3px 2px 4px #000000bd
  }
  .el-input.is-disabled .el-input__inner{
    background: @input-inner;
    border: none;
  }
  .el-radio__input.is-checked + .el-radio__label {
    color: #409EFF !important;
  }

  .el-radio__input.is-checked .el-radio__inner {
    border-color: #409EFF !important;
    background: #409EFF !important;
  }

  .el-radio-group {
    font-size: 0;
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }

  .el-radio {
    margin: 0;
  }

  .el-radio__label {
    padding: 0 12px 0 6px;
  }


  .el-date-editor {
    width: 290px;
    margin-left: 12px;

    &.el-input__inner {
      background-color: rgba(79, 159, 255, 0.20);
      border: none;
    }

    .el-range-separator {
      color: #fefefe;
    }

    .el-range-input {
      background-color: transparent;
      color: #fefefe;
    }
  }
}
