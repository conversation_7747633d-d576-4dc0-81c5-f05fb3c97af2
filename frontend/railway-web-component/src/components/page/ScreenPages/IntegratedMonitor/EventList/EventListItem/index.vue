<template>
  <!-- 告警列表项，用于显示每个告警的详细信息，包括告警图标、告警来源、告警级别、告警名称、告警位置、告警时间等 -->
  <div
    class="list-table-item"
    :class="{ 'table-item-clicked': orderId === item.id }"
    @click="getAlarmInfo(item)"
  >
    <div class="item-left">
      <!-- 显示告警图标，如果不存在则显示默认图标 -->
      <img
        alt="告警图片"
        :src="getFileImgUrlIcon(item.fileImgUrlIcon) || item.imgUrl || staticImg"
        fit="contain"
        class="eventListImage"
        @mouseover="mouseoverFile(getFileImgUrlIcon(item.fileImgUrlIcon) || item.imgUrl || staticImg)"
        @mouseleave="mouseoutFile"
      />
      <!-- 显示告警来源名称 -->
      <span :title="getEvtSource(item.eventSource)">{{ getEvtSource(item.eventSource) }}</span>
    </div>
    <div class="item-right">
      <!-- 告警标题区域，包括告警级别标签和告警名称 -->
      <div class="item-right-title">
        <div class="title-left">
          <!-- 根据告警级别显示不同的标签颜色 -->
          <!-- congestLevel emergencyLevel -->
          <span
            v-if="item.emergencyLevelName"
            :class="`level-tag level-tag${getCongestLevel(item.emergencyLevelName)}`"
          >{{ item.emergencyLevelName }}</span>
          <!-- 显示告警名称 -->
          <span class="name" :title="item.title">{{ item.title }}</span>
        </div>
        <!-- 收藏按钮，根据告警是否已收藏显示不同状态 -->
        <img
          alt="收藏"
          v-if="item.isCollecltion !== '1'"
          :src="require(`@/assets/images/alarmEvent/alarm/shoucang_nor.svg`)"
          @click.stop="collEvent(item, '1')"
        />
        <img
          alt="已收藏"
          v-else
          :src="require(`@/assets/images/alarmEvent/alarm/shoucang_huang_sel.svg`)"
          @click.stop="collEvent(item, '0')"
        />
      </div>
      <!-- 显示告警位置 -->
      <div class="item-span" :title="item.eventAddress">{{ item.eventAddress }}</div>
      <div class="item-right-con">
        <!-- 显示告警发生时间 -->
        <div class="item-right-con-l">
          <span class="item-span">{{ getOccTime(item.occurTime) }}</span>
        </div>
      </div>
      <!-- 显示告警处理状态标签 -->
      <div class="item-tags">
        <!-- <div :class="`flow-node ${END_NODE.includes(item.orderStatus) ? 'flow-node-end' : ''}`">
          {{ END_NODE.includes(item.orderStatus) ? '已办结' : flowLinkCache[`${item.flowId}_${item.linkId}`]?.linkName
          }} 
        </div>-->
        <!-- ${getFusionStatus(item.fusionStatus).dictLabel === '未消散' ? 'fusion1':'fusion2'} -->
        <div class="flow-node">
          <img
            class="evt-type-img"
            :src="require(`@/assets/images/alarmEvent/alarm/icon_sijijao_${getFusionStatus(item.fusionStatus).dictLabel === '未消散'?'huang':'lv'}.svg`)"
            alt
          />
          {{ getFusionStatus(item.fusionStatus).dictLabel }}
        </div>
        <!-- ${END_NODE.includes(item.alarmStatus) ? 'flow-node-end' : ''} -->
        <div class="flow-node">{{ getDealState(item.alarmStatus) }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import api from '@/api'
import dayjs from 'dayjs'
import {
  dealStateEnm,
  alarmStatusListDataDefault,
} from '@/components/page/ScreenPages/IntegratedMonitor/EventFilter/typeDatas'

// 定义已办结状态的告警订单状态码列表
const END_NODE = ['0', '6', '7']
const congestLevelEnm = {
  重大: 1,
  严重: 2,
  一般: 3,
  轻微: 4,
}

export default {
  name: 'alarmListItem',
  props: {
    item: {
      type: Object,
      default: {},
    },
    power: {},
    orderId: {
      type: String | Number,
      default: '',
    },
    flowLinkCache: {
      type: Object,
      default: {},
    },
    mouseoutFile: {
      type: Function,
      default: () => {
        console.log('mouseoutFile')
      },
    },
  },
  computed: {
    getEvtSource() {
      return key => {
        const obj = this.$store.state.event?.evtSourceList?.find(
          v => v.dictValue === key
        )
        return obj?.dictLabel || ''
      }
    },
    getCongestLevel() {
      return key => congestLevelEnm?.[key]
    },
    getDealState() {
      return key => dealStateEnm?.[key]
    },
    getFusionStatus() {
      return key => {
        const obj = alarmStatusListDataDefault.find(v =>
          v.dictValue.includes(key)
        )
        return obj || {}
      }
    },
    getOccTime() {
      return key => (key ? dayjs(key).format('YYYY-MM-DD HH:mm:ss') : key)
    },
    getFileImgUrlIcon() {
      return imgUrl => {
        if (!imgUrl) {
          return ''
        } else {
          // decodeURIComponent(imgUrl)
          return imgUrl.split('%2C')?.[0] || ''
        }
      }
    },
  },
  data() {
    return {
      END_NODE,
      staticImg: require('@/assets/images/alarmEvent/alarm/alarmNull.png'), // 告警默认图片
    }
  },
  methods: {
    /**
     * 收藏或取消收藏告警事件
     * @param {Object} item 告警事件对象
     * @param {String} isCollecltion 是否收藏的状态标识 ('0': 未收藏, '1': 已收藏)
     */
    collEvent(item, isCollecltion) {
      console.log('收藏点击--', item, isCollecltion)
      // 构造请求参数
      const params = {
        operationType: isCollecltion, //1-新增收藏； 0-取消收藏
        eventId: item.id || item.orderId,
        // 原接口入参
        // warningOrderId: item.id,
        // alarmTime: item.alarmTime,
      }
      // 发送收藏操作的请求
      // 原接口 ${this.$env.VUE_APP_REQ_PREFIX_BIZ}/event/warningOrderCollection
      api
        .post(
          `${this.$env.VUE_APP_REQ_PREFIX_BIZ}/fusionEvent/favorite`,
          params
        )
        .then(res => {
          if (res.code === 200) {
            // 收藏操作成功
            this.$message({
              type: 'success',
              message: res.msg,
            })
            this.$parent.getAlarmList() // 刷新告警列表
          } else {
            // 收藏操作失败
            this.$message({
              type: 'error',
              message: res.msg || '收藏操作失败！',
            })
          }
        })
    },
    /**
     * 点击告警列表项时触发，用于打开告警详情页面
     * @param {Object} item 告警事件对象
     */
    getAlarmInfo(item) {
      if (item) {
        // 触发自定义事件，传递告警事件对象给父组件处理
        this.$emit('alarmInfoFn', item)
      } else {
        this.$message({
          type: 'error',
          message: '告警id不存在!',
        })
      }
    },
    /**
     * 鼠标移入告警图标时触发，用于显示大图
     * @param {String} data 图片的URL
     */
    mouseoverFile(data) {
      this.$emit('mouseoverFile', data)
    },
  },
}
</script>

<style lang='less' src='./index.less' />
