.alarm-list-outter {
  width: 100%;
  height: 100%;
  z-index: 9;

  .btn-handle {
    height: 402px;
    width: 29px;
    position: absolute;
    top: 50%;
    left: -28px;
    transform: translateY(-50%) rotateY(180deg);
    cursor: pointer;

    &.close {
      background: url("./img/icon_open.png") 100% 100% no-repeat;
      background-size: 100% 100%;
    }

    &.open {
      background: url("./img/icon_close.png") 100% 100% no-repeat;
      background-size: 100% 100%;
    }
  }
  .topContent {
    width: 366px;
    height: 108px;
    background: #172537;
    border-radius: 8px;
    padding: 12px 0;
    box-sizing: border-box;

    .titleWrap {
      padding: 0 12px 0 6px;
      height: 34px;
      width: 100%;
      box-sizing: border-box;

      .title {
        width: 100%;
        height: 100%;
        background: url("~@/assets/images/alarmEvent/alarm/event_index_bg.svg")
          no-repeat 100% 100%;
        background-size: 100%;
        display: flex;

        &::before {
          content: "";
          width: 21px;
          height: 23px;
          margin-left: 21px;
          margin-top: 2px;
          background: url("~@/assets/images/alarmEvent/alarm/event_index_title_icon.svg")
            no-repeat 100% 100%;
          background-size: 100%;
        }

        .titlName {
          font-family: PingFangSC-Medium;
          font-size: 14px;
          color: #e8f3ff;
          letter-spacing: 0;
          line-height: 20px;
          font-weight: 500;
          margin-left: 24px;
          margin-top: 4px;
        }

        .unit {
          font-family: PingFangSC-Regular;
          font-size: 14px;
          color: rgba(232, 243, 255, 0.6);
          letter-spacing: 0;
          line-height: 20px;
          font-weight: 400;
          margin-top: 4px;
        }
      }
    }

    .eventIndex {
      margin-top: 10px;
      padding: 0 12px;
      display: flex;
      justify-content: space-between;
      position: relative;

      &::after {
        content: "";
        position: absolute;
        top: 23px;
        left: 12px;
        width: calc(100% - 24px);
        height: 19px;
        background: url("~@/assets/images/alarmEvent/alarm/event_index_top_bg.png")
          no-repeat 100% 100%;
        background-size: 100%;
      }

      .eventIndexItem {
        width: 104px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;

        .indexKey {
          font-family: PingFangSC-Regular;
          font-size: 12px;
          color: #e8f3ff;
          letter-spacing: 0;
          font-weight: 400;
        }

        .indexValue {
          font-family: DINAlternate-Bold;
          font-size: 20px;
          color: #e8f3ff;
          letter-spacing: 0;
          font-weight: 700;
          margin-left: 6px;
        }
      }

      .todayItem {
        background-image: linear-gradient(
          90deg,
          rgba(237, 81, 88, 0) 0%,
          rgba(237, 81, 88, 0.5) 47%,
          rgba(237, 81, 88, 0) 100%
        );

        .indexIcon {
          width: 16px;
          height: 14px;
          margin-right: 6px;
        }
      }

      .pendingItem {
        // background-image: linear-gradient(
        //   90deg,
        //   rgba(58, 119, 229, 0) 0%,
        //   rgba(58, 119, 229, 0.5) 49%,
        //   rgba(58, 119, 229, 0) 100%
        // );
        background-image: linear-gradient(
          90deg,
          rgba(229, 132, 58, 0) 0%,
          rgba(229, 132, 58, 0.5) 49%,
          rgba(229, 132, 58, 0) 100%
        );

        .indexIcon {
          width: 9px;
          height: 14px;
          margin-right: 9px;
        }
      }

      .finishedItem {
        // background-image: linear-gradient(
        //   90deg,
        //   rgba(21, 189, 148, 0) 0%,
        //   rgba(21, 189, 148, 0.5) 49%,
        //   rgba(21, 189, 148, 0) 100%
        // );
        background-image: linear-gradient(
          90deg,
          rgba(21, 189, 148, 0) 0%,
          rgba(21, 189, 148, 0.5) 49%,
          rgba(21, 189, 148, 0) 100%
        );

        .indexIcon {
          width: 16px;
          height: 14px;
          margin-right: 6px;
        }
      }
    }
  }

  .rt-alarm-list {
    margin-top: 6px;
    width: 100%;
    height: calc(100% - 126px);
    background: #172537;
    border-radius: 8px;
    font-size: 12px;
    color: #ffffff;
    letter-spacing: 0;
    pointer-events: auto;

    .list-tab {
      display: flex;
      height: 47px;
      justify-content: space-around;
      align-items: center;
      position: relative;
      // padding: 0 1rem;
      font-family: PingFangSC-Regular;
      font-size: 14px;
      color: #ffffff;
      letter-spacing: 0;
      font-weight: 400;

      &::after {
        content: "";
        width: 320px;
        height: 1px;
        position: absolute;
        background: url("~@/assets/images/alarmEvent/alarm/gaojingliebiao_xian_chang.png")
          no-repeat;
        bottom: 0;
      }

      .tab-item {
        position: relative;
        height: 100%;
        width: 50%;
        display: flex;
        justify-content: center;
        // align-items: center;
        cursor: pointer;

        &:hover {
          color: #e8f3ff;
        }

        span {
          margin-top: 16px;
          position: relative;

          .total-num {
            position: absolute;
            right: -25px;
            top: -10px;
            width: 20px;
            height: 20px;
            border-radius: 20px 20px 20px 0;
            font-size: 14px;
            font-style: normal;
            font-weight: 700;
            display: flex;
            align-items: center;
            justify-content: center;
            // background-color: red;
            background-image: linear-gradient(180deg, #ff6a6c 0%, #fb2c2e 100%);
          }
        }
      }

      .tab-item-click {
        color: #e8f3ff;

        &::after {
          content: "";
          width: 140px;
          height: 11px;
          position: absolute;
          background: url("~@/assets/images/alarmEvent/alarm/gaojingliebiao_xian_sel.png")
            no-repeat;
          background-size: contain;
          bottom: -3px;
        }
      }
    }

    .list-table {
      .el-loading-mask {
        // background-color: transparent;
        background: rgba(23, 37, 55, 0.85)
          url("~@/assets/images/comm/loading-dark.gif") 50% 50% no-repeat !important;
        background-size: 100px 80px !important;
      }

      height: calc(100% - 47px - 28px);
      overflow-x: hidden;
      overflow-y: auto;

      // .table-body {
      //   padding: 0 12px;
      // }
    }

    .list-pagination {
      height: 28px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 12px;
      padding: 0 12px;

      .page-number {
        font-size: 12px;
        color: #e8f3ff;
        letter-spacing: 0;
        font-weight: 400;
      }

      .el-pagination {
        display: flex;
        align-items: center;
      }

      .el-pagination button:hover {
        color: #409eff;
      }

      .el-pagination button:disabled {
        background: transparent;
        color: #c0c4cc;
      }

      .el-pagination .btn-next,
      .el-pagination .btn-prev {
        background: transparent;
        color: #fff;
      }

      .el-pagination .el-pager li {
        color: #fff;
        font-weight: 400;
        background: transparent;
        line-height: 22px;
      }

      .el-pagination .el-pager li.active {
        background: #1989fa;
        border-radius: 0.02rem;
        color: #fff;
      }

      .alarm-icon {
        font-size: 16px;
        cursor: pointer;
      }
    }
  }

  //图片视频放大样式
  .alarm-max-file-box {
    width: 926px;
    height: 522px;
    border-radius: 8px;
    background: linear-gradient(180deg, rgba(0, 19, 30, 0.7) 0%, #00131e 100%);
    border-image: linear-gradient(
        360deg,
        rgba(7, 91, 74, 0.75),
        rgba(7, 91, 74, 0.3)
      )
      1 1;
    position: fixed;
    top: 58px;
    left: 475px;
    overflow: hidden;

    .carousel-img-big {
      width: 100%;
      height: 100%;
    }
  }
}
