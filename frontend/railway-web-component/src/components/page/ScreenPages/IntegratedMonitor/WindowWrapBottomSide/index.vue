 template
<!--
 * 底部窗口组件
 * @Description: 用于在页面底部显示和管理一系列窗口的内容区域。
 * @Autor: he.chao
 * @Date: 2022-10-19 15:07:55
 * @LastEditors: liu.yongli
 * @LastEditTime: 2023-12-13 14:36:14
-->
<template>
  <div id="bottom-side-window-ins" class="bottom-side-window"></div>
</template>

<script>
import store from '@/store'
import Vue from 'vue'

export default {
  data() {
    return {
      // 存储所有内容窗口实例的数组
      contentWidows: [],
    }
  },
  props: {},
  methods: {
    /**
     * 添加内容窗口
     * @param {Object} config - 内容窗口的配置对象
     * @param {Object} config.component - 内容窗口的组件
     * @param {Object} config.props - 内容窗口组件的属性
     * @param {String} config.key - 内容窗口的唯一键
     * @param {Function} config.onclose - 关闭内容窗口时的回调函数
     * @param {Function} config.onok - 确认操作时的回调函数
     */
    addContent({
      component,
      props,
      key,
      onclose,
      onok = () => {
        console.log('onok')
      },
    }) {
      const that = this
      let comp
      // 创建一个自定义内容窗口组件
      let Content = Vue.extend({
        // 自定义模板继承
        template: `
          <base-info :propData='nameExtend' :key='key' :close='close' :okCalBack='okCalBack'></base-info>`,
        name: 'child',
        components: {
          'base-info': component, // 弹框用子组件包裹
        },
        data() {
          return {
            key: key,
            nameExtend: props,
          }
        },
        methods: {
          okCalBack() {
            onok && onok(key)
          },
          close() {
            that.closeContent(key)
            onclose && onclose(key)
          },
        },
      })
      const div = document.getElementById('bottom-side-window-ins')
      comp = new Content({ store }).$mount()
      div.appendChild(comp.$el)
      this.contentWidows[key] = comp
    },
    /**
     * 关闭指定内容窗口
     * @param {String} key - 内容窗口的唯一键
     */
    closeContent(key) {
      const comp = this.contentWidows[key]
      if (comp) {
        // 销毁组件
        comp.$destroy()
        const div = document.getElementById('bottom-side-window-ins')
        div.hasChildNodes() && div.removeChild(comp.$el)
        delete this.contentWidows[key]
      }
    },
    /**
     * 关闭所有内容窗口
     */
    closeAllContent() {
      for (const key in this.contentWidows) {
        this.closeContent(key)
      }
    },
    /**
     * 根据前缀关闭部分内容窗口
     * @param {String} prefix - 窗口键的前缀
     */
    closeByIdPrefix(prefix) {
      const groupWindow = this.contentWidows
      groupWindow &&
        Object.keys(groupWindow).forEach(key => {
          const comp = this.contentWidows[key]
          if (comp && key.indexOf(prefix) === 0) {
            // 销毁组件
            comp.$destroy()
            const div = document.getElementById('bottom-side-window-ins')
            div.hasChildNodes() && div.removeChild(comp.$el)
            delete this.contentWidows[key]
          }
        })
    },
  },
}
</script>

<style lang='less' src='./index.less' scoped />
