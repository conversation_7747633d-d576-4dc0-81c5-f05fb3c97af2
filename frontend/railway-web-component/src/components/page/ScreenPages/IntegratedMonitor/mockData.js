export const mockData = {
  "provinceId": "110000",
  "countyId": "110102",
  "cityId": "110100",
  "deviceCode": "11010500831327000012",
  "deviceName": "22222333",
  "channelCode": "410102630102000311130000006",
  "tenantId": "*********",
  "industryCode": "800001",
  "orderId": "20250217*********2533980",
  "videoUrl": "https://slw-base-video-test.obs.cn-north-4.myhuaweicloud.com/ThirdAlarm/video/KLYR-1212102001_7f10bda0ba094caf9bbdb5fc671176a48c9b2e4da43f4025a7f5f57a61823027_976875.mp4?AccessKeyId=6RTWXLU6OWVQWSKHXWTC&Expires=1742263533&Signature=z9VYcuqfHMqQ8d%2B9%2FEG4m2lFbo8%3D",
  "fileImgUrlIcon": "https://slw-base-video-test.obs.cn-north-4.myhuaweicloud.com/ThirdAlarm/pic/KLYR-1212102001_7f10bda0ba094caf9bbdb5fc671176a48c9b2e4da43f4025a7f5f57a61823027_01_976.jpg?AccessKeyId=6RTWXLU6OWVQWSKHXWTC&Expires=1742263533&Signature=2ag1pq64PL7lU%2FSKHqtUathXKkk%3D"
}

export const cameraThemeConfigDetailData = {
  data: [
    {
      "layeIconUrl": "https://video-platform.obs.cn-north-4.myhuaweicloud.com/slyy2/dev/1.0/ecd0c97ddb7c95b8db34f721db7d595f/e9cb9879efe678470abca11f76b379cf/ffec6fdc77d046a593622ff80ada2d4a.svg?AccessKeyId=CRFYWADDOJHXLKROOIXX&Expires=1742544096&Signature=OBmQsznoi5XKCg9e6gs5x%2Baw72g%3D",
      "poiDetailHeight": "378",
      "operCtrl": "M",
      "tenantId": "121062345",
      "poiIconJson": {
        "mapExceptionIcon": {},
        "mapIcon": {}
      },
      "layerName": "摄像机",
      "state": "A",
      "layerCode": "CAMERA",
      "poiDetailJson": {
        "CAMERA": {
          "ctrl": [
            "reltimeBroadcast",
            "playVideo",
            "viewArea",
            "passRoute"
          ],
          "groups": [
            {
              "groupName": "基础信息",
              "content": [
                {
                  "icon": [
                    "copy"
                  ],
                  "label": "设备编号：",
                  "key": "deviceCode"
                },
                {
                  "label": "设备名称：",
                  "key": "deviceName"
                },
                {
                  "label": "设备地址：",
                  "key": "location"
                },
                {
                  "code": "deviceStatus",
                  "label": "设备状态：",
                  "key": "deviceStatus"
                },
                {
                  "label": "设备挂高：",
                  "key": "height"
                },
                {
                  "label": "设备厂家：",
                  "key": "manufacturerName"
                }
              ]
            },
            {
              "dataKey": "channelCode",
              "path": "channels",
              "groupName": "通道信息",
              "singleHead": true,
              "clickable": true,
              "content": [
                {
                  "icon": [
                    "copy"
                  ],
                  "label": "通道编号：",
                  "key": "channelCode"
                },
                {
                  "label": "通道名称：",
                  "key": "channelName"
                }
              ]
            }
          ],
          "title": "摄像机详情"
        }
      },
      "poiDetailWidth": "360",
      "poiDetailUrl": "railway-biz-service/deviceFacilities/detail",
      "showTech": "NATIVE"
    },
    {
      "layeIconUrl": "https://video-platform.obs.cn-north-4.myhuaweicloud.com/slyy2/dev/1.0/ecd0c97ddb7c95b8db34f721db7d595f/e9cb9879efe678470abca11f76b379cf/ffec6fdc77d046a593622ff80ada2d4a.svg?AccessKeyId=CRFYWADDOJHXLKROOIXX&Expires=1742544096&Signature=OBmQsznoi5XKCg9e6gs5x%2Baw72g%3D",
      "poiDetailHeight": "378",
      "operCtrl": "M",
      "tenantId": "121062345",
      "poiIconJson": {
        "mapExceptionIcon": {},
        "mapIcon": {}
      },
      "layerName": "摄像机",
      "state": "A",
      "layerCode": "CAMERA",
      "poiDetailJson": {
        "CAMERA": {
          "ctrl": [
            "reltimeBroadcast",
            "playVideo",
            "viewArea",
            "passRoute"
          ],
          "groups": [
            {
              "groupName": "基础信息",
              "content": [
                {
                  "icon": [
                    "copy"
                  ],
                  "label": "设备编号：",
                  "key": "deviceCode"
                },
                {
                  "label": "设备名称：",
                  "key": "deviceName"
                },
                {
                  "label": "设备地址：",
                  "key": "location"
                },
                {
                  "code": "deviceStatus",
                  "label": "设备状态：",
                  "key": "deviceStatus"
                },
                {
                  "label": "设备挂高：",
                  "key": "height"
                },
                {
                  "label": "设备厂家：",
                  "key": "manufacturerName"
                }
              ]
            },
            {
              "dataKey": "channelCode",
              "path": "channels",
              "groupName": "通道信息",
              "singleHead": true,
              "clickable": true,
              "content": [
                {
                  "icon": [
                    "copy"
                  ],
                  "label": "通道编号：",
                  "key": "channelCode"
                },
                {
                  "label": "通道名称：",
                  "key": "channelName"
                }
              ]
            }
          ],
          "title": "摄像机详情"
        }
      },
      "poiDetailWidth": "360",
      "poiDetailUrl": "railway-biz-service/deviceFacilities/detail",
      "showTech": "NATIVE"
    },

  ]
}
export const cameraDetailData = {
  data: {
    "cityCode": "110100",
    "provinceCode": "110000",
    "latitude": "39.969849000",
    "streetCode": "110109108000",
    "deviceCode": "11080200001327000055",
    "poiDetailJson": {
      "CAMERA": {
        "ctrl": [
          "reltimeBroadcast",
          "playVideo",
          "viewArea",
          "passRoute"
        ],
        "groups": [
          {
            "groupName": "基础信息",
            "content": [
              {
                "icon": [
                  "copy"
                ],
                "label": "设备编号：",
                "key": "deviceCode"
              },
              {
                "label": "设备名称：",
                "key": "deviceName"
              },
              {
                "label": "设备地址：",
                "key": "location"
              },
              {
                "code": "deviceStatus",
                "label": "设备状态：",
                "key": "deviceStatus"
              },
              {
                "label": "设备挂高：",
                "key": "height"
              },
              {
                "label": "设备厂家：",
                "key": "manufacturerName"
              }
            ]
          },
          {
            "dataKey": "channelCode",
            "path": "channels",
            "groupName": "通道信息",
            "singleHead": true,
            "clickable": true,
            "content": [
              {
                "icon": [
                  "copy"
                ],
                "label": "通道编号：",
                "key": "channelCode"
              },
              {
                "label": "通道名称：",
                "key": "channelName"
              }
            ]
          }
        ],
        "title": "摄像机详情"
      }
    },
    "deviceName": "铁塔产业园-11号楼",
    "deviceStatus": 0,
    "countyCode": "110109",
    "streetName": "妙峰山镇",
    "channels": [
      {
        "vertiRange": "0",
        "horizRange": "0",
        "visualRange": "0",
        "channelName": "铁塔产业园-11号楼_0",
        "channelStatus": 0,
        "channelCode": "110802100000000011310000026"
      }
    ],
    "cityName": "北京市",
    "admCode": "110108",
    "modelCode": "dhua",
    "hornDevices": [
      {
        "creator": null,
        "createTime": null,
        "createBy": null,
        "updateBy": null,
        "modifier": null,
        "updateTime": null,
        "deleteFlag": null,
        "tenantId": "*********",
        "userId": null,
        "appVerCode": null,
        "industryCode": null,
        "platformCode": null,
        "dataScope": null,
        "criteriaList": null,
        "screenId": null,
        "screenType": null,
        "dataPermissions": null,
        "id": null,
        "deviceCode": "1234578978945",
        "deviceName": "大连腾飞园区5号喇叭",
        "deviceStatus": "1",
        "deviceStatusName": null,
        "manufacturerCode": "other",
        "manufacturerName": "其他",
        "modelCode": "12321DSDDDDDA",
        "deviceType": "4001",
        "deviceTypeName": "音柱",
        "orderStatus": "1",
        "orderStatusName": null,
        "orgRels": [
          {
            "createBy": null,
            "createTime": null,
            "updateBy": null,
            "updateTime": null,
            "id": "6656853376331368759",
            "tenantId": "*********",
            "deptId": "*********-01",
            "deviceCode": "1234578978945",
            "deleteFlag": null,
            "deviceType": 0,
            "industryCode": "800001",
            "appVerCode": null,
            "deptName": "湖北泰龙互联通信股份有限公司-铁路护路版本-1.0"
          }
        ],
        "orgName": "湖北泰龙互联通信股份有限公司-铁路护路版本-1.0",
        "provinceCode": "110000",
        "provinceName": "北京市",
        "provinceCompany": null,
        "cityCode": "110100",
        "cityName": "北京市",
        "cityCompany": null,
        "countyCode": "110109",
        "countyName": "门头沟区",
        "countyCompany": null,
        "townCode": "110109108000",
        "townName": "妙峰山镇",
        "custCount": null,
        "height": "50",
        "longitude": "116.*********",
        "latitude": "39.*********",
        "location": "北京市门头沟区妙峰山镇中共斜河涧村支部委员会",
        "accessProtocols": null,
        "shareProperty": null,
        "installTime": null,
        "weight": null,
        "heightType": null,
        "siteCode": "f2c97947e8604ad99c49647abeedec9a",
        "siteName": null,
        "isMonitor": "0",
        "orderCode": null,
        "list": null,
        "broadcastResourceCode": null,
        "distance": null,
        "fileUrlList": null,
        "locationCode": null,
        "optType": null,
        "areaCode": null,
        "admCode": null,
        "locationName": null,
        "deptId": null,
        "opType": null,
        "tagCollect": "0"
      }
    ],
    "location": "北京市门头沟区妙峰山镇斜河涧站",
    "provinceName": "北京市",
    "hornDevice": {
      "creator": null,
      "createTime": null,
      "createBy": null,
      "updateBy": null,
      "modifier": null,
      "updateTime": null,
      "deleteFlag": null,
      "tenantId": "*********",
      "userId": null,
      "appVerCode": null,
      "industryCode": null,
      "platformCode": null,
      "dataScope": null,
      "criteriaList": null,
      "screenId": null,
      "screenType": null,
      "dataPermissions": null,
      "id": null,
      "deviceCode": "1234578978945",
      "deviceName": "大连腾飞园区5号喇叭",
      "deviceStatus": "1",
      "deviceStatusName": null,
      "manufacturerCode": "other",
      "manufacturerName": "其他",
      "modelCode": "12321DSDDDDDA",
      "deviceType": "4001",
      "deviceTypeName": "音柱",
      "orderStatus": "1",
      "orderStatusName": null,
      "orgRels": [
        {
          "createBy": null,
          "createTime": null,
          "updateBy": null,
          "updateTime": null,
          "id": "6656853376331368759",
          "tenantId": "*********",
          "deptId": "*********-01",
          "deviceCode": "1234578978945",
          "deleteFlag": null,
          "deviceType": 0,
          "industryCode": "800001",
          "appVerCode": null,
          "deptName": "湖北泰龙互联通信股份有限公司-铁路护路版本-1.0"
        }
      ],
      "orgName": "湖北泰龙互联通信股份有限公司-铁路护路版本-1.0",
      "provinceCode": "110000",
      "provinceName": "北京市",
      "provinceCompany": null,
      "cityCode": "110100",
      "cityName": "北京市",
      "cityCompany": null,
      "countyCode": "110109",
      "countyName": "门头沟区",
      "countyCompany": null,
      "townCode": "110109108000",
      "townName": "妙峰山镇",
      "custCount": null,
      "height": "50",
      "longitude": "116.*********",
      "latitude": "39.*********",
      "location": "北京市门头沟区妙峰山镇中共斜河涧村支部委员会",
      "accessProtocols": null,
      "shareProperty": null,
      "installTime": null,
      "weight": null,
      "heightType": null,
      "siteCode": "f2c97947e8604ad99c49647abeedec9a",
      "siteName": null,
      "isMonitor": "0",
      "orderCode": null,
      "list": null,
      "broadcastResourceCode": null,
      "distance": null,
      "fileUrlList": null,
      "locationCode": null,
      "optType": null,
      "areaCode": null,
      "admCode": null,
      "locationName": null,
      "deptId": null,
      "opType": null,
      "tagCollect": "0"
    },
    "countyName": "门头沟区",
    "height": "50",
    "longitude": "116.*********"
  }
}

export const tunnelThemeConfigDetailData = {
  data: [
    {
      "poiDetailHeight": "410",
      "operCtrl": "D",
      "poiDetailJson": {
        "R:202406041411449843": {
          "ctrl": [],
          "groups": [
            {
              "groupName": "基础信息",
              "content": [
                {
                  "label": "名称：",
                  "key": "resourceName"
                },
                {
                  "label": "地址：",
                  "key": "addr"
                },
                {
                  "label": "高程（m）：",
                  "key": "elevation"
                },
                {
                  "label": "备注：",
                  "key": "remark"
                }
              ]
            },
            {
              "groupName": "图片信息",
              "content": [
                {
                  "inputType": "videoImage",
                  "key": "url"
                }
              ]
            }
          ],
          "title": "隧道详情"
        }
      },
      "poiDetailUrl": "railway-biz-service/resource/detail",
      "layeIconUrl": "https://video-platform.obs.cn-north-4.myhuaweicloud.com/slyy2/dev/1.0/bb08f84d9ddf54459ea7b705612bafa1/39d674a395ee508382042279d2f593d6/d88e5a5caa3046a3a97cc762b65137dc.svg?AccessKeyId=CRFYWADDOJHXLKROOIXX&Expires=1720690718&Signature=GPLvmSuOdf83D2YrYc%2BmJkhL41M%3D",
      "isDefault": "Y",
      "modelCode": "MAP_LAYER_EVENT",
      "tenantId": "*********",
      "poiIconJson": {
        "mapExceptionIcon": {
          "normal": "https://video-platform.obs.cn-north-4.myhuaweicloud.com/slyy2/dev/1.0/bb08f84d9ddf54459ea7b705612bafa1/39d674a395ee508382042279d2f593d6/0ca827db98b744c98578a5e587290fae.svg?AccessKeyId=CRFYWADDOJHXLKROOIXX&Expires=1750923861&Signature=%2FsAc%2Fc3gJ6tjTVHdhzYVGSeiZT8%3D",
          "select": "https://video-platform.obs.cn-north-4.myhuaweicloud.com/slyy2/dev/1.0/bb08f84d9ddf54459ea7b705612bafa1/39d674a395ee508382042279d2f593d6/d38cd011dc3a413d85328c4a277467f5.svg?AccessKeyId=CRFYWADDOJHXLKROOIXX&Expires=1750923861&Signature=nONkiRmS3DjsCfgMNbJfpAjRxyU%3D"
        },
        "mapIcon": {
          "normal": "https://video-platform.obs.cn-north-4.myhuaweicloud.com/slyy2/dev/1.0/bb08f84d9ddf54459ea7b705612bafa1/39d674a395ee508382042279d2f593d6/0ca827db98b744c98578a5e587290fae.svg?AccessKeyId=CRFYWADDOJHXLKROOIXX&Expires=1750923861&Signature=%2FsAc%2Fc3gJ6tjTVHdhzYVGSeiZT8%3D",
          "select": "https://video-platform.obs.cn-north-4.myhuaweicloud.com/slyy2/dev/1.0/bb08f84d9ddf54459ea7b705612bafa1/39d674a395ee508382042279d2f593d6/d38cd011dc3a413d85328c4a277467f5.svg?AccessKeyId=CRFYWADDOJHXLKROOIXX&Expires=1750923861&Signature=nONkiRmS3DjsCfgMNbJfpAjRxyU%3D"
        }
      },
      "layerName": "隧道",
      "state": "A",
      "layerCode": "R:202406041411449843",
      "poiDetailWidth": "360",
      "seq": 4,
      "showTech": "NATIVE"
    },
    {
      "poiDetailHeight": "410",
      "operCtrl": "D",
      "poiDetailJson": {
        "R:202406041411449843": {
          "ctrl": [],
          "groups": [
            {
              "groupName": "基础信息",
              "content": [
                {
                  "label": "名称：",
                  "key": "resourceName"
                },
                {
                  "label": "地址：",
                  "key": "addr"
                },
                {
                  "label": "高程（m）：",
                  "key": "elevation"
                },
                {
                  "label": "备注：",
                  "key": "remark"
                }
              ]
            },
            {
              "groupName": "图片信息",
              "content": [
                {
                  "inputType": "videoImage",
                  "key": "url"
                }
              ]
            }
          ],
          "title": "隧道详情"
        }
      },
      "poiDetailUrl": "railway-biz-service/resource/detail",
      "layeIconUrl": "https://video-platform.obs.cn-north-4.myhuaweicloud.com/slyy2/dev/1.0/bb08f84d9ddf54459ea7b705612bafa1/39d674a395ee508382042279d2f593d6/d88e5a5caa3046a3a97cc762b65137dc.svg?AccessKeyId=CRFYWADDOJHXLKROOIXX&Expires=1720690718&Signature=GPLvmSuOdf83D2YrYc%2BmJkhL41M%3D",
      "isDefault": "Y",
      "modelCode": "MAP_LAYER_EVENT",
      "tenantId": "*********",
      "poiIconJson": {
        "mapExceptionIcon": {
          "normal": "https://video-platform.obs.cn-north-4.myhuaweicloud.com/slyy2/dev/1.0/bb08f84d9ddf54459ea7b705612bafa1/39d674a395ee508382042279d2f593d6/0ca827db98b744c98578a5e587290fae.svg?AccessKeyId=CRFYWADDOJHXLKROOIXX&Expires=1750923861&Signature=%2FsAc%2Fc3gJ6tjTVHdhzYVGSeiZT8%3D",
          "select": "https://video-platform.obs.cn-north-4.myhuaweicloud.com/slyy2/dev/1.0/bb08f84d9ddf54459ea7b705612bafa1/39d674a395ee508382042279d2f593d6/d38cd011dc3a413d85328c4a277467f5.svg?AccessKeyId=CRFYWADDOJHXLKROOIXX&Expires=1750923861&Signature=nONkiRmS3DjsCfgMNbJfpAjRxyU%3D"
        },
        "mapIcon": {
          "normal": "https://video-platform.obs.cn-north-4.myhuaweicloud.com/slyy2/dev/1.0/bb08f84d9ddf54459ea7b705612bafa1/39d674a395ee508382042279d2f593d6/0ca827db98b744c98578a5e587290fae.svg?AccessKeyId=CRFYWADDOJHXLKROOIXX&Expires=1750923861&Signature=%2FsAc%2Fc3gJ6tjTVHdhzYVGSeiZT8%3D",
          "select": "https://video-platform.obs.cn-north-4.myhuaweicloud.com/slyy2/dev/1.0/bb08f84d9ddf54459ea7b705612bafa1/39d674a395ee508382042279d2f593d6/d38cd011dc3a413d85328c4a277467f5.svg?AccessKeyId=CRFYWADDOJHXLKROOIXX&Expires=1750923861&Signature=nONkiRmS3DjsCfgMNbJfpAjRxyU%3D"
        }
      },
      "layerName": "隧道",
      "state": "A",
      "layerCode": "R:202406041411449843",
      "poiDetailWidth": "360",
      "seq": 4,
      "showTech": "NATIVE"
    },
  ]
}
export const tunnelDetailData = {
  data: {
    "resourceId": "202406111722397984",
    "color": "",
    "latitude": "40.024893",
    "modifier": "railway01",
    "icon": "https://video-platform.obs.cn-north-4.myhuaweicloud.com/slyy2/dev/1.0/bb08f84d9ddf54459ea7b705612bafa1/f4b69138255874afac8355fd40a21237/038a8a7fff48487e8da0f15284c3e340.svg?AccessKeyId=CRFYWADDOJHXLKROOIXX&Expires=1750924489&Signature=RM853zSi1FhOmtZ3zIJr6pJQiSI%3D",
    "pageSize": 10,
    "remark": "单独的",
    "resourceProperty": "1",
    "industryCode": "800001",
    "deleteFlag": 0,
    "videoUrl": "",
    "id": 24343,
    "addr": "北京市海淀区清河街道G7京新高速",
    "longitude": "116.317529",
    "elevation": "22",
    "creator": "railway01",
    "resourceName": "北京隧道",
    "updateTime": "2025-03-24 21:33:38",
    "layoutJson": {
      "R:202406041411449843": {
        "ctrl": [],
        "groups": [
          {
            "groupName": "基础信息",
            "content": [
              {
                "label": "名称：",
                "key": "resourceName"
              },
              {
                "label": "地址：",
                "key": "addr"
              },
              {
                "label": "高程（m）：",
                "key": "elevation"
              },
              {
                "label": "备注：",
                "key": "remark"
              }
            ]
          },
          {
            "groupName": "图片信息",
            "content": [
              {
                "inputType": "videoImage",
                "key": "url"
              }
            ]
          }
        ],
        "title": "隧道详情"
      }
    },
    "pageNum": 1,
    "appVerCode": "1.0",
    "imgUrl": "https://video-platform.obs.cn-north-4.myhuaweicloud.com/slyy2/dev/1.0/bb08f84d9ddf54459ea7b705612bafa1/39d674a395ee508382042279d2f593d6/ceeff50168b645d69d371fb7c28e33fd.jpg?AccessKeyId=CRFYWADDOJHXLKROOIXX&Expires=1750924489&Signature=TqY1QKRki0r7sYWZNb%2Ff8N%2BMN0E%3D",
    "isMonitor": 0,
    "createTime": "2024-06-11 17:22:39",
    "resourceTypeId": "202406041411449843",
    "tenantId": "*********",
    "resourceTypeName": "隧道",
    "status": "0"
  }
}

export const orderResourceData = {
  data: {
    "industryDeviceTreesList": [
      {
        "longitude": "116.261964358",
        "latitude": "39.958102729",
        "siteCode": "67c6cfcbb4024446a77c10c3e9b58b47",
        "provinceId": "110000",
        "cityId": "110100",
        "countyId": "110108",
        "siteName": "铁塔产业园区",
        "distance": "0.04992705008727243",
        "location": "北京市海淀区四季青镇常青路",
        "list": [
          {
            "deviceCode": "11080200001327000055",
            "deviceName": "铁塔产业园-11号楼",
            "provinceId": "110000",
            "cityId": "110100",
            "countyId": "110108",
            "height": "50",
            "deviceStatus": "0",
            "channelInfoVOList": [
              {
                "channelId": 89124,
                "channelCode": "110802100000000011310000026",
                "deviceCode": "11080200001327000055",
                "channelName": "铁塔产业园-11号楼_0",
                "channelStatus": 0,
                "streamType": 1,
                "maxGuard": null,
                "maxPreset": 255,
                "maxPatrol": 255,
                "facade": 1,
                "channelType": 1,
                "visualRange": "0",
                "horizRange": "0",
                "vertiRange": "0",
                "creator": "system",
                "creatorDate": "2025-01-13 16:25:13",
                "modifier": "Twqx501",
                "modifierDate": "2025-05-25 08:10:29",
                "deleteFlag": "0"
              }
            ],
            "categoryCode": "2"
          }
        ]
      },
      {
        "longitude": "116.262022020",
        "latitude": "39.958419030",
        "siteCode": "3ac22c896fe0445abec124082da4d2c0",
        "provinceId": "110000",
        "cityId": "110100",
        "countyId": "110108",
        "siteName": "东埠头回迁楼",
        "distance": "35.72826706844752",
        "location": "北京市海淀区四季青镇常青路",
        "list": [
          {
            "deviceCode": "11080200001327000002-33030400832127222346",
            "deviceName": "铁塔产业园区6号楼",
            "provinceId": "110000",
            "cityId": "110100",
            "countyId": "110108",
            "height": "15",
            "deviceStatus": "0",
            "channelInfoVOList": [
              {
                "channelId": 42321,
                "channelCode": "11080200001310000003@33030400832127222346",
                "deviceCode": "11080200001327000002-33030400832127222346",
                "channelName": "铁塔产业园区6号楼_0",
                "channelStatus": 1,
                "streamType": null,
                "maxGuard": null,
                "maxPreset": 0,
                "maxPatrol": 0,
                "facade": null,
                "channelType": null,
                "visualRange": null,
                "horizRange": null,
                "vertiRange": null,
                "creator": null,
                "creatorDate": "2024-05-18 02:29:34",
                "modifier": "zhangfulin555",
                "modifierDate": "2025-05-10 17:28:59",
                "deleteFlag": "0"
              }
            ],
            "categoryCode": "2",
            "imageUrl": "https://video-platform.obs.cn-north-4.myhuaweicloud.com/slyy2/dev/1.0/ecd0c97ddb7c95b8db34f721db7d595f/94736018f6e941164e7ba1757a43dab5/3ad3ae6b2e5b4ab288205e3153ba2514.svg?AccessKeyId=CRFYWADDOJHXLKROOIXX&Expires=1748314512&Signature=7oFEdK%2FqqX64FkLW%2Ft5G0jLIwoE%3D"
          }
        ]
      },
    ],
    "hornDeviceVoList": [],
    "resourceConfsList": {
      "R:202406041411449843": [],
      "R:202405271745072609": []
    }
  }
}

export const videoData = []

export const imgsData = [
  'https://video-platform.obs.cn-north-4.myhuaweicloud.com/slyy2/dev/1.0/c1b0cdc5cff2ef16225b5a90bd2f4eed/a65dc6d804253a38c2bc8f01ceb36dfb/ca7a21c74ba84d18b83796f27820b2fc.png?AccessKeyId=CRFYWADDOJHXLKROOIXX&Expires=1749007683&Signature=xcGXPs%2FXZ%2B1m4IhY2Wj4wK5owfY%3D',
  "https://video-platform.obs.cn-north-4.myhuaweicloud.com/slyy2/dev/1.0/c1b0cdc5cff2ef16225b5a90bd2f4eed/a65dc6d804253a38c2bc8f01ceb36dfb/ca7a21c74ba84d18b83796f27820b2fc.png?AccessKeyId=CRFYWADDOJHXLKROOIXX&Expires=1749007683&Signature=xcGXPs%2FXZ%2B1m4IhY2Wj4wK5owfY%3D",
  'https://video-platform.obs.cn-north-4.myhuaweicloud.com/slyy2/dev/1.0/c1b0cdc5cff2ef16225b5a90bd2f4eed/a65dc6d804253a38c2bc8f01ceb36dfb/693680211b3d4a2293eca80ea5336259.png?AccessKeyId=CRFYWADDOJHXLKROOIXX&Expires=1749001596&Signature=GOtsJ2eODyT8XNe5yNdjlpA%2FmSw%3D',
  'https://video-platform.obs.cn-north-4.myhuaweicloud.com/slyy2/dev/1.0/c1b0cdc5cff2ef16225b5a90bd2f4eed/a65dc6d804253a38c2bc8f01ceb36dfb/693680211b3d4a2293eca80ea5336259.png?AccessKeyId=CRFYWADDOJHXLKROOIXX&Expires=1749001596&Signature=GOtsJ2eODyT8XNe5yNdjlpA%2FmSw%3D',
  'https://video-platform.obs.cn-north-4.myhuaweicloud.com/slyy2/dev/1.0/c1b0cdc5cff2ef16225b5a90bd2f4eed/a65dc6d804253a38c2bc8f01ceb36dfb/693680211b3d4a2293eca80ea5336259.png?AccessKeyId=CRFYWADDOJHXLKROOIXX&Expires=1749001596&Signature=GOtsJ2eODyT8XNe5yNdjlpA%2FmSw%3D',
  'https://video-platform.obs.cn-north-4.myhuaweicloud.com/slyy2/dev/1.0/c1b0cdc5cff2ef16225b5a90bd2f4eed/a65dc6d804253a38c2bc8f01ceb36dfb/693680211b3d4a2293eca80ea5336259.png?AccessKeyId=CRFYWADDOJHXLKROOIXX&Expires=1749001596&Signature=GOtsJ2eODyT8XNe5yNdjlpA%2FmSw%3D'
]

// 广播
export const layoutThemeConfigDetailData = {
  data: [
    {
      "layeIconUrl": "https://video-platform.obs.cn-north-4.myhuaweicloud.com/slyy2/pre/1.0/6a1771e38a628c024055e01ce8f2ad9f/24cf7209e3c07039f715f45b4ce74f59/daab68b2218743058c100e179aaa39fe.svg?AccessKeyId=CRFYWADDOJHXLKROOIXX&Expires=1728465785&Signature=KtQeRhcHg69Zp2Ztl/nzPNzpsbE%3D",
      "poiDetailHeight": "378",
      "operCtrl": "D",
      "tenantId": "123044669",
      "poiIconJson": {
        "mapExceptionIcon": {},
        "mapIcon": {}
      },
      "layerName": "云广播",
      "state": "A",
      "layerCode": "LOUDSPEAKER",
      "poiDetailJson": {
        "LOUDSPEAKER": {
          "ctrl": [
            "broadcast",
            "reltimeBroadcast"
          ],
          "groups": [
            {
              "groupName": "基础信息",
              "content": [
                {
                  "icon": [
                    "copy"
                  ],
                  "label": "设备编号：",
                  "key": "deviceCode"
                },
                {
                  "label": "设备名称：",
                  "key": "deviceName"
                },
                {
                  "label": "设备地址：",
                  "key": "location"
                },
                {
                  "code": "deviceStatus",
                  "label": "设备状态：",
                  "key": "deviceStatus"
                },
                {
                  "label": "设备音量：",
                  "key": "height"
                },
                {
                  "label": "设备挂高：",
                  "key": "height"
                },
                {
                  "label": "设备厂家：",
                  "key": "manufacturerName"
                },
                {
                  "label": "设备型号：",
                  "key": "modelCode"
                }
              ]
            }
          ],
          "title": "云广播详情"
        }
      },
      "poiDetailWidth": "360",
      "poiDetailUrl": "railway-biz-service/deviceFacilities/detail",
      "showTech": "NATIVE"
    },
    {
      "poiDetailHeight": "378",
      "operCtrl": "M",
      "poiDetailJson": {
        "LOUDSPEAKER": {
          "ctrl": [
            "broadcast",
            "reltimeBroadcast"
          ],
          "groups": [
            {
              "groupName": "基础信息",
              "content": [
                {
                  "icon": [
                    "copy"
                  ],
                  "label": "设备编号：",
                  "key": "deviceCode"
                },
                {
                  "label": "设备名称：",
                  "key": "deviceName"
                },
                {
                  "label": "设备地址：",
                  "key": "location"
                },
                {
                  "code": "deviceStatus",
                  "label": "设备状态：",
                  "key": "deviceStatus"
                },
                {
                  "label": "设备音量：",
                  "key": "height"
                },
                {
                  "label": "设备挂高：",
                  "key": "height"
                },
                {
                  "label": "设备厂家：",
                  "key": "manufacturerName"
                },
                {
                  "label": "设备型号：",
                  "key": "modelCode"
                }
              ]
            }
          ],
          "title": "云广播详情"
        }
      },
      "layeIconUrl": "https://video-platform.obs.cn-north-4.myhuaweicloud.com/slyy2/pre/1.0/bb08f84d9ddf54459ea7b705612bafa1/dc8318b68c285babc3f6b00029c115ab/ca7c2942e22b41539db72fdfc7c2ef26.svg?AccessKeyId=CRFYWADDOJHXLKROOIXX&Expires=1721636455&Signature=NLtUoC5mtz7XlIkpyVUvzlSBTuI%3D",
      "isDefault": "Y",
      "modelCode": "MAP_LAYER_EVENT",
      "tenantId": "121063487",
      "poiIconJson": {
        "mapExceptionIcon": {},
        "mapIcon": {}
      },
      "layerName": "云广播",
      "state": "A",
      "layerCode": "LOUDSPEAKER",
      "poiDetailWidth": "360",
      "seq": 2,
      "showTech": "NATIVE"
    },
  ]
}