<template>
  <div class="pass-route-dialog">
    <div class="pass-route-container" :style="cusStyle">
      <i class="el-icon-error" @click="close" />
      <div class="title">过车记录</div>
      <div class="pass-route-content">
        <div class="pass-route-content-inner">
          <!-- <div class="map-container" id="route-map" /> -->
          <RemoteComponentSyncLoader
            v-if="propsComponents['common-comp-map']"
            :showMapControl="false"
            :class="['route-map']"
            chooseMapMemoryKey
            :config="propsComponents['common-comp-map']"
            :mapId="mapId"
            :defaultTileMode="5"
            :tileModes="tileModes"
          />
          <CameraTree />
          <PassRoute v-if="mapLoaded" :mapId="mapId" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { Events } from '@/components/page/ScreenPages/IntegratedMonitor/enum/EventsEnum'
import PassRoute from '@/components/page/ScreenPages/IntegratedMonitor/PassRoute/index.vue'
import { createMap } from '@/utils/map3.0'
import CameraTree from '@/components/page/ScreenPages/IntegratedMonitor/PassRoutePanel/CameraTree.vue'
import { mapMutations } from 'vuex'
import RemoteComponentSyncLoader from '@ct/remote-page-sync-loader/remote-page-sync-loader.umd.js'
import CTMapOl from '@ct/ct_map_ol'
import ss from '@/components/page/ScreenPages/IntegratedMonitor/Map/MapTool/img/2D-dark-map.png'
let routeMap = null // 地图

/**
 * 综合监控页面中的路径规划组件。
 * 用于显示路径规划地图和控制路径图层的切换。
 */
export default {
  inject: ['mapFlag', 'mapRef'],
  components: { PassRoute, CameraTree, RemoteComponentSyncLoader },
  props: {
    propsComponents: {
      type: Object,
    },
    tileModes: {
      type: Array,
      default: [],
    },
  },
  data() {
    return {
      mapId: 'routeMap',
      // 地图是否加载的标志
      mapLoaded: false,
      // 路径规划地图对象
      routeMap: {},
      cusStyle: {},
    }
  },
  computed: {},
  created() {
    // 监听基本类型（通过函数）
    this.$watch(
      () => this.mapFlag(), // 监听函数返回值
      () => {
        // 初始化地图
        this.initMap()
      }
    )
    // this.setScale()
    // window.addEventListener('resize', this.setScale)
  },
  mounted() {
    // 注册地图图层切换事件监听
    this.$EventBus.$on(
      Events.MAP_TOOL_EVT.MAP_LAYER_EXCHANGE,
      this.onMaLayerExchange
    )
  },
  beforeDestroy() {
    // 取消地图图层切换事件监听
    this.$EventBus.$off(
      Events.MAP_TOOL_EVT.MAP_LAYER_EXCHANGE,
      this.onMaLayerExchange
    )
    // window.removeEventListener('resize', this.setScale)
    const _mapRef = this.mapRef.getMapRef(this.mapId)
    if (_mapRef) {
      // 销毁地图
      CTMapOl.MapControl.common.destroyMapInstance({
        mapRef: _mapRef,
      })
      routeMap = null
      this.mapRef.setMapRef(
        this.mapId,
        {
          mapRef: _mapRef,
        },
        true
      )
    }
  },
  methods: {
    ...mapMutations('map', ['setPassRouteSwitch']),
    setScale() {
      // 设计上要求缩放
      const viewportHeight = window.innerHeight
      // console.log('viewportHeight--', viewportHeight)
      // 设计稿1080
      const scale = viewportHeight / 1080
      this.cusStyle = {
        transform: `scale(${scale})`,
      }
    },
    /**
     * 处理地图图层切换事件。
     * @param {number} newval 切换的图层类型，0为矢量图层，1为卫星图层。
     */
    onMaLayerExchange(newval) {
      if (newval === 0) {
        routeMap.changeLayersHj('vector', 0)
      }
      if (newval === 1) {
        routeMap.changeLayersHj('satellite', 1)
      }

      if (newval === 2) {
        routeMap.changeLayersHj('vector', 2)
      }
    },
    /**
     * 初始化地图对象。
     * 设置地图的初始中心点和缩放级别，并加载铁路路线图层。
     */
    initMap() {
      const _mapRef = this.mapRef.getMapRef(this.mapId)
      if (!_mapRef) return
      routeMap = createMap(_mapRef, {
        center: [116.38813, 39.89446],
        zoom: 12,
        maxZoom: 17,
        minZoom: 1,
      })
      this.onMapLoad(routeMap)
      // 显示深色的铁路线路
      routeMap.railwayRouteLayer(true, 5)
      this.mapLoaded = true
    },
    /**
     * 地图加载后的处理函数。
     * @param {Object} map 加载后的地图对象。
     */
    onMapLoad(map) {
      // 当地图加载后操作
    },
    close() {
      this.$EventBus.$emit(Events.PassRouteEvt.CHANGE_PASS_ROUTE_OPEN_STATUS, {
        open: false,
      })
    },
  },
}
</script>

<style lang="less" scoped>
@import './index.less';
</style>