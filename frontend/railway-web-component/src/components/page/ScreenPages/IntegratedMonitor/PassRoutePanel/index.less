.pass-route-dialog {
  .pass-route-container {
    position: fixed;
    width: 1200px;
    height: 800px;
    z-index: 1002;
    background: url(./img/bg_pass_route_1200.png) 100% 100% no-repeat;
    background-size: 100% 100%;
    top: 112px;
    left: 432px;
    transform-origin: left top;
    // position: relative;
    // top: 50%;
    // left: 50%;
    // transform: translate(-50%, -50%);
    .el-icon-error {
      right: 12px;
      position: absolute;
      top: 12px;
      font-size: 16px;
      cursor: pointer;
      color: #fff;
    }
    .title {
      width: 144px;
      height: 50px;
      background: url(./img/gaojingliebiao_xian_sel.png) 100% 100% no-repeat;
      background-size: 100% 100%;
      padding-top: 16px;
      padding-left: 40px;
      font-family: PingFangSC-Medium;
      font-size: 16px;
      color: #e8f3ff;
      letter-spacing: 0;
      text-shadow: 0 0 4px #4f9fff;
      font-weight: 500;
      box-sizing: border-box;
    }

    .pass-route-content {
      width: 100%;
      height: calc(100% - 50px);
      box-sizing: border-box;
      padding: 12px;
      .pass-route-content-inner {
        width: 100%;
        height: 100%;
        position: relative;
        .route-map,
        .map-container {
          position: relative;
          width: 100%;
          height: 100%;
        }
      }
    }
  }
}
