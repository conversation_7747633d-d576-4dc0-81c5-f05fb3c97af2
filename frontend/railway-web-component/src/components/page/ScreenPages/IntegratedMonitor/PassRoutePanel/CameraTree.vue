<template>
  <div class="camera-tree-wrap">
    <div :class="['cur-camera-wrap', openTree && 'cur-camera-wrap-open']" @click="toggleOpenTree">
      <span class="text">{{ curSelectCamera.deviceName }}</span>
      <i class="cus-icon el-icon-arrow-down" />
    </div>
    <div :class="['tree-wrap', openTree && 'tree-open']">
      <div class="tree-filter">
        <el-select v-model="treeParam.type" placeholder="请选择" :popper-append-to-body="false">
          <el-option
            v-for="item in treeTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
        <el-input
          placeholder="输入关键字"
          v-model="filterText"
          suffix-icon="el-icon-search"
          class="filter-input"
        ></el-input>
        <span class="icons">
          <!-- 收藏按钮，根据告警是否已收藏显示不同状态 -->
          <img
            v-if="treeParam.isMonitorFlag === '0'"
            alt="收藏"
            :src="
              require(`@/assets/images/alarmEvent/alarm/shoucang_huang_sel.svg`)
            "
            @click.stop="changeCollect('1')"
          />
          <img
            v-else
            alt="已收藏"
            :src="require(`@/assets/images/alarmEvent/alarm/shoucang_nor.svg`)"
            @click.stop="changeCollect('0')"
          />
        </span>
      </div>
      <div class="tree-content">
        <div class="icons"></div>
        <template v-if="treeData.length">
          <ct-virtual-tree
            v-ct-loading="loading"
            ref="cameraTree"
            :data="treeData"
            :default-checked-keys="[curSelectCamera.deviceCode]"
            :default-expand-all="true"
            :filter-node-method="filterNode"
            :expand-on-click-node="false"
            :indent="6"
            :props="{ children: 'list', label: 'name' }"
            :noLeftPadding="false"
            empty-text="暂无数据"
            :deep="1"
            @refresh="getTreeData"
            :statisticsSelectionShow="true"
            :fixedRoot="true"
            :height="352"
            :isLastChild="true"
            @node-click="onNodeClick"
            node-key="uniqueCode"
          >
            <template v-slot:default="{ node, data }">
              <span class="custom-tree-node" :video-uniqueCode="data?.uniqueCode">
                <span
                  v-if="node.isLeaf"
                  class="iconfont icon-linye_icon_dibugongneng_shexiangji_n custom-tree-node-leafIcon"
                ></span>
                <span :title="node.label" class="node-name">{{ node.label }}</span>
                <span v-if="!node.isLeaf" class="count node-name">{{ data.statistics }}</span>
              </span>
            </template>
          </ct-virtual-tree>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
// import { Events } from '@/components/page/ScreenPages/IntegratedMonitor/enum/EventsEnum';
import { mapMutations } from 'vuex'
import { getMonitorLabelTree } from '@/api/service/imScreenService'
/**
 * 综合监控页面中的路径规划组件。
 * 用于显示路径规划地图和控制路径图层的切换。
 */
export default {
  data() {
    return {
      loading: false,
      openTree: false,
      treeType: '2',
      cusStyle: {},
      treeTypeOptions: [
        {
          label: '按区域展示',
          value: '2',
        },
        {
          label: '按标签展示',
          value: '1',
        },
      ],
      treeParam: {
        type: '2',
        isMonitorFlag: '1', // 是否收藏
      },
      filterText: '',
      treeData: [],
      defaultProps: { label: 'name', children: 'list' },
    }
  },
  computed: {
    curSelectCamera() {
      return this.$store.state.captureInfo.curSelectCamera
    },
  },
  watch: {
    filterText(val) {
      this.$refs.cameraTree.filter(val)
    },
    'treeParam.type': {
      handler(newVal, oldVal) {
        // 修改类型时，清空过滤文字
        if (newVal !== oldVal) {
          this.filterText = ''
        }
      },
    },
    treeParam: {
      handler() {
        this.getTreeData()
      },
      deep: true,
    },
  },
  created() {
    // 设计上要求缩放
    const viewportWidth = window.innerWidth
    // 设计稿1920
    const scale = viewportWidth / 1920
    this.cusStyle = {
      transform: `scale(${scale})`,
    }
  },
  mounted() {
    this.getTreeData()
  },
  methods: {
    ...mapMutations('map', ['setPassRouteSwitch']),
    ...mapMutations('captureInfo', ['setCurSelectCamera']),
    toggleOpenTree() {
      this.openTree = !this.openTree
    },
    /**
     * 获取树节点的标签
     * 该函数用于生成树形结构中节点的标签，标签可能包含节点名称和在线/总数信息
     * @param {Object} node - 树节点对象，包含节点的各种信息
     * @param {Object} data - 节点相关的数据对象，可能包含在线数量和总数量信息
     * @returns {String} - 返回格式化的节点标签字符串
     */
    getTreeLabel(node, data) {
      let name = node.label ? node.label : ''
      if (data.deviceCode) {
        return name
      }
      // 根据数据生成在线/总数的字符串，如果数据不全则为空字符串
      let num =
        data.onlineNum !== undefined && data.videoNum !== undefined
          ? '（' + data.onlineNum + '/' + data.videoNum + '）'
          : ''
      // 获取节点名称，如果不存在则为空字符串
      // 返回格式化的节点标签，包含名称和在线/总数信息
      return name + num
    },
    onNodeClick(data, node) {
      if (node.isLeaf) {
        this.setCurSelectCamera({
          deviceCode: data.deviceCode,
          deviceName: data.name,
        })
        this.openTree = false
      }
    },
    /**
     * 过滤通道
     * 该函数的目的是从给定的树形结构数据中移除具有设备码（deviceCode）的节点，
     * 无论它们是否具有通道码（channelCode），以此来筛选出特定的节点类型
     * @param {Array} tree - 一个包含设备和通道信息的树形结构数组
     */
    dealUniqueCode(tree, uniqueCode) {
      if (!tree?.uniqueCode) {
        if (uniqueCode) {
          tree.uniqueCode = uniqueCode + '_' + tree.code
        } else {
          tree.uniqueCode = tree.code
        }
      }
    },
    /**
     * 视频树格式化
     */
    videoTreeFormat(tree, uniqueCode = '') {
      for (let i = 0; i < tree.length; i++) {
        this.dealUniqueCode(tree[i], uniqueCode)
        const data = tree[i]
        const num =
          data.onlineNum !== undefined && data.videoNum !== undefined
            ? '（' + data.onlineNum + '/' + data.videoNum + '）'
            : ''
        tree[i].statistics = num
        // 如果节点有设备码但没有通道码，删除该节点，因为它不符合显示要求
        if (tree[i].deviceCode && !tree[i].channelCode) {
          //删除设备不显示
          tree[i].list = []
          continue
        }
        if (tree[i].list && tree[i].list.length > 0) {
          this.videoTreeFormat(tree[i].list, tree?.[i]?.uniqueCode)
        }
      }
    },
    async getTreeData() {
      this.loading = true
      const [, resData] = await getMonitorLabelTree(this.treeParam)
      // 测试数据 resData?.length ? resData : getMonitorLabelTreeData.data
      const data = resData
      if (data) {
        // 对视频树结构数据进行格式化处理
        this.videoTreeFormat(data)
        this.treeData = data
      }
      this.loading = false
    },
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    changeCollect(flag) {
      this.treeParam.isMonitorFlag = flag
    },
  },
}
</script>

<style lang="less" scoped>
.camera-tree-wrap {
  position: absolute;
  left: 12px;
  top: 12px;
  z-index: 2;
  .cur-camera-wrap {
    width: 292px;
    height: 40px;
    background: url(./img/bg_cur_camera_292.png) 100% 100% no-repeat;
    background-size: 100% 100%;
    // padding-left: 48px;
    // padding-top: 6px;
    padding: 6px 24px 0 48px;
    position: relative;
    box-sizing: border-box;
    color: #fff;
    font-size: 14px;
    letter-spacing: 0;
    font-weight: 400;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    &::before {
      content: '';
      position: absolute;
      top: 8px;
      left: 24px;
      width: 18px;
      height: 18px;
      background: url(./img/icon_camera_12_n.svg) 100% 100% no-repeat;
      background-size: 100% 100%;
    }
    .text {
      display: inline-block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .cus-icon {
      transition: all 0.3s;
      font-size: 14px;
    }
    &.cur-camera-wrap-open {
      .cus-icon {
        transform: rotate(180deg);
      }
    }
  }
  .tree-wrap {
    margin-top: 6px;
    width: 280px;
    height: 0;
    transition: all 0.3s;
    overflow: hidden;
    background: #1b2538;
    &.tree-open {
      height: 416px;
    }
    .tree-filter {
      width: 100%;
      // height: 32px;
      background: rgba(79, 159, 255, 0.2);
      border-radius: 4px;
      display: flex;
      align-items: center;
      padding-right: 6px;
      box-sizing: border-box;
      .icons {
        display: flex;
        img {
          width: 12px;
          height: 11px;
          cursor: pointer;
        }
      }
      .filter-input {
        flex: 1;
      }

      /deep/.el-input__inner {
        width: 100%;
        background: transparent;
        border-radius: 4px;
        border: none;
        color: #e8f3ff;
      }

      /deep/.el-select {
        width: 115px;
        color: #e8f3ff;
        position: relative;
        height: 100%;

        .el-select-dropdown {
          position: absolute !important;
          top: 21px !important;
          left: 3px !important;
        }
      }

      /deep/ .el-select-dropdown {
        box-shadow: unset;
      }
    }
    .tree-content {
      width: 100%;
      height: calc(100% - 32px);
      .el-tree {
        background: transparent;
        color: #e8f3ff;
        height: 100%;
        display: flex;
        flex-direction: column;

        /deep/ .vue-recycle-scroller {
          flex: 1;
        }

        .custom-tree-node {
          display: flex;
          align-items: center;
          font-size: 14px;
          padding-right: 12px;
          height: 32px;
          line-height: 32px;
          flex: 1;
          min-width: 0;
          width: 100%;
          .node-name {
            display: inline-block;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          .custom-tree-node-leafIcon {
            margin-right: 6px;
            color: #909399;
            font-size: 14px;
          }
          .icon-linye_icon_dibugongneng_shexiangji_n {
            width: 12px;
            height: 12px;
            background: url(./img/icon_camera_12_n.svg) 100% 100% no-repeat;
            background-size: 100% 100%;
            flex-shrink: 0;

            &::before {
              display: none;
            }
          }
          .channel.checked .channel-icon ~ .node-name {
            color: #4f9fff !important;
          }
        }
        /deep/ .el-tree-node__content > div {
          display: flex;
        }
        /deep/ .el-tree-node__content:hover,
        /deep/ .el-upload-list__item:hover {
          background: rgba(79, 159, 255, 0.4) !important;
        }
        /deep/ .el-tree-node:focus > .el-tree-node__content {
          background: rgba(79, 159, 255, 0.4) !important;
        }
        /deep/ .ct-tech-ui.el-loading-mask.ct-loading {
          background-color: transparent !important;
        }
        /deep/ .el-tree__empty-block {
          margin-top: 40px;
        }
      }
    }
  }
}
</style>
