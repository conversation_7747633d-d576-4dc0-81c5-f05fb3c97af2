/*
 * @Description: 
 * @Author: liu.yongli
 * @Date: 2025-07-07 09:00:23
 * @LastEditTime: 2025-07-07 15:23:49
 * @LastEditors: liu.yongli
 */
import store from '@/store';
import Vue from 'vue';

/**
 * 组件处理对象，用于创建和管理Vue组件。
 */
const ComponentHandle = {
  /**
   * 创建一个组件实例。
   *
   * @param {Object} options 组件配置对象。
   * @param {string} options.wrapper 组件挂载的DOM元素ID，默认为'left-side-window-ins'。
   * @param {Object} options.component 要创建的组件对象。
   * @param {Object} options.props 组件的props配置。
   * @param {string} options.key 组件的唯一键。
   * @param {string} options.group 组件所属的组。
   * @param {Function} options.onclose 组件关闭时的回调函数，默认为空函数。
   * @param {Function} options.mapRef 地图实例。
   * @returns {Object} 返回创建的组件实例。
   */
  createComponent({
    wrapper = 'left-side-window-ins',
    component,
    props,
    key,
    group,
    onclose = () => {
      console.log('ComponentHandle onclose');
    },
    mapRef = null,
  }) {
    let comp;
    // 自定义弹出窗口组件
    let Content = Vue.extend({
      // 自定义模板
      template: `
        <base-info class='window-content' :propData='nameExtend'
                   :close='close'></base-info>`,
      name: 'child',
      components: {
        'base-info': component, // 引用传入的组件作为子组件
      },
      provide() {
        return {
          mapRef,
        }
      },
      data() {
        return {
          nameExtend: props, // 传递props数据给子组件
        };
      },
      methods: {
        close(params) {
          onclose && onclose(params); // 调用关闭回调函数
        },
      },
    });
    // 获取挂载容器并创建组件实例
    const div = document.getElementById(wrapper);
    comp = new Content({ store }).$mount();
    div.appendChild(comp.$el);
    return comp;
  },
};
export default ComponentHandle;
