<template>
  <div
    ref="reference"
    :class="[
      'el-cascader',
      realSize && `el-cascader--${realSize}`,
      { 'is-disabled': isDisabled },
    ]"
    v-clickoutside="() => toggleDropDownVisible(false)"
    @mouseenter="inputHover = true"
    @mouseleave="inputHover = false"
    @click="() => toggleDropDownVisible(readonly ? undefined : true)"
  >
    <el-input
      ref="input"
      v-model="inputValue"
      :size="realSize"
      :placeholder="placeholder"
      :readonly="readonly"
      :disabled="isDisabled"
      :validate-event="false"
      :class="{ 'is-focus': dropDownVisible }"
      @focus="handleFocus"
      @blur="handleBlur"
      @input="handleInput"
    >
      <template slot="suffix">
        <i
          v-if="clearBtnVisible"
          key="clear"
          class="el-input__icon el-icon-circle-close"
          @click.stop="handleClear"
        ></i>
        <i
          v-else
          key="arrow-down"
          :class="[
            'el-input__icon',
            'el-icon-arrow-down',
            dropDownVisible && 'is-reverse',
          ]"
          @click.stop="toggleDropDownVisible()"
        ></i>
      </template>
    </el-input>

    <transition name="el-zoom-in-top" @after-leave="handleDropdownLeave">
      <div
        v-show="dropDownVisible"
        ref="popper"
        :class="[
          'el-popper',
          'el-cascader__dropdown',
          'custom_cascader_list',
          popperClass,
        ]"
      >
        <div class="parent_list">
          <template v-if="optionsData.length">
            <ul>
              <li
                v-for="(item, index) in optionsData"
                :key="item.labelId"
                :class="[
                  'el-cascader__suggestion-item',
                  checkedValue.findIndex((t) => t === item.labelId) > -1 &&
                    'is-checked',
                ]"
                :tabindex="-1"
                @click="handleParentClick(item, index)"
              >
                <span
                  v-if="
                    inputValue === null ||
                    inputValue === '' ||
                    checkedValue.length > 0
                  "
                  >{{ item.labelName }}</span
                >
                <span
                  v-if="
                    checkedValue.length === 0 &&
                    inputValue &&
                    inputValue.length > 0
                  "
                  >{{
                    item.labelName.substring(
                      0,
                      item.labelName.indexOf(inputValue)
                    )
                  }}<span class="font-blue">{{ inputValue }}</span
                  >{{
                    item.labelName.slice(
                      item.labelName.indexOf(inputValue) + inputValue.length
                    )
                  }}</span
                >
                <i
                  v-if="checkedValue.findIndex((t) => t === item.labelId) > -1"
                  class="el-icon-check"
                ></i>
              </li>
            </ul>
          </template>
          <slot v-else name="empty">
            <ul>
              <li class="el-cascader__empty-text">
                {{ t('el.cascader.noMatch') }}
              </li>
            </ul>
          </slot>
        </div>
        <div class="child_list" v-show="childShow">
          <template v-if="childData.length">
            <ul>
              <li
                v-for="item in childData"
                :key="item.deviceCode"
                :class="['el-cascader__suggestion-item']"
                :tabindex="-1"
                @click="handleChildClick(item)"
              >
                <span>{{ item.deviceName }}</span>
              </li>
            </ul>
          </template>
          <slot v-else name="empty">
            <ul>
              <li class="el-cascader__empty-text">
                {{ t('el.cascader.noMatch') }}
              </li>
            </ul>
          </slot>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
import ElInput from 'element-ui/packages/input';
import ElTag from 'element-ui/packages/tag';
import { t } from 'element-ui/src/locale';
import Emitter from 'element-ui/src/mixins/emitter';
import Locale from 'element-ui/src/mixins/locale';
import Migrating from 'element-ui/src/mixins/migrating';
import Clickoutside from 'element-ui/src/utils/clickoutside';
import {
  addResizeListener,
  removeResizeListener,
} from 'element-ui/src/utils/resize-event';
import { isDef } from 'element-ui/src/utils/shared';
import { isFunction } from 'element-ui/src/utils/types';
import { isEmpty, isEqual, kebabCase } from 'element-ui/src/utils/util';
import Popper from 'element-ui/src/utils/vue-popper';
import debounce from 'throttle-debounce/debounce';

const MigratingProps = {
  expandTrigger: {
    newProp: 'expandTrigger',
    type: String,
  },
  changeOnSelect: {
    newProp: 'checkStrictly',
    type: Boolean,
  },
  hoverThreshold: {
    newProp: 'hoverThreshold',
    type: Number,
  },
};

const PopperMixin = {
  props: {
    placement: {
      type: String,
      default: 'bottom-start',
    },
    appendToBody: Popper.props.appendToBody,
    visibleArrow: {
      type: Boolean,
      default: true,
    },
    arrowOffset: Popper.props.arrowOffset,
    offset: Popper.props.offset,
    boundariesPadding: Popper.props.boundariesPadding,
    popperOptions: Popper.props.popperOptions,
    transformOrigin: Popper.props.transformOrigin,
  },
  methods: Popper.methods,
  data: Popper.data,
  beforeDestroy: Popper.beforeDestroy,
};

const InputSizeMap = {
  medium: 36,
  small: 32,
  mini: 28,
};

export default {
  name: 'ElCascader',

  directives: { Clickoutside },

  mixins: [PopperMixin, Emitter, Locale, Migrating],

  inject: {
    elForm: {
      default: '',
    },
    elFormItem: {
      default: '',
    },
  },

  components: {
    ElInput,
    ElTag,
  },

  props: {
    initData: Array,
    optionsData: Array,
    parentClick: Function,
    childClick: Function,

    value: {},
    options: Array,
    props: Object,
    size: String,
    placeholder: {
      type: String,
      default: () => t('el.cascader.placeholder'),
    },
    disabled: Boolean,
    clearable: Boolean,
    filterable: Boolean,
    filterMethod: Function,
    separator: {
      type: String,
      default: ' / ',
    },
    showAllLevels: {
      type: Boolean,
      default: true,
    },
    collapseTags: Boolean,
    debounce: {
      type: Number,
      default: 300,
    },
    beforeFilter: {
      type: Function,
      default: () => () => {
        console.log('beforeFilter');
      },
    },
    popperClass: String,
  },

  data() {
    return {
      dropDownVisible: false,
      checkedValue: [],
      inputHover: false,
      inputValue: null,
      presentText: null,
      presentTags: [],
      checkedNodes: [],
      filtering: false,
      suggestions: [],
      inputInitialHeight: 0,
      pressDeleteCount: 0,

      childShow: false,
      childData: [],
    };
  },

  computed: {
    realSize() {
      const _elFormItemSize = (this.elFormItem || {}).elFormItemSize;
      return this.size || _elFormItemSize || (this.$ELEMENT || {}).size;
    },
    tagSize() {
      return ['small', 'mini'].indexOf(this.realSize) > -1 ? 'mini' : 'small';
    },
    isDisabled() {
      return this.disabled || (this.elForm || {}).disabled;
    },
    config() {
      const config = this.props || {};
      const { $attrs } = this;

      Object.keys(MigratingProps).forEach((oldProp) => {
        const { newProp, type } = MigratingProps[oldProp];
        let oldValue = $attrs[oldProp] || $attrs[kebabCase(oldProp)];
        if (isDef(oldProp) && !isDef(config[newProp])) {
          if (type === Boolean && oldValue === '') {
            oldValue = true;
          }
          config[newProp] = oldValue;
        }
      });

      return config;
    },
    leafOnly() {
      return !this.config.checkStrictly;
    },
    readonly() {
      return !this.filterable;
    },
    clearBtnVisible() {
      if (
        !this.clearable ||
        this.isDisabled ||
        this.filtering ||
        !this.inputHover
      ) {
        return false;
      }

      return !!this.inputValue;
    },
    panel() {
      return this.$refs.panel;
    },
  },

  watch: {
    disabled() {
      this.computePresentContent();
    },
    value(val) {
      if (!isEqual(val, this.checkedValue)) {
        this.checkedValue.push(val);
        this.computePresentContent();
      }
    },
    options: {
      handler: function () {
        this.$nextTick(this.computePresentContent);
      },
      deep: true,
    },
    presentText(val) {
      this.inputValue = val;
    },
    presentTags(val, oldVal) {
      if (val.length || oldVal.length) {
        this.$nextTick(this.updateStyle);
      }
    },
    filtering(val) {
      this.$nextTick(this.updatePopper);
    },
  },

  mounted() {
    this.optionsData = this.initData;
    const { input } = this.$refs;
    if (input && input.$el) {
      this.inputInitialHeight =
        input.$el.offsetHeight || InputSizeMap[this.realSize] || 40;
    }

    this.filterHandler = debounce(this.debounce, () => {
      const { inputValue } = this;

      if (!inputValue) {
        this.filtering = false;
        return;
      }

      const before = this.beforeFilter(inputValue);
      if (before && before.then) {
        before.then(this.getSuggestions);
      } else if (before !== false) {
        this.getSuggestions();
      } else {
        this.filtering = false;
      }
    });

    addResizeListener(this.$el, this.updateStyle);
  },

  beforeDestroy() {
    removeResizeListener(this.$el, this.updateStyle);
  },

  methods: {
    /**
     * 控制下拉框的可见性
     * @param {boolean} visible - 指定下拉框的可见性，如果未定义，则根据当前可见性取反
     */
    toggleDropDownVisible(visible) {
      // 如果组件被禁用，则不进行任何操作
      if (this.isDisabled) {
        return;
      }
      // 获取当前的下拉框可见性
      const { dropDownVisible } = this;
      // 获取输入框的引用
      const { input } = this.$refs;
      // 如果传入了visible，则使用之，否则与当前的dropDownVisible取反
      visible = isDef(visible) ? visible : !dropDownVisible;
      // 如果可见性发生了变化
      if (visible !== dropDownVisible) {
        // 更新下拉框的可见性
        this.dropDownVisible = visible;
        // 如果下拉框变为可见，则更新popper的位置
        if (visible) {
          this.$nextTick(() => {
            this.updatePopper();
          });
        }
        // 更新输入框的aria-expanded属性
        input.$refs.input.setAttribute('aria-expanded', visible);
        // 触发visible-change事件
        this.$emit('visible-change', visible);
      }
      // 根据已选值更新输入框的值
      if (this.checkedValue.length > 0) {
        this.initData.forEach((item) => {
          if (this.checkedValue.findIndex((val) => val === item.labelId) > -1) {
            this.inputValue = item.labelName;
          }
        });
      } else {
        // 如果没有已选值，则清空输入框的值
        this.inputValue = '';
      }
      // 重置选项数据为初始数据
      this.optionsData = this.initData;
    },
    /**
     * 处理下拉框离开事件
     */
    handleDropdownLeave() {
      // 停止过滤
      this.filtering = false;
      // 销毁popper
      this.doDestroy();
    },
    /**
     * 处理输入框聚焦事件
     * @param {Event} e - 聚焦事件对象
     */
    handleFocus(e) {
      // 触发focus事件
      this.$emit('focus', e);
    },
    /**
     * 处理输入框失去焦点事件
     * @param {Event} e - 失焦事件对象
     */
    handleBlur(e) {
      // 触发blur事件
      this.$emit('blur', e);
    },
    /**
     * 处理输入框值变化事件
     * @param {string} val - 新的输入值
     * @param {Event} event - 输入事件对象
     */
    handleInput(val, event) {
      // 如果下拉框不可见且输入了值，则将其可见
      !this.dropDownVisible && this.toggleDropDownVisible(true);
      // 如果事件正在合成中，则不进行处理
      if (event && event.isComposing) {
        return;
      }
      // 根据输入值进行过滤处理或重置选项数据
      if (val) {
        this.filterHandler();
        this.childShow = false;
      } else {
        this.optionsData = this.initData;
        this.filtering = false;
        // 如果有已选值，则显示子选项
        if (this.checkedValue.length > 0) {
          this.childShow = true;
        }
      }
    },
    /**
     * 处理清除按钮点击事件
     */
    handleClear() {
      // 清空显示文本、输入框值、已选值、隐藏的子选项数据，并重置选项数据为初始数据
      this.presentText = '';
      this.inputValue = '';
      this.checkedValue = [];
      this.childShow = false;
      this.childData = [];
      this.optionsData = this.initData;
      // 触发clearHandle事件
      this.$emit('clearHandle', null);
    },
    /**
     * 计算当前显示的文本内容
     */
    computePresentContent() {
      // 下一个tick后计算并更新当前文本
      this.$nextTick(() => {
        this.computePresentText();
      });
    },
    /**
     * 更新当前文本
     */
    computePresentText() {
      // 获取已选值和配置信息
      const { checkedValue, config } = this;
      // 如果有已选值
      if (checkedValue.length > 0) {
        // 根据已选值获取节点
        const node = this.panel.getNodeByValue(checkedValue);
        // 如果节点存在，并且满足条件（严格模式下或节点是叶子节点）
        if (node && (config.checkStrictly || node.isLeaf)) {
          // 更新当前文本为节点的文本
          this.presentText = node.getText(this.showAllLevels, this.separator);
          return;
        }
      }
      // 如果没有已选值或节点不符合条件，则清空当前文本
      this.presentText = null;
    },
    /**
     * 获取建议选项
     */
    getSuggestions() {
      // 根据输入值过滤选项数据
      const filterOptionsData = this.initData.filter((item) =>
        item.labelName.includes(this.inputValue)
      );
      // 更新选项数据
      this.optionsData = filterOptionsData;
    },
    /**
     * 处理子选项点击事件
     * @param {Object} item - 被点击的选项
     */
    handleChildClick(item) {
      // 触发change事件
      this.$emit('change', item);
      // 隐藏下拉框
      this.toggleDropDownVisible(false);
    },
    /**
     * 处理父选项点击事件
     * @param {Object} item - 被点击的父选项
     * @param {number} index - 父选项的索引
     */
    handleParentClick(item, index) {
      // 根据已选值中是否包含当前项的labelId，来决定是移除还是添加到已选值
      const i = this.checkedValue.findIndex((val) => val === item.labelId);
      if (i > -1) {
        this.checkedValue.splice(i, 1);
      } else {
        this.checkedValue.push(item.labelId);
      }
      // 根据已选值更新输入框的值和子选项数据
      if (this.checkedValue.length > 0) {
        this.inputValue = item.labelName;
        let pChildData = [];
        const pCheckedValue = [...this.checkedValue];
        this.initData.forEach((val) => {
          const ii = pCheckedValue.findIndex((p) => p === val.labelId);
          if (ii > -1) {
            pChildData.push(...val.list);
          }
        });
        this.childData = pChildData;
        this.childShow = true;
      } else {
        // 如果没有已选值，则清空输入框和子选项数据，并隐藏子选项
        this.inputValue = '';
        this.childData = [];
        this.childShow = false;
      }
      // 重置选项数据为初始数据
      this.optionsData = this.initData;
      // 处理并发送父选项点击事件
      let pParentClickItem = { ...item };
      pParentClickItem.list = [...this.childData];
      this.$emit('parentClick', pParentClickItem);
    },
    /**
     * 更新组件的样式
     */
    updateStyle() {
      // 获取组件的根元素和输入框的初始高度
      const { $el, inputInitialHeight } = this;
      // 如果在服务端渲染或根元素不存在，则不进行任何操作
      if (this.$isServer || !$el) {
        return;
      }
      // 获取引用的suggestionPanel和输入框元素
      const { suggestionPanel } = this.$refs;
      const inputInner = $el.querySelector('.el-input__inner');
      // 如果输入框元素不存在，则不进行任何操作
      if (!inputInner) {
        return;
      }
      // 获取标签元素
      const tags = $el.querySelector('.el-cascader__tags');
      // 如果存在suggestionPanel，则更新suggestionList的最小宽度为输入框的宽度
      let suggestionPanelEl = null;
      if (suggestionPanel && (suggestionPanelEl = suggestionPanel.$el)) {
        const suggestionList = suggestionPanelEl.querySelector(
          '.el-cascader__suggestion-list'
        );
        suggestionList.style.minWidth = inputInner.offsetWidth + 'px';
      }

      if (tags) {
        const offsetHeight = Math.round(tags.getBoundingClientRect().height);
        const height = Math.max(offsetHeight + 6, inputInitialHeight) + 'px';
        inputInner.style.height = height;
        if (this.dropDownVisible) {
          this.updatePopper();
        }
      }
    },

    /**
     * public methods
     */
    getCheckedNodes(leafOnly) {
      return this.panel.getCheckedNodes(leafOnly);
    },
  },
};
</script>

<style lang="less" scoped>
.custom_cascader_list {
  display: flex;
  background: rgba(5, 46, 80, 0.8);
  border: 1px solid rgba(31, 221, 255, 1);
  border-radius: 4px;
  color: #fff;

  .parent_list,
  .child_list {
    height: 200px;
    width: 180px;
    overflow-y: overlay;
    overflow-x: hidden;

    li {
      span {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      &:hover {
        background: #0090ff40;
      }

      &.is-checked {
        color: #fff;
        font-weight: 700;
        background: #0090ff82;
      }

      .font-blue {
        color: #1fddff;
      }
    }
  }

  .el-cascader__suggestion-item {
    background: transparent;
  }

  .child_list {
    border-left: solid 1px #9d9d9d;
  }
}
</style>
