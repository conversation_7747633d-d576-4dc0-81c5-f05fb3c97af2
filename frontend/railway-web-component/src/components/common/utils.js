import store from '@/store';
import { UUID } from 'uuidjs';
import VueObj from 'vue';
import { CfgEnum } from '@/components/page/ScreenPages/IntegratedMonitor/enum/CfgEnum'

// 数字0-9的中文表示
const changeNum = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
// 数字单位：十、百、千、万
const unit = ['', '十', '百', '千', '万'];

/**
 * 将数字转换为中文表示
 * @param {number} num - 需要转换的数字
 * @returns {string} - 转换后的中文数字字符串
 */
const toChinesNum = function (num) {
  num = parseInt(num);
  let overWan = Math.floor(num / 10000);
  let noWan = num % 10000;
  if (noWan.toString().length < 4) {
    noWan = '0' + noWan;
  }
  return overWan ? getWan(overWan) + '万' + getWan(noWan) : getWan(num);
};

/**
 * 辅助函数，将数字转换为中文表示，处理万以上的数字
 * @param {number} temp - 需要转换的数字
 * @returns {string} - 转换后的中文数字字符串
 */
/**
 * 数字转换中文
 * @param temp
 * @returns {*|string}
 */
const getWan = (temp) => {
  let strArr = temp.toString().split('').reverse();
  let newArr = [];
  // 改成for
  for (let index = 0; index < strArr.length; index += 1) {
    let item = strArr[index];
    if (item !== '0') {
      newArr.unshift(changeNum[item] + unit[index]);
      continue;
    }
    newArr.unshift(changeNum[item]);
  }
  let numArr = [];
  for (let index = 0; index < newArr.length; index += 1) {
    let item = newArr[index];
    if (item === '零') {
      continue;
    }
    numArr.push(index);
  }
  if (newArr.length <= 1) {
    return newArr[0];
  }
  return getNewNumber(newArr, numArr);
};

/**
 * 处理零的特殊情况，合并数字单位
 * @param {Array} newArr - 经过处理的数字数组
 * @param {Array} numArr - 非零数字的索引数组
 * @returns {string} - 合并后的中文数字字符串
 */
const getNewNumber = function (newArr, numArr) {
  let newNum = '';
  newArr.forEach((m, n) => {
    if (newArr[newArr.length - 1] === '零') {
      if (n <= numArr[numArr.length - 1]) {
        newNum += m;
      }
    } else {
      newNum += m;
    }
  });
  return newNum;
};

/**
 * 根据数值转换为对应的中文文本
 * @param {number} val - 需要转换的数值
 * @returns {string} - 转换后的中文文本
 */
const formatTxt = function (val) {
  if (val) {
    if (val == '0') {
      return '否';
    }
    if (val == '1') {
      return '是';
    }
    return val;
  }
  return '--';
};

/**
 * 格式化值，若值为空则返回'--'
 * @param {any} val - 需要格式化的值
 * @returns {string} - 格式化后的字符串
 */
export const formatVal = function (val) {
  if (isNotEmpty(val)) {
    return val;
  }
  return '--';
};

/**
 * 在地图上显示信息窗口
 * @param {Object} map - 地图对象
 * @param {Object} options - 信息窗口配置项，包括内容、数据、位置、偏移量、锚点等
 * @param {Function} onclose - 信息窗口关闭时的回调函数
 * @returns {AMap.InfoWindow} - 创建的信息窗口对象
 */
const showWeatherInfoWindow = (
  map,
  { content, data, position, offset, anchor = null, avoid = null },
  onclose = () => {
    console.info('-----------no callback--------');
  }
) => {
  let infoWindow = new AMap.InfoWindow({
    isCustom: true, //使用自定义窗体
    // retainWhenClose: true,
    closeWhenClickMap: true,
    anchor: anchor,
    offset: offset,
    avoid,
  });
  infoWindow.on('close', () => {
    onclose(infoWindow);
  });
  let component = createInfoDom(content, data, () => {
    onclose(infoWindow);
  });
  infoWindow.setContent(component.$el);

  infoWindow.open(map, position);

  return infoWindow;
};

/**
 * 飞行到地图上的指定位置，并调整地图视野范围
 * @param {Object} map - 地图对象
 * @param {Object} item - 目标位置的经纬度信息
 * @param {Object} options - 飞行选项，包括动画过渡、周围边距、最大缩放级别等
 */
const flyTo = (map, item, { avoid = [300, 60, 100, 60], maxZoom = 14 }) => {
  map.panTo([item.lng, item.lat]);
  map.setFitView(null, false, avoid, maxZoom);
};

/**
 * 创建信息窗口的DOM元素
 * @param {Object} comp - 信息窗口的组件定义
 * @param {Object} item - 传递给信息窗口组件的数据
 * @param {Function} cbClose - 信息窗口关闭时的回调函数
 * @returns {Object} - 创建的Vue组件实例
 */
const createInfoDom = (
  comp,
  item,
  cbClose = () => {
    console.info('-----------no callback--------');
  }
) => {
  //自定义弹框
  let Content = VueObj.extend({
    //自定义模板继承
    template: `
      <base-info :propData='nameExtend' :onClose='onClose'></base-info>`,
    name: 'child',
    components: {
      'base-info': comp, //弹框用子组件包裹
    },
    data() {
      return {
        nameExtend: item,
      };
    },
    methods: {
      onClose() {
        cbClose();
      },
    },
  });
  return new Content({ store }).$mount();
};

/**
 * 从URL查询字符串中获取指定参数的值
 * @param {string} name - 参数名
 * @returns {string|null} - 参数值，若参数不存在则返回null
 */
const getQueryString = (name) => {
  let reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
  let r = window.location.search.substr(1).match(reg);
  if (r != null) {
    return decodeURIComponent(r[2]);
  }
  return null;
};

/**
 * 判断值是否既不为空也不为undefined
 * @param {*} value - 需要检查的值
 * @returns {boolean} - 如果值既不为空也不为undefined，返回true，否则返回false
 */
export const isEmptyOrUndefined = (value) => {
  return value === null || undefined === value;
};

/**
 * 从本地存储中获取用户特定数据
 * @param {string} name - 数据名
 * @returns {*} - 存储的值
 */
export const getUserLocalStorage = (name) => {
  return getLocalStorage(`${name}-${window.userInfo.userId}`);
};

/**
 * 获取localStorage
 */
export const getLocalStorage = (name) => {
  if (!name) {
    return;
  }
  return JSON.parse(window.localStorage.getItem(name));
};


/**
 * 将数据存储到本地存储中，指定用户
 * @param {string} name - 数据名
 * @param {*} content - 需要存储的数据
 */
export const setUserLocalStorage = (name, content) => {
  setLocalStorage(`${name}-${window.userInfo.userId}`, content);
};

/**
 * 从本地存储中移除用户特定数据
 * @param {string} name - 数据名
 */
export const removeUserLocalStorage = (name) => {
  removeLocalStorage(`${name}-${window.userInfo.userId}`);
};

/**
 * 存储数据到本地存储
 * @param {string} name - 数据名
 * @param {*} content - 需要存储的数据
 */
export const setLocalStorage = (name, content) => {
  if (!name) {
    return;
  }
  if (typeof content !== 'string') {
    content = JSON.stringify(content);
  }
  window.localStorage.setItem(name, content);
};

/**
 * 从本地存储中获取数据
 * @param {string} name - 数据名
 * @returns {*} - 存储的值
 */
export const removeLocalStorage = (name) => {
  if (!name) {
    return;
  }
  window.localStorage.removeItem(name);
};
/**
 * 获取当前选中的区域范围。
 *
 * @returns {Function} 返回一个函数，该函数用于获取选中的区域范围。
 */
export const getSelectedRegion = () => {
  return store.getters.getSelectionRange;
};

/**
 * 判断给定的值是否为非空字符串。
 *
 * @param {*} value - 待检查的值。
 * @returns {Function} 返回一个函数，该函数用于判断给定的值是否为非空字符串。
 */
export const isNotEmptyString = (value) => {
  return isNotEmpty(value);
};

/**
 * 判断给定的值是否不为空。
 *
 * @param {*} value - 待检查的值。
 * @returns {boolean} 如果值不为空，则返回true；否则返回false。
 */
export const isNotEmpty = (value) => {
  if (!value) {
    return false;
  }
  if (value instanceof String && value.trim() === '') {
    return false;
  }
  return !(value instanceof Array && value.length === 0);
};

/**
 * 生成一个随机数。
 *
 * @param {number} number - 随机数的范围。
 * @returns {Function} 返回一个函数，用于生成指定范围内的随机数。
 */
const rand = (number) => {

  let today = new Date();

  let seed = today.getTime();

  /**
   * 生成随机数。
   *
   * @returns {number} 返回一个介于0和1之间的随机数。
   */
  const rnd = () => {
    seed = (seed * 9301 + 49297) % 233280;
    return seed / (233280.0);
  };

  return Math.ceil(rnd() * number) / number;
};

/**
 * 生成一个唯一标识符。
 *
 * @returns {string} 返回一个唯一标识符。
 */
export const uuid = () => {
  return UUID.generate();
};

/**
 * 获取URL的域名部分包括端口。
 *
 * 该函数通过正则表达式匹配URL，以提取协议、域名和端口号。
 * 返回匹配到的完整URL字符串，如果没有匹配到，则返回null。
 *
 * @param {string} href - 需要解析的URL字符串。
 * @returns {string|null} - 匹配到的URL字符串，如果没有匹配到则返回null。
 */
export const getUrlDomain = (href) => {
  // 定义正则表达式，用于匹配URL的协议、域名和可选的端口号。
  const reg = new RegExp(/(\w+):\/\/([^/:]+)(:\d*)?/)

  // 使用正则表达式匹配href，获取URL的各个组成部分。
  let matchObj = href.match(reg)

  // 如果匹配成功，返回匹配到的完整URL字符串；否则返回null。
  if (matchObj) {
    return matchObj[0]
  } else {
    return null
  }
}

/**
 * 事件筛选参数
 * @param {string} filterState  筛选组件的操作状态
 * @returns obj
 */
export const getEvtParams = (filterState) => {
  let params = {};
  // 事件筛选参数
  if (filterState === 'confirm') {
    params = { ...VueObj.prototype.$store.state.map.eventFilterParams }
  }
  // 获取当前选择的区域
  // 加入到入参中
  const areaParams = getUserLocalStorage(CfgEnum.STORAGE_KEY.SELECT_REGION)
  Object.keys({
    provinceId: '',
    cityId: '',
    countyId: '',
  }).forEach((item, i) => {
    areaParams?.nodes?.[i] && (params[item] = areaParams.nodes[i])
  })
  return params;
};

/**
 * 全景展示事件筛选参数
 * @returns obj
 */
export const getFullViewEvtParams = (filterState) => {
  let params = {
    ...getEvtParams(filterState),
    isFocus: undefined,
  }
  return params;
};
/**
 * 默认导出包含各种实用函数的对象。
 *
 * @returns {Object} 返回一个包含各种实用函数的对象。
 */
export default {
  toChinesNum,
  formatTxt,
  showWeatherInfoWindow,
  getQueryString,
  flyTo,
};

