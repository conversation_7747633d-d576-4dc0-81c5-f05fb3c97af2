/**
 * 导入Vue库
 */
import Vue from 'vue';

/**
 * 定义一个名为dialogDrag的Vue指令，用于使对话框具有拖拽功能
 * @param {HTMLElement} el - 指令绑定的元素
 * @param {Object} binding - 指令的绑定对象
 * @param {VNode} vnode - 指令对应的虚拟节点
 * @param {VNode} oldVnode - 旧的虚拟节点
 */
Vue.directive('dialogDrag', {
  bind(el, binding, vnode, oldVnode) {
    // 获取对话框的标题栏元素和对话框本身元素
    const dialogHeaderEl = el.querySelector('.el-dialog__header');
    const dragDom = el.querySelector('.el-dialog');

    // 为对话框标题栏添加可拖拽的样式
    dialogHeaderEl.style.cssText += ';cursor:move;';
    dragDom.style.cssText += ';top:0px;';

    /**
     * 获取元素的样式属性，兼容IE和非IE浏览器
     * @param {HTMLElement} dom - 需要获取样式的元素
     * @param {string} attr - 需要获取的样式属性
     * @returns {string} - 元素的样式属性值
     */
    const sty = (() => {
      if (window.document.currentStyle) {
        return (dom, attr) => dom.currentStyle[attr];
      } else {
        return (dom, attr) => getComputedStyle(dom, false)[attr];
      }
    })();

    // 添加鼠标按下事件监听器，实现拖拽功能
    dialogHeaderEl.onmousedown = (e) => {
      // 计算鼠标按下时鼠标位置与对话框位置的偏移量
      const disX = e.clientX - dialogHeaderEl.offsetLeft;
      const disY = e.clientY - dialogHeaderEl.offsetTop;

      // 获取屏幕尺寸和对话框尺寸
      const screenWidth = document.body.clientWidth; // body当前宽度
      const screenHeight = document.documentElement.clientHeight; // 可见区域高度(应为body高度，可某些环境下无法获取)
      const dragDomWidth = dragDom.offsetWidth; // 对话框宽度
      const dragDomheight = dragDom.offsetHeight; // 对话框高度

      // 计算对话框可拖拽的最小和最大位置
      const minDragDomLeft = dragDom.offsetLeft;
      const maxDragDomLeft = screenWidth - dragDom.offsetLeft - dragDomWidth;
      const minDragDomTop = dragDom.offsetTop;
      const maxDragDomTop = screenHeight - dragDom.offsetTop - dragDomheight;

      // 获取并处理对话框的left和top样式值，以兼容百分比和像素值
      let styL = sty(dragDom, 'left');
      let styT = sty(dragDom, 'top');
      if (styL.includes('%')) {
        styL = +document.body.clientWidth * (+styL.replace(/\%/g, '') / 100);
        styT = +document.body.clientHeight * (+styT.replace(/\%/g, '') / 100);
      } else {
        styL = +styL.replace(/\px/g, '');
        styT = +styT.replace(/\px/g, '');
      }

      // 添加鼠标移动事件监听器，实时更新对话框位置
      document.onmousemove = function (e2) {
        // 计算对话框的新位置，并限制在可拖拽范围内
        let left = e2.clientX - disX;
        let top = e2.clientY - disY;
        if (-left > minDragDomLeft) {
          left = -minDragDomLeft;
        }
        if (left > maxDragDomLeft) {
          left = maxDragDomLeft;
        }
        if (-top > minDragDomTop) {
          top = -minDragDomTop;
        }
        if (top > maxDragDomTop) {
          top = maxDragDomTop;
        }

        // 更新对话框的位置
        dragDom.style.cssText += `;left:${left + styL}px;top:${top + styT}px;`;
      };

      // 添加鼠标松开事件监听器，清除鼠标移动事件监听器
      document.onmouseup = function (e3) {
        document.onmousemove = null;
        document.onmouseup = null;
      };
    };
  },
});
