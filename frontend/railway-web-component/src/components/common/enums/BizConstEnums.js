/**
 * 业务常量枚举对象，用于定义和存储业务中使用的常量枚举。
 * 这样做的目的是为了提高代码的可读性和可维护性，通过命名常量代替硬编码的字符串或数字。
 */
const BizConstEnums = {
  /**
   * 技术类型枚举，定义了两种技术类型：NATIVE和IFRAME。
   */
  techType: {
    /**
     * 原生Native类型。
     */
    NATIVE: {
      name: '原生Native',
      value: 'NATIVE',
      apiLabel: '详情接口'
    },
    /**
     * 内嵌IFRAME类型。
     */
    IFRAME: {
      name: '内嵌',
      value: 'IFRAME',
      apiLabel: '访问地址'
    }
  },
};
export default BizConstEnums;
