<!--
 * @Description: 组件用于渲染一个基于ECharts的图表。
 * @Autor: he.chao
 * @Date: 2022-10-19 15:07:55
 * @LastEditors: liu.yongli
 * @LastEditTime: 2023-12-13 14:36:14
-->
<template>
  <div
    v-resize='initChart'
    style='width: 100%;height: 100%;'
    :id='chartId'
  />
</template>

<script>
import { uuid } from '@/components/common/utils';

/**
 * 图表组件。
 * 该组件使用ECharts库来渲染图表。它接受一系列的props，包括图表配置(option)、图表ID(chartId)、
 * 数据(data)和颜色(color)。组件会在挂载时初始化图表，并在数据变化时更新图表。
 */
export default {
  props: {
    /**
     * ECharts图表的配置项。
     * 这是一个默认的配置项对象，用于创建一个饼图。它定义了图表的样式和初始数据。
     */
    option: {
      type: Object,
      default: function() {
        const _this = this;
        return {
          series: [
            {
              type: 'pie',
              radius: ['40%', '70%'],
              avoidLabelOverlap: false,
              label: {
                show: false,
                position: 'center'
              },
              itemStyle: {
                normal: {
                  color: function(colors) {
                    return _this.color[colors.dataIndex % _this.color.length];
                  }
                }
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: 10,
                  fontWeight: 'bold'
                }
              },
              labelLine: {
                show: false
              },
              data: [
                {
                  value: 1048,
                  name: 'Search Engine'
                },
                {
                  value: 735,
                  name: 'Direct'
                },
                {
                  value: 580,
                  name: 'Email'
                },
                {
                  value: 484,
                  name: 'Union Ads'
                },
                {
                  value: 300,
                  name: 'Video Ads'
                }
              ]
            }
          ]
        };
      }
    },
    /**
     * 图表的容器ID。
     * 默认情况下，会生成一个唯一的ID，但也可以通过props传入一个特定的ID。
     */
    chartId: {
      type: String,
      default: () => {
        return uuid();
      }
    },
    /**
     * 图表的数据。
     * 这是一个默认为空数组的prop，用于存储图表显示的具体数据。当数据发生变化时，图表会自动更新。
     */
    data: {
      type: Array,
      default: () => {
        return [];
      }
    },
    /**
     * 图表中颜色的数组。
     * 默认为空数组，用于根据数据索引为图表的各个部分指定颜色。
     */
    color: {
      type: Array,
      default: () => {
        return [];
      }
    }
  },
  watch: {
    /**
     * 监听data的变化。
     * 当data数组的内容发生变化时，会调用initChart方法来更新图表。
     */
    data: {
      handler(newVal, oldVal) {
        this.initChart();
      },
      deep: true
    }
  },
  data: function() {
    return {};
  },
  mounted() {
    /**
     * 在组件挂载后初始化图表。
     * 使用$nextTick确保DOM已经更新后再初始化ECharts实例。
     */
    this.$nextTick(() => {
      this.initChart();
    });
  },
  methods: {
    /**
     * 初始化图表的方法。
     * 该方法首先根据chartId获取图表的DOM元素，然后初始化ECharts实例，并设置图表的配置项。
     */
    initChart() {
      const chart = this.$echarts.init(document.getElementById(this.chartId));
      this.option.series[0].data = this.data;
      chart.setOption(this.option);
    }
  }
};
</script>
