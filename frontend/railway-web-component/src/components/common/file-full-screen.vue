<!--  告警详情中环节信息的图片视频 全屏展示组件  -->
<template>
  <div class='show-imgs-container' @contextmenu='disableRightClick'>
    <div class='video-main' @mouseover='isIn = true' @mouseout='isIn = false'>
      <div class='close-btn' @click='closeFull'></div>
      <img :src='videoImgUrl' style='max-width: 100%;max-height: 100%' alt=''
           v-if="videoImgUrl?.indexOf('.mp4') === -1 && videoImgUrl?.indexOf('.MP4') === -1" />
      <video-player class='video video-player vjs-custom-skin' :playsinline='true' :options='playerOptions'
                    @statechanged='playStatechanged($event)' v-else />
      <div class='prevLeft' @click='prevUrl' v-if='showBtn'>
        <div class='iconfont icon-zhankai leftIcon'></div>
      </div>
      <div class='nextRigth' @click='nextUrl' v-if='showBtn'>
        <div class='iconfont icon-zhankai rightIcon'></div>
      </div>
    </div>
  </div>
</template>
<script>

export default {
  name: 'file-full-screen',
  props: {
    fileFullList: {
      type: Object,
      default: {}
    },
    showBtn: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      isIn: false,//鼠标是否在播放器内
      videoImgUrl: '',
      playerOptions: {
        playbackRates: [0.5, 1.0, 1.5, 2.0], //播放速度
        autoplay: true, //如果true,浏览器准备好时开始回放。
        loop: true, // 导致视频一结束就重新开始。
        muted: true, // 默认情况下将会消除任何音频。
        preload: 'auto', // 建议浏览器在<video>加载元素后是否应该开始下载视频数据。auto浏览器选择最佳行为,立即开始加载视频（如果浏览器支持）
        language: 'zh-CN',
        fluid: true, // 当true时，Video.js player将拥有流体大小。换句话说，它将按比例缩放以适应其容器。
        hls: false,
        sources: [
          {
            // type: 'application/x-mpegURL',
            type: 'video/mp4',
            src: ''
          }
        ],
        aspectRatio: '16:9',
        poster: '', // 封面地址
        choosed: false, //被选中的
        notSupportedMessage: '视频录像服务维护中', //允许覆盖Video.js无法播放媒体源时显示的默认信息。
        controlBar: {
          timeDivider: false,
          durationDisplay: false,
          remainingTimeDisplay: false,
          fullscreenToggle: false  //全屏按钮
        }
      }
    };
  },
  mounted() {
    document.body.appendChild(this.$el);//把该页面加载到body上,以便让它的层级高于父辈文件
    this.getUrl();
    if(this.fileFullList?.videoImgUrl?.length <2){
        this.showBtn = false;
    }
  },
  methods: {
    deassignSource() {
      setTimeout(() => {
        // 检查 videoImgUrl 是否以 '.mp4' 或 '.MP4' 结尾
        if (this.videoImgUrl?.indexOf('.mp4') !== -1 || this.videoImgUrl?.indexOf('.MP4') !== -1) {
          // 将 playerOptions 的 source 设置为 videoImgUrl
          this.playerOptions.sources[0].src = this.videoImgUrl;
        }
      });
    },
    findUrlIndex() {
      let urlIndex = null;
      // 遍历 videoImgSrc 数组，查找与 videoImgUrl 匹配的元素索引
      this.videoImgSrc.forEach((item, index) => {
        if (this.videoImgUrl === item) {
          urlIndex = index;
        }
      });
      return urlIndex; // 返回匹配的索引或 null
    },
    getUrl() {
      // 将 fileFullList 中的 videoImgUrl 数组赋值给 videoImgSrc
      this.videoImgSrc = this.fileFullList.videoImgUrl;
      // 将 fileFullList 中的 index 赋值给 videoImgIndex
      this.videoImgIndex = this.fileFullList.index;
      // 初始化 videoImgUrl 为空字符串
      this.videoImgUrl = '';
      // 根据 videoImgIndex 从 videoImgSrc 数组中获取对应的 URL 并赋值给 videoImgUrl
      this.videoImgUrl = this.videoImgSrc[this.videoImgIndex];
      // 调用 deassignSource 方法更新播放源
      this.deassignSource();
    },
    /**
     * 禁用鼠标右键
     */
    disableRightClick(e) {
      // 阻止默认的右键菜单事件
      e.preventDefault();
    },
    /**
     * 上一张
     */
    prevUrl() {
      // 获取当前 videoImgUrl 在 videoImgSrc 数组中的索引
      let urlIndex = this.findUrlIndex();
      // 如果索引大于 0，则将 videoImgUrl 设置为数组中前一个元素
      if (urlIndex > 0) {
        this.videoImgUrl = this.videoImgSrc[urlIndex - 1];
      } else {
        // 如果索引为 0，则将 videoImgUrl 设置为数组中最后一个元素
        this.videoImgUrl = this.videoImgSrc[this.videoImgSrc.length - 1];
      }
      // 调用 deassignSource 方法更新播放源
      this.deassignSource();
    },
    /**
     * 下一张
     */
    nextUrl() {
      // 获取当前 videoImgUrl 在 videoImgSrc 数组中的索引
      let urlIndex = this.findUrlIndex();
      // 如果索引小于数组长度减一，则将 videoImgUrl 设置为数组中下一个元素
      if (urlIndex < this.videoImgSrc.length - 1) {
        this.videoImgUrl = this.videoImgSrc[urlIndex + 1];
      } else {
        // 如果索引为数组长度减一，则将 videoImgUrl 设置为数组中第一个元素
        this.videoImgUrl = this.videoImgSrc[0];
      }
      // 调用 deassignSource 方法更新播放源
      this.deassignSource();
    },
    closeFull() {
      // 触发 'closeFull' 事件，用于关闭全屏模式
      this.$emit('closeFull');
    },
    // 视频播放失败重新加载
    playStatechanged(status) {
      // 如果播放状态中包含错误信息
      if (status.error) {
        console.log('player current update state', status); // 输出当前播放状态信息到控制台
        // 修改视频播放器错误提示信息为“告警视频正在努力生成中，请稍后...”
        $($('.vjs-modal-dialog .vjs-modal-dialog-content')[0]).html('告警视频正在努力生成中，请稍后...');
      }
    }
  }
};
</script>
<style lang='less' scoped>
.show-imgs-container {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 2999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn {
  //关闭按钮
  width: 22px;
  height: 22px;
  position: absolute;
  top: 11%;
  right: 14%;
  background: url("~@/assets/images/alarmEvent/eventFile/icon_close.svg") 100% 100% no-repeat;
  background-size: 100% 100%;
  cursor: pointer;
  z-index: 2;
}

.video-main {
  height: 70%;
  width: 70%;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(192, 196, 204, 0.20);
}

.prevLeft {
  width: 40px;
  height: 40px;
  background: rgb(79 159 255);
  border-radius: 50%;
  position: absolute;
  top: 45%;
  left: 11%;
  color: #FFFFFF;
  line-height: 50px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nextRigth {
  width: 40px;
  height: 40px;
  background: rgb(79 159 255);
  border-radius: 50%;
  position: absolute;
  top: 45%;
  left: 86%;
  color: #FFFFFF;
  display: flex;
  flex-wrap: wrap;
  align-content: center;
  justify-content: center;
  cursor: pointer;
}

.rightIcon {
  font-size: 24px;
  transform: rotate(270deg);
}

.leftIcon {
  font-size: 24px;
  transform: rotate(90deg);
}

.video-player {
  height: 100%;
  width: 100%;
}

.video-player /deep/ .video-js.vjs-fluid {
  width: 100%;
  height: 100%;
  padding: 0;
}
</style>
