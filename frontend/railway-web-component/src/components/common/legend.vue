<!--
 * @Description  : 地图图层图例
 * <AUTHOR> wnj
 * @Date         : 2023-06-08 15:01:34
 * @LastEditors: liu.yongli
 * @LastEditTime: 2025-02-21 09:32:12
 * @FilePath     :  / src / components / page / alarmEvent / components / legend.vue
-->

<template>
  <div
    :class="['map_legend_main_outter',className]"
    v-if="screenType === 'monitor'"
    :style="{bottom:$store.state.map.mapToolSwitch ? pxToRem(145) : pxToRem(20)}"
  >
    <div class="legend_title">
      <div class="shu1" />
      <div class="shu2" />
      <div class="shu3" />
      <span>图例</span>
      <div class="shu3" />
      <div class="shu2" />
      <div class="shu1" />
    </div>
    <div
      v-for="item in monitorLayers"
      :key="item.key"
      class="legend-item"
      @click="handleClick(item.key)"
    >
      <img
        :alt="item.title"
        :src="`${item.key !== 'juhe'? item.icon : require(`@/assets/images/alarmEvent/legend/icon_${item.key}.png`)}`"
      />
      {{ item.title }}
    </div>
  </div>
  <div
    :class="['map_legend_main_outter',className]"
    v-else
    :style="{bottom:pxToRem(124), right:pxToRem(55)}"
  >
    <div class="legend_title">
      <div class="shu1" />
      <div class="shu2" />
      <div class="shu3" />
      <span>图例</span>
      <div class="shu3" />
      <div class="shu2" />
      <div class="shu1" />
    </div>
    <div
      v-for="item in fullViewLayers"
      :key="item.key"
      class="legend-item"
      @click="handleClick(item.key)"
    >
      <img
        :alt="item.title"
        :src="require(`@/assets/images/alarmEvent/legend/icon_${item.key}.png`)"
      />
      {{ item.title }}
    </div>
  </div>
</template>

<script>
// 引入依赖组件和工具
import bus from '@/components/common/bus.js'
import { mapGetters } from 'vuex'
import feifachuangru from '@/assets/images/alarmEvent/legend/tuli_feifachuangru.svg' // 导入点图标
import piaofuwu from '@/assets/images/alarmEvent/legend/tuli_piaofuwu.svg' // 导入点图标
import qita from '@/assets/images/alarmEvent/legend/tuli_qita.svg' // 导入点图标
import weifaduifang from '@/assets/images/alarmEvent/legend/tuli_weifaduifang.svg' // 导入聚合图标
import weifashigong from '@/assets/images/alarmEvent/legend/tuli_weifashigong.svg' // 导入聚合图标

// 事件类型
const eventTypes = {
  非法闯入: {
    title: '非法闯入',
    key: 'ffcr',
    icon: feifachuangru,
  },
  漂浮物: {
    title: '漂浮物',
    key: 'pfw',
    icon: piaofuwu,
  },
  违法排放堆放: {
    title: '违法排放堆放',
    key: 'wfpfdf',
    icon: weifaduifang, // 点图标
  },
  违法施工: {
    title: '违法施工',
    key: 'wfsg',
    icon: weifashigong, // 点图标
  },
  其它: {
    title: '其它',
    key: 'qt',
    icon: qita, // 点图标
  },
  其他: {
    title: '其他',
    key: 'qt',
    icon: qita, // 点图标
  },
}
// 默认的图例
const eventDefaultLegend = [
  eventTypes['非法闯入'],
  eventTypes['漂浮物'],
  eventTypes['违法排放堆放'],
  eventTypes['违法施工'],
  eventTypes['其他'],
]
const defaultLegend = [
  {
    key: 'juhe',
    title: '图层聚合',
  },
]

export default {
  // 父组件传参
  props: {
    // 大屏类型：monitor：综合监测， fullView ：全景视图
    screenType: {
      type: String,
      required: true,
    },
    // 自定义类名
    className: {
      type: String,
      default: '',
    },
  },
  // 引入依赖组件
  components: {
    // InfoItem: InfoItem,
  },
  // 组件变量区域
  data() {
    return {
      monitorLayers: [], // 综合监测图例
      fullViewLayers: [
        {
          key: 'aiMoniter',
          title: '智能识别事件',
        },
        {
          key: 'manReport',
          title: '人工上报事件',
        },
        {
          key: 'juhe',
          title: '图层聚合',
        },
      ], // 全景展示图例
      layers: [
        // {
        //     key: "lukuang",
        //     title: "路况",
        // },
        {
          key: 'shexiangji',
          title: '摄像机',
        },
        {
          key: 'qingbaoban',
          title: '情报板',
        },
        {
          key: 'qixiangzhan',
          title: '气象站',
        },
        {
          key: 'gouzaowu',
          title: '构造物',
          children: [
            {
              key: 'qiaoliang',
              title: '桥梁',
            },
            {
              key: 'suidao',
              title: '隧道',
            },
          ],
        },
        {
          key: 'yunguangbo',
          title: '云广播',
        },
        {
          key: 'zhuanghao',
          title: '桩号',
        },
        {
          key: 'shoufeizhan',
          title: '收费站',
        },
        {
          key: 'fuwuqu',
          title: '服务区',
        },
      ],
      selectedLayers: [
        // "shexiangji",
        // "qixiangzhan",
        // "qiaoliang",
        // "suidao",
        // "shoufeizhan",
        // "fuwuqu",
      ], // 默认选中：摄像机、气象站、桥梁、隧道、收费站、服务区
      showChilren: [],
    }
  },
  // 组件的方法区域
  methods: {
    handleClick: function (key) {
      // if (key === 'gouzaowu') {
      //     this.haveChildrenClick(key);
      //     return;
      // }
      // if (this.selectedLayers.includes(key)) {
      //     this.selectedLayers = this.selectedLayers.filter(
      //         (item) => item != key
      //     );
      // } else {
      //     this.selectedLayers.push(key);
      // }
      // bus.$emit("selectedLayers", {
      //     key,
      //     visible: this.selectedLayers.includes(key),
      // });
    },
    haveChildrenClick(key) {
      if (this.showChilren.includes(key)) {
        this.showChilren = this.showChilren.filter(item => item != key)
      } else {
        this.showChilren.push(key)
      }
    },
    isSelected(item) {
      let isSelected = false
      if (item.children) {
        item.children.forEach(t => {
          if (!isSelected) {
            isSelected = this.selectedLayers.includes(t.key)
          }
        })
      } else {
        isSelected = this.selectedLayers.includes(item.key)
      }
      return isSelected
    },
  },
  // 创建完成钩子
  created() {
    // 初始化
  },
  // 挂载完成后钩子
  mounted() {
    // this.handleClick("lukuang");
    // 切换城市需要清除当前地图的图层，然后在新城市地图重新渲染这些图层
    bus.$on('onProvinceCityChange', ({ provinceId, cityId }) => {
      this.selectedLayers.forEach(item => {
        bus.$emit('selectedLayers', {
          key: item,
          visible: false,
        })
      })
      setTimeout(() => {
        this.selectedLayers.forEach(item => {
          bus.$emit('selectedLayers', {
            key: item,
            visible: true,
          })
        })
      }, 1000)
    })
  },
  computed: {
    ...mapGetters('event', ['getEvtTypeList']),
  },
  // 监听器
  watch: {
    getEvtTypeList: {
      handler(val) {
        if (this.screenType === 'monitor') {
          const list = val.length
            ? val.map(v => eventTypes?.[v.dictLabel])
            : eventDefaultLegend
          this.monitorLayers = [...list, ...defaultLegend]
        }
      },
      immediate: true,
      deep: true,
    },
  },
  // 组件销毁后执行
  destroyed() {
    bus.$off('onProvinceCityChange')
  },
}
</script>

<style lang="scss" scoped>
@import '~@/assets/styles/px-to-rem';
.map_legend_main_outter {
  background: rgba(15 38 71 / 80%);
  border: px-to-rem(1) solid rgba(0, 145, 255, 0.58);
  position: absolute;
  right: px-to-rem(390);
  bottom: px-to-rem(20);
  width: px-to-rem(160);
  box-shadow: inset 0px 0px px-to-rem(6) px-to-rem(5) rgba(0, 168, 255, 0.27);
  z-index: 1;

  .legend_title {
    height: px-to-rem(35);
    margin-top: px-to-rem(7);
    padding: 0 px-to-rem(25);
    font-family: PingFangSC-Regular, sans-serif;
    font-size: px-to-rem(20);
    color: #1fddff;
    font-weight: 400;
    display: flex;
    justify-content: space-between;
    align-items: center;

    span {
      background: -webkit-linear-gradient(top, #ffffff, #63b3ff);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .shu1 {
      width: px-to-rem(1);
      height: 10%;
      transform: scaleX(-1);
      background: #0091ff;
    }

    .shu2 {
      width: px-to-rem(1);
      height: 20%;
      transform: scaleX(-1);
      background: #0091ff;
    }

    .shu3 {
      width: px-to-rem(1);
      height: 30%;
      transform: scaleX(-1);
      background: #0091ff;
    }
  }
  .legend-item {
    height: px-to-rem(35);
    padding: 0 px-to-rem(12);
    display: flex;
    justify-content: flex-start;
    align-items: center;
    cursor: pointer;
    pointer-events: auto;
    font-size: px-to-rem(17);
    color: rgba(255, 255, 255, 0.7);

    img {
      padding-right: px-to-rem(5);
      width: px-to-rem(28);
      // height: px-to-rem(28);
    }
  }

  .legend-item.selected {
    color: #00ffdb;
  }
}
</style>
