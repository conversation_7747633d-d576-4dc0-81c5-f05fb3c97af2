<!-- svg组件 -->
<template>
  <svg class="svg-icon" :class="svgClass" aria-hidden="true">
    <use :xlink:href="iconName" :href="iconName" />
  </svg>
</template>

<script>
/**
 * SvgIcon组件 - 用于显示SVG图标。
 *
 * 通过接收svgName属性来确定要显示的图标，利用computed属性计算出icon的URL和类名。
 * 其中，iconName计算出SVG符号的URL，svgClass计算出应用到SVG元素上的类名。
 *
 * @param {string} svgName - 图标名称，用于确定要显示的SVG图标。
 */
export default {
  name: 'SvgIcon',
  props: {
    // svg 的名称
    svgName: {
      type: String,
      required: true,
    },
  },
  computed: {
    /**
     * 计算SVG符号的URL。
     *
     * @returns {string} - SVG符号的URL，格式为`#icon-图标名称`。
     */
    iconName() {
      return `#icon-${this.svgName}`
    },
    /**
     * 计算应用到SVG元素上的类名。
     *
     * 如果svgName存在，则返回`svg-icon`加上图标名称的字符串；
     * 否则，只返回`svg-icon`。
     *
     * @returns {string} - SVG元素的类名。
     */
    svgClass() {
      if (this.svgName) {
        return 'svg-icon ' + this.svgName
      } else {
        return 'svg-icon'
      }
    },
  },
}
</script>

<style scoped>
.svg-icon {
  width: 24px;
  height: 24px;
  margin-right: 20px;
  fill: currentColor;
  overflow: hidden;
}
</style>
