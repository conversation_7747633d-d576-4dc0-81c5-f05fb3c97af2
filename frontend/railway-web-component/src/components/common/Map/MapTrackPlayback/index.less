.map_opt_content {
    width: 100%;
    height: 100%;
    position: relative;

    /deep/ .map_tool {
        position: absolute;
        top: 20px;
        right: 20px;
        display: flex;
        align-items: center;
        justify-content: center;

        .tool_btn {
            display: flex;
            width: 25px;
            height: 25px;
            align-items: center;
            justify-content: center;
            background-color: #fefefe;
            box-shadow: 0px 1px 8px 1px;
            border-radius: 6px;
            cursor: pointer;
            margin-left: 20px;

            > i {
                display: inline-block;
                width: 20px;
                height: 20px;
            }

            .btn_back {
                background: url(./img/back.svg) no-repeat 100% 100%;
                background-size: 100%;
            }

            .btn_start {
                background: url(./img/start_play.svg) no-repeat 100% 100%;
                background-size: 100%;
            }

            .btn_stop {
                background: url(./img/stop_play.svg) no-repeat 100% 100%;
                background-size: 100%;
            }
        }
    }
}

/deep/ .car_point_content {
    width: 280px;
    background-color: #00000085;
    color: #fefefe;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    padding: 10px;
}
