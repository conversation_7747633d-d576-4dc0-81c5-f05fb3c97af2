<template>
  <!-- 地图操作内容的容器 -->
  <div class="map_opt_content">
    <!-- 地图显示区域 -->
    <div id="map" class="map" style="height: 100%"></div>
    <!-- 地图工具栏 -->
    <div class="map_tool">
      <!-- 返回按钮的工具提示 -->
      <el-tooltip effect="dark" content="返回" placement="bottom-start">
        <!-- 返回按钮，只有在配置中 showBack 为 true 时才显示 -->
        <span class="tool_btn" v-if="cfg.showBack">
          <!-- 返回按钮图标，点击时触发 onBack 方法 -->
          <i class="btn_back" @click="onBack" />
        </span>
      </el-tooltip>
      <!-- 轨迹控制按钮的工具提示 -->
      <el-tooltip effect="dark" content="轨迹控制" placement="bottom-start">
        <!-- 轨迹控制按钮，只有在配置中 showPlay 为 true 时才显示 -->
        <span class="tool_btn" v-if="cfg.showPlay">
          <!-- 轨迹控制按钮图标，点击时触发 onPlay 方法 -->
          <i :class="[`btn_${mode}`]" @click="onPlay" />
        </span>
      </el-tooltip>
    </div>
  </div>
</template>
<script>
import { createMap, polyline } from '@/utils/map3.0' // 导入创建地图和绘制折线的工具函数
import CTMapOl from '@ct/ct_map_ol' // 导入 CTMapOl 库
import { shallowRef } from '@vue/reactivity' // 导入 Vue 的 shallowRef 函数
let marker // 定义 marker 变量
let carWindow // 定义 carWindow 变量
export default {
  props: {
    // 地图初始化完成时的回调函数
    onMapComplete: {
      type: Function,
      default: null,
    },
    // 返回按钮点击时的回调函数
    onMapBack: {
      type: Function,
      default: () => {
        console.log('onMapBack')
      },
    },
    // 轨迹线数据
    line: {
      default: [],
    },
    // 配置对象，控制按钮显示
    cfg: {
      default: () => {
        return {
          showPlay: true, // 是否显示播放按钮
          showBack: false, // 是否显示返回按钮
        }
      },
    },
  },
  data: function () {
    return {
      currentRow: null, // 当前选中的行
      mode: 'start', // 当前模式，初始为 "start"
      paths: [], // 路径数据
      needInit: true, // 是否需要初始化
      locusPlay: null, // 轨迹播放对象
      infoWindow: null, // 信息窗口对象
    }
  },
  mounted() {
    // 组件挂载后初始化地图并绘制轨迹线
    this.initMap(() => {
      this.drawPolyLine(this.line)
    })
  },
  setup() {
    const map = shallowRef(null) // 使用 shallowRef 创建 map 引用
    return {
      map,
    }
  },
  watch: {
    // 监听 line 属性的变化
    line: function (newVal, oldVal) {
      if (newVal !== oldVal) {
        this.needInit = true // 需要重新初始化
        this.mode = 'start' // 重置模式为 "start"
        this.drawPolyLine(newVal) // 绘制新的轨迹线
      }
    },
  },
  methods: {
    // 返回按钮点击处理函数
    onBack() {
      this.onMapBack() // 调用传入的 onMapBack 回调函数
    },
    // 初始化地图
    initMap(
      cb = () => {
        console.log('initMap')
      }
    ) {
      if (!this.map) {
        // 创建地图对象
        this.map = createMap('map', {
          center: [116.397428, 39.90923], // 地图中心坐标
          zoom: 9.7, // 初始缩放级别
          maxZoom: 18, // 最大缩放级别
          minZoom: 1, // 最小缩放级别
        })
        // 创建信息窗口
        this.infoWindow = new CTMapOl.Overlay({
          element: null,
          offset: [0, -40],
          positioning: 'bottom-center',
          stopEvent: false,
        })
        let pupUpDiv = document.createElement('div')
        pupUpDiv.id = 'veh-popup-content'
        this.infoWindow.setElement(pupUpDiv)
        this.map.addOverlay(this.infoWindow)
        // 调用地图初始化完成的回调函数
        this.onMapComplete(this.map)
        cb(this.map) // 执行传入的回调函数
      }
    },
    // 轨迹控制按钮点击处理函数
    onPlay() {
      console.info('this.mode-------', this.mode)
      console.info('this.needInit-------', this.needInit)
      console.info('this.marker-------', marker)
      if (!this.locusPlay) {
        return // 如果没有轨迹播放对象，直接返回
      }
      if (this.mode === 'stop') {
        this.locusPlay.pause() // 暂停播放
      }
      if (this.mode === 'start' && this.needInit) {
        this.locusPlay.playback() // 开始播放
        this.needInit = false // 设置为不需要初始化
      }
      if (this.mode === 'start' && !this.needInit) {
        this.locusPlay.moveTo(0) // 移动到起始位置
        this.locusPlay.playback() // 开始播放
      }
      this.mode = this.mode === 'stop' ? 'start' : 'stop' // 切换模式
    },
    // 清除地图上的轨迹和信息窗口
    clearMap() {
      if (this.locusPlay) {
        this.locusPlay.destroy() // 销毁轨迹播放对象
        this.locusPlay = null // 重置轨迹播放对象
      }
      this.mode = 'start' // 重置模式为 "start"
      this.needInit = true // 设置为需要初始化
      if (this.infoWindow) {
        this.infoWindow.setPosition(null) // 清除信息窗口位置
      }
    },
    // 准备轨迹线数据
    prepareLine(line) {
      let points = line.map(item => {
        return { longitude: item.lng * 1, latitude: item.lat * 1 } // 转换坐标数据
      })
      return points // 返回转换后的坐标数据
    },
    // 获取移动中的轨迹信息
    getMoving(line) {
      let lnglats = this.locusPlay._movingFeature.getGeometry().getCoordinates() // 获取当前移动的坐标
      let heading = this.locusPlay._movingFeature.get('heading') // 获取当前方向
      let nearestIndex = this.locusPlay._movingFeature.get('nearestIndex') // 获取最近的索引
      if (heading && !isNaN(heading)) {
        this.locusPlay._movingFeature
          .getStyle()
          .getImage()
          .setRotation((heading * Math.PI) / 180) // 设置旋转角度
      }
      console.log('nearestIndex:' + nearestIndex)
      let curLineItem = line[0]
      if (line.length > nearestIndex) {
        curLineItem = line[nearestIndex] // 获取当前轨迹点信息
      }
      let content = document.getElementById('veh-popup-content')
      content.innerHTML = `<div class="car_point_content">
                         <div class="car_point_item"><span>车牌号：</span><span>${curLineItem.plateNumber}</span></div>
                         <div class="car_point_item"><span>坐标：</span><span>${curLineItem.lng},${curLineItem.lat}</span></div>
                         <div class="car_point_item"><span>坐标时间：</span><span>${curLineItem.gpsTime}</span></div>
                   </div>` // 更新信息窗口内容
      this.infoWindow.setPosition(lnglats) // 设置信息窗口位置
    },
    // 绘制轨迹线
    drawPolyLine(line) {
      if (!line) {
        return // 如果没有轨迹线数据，直接返回
      }
      this.clearMap() // 清除地图上的轨迹和信息窗口
      const locusArray = this.prepareLine(line) // 准备轨迹线数据
      const options = {
        duration: 6, // 自动回放时长，单位秒，默认 5 秒
        color: '#28F', // 轨迹线颜色，默认 #ffea00
        width: 6, // 轨迹线宽度，默认 6px
        enableHeading: true,
        movingIconStyle: new CTMapOl.style.Style({
          image: new CTMapOl.style.Icon({
            anchorOrigin: 'bottom-left',
            src: require(`@/assets/images/map2.0/car.png`), // 移动图标的图片路径
            scale: 1.1,
            rotateWithView: false,
            rotation: 0,
            offsetOrigin: 'bottom-left',
          }),
        }),
        onPlay: info => {
          this.getMoving(line) // 获取移动中的轨迹信息
          if (info.finish) {
            this.mode = 'start' // 如果播放完成，重置模式为 "start"
          }
        },
      }
      this.locusPlay = new CTMapOl.extend.LocusPlayback(
        this.map,
        locusArray,
        options
      ) // 创建轨迹播放对象
      const path = line.map(item => [item.lng * 1, item.lat * 1]) // 转换轨迹线坐标数据
      const curPolyline = polyline(
        path,
        {
          strokeColor: '#09E6C8', // 线颜色
          strokeOpacity: 1,
          strokeWeight: 6, // 线宽
          zIndex: 1000,
        },
        { type: 'curPolyline' }
      )
      this.map.getView().fit(curPolyline.getGeometry(), {
        duration: 1000, // 视图适应轨迹线
      })
    },
  },
}
</script>
<style lang="less" src="./index.less" scoped></style>