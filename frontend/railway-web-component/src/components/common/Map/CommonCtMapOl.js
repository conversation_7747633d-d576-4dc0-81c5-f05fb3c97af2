import { polyline } from '@/utils/map3.0'; // 导入绘制折线的工具函数
import CTMapOl from '@ct/ct_map_ol'; // 导入地图相关的库
/**
 * 关闭地图弹窗
 */
export function closeInfoWindow(
  map, // 地图实例
  infoWindowEntity, // 信息窗口实体
  WindowsId, // 信息窗口的ID
  closeDeviceblock = () => {
    console.log('closeInfoWindow closeDeviceblock'); // 默认关闭设备块的回调函数
  }
  // ,supview
) {
  // 检查地图上是否存在指定ID的覆盖物或信息窗口实体
  if (map.getOverlayById(WindowsId) || infoWindowEntity) {
    map.removeOverlay(infoWindowEntity); // 移除信息窗口实体
    closeDeviceblock(); // 调用关闭设备块的回调函数
  }
}

/**
 * 移除图层
 * @param map 地图实体
 * @param layerPoint 图层
 */
export function removeLayer(map, layerPoint) {
  if (layerPoint) {
    map.removeLayer(layerPoint); // 移除图层
  }
}
export function openInfoWindow({
  map, // 地图实例
  lng, // 经度
  lat, // 纬度
  content, // 弹窗内容
  popClass, // 弹窗的CSS类名
  offsetX, // 弹窗的X轴偏移
  offsetY, // 弹窗的Y轴偏移
  infoWindowId = 'deviceWindowsIdviceWindowsId', // 信息窗口的ID
  infoBlock = () => {
    console.log('openInfoWindow infoBlock'); // 默认信息块的回调函数
  },
  autoPan = true // 是否自动平移地图以显示弹窗
}) {
  let infoWindow; // 信息窗口实例
  let $info; // 信息窗口的DOM元素
  $info = document.createElement('div'); // 创建一个div元素作为信息窗口
  $info.id = infoWindowId; // 设置信息窗口的ID
  $info.className = popClass; // 设置信息窗口的CSS类名
  $info.onmousewheel = function (e) {
    e.stopPropagation(); // 阻止滚轮事件冒泡
  };
  document.body.appendChild($info); // 将信息窗口添加到文档中
  // 单坐标转换指定投影系
  let coordinate = [lng, lat]; // 坐标数组
  /**
   * Create an overlay to anchor the popup to the map.
   */
  const opt = {
    id: infoWindowId, // 信息窗口的ID
    element: $info, // 信息窗口的DOM元素
    position: coordinate, // 信息窗口的位置
    positioning: 'bottom-center', // 信息窗口的定位方式
    stopEvent: true, // 是否阻止事件冒泡
    offset: [offsetX, offsetY], // 信息窗口的偏移量
    zIndex: 100, // 信息窗口的层级
  };
  if (autoPan) {
    Object.assign(opt, {
      autoPan: {
        animation: {
          duration: 250, // 自动平移的动画持续时间
        },
      },
    });
  }
  infoWindow = new CTMapOl.Overlay(opt); // 创建信息窗口的覆盖物
  map.addOverlay(infoWindow); // 将信息窗口添加到地图上
  const [, el] = content; // 获取弹窗内容的元素
  document.getElementById(infoWindowId)?.appendChild(el); // 将内容元素添加到信息窗口中
  setTimeout(function () {
    infoBlock(); // 延迟调用信息块的回调函数
  }, 100);
  return infoWindow; // 返回信息窗口实例
}

/**
 * APIMethod:OpenLayers绘制扇形的接口扩展
 * @param origin 圆心
 * @param radius 半径(米)
 * @param sides 边数 （360 划分最小单元）
 * @param start 弧度 正北算0度 开始的位置
 * @param angel 旋转角度（扇形右边半径与x正向轴的角度）
 * @returns {OpenLayers.Geometry.Polygon}
 */
const createRegularPolygonCurve = (origin, radius, sides, start, angel) => {
  let points = []; // 存储多边形的顶点
  points.push(origin); // 添加圆心作为第一个顶点
  const r = (360 / sides).toFixed(2); // 每个边的角度
  const curveSides = (angel / r).toFixed(0) * 1; // 计算曲线的边数
  for (let i = 1; i <= curveSides; i++) {
    points.push(azimuthOffset(origin[0], origin[1], start + i * r, radius)); // 计算每个顶点的坐标
    if (i === curveSides - 1) {
      points.push(azimuthOffset(origin[0], origin[1], start + angel, radius)); // 添加最后一个顶点
    }
  }
  let ring = new CTMapOl.geom.LinearRing(points); // 创建线性环
  let list = ring.getCoordinates(); // 获取线性环的坐标
  return new CTMapOl.geom.Polygon([list]); // 返回多边形
};
const azimuthOffset = (origin_lon, origin_lat, azimuth, distance) => {
  let lonlat = [0.0, 0.0]; // 存储计算后的经纬度
  if (azimuth != null && distance > 0) {
    lonlat[0] =
      origin_lon +
      (distance * Math.sin((azimuth * Math.PI) / 180) * 180) /
      (Math.PI * 6371229 * Math.cos((origin_lat * Math.PI) / 180)); // 计算偏移后的经度
    lonlat[1] =
      origin_lat +
      (distance * Math.cos((azimuth * Math.PI) / 180)) /
      ((Math.PI * 6371229) / 180); // 计算偏移后的纬度
  } else {
    lonlat[0] = origin_lon; // 如果没有偏移，则使用原始经度
    lonlat[1] = origin_lat; // 如果没有偏移，则使用原始纬度
  }
  return lonlat; // 返回计算后的经纬度
};

/**
 * 绘制可视域
 * @param map 地图实例
 * @param point 坐标点
 * @param layerKey 图层键值
 * @param visualRange 可视范围
 * @param horizViewRange 水平视角范围
 * @param horizRange 水平范围
 * @param style 样式
 * @returns {CTMapOl.extend.LayeredClusterViewShed}
 */
export const initViewArea = ({
  map, // 地图实例
  point, // 坐标点
  layerKey, // 图层键值
  visualRange, // 可视范围
  horizViewRange, // 水平视角范围
  horizRange, // 水平范围
  style, // 样式
}) => {
  const lcvs = new CTMapOl.extend.LayeredClusterViewShed(
    map,
    [
      {
        data: [], // 数据
        zIndex: 0, // 层级
        minZoom: 1, // 最小缩放级别
        onClick: (data) => {
          console.log(data); // 点击事件处理
        },
      },
    ],
    {
      minViewShed: 1, // 最小可视域
      cameraStyleFunc: () => null, // 摄像机样式函数
    }
  );
  lcvs.setViewShedVisible(true); // 设置可视域可见
  lcvs.disableCluster(true); // 禁用聚类
  lcvs.addViewShedVisibleById(
    'id', // ID
    layerKey, // 图层键值
    {
      guid: layerKey, // GUID
      id: layerKey, // ID
      longitude: point.lng, // 经度
      latitude: point.lat, // 纬度
      distance: visualRange, // 距离
      heading: horizViewRange, // 视角
      angle: horizRange, // 角度
      ishalf: false, // 是否半视角
      ...style, // 样式
    },
    0 // 层级
  );
  return lcvs; // 返回可视域实例
};

/**
 * 绘制地图线
 * @param path 路径
 * @param layer 图层
 * @param options 选项
 * @param attribute 属性
 * @returns {*}
 */
// export const drawLine = ({
//   path, // 路径
//   layer = null, // 图层
//   options = {}, // 选项
//   attribute = {}, // 属性
// }) => {
//   if (!layer) {
//     layer = new CTMapOl.layer.Vector({
//       source: new CTMapOl.source.Vector(), // 创建新的矢量图层
//     });
//     window.map.addLayer(layer); // 将图层添加到地图
//   }
//   layer.getSource().clear(true); // 清除图层上的所有要素
//   const opt = Object.assign(
//     {
//       strokeColor: '#eae1de', // 线条颜色
//       strokeWeight: 5, // 线条宽度
//       strokeDasharray: [20, 40], // 虚线样式
//     },
//     options // 合并选项
//   );
//   const attr = Object.assign({ type: 'curPolyline' }, attribute); // 合并属性
//   const line = polyline(path, opt, attr); // 创建折线
//   layer.getSource().addFeatures([line]); // 将折线添加到图层
//   window.map.getView().fit(line.getGeometry(), {
//     duration: 1000, // 视图适应折线的动画持续时间
//   });
//   return layer; // 返回图层
// };
export const drawMultiLine = ({
  paths, // 多条路径
  layer = null, // 图层
  options = {}, // 选项
  attribute = {}, // 属性
  mapIns = null, // 地图实例
}) => {
  const map = mapIns; // || window.map; // 使用传入的地图实例或全局地图实例
  if (!layer) {
    layer = new CTMapOl.layer.Vector({
      source: new CTMapOl.source.Vector(), // 创建新的矢量图层
      zIndex: 8, // 设置图层的层级
    });
    map.addLayer(layer); // 将图层添加到地图
  }
  layer.getSource().clear(true); // 清除图层上的所有要素
  const opt = Object.assign(
    {
      strokeColor: '#eae1de', // 线条颜色
      strokeWeight: 3, // 线条宽度
      strokeDasharray: [30, 40], // 虚线样式
    },
    options // 合并选项
  );
  const attr = Object.assign({ type: 'curPolyline' }, attribute); // 合并属性
  const line = paths.map((path) => polyline(path, opt, attr)); // 创建多条折线
  layer.getSource().addFeatures(line); // 将折线添加到图层
  return layer; // 返回图层
};
// export const fitBounds = (bounds, mapIns) => {
//   const map = mapIns || window.map; // 使用传入的地图实例或全局地图实例
//   const line = polyline(bounds, {}, {}); // 创建折线
//   map.getView().fit(line.getGeometry(), {
//     duration: 1000, // 视图适应折线的动画持续时间
//     maxZoom: 12, // 最大缩放级别
//     padding: [250, 50, 150, 50], // 视图适应时的内边距
//   });
// };
