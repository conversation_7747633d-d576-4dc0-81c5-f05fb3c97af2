<!--
 * @Description  :
 * <AUTHOR> wnj
 * @Date         : 2023-09-01 15:48:31
 * @LastEditors  : wnj
 * @LastEditTime : 2023-10-25 10:25:54
 * @FilePath     :  / src / components / common / Map / SelectMap / index.vue
-->
<template>
  <!-- 定义一个对话框组件，用于坐标定位 -->
  <el-dialog
    title="坐标定位"
    :visible="true"
    :before-close="closeWindow"
    :close-on-click-modal="false"
    width="60vw"
    height="50vh"
    top="8vh"
    append-to-body
  >
    <!-- 搜索信息输入框 -->
    <div class="search_info">
      <!-- 使用el-select组件实现地址搜索功能 -->
      <el-select
        v-model="address"
        filterable
        remote
        reserve-keyword
        placeholder="请输入关键词"
        :remote-method="remoteMethod"
        :loading="loading"
        :clearable="true"
        size="mini"
        @change="currentSelect"
        style="width: 500px"
      >
        <!-- 遍历搜索结果选项 -->
        <el-option
          v-for="item in options"
          :key="item.id"
          :label="item.formattedAddress"
          :value="item"
          class="one-text"
        >
          <!-- 显示省份 -->
          <span style="float: left">{{ item.province }}</span>
          <!-- 显示格式化地址 -->
          <span style="float: right; color: #8492a6; font-size: 13px">{{ item.formattedAddress }}</span>
        </el-option>
      </el-select>
    </div>
    <!-- 地图显示区域 -->
    <div id="selectMap" class="select_map"></div>
    <!-- 对话框底部按钮 -->
    <div slot="footer" class="dialog-footer">
      <!-- 确定按钮，点击时触发confirmClick方法 -->
      <el-button type="primary" @click="confirmClick">确 定</el-button>
      <!-- 取消按钮，点击时触发closeWindow方法 -->
      <el-button @click="closeWindow">取 消</el-button>
    </div>
  </el-dialog>
</template>
<script>
import markerIcon from '@/assets/images/map2.0/location.svg'
import { createPlatMap, imageMarker, polygon } from '@/utils/map3.0'
import CTMapOl from '@ct/ct_map_ol'
import { fromExtent } from 'ol/geom/Polygon'
export default {
  props: ['cityName'], // 接收父组件传入的城市名称
  components: {
    // InfoItem: InfoItem,
  },
  data() {
    return {
      dialogFormVisible: true, // 对话框是否可见
      map: null, // 地图对象
      polygens: [], // 多边形集合
      polygon: null, // 当前多边形
      marker: null, // 标记点
      lat: null, // 纬度
      lng: null, // 经度
      geoCoder: null, // 经纬度转地址的转换器
      AutoComplete: null, // 搜索组件
      address: '', // 具体详细地址
      loading: false, // 搜索加载状态
      options: [], // 搜索下拉框选项
      markersLayer: null, // 标记点图层
      maskLayer: null, // 遮罩图层
    }
  },
  methods: {
    confirmClick() {
      // 关闭地图窗口，把选择的经纬度和详细地址传给父组件
      if (this.address) {
        // 需要判断是否入参城市内的地址，如果不是需要提示
        // if (this.address.indexOf(this.cityName) == -1) {
        //   this.$message({
        //     type: 'warn',
        //     message: '请选择或输入范围内的详细地址！'
        //   });
        //   return;
        // }
        this.$emit('setAddress', {
          lat: this.lat,
          lng: this.lng,
          address: this.address,
        })
      } else {
        this.$message({
          type: 'warn',
          message: '未选择地址',
        })
      }
    },
    closeWindow() {
      // 调用父组件关闭地图窗口即可
      this.$emit('closeMapSelectWindow')
    },
    initMap() {
      // 初始化地图
      if (!this.map) {
        this.map = createPlatMap('selectMap', {
          center: [115.6957, 40.78], // 设置地图中心点
          zoom: 9.7, // 设置地图缩放级别
          maxZoom: 18, // 最大缩放级别
          minZoom: 1, // 最小缩放级别
        })
        // 创建遮罩图层
        this.maskLayer = new CTMapOl.layer.Vector({
          source: new CTMapOl.source.Vector(),
        })
        this.map.addLayer(this.maskLayer)
        // 创建标记点图层
        this.markersLayer = new CTMapOl.layer.Vector({
          source: new CTMapOl.source.Vector(),
        })
        this.map.addLayer(this.markersLayer)
        this.changeCityPolygon(this.cityName)
      }
    },
    erase(geom) {
      // 擦除地图上的某个几何图形
      let extent = [-180, -90, 180, 90] // 地图范围
      let polygonRing1 = fromExtent(extent) // 创建一个多边形环
      let coords = geom.getCoordinates() // 获取几何图形的坐标
      let sitePoints = []
      for (let i = 0; i < coords[0].length; i++) {
        sitePoints.push([coords[0][i][0], coords[0][i][1]])
      }
      sitePoints.push(sitePoints[0]) // 闭合多边形
      let linearRing1 = new CTMapOl.geom.LinearRing(sitePoints) // 创建线性环
      polygonRing1.appendLinearRing(linearRing1) // 添加线性环到多边形环
      return polygonRing1
    },
    addconver(geojson) {
      // 添加转换后的几何图形到地图
      let fts = new CTMapOl.format.GeoJSON().readFeatures(geojson) // 读取GeoJSON格式的特征
      let ft = fts[0]
      let converGeom = this.erase(ft.getGeometry()) // 擦除几何图形
      let convertFt = new CTMapOl.Feature({
        geometry: converGeom,
      })
      let polygonStyle = new CTMapOl.style.Style({
        fill: new CTMapOl.style.Fill({ color: '#ffffffa3' }), // 填充样式
        stroke: new CTMapOl.style.Stroke({
          color: '#1FDDFF', // 边框颜色
          width: 3, // 边框宽度
        }),
      })
      convertFt.setStyle(polygonStyle) // 设置样式
      this.maskLayer.getSource().clear(true) // 清除遮罩图层
      this.maskLayer.getSource().addFeatures([convertFt]) // 添加特征到遮罩图层
    },
    changeCityPolygon(areaName) {
      // 根据城市名称改变地图上的多边形
      this.map.on('singleclick', e => {
        // 地图点击事件，获取点击点的坐标
        let pixel = this.map.getEventPixel(e.originalEvent)
        let currentFeature = this.map.forEachFeatureAtPixel(
          pixel,
          function (feature, layer) {
            return feature
          }
        )
        if (!currentFeature) {
          let lonlat = this.map.getCoordinateFromPixel(e.pixel)
          this.lng = lonlat[0]
          this.lat = lonlat[1]
          // 清除点
          this.removeMarker()
          // 标记点
          this.setMapMarker()
          // 经纬度转具体地址
          this.toGeoCoder()
        }
      })
      if (!areaName) {
        return
      }
      let region = new CTMapOl.netApi.regionInfo({ keyWord: areaName })
      region.then(({ code, data }) => {
        if (code == 200 && data) {
          const polygons = (data.polygon && data.polygon.split('|')) || []
          let curMaxCoordinates = []
          let curPolygen = null
          polygons.forEach(item => {
            let coordinates = item
              .split(';')
              .map(i => [i.split(',')[0] * 1, i.split(',')[1] * 1])
            if (coordinates.length > curMaxCoordinates.length) {
              curMaxCoordinates = coordinates
              curPolygen = polygon(coordinates, {
                fillColor: '#ffffffb9',
                strokeOpacity: 1,
                fillOpacity: 0.5,
                strokeColor: '#1FDDFF',
                strokeWeight: 2,
                strokeStyle: 'dashed',
                strokeDasharray: [5, 5],
              })
            }
          })
          const geojson = {
            type: 'FeatureCollection',
            features: [
              {
                type: 'Feature',
                properties: {},
                geometry: {
                  coordinates: [curMaxCoordinates],
                  type: 'Polygon',
                },
              },
            ],
          }
          this.addconver(geojson)
          this.map.getView().fit(curPolygen.getGeometry(), {
            duration: 1000,
          })
        }
      })
    },
    // 标记点
    setMapMarker(toCenter = false) {
      // 自动适应显示想显示的范围区域
      this.markersLayer.getSource().clear(true)
      const marker = imageMarker(
        [this.lng * 1, this.lat * 1],
        {
          icon: markerIcon,
          text: '定位',
          anchor: [20, 40],
        },
        { id: `searchmap-dw-${new Date().getTime()}` }
      )
      this.markersLayer.getSource().addFeatures([marker])
      if (toCenter) {
        this.map.getView().setCenter([this.lng * 1, this.lat * 1])
      }
      const level =
        this.map.getView().getZoom() > 9.7 ? this.map.getView().getZoom() : 9.7
      this.map.getView().setZoom(level)
    },
    // 清除点
    removeMarker() {
      if (this.marker) {
        this.map.remove(this.marker)
      }
    },
    toGeoCoder() {
      // 经纬度转具体地址
      let lnglat = [this.lng, this.lat]
      CTMapOl.netApi
        .codeToAddress({ location: lnglat.join(',') })
        .then(({ code, data }) => {
          if (code === 200 && data) {
            if (data.length > 0) {
              const item = data[0]
              this.address = item.address
            }
          }
        })
    },
    // 搜索
    remoteMethod(query) {
      // 远程搜索方法
      console.log(query)
      if (query !== '') {
        this.loading = true
        setTimeout(() => {
          this.loading = false
          let qData = {
            key: 'd6dec8c590af05d092033afc2cd51a57',
            address: query,
          }
          CTMapOl.netApi.addressToCode(qData).then(({ code, data }) => {
            if (code === 200 && data) {
              data.forEach((item, index) => {
                item.id = `${index}-${new Date().getTime()}`
              })
              this.options = data
            }
          })
        }, 200)
      } else {
        this.options = []
      }
    },
    // 选中提示
    currentSelect(val) {
      // 选中地址后更新经纬度和地址信息
      if (!val) {
        return
      }
      this.lng = val.location.split(',')[0]
      this.lat = val.location.split(',')[1]
      this.address = val.formattedAddress
      // 清除点
      this.removeMarker()
      // 标记点
      this.setMapMarker(true)
    },
  },
  created() {
    // 初始化
    // 根据入参cityCode查询城市边界，渲染地图边界
  },
  mounted() {
    // 在组件挂载后初始化地图
    setTimeout(() => this.initMap(), 500)
  },
  watch: {
    // detailItem(newData, oldData) {},
  },
}
</script>
<style lang='less' scoped>
.search_info {
  width: 500px;
  height: 40px;
  position: absolute;
  z-index: 12;
  margin-top: 20px;
}
.select_map {
  width: 100%;
  height: 500px;
  margin: 0;
}
.dialog-footer {
  width: 100%;
  height: 40px;
  text-align: end;
  padding-top: 15px;
  border-top: solid 1px #eeeeee;
}
</style>