<template></template>
<script>
import { mapMutations } from 'vuex'
import { CfgEnum } from '@/components/page/ScreenPages/IntegratedMonitor/enum/CfgEnum'
import { getUserLocalStorage } from '@/components/common/utils'

export default {
  inject: ['mapRef'],
  props: {
    mapId: {
      type: String,
    },
    tileModes: {
      type: Array,
      default: [],
    },
    pageType: {
      type: String,
    },
  },
  components: {},
  data() {
    return {}
  },
  // created() {},
  mounted() {
    // 监听地图类型切换事件
    document
      ?.querySelector('.ctmap-union-layer-switcher__layerlist')
      ?.addEventListener('click', this.changeTileType)
  },
  beforeDestroy() {
    document
      ?.querySelector('.ctmap-union-layer-switcher__layerlist')
      ?.removeEventListener('click', this.changeTileType)
  },
  methods: {
    ...mapMutations('map', [
      'setMapModeId', // 设置地图类型 id
    ]),
    changeTileType(e) {
      const typeName = e.target.innerText
      const modes = this.tileModes.find(v => typeName.includes(v.name))
      this.setMapModeId(modes.modeId)
      let isShow = true
      // 从用户本地存储中获取侧边栏铁路线路是否开启状态
      if (['eventFile', 'FullViewMonitor'].includes(this.pageType)) {
        // 事件档案大屏默认
        isShow = true
      } else {
        const usrCachedCfg = getUserLocalStorage(
          CfgEnum.STORAGE_KEY.SIDEBAR_KEY
        )
        isShow = usrCachedCfg?.RAILWAY_LINE ?? false
      }

      // console.log('切换地图模式后切换铁路线路', modes)
      this.mapRef
        .getMapRef(this.mapId)
        .mapInstance.railwayRouteLayer(isShow, modes.modeId)
    },
  },
}
</script>