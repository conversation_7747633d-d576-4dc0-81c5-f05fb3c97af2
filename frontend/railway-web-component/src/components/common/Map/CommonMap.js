import store from '@/store'; // 导入 Vuex 的 store
import CTMapOl from '@ct/ct_map_ol'; // 导入 CTMapOl 模块
import Vue from 'vue'; // 导入 Vue 框架
import {
  closeInfoWindow,
  initViewArea,
  openInfoWindow,
  removeLayer,
} from './CommonCtMapOl'; // 导入地图操作的公共方法
import { transformPointToMercator } from '@/utils'

// 用于存储所有信息窗口对象的字典
let infoWindowList = {};
// 用于存储所有视域对象的字典
let viewshedList = {};

/**
 * CommonMap 类提供了地图操作的静态方法，包括信息窗口的管理、视域的验证和设置、图层的移除等。
 */
export default class CommonMap {
  /**
   * 根据 WindowsId 获取信息窗口对象。
   * @param {string} WindowsId 信息窗口的唯一标识。
   * @returns {Object} 信息窗口对象。
   */
  static getInfoWindow(WindowsId) {
    return infoWindowList[WindowsId];
  }
  /**
   * 设置信息窗口对象。
   * @param {Object} infoWindow 信息窗口对象。
   * @param {string} WindowsId 信息窗口的唯一标识。
   */
  static setInfoWindow(infoWindow, WindowsId) {
    infoWindowList[WindowsId] = infoWindow;
  }
  /**
   * 验证给定设备代码的视域是否存在。
   * @param {string} devcCode 设备代码。
   * @returns {boolean} 如果视域存在返回 true，否则返回 false。
   */
  static verifyViewshed(devcCode) {
    if (viewshedList[devcCode]) {
      return true;
    }
    return false;
  }
  /**
   * 根据设备代码获取视域对象。
   * @param {string} devcCode 设备代码。
   * @returns {Object} 视域对象。
   */
  static getViewshed(devcCode) {
    return viewshedList[devcCode];
  }
  /**
   * 设置视域列表。
   * @param {string} devcCode 设备代码。
   * @param {Object} viewsData 视域数据，包括地图、点位、视域范围等信息。
   */
  static setViewshedList(devcCode, { map, point, views = [], style }) {
    const lcvs = views.map((view, index) => {
      const { horizViewRange, horizRange, visualRange } = view;
      return initViewArea({
        map,
        point,
        visualRange,
        horizViewRange,
        horizRange,
        style,
        layerKey: `${devcCode}-${index}`,
      });
    });
    viewshedList[devcCode] = lcvs;
  }
  /**
   * 移除指定设备代码的视域。
   * @param {string} devcCode 设备代码。
   */
  static removeViewshed(devcCode) {
    if (!viewshedList || !viewshedList[devcCode]) {
      return;
    }
    if (viewshedList[devcCode] || viewshedList[devcCode].length > 0) {
      viewshedList[devcCode].map((layer, index) => {
        const id = `${devcCode}-${index}`;
        layer.removeViewShedVisibleById('id', id, { guid: id });
      });
    }
    delete viewshedList[devcCode];
  }
  /**
   * 移除地图上的图层。
   * @param {Object} localMapInstance 地图实例。
   * @param {Object} layerPoint 图层。
   * @returns {boolean} 移除操作的结果。
   */
  static removeLayer(localMapInstance, layerPoint) {
    return removeLayer(localMapInstance, layerPoint);
  }
  /**
   * 根据前缀关闭所有匹配的信息窗口。
   * @param {string} prefix 信息窗口标识的前缀。
   * @param {Object} localMapInstance 地图实例。
   * @param {boolean} is3Dprivate 是否为3D私有信息窗口。
   */
  static closeInfoWindowByPrefix(
    prefix,
    localMapInstance,
    is3Dprivate = false
  ) {
    let keys = Object.keys(infoWindowList);
    keys.forEach((key) => {
      if (key.indexOf(prefix) > -1) {
        this.closeInfoWindow(key, localMapInstance, false);
      }
    });
  }
  /**
   * 关闭指定信息窗口。
   * @param {string} WindowsId 信息窗口的唯一标识。
   * @param {Object} mapinstance 地图实例。
   * @param {boolean} is3Dprivate 是否为3D私有信息窗口。
   * @param {Function} closeDeviceblock 关闭设备块的回调函数。
   */
  static closeInfoWindow(
    WindowsId,
    mapinstance,
    is3Dprivate,
    closeDeviceblock = () => {
      console.log('closeInfoWindow closeDeviceblock');
    }
    // ,supview
  ) {
    let infoWindow = this.getInfoWindow(WindowsId);
    console.log('关闭弹窗infoWindow', is3Dprivate);
    if (infoWindow) {
      if (is3Dprivate) {
        console.log('关闭弹窗是3d', is3Dprivate, infoWindow);
        infoWindow.destroy();
      } else {
        console.log('不是3d');
        closeInfoWindow(
          mapinstance,
          infoWindow,
          WindowsId,
          closeDeviceblock
          // ,supview
        ); //关闭地图弹窗
      }
    }
  }
  /**
   * 关闭所有地图提示信息窗口。
   * @param {Object} map 地图实例。
   */
  static closeTip(map) {
    const mapIns = map; // || window.map;
    CommonMap.closeInfoWindowByPrefix('map-tip', mapIns, false);
  }
  /**
   * 在地图上显示提示信息窗口。
   * @param {number} lng 经度。
   * @param {number} lat 纬度。
   * @param {string} content 提示内容。
   * @param {Object} mapinstance 地图实例。
   * @param {string} popClass 弹窗的类名。
   * @param {number} offsetX 偏移量X。
   * @param {number} offsetY 偏移量Y。
   * @param {string} infoWindowid 信息窗口的唯一标识。
   * @param {boolean} is3Dprivate 是否为3D私有信息窗口。
   * @param {string} mapId 地图的唯一标识。
   * @param {Function} infoBlock 信息块的回调函数。
   * @param {boolean} autoPan 是否自动调整地图以显示信息窗口。
   * @returns {Object} 信息窗口对象。
   */
  static mapTip({
    lng,
    lat,
    content,
    mapinstance,
    popClass = 'popup',
    offsetX = 40,
    offsetY = -30,
    infoWindowid = 'map-tip',
    is3Dprivate = false,
    mapId = 'mainMap',
    infoBlock = () => {
      console.log('mapTip infoBlock');
    },
    autoPan = false,
  }) {
    return CommonMap.infoWindow(
      lng,
      lat,
      content,
      popClass,
      offsetX,
      offsetY,
      infoWindowid,
      mapinstance,
      is3Dprivate,
      mapId,
      infoBlock,
      autoPan
    );
  }
  /**
   * 创建并显示信息窗口。
   * @param {number} lng 经度。
   * @param {number} lat 纬度。
   * @param {string} content 信息窗口的内容。
   * @param {string} popClass 弹窗的类名。
   * @param {number} offsetX 偏移量X。
   * @param {number} offsetY 偏移量Y。
   * @param {string} infoWindowid 信息窗口的唯一标识。
   * @param {Object} mapinstance 地图实例。
   * @param {boolean} is3Dprivate 是否为3D私有信息窗口。
   * @param {string} mapId 地图的唯一标识。
   * @param {Function} infoBlock 信息块的回调函数。
   * @param {boolean} autoPan 是否自动调整地图以显示信息窗口。
   * @returns {Object} 信息窗口对象。
   */
  static infoWindow(
    lng,
    lat,
    content,
    popClass,
    offsetX,
    offsetY,
    infoWindowid,
    mapinstance,
    is3Dprivate,
    mapId,
    infoBlock = () => {
      console.log('infoWindow infoBlock');
    },
    autoPan
  ) {
    let infoWindow;
    if (is3Dprivate) {
      this.closeInfoWindow(infoWindowid, mapinstance, is3Dprivate); //关闭地图弹窗
      infoWindow = new CTMapOl.cesiumComponent.InforWindow(
        mapinstance,
        {
          position: [parseFloat(lng), parseFloat(lat)],
          anchor: ['50%', '100%'],
          offset: [offsetX, offsetY],
          rotate: 0,
          content: content.innerHTML || content,
          selfStyle: true,
          popClass: 'alarmEventInfoWindow ' + popClass,
          isMultiple: false, //多个显示框
        },
        mapId
      );
    } else {
      this.closeInfoWindow(infoWindowid, mapinstance, is3Dprivate); //关闭地图弹窗
      infoWindow = openInfoWindow({
        map: mapinstance,
        lng,
        lat,
        content,
        popClass,
        offsetX,
        offsetY,
        infoWindowId: infoWindowid,
        infoBlock,
        autoPan
      });
    }
    this.setInfoWindow(infoWindow, infoWindowid);
    return infoWindow;
  }
  /**
   * 将 Vue 组件转换为 HTML。
   * @param {Object} options 配置选项。
   * @returns {Array} 包含组件实例、HTML 元素和内容引用的数组。
   */
  static componentToHtml({
    className = '',
    component,
    props,
    onClose = () => {
      console.log('componentToHtml onClose');
    },
    onSelected = () => {
      console.log('componentToHtml onSelected');
    },
  }) {
    let comp;
    let Content = Vue.extend({
      //自定义模板继承
      template: `
        <base-info ref='contentRef'
                   class='window-content ${className}'
                   :propData='nameExtend'
                   :onSelected='onSelected'
                   :close='close'></base-info>`,
      name: 'child',
      components: {
        'base-info': component, //弹框用子组件包裹
      },
      data() {
        return {
          nameExtend: props,
        };
      },
      methods: {
        close() {
          if (comp) {
            comp.$destroy();
            onClose();
          }
        },
        onSelected(row) {
          onSelected && onSelected(row);
        },
      },
    });
    comp = new Content({ store }).$mount();
    return [comp, comp.$el, comp.$refs.contentRef];
  }
  /**
   * 坐标转换
   * @param wkt
   * @returns {Array}
   */
  static wktToPoint = (wkt, change = false) => {
    if (wkt.indexOf('MULTILINESTRING') > -1) {
      let pointString = wkt.split(/MULTILINESTRING\s+/)[1];
      let lineStrings = pointString
        .replaceAll('((', '')
        .replaceAll('))', '')
        .split(/\),\s+\(/);
      return lineStrings.map((line) => {
        let linePointsStr = line.split(/,\s+/);
        return linePointsStr.map((pointStr) => {
          let point = pointStr.split(/\s+/);
          const pointList = [Number(point[0]), Number(point[1])]
          // 转座标系墨卡托
          return change ? transformPointToMercator(pointList) : pointList;
        });
      });
    }
    if (wkt.indexOf('LINESTRING') > -1) {
      let pointString = wkt.split(/LINESTRING\s+/)[1];
      let linePointsStr = pointString
        .replaceAll('(', '')
        .replaceAll(')', '')
        .split(/,\s+/);
      return [
        linePointsStr.map((pointStr) => {
          let point = pointStr.split(/\s+/);
          const pointList = [Number(point[0]), Number(point[1])]
          // 转座标系墨卡托
          return change ? transformPointToMercator(pointList) : pointList;
        }),
      ];
    }
    if (wkt.indexOf('MULTIPOLYGON') > -1) {
      let pointString = wkt.split(/MULTIPOLYGON\s+/)[1];
      let linePointsStr = pointString
        .replaceAll('(', '')
        .replaceAll(')', '')
        .split(/,\s+/);
      return linePointsStr.map((pointStr) => {
        let point = pointStr.split(/\s+/);
        const pointList = [Number(point[0]), Number(point[1])]
        // 转座标系墨卡托
        return change ? transformPointToMercator(pointList) : pointList;
      });
    }
    return wkt;
  };
}
