/**
 * 该模块定义了一系列常量，用于在应用程序中表示各种配置和约定。
 * 包括HTTP状态码、日期格式、事件总线事件名称、巡查类型、巡查周期、区域级别等。
 * 这样可以在代码的多个地方引用这些约定，而不需要硬编码具体的值，提高了代码的可维护性和可读性。
 */
export default {
    /**
     * HTTP状态码常量。
     * 定义了一些常见的HTTP状态码，例如成功的状态码200和请求错误的状态码400。
     * 这样可以在处理HTTP响应时直接使用这些常量，而不是硬编码状态码数字。
     */
    HTTP_CODE: {
        ok: 200,
        error_400: 400,
    },
    /**
     * 日期格式常量。
     * 定义了一些常用的日期格式字符串，例如'YYYY-MM-DD'和'YYYYMMDDHHmmss'。
     * 这样在处理日期时，可以根据需要选择合适的格式，而不需要每次都手动构造格式字符串。
     */
    DATE_FORMAT: {
        yyyy_mm_dd: 'YYYY-MM-DD',
        yyyymmdd: 'YYYYMMDD',
        yyyy_mm_dd_hh_mm_ss: 'YYYY-MM-DD HH:mm:ss',
        yyyymmddhhmmss: 'YYYYMMDDHHmmss',
        zero: 'YYYY-MM-DD 00:00:00',
        H24: 'YYYY-MM-DD 23:59:59',
        hh_mm_ss: 'HH:mm:ss'
    },
    /**
     * 事件总线事件名称常量。
     * 定义了一些应用程序中使用的事件名称，例如'detail_date_change'和'non_scene_screen_marker_click'。
     * 这样可以在组件之间通过事件总线通信时，使用这些常量作为事件的标识，避免硬编码字符串。
     */
    EVENT_BUS: {
        non_scene_screen_detail_date_change: 'detail_date_change',
        non_scene_screen_marker_click: 'non_scene_screen_marker_click',
        non_scene_screen_top_list_click: 'non_scene_screen_top_list_click',
    },
    /**
     * 巡查类型常量。
     * 定义了两种巡查类型，例如'日常巡查'和'重点巡查'。
     * 这样在处理与巡查类型相关的逻辑时，可以直接使用这些常量，而不是硬编码字符串。
     */
    CHECK_TYPE: {
        '1': {
            txt: '日常巡查',
        },
        '2': {
            txt: '重点巡查',
        },
    },
    /**
     * 巡查周期常量。
     * 定义了巡查的周期类型，例如'每日'、'每周'和'每月'。
     * 这样在设置巡查计划时，可以根据需要选择合适的周期类型。
     */
    INSPECTION_PERIOD: {
        'D': {
            txt: '每日',
            code: 'D',
        },
        'W': {
            txt: '每周',
            code: 'W',
        },
        'M': {
            txt: '每月',
            code: 'M',
        },
    },
    /**
     * 周周期常量。
     * 定义了周的每一天，例如'星期一'到'星期日'。
     * 这样在处理与周有关的逻辑时，可以直接使用这些常量。
     */
    PERIOD_W: {
        1: {
            txt: '星期一',
        },
        2: {
            txt: '星期二',
        },
        3: {
            txt: '星期三',
        },
        4: {
            txt: '星期四',
        },
        5: {
            txt: '星期五',
        },
        6: {
            txt: '星期六',
        },
        7: {
            txt: '星期日',
        },
    },
    /**
     * 月周期常量。
     * 定义了每个月的每一天，例如'1号'到'31号'。
     * 这样在处理与月有关的逻辑时，可以直接使用这些常量。
     */
    PERIOD_M: {
        1: {txt: '1号'},
        2: {txt: '2号'},
        3: {txt: '3号'},
        4: {txt: '4号'},
        5: {txt: '5号'},
        6: {txt: '6号'},
        7: {txt: '7号'},
        8: {txt: '8号'},
        9: {txt: '9号'},
        10: {txt: '10号'},
        11: {txt: '11号'},
        12: {txt: '12号'},
        13: {txt: '13号'},
        14: {txt: '14号'},
        15: {txt: '15号'},
        16: {txt: '16号'},
        17: {txt: '17号'},
        18: {txt: '18号'},
        19: {txt: '19号'},
        20: {txt: '20号'},
        21: {txt: '21号'},
        22: {txt: '22号'},
        23: {txt: '23号'},
        24: {txt: '24号'},
        25: {txt: '25号'},
        26: {txt: '26号'},
        27: {txt: '27号'},
        28: {txt: '28号'},
        29: {txt: '29号'},
        30: {txt: '30号'},
        31: {txt: '31号'},
    },
    /**
     * 区域级别常量。
     * 定义了区域的三个级别：省、市、区。
     * 这样在处理与区域级别有关的逻辑时，可以直接使用这些常量。
     */
    REGION_LEVEL: {
        // 省、市、区 三级
        province: 'province',
        city: 'city',
        district: 'district'
    },
    DIALOG_MODE: {
        add: {
            txt: '新增',
            code: 'add'
        },
        edit: {
            txt: '编辑',
            code: 'edit'
        }
    },
    HEAD_OPT_TYPE: {
        filter: 'filter',
        sort: 'sort'
    },
    SORT: {
        asc: 'ascending',
        desc: 'descending'
    }
};
