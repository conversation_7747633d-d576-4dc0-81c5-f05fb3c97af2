<template>
  <div class="progress-wrapper">
    <div
      class="progress-change"
      @click="()=>handleChange(stepKey-1)"
      :class="[stepKey <= 1 && 'step-none']"
    >
      <img src="../../../assets/images/alarmEvent/icon_zuo_12.svg" alt />
    </div>
    <el-steps
      :active="stepKey"
      finish-status="finish"
      :class="[ (!list || list.length <= 1) && 'steps-one-wrap']"
    >
      <el-step
        v-for="(item,i) in list"
        @click.native="handleChange(i+1)"
        :key="item.alarmId"
        :class="[stepKey === i+1 && 'step-active']"
      >
        <template v-if="stepKey === i+1">
          <span class="point-date" slot="title">{{ item.date }}</span>
          <span class="point-time" slot="description">{{ item.clock }}</span>
        </template>
      </el-step>
    </el-steps>
    <div
      class="progress-change"
      @click="()=>handleChange(stepKey+1)"
      :class="[stepKey >= list.length && 'step-none']"
    >
      <img src="../../../assets/images/alarmEvent/icon_you_12.svg" alt />
    </div>
  </div>
</template>

<script>
export default {
  name: 'TimeLineCarouselCom',
  props: {
    list: {
      type: Array,
      default: [],
    },
  },
  data() {
    return {
      stepKey: 1,
    }
  },
  // mounted() {},
  methods: {
    handleChange(index) {
      if (this.stepKey !== index && index > 0 && index <= this.list.length) {
        this.$emit('change', index)
      }
      if (index <= 1) {
        this.stepKey = 1
        return
      } else if (index >= this.list.length) {
        this.stepKey = this.list.length
        return
      } else {
        // to do
      }
      this.stepKey = index
    },
  },
}
</script>
<style scoped lang="scss">
@import '~@/assets/styles/px-to-rem';

.progress-wrapper {
  position: relative;
  padding: 0 px-to-rem(12);
  margin: px-to-rem(10) 0;
  display: flex;
  align-items: flex-start;

  .progress-change {
    cursor: pointer;
    display: flex;
  }

  .step-none {
    cursor: not-allowed;
    // pointer-events: none;
    -webkit-filter: grayscale(100%);
    filter: grayscale(100%);
    opacity: 0.6;
  }

  .steps-one-wrap {
    display: flex;
    justify-content: center;

    &::after {
      content: '';
      position: absolute;
      width: 100%;
      height: 2px;
      background: rgba(46, 126, 251, 0.5);
      border-radius: 2px;
      bottom: 6px;
    }
  }

  ::v-deep .el-steps {
    flex: 1;
    margin: 0 px-to-rem(8);
    position: relative;
    display: flex;

    .el-step__head {
      display: flex;
      .el-step__line {
        background: rgba(46, 126, 251, 0.5);
        border-radius: px-to-rem(2);
        top: px-to-rem(13);

        .el-step__line-inner {
          width: 0 !important;
        }
      }
      .el-step__icon.is-text {
        cursor: pointer;
        width: px-to-rem(7);
        height: px-to-rem(7);
        margin-top: px-to-rem(10);
        border: 0;
        background: #a7b8cf;
        border: 1px solid rgba(255, 255, 255, 1);

        .el-step__icon-inner {
          font-size: 0;
        }
      }
    }

    .step-active {
      .el-step__icon.is-text {
        background: url('~@/assets/images/alarmEvent/point_shijian.svg')
          no-repeat;
        background-size: contain;
        border: 0;
        width: px-to-rem(16);
        height: px-to-rem(16);
        margin-top: px-to-rem(6);
      }
    }

    .el-step__main {
      position: absolute;
      font-size: px-to-rem(12);
      left: px-to-rem(-12);
      width: px-to-rem(60);

      .el-step__title {
        line-height: px-to-rem(35);
        .point-date {
          color: rgba(232, 243, 255, 0.7);
          font-size: px-to-rem(12);
          letter-spacing: 0;
          font-weight: 400;
          background: url('~@/assets/images/alarmEvent/bg_riqi_sel.svg')
            no-repeat;
          background-size: cover;
          padding: px-to-rem(2) px-to-rem(3);
        }
      }
      .el-step__description {
        position: absolute;
        top: px-to-rem(-40);

        .point-time {
          color: #e8f3ff;
          letter-spacing: 0;
          font-weight: 500;
          background: url('~@/assets/images/alarmEvent/bg_shijian_sel.png')
            no-repeat;
          background-size: cover;
          padding: px-to-rem(2) px-to-rem(4);
        }
      }
    }

    // .el-step:last-of-type .el-step__main {
    //   width: px-to-rem(60);
    // }
  }
}
</style>

