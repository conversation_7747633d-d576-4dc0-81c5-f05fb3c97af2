import Vue from "vue"; // 引入 Vue 是因为要用到 Vue.extend() 这个方法
import message from "./message.vue"; // 引入刚才的 toast 组件

let messageConstructor = Vue.extend(message); // 这个在前面的前置知识内容里面有讲到
let instance;

const Message = function (options = {}) {
    instance = new messageConstructor({
        data: options // 这里的 data 会传到 message.vue 组件中的 data 中，当然也可以写在 props 里
    }); // 渲染组件
    document.body.appendChild(instance.$mount().$el); // 挂载到 body 下
};

/**
 * 为Message对象的success和error方法定义简化的调用方式。
 * 这段代码通过遍历一个包含"success"和"error"的数组，为每个类型动态地创建一个方法。
 * 这样做的目的是为了提供一种更直观、更简洁的方式来调用Message对象的成功和错误处理方法。
 */
["success", "error"].forEach(type => {
    /**
     * 根据传入的选项创建一个消息。
     * 这个函数的目的是为了简化Message对象的调用，使得调用者可以更方便地传递消息内容或者完整的选项对象。
     * @param {string|Object} opts - 如果是字符串，则作为消息内容；如果是对象，则作为选项。
     * @returns {Object} - 返回Message对象的实例。
     */
    Message[type] = opts => {
        // 初始化一个空的对象来存储选项。
        let options = {}
        // 判断opts的类型，如果是字符串，则只设置内容；否则，直接使用opts对象。
        if (typeof opts === 'string') {
            options = {content: opts}
        } else {
            options = {...opts}
        }
        // 设置消息的类型为当前的type。
        options.type = type;
        // 调用Message对象的方法，并返回结果。
        return Message(options);
    };
});


export default Message;
