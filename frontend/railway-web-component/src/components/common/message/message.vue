<template>
  <!--
    消息组件，用于展示不同类型的消息。
    类型由`type`属性决定，展示样式和图标会根据类型变化。
    `content`属性用于显示消息的具体内容。
  -->
  <div class='echojoy-message' :class="type ? `echojoy-message--${type}` : ''">
    <!--
      消息内容区域，包含一个图标和文字内容。
      图标由`type`属性决定，通过`svg-icon`组件显示。
    -->
    <p class='echojoy-message-content'>
      <svg-icon :svg-name="type+'_icon'"></svg-icon>
      {{ content }}
    </p>
  </div>
</template>


<script>
export default {
  name: 'EchojoyMessage',
  data() {
    return {
      // 消息类型，默认为成功类型
      type: 'success',
      // 消息内容
      content: '',
      // 消息展示的持续时间，默认为3000毫秒
      duration: 3000
    };
  },
  mounted() {
    // 使用setTimeout延迟执行清理工作，确保组件在DOM中存在足够长的时间后被移除
    setTimeout(() => {
      // 调用$destroy方法主动销毁组件
      // 参数true表示保留子节点，这里可能是为了确保整个组件被干净地移除而不只是解绑事件
      this.$destroy(true);
      // 从DOM中移除组件的根元素，这是在组件销毁后清理DOM的常见操作
      this.$el.parentNode.removeChild(this.$el);
    }, this.duration);
  }
};
</script>
<style lang='less' scoped>
.echojoy-message {
  position: fixed;
  top: 30vh;
  left: 50%;
  transform: translateX(-50%);
  color: #fff;
  z-index: 9999;
  background: transparent;

  > p {
    padding: 0 22px;
    font-size: 18px;
    border-radius: 4px;
    background: rgba(24, 25, 30, 0.8);
    min-width: 260px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &--error p {
    border: 1px solid rgba(212, 0, 0, 1);
  }

  &--success p {
    border: 1px solid rgba(109, 212, 0, 1);
  }
}
</style>

