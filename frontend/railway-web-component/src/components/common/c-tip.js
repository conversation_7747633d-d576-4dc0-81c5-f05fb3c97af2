/**
 * 该模块实现了基于jQuery的提示框功能，用于在鼠标悬停或移动时显示元素上的提示信息。
 * 提示框的位置根据鼠标位置和屏幕边界进行调整，以确保其始终可见。
 * 支持自定义提示框样式类和放置位置。
 */

import $ from 'jquery';

// 定义延时和更新提示的定时器
let tipDelay, tipUpdate;

// 为具有c-tip属性的元素绑定鼠标事件
$('body').on('mouseenter mousemove mouseleave', '[c-tip]', function(e) {
  clearTimeout(tipDelay); // 清除延时定时器
  if (e.type === 'mouseenter' || e.type === 'mouseleave') {
    clearInterval(tipUpdate); // 清除更新定时器
    $('.c-tip').remove(); // 移除所有现有的提示框
  }
  if (!(e.type === 'mouseenter' ||
    (e.type === 'mousemove' && !$('.c-tip').length))) {
    return; // 如果不是鼠标进入或移动时没有现有提示框，则不执行
  }
  tipDelay = setTimeout(() => {
    handleTip(this, e); // 延时后处理提示框显示
  }, 500);
});

let x, y; // 用于存储提示框的最终位置

/**
 * 处理提示框的显示
 * @param {HTMLElement} _this - 当前元素
 * @param {Event} e - 鼠标事件对象
 */
const handleTip = (_this, e) => {
  const offsetWidth = $(_this)[0].offsetWidth; // 元素的可视宽度
  const scrollWidth = $(_this)[0].scrollWidth; // 元素的总宽度，包括不可见部分
  const auto = $(_this).attr('c-tip-auto'); // 是否自动隐藏提示框的标志
  if (auto && scrollWidth <= offsetWidth) {
    return; // 如果元素宽度足够显示所有文本，则不显示提示框
  }
  let tipClass = 'c-tip'; // 提示框的类名
  const customClass = $(_this).attr('c-tip-class'); // 自定义提示框类名
  if (customClass) {
    tipClass += ' ' + customClass; // 添加自定义类名
  }
  let text = $(_this).attr('c-tip'); // 提示框的文本内容
  const str = `<i class='${tipClass}'>${text}</i>`; // 构建提示框的HTML字符串
  $('body').append(str); // 将提示框添加到body或全屏元素中
  if (window.fullScreenId && checkScreenFull()) {
    $('#' + window.fullScreenId).append(str);
  }
  cTipFit(e, $(_this)); // 调整提示框的位置
  tipUpdate = setInterval(() => {
    text = $(_this).attr('c-tip'); // 更新提示框的文本内容
    if ($('.c-tip:eq(0)').text() !== text) {
      $('.c-tip').text($(_this).attr('c-tip')); // 如果内容改变，则更新提示框文本
      cTipFit(e, $(_this)); // 重新调整提示框位置
    }
    if (!$(_this).is(':visible')) {
      clearInterval(tipUpdate); // 如果源元素不可见，则清除更新定时器
      $('.c-tip').remove(); // 移除提示框
    }
  }, 500);
};

/**
 * 调整提示框的位置以适应鼠标位置和屏幕边界
 * @param {Event} e - 鼠标事件对象
 * @param {jQuery} $dom - 当前元素的jQuery对象
 */
function cTipFit(e, $dom) {
  const $tip = $('.c-tip'); // 获取提示框元素
  const winW = $(window).width(); // 窗口宽度
  const winH = $(window).height(); // 窗口高度
  const tipW = $tip.outerWidth(); // 提示框宽度
  const tipH = $tip.outerHeight(); // 提示框高度
  x = e.clientX + 10; // 预设提示框的横坐标
  y = e.clientY + 14; // 预设提示框的纵坐标
  if (x + tipW > winW) {
    x -= tipW + 18; // 如果提示框超出窗口右侧，则向左移动
  }
  if (y + tipH > winH) {
    y -= tipH + 10; // 如果提示框超出窗口底部，则向上移动
  }
  $tip.css({
    display: 'block',
    left: x,
    top: y
  }); // 显示提示框并设置初始位置
  const tipPlacement = $dom.attr('c-tip-placement'); // 获取提示框的放置位置
  const domRect = $dom[0].getBoundingClientRect(); // 获取当前元素的边界信息
  if (tipPlacement === 'top') {
    tipToTop(domRect, winW, winH, tipH, tipW); // 如果放置位置为顶部，则进行调整
  }
  if (tipPlacement === 'bottom') {
    tipToBottom(domRect, winW, winH, tipH, tipW); // 如果放置位置为底部，则进行调整
  }
  if (tipPlacement === 'left') {
    tipToLeft(domRect, winW, winH, tipH, tipW); // 如果放置位置为左侧，则进行调整
  }
  if (tipPlacement === 'right') {
    tipToRight(domRect, winW, winH, tipH, tipW); // 如果放置位置为右侧，则进行调整
  }
  $tip.css({
    display: 'block',
    left: x,
    top: y
  }); // 最终设置提示框的位置
}

/**
 * 将提示框调整到元素顶部
 * @param {DOMRect} domRect - 当前元素的边界信息
 * @param {number} winW - 窗口宽度
 * @param {number} winH - 窗口高度
 * @param {number} tipH - 提示框高度
 * @param {number} tipW - 提示框宽度
 */
function tipToTop(domRect, winW, winH, tipH, tipW) {
  x = domRect.left + domRect.width / 2 - tipW / 2; // 计算提示框的横坐标
  y = domRect.top - tipH - 4; // 计算提示框的纵坐标
  if (x < 0) {
    x = 0; // 如果提示框超出窗口左侧，则靠左显示
  }
  if (x + tipW > winW) {
    x = winW - tipW; // 如果提示框超出窗口右侧，则靠右显示
  }
  if (y < 0) {
    y = domRect.bottom + 4; // 如果提示框超出窗口顶部，则改为显示在元素底部
  }
}

/**
 * 将提示框调整到元素底部
 * @param {DOMRect} domRect - 当前元素的边界信息
 * @param {number} winW - 窗口宽度
 * @param {number} winH - 窗口高度
 * @param {number} tipH - 提示框高度
 * @param {number} tipW - 提示框宽度
 */
function tipToBottom(domRect, winW, winH, tipH, tipW) {
  x = domRect.left + domRect.width / 2 - tipW / 2; // 计算提示框的横坐标
  y = domRect.bottom + 4; // 计算提示框的纵坐标
  if (x < 0) {
    x = 0; // 如果提示框超出窗口左侧，则靠左显示
  }
  if (x + tipW > winW) {
    x = winW - tipW; // 如果提示框超出窗口右侧，则靠右显示
  }
  if (y + tipH > winH) {
    y = domRect.top - tipH - 4; // 如果提示框超出窗口底部，则改为显示在元素顶部
  }
}

/**
 * 将提示框调整到元素左侧
 * @param {DOMRect} domRect - 当前元素的边界信息
 * @param {number} winW - 窗口宽度
 * @param {number} winH - 窗口高度
 * @param {number} tipH - 提示框高度
 * @param {number} tipW - 提示框宽度
 */
function tipToLeft(domRect, winW, winH, tipH, tipW) {
  x = domRect.left - tipW - 4; // 计算提示框的横坐标
  y = domRect.top + domRect.height / 2 - tipH / 2; // 计算提示框的纵坐标
  if (x < 0) {
    x = domRect.right + 4; // 如果提示框超出窗口左侧，则改为显示在元素右侧
  }
}

/**
 * 将提示框调整到元素右侧
 * @param {DOMRect} domRect - 当前元素的边界信息
 * @param {number} winW - 窗口宽度
 * @param {number} winH - 窗口高度
 * @param {number} tipH - 提示框高度
 * @param {number} tipW - 提示框宽度
 */
function tipToRight(domRect, winW, winH, tipH, tipW) {
  x = domRect.right + 4; // 计算提示框的横坐标
  y = domRect.top + domRect.height / 2 - tipH / 2; // 计算提示框的纵坐标
  if (x + tipW > winW) {
    x = domRect.left - tipW - 4; // 如果提示框超出窗口右侧，则改为显示在元素左侧
  }
}

/**
 * 检查是否处于全屏模式
 * @returns {boolean} - 是否全屏
 */
function checkScreenFull() {
  const webkitFull = document.webkitIsFullScreen ||
    document.webkitRequestFullScreen;
  const mozFull = document.mozFullScreen || document.mozRequestFullScreen;

  return mozFull || webkitFull || document.fullScreen ||
    document.msFullscreenEnabled;
}
