@theme-color: #FF6A6C;
.iframe_container {
    width: calc(100% - 40px);
    height: calc(100% - 40px);
    background-color: #ffffff;
    margin: 20px 20px;

    /deep/ .el-tabs__item {
        color: #FF696A;
    }

    /deep/ .el-button--primary {
        color: #FF696A;
        background-color: #fefefe;
        border-color: #FF696A;
        cursor: pointer !important;

        &:hover {
            color: #fefefe;
            background-color: #FF696A;
            border-color: #FF696A;
        }
    }

    /deep/ .el-pagination.is-background .el-pager li:not(.disabled).active {
        background-color: #FF6A6C;
    }

    /deep/ .el-pager li {
        font-weight: 100;
        background-color: transparent;
    }

    /deep/ .el-pagination.is-background .btn-prev, /deep/ .el-pagination.is-background .btn-next {
        background-color: transparent !important;
    }

    /deep/ .el-dialog__wrapper {
        &.big-size .el-dialog {
            width: 60% !important;
            min-height: 70vh;
        }

        &.large-size .el-dialog {
            width: 80% !important;
            min-height: 70vh;
        }

        &.small-size .el-dialog {
            width: 35% !important;
            min-height: 30vh;
        }

        &.middle-size .el-dialog {
            width: 35% !important;
            min-height: 50vh;
        }

        .el-dialog {
            .el-dialog__header {
                border-bottom: 1px solid #00000012;
                margin-bottom: 10px;
            }

            .el-dialog__body {
                padding: 10px 20px;
            }

            .el-dialog__title {
                font-size: 16px;
                color: #000000;
                font-weight: bold;
            }

            .el-dialog__footer {
                border-top: 1px solid #00000012;
            }

            .dialog-footer {
                display: flex;

            }
        }

    }

    /deep/ .el-loading-spinner .path {
        stroke: #FF6A6C;
    }

    /deep/ .el-slider {
        .el-slider__button {
            width: 8px;
            height: 8px;
            border: 2px solid @theme-color;
        }

        .el-slider__bar {
            height: 3px;
            background-color: @theme-color;
            border-top-left-radius: 3px;
            border-bottom-left-radius: 3px;
            position: absolute;
        }

        .el-slider__runway {
            height: 3px;
            background-color: #c4c6cf;
        }
    }

    /deep/ .el-loading-spinner .path {
        stroke: #FF6A6C;
    }

    //   /deep/ .el-table::before {
    //     background-color: transparent;
    //   }

    /deep/ .el-radio.is-bordered.is-checked {
        border-color: #FF6A6C;
    }

    /deep/ .el-radio__input.is-checked + .el-radio__label {
        color: #FF6A6C;
    }

    /deep/ .el-radio__input.is-checked .el-radio__inner {
        border-color: #FF6A6C;
        background: #FF6A6C;
    }

    /deep/ .el-radio__inner:hover {
        border-color: #FF6A6C;
    }

    /deep/ .el-icon-btn-delete {
        background: url(./img/delete_btn.svg) no-repeat;
        background-size: cover;
        display: inline-block;
        width: 20px;
        height: 20px;
    }
}

.settings_content {
    .content_control {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .control {
            display: flex;
            align-items: center;
            justify-content: flex-start;

            .control_label {
                font-size: 14px;
                color: #606266;
                letter-spacing: 0;
                font-weight: bold;
                margin-right: 10px;
                font-family: "Alibaba-PuHuiTi-H", sans-serif;

                &.in_need:before {
                    content: "*";
                    color: #f56c6c;
                    margin-right: 4px;
                    text-align: center;
                    vertical-align: middle;
                }
            }

            .el-autocomplete {
                width: 300px;
            }
        }
    }

    .content_btn {
        padding: 15px 0;
        border-bottom: #F0F0F0 1px solid;
        margin-bottom: 10px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
    }

    .content_foot {
        display: flex;
        align-items: center;
        justify-content: flex-end;
    }
}

/deep/ .overflow {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
}

.overflow_2 {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
}

