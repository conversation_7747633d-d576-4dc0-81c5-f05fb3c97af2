<template>
  <div class="flip-counter">
    <!-- 使用 Vue 的 transition 组件来实现动画效果，direction 用于控制动画方向 -->
    <transition :name="direction" mode="out-in">
      <!-- key 绑定到 value 上，以便在 value 改变时触发过渡效果 -->
      <div :key="value" class="number-container">
        <!-- 显示当前的数字值 -->
        <span class="number">{{ value }}</span>
      </div>
    </transition>
  </div>
</template>
<script>
export default {
  props: {
    value: { type: Number, required: true } // 外部传入的数字，必须是数字类型
  },
  data() {
    return {
      direction: 'up' // 方向标记: up/down，默认向上
    };
  },
  watch: {
    value(newVal, oldVal) {
      // 监听数字变化，判断方向
      this.direction = newVal > oldVal ? 'up' : 'down'; // 如果新值大于旧值，方向为 up，否则为 down
    }
  }
};
</script>
<style scoped>
.flip-counter {
  display: inline-block;
  font-size: 20px;
  position: relative;
  overflow: hidden; /* 隐藏溢出内容 */
  height: 24px; /* 固定高度 */
  flex: 1;
}
.number-container {
  display: inline-block;
  position: absolute;
  width: 100%;
  text-align: center;
  transition: transform 0.2s ease; /* 平滑过渡 */
}
.number {
  display: inline-block;
  width: 100%;
  height: 100%;
}

/* 向上增长动画 */
.up-enter-active {
  transform: translateY(-100%); /* 新数字从上方进入 */
}
.up-enter-to {
  transform: translateY(0);
}
.up-leave-active {
  transform: translateY(0); /* 旧数字从下方离开 */
}
.up-leave-to {
  transform: translateY(100%);
}

/* 向下减少动画 */
.down-enter-active {
  transform: translateY(100%); /* 新数字从下方进入 */
}
.down-enter-to {
  transform: translateY(0);
}
.down-leave-active {
  transform: translateY(0); /* 旧数字从上方离开 */
}
.down-leave-to {
  transform: translateY(-100%);
}
</style>
