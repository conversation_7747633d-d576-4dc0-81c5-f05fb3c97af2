<!--
 * @Description: 定义了一个可排序的表格列，根据是否可排序使用不同的实现方式。
 * @Autor: he.chao
 * @Date: 2022-10-19 15:07:55
 * @LastEditors: liu.yongli
 * @LastEditTime: 2023-12-13 14:36:14
-->
<template>
  <!-- 当不可排序时，使用以下表格列配置 -->
  <el-table-column
    v-if='!sortable'
    align='center'
    :key='columnKey'
    :columnKey='columnKey'
    :prop='prop'
    :label='label'
    :filter-multiple='false'
    :filtered-value='filteredValue'
    :filters='filters'
    :min-width='minWidth'
    :show-overflow-tooltip='true'
  >
    <!-- 头部模板，显示列标签并包含筛选图标 -->
    <template slot='header' slot-scope='scope'>
      {{ label }}
      <icon-table-header-filter />
    </template>
    <!-- 行数据模板，通过插槽自定义行显示内容 -->
    <template slot-scope='scope'>
      <slot name='row' :rowData='scope.row'></slot>
    </template>
  </el-table-column>
  <!-- 当可排序时，使用以下表格列配置 -->
  <el-table-column
    v-else
    :key='columnKey'
    :columnKey='columnKey'
    align='center'
    :prop='prop'
    :label='label'
    sortable='custom'
    :min-width='minWidth'
    :show-overflow-tooltip='true'
  >
    <!-- 行数据模板，通过插槽自定义行显示内容 -->
    <template slot-scope='scope'>
      <slot name='row' :rowData='scope.row'></slot>
    </template>
  </el-table-column>
</template>

<script>
import IconTableHeaderFilter from '@/components/page/ScreenPages/IntegratedMonitor/Icons/icon-table-header-filter.vue';

export default {
  // 组件名称
  components: { IconTableHeaderFilter },
  props: {
    // 是否可排序
    sortable: {
      type: Boolean, // Boolean
      default: false
    },
    // 表格列的key
    columnKey: {
      type: String,// String
      default: ''// ''
    },
    // 表格列的prop
    prop: {
      type: String,// String
      default: ''
    },
    // 表格列的label
    label: {
      type: String,// String
      default: ''
    },
    // 筛选条件值
    filteredValue: {
      type: Array, // Array
      default: () => []
    },
    // 筛选条件
    filters: {
      type: Array, // Array
      default: () => []
    },
    // 列最小宽度
    minWidth: {
      type: String, // px
      default: '10%'
    }
  },
  /**
   *  获取组件数据
   * @returns {{}}
   */
  data() {
    return {};
  },
  /**
   * 组件方法定义
   */
  methods: {}
};
</script>
