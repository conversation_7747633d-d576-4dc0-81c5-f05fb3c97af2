<template>
  <div ref="remoteLayout" style="width: 100%; height: 100%">
    <div class="left-wrapper">
      <template v-for="(component, index) in components">
        <!-- 天气组件 -->
        <template v-if="component.config.name === 'common-comp-weather'">
          <component :is="component.name" :key="`weather-${index}`" :config="component.config || {}" v-bind="weatherProp" />
        </template>
        <!-- 地图搜索 -->
        <template v-else-if="component.config.name === 'common-comp-search-map'">
          <component
            :is="component.name"
            :key="`search-map-${index}`"
            style="z-index: 111"
            :config="component.config || {}"
            v-bind="weatherProp"
            :mapId="mapId"
          />
        </template>
      </template>
      <div class="tree-wrapper">
        <template v-for="(component, index) in components">
          <!-- 摄像机tree -->
          <template v-if="component.config.name === 'common-comp-tree'">
            <component
              :is="component.name"
              :key="`tree-${index}`"
              :config="component.config || {}"
              :mapId="mapId"
              :title="title"
              menuConfigMemoryName="homeAlarmEventSettingMenu"
              treeSortMemoryName="functionMenuSortTypeLy"
            />
          </template>
          <!-- iot tree -->
          <template v-if="component.config.name === 'common-comp-iot-tree'">
            <component
              :is="component.name"
              :key="`iot-${index}`"
              :config="component.config || {}"
              :mapId="mapId"
              :title="titleWl"
              treeSortMemoryName="functionMenuSortTypeLy"
            />
          </template>

          <!-- uav tree -->
          <template v-if="component.config.name === 'common-comp-uav-tree'">
            <component
              :is="component.name"
              :key="`uav-${index}`"
              :config="component.config || {}"
              :mapId="mapId"
              :title="titleWRJ"
              treeSortMemoryName="functionMenuSortTypeLy"
            />
          </template>

          <!-- horn tree -->
          <template v-if="component.config.name === 'common-comp-horn-tree'">
            <component
              :is="component.name"
              :key="`horm-${index}`"
              :config="component.config || {}"
              :mapId="mapId"
              :title="titleLb"
              treeSortMemoryName="functionMenuSortTypeLy"
            />
          </template>

          <!-- source tree -->
          <template v-if="component.config.name === 'common-comp-source-tree'">
            <component
              :is="component.name"
              :key="`source-${index}`"
              :config="component.config || {}"
              :mapId="mapId"
              :title="sourceTitle"
              treeSortMemoryName="functionMenuSortTypeLy"
            />
          </template>

          <!-- grid tree -->
          <template v-if="component.config.name === 'common-comp-grid-tree'">
            <component
              :is="component.name"
              :key="`source-${index}`"
              :config="component.config || {}"
              :mapId="mapId"
              :title="gridTitle"
              treeSortMemoryName="functionMenuSortTypeLy"
            />
          </template>
          <!-- grid operator tree -->
          <template v-if="component.config.name === 'common-comp-grid-operator-tree'">
            <component
              :is="component.name"
              :key="`source-${index}`"
              :config="component.config || {}"
              :title="titleWGY"
              treeSortMemoryName="functionMenuSortTypeLy"
              :mapId="mapId"
            />
          </template>
        </template>
      </div>
    </div>
    <template v-for="(component, index) in components">
      <!-- footer组件 -->
      <template v-if="component.config.name === 'common-comp-footer'">
        <component
          :is="component.name"
          :key="component + index"
          :config="component.config || {}"
          :mapId="mapId"
          menuConfigMemoryName="homeAlarmEventSettingMenu"
          treeSortMemoryName="functionMenuSortTypeLy"
        />
      </template>
      <!-- 小工具组件 -->
      <template v-if="component.config.name === 'common-comp-tool-box'">
        <component :is="component.name" :key="component + index" :config="component.config || {}" :mapId="mapId" style="right: 415px" />
      </template>
      <!-- 告警组件 -->
      <template v-if="component.config.name === 'common-comp-fire-alarm-group'">
        <component :is="component.name" :key="component + index" :config="component.config || {}" :mapId="mapId" />
      </template>
      <!-- 告警详情组件 -->
      <template v-if="component.config.name === 'common-comp-alarm-detail'">
        <component :is="component.name" :key="component + index" :config="component.config || {}" :mapId="mapId" :is3dMap="false" />
      </template>
      <!--地图-->
      <template v-if="component.config.name === 'common-comp-map'">
        <div :id="mapId" :key="component.config.name" class="map-div">
          <component :is="component.name" :key="component + index" :mapId="mapId" :config="component.config || {}" />
        </div>
      </template>
    </template>
    <button style="position: absolute; bottom: 10px; left: 10px" @click="changeTheme">切换主题</button>
  </div>
</template>
<script>
const PORT = 3000
const HOST = 'http://127.0.0.1'
const baseCompObj = {
  name: 'RemoteComponentsLoader',
  config: {
    name: '',
    description: '',
    js: '',
    css: ''
  }
}

const COMP_ARR = [
  // {
  //   name: 'common-comp-footer',
  //   description: '远程footer组件',
  // },
  // {
  //   name: 'common-comp-tree',
  //   description: '远程tree组件',
  // },
  // {
  //   name: 'common-comp-weather',
  //   description: '远程weather组件',
  // },
  // {
  //   name: 'common-comp-search-map',
  //   description: '远程search-map组件',
  // },

  // {
  //   name: 'common-comp-iot-tree',
  //   description: '远程iot-tree组件',
  // },
  // {
  //   name: 'common-comp-horn-tree',
  //   description: '远程horn-tree组件',
  // },

  // {
  //   name: 'common-comp-uav-tree',
  //   description: '远程uav-tree组件',
  // },
  // {
  //   name: 'common-comp-source-tree',
  //   description: '远程source-tree组件',
  // },
  // {
  //   name: 'common-comp-grid-tree',
  //   description: '远程网格树组件',
  // },
  // {
  //   name: 'common-comp-grid-operator-tree',
  //   description: '远程网格员树组件',
  // },
  {
    name: 'common-comp-tool-box',
    description: '远程小工具箱组件'
  },
  // {
  //   name: 'common-comp-fire-alarm-group',
  //   description: '远程告警组件',
  // },
  // {
  //   name: 'common-comp-alarm-detail',
  //   description: '远程告警详情组件',
  // },
  {
    name: 'common-comp-map',
    description: '远程地图组件'
  }
]

import RemoteComponentsLoader from '@ct/remote-page-loader/remote-page-loader.umd.js'
import { requestSDK } from '@ct/iframe-connect-sdk'
import { httpService } from 'common/config/common'
import { loadSpaceShardAsync } from '@ct/remote-page-loader/utils/remote-loader'

let themeIndex = 0

const getUri = (host, port) => `${host}:${port}`

const uri = getUri(HOST, PORT)

const generateCompsConfig = () => {
  const res = COMP_ARR.map(elem => {
    return {
      ...baseCompObj,
      config: {
        ...elem,
        js: `${uri}/${elem.name}/${elem.name}.umd.min.js`,
        css: `${uri}/${elem.name}/${elem.name}.css`
      }
    }
  })
  return res
}

export default {
  name: 'RemoteComponentLayout',
  components: {
    RemoteComponentsLoader
  },
  data() {
    return {
      loaded: false,
      components: [],
      remoteComponents: [],
      value: '',
      title: '',
      titleWl: '',
      titleLb: '',
      sourceTitle: '',
      gridTitle: '',
      titleWRJ: '',
      titleWGY: '',
      mapId: 'monitorWarn-map',
      showFireWarnList: 0, // 是否展开告警列表 0 关闭 1 展开 2 半展开 (火警预警不再使用)
      showAlarmList: 0, // 是否展开告警列表 0 关闭 1 展开 2 半展开(火警预警不再使用)
      mapReady: false,
      map: null,
      mapInstance: null,
      CTMapType: null,
      weatherProp: null,
      /**
       * footer组件传递属性
       */
      currentSelectFunction: [],
      isAllOpen: true,
      mapObj: {}
    }
  },
  created() {
    this.loaded = true
    // const remotes = generateCompsConfig();
    // loadSpaceShardAsync({ data: remotes }).then(resp => {
    //   this.components = resp;
    // });
  },
  async mounted() {
    await this.getInfo()
    this.setGisServe()
    await this.getRemoteComps()
    this.changeTheme()
  },
  methods: {
    changeTheme() {
      if (themeIndex === 0) {
        document.documentElement.setAttribute('data-theme', `theme-aquamarine`)
        this.$globalEventBus.$emit('data-theme', 'theme-aquamarine')
        console.log('林业', themeIndex)
      }
      if (themeIndex === 1) {
        document.documentElement.setAttribute('data-theme', `theme-terracotta`)
        this.$globalEventBus.$emit('data-theme', 'theme-terracotta')
        console.log('国土', themeIndex)
      }
      if (themeIndex === 2) {
        document.documentElement.setAttribute('data-theme', `theme-wiseblue`)
        this.$globalEventBus.$emit('data-theme', 'theme-wiseblue')
        console.log('通用', themeIndex)
      }

      if (themeIndex >= 2) {
        themeIndex = 0
        return
      }
      themeIndex += 1
      console.log(themeIndex)
    },
    async getInfo() {
      const resp = await requestSDK('getInfo')
      if (resp.code === 200) {
        sessionStorage.setItem('Admin-Token', resp.user.token)
        return resp
      }
    },
    setGisServe() {
      localStorage.setItem('gisUrl', process.env.VUE_APP_GIS_RESOURCE_API)
    },
    async getRemoteComps() {
      // console.log(httpService.requestSDK, '>>>>>>>>>', window.requestSDK);

      const param = {
        components: [
          { uniqueId: '2024032559247', version: '1.6.37' },
          { uniqueId: '2024032542550', version: '1.6.37' }
        ]
      }

      // window.requestSDK('/component-gallery/api/business/files', param, {}, 'post');
      httpService.post(
        this,
        '/component-gallery/api/business/files',
        {
          components: [
            // { uniqueId: '2024032559247', version: '1.6.37' },
            // { uniqueId: '2024032542550', version: '1.6.37' },
            { uniqueId: '2024041064235', version: '1.6.46' }
            // { uniqueId: '2024033005840', version: '1.6.37' },
            // { uniqueId: '2024033012161', version: '1.6.37' },
            // { uniqueId: '2024033006830', version: '1.6.37' },
            // { uniqueId: '2024033023225', version: '1.6.37' },
            // { uniqueId: '2024033000435', version: '1.6.37' },
            // { uniqueId: '2024033038811', version: '1.6.37' },
            // { uniqueId: '2024033026998', version: '1.6.37' },
            // { uniqueId: '2024033063059', version: '1.6.37' },
            // { uniqueId: '2024033004755', version: '1.6.37' },
            // { uniqueId: '2024033013685', version: '1.6.37' },
            // { uniqueId: '2024033008625', version: '1.6.37' },
            // { uniqueId: '2024033032256', version: '1.6.37' },
            // { uniqueId: '2024033081836', version: '1.6.37' },
            // { uniqueId: '2024033022088', version: '1.6.37' },
          ]

          // components: [
          //   {
          //     uniqueId: '2024032595762',
          //     version: '1.6.37',
          //   },
          //   {
          //     uniqueId: '2024032501639',
          //     version: '1.6.37',
          //   },
          //   {
          //     uniqueId: '2024032559247',
          //     version: '1.6.37',
          //   },
          //   {
          //     uniqueId: '2024032520870',
          //     version: '1.6.37',
          //   },
          //   {
          //     uniqueId: '2024032549185',
          //     version: '1.6.37',
          //   },
          //   {
          //     uniqueId: '2024032506924',
          //     version: '1.6.37',
          //   },
          //   {
          //     uniqueId: '2024032599244',
          //     version: '1.6.37',
          //   },
          //   {
          //     uniqueId: '2024032534737',
          //     version: '1.6.37',
          //   },
          //   {
          //     uniqueId: '2024032500807',
          //     version: '1.6.37',
          //   },
          //   {
          //     uniqueId: '2024032538111',
          //     version: '1.6.37',
          //   },
          //   {
          //     uniqueId: '2024032597139',
          //     version: '1.6.37',
          //   },
          //   {
          //     uniqueId: '2024032584114',
          //     version: '1.6.37',
          //   },
          //   {
          //     uniqueId: '2024032545783',
          //     version: '1.6.37',
          //   },
          //   {
          //     uniqueId: '2024032542550',
          //     version: '1.6.37',
          //   },
          // ],
        },
        async resp => {
          const { code, data } = resp
          if (code && code === 200) {
            const baseCompObj2 = {
              name: 'RemoteComponentsLoader',
              config: {
                name: '',
                description: '',
                js: '',
                css: ''
              }
            }
            const keys = Object.keys(data)
            const remotes = keys.map(key => {
              const { js, css } = data[key]
              return {
                ...baseCompObj2,
                config: {
                  name: key,
                  css,
                  js
                }
              }
            })
            const resp2 = await loadSpaceShardAsync({ data: remotes })
            console.log('res:', resp2, '>>>>>>')
            this.$nextTick(() => {
              this.components = resp2
            })
            return
          }

          this.$notify.error({
            title: `获取远程组件失败`,
            message: `获取远程组件失败`
          })
        },
        err => {
          const { title, message } = err
          this.$notify.error({
            title: `${title}`,
            message: `${message}`
          })
        },
        {
          loading: false
        }
      )
    }
  }
}
</script>
<style lang="scss">
html,
body,
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
  height: 100vh;
  width: 100vw;
  margin: 0px;
  padding: 0px;
  /* overflow: hidden; */
}

.map-div {
  width: 100%;
  height: 100%;
}

.change-theme {
  position: absolute;
  bottom: 30px;
  left: 30px;
  z-index: 999;
}

.left-wrapper {
  position: absolute;
  top: 24px;
  left: 24px;
  display: flex;
  flex-flow: column;
  gap: 12px;
  height: calc(100% - 100px);
  .tree-wrapper {
    position: relative;
    flex: 1;
  }
}
</style>
