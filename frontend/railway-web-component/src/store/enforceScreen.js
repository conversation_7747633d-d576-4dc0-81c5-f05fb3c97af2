import moment from 'moment'; // 导入 moment 库，用于处理日期和时间
export default {
    namespaced: true, // 启用命名空间，确保模块内的状态、变更和动作在全局中具有唯一性
    state: {
        searchCondition: { // 定义模块的状态对象
            selDate: [moment().subtract(7, 'days'), moment()], // 默认选择日期范围为过去7天到今天
            checkPoint: '', // 检查点的标识符，初始为空字符串
            checkPointName: '', // 检查点的名称，初始为空字符串
        },
    },
    mutations: {
        setSearchCondition(state, item) { // 定义一个 mutation，用于更新搜索条件
            state.searchCondition = item; // 将传入的 item 对象赋值给 state 中的 searchCondition
        },
    },
    getters: {
        searchCondition: state => { // 定义一个 getter，用于获取格式化后的搜索条件
            return {
                startDate: moment(state.searchCondition.selDate[0]).format('YYYY-MM-DD'), // 格式化开始日期为 'YYYY-MM-DD' 格式
                endDate: moment(state.searchCondition.selDate[1]).format('YYYY-MM-DD'), // 格式化结束日期为 'YYYY-MM-DD' 格式
                zcId: state.searchCondition.checkPoint, // 获取检查点标识符
                roadId: state.searchCondition.roadId, // 获取道路标识符
            };
        },
        oriSearchCondition: state => { // 定义一个 getter，用于获取原始搜索条件
            return state.searchCondition; // 返回未格式化的搜索条件对象
        },
    },
};
