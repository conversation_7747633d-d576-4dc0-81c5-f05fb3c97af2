/*
 * @Description:
 * @Autor: he.chao
 * @Date: 2022-11-10 14:26:46
 * @LastEditors: he.chao
 * @LastEditTime: 2022-12-07 15:09:45
 */
import Vue from "vue";
import Vuex from "vuex";
import map from '@/store/map';
import event from '@/store/event'
import mockDataStore from '@/store/mockDataStore'
import captureInfo from '@/store/captureInfo'

Vue.use(Vuex);
export default new Vuex.Store({
    namespaced: true,
    state: {

    },
    mutations: {

    },
    getters: {
    },
    modules: {
        map,
        event,
        mockDataStore,
        captureInfo
    }
});
