/*
 * @Description: Vuex模块，用于管理过车相关的状态
 */
// import ConstEnum from '@/components/common/ConstEnum'; // 导入常量枚举
// import { load } from '@amap/amap-jsapi-loader';
// import { init } from 'echarts';
// import moment from 'moment'; // 导入moment库用于日期格式化
import Vue from 'vue';

export default {
  namespaced: true, // 启用命名空间
  state: {
    captureStorage: {}, // 后台ws推送的过车数据，key-设备code，value-过车数据队列，{deviceCode:[]}
    curRenderCaptureDevc: [], // 当前渲染的过车数据，[deviceCode1,deviceCode2,deviceCode3]
    oriVideLayerOpenStatus: false, // 原始视频层是否打开
    curSelectCamera: null, // 当前选择的摄像头
    cameraLayerReady: false, // 摄像机图层是否准备好
  },
  mutations: {
    // 设置当前选择的摄像头
    setCurSelectCamera(state, item) {
      state.curSelectCamera = item;
    },
    // 设置原始视频层是否打开
    setOriVideLayerOpenStatus(state, item) {
      state.oriVideLayerOpenStatus = item;
    },
    // 设置摄像机图层是否准备好
    setCameraLayerReady(state, item) {
      state.cameraLayerReady = item;
    },
    INIT_CAPTURE_INFO(state) {
      state.captureStorage = {};
      state.curRenderCaptureDevc = [];
    },
    // 存储层操作
    ADD_CAPTURE_INFO(state, { deviceCode, captureInfo }) {
      if (!state.captureStorage[deviceCode]) {
        Vue.set(state.captureStorage, deviceCode, []);
      }
      state.captureStorage[deviceCode].push(captureInfo);
    },
    REMOVE_CAPTURE_INFO(state, { deviceCode }) {
      if (state.captureStorage[deviceCode]?.length) {
        state.captureStorage[deviceCode].shift();
        if (state.captureStorage[deviceCode].length === 0) {
          Vue.delete(state.captureStorage, deviceCode)
        }
      }
    },
    // 播放层操作
    START_RENDER(state, { deviceCode }) {
      const newDevcArr = [...state.curRenderCaptureDevc];
      if (!newDevcArr.includes(deviceCode)) {
        newDevcArr.push(deviceCode);
      }
      state.curRenderCaptureDevc = newDevcArr;
    },
    STOP_RENDER(state, { deviceCode }) {
      const newDevcArr = [...state.curRenderCaptureDevc].filter(item => item !== deviceCode);
      state.curRenderCaptureDevc = newDevcArr;
    },
    // SET_TIMER(state, { deviceCode, timerId }) {
    //     const status = state.playbackStatus[deviceCode];
    //     if (status) {
    //         status.timerId = timerId;
    //     }
    // }
  },
  actions: {
    // 初始化过车数据存储
    initCaptureInfo({ commit }) {
      commit('INIT_CAPTURE_INFO');
    },
    // 添加新任务（纯存储操作）
    addCaptureTask({ commit }, { deviceCode, captureInfo }) {
      commit('ADD_CAPTURE_INFO', { deviceCode, captureInfo });
      commit('START_RENDER', { deviceCode });
    },

    // 加载数据，此时消费完成，删除队列中的数据
    loadedCaptureTask({ commit }, deviceCode) {
      commit('REMOVE_CAPTURE_INFO', { deviceCode });
    },

    // 停止播放
    stopRenderCaptureWin({ commit }, deviceCode) {
      commit('STOP_RENDER', { deviceCode });
    },

    // 尝试播放逻辑
    // async tryPlayNext({ state, commit }, deviceCode) {
    //     // 如果设备正在播放中则跳过
    //     if (state.playbackStatus[deviceCode]) return;

    //     const queue = state.captureStorage[deviceCode] || [];
    //     if (queue.length === 0) return;

    //     // 取出任务
    //     const captureInfo = queue[0];
    //     commit('START_RENDER', { deviceCode, captureInfo });
    //     commit('REMOVE_CAPTURE_INFO', { deviceCode });

    //     // 设置播放结束定时器
    //     const timerId = setTimeout(() => {
    //         commit('STOP_PLAYING', deviceCode);
    //         this.dispatch('tryPlayNext', deviceCode); // 自动触发下一个
    //     }, 10000);

    //     commit('SET_TIMER', { deviceCode, timerId });
    // },

    // // 强制中断当前播放
    // forceStop({ state, commit }, deviceCode) {
    //     const status = state.playbackStatus[deviceCode];
    //     if (!status) return;

    //     clearTimeout(status.timerId);
    //     commit('STOP_PLAYING', deviceCode);
    //     this.dispatch('tryPlayNext', deviceCode);
    // }
  },
  getters: {
    // 获取过车数据
    getDeviceCaptureList: state => (deviceCode) => {
      return state.captureStorage[deviceCode] || [];
    },
    getOriVideLayerOpenStatus: state => state.oriVideLayerOpenStatus,
    getCurSelectCamera: state => state.curSelectCamera,
    getCameraLayerReady: state => state.cameraLayerReady,
  }
};
