/*
 * @Description: Vuex模块，用于管理事件相关的状态
 * @Autor: he.chao
 * @Date: 2022-11-10 14:26:46
 * @LastEditors: he.chao
 * @LastEditTime: 2022-11-16 17:08:10
 */
import ConstEnum from '@/components/common/ConstEnum'; // 导入常量枚举
import moment from 'moment'; // 导入moment库用于日期格式化
import commonService from '@/api/service/common'
import { eventSourceListDataDefault, dealStateList, evtTypesList } from '@/components/page/ScreenPages/IntegratedMonitor/EventFilter/typeDatas'
import DicConst from '@/components/page/ScreenPages/FullViewMonitor/enums/DicConst'

export default {
  namespaced: true, // 启用命名空间
  state: {
    curEvent: {}, // 当前事件对象
    evtListOpen: true, // 事件列表是否展开
    evtFilter: {}, // 事件过滤条件
    evtTypeList: [], // 事件类型列表
    evtSourceList: [], // 事件来源列表
    evtOrderStatusList: [], // 处置状态列表
    filterState: '',// 工具箱事件筛选的状态
  },
  mutations: {
    // 设置当前事件
    setCurEvent(state, item) {
      // 如果当前事件与传入事件相同，则清空当前事件，否则设置为传入事件
      state.curEvent = state.curEvent == item ? {} : item;
    },
    // 设置事件列表展开状态
    setEvtListOpen(state, item) {
      state.evtListOpen = item;
    },
    // 设置事件过滤条件
    setEvtFilter(state, item) {
      // 合并现有过滤条件与传入的过滤条件
      state.evtFilter = Object.assign({}, state.evtFilter, item);
    },
    // 设置事件 类型列表
    setEvtTypeList(state, item) {
      state.evtTypeList = item
    },
    // 设置事件来源列表
    setEvtSourceList(state, item) {
      state.evtSourceList = item
    },
    // 设置处置状态
    setEvtOrderStatusList(state, item) {
      state.evtOrderStatusList = item
    },
    setEvtListFilterState(state, item) {
      state.filterState = item;
    },
  },
  getters: {
    // 获取当前事件
    getCurEvent: state => state.curEvent,
    // 获取事件列表展开状态
    getEvtListOpen: state => state.evtListOpen,
    // 格式化事件过滤条件
    formatEvtFilter: state => {
      return {
        ...state.evtFilter,
        ...{
          // 格式化报警开始时间，如果未设置则默认6个月前
          alarmTimeStart: state.evtFilter.alarmTimeStart
            ? moment(state.evtFilter.alarmTimeStart).format(
              ConstEnum.DATE_FORMAT.zero
            )
            : moment().subtract(6, 'month').format(
              ConstEnum.DATE_FORMAT.zero
            ),
          // 格式化报警结束时间，如果未设置则默认当前时间
          alarmTimeEnd: state.evtFilter.alarmTimeEnd
            ? moment(state.evtFilter.alarmTimeEnd).format(
              ConstEnum.DATE_FORMAT.H24) : moment().format(
                ConstEnum.DATE_FORMAT.yyyy_mm_dd_hh_mm_ss
              )
        }
      };
    },
    // 获取事件类型列表
    getEvtTypeList: state => state.evtTypeList,
    // 获取事件类型列表
    getEvtSourceList: state => state.evtSourceList,
    getEvtOrderStatusList: state => state.evtOrderStatusList,
  },
  actions: {
    /**
     * 查询事件来源
     */
    async fetchEventSourceData({ commit }) {
      try {
        const res = await commonService.getCommonPlatDictByDictType({
          dictType: 'warning_source_common', // 40
        })
        // 提交mutation更新state
        commit('setEvtSourceList', res?.data ?? eventSourceListDataDefault)
      } catch (e) {
        console.error('获取数据失败:', error)
        throw error // 可以抛出错误让组件处理
      }
    },
    /**
     * 查询事件类型
     */
    async fetchEventData({ commit }) {
      try {
        const res = await commonService.getEventTypeDictByDictType()
        const data = res?.data ?? evtTypesList
        // 存入 store
        const _list = data?.length
          ? data.map(v => ({
            ...v,
            title: v.tagName,
            dictLabel: v.tagName,
            dictValue: v.tagCode,
          }))
          : []
        // 提交mutation更新state
        commit('setEvtTypeList', _list)
      } catch (e) {
        console.error('获取数据失败:', error)
        throw error // 可以抛出错误让组件处理
      }
    },
    /**
     * 查询处置状态类型
     */
    async fetchOrderStatusData({ commit }) {
      try {
        // const res = await commonService.getDictListByCatCode(
        //   DicConst.DIC_CODE.ALARM_STATUS
        // )
        const res = await commonService.getCommonPlatDictByDictType({
          dictType: 65
        })
        // const data = res?.data ?? dealStateList
        // 存入 store
        // const _list = data?.length
        //   ? data.map(v => ({
        //     ...v,
        //     title: v.codeName,
        //     dictLabel: v.codeName,
        //     dictValue: v.dicValue,
        //   }))
        //   : []
        // 提交mutation更新state
        commit('setEvtOrderStatusList', res?.data ?? dealStateList)
      } catch (e) {
        console.error('获取数据失败:', error)
        throw error // 可以抛出错误让组件处理
      }
    },
  }
};
