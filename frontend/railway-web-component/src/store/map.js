/*
 * @Description: Vuex模块，用于管理地图相关的状态
 * @Autor: he.chao
 * @Date: 2022-11-10 14:26:46
 * @LastEditors: liu.yongli
 * @LastEditTime: 2025-02-20 20:04:47
 */
import ConstEnum from '@/components/common/ConstEnum'; // 导入常量枚举
export default {
  namespaced: true, // 启用命名空间
  state: {
    mapType: 2, // 地图类型
    pickLnglat: "", // 选取的经纬度
    locationLnglat: "", // 定位的经纬度
    checkMode: false, // 检查模式开关
    passRouteSwitch: false, // 路径通过开关
    mapToolSwitch: false, // 地图工具开关
    selectedRegion: null, // 选中的区域
    heatMapLayerSwitch: false, // 热力图层开关
    evtLayerSwitch: true, // 事件图层开关
    evtFilterSwitch: false, // 事件过滤开关
    // 事件筛选参数
    eventFilterParams: {
      // “是否重点关注”：Y-是，N-否,默认Y
      isFocus: 'Y',
    },
    mapStoreRef: null, // 地图容器
    mapModeId: 5, // 地图类型Id , 目前 5-深色地图，1-卫星地图，6-常规地图
  },
  mutations: {
    setMapModeId(state, item) { // 地图类型Id
      state.mapModeId = item;
    },
    setMapType(state, item) { // 设置地图类型
      state.mapType = item;
    },
    setPickLnglat(state, item) { // 设置选取的经纬度
      state.pickLnglat = item;
    },
    setLocationLnglat(state, item) { // 设置定位的经纬度
      state.locationLnglat = item;
    },
    setCheckMode(state, item) { // 设置检查模式开关
      state.checkMode = item;
    },
    setPassRouteSwitch(state, item) { // 设置路径通过开关
      state.passRouteSwitch = item;
    },
    setMapToolSwitch(state, item) { // 设置地图工具开关
      state.mapToolSwitch = item;
    },
    setSelectionRange(state, item) { // 设置选中的区域
      state.selectedRegion = item;
    },
    setEvtLayerSwitch(state, item) { // 设置事件图层开关
      state.evtLayerSwitch = item;
    },
    setHeatMapLayerSwitch(state, item) { // 设置热力图层开关
      state.heatMapLayerSwitch = item;
    },
    setEvtFilterSwitch(state, item) { // 设置事件过滤开关
      state.evtFilterSwitch = item;
    },
    setEventFilterParams(state, item) { // 设置事件筛选参数
      state.eventFilterParams = item;
    },
    setMapStoreRef(state, item) { // 设置事件筛选参数
      state.mapStoreRef = item;
    },
  },
  getters: {
    getMapType: (state) => state.mapType, // 获取地图类型
    getPickLnglat: (state) => state.pickLnglat, // 获取选取的经纬度
    getLocationLnglat: (state) => state.locationLnglat, // 获取定位的经纬度
    getCheckMode: (state) => state.checkMode, // 获取检查模式开关状态
    getPassRouteSwitch: (state) => state.passRouteSwitch, // 获取路径通过开关状态
    getMapToolSwitch: (state) => state.mapToolSwitch, // 获取地图工具开关状态
    formatSelectedRegion: (state) => { // 格式化选中的区域
      if (!state.selectedRegion) {
        return {};
      }
      const {
        regionCode,
        regionLevel
      } = state.selectedRegion;
      return {
        countyId: ConstEnum.REGION_LEVEL.district === regionLevel
          ? regionCode
          : '',
        cityId: ConstEnum.REGION_LEVEL.city === regionLevel
          ? regionCode
          : '',
        provinceId: ConstEnum.REGION_LEVEL.province === regionLevel
          ? regionCode
          : ''
      };
    },
    evtLayerSwitch: (state) => state.evtLayerSwitch, // 获取事件图层开关状态
    heatMapLayerSwitch: (state) => state.heatMapLayerSwitch, // 获取热力图层开关状态
    evtFilterSwitch: (state) => state.evtFilterSwitch, // 获取事件过滤开关状态
    getEventFilterParams: (state) => state.eventFilterParams, // 获取事件筛选参数
  },
};
