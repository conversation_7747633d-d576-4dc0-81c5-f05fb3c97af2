/*
 * @Description: mock数据存储
 */
import LoudSpeakerLayerData from "@/components/page/ScreenPages/demoIntegratedMonitor/mockData/LoudSpeakerLayerData";
import { enums } from '@/components/page/ScreenPages/IntegratedMonitor/DetailInfo/enums'
export default {
  namespaced: true,
  state: {
    // 全量mock事件数据
    allEventData: [],
    // 当前加载的事件数据（地图展示全量，列表组件内部进行分页展示）
    curLoadedEventData: [],
    // 当前加入的最新一条数据，弹出提示框用
    curAddEventData: null,
    // 当前收藏的事件数据（页面上操作收藏或取消收藏）
    curCollectEventData: [],
    // 当前加载的事件数据在全量中的序号
    curLoadedIndex: 0,
    // 是否全部加载完成
    isLoadedComplete: false,
    // 定时刷新任务
    timer: null,
    // 广播设备
    allLoudSpeakerData: [...LoudSpeakerLayerData.data],
    // 正在广播中的云广播设备
    curPlayingLoudSpeakerData: [],
  },
  mutations: {
    // 初始化加载前 10 条数据
    initializeCurLoadedEventData(state) {
      // 从全量事件数据中获取最后10条数据作为当前加载的数据
      state.curLoadedEventData = state.allEventData.slice(-10);
      // 清空当前收藏的事件数据
      state.curCollectEventData = [];
      // 设置当前加载的事件数据在全量中的序号
      state.curLoadedIndex = state.allEventData.length - 11;
      // 标记未全部加载完成
      state.isLoadedComplete = false;
    },
    // 加载下一条数据
    loadNextEventData(state) {
      if (state.curLoadedIndex >= 0) {
        // 从全量事件数据中获取当前序号的数据并插入到当前加载的数据头部
        state.curAddEventData = state.allEventData[state.curLoadedIndex];
        state.curLoadedEventData.unshift(state.allEventData[state.curLoadedIndex]);
        // 更新当前加载的事件数据在全量中的序号
        state.curLoadedIndex--;
      } else {
        // 如果没有更多数据可加载，标记为全部加载完成
        state.isLoadedComplete = true;
      }
    },
    // 结束定时加载任务
    clearLoadEventDataTimer(state) {
      if (state.timer) {
        // 清除定时器
        clearInterval(state.timer);
        state.timer = null;
      }
    },
    // 设置全量事件数据
    setAllEventData(state, item) {
      state.allEventData = item;
    },
    // 设置当前加载的事件数据
    setCurLoadedEventData(state, item) {
      state.curLoadedEventData = item;
    },
    // 设置当前加载的事件数据在全量中的序号
    setCurLoadedIndex(state, item) {
      state.curLoadedIndex = item;
    },
    // 设置当前收藏的事件数据
    setCurCollectEventData(state, item) {
      state.curCollectEventData = item;
    },
    // 设置当前正在播放的广播设备数据
    setCurPlayingLoudSpeakerData(state, item) {
      console.log('setCurPlayingLoudSpeakerData', item);
      // 获取设备编码列表
      const deviceCodes = item.map(i => i.deviceCode);
      // 更新所有广播设备的数据状态
      const newAllData = state.allLoudSpeakerData.map(i => {
        return {
          ...i,
          deviceStatus: deviceCodes.includes(i.deviceCode) ? enums.LOUDSPEAKER.deviceStatus.playing.code : i.initDeviceStatus
        }
      });
      state.allLoudSpeakerData = newAllData;
      state.curPlayingLoudSpeakerData = item;
    },
  },
  getters: {
    // 获取当前加载的事件数据
    getCurLoadedEventData: state => state.curLoadedEventData,
    // 获取当前加载的事件数据在全量中的序号
    getCurLoadedIndex: state => state.curLoadedIndex,
    // 获取当前收藏的事件数据
    getCurCollectEventData: state => state.curCollectEventData,
    // 获取当前正在播放的广播设备数据
    getCurPlayingLoudSpeakerData: state => state.curPlayingLoudSpeakerData,
    // 获取所有广播设备数据
    getAllLoudSpeakerData: state => state.allLoudSpeakerData,
  },
  actions: {
    // 初始化数据
    initializeCurLoadedEventData({ commit }) {
      commit('initializeCurLoadedEventData');
    },
    // 每 30 秒加载一条数据
    startLoadEventData({ commit, state }) {
      state.timer = setInterval(() => {
        commit('loadNextEventData');
        if (state.isLoadingComplete) {
          commit('clearLoadEventDataTimer');
          setTimeout(() => {
            commit('initializeCurLoadedEventData');
            dispatch('startLoadEventData'); // 重新开始加载
          }, 5 * 60 * 1000); // 停滞 5 分钟
        }
      }, 30 * 1000); // 30 秒
    },
    // 结束加载数据
    endLoadEventData({ commit }) {
      commit('clearLoadEventDataTimer');
    }
  }
};
