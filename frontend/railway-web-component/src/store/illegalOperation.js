/*
 * @Description: 模块的描述信息
 * @Autor: he.chao
 * @Date: 2022-11-10 14:26:46
 * @LastEditors: he.chao
 * @LastEditTime: 2022-12-07 15:09:45
 */
export default {
    namespaced: true, // 启用命名空间，使得模块可以在全局 store 中使用模块名访问
    state: {
        curEvent: {}, // 当前事件对象，初始为空对象
        map: null, // 地图对象，初始为 null
    },
    mutations: {
        // 设置当前事件
        setCurEvent(state, item) {
            // 如果当前事件与传入的事件相同，则重置为空对象，否则设置为传入的事件
            state.curEvent = state.curEvent == item ? {} : item
        },
        // 设置地图对象
        setMap(state, item) {
            // 将地图对象设置为传入的 item
            state.map = item
        }
    },
    getters: {
        // 获取当前事件
        getCurEvent: state => state.curEvent, // 返回当前事件对象
    }
};
