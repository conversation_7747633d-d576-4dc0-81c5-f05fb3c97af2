// 导入axios库，用于发起HTTP请求
import axios from 'axios';

// 创建一个axios实例，设置超时时间为5000毫秒
const service = axios.create({
    timeout: 5000 // 设置请求超时时间
});

// 配置请求拦截器，用于在请求发送前进行统一处理
service.interceptors.request.use(
    config => {
        // 在这里可以添加一些公共的请求头或者其他必要的配置
        return config; // 返回处理后的配置对象
    },
    error => {
        console.log(error); // 打印请求错误信息
        return Promise.reject(); // 返回一个拒绝的Promise对象
    }
);

// 配置响应拦截器，用于在请求返回后进行统一处理
service.interceptors.response.use(
    response => {
        // 检查响应状态码是否为200，表示请求成功
        if (response.status === 200) {
            // 返回响应数据
            return response.data;
        } else {
            Promise.reject(); // 状态码不为200时，返回一个拒绝的Promise对象
        }
    },
    error => {
        console.log(error); // 打印响应错误信息
        return Promise.reject(); // 返回一个拒绝的Promise对象
    }
);

// 导出创建的axios实例
export default service;
