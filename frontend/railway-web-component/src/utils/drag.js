/*
 * @Description:
 * @Autor: he.chao
 * @Date: 2023-01-04 15:01:30
 * @LastEditors: liu.yongli
 * @LastEditTime: 2024-06-17 15:23:41
 */
import Vue from 'vue'

Vue.directive('drag', {
    //1.指令绑定到元素上回立刻执行bind函数，只执行一次
    //2.每个函数中第一个参数永远是el，表示绑定指令的元素，el参数是原生js对象
    //3.通过el.focus()是无法获取焦点的，因为只有插入DOM后才生效
    bind: function (el) {
        console.log('bind')
    },
    //inserted表示一个元素，插入到DOM中会执行inserted函数，只触发一次
    inserted: function (el) {
        el.onmousedown = function (e) {
            var disx = e.pageX - el.offsetLeft;
            var disy = e.pageY - el.offsetTop;
            document.onmousemove = function (e2) {
                el.style.left = e2.pageX - disx + 'px';
                el.style.top = e2.pageY - disy + 'px';
            }
            document.onmouseup = function () {
                document.onmousemove = document.onmouseup = null;
            }
        }
    },
    //当VNode更新的时候会执行updated，可以触发多次
    updated: function (el) {
        console.log('updated')
    }
})
