/*
 * @Description: 默认导出一个包含数据、方法和挂载钩子的对象，用于处理元素的缩放
 * @Autor: he.chao
 * @Date: 2022-11-03 14:06:02
 * @LastEditors: he.chao
 * @LastEditTime: 2022-11-03 15:11:52
 */
export default {
    /* 定义组件的数据属性，其中style用于控制元素的样式 */
    data() {
        return {
            style: {
                width: "1920",
                height: "960",
                transform: "scaleY(1) scaleX(1)",
            },
        }
    },
    /* 定义组件的方法，用于计算和设置元素的缩放比例 */
    methods: {
        /* 获取当前容器的缩放比例 */
        getScale() {
            /* 计算容器宽度与预设宽度的比例 */
            const w = this.$refs.container.parentElement.offsetWidth / this.style.width;
            /* 计算容器高度与预设高度的比例 */
            const h = this.$refs.container.parentElement.offsetHeight / this.style.height;
            /* 返回宽高缩放比例的对象 */
            return {x: w, y: h};
        },
        /* 设置元素的缩放样式 */
        setScale() {
            /* 调用getScale方法获取缩放比例 */
            let scale = this.getScale();
            /* 根据获取的缩放比例设置元素的transform属性 */
            this.style.transform =
                "scaleY(" + scale.y + ") scaleX(" + scale.x + ")";
        },
    },
    /* 在组件挂载到DOM后执行的钩子函数 */
    mounted() {
        /* 创建一个指向当前实例的变量，用于在window.onresize事件中调用方法 */
        let that = this;
        /* 初始设置元素的缩放样式 */
        that.setScale();
        /* 监听窗口大小改变事件，动态调整元素的缩放样式 */
        /* 窗口改变事件 */
        window.onresize = () => {
            that.setScale();
        };
    },
}
