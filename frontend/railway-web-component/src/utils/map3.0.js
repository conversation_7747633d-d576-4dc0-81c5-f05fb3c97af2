/*
 * @Description:
 * @Author: liu.yongli
 * @Date: 2024-02-21 11:27:56
 * @LastEditTime: 2025-02-27 17:43:47
 * @LastEditors: liu.yongli
 */
/*
 * @Description  :
 * <AUTHOR> wnj
 * @Date         : 2023-08-29 10:20:42
 * @LastEditors: liu.yongli
 * @LastEditTime: 2025-02-21 14:05:18
 * @FilePath     :  / src / utils / map2.0.js
 */
import {
  Events
} from '@/components/page/ScreenPages/IntegratedMonitor/enum/EventsEnum';
// setUserLocalStorage
import { getUserLocalStorage, } from '@/components/common/utils';
import { CfgEnum } from '@/components/page/ScreenPages/IntegratedMonitor/enum/CfgEnum';
import vue from '@/main';
import CTMapOl from '@ct/ct_map_ol';
import { Feature } from 'ol';
import Mask from 'ol-ext/filter/Mask';
import { Circle, LinearRing, Polygon } from 'ol/geom';
import { transformPointToMercator } from '@/utils'
// import { fromLonLat } from 'ol/proj';

const { MapEvent } = Events;
// 聚合图层聚合视距 单位px
const clusterDistance = 30;
/**
 * 根据给定的源类型创建并返回相应的地图源对象。
 * @param {string} sourceType - 地图源的类型，例如 'TileWMS' 或 'XYZ'。
 * @param {Object} opt - 创建地图源时所需的配置选项。
 * @returns {Object} 根据 sourceType 返回相应的地图源对象，如果 sourceType 不匹配则返回一个空对象。
 */
const getSource = (sourceType, opt) => {
  // 如果源类型是 'TileWMS'，则创建并返回一个 TileWMS 地图源对象。
  if (sourceType === 'TileWMS') {
    return new CTMapOl.source.TileWMS(opt);
  }
  // 如果源类型是 'XYZ'，则创建并返回一个 XYZ 地图源对象。
  if (sourceType === 'XYZ') {
    return new CTMapOl.source.XYZ(opt);
  }
  // 如果源类型不匹配任何已知类型，则返回一个空对象。
  return {};
};

/**
 * 基础地图
 * @param {*} target
 * @param {*} option
 * @returns
 */
const createCTMap = (target, option) => {
  // 创建并返回一个CTMapOl.Map对象
  return new CTMapOl.Map({
    // 设置地图的视图属性
    view: new CTMapOl.View({
      // 使用EPSG:4326投影，这是一种地理坐标系，基于WGS84
      projection: 'EPSG:4326',
      // 设置地图中心点，如果option.center存在则使用option.center，否则使用默认值[116.38813, 39.89446]，即北京的经纬度
      center: option.center || [116.38813, 39.89446],
      // 设置地图的初始缩放级别，如果option.zoom存在则使用option.zoom，否则使用默认值12
      zoom: option.zoom || 12,
      // 设置地图的最大缩放级别，如果option.maxZoom存在则使用option.maxZoom，否则使用默认值17
      maxZoom: option.maxZoom || 17,
      // 设置地图的最小缩放级别，如果option.minZoom存在则使用option.minZoom，否则使用默认值1
      minZoom: option.minZoom || 1
    }),
    // 设置地图的控件，默认情况下禁用地图的归属信息、旋转按钮和缩放控件
    controls: CTMapOl.control.defaults({
      attribution: false, // 禁用归属信息控件
      rotate: false,      // 禁用旋转控件
      zoom: false         // 禁用缩放控件
    }),
    // 设置地图容器的目标DOM元素
    target
  });
}

/**
 * 绑定地图切换事件
 * @param {*} param0
 */
const bindChangeLayersHj = ({ map, satelliteLayers, tileLayer2, darkTileLayer, railRoute, darkRailRoute }) => {
  // 定义一个函数 changeLayersHj 在 map 对象上，该函数用于根据类型切换地图图层
  map.changeLayersHj = (type, type2) => {
    // 从本地存储获取用户配置信息
    const usrCachedCfg = getUserLocalStorage(CfgEnum.STORAGE_KEY.SIDEBAR_KEY);
    // 如果类型为 'vector'，则隐藏所有卫星图层
    if (type === 'vector') {
      satelliteLayers.forEach(layer => {
        layer.setVisible(false); // 遍历卫星图层并设置其可见性为 false
      });
      // 如果 type2 为 2，则显示黑色底图层并隐藏普通底图层
      if (type2 == 2) {
        tileLayer2.setVisible(false); // 隐藏普通底图层
        darkTileLayer.setVisible(true); // 显示黑色底图层
        usrCachedCfg.RAILWAY_LINE && darkRailRoute.setVisible(true); // 如果用户配置中包含铁路线，显示黑色铁路线图层
        railRoute.setVisible(false); // 隐藏普通铁路线图层
      } else {
        darkTileLayer.setVisible(false); // 隐藏黑色底图层
        tileLayer2.setVisible(true); // 显示普通底图层
        darkRailRoute.setVisible(false); // 隐藏黑色铁路线图层
        usrCachedCfg.RAILWAY_LINE && railRoute.setVisible(true); // 如果用户配置中包含铁路线，显示普通铁路线图层
      }
      return; // 结束函数执行
    }
    // 如果类型为 'satellite'，则显示所有卫星图层，并隐藏其他图层
    if (type === 'satellite') {
      satelliteLayers.forEach(layer => {
        layer.setVisible(true); // 遍历卫星图层并设置其可见性为 true
      });
      tileLayer2.setVisible(false); // 隐藏普通底图层
      usrCachedCfg.RAILWAY_LINE && darkRailRoute.setVisible(true); // 如果用户配置中包含铁路线，显示黑色铁路线图层
      railRoute.setVisible(false); // 隐藏普通铁路线图层
      return; // 结束函数执行
    }
  };
}

const bindRailwayRouteLayer = ({ map, darkRailRoute, railRoute }) => {
  // 定义一个名为 railwayRouteLayer 的方法在 map 对象上
  map.railwayRouteLayer = (show, modeId) => {
    // 如果 show 为真，则根据条件显示相应的铁路路线图层
    if (show) {
      // 如果深色底图层或卫星图层可见，则显示深色铁路路线图层；否则显示普通铁路路线图层
      if (modeId === 6) {
        railRoute.visable()
        darkRailRoute.invisable()
      } else {
        railRoute.invisable()
        darkRailRoute.visable()
      }
    } else {
      // 如果 show 为假，隐藏相应的铁路路线图层
      railRoute.invisable()
      darkRailRoute.invisable()
    }
  };
}

// 导出一个名为 createPlatMap 的函数，用于创建地图实例
export const createPlatMap = (target, option, layerColor, layerType) => {
  // 从 localStorage 中获取 gisUrl
  let gisUrl = localStorage.getItem('gisUrl');
  // 打印 gisUrl 到控制台，使用特定的样式
  console.log('%c █░░░░░░░░░░░░█ ,注释-gisUrl', 'color: #FAC800', gisUrl);
  // 如果 gisUrl 不存在，则生成一个新的 URL 并存储到 localStorage 中
  if (!gisUrl) {
    // 根据当前窗口的 origin 和 port 生成新的 API URL
    let newUrl = window.location.origin.split(`:${window.location.port}`)[0] +
      '/api';
    // 打印新生成的 URL 到控制台，使用特定的样式
    console.log('%c █░░░░░░░░░░░░█ ,注释-newUrl', 'color: #FAC800', newUrl);
    localStorage.setItem('gisUrl', newUrl);
  }

  // 创建一个新的地图实例，使用 CTMapOl 库的 InitMap 类
  const map = new CTMapOl.extend.InitMap({
    domId: target, // 地图容器的 DOM ID
    tile: 'vector', // 地图瓦片类型为矢量
    center: option.center || [104.076452, 30.651696], // 地图中心点坐标，如果 option 中未提供则使用默认值
    zoom: option.zoom || 6.8, // 地图初始缩放级别，如果 option 中未提供则使用默认值
    maxZoom: option.maxZoom || 18, // 地图最大缩放级别，如果 option 中未提供则使用默认值
    minZoom: option.minZoom || 1 // 地图最小缩放级别，如果 option 中未提供则使用默认值
  });
  // 设置地图视图，包括投影方式、中心点坐标、缩放级别等
  map.map.setView(
    new CTMapOl.View({
      projection: 'EPSG:4326', // 使用 EPSG:4326 投影
      center: option.center || [104.076452, 30.651696], // 设置中心点坐标
      zoom: option.zoom || 6.8, // 设置初始缩放级别
      maxZoom: option.maxZoom || 18, // 设置最大缩放级别
      minZoom: option.minZoom || 1 // 设置最小缩放级别
    })
  );
  // 将创建的地图实例存储在全局变量 window.consoleMapIns 中，便于其他地方访问
  window.consoleMapIns = map;
  // 返回地图实例的 map 属性
  return map.map;
};

// 导出一个名为 mouseAreaTool 的函数，用于在地图上添加鼠标区域测量工具
export const mouseAreaTool = (map) => {
  // 创建一个新的测量工具实例，并将其附加到全局window对象中
  return new CTMapOl.extend.ToolMeasure(map, {
    color: '#80d8ff', // 设置测量工具的线条颜色为指定的蓝色
    type: 'area' // 设置测量类型为'area'，即面积测量
  });
};

/**
 * 3.0地图 api，根据给定点和半径创建一个圆对象
 * @param {*} map 
 * @param {*} point 
 * @param {*} radius 
 * @param {*} opt 
 * @returns 
 */
export const drawCircle30 = (map, point, radius, opt = {}) => {
  // 根据给定点和半径创建一个圆对象
  // 3.0地图 api
  const buffer = new CTMapOl.extend.ToolBuffer(map, {
    rightClickAction: '', // 全局右键功能，可选 'cancel'（撤销），'end'（结束），每个图形可单独配置
    // onPointerMove: info => {
    //   console.log('鼠标移动', info);
    // },
    // onAddPoint: info => {
    //   console.log('增加一个点', info);
    //   const limit = false // 启用 onAddPoint 回调时，会自动禁用默认缓冲限制，不然可能有冲突
    //   if (info.length > 1000 || info.area > 1000000) {
    //     // this.buffer.undo() // 超出撤销
    //   }
    // }
  }, result => {
    // result.origin 为原始图形geojson
    // result.buffer 缓冲区geojson
    // result.center 缓冲区覆盖圆 中心点
    // result.radius 缓冲区覆盖圆 半径
    console.log(result);
  }, _ => {
    console.error('范围超出限制');
  });
  // 提取opt对象中的dashStock属性，默认为true
  const { dashStock = true } = opt;
  // 定义圆的边框样式
  const stock = {
    color: opt.strokeColor || 'rgba(21,91,212,1)', // 边框颜色，默认为蓝色
    width: opt.strokeWidth || 2 // 边框宽度，默认为2
  };
  // 如果dashStock为true，则设置虚线样式
  if (dashStock) {
    stock.lineDash = opt.lineDash || [10, 8]; // 虚线模式，默认为[10, 8]
    stock.lineCap = opt.lineCap || 'square'; // 线段末端样式，默认为'square'
  }
  // 定义圆的样式参数，包括填充颜色、边框样式和图层的z-index
  let param = {
    fill: new CTMapOl.style.Fill({
      color: opt.fillColor || 'rgba(21,91,212,0.1)' // 填充颜色，默认为透明蓝色
    }),
    stroke: new CTMapOl.style.Stroke(stock), // 边框样式
    zIndex: 2 // 图层的z-index，用于控制图层的叠加顺序
  };
  // 3.0 方法
  buffer.loadFromData({
    geometry: new CTMapOl.geom.Point(point).transform('EPSG:4326', 'EPSG:3857'),
    geoJson: null, // 也可以加载geoJson
    radius: radius / 1000, // 半径是默认是千米
    originStyle: new CTMapOl.style.Style({
      stroke: new CTMapOl.style.Stroke({ width: 0, color: '#00ffe9' }),
      fill: new CTMapOl.style.Fill({ color: 'rgba(0,255,233,0.3)' })
    }),
    bufferStyle: new CTMapOl.style.Style(param)
  });
  return buffer
};

/**
 * 绘制指定半径的圆
 * @param {*} center 中心点
 * @param {*} radius 半径
 * @returns 
 */
export const isExistCircle = (center, radius) => {
  const circle = new Circle(transformPointToMercator(center), radius);
  return circle
}

/**
 * 2.0地图 api，根据给定点和半径创建一个圆对象
 * 通用组件指点飞行绘制的圆与2.0的方法绘制的相同半径的圆大小有差异
 * @param {*} map 
 * @param {*} point 
 * @param {*} radius 
 * @param {*} opt 
 * @returns 
 */
export const drawCircle = (map, point, radius, opt = {}) => {
  // 根据给定点和半径创建一个圆对象
  // 注意：这里的半径被除以100000，可能是因为输入的半径是以某种特殊单位（如微米）给出的
  // radius / 100000
  // 2.0 api
  // const center = fromLonLat(point); // 圆心坐标（经纬度 -> EPSG:3857）
  const circle = new Circle(transformPointToMercator(point), radius);
  // 提取opt对象中的dashStock属性，默认为true
  const { dashStock = true } = opt;
  // 定义圆的边框样式
  const stock = {
    color: opt.strokeColor || 'rgba(21,91,212,1)', // 边框颜色，默认为蓝色
    width: opt.strokeWidth || 2 // 边框宽度，默认为2
  };
  // 如果dashStock为true，则设置虚线样式
  if (dashStock) {
    stock.lineDash = opt.lineDash || [10, 8]; // 虚线模式，默认为[10, 8]
    stock.lineCap = opt.lineCap || 'square'; // 线段末端样式，默认为'square'
  }
  // 定义圆的样式参数，包括填充颜色、边框样式和图层的z-index
  let param = {
    fill: new CTMapOl.style.Fill({
      color: opt.fillColor || 'rgba(21,91,212,0.1)' // 填充颜色，默认为透明蓝色
    }),
    stroke: new CTMapOl.style.Stroke(stock), // 边框样式
    zIndex: 2 // 图层的z-index，用于控制图层的叠加顺序
  };

  // 3.0 方法
  // buffer.loadFromData({
  //   geometry: new CTMapOl.geom.Point(point).transform('EPSG:4326', 'EPSG:3857'),
  //   geoJson: null, // 也可以加载geoJson
  //   radius: radius / 1000,
  //   originStyle: new CTMapOl.style.Style({
  //     stroke: new CTMapOl.style.Stroke({ width: 0, color: '#00ffe9' }),
  //     fill: new CTMapOl.style.Fill({ color: 'rgba(0,255,233,0.3)' })
  //   }),
  //   bufferStyle: new CTMapOl.style.Style(param)
  // });

  // 创建一个矢量图层
  const vectorLayer = new CTMapOl.layer.Vector({
    source: new CTMapOl.source.Vector() // 图层的数据源为矢量数据源
  });
  // 创建一个要素对象，包含圆的几何信息
  const feature = new CTMapOl.Feature(circle);
  // 设置要素的样式
  feature.setStyle(new CTMapOl.style.Style(param));
  // 将要素添加到矢量图层的数据源中
  vectorLayer.getSource().addFeatures([feature]);
  // 将矢量图层添加到地图中
  map.addLayer(vectorLayer);
  // 返回创建的矢量图层
  return vectorLayer;
};

/**
 * APIMethod:OpenLayers绘制扇形的接口扩展
 * @param origin 圆心坐标数组，如[longitude, latitude]
 * @param radius 扇形的半径，单位未知
 * @param sides 扇形的边数，即构成扇形的多边形的边数
 * @param r 扇形张开的角度，单位为度
 * @param angel 扇形旋转角度（扇形右边半径与x正向轴的角度），单位为度
 * @returns {OpenLayers.Geometry.Polygon} 返回一个OpenLayers的多边形对象，该对象表示绘制的扇形
 */
const createRegularPolygonCurve = (origin, radius, sides, r, angel) => {
  // 计算旋转后的角度
  let rotation = 360 - r;
  // 计算每个顶点的角度差
  let angle = Math.PI * ((1 / sides) - (1 / 2));
  // 如果存在旋转，则调整初始角度
  if (rotation) {
    angle += (rotation / 180) * Math.PI;
  }
  let rotatedAngle, x, y;
  let points = [];
  // 生成扇形的各个顶点坐标
  for (let i = 0; i < sides; ++i) {
    let an = i * ((360 - rotation) / 360);
    rotatedAngle = angle + (an * 2 * Math.PI / sides);
    // 计算每个点的x和y坐标，并将其添加到points数组中
    x = origin[0] + (radius * Math.cos(rotatedAngle));
    y = origin[1] + (radius * Math.sin(rotatedAngle));
    points.push([x, y]);
  }
  // 如果旋转角度不为零，则将圆心添加为最后一个点，形成闭合多边形
  if (rotation != 0) {
    points.push(origin);
  }
  // 创建一个线性环对象，用于表示多边形的边界
  var ring = new LinearRing(points);
  // 根据给定的旋转角度和圆心坐标旋转该线性环
  ring.rotate(angel / 57.3, origin); // 57.3为将角度转换为弧度的近似值
  // 获取旋转后的线性环的坐标数组
  let list = ring.getCoordinates();

  // 返回一个新的多边形对象，该对象由一个线性环构成
  return new Polygon([list]);
};

/**
 * 获取转换后单位的半径
 * @param {Number} radius 以米为单位的半径的值
 * @param {Object} mapIns 地图实例对象
 * @returns {Number} circleRadius 以地图投影单位为单位的半径的值
 */
let getRadius = (radius, mapIns) => {
  // 获取地图的投影单位每米的长度
  let metersPerUnit = mapIns.getView().getProjection().getMetersPerUnit();
  // 计算转换后单位的半径值
  let circleRadius = radius / metersPerUnit;
  return circleRadius;
};

// 导出一个函数，用于在地图上启用鼠标测量工具
export const mouseLineTool = (_mapRef) => {
  // 2.0 方法双击后不能结束测量
  // return new CTMapOl.extend.ToolMeasure(_mapRef.mapInstance, {
  //   color: '#FF33FF', // 测量工具的颜色设置为粉红色
  //   beginPointStyle: new CTMapOl.style.Style({
  //     image: new CTMapOl.style.Icon({
  //       src: require(
  //         `@/assets/images/map2.0/start.png`), // 设置测量起点的图标图片路径
  //       scale: 0.5, // 设置图标的缩放比例为 0.5
  //       anchor: [0.5, 1] // 设置图标的锚点位置
  //     })
  //   }),
  //   endPointStyle: new CTMapOl.style.Style({
  //     image: new CTMapOl.style.Icon({
  //       src: require(`@/assets/images/map2.0/end.png`), // 设置测量终点的图标图片路径
  //       scale: 0.5, // 设置图标的缩放比例为 0.5
  //       anchor: [0.5, 1] // 设置图标的锚点位置
  //     })
  //   })
  // });

  // 使用 3.0 方法
  const pointOption = {
    strokeColor: "#ffffff",
    strokeOpacity: 1,
    strokeWeight: 2,
    outlineColor: "#FF33FF",
    outlineColorOpacity: 2,
    borderWeight: 2,
  };
  const lineOptions = {
    strokeColor: "#FF33FF",
    strokeOpacity: 1,
    strokeWeight: 2,
  };
  const _toFixed = (num, fixed = 3) => {
    return Math.round(num * Math.pow(10, fixed)) / Math.pow(10, fixed);
  }
  return new CTMapOl.InteractionControl.lib.MeasureDistance(
    { mapRef: _mapRef },
    {
      type: "ground",
      mode: 'default',
      // showTempResult: true,
      map: _mapRef.mapInstance, //地图实例
      rightClickEnd: true,
      rightClickAction: "end",
      pointOption,
      lineOptions,
      infinite: true, // 连续测量，默认true
      infoWindow: {
        anchor: ["50%", 25],
        offset: [0, 0],
        content: (distance) => {
          distance = _toFixed(distance / 1000, 2);
          let str = `<div class="ctm-space-ground-measure"><span class="distance">${distance} </span><span class="unit">千米</span><span class="close">x</span><span class="trash">x</span></div>`;
          return str;
        },
      },
      tip: {
        anchor: ["50%", 25],
        offset: [0, 0],
        // startTip: '单击确定起点',
        // elseTip: "单击继续,右击或者双击结束",
      },
      onFinished: (mapRef, event, coordinates) => {
        console.log("测量结束：", coordinates);
      },
      onProfileFinished: (mapRef, event, profile) => {
        console.log("测量结束：", mapRef, event, profile);
      },
    }
  );
};

export const monitorIcon = (imgIcon, text) => {
  const textFill = new CTMapOl.style.Fill({
    color: '#fefefe'
  });
  const textStroke = new CTMapOl.style.Stroke({
    color: 'rgba(0, 0, 0, 0.6)',
    width: 1
  });
  return new CTMapOl.style.Style({
    image: new CTMapOl.style.Icon({
      anchor: [0.5, 1],
      src: imgIcon || require(`@/assets/images/home/<USER>/shexiangji.svg`)
    }),
    text: new CTMapOl.style.Text({
      offsetY: -48, //位置
      textAlign: 'center', //基准线
      textBaseline: 'middle', // //文字样式
      font: '10px',
      //文本内容 一定要是字符串，否则字符串报错
      text: text + '', //文本填充样式（即文字颜色）
      fill: textFill, //描边颜色，蓝色
      stroke: textStroke
    })
  });
};
const createPointSource = () => {
  return new CTMapOl.source.Vector();
};
const createClusterLayer = ({
  pointsSource,
  map,
  type,
  onclick = () => {
    console.log('createClusterLayer onclick')
  }
}) => {
  const vectorLayer = new CTMapOl.layer.Vector({
    source: new CTMapOl.source.Cluster({
      distance: clusterDistance,
      minDistance: 0,
      source: pointsSource,
      createCluster: (point, features) => {
        return new Feature({
          geometry: point,
          features: features,
          attributes: {
            onclick: (features2, geometry) => {
              onclick({
                features: features2,
                type,
                geometry
              });
            }
          }
        });
      }
    }),
    style: (features) => {
      return clusterStyle(features, map);
    },
    zIndex: type === 'ALARM_EVENT' ? 11 : 10
  });

  return vectorLayer;
};
export const creatHeatMap = ({
  mapInstance,
  heatData
}) => {
  const heatMapLayer = new CTMapOl.layer.Heatmap({
    source: new CTMapOl.source.Vector({
      features: new CTMapOl.format.GeoJSON().readFeatures(heatData)
    }),
    opacity: 1,
    gradient: ['#00f', '#0ff', '#0f0', '#ff0', '#f00'],
    // blur: 40,
    // radius: 20,
    weight: (e) => {
      //根据权重展示热力图！关键点，weight范围为：0-1！！！
      return e.values_.weight;
    }
  });
  mapInstance.addLayer(heatMapLayer);
  return heatMapLayer;
};
const createSource = ({
  markers,
  type,
  onclick = () => {
    console.log('createSource onclick')
  }
}) => {
  let pointsSource = createPointSource();
  pointsSource.addFeatures(markers);
  return new CTMapOl.source.Cluster({
    distance: clusterDistance,
    minDistance: 0,
    source: pointsSource,
    createCluster: (point, features) => {
      return new Feature({
        geometry: point,
        features: features,
        attributes: {
          onclick: (features2, geometry) => {
            onclick({
              features: features2,
              type,
              geometry
            });
          }
        }
      });
    }
  });
};

// 聚合点样式
const styleCluster = (count, map) => {
  if (map.getView().getZoom() === map.getView().getMaxZoom()) {
    return monitorIcon(
      require(`@/assets/images/map2.0/icon_click_cluster_white.svg`), count);
  }
  return new CTMapOl.style.Style({
    image: new CTMapOl.style.Icon({
      anchor: [0.5, 0.65],
      src: require(
        `@/assets/images/map2.0/icon_cluster.svg`)
    })
  });
};
const clusterStyle = (feature, map) => {
  if (feature.get('features') && feature.get('features').length > 1) {
    return styleCluster(feature.get('features').length,
      map);
  }
  if (feature.get('features') && feature.get('features').length === 1) {
    let sel = feature.get('features')[0].get('attributes');
    return sel && feature.get('features')[0].style_;
  }

  return monitorIcon();
};
export const useClusterLayer = ({
  markers = [],
  map,
  onclick = () => {
    console.log('useClusterLayer onclick')
  },
  type
}) => {
  let pointsSource = createPointSource();
  pointsSource.addFeatures(markers);
  let pointsClusterLayer = createClusterLayer({
    pointsSource,
    map,
    onclick,
    type
  });
  pointsClusterLayer.setMinZoom(0);
  pointsClusterLayer.setMaxZoom(18);
  pointsClusterLayer.setVisible(true);
  map.on('moveend', (event) => {
    const zoom = map.getView().getZoom();
    if (zoom < 8) {
      pointsClusterLayer.setVisible(false);
    } else {
      pointsClusterLayer.setVisible(true);
    }
    if (zoom === map.getView().getMaxZoom()) {
      pointsClusterLayer.getSource().setDistance(0);
    } else {
      // 修改聚合点视距聚合，单位px
      pointsClusterLayer.getSource().setDistance(clusterDistance);
    }
    vue.$EventBus.$emit(MapEvent.EVENT_MAP_ZOOM_CHANGE, zoom);
  });
  map.addLayer(pointsClusterLayer);
  return pointsClusterLayer;
};
/**
 * 图片icon
 * @param lnglat
 * @param option
 * @param attributes
 * @returns {CTMapOl.Feature}
 */
const imageMarker = (lnglat, option, attributes = {}) => {
  let imgIcon = option.icon;
  let feature = new CTMapOl.Feature({
    geometry: new CTMapOl.geom.Point(lnglat),
    attributes: attributes
  });
  feature.setStyle(new CTMapOl.style.Style({
    image: new CTMapOl.style.Icon({
      anchorOrigin: option.anchorOrigin || 'top-left',
      anchor: option.anchor || [0.5, 1],// attributes.type == "gaojing" ? [1, 0.6] : (option.anchor || [0.5, 1]),
      anchorXUnits: option.anchorXUnits || 'pixels',
      anchorYUnits: option.anchorYUnits || 'pixels',
      src: imgIcon,
      scale: 1,
      offset: option.offset || [1, 0],
      offsetOrigin: option.offsetOrigin || 'top-left',
      size: option.size || undefined
    }),
    zIndex: 2
  }));
  return feature;
};
/**
 * 根据选项生成标记的样式。
 * @param {Object} option - 标记的配置选项，包括锚点位置、单位、图标源等。
 * @returns {Style} 返回一个OpenLayers的样式对象，用于标记的样式设置。
 */
const getMarkerStyle = (option) => {
  return new CTMapOl.style.Style({
    image: new CTMapOl.style.Icon({
      // anchorOrigin: option.anchorOrigin || "top-left",
      anchor: option.anchor || [0.5, 1],// attributes.type == "gaojing" ? [1, 0.6] : (option.anchor || [0.5, 1]),
      anchorXUnits: option.anchorXUnits || 'pixels',
      anchorYUnits: option.anchorYUnits || 'pixels',
      src: option.icon,
      scale: 1,
      offset: option.offset || [1, 0],
      offsetOrigin: option.offsetOrigin || 'top-left',
      size: option.size || undefined
    }),
    zIndex: 2
  });
};

/**
 * 创建带背景的图像标记。
 * @param {Array} lnglat - 标记的经纬度位置。
 * @param {Object} option - 标记的配置选项，包括图标源等。
 * @param {Object} attributes - 标记的属性信息。
 * @param {Object} bg - 标记的背景配置选项，包括背景图标源等。
 * @returns {Feature} 返回一个带有图像标记和背景的OpenLayers特征对象。
 */
const imageMarkerWidthBg = ({
  lnglat,
  option,
  attributes,
  bg = {}
}) => {
  const feature = imageMarker(lnglat, option, attributes);
  if (bg.bgSrc) {
    option.src = bg.bgSrc;
    const bg2 = imageMarker(lnglat, option, attributes);
    feature.bg = bg2;
  }
  return feature;
};

/**
 * 变更图像标记的位置和样式。
 * @param {Feature} feature - 需要变更的图像标记特征对象。
 * @param {Object} attr - 标记的位置和属性信息。
 * @param {Object} style - 标记的样式配置选项。
 */
export const changeImageMarker = ({
  feature,
  attr = {
    point: [],
    attributes: {}
  },
  style = {}
}) => {
  if (!feature) {
    console.log('changeMarker feature is null');
    return;
  }
  const props = {
    attributes: attr.attributes
  };

  if (attr.point) {
    props.geometry = new CTMapOl.geom.Point(attr.point);
  }
  feature.values_.features[0].values_.attributes = props.attributes;
  // 以下代码在聚合图层是失效
  feature.style_ = new CTMapOl.style.Style({
    image: new CTMapOl.style.Icon({
      ...style, // anchorOrigin: option.anchorOrigin || "top-left",
      anchor: style.anchor || [0.5, 1],// attributes.type == "gaojing" ? [1, 0.6] : (option.anchor || [0.5, 1]),
      anchorXUnits: style.anchorXUnits || 'pixels',
      anchorYUnits: style.anchorYUnits || 'pixels',
      src: style.src,
      scale: 1,
      offset: style.offset || [1, 0],
      offsetOrigin: style.offsetOrigin || 'top-left',
      size: style.size || undefined
    })
  });
};

/**
 * 创建文本标记。
 * @param {Array} lnglat - 标记的经纬度位置。
 * @param {Object} option - 标记的配置选项，包括文本内容、偏移量等。
 * @param {Object} attributes - 标记的属性信息。
 * @returns {Feature} 返回一个带有文本标记的OpenLayers特征对象。
 */
const textMarker = (lnglat, option, attributes) => {
  let feature = new CTMapOl.Feature({
    geometry: new CTMapOl.geom.Point(lnglat),
    attributes: attributes
  });
  feature.setStyle(new CTMapOl.style.Style({
    text: new CTMapOl.style.Text({
      textAlign: 'center',
      offsetY: option.offsetY || -7,
      text: option.text,
      font: '14px 宋体',
      backgroundFill: new CTMapOl.style.Fill({
        color: '#ff0'
      }),
      backgroundStroke: new CTMapOl.style.Stroke({
        color: '#FFF',
        width: 1
      }),
      fill: new CTMapOl.style.Fill({
        color: '#000'
      }),
      stroke: new CTMapOl.style.Stroke({
        color: 'rgba(0, 0, 0, 0.6)',
        width: 1
      })
    })
  }));
  return feature;
};

/**
 * 创建多边形。
 * @param {Array} coords - 多边形的坐标数组。
 * @param {Object} option - 多边形的样式配置选项，包括填充色、边框色等。
 * @returns {Feature} 返回一个OpenLayers的多边形特征对象。
 */
const polygon = (coords, option) => {
  // fillColor: "#03427485",
  // strokeOpacity: 1,
  // fillOpacity: 0.5,
  // strokeColor: "#034274",
  // strokeWeight: 2,
  // strokeStyle: "dashed",
  // strokeDasharray: [5, 5],
  let polygonStyle = new CTMapOl.style.Style({
    // text: new CTMapOl.style.Text({ testAlign: 'center', text: "区域", font: 'bold 20px 微软雅黑', fill: new CTMapOl.style.Fill({ color: 'black' }) }),
    fill: new CTMapOl.style.Fill({ color: option.fillColor || '#0055ff' }),
    stroke: new CTMapOl.style.Stroke({
      color: option.strokeColor || '#ffcc33',
      width: option.strokeWeight || 3,
      style: 'dashed'
    })
  });
  let feature = new CTMapOl.Feature({
    geometry: new CTMapOl.geom.Polygon([coords]),
    attributes: null
  });
  feature.setStyle(polygonStyle);
  return feature;
};

/**
 * 创建折线。
 * @param {Array} coords - 折线的坐标数组。
 * @param {Object} option - 折线的样式配置选项，包括填充色、边框色等。
 * @param {Object} attributes - 折线的属性信息。
 * @returns {Feature} 返回一个OpenLayers的折线特征对象。
 */
const polyline = (coords, option, attributes) => {
  let polygonStyle = new CTMapOl.style.Style({
    fill: new CTMapOl.style.Fill({ color: option.fillColor || '#0055ff' }),
    stroke: new CTMapOl.style.Stroke({
      color: option.strokeColor || '#ffcc33',
      width: option.strokeWeight || 3,
      lineCap: 'square', // Line cap style: butt, round, or square.
      lineDash: option.strokeDasharray || null,
      radius: 0,
      glow: 1
    })
  });
  let feature = new CTMapOl.Feature({
    geometry: new CTMapOl.geom.LineString(coords),
    attributes: attributes
  });
  feature.setStyle(polygonStyle);
  return feature;
};

/**
 * 高亮显示区域。
 * @param {Map} map - 地图对象。
 * @param {Array} list - 区域的坐标列表。
 * @returns {Mask} 返回一个高亮区域的遮罩对象。
 */
const highLightArea = (map, list) => {
  const geojson = {
    'type': 'Feature',
    'geometry': {
      'type': 'MultiPolygon',
      'coordinates': list
    }
  };

  // 将GeoJSON对象转换为OpenLayers中的Feature对象
  const feature = new CTMapOl.format.GeoJSON().readFeatures(geojson)[0];

  // 将Feature的几何体坐标从EPSG:4326转换为EPSG:3857
  // feature.getGeometry().transform('EPSG:4326', 'EPSG:3857');

  // 底图
  const highlight = new Mask({
    feature: feature,
    fill: new CTMapOl.style.Fill({
      color: 'rgba(48, 56, 73, 0.8)'
    })
  });

  const tileLayer0 = map.getLayers().getArray()[0];
  tileLayer0.addFilter(highlight);
  const tileLayer1 = map.getLayers().getArray()[1];
  tileLayer1.exts = {
    highlight
  };
  if (!tileLayer0.getVisible()) {
    tileLayer1.addFilter(highlight);
  }
  // 卫星
  const sateliteLayer = map.getLayers().getArray()[2];
  sateliteLayer.addFilter(highlight);
  const geom = new CTMapOl.format.GeoJSON().readGeometry(geojson.geometry);
  map.getView().fit(geom, {
    // duration: 1000,
    callback: function () {
      map.getView().animate({
        zoom: 9,
        duration: 1000
      });
    }
  });
  // 3.0 方法位移地图 viewer 的中心点和层级
  // CTMapOl.ViewControl.common.fitView({
  //   mapRef: window.mapRef
  // }, {
  //   target: { feature },
  //   duration: 1000,
  // }).catch((error) => {
  //   console.log('上次飞行已取消');
  // });
  return highlight;
};

/**
 * 高亮显示区域。
 * 使用3.0
 * @param {*} mapRef 
 * @param {string} regionCode - 行政区划代码字符串 
 * @param {*} key - key 行政区划几级
 */
export const highLightArea30 = async (mapRef, regionCode, key) => {
  const _map = mapRef.mapInstance
  const options = {
    fitToRegion: true,
    style2D: {
      fillcolor: 'rgba(48, 56, 73, 0.8)',    //'颜色',  
      outline: {
        // 高亮区域的边线颜色和边线宽度
        color: '#409EFF', //'颜色',
        width: 2, //'宽度'
      },
    },
    style3D: {
      color: 'rgba(48, 56, 73, 0.8)',//'颜色',
      opacity: 1,//'透明度',
      outline: {
        //高亮区域的边线颜色和边线宽度
        color: '#409EFF',
        //'颜色',
        width: 2
        //'宽度'
      }
    }//高亮样式
  };
  const maskByRegionCodeObj = new CTMapOl.DataSourceControl.lib.MaskByRegionCode({
    mapRef,
    regionCode,
  }, options);
  await maskByRegionCodeObj.initSync();
  maskByRegionCodeObj.mount();
  // 市以下的地市指定层级
  if (key >= 1) {
    _map.getView().animate({
      zoom: key === 1 ? 9 : 11,
      duration: 1000
    });
  }
  // _map.getView().setZoom(8)
  // setTimeout(() => {
  //   // 缩放层级
  //   _map.getView().animate({
  //     zoom: 9,
  //     duration: 1000
  //   });
  // }, 50);
  return maskByRegionCodeObj
}

/**
 * 移除高亮区域。
 * @param {Map} map - 地图对象。
 * @param {Mask} highlight - 高亮区域的遮罩对象。
 */
const removeHighLightArea = (map, highlight) => {
  try {
    map.getLayers().getArray()[0]?.removeFilter(highlight);
    map.getLayers().getArray()[1]?.removeFilter(highlight);
    map.getLayers().getArray()[2]?.removeFilter(highlight);
  } catch (e) {
    console.log(e);
  }
};

/**
 * 创建信息窗口。
 * @param {Object} config - 信息窗口的配置选项，包括ID和位置等。
 * @returns {Overlay} 返回一个OpenLayers的信息窗口对象。
 */
// const createInfoWindow = ({
//   id,
//   position
// }) => {
//   const weatherInfoWindow = new CTMapOl.Overlay({
//     element: null,
//     autoPan: {
//       margin: 200,
//       animation: {
//         duration: 500
//       }
//     },
//     positioning: position || 'bottom-center',
//     stopEvent: false
//   });
//   window.map.addOverlay(weatherInfoWindow);
//   let pupUpDiv = document.createElement('div');
//   pupUpDiv.id = id;
//   weatherInfoWindow.setElement(pupUpDiv);
//   weatherInfoWindow.close = () => {
//     console.log('weatherInfoWindow.close')
//   };
//   return weatherInfoWindow;
// };

/**
 * 绘制多边形。
 * @param {Map} map - 地图对象。
 * @param {Array} polys - 多边形的坐标数组列表。
 * @param {Object} option - 多边形的样式配置选项。
 * @returns {Layer} 返回绘制的多边形图层。
 */
export const drawPoly = ({
  map,
  polys = [],
  option = {}
}) => {
  // 创建多边形几何对象
  const polygonGeometry = new Polygon(polys);
  // 创建一个包含多边形的特征
  const polygonFeature = new Feature({
    geometry: polygonGeometry // .transform('EPSG:4326', 'EPSG:3857')
  });
  // 创建矢量源
  const vectorSource = new CTMapOl.source.Vector({
    features: [polygonFeature]
  });

  // 创建矢量图层
  const vectorLayer = new CTMapOl.layer.Vector({
    source: vectorSource,
    style: new CTMapOl.style.Style({
      fill: new CTMapOl.style.Fill({
        color: option.fillColor || 'rgba(255, 0, 0, 0.2)'
      }),
      stroke: new CTMapOl.style.Stroke({
        color: option.strokeColor || '#ffcc33',
        width: option.strokeWidth || 2
      })
    })
  });
  vectorLayer.exts = {
    remove: () => {
      map.removeLayer(vectorLayer);
    }
  };
  map.addLayer(vectorLayer);
  return vectorLayer;
};

/**
 * 绘制带有箭头的线路的样式
 * @param {*} feature
 * @param {*} resolution
 * @param {*} polygonStyle
 * @returns
 */
const arrowLineStyles = (feature, resolution, polygonStyle) => {
  let styles = [];
  styles.push(polygonStyle);
  let geometry = feature.getGeometry();
  // 获取线段长度
  const length = geometry.getLength();
  // 箭头间隔距离（像素）
  const step = 50;
  // 将间隔像素距离转换成地图的真实距离
  const StepLength = step * resolution;
  // 得到一共需要绘制多少个 箭头
  const arrowNum = Math.floor(length / StepLength);
  const rotations = [];
  const distances = [0];
  geometry.forEachSegment(function (start, end) {
    let dx = end[0] - start[0];
    let dy = end[1] - start[1];
    let rotation = Math.atan2(dy, dx);
    distances.unshift(Math.sqrt(dx ** 2 + dy ** 2) + distances[0]);
    rotations.push(rotation);
  });
  // 利用之前计算得到的线段矢量信息，生成对应的点样式塞入默认样式中
  // 从而绘制内部箭头
  for (let i = 1; i < arrowNum; ++i) {
    const arrowCoord = geometry.getCoordinateAt(i / arrowNum);
    const d = i * StepLength;
    const grid = distances.findIndex((x) => x <= d);

    styles.push(
      new CTMapOl.style.Style({
        geometry: new CTMapOl.geom.Point(arrowCoord),
        image: new CTMapOl.style.Icon({
          src: require(`@/assets/images/map2.0/lineArrow.png`),
          opacity: 1,
          anchor: [0.5, 0.5],
          rotateWithView: false,
          // 读取 rotations 中计算存放的方向信息
          rotation: -rotations[distances.length - grid - 1],
          scale: 1,
        }),
      })
    );
  }
  return styles;
};

/**
 * 获取带阴影的线样式
 * @param {线的宽度} lineWidth
 * @param {线的填充颜色} strokeStyle
 * @param {阴影颜色} shadowColor
 * @param {阴影模糊半径} shadowBlur
 * @param {阴影X方向偏移} shadowOffsetX
 * @param {阴影Y方向偏移} shadowOffsetY
 * @returns
 */
const getShadowLineStyle = ({ lineWidth, strokeStyle, shadowColor, shadowBlur, shadowOffsetX, shadowOffsetY }) => {
  return new CTMapOl.style.Style({
    renderer: (coords, state) => {
      const ctx = state.context;
      ctx.save();

      // 设置阴影参数
      ctx.shadowColor = shadowColor || 'rgba(255,191,0,1)';
      ctx.shadowBlur = shadowBlur || 20;
      ctx.shadowOffsetX = shadowOffsetX || 0;
      ctx.shadowOffsetY = shadowOffsetY || 1;

      ctx.strokeStyle = strokeStyle || '#D5FF00';
      ctx.lineWidth = lineWidth || 10;
      // 绘制线
      ctx.beginPath();
      ctx.moveTo(coords[0][0], coords[0][1]);
      for (let i = 1; i < coords.length; i++) {
        ctx.lineTo(coords[i][0], coords[i][1]);
      }
      ctx.stroke();
      // 再叠一层线，解决线路本身被阴影侵占问题
      // // 设置阴影参数
      // ctx.shadowColor = 'transparent';
      // ctx.strokeStyle = strokeStyle || '#D5FF00';
      // ctx.lineWidth = (lineWidth || 10) - 4;
      // // 绘制线
      // ctx.beginPath();
      // ctx.moveTo(coords[0][0], coords[0][1]);
      // for (let i = 1; i < coords.length; i++) {
      //     ctx.lineTo(coords[i][0], coords[i][1]);
      // }
      ctx.stroke();


      ctx.restore();
    }
  });
}

// 创建工作台地图 (默认白色、不要线路)
const createWorkBenchMap = (target, option, layerType) => {
  const {
    MAP_CFG
  } = window.hjCfg;
  const map = createCTMap(target, option);
  const {
    tiles: {
      defTile,
      satelliteTile,
      railwayLayer
    }
  } = MAP_CFG;

  // 亮色地底图
  const tileLayer2 = new CTMapOl.layer.Tile({
    extent: [
      73.**************,
      3.833843469619751,
      135.08738708496094,
      53.55849838256836
    ],
    source: getSource(defTile.sourceType, defTile.sourceOpt)
  });

  // 亮色系和卫星图适配的铁路图层
  const railRoute = new CTMapOl.layer.Tile({
    extent: [
      73.**************,
      3.833843469619751,
      135.08738708496094,
      53.55849838256836
    ],
    source: getSource(railwayLayer.sourceType, railwayLayer.sourceOpt),
    zIndex: 8
  });
  const satelliteLayers = [];
  // 卫星底图
  if (window.tenantMapConfig && window.tenantMapConfig.satellite_2D && window.tenantMapConfig.satellite_2D.length > 0) {
    // 如果有线上配置
    window.tenantMapConfig.satellite_2D.forEach((item, index) => {
      satelliteLayers.push(new CTMapOl.layer.Tile({
        title: `卫星底图${index}`,
        source: getSource('XYZ', item),
        visible: false
      }));
    });
  } else {
    satelliteLayers.push(new CTMapOl.layer.Tile({
      title: '卫星底图',
      source: getSource(satelliteTile[0].sourceType, satelliteTile[0].sourceOpt),
      visible: false
    }));
    satelliteLayers.push(new CTMapOl.layer.Tile({
      title: '卫星底图标注',
      source: getSource(satelliteTile[1].sourceType, satelliteTile[1].sourceOpt),
      visible: false
    }));
  }
  satelliteLayers.forEach(layer => {
    layer.setVisible(false);
  });

  map.setLayers([
    tileLayer2, ...satelliteLayers
  ]);
  map.mapToCenter = (point) => {
    map.getView().animate({
      center: point,
      zoom: 12,
      duration: 2000
    });
  };
  return map;
};
/**
 * 绑定全景展示地图切换事件
 */
const bindFullViewMapChangeLayer = ({ map, satelliteLayers, tileLayer2, darkTileLayer, darkRailRoute, railRoute }) => {
  // 切换地图类型 type:vector/satellite  type2:1/2/0  0：白色  2:表示黑色
  map.changeLayersHj = (type, type2) => {
    if (type === 'vector') {
      satelliteLayers.forEach(layer => {
        layer.setVisible(false);
      });
      if (type2 == 2) {
        tileLayer2.setVisible(false);
        darkTileLayer.setVisible(true);
        darkRailRoute.setVisible(true);
        railRoute.setVisible(false);
      } else {
        darkTileLayer.setVisible(false);
        tileLayer2.setVisible(true);
        darkRailRoute.setVisible(false);
        railRoute.setVisible(true);
      }

      return;
    }
    if (type === 'satellite') {
      satelliteLayers.forEach(layer => {
        layer.setVisible(true);
      });
      tileLayer2.setVisible(false);
      darkRailRoute.setVisible(true);
      railRoute.setVisible(false);
      return;
    }
  };
}

/**
 * 转到中心点
 * @param {*} option 
 */
const setZoomAndCenter = (mapRef, option) => {
  CTMapOl.ViewControl.common.setZoomAndCenter({
    mapRef,
  }, {
    center: option.point,// transformPointToMercator(point),
    zoom: option.zoom || 12,
    duration: option.duration || 2000,
    // offset: [100, 100]
  });
}

/**
 * 综合监测大屏地图
 * @param {*} mapRef 
 * @param {*} option 
 * @param {*} layerType 
 * @returns 
 */
const createMap = (mapRef, option, layerType = {}) => {
  const mapInstance = mapRef.mapInstance
  // 从全局配置中获取地图配置
  const {
    MAP_CFG
  } = window.hjCfg;
  // 创建地图实例
  const map = mapInstance // createCTMap(target, option);
  if (mapRef.mapType === '2D') {
    map.getView().setMaxZoom(option.maxZoom)
    map.getView().setMinZoom(option.minZoom)
    // setTimeout(() => {
    option.center && map.getView().animate({
      center: transformPointToMercator(option.center),
      zoom: 12,
      duration: 2000
    });
    // }, 100);
  }

  // 解构出地图配置中的图层配置信息
  const {
    tiles: {
      // defTile,
      // satelliteTile,
      railwayLayer
    }
  } = MAP_CFG;
  const options = {
    extent: [
      73.**************,
      3.833843469619751,
      135.08738708496094,
      53.55849838256836
    ],
    zIndex: mapRef.mapType === '2D' ? -1 : 1,
    tiled: true,
    // gutter: 20,
    maxZoom: option.maxZoom,
    minZoom: option.minZoom,
  }
  // 创建亮色系的铁路图层
  const _params1 = {
    ...railwayLayer.sourceOpt.params
  }
  delete _params1.LAYERSDARK
  const railRoute = new CTMapOl.LayerControl.lib.WMSLayer({
    mapRef,
    url: railwayLayer.sourceOpt.url, // vue.$env.VUE_APP_RAILWAY_LINE_URL,
    // 在3D地形图上图层错乱
    params: _params1
  }, options)

  // 创建暗色系的火车线路图层
  const darkRailWayConfig = JSON.parse(JSON.stringify(railwayLayer.sourceOpt))
  darkRailWayConfig.params.LAYERS = darkRailWayConfig.params.LAYERSDARK //  railwayLayer.sourceOpt.params.LAYERS + '-dark'

  const darkRailRoute = new CTMapOl.LayerControl.lib.WMSLayer({
    mapRef,
    url: railwayLayer.sourceOpt.url, // vue.$env.VUE_APP_RAILWAY_LINE_URL,
    // projection: 'EPSG:3857', // 使用 EPSG:4326 投影
    // 在3D地形图上图层错乱
    params: darkRailWayConfig.params
    // params: {
    //   FORMAT: "image/png",
    //   VERSION: "1.1.1",
    //   LAYERS: "railway_space:railway-rail-single-dark",
    //   STYLES: "",
    // },
  }, options)

  setTimeout(() => {
    // 隐藏黑色火车线路图层
    if (window.userInfo) {
      const usrCachedCfgOld = getUserLocalStorage(CfgEnum.STORAGE_KEY.SIDEBAR_KEY);
      if (!usrCachedCfgOld.RAILWAY_LINE) {
        darkRailRoute.invisable()
      }
    }
  }, 1500)

  darkRailRoute.init()
  darkRailRoute.mount()
  darkRailRoute.visable()

  railRoute.init()
  railRoute.mount()
  railRoute.invisable() // 先隐藏，切换到亮色常规地图才展示

  // 对火车线路图层操作显隐
  bindRailwayRouteLayer({
    map,
    darkRailRoute,
    railRoute
  });

  map.mapToCenter = (point) => {
    setZoomAndCenter(mapRef, { point })
  };
  return map;
};

/**
 * 创建全景展示地图（与综合监测的区别是不需要控制线路的显示隐藏）
 * @param {*} mapRef 
 * @param {*} option 
 * @param {*} layerType 
 */
const createFullViewMap = (mapRef, option, layerType) => {
  const mapInstance = mapRef.mapInstance
  const {
    MAP_CFG
  } = window.hjCfg;
  const map = mapInstance // createCTMap(target, option);
  map.getView().setMaxZoom(option.maxZoom)
  map.getView().setMinZoom(option.minZoom)
  const {
    tiles: {
      // defTile,
      // satelliteTile,
      railwayLayer
    }
  } = MAP_CFG;
  const options = {
    extent: [
      73.**************,
      3.833843469619751,
      135.08738708496094,
      53.55849838256836
    ],
    zIndex: -1,
    tiled: true,
    // gutter: 20,
    maxZoom: option.maxZoom,
    minZoom: option.minZoom,
  }

  // 创建亮色系的铁路图层
  const _params1 = {
    ...railwayLayer.sourceOpt.params
  }
  delete _params1.LAYERSDARK
  const railRoute = new CTMapOl.LayerControl.lib.WMSLayer({
    mapRef,
    url: railwayLayer.sourceOpt.url, // vue.$env.VUE_APP_RAILWAY_LINE_URL,
    // 在3D地形图上图层错乱
    params: _params1
  }, options)

  // 暗色火车线路图层
  const darkRailWayConfig = JSON.parse(JSON.stringify(railwayLayer.sourceOpt))
  darkRailWayConfig.params.LAYERS = darkRailWayConfig.params.LAYERSDARK  // railwayLayer.sourceOpt.params.LAYERS + '-dark'
  const darkRailRoute = new CTMapOl.LayerControl.lib.WMSLayer({
    mapRef,
    url: railwayLayer.sourceOpt.url, // vue.$env.VUE_APP_RAILWAY_LINE_URL,
    // projection: 'EPSG:3857', // 使用 EPSG:4326 投影
    params: darkRailWayConfig.params
  }, options)

  darkRailRoute.init()
  darkRailRoute.mount()
  darkRailRoute.visable()

  railRoute.init()
  railRoute.mount()
  railRoute.invisable() // 先隐藏，切换到亮色常规地图才展示

  // 对火车线路图层操作显隐
  bindRailwayRouteLayer({
    map,
    darkRailRoute,
    railRoute
  });

  map.mapToCenter = (point) => {
    setZoomAndCenter(mapRef, { point })
  };
  return map;
};

export {
  createMap,
  createSource,
  getMarkerStyle,
  imageMarker,
  textMarker,
  polygon,
  polyline,
  highLightArea,
  removeHighLightArea,
  imageMarkerWidthBg,
  arrowLineStyles,
  getShadowLineStyle,
  createWorkBenchMap,
  createFullViewMap,
  setZoomAndCenter
};
