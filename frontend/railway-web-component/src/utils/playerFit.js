/**
 * 该模块提供了一些方法来处理播放器窗口的布局和定位。
 * 它主要支持右侧和中间的窗口定位策略。
 */
import { iframeDialog, videoPostMessage } from './common.js';

export class $playerFit {
  static playerCache = [];
  /**
   * 在右侧通过追加方式布局播放器窗口。
   *
   * @param originList 原始播放列表，包含已布局的窗口信息。
   * @param list 追加的播放列表，需要布局的新窗口信息。
   * @param type 布局类型，用于区分是追加还是移除窗口。
   * @param positon 窗口布局位置信息，包括top和right属性。
   */
   static rightByAppend(originList, list, type, positon = {}) {
     list = list || [];
     positon.top = positon.top || '36%';
     iframeDialog('get', {
       iframeIds: ['bottom', 'right', 'floattool']
     }).then((windowInfo) => {
       this.rightByAppendAfter(originList, list, type, positon, windowInfo);
     });
  }
  // 根据窗口信息和布局策略计算新窗口的位置和大小
  static rightByAppendAfter(originList, list, type, positon, windowInfo) {
    // 获取窗口的宽度和高度
    const winW = windowInfo.innerWidth;
    const winH = windowInfo.innerHeight;
    // 获取iframe的信息，如果不存在则使用空对象
    const iframeInfo = windowInfo.iframeInfo || {};
    // 获取iframe底部的高度，如果不存在则使用默认值8
    const bottomH = iframeInfo['bottom']?.offsetHeight || 8;
    // 获取位置的边距，默认为8
    const margin = positon.margin || 8;
    // 将top位置转换为浮点数
    let top = parseFloat(positon.top);
    // 如果top位置是以百分比形式给出，则转换为实际像素值
    if(String(positon.top).indexOf('%') !== -1) {
      top = top / 100 * winH;
    }
    // 初始化rightR为0
    let rightR = 0;
    // 如果未指定right位置
    if(!positon.right) {
      // 如果存在浮动工具栏，则将其放在浮动工具栏的右侧
      if(iframeInfo['floattool']) {
        positon.right = winW - iframeInfo['floattool'].offsetLeft - iframeInfo['floattool'].offsetWidth;
      } else {
        // 如果不存在浮动工具栏，则将其放在默认位置，并计算rightR
        positon.right = 402;
        rightR = winW - (iframeInfo['right']?.offsetLeft || winW) - (iframeInfo['right']?.offsetWidth || 0);
      }
    }
    // 将right位置转换为浮点数
    let right = parseFloat(positon.right);
    // 如果right位置是以百分比形式给出，则转换为实际像素值
    if(String(positon.right).indexOf('%') !== -1) {
      right = right / 100 * winW;
    }
    // 计算left位置，使得元素位于指定的right位置
    const left = winW - right - rightR;
    // 计算可用高度，减去顶部和底部的高度
    const bodyH = winH - top - bottomH;
    // 计算每个自动调整大小元素的高度，高度为可用高度的三分之一减去边距
    const autoH = bodyH / 3 - margin;
    // 根据高度计算宽度，保持16:9的宽高比
    const autoW = autoH * 16 / 9;
    // 定义需要改变元素的索引
    const changeIndex = 2;
    // 复制list以避免直接修改原始数据
    list = JSON.parse(JSON.stringify(list));
    // 调用rightByAppendAfter2方法进行进一步的布局处理
    this.rightByAppendAfter2({list, originList, type, autoH, autoW, top, left, margin, changeIndex});
  }

  // 根据布局参数调整窗口的位置和大小
  static rightByAppendAfter2(params) {
     // 解构参数对象，获取所需变量
     let {list, originList, type, autoH, autoW, top, left, margin, changeIndex} = params;
    const listNew = []; // 初始化一个新的列表，用于存储处理后的窗口信息
    const appendList = list.map(ele => ele.channelCode); // 从params.list中提取channelCode，形成一个新列表
    originList.forEach((item, index) => {
      if(index > 4) {
        return false; // 如果索引大于4，停止处理当前列表项
      }
      if(!item) {
        return false; // 如果当前列表项为空，停止处理当前列表项
      }
      // 设置窗口的高度
      item.videoHeight = parseInt(autoH) + 'px';
      // 设置窗口的左位置（注意：这里的逻辑似乎是想将窗口放置在左侧，但是左位置的计算方式可能有误）
      item.positonLeft = parseInt(left - autoW) + 'px';
      // 设置窗口的上位置
      item.positonTop = parseInt(top + autoH * index + margin * (index + 1)) + 'px';
      // 如果索引大于changeIndex，重新计算窗口的左和上位置
      if(index > changeIndex) {
        // 重新计算左位置，逻辑为向左偏移一定距离
        item.positonLeft = parseInt(left - (autoW + margin) * (index - changeIndex) - autoW) + 'px';
        // 重新计算上位置
        item.positonTop = parseInt(top + (autoH + margin) * changeIndex + margin) + 'px';
      }
      // 设置窗口的变换原点
      item.transformOrigin = '0 0';
      // 如果通道代码存在于appendList中，则将该项添加到listNew中
      if (appendList.indexOf(item.channelCode) > -1) {
        listNew.push(item);
      }
    });
    // 根据type调用doCall方法，如果是'remove'类型，则使用原始list，否则使用处理后的listNew
    this.doCall(type === 'remove' ? list : listNew, type);
  }


  /**
   * 中间定位
   * @param list 播放视频列表 必传, 参数: deviceCode(设备编码 Y)、channelCode(通道编码 Y)、longitude(经度 N)、latitude(纬度 N)、showClose(是否显示关闭按钮, 默认true显示 N)，后期可选videoHeight(窗口高度)
   * @param videoType 后期可选 new(清空后打开), append(拼接播放)
   * @param positon
   */
  static center(list, videoType, position) {
    // 如果没有传入列表，则使用空数组
    list = list || [];
    // 如果没有传入视频类型，则默认为 'new'
    videoType = videoType || 'new';
    // 获取当前正在播放的视频窗口
    this.get().then((playings) => {
      let len = list.length;
      // 如果视频类型为 'append'，则将当前正在播放的视频窗口数量加到列表长度上
      if(videoType === 'append') {
        len += playings.length;
      }
      // 如果列表长度大于1，则调用 right 方法并将返回值设为 false
      if(len > 1) {
        this.right(list, videoType, position);
        return false;
      }
      // 获取窗口高度
      iframeDialog('get').then((windowInfo) => {
        const winH = windowInfo.innerHeight;
        // 深拷贝列表以避免直接修改原列表
        list = JSON.parse(JSON.stringify(list));
        const item = list[0];
        item.index = 0;
        // 获取视频高度，如果视频高度没有指定，则默认为窗口高度的65%
        let height = parseFloat(item.videoHeight || winH * 0.65);
        // 如果视频高度是以百分比形式给出，则计算出实际高度
        if(String(item.videoHeight).indexOf('%') > -1) {
          height = winH * height / 100;
        }
        // 将视频高度转换为字符串形式，并添加 'px' 单位
        item.videoHeight = parseInt(height) + 'px';
        // 如果列表为空，则返回 false
        if(!list.length) {
          return false;
        }
        // 调用 doCall 方法处理中心视频
        this.doCall(list, videoType);
      });
    });
  }

  /**
   * 在右侧布局播放器窗口。
   *
   * @param list 播放列表，包含需要布局的窗口信息。
   * @param videoType 布局类型，决定是新布局还是追加到已有布局。
   * @param positon 窗口布局位置信息，包括top和right属性。
   */
  static right(list, videoType, positon = {}) {
    // 如果list未提供，则初始化为空数组
    list = list || [];
    // 如果videoType未提供，则默认为'new'
    videoType = videoType || 'new';
    // 获取当前正在播放的视频窗口列表
    this.get().then((playings) => {
      // 如果positon.top未提供，则默认为'36%'
      positon.top = positon.top || '36%';
      // 获取特定ID的iframe窗口信息
      iframeDialog('get', {
        iframeIds: ['bottom', 'right', 'floattool']
      }).then((windowInfo) => {
        // 调用rightAfter方法处理视频窗口列表、视频类型、位置信息以及正在播放的窗口信息
        this.rightAfter(list, videoType, positon, playings, windowInfo);
      });
    });
  }

  static appendLeftSmall(list, videoType) {
    // 如果list未提供，则初始化为空数组
    list = list || [];
    // 计算即将添加的视频窗口的索引
    const playerIndex = this.playerCache.length;
    // 将新的视频窗口列表添加到playerCache中
    this.playerCache.push(...list);
    // 获取当前正在播放的视频窗口
    this.get().then((playings) => {
      // 获取窗口高度
      iframeDialog('get').then((windowInfo) => {
        // 获取窗口高度
        const winH = windowInfo.height;
        // 定义小视频窗口的高度和宽度
        let height = 215;
        let width = 380;
        // 定义小视频窗口的初始位置
        let originPoint = {
          top: winH - 380 - 12 - height,
          left: 360 + 12 + 12
        };
        // 遍历list，为每个视频窗口设置索引、高度、宽度以及计算其在左侧的位置
        list.map((item, index) => {
          // 设置当前视频窗口的索引
          item.index = playerIndex + index;
          // 计算列索引
          const columnIndex = Math.floor(item.index / 2);
          // 计算行索引
          const rowIndex = item.index % 2;
          // 设置视频窗口的高度
          item.videoHeight = height + 'px';
          // 设置视频窗口的宽度
          item.videoWidth = width + 'px';
          // 计算窗口的左上角位置【left】
          // 窗口的左上角位置【left】 = 窗口的左上角位置 + （窗口的宽度+40【margin：12px + 假设的间距】） * 列索引
          item.positonLeft = originPoint.left + (width + 40) * columnIndex + 'px';
          // 计算窗口的左上角位置【top】
          // 窗口的左上角位置【top】 = 窗口的左上角位置 - （窗口的高度+12【margin：12px】） * 行索引
          item.positonTop = originPoint.top - (height + 12) * rowIndex + 'px';
          return item;
        });
        // 如果list为空，则直接返回不进行后续操作
        if (list.length < 1) {
          return;
        }
        // 调用doCall方法执行视频窗口的添加操作
        this.doCall(list, videoType);
      });
    });
  }

  // 根据窗口信息和布局策略计算新窗口的位置和大小
  // 对窗口列表进行处理，准备进行布局
  static rightAfter(list, videoType, positon, playings, windowInfo) {
    // 获取当前窗口的宽度和高度
    const winW = windowInfo.innerWidth;
    const winH = windowInfo.innerHeight;

    // 获取iframe的信息，默认为空对象
    const iframeInfo = windowInfo.iframeInfo || {};

    // 获取iframe底部元素的高度，如果没有则默认为8
    const bottomH = iframeInfo['bottom']?.offsetHeight || 8;

    // 获取布局中的边距，默认为8
    const margin = positon.margin || 8;

    // 初始化top的位置，如果top是百分比形式，则转换为实际像素值
    let top = parseFloat(positon.top);
    if(String(positon.top).indexOf('%') !== -1) {
      top = top / 100 * winH;
    }

    // 初始化right的位置，如果未提供right，则根据iframeInfo中的floattool元素计算
    let rightR = 0;
    if(!positon.right) {
      if(iframeInfo['floattool']) {
        positon.right = winW - iframeInfo['floattool'].offsetLeft - iframeInfo['floattool'].offsetWidth;
      } else {
        positon.right = 402;
        rightR = winW - (iframeInfo['right']?.offsetLeft || winW) - (iframeInfo['right']?.offsetWidth || 0);
      }
    }

    // 初始化right的位置，如果right是百分比形式，则转换为实际像素值
    let right = parseFloat(positon.right);
    if(String(positon.right).indexOf('%') !== -1) {
      right = right / 100 * winW;
    }

    // 计算left的位置
    const left = winW - right - rightR;

    // 计算播放区域的高度，减去顶部和底部的高度
    const bodyH = winH - top - bottomH;

    // 计算每个播放窗口的高度和宽度，高度为bodyH的三分之一减去边距，宽度根据16:9的比例计算
    const autoH = bodyH / 3 - margin;
    const autoW = autoH * 16 / 9;

    // 定义改变布局的索引位置
    const changeIndex = 2;

    // 复制并截取播放列表的前5个元素
    list = JSON.parse(JSON.stringify(list)).slice(0, 5);

    // 如果视频类型为'append'，则调用beforePlay方法处理播放列表
    if(videoType === 'append') {
      this.beforePlay(list, playings);
    }

    // 调用rightAfter2方法根据计算的参数调整窗口的位置和大小
    this.rightAfter2({list, videoType, autoH, autoW, top, left, margin, changeIndex});
  }

  // 根据布局参数调整窗口的位置和大小
  static rightAfter2(params) {
    // 解构赋值获取参数
    let {list, videoType, autoH, autoW, top, left, margin, changeIndex} = params;

    // 遍历播放列表，为每个播放项设置位置和大小
    list.forEach((item, i) => {
      let index = i;
      // 如果视频类型为'append'且播放项中有index属性，则使用该项的index属性
      if(videoType === 'append' && item.index !== undefined) {
        index = item.index;
      }
      // 设置播放项的index属性
      item.index = index;

      // 设置播放窗口的高度
      item.videoHeight = parseInt(autoH) + 'px';

      // 设置播放窗口的左边位置
      item.positonLeft = parseInt(left - autoW) + 'px';

      // 设置播放窗口的顶部位置
      item.positonTop = parseInt(top + autoH * index + margin * (index + 1)) + 'px';

      // 如果index大于changeIndex，则调整播放窗口的位置到右边
      if(index > changeIndex) {
        item.positonLeft = parseInt(left - (autoW + margin) * (index - changeIndex) - autoW) + 'px';
        item.positonTop = parseInt(top + (autoH + margin) * changeIndex + margin) + 'px';
      }

      // 设置transform-origin属性
      item.transformOrigin = '0 0';
    });

    // 如果播放列表为空，则返回false
    if(!list.length) {
      return false;
    }

    // 调用doCall方法处理播放列表
    this.doCall(list, videoType);
  }

  /**
   * 在播放前处理窗口布局。
   *
   * @param list 新的播放列表。
   * @param old 已经布局的窗口列表。
   */
  static beforePlay(list, old) {
    // 定义最大播放窗口数量
    const max = 5;

    // 用于保存需要保留的窗口索引
    const keep = [];

    // 遍历新的播放列表，移除已经在播放列表中的项，并保留其索引
    for(let i = list.length - 1; i >= 0; i--) {
      const isPlaying = old.find(item => item.deviceCode === list[i].deviceCode && item.channelCode === list[i].channelCode);
      if(isPlaying) {
        list.splice(i, 1);
        keep.push(isPlaying.index);
      }
    }

    // 对已有的播放列表按照索引进行排序
    old.sort((a, b) => a.index - b.index);

    // 复制已有的播放列表，并填充false占位符以保持正确的索引位置
    const oldF = JSON.parse(JSON.stringify(old));
    for(let j = 0; j < max; j++) {
      if(oldF[j]?.index !== j) {
        oldF.splice(j, 0, false);
      }
    }

    // 计算需要填充的空位数量
    let gap = max - old.length;
    let count = 0;

    // 填充新的播放项到已有的播放列表中
    for(let k = 0; k < oldF.length; k ++) {
      if(oldF[k]) {
        continue;
      }
      if(list[count]) {
        list[count].index = k;
      }
      if(++count >= gap) {
        break;
      }
    }

    // 调用beforePlay2方法根据旧的播放列表和需要保留的索引为新的播放项分配位置
    this.beforePlay2(old, keep, list, gap);
  }

  // 根据旧窗口的布局情况，为新窗口分配布局位置
  static beforePlay2(old, keep, list, gap) {
    // 遍历已有的播放列表，为不在保留索引中的播放项分配新的播放项的位置
    old.forEach((item) => {
      if(!keep.includes(item.index) && list[gap]) {
        list[gap].index = item.index;
        // 关闭旧的播放窗口
        this.close([item]);
        gap ++;
      }
    });
  }


  /**
   * 关闭指定的播放器窗口。
   *
   * @param list 需要关闭的窗口列表，可以通过设备和通道编码标识窗口。
   */
  static close(list) {
    // 如果传入的列表为空，则初始化为空数组
    list = list || [];
    // 深拷贝列表以避免修改原对象
    list = JSON.parse(JSON.stringify(list));
    // 根据列表长度确定操作类型：如果列表不为空则为'remove'，否则为'removeAll'
    const videoType = list.length ? 'remove' : 'removeAll';
    // 清空播放器缓存
    this.playerCache = [];
    // 如果列表长度大于0，则过滤出不在列表中的播放项
    if (list.length > 0) {
      this.playerCache = this.playerCache.filter(item => !list.find(
        i => i.deviceCode === item.deviceCode && i.channelCode === item.channelCode));
    }
    // 调用doCall方法执行关闭操作
    this.doCall(list, videoType);
  }

  /**
   * 获取当前正在播放的窗口列表。
   *
   * @returns Promise，解析为当前播放窗口列表。
   */
  static get() {
    return new Promise((result) => {
      // 定义操作类型为'getPlaying'
      const videoType = 'getPlaying';
      // 构建视频参数对象
      const videoParam = {
        videoType
      };
      // 通过videoPostMessage发送获取播放列表的请求
      videoPostMessage(videoParam);
      // 监听窗口消息，处理返回的播放数据
      window.onmessage = (event) => {
        // 检查消息是否为预期的回调方法
        if(event?.data?.callBackMethod === videoType) {
          // 对返回的视频数据按index进行排序
          event.data.videoData.sort((a, b) => a.index - b.index);
          // 解析Promise并返回排序后的视频数据
          result(event.data.videoData);
        }
      };
    });
  }

  /**
   * 触发播放器相关操作，如打开、关闭、点击等。
   *
   * @param list 播放列表，包含需要操作的窗口信息。
   * @param videoType 操作类型，如打开、关闭等。
   */
  static doCall(list, videoType) {
    // 打印操作类型和播放列表
    console.log('===================doCall=====================', list)
    // 遍历播放列表，为每个播放项设置默认的回调方法和工具栏显示状态
    list.forEach((item) => {
      item.closeCallBackMethod = item.closeCallBackMethod || 'bigScreenPlayerClose';
      item.clickCallBackMethod = item.clickCallBackMethod || 'bigScreenPlayerClick';
      item.openQuickTool = true;
    });
    // 构建视频参数对象，包含操作类型和处理过的播放列表
    const videoParam = {
    	videoType: videoType,
      videoDataList: list
    };
    // 通过videoPostMessage发送操作请求
    videoPostMessage(videoParam);
  }
}
