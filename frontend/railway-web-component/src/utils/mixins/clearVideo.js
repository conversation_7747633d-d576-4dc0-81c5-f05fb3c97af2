/**
 * 移除所有视频，页面加载的时候先关闭下之前存在的视频
 */
import { $playerFit } from '@/utils/playerFit';
import { videoPostMessage } from '@/utils/common.js';

export default {
  /* 在组件挂载到DOM后执行的钩子函数 */
  mounted() {
    this.removeAllVideo();
  },
  /**
   * 定义组件的方法。
   */
  methods: {
    /**
     * 触发全屏模式。
     * @param {Object} event - 触发全屏事件的对象。
     * 此方法使用this.$fullscreen.enter方法将指定元素进入全屏模式。
     * 它不包裹元素，并在全屏模式改变时更新fullscreenFlag的值。
     */
    removeAllVideo(event) {
      $playerFit.close();
      const videoParam = {
        videoType: 'removeMutiAll',
        videosData: {
          videoList: [],
        },
      };
      videoPostMessage(videoParam);
    },
  },
};
