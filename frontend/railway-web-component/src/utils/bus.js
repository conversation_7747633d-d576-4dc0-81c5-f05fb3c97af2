import Vue from 'vue';

/**
 * 全局事件总线
 * 用于组件间通信，提供事件的发布和订阅功能
 *
 * 使用方法：
 * 1. 发送事件：bus.$emit('eventName', data)
 * 2. 监听事件：bus.$on('eventName', callback)
 * 3. 移除监听：bus.$off('eventName', callback)
 * 4. 一次性监听：bus.$once('eventName', callback)
 */
class EventBus {
  constructor() {
    this.vue = new Vue();
  }

  /**
   * 发送事件
   * @param {string} event 事件名称
   * @param {...any} args 事件参数
   */
  $emit(event, ...args) {
    this.vue.$emit(event, ...args);
  }

  /**
   * 监听事件
   * @param {string} event 事件名称
   * @param {Function} callback 回调函数
   */
  $on(event, callback) {
    this.vue.$on(event, callback);
  }

  /**
   * 移除事件监听
   * @param {string} event 事件名称
   * @param {Function} callback 回调函数
   */
  $off(event, callback) {
    this.vue.$off(event, callback);
  }

  /**
   * 一次性事件监听
   * @param {string} event 事件名称
   * @param {Function} callback 回调函数
   */
  $once(event, callback) {
    this.vue.$once(event, callback);
  }

  /**
   * 销毁事件总线
   */
  destroy() {
    this.vue.$destroy();
  }
}

// 创建全局事件总线实例
const bus = new EventBus();

// 将事件总线挂载到 Vue 的原型上，使得所有 Vue 实例都可以通过 this.$EventBus 访问到事件总线
Vue.prototype.$EventBus = bus;

export default bus;