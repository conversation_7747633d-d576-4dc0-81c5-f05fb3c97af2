/*
 * @Author: 逗逗飞 
 * @Date: 2024-05-24 11:53:15
 * @LastEditors: 逗逗飞 
 * @LastEditTime: 2024-05-26 14:31:34
 * @FilePath: /global-awareness-pc/src/utils/index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { requestSDK } from '@ct/iframe-connect-sdk'
import { loadSpaceShardAsync } from '@ct/remote-page-loader/utils/remote-loader'
import CTMapOl from "@ct/ct_map_ol";
import Mask from "ol-ext/filter/Mask";
import mainVue from '@/main'

export const getInfo = async () => {
  const resp = await requestSDK('getInfo')
  if (resp.code === 200) {
    sessionStorage.setItem('Admin-Token', resp.user.token)
    return resp
  }
}
// 部署时组件库地址为独立域名，从工作台businessComponentFilsUrl配置获取
export const getFisUrl = () => {
  return window.requestSDK('/admin/system/config/base/detail/businessComponentFilsUrl', {}, {}, 'get')
}
export const getBusinessFiles = (configValue, param) => {
  return window.requestSDK(configValue || '/component-gallery/api/business/files', param, {}, 'post').then(resp => {
    return new Promise(async (resolve, reject) => {
      const { code, data } = resp
      if (code && code === 200) {
        const baseCompObj = {
          name: 'RemoteComponentsLoader',
          config: {
            name: '',
            description: '',
            js: '',
            css: ''
          }
        }
        const keys = Object.keys(data)
        const remotes = keys.map(key => {
          const { js, css } = data[key]
          return {
            ...baseCompObj,
            name: key,
            config: {
              name: key,
              css,
              js
            }
          }
        })
        const resp = await loadSpaceShardAsync({ data: remotes })
        setTimeout(() => {
          const obj = {}
          resp.forEach(o => {
            obj[o.config.name] = o.config
          })
          let result = {
            components: obj,
            loaded: true,
          }
          return resolve(result)

        }, 0);
        return
      }
      reject(
        this?.$notify?.error({
          title: `获取远程组件失败`,
          message: `获取远程组件失败`
        })
      )
    })

  }).catch(err => {
    const { title, message } = err
    this.$notify.error({
      title: `${title}`,
      message: `${message}`
    })
  }).finally(() => {
    false
  })
}

// 异步版本。差别是，Async版本不在这里等待scriptLoad，脚本加载推迟到远程加载组件自己做。
export const getBusinessFilesAsync = (configValue, param) => {
  return window.requestSDK(configValue || '/component-gallery/api/business/files', param, {}, 'post').then(resp => {
    return new Promise(async (resolve, reject) => {
      const { code, data } = resp
      if (code && code === 200) {
        const baseCompObj = {
          name: 'RemoteComponentsLoader',
          config: {
            name: '',
            description: '',
            js: '',
            css: ''
          }
        }
        const keys = Object.keys(data)
        const remotes = keys.map(key => {
          const { js, css } = data[key]
          return {
            ...baseCompObj,
            name: key,
            config: {
              name: key,
              css,
              js
            }
          }
        })
        const obj = {}
        remotes.forEach(item => {
          obj[item.config.name] = item
        })
        return resolve({
          components: obj,
          loaded: true,
        })
      } else {
        reject(
          mainVue.$notify?.error({
            title: `获取远程组件失败`,
            message: `获取远程组件失败`
          })
        )
      }
    })

  }).catch(err => {
    const { title, message } = err || {}
    mainVue.$notify?.error({
      title: `${title}`,
      message: `${message}`
    })
  }).finally(() => {
    false
  })
}

/**
 * 动态处理根部font-size
 * @param {string} [direction='v' | 'h'] //- 'v' 竖屏 'h' 横
 */
export const setRootFontSize = (direction = 'v') => {
  const htmlElement = document.documentElement
  const viewportHeight = window.innerHeight
  const baseHeight = direction === 'v' ? 1080 : 1920
  const referenceHeight = 1032
  const newFontSize =
    direction === 'v'
      ? `calc((100vh / ${baseHeight}) * 100 * (${baseHeight} / ${referenceHeight}))`
      : `calc((100vw / ${baseHeight}) * 100)`

  htmlElement.style.fontSize = newFontSize
}

/**
 * 坐标转换, 4326转3857，84坐标系转墨卡托投影
 * @param {*} point
 * @returns
 */
export const transformPointToMercator = (point, change = '') => {
  const sourceProj = "EPSG:4326"; // 定义源投影为WGS 84
  const targetProj = "EPSG:3857"; // 定义目标投影为Web Mercator

  // 将经纬度坐标从源投影转换为目标投影
  let pointTransformed = null
  if (change === '4326') {
    pointTransformed = CTMapOl.proj.transform([point[0] * 1, point[1] * 1], targetProj, sourceProj);
  } else {
    pointTransformed = CTMapOl.proj.transform([point[0] * 1, point[1] * 1], sourceProj, targetProj);
  }
  return pointTransformed; // 返回转换后的坐标
};

export const removeHighLightArea = (map, highlight) => {
  try {
    // 获取地图上的所有图层，并筛选出groupName属性为'baseLayer'的图层
    const tileLayers = map.getLayers().getArray().filter(i => i.groupName && i.groupName === 'baseLayer');

    // 遍历筛选出的图层，移除这些图层上的所有高亮滤镜
    tileLayers.forEach(tileLayer => {
      // 检查当前图层是否有filters_属性且该属性长度大于0
      if (tileLayer.filters_ && tileLayer.filters_.length > 0) {
        // 遍历filters_数组，移除每个滤镜
        tileLayer.filters_.forEach(filter => {
          tileLayer.removeFilter(filter);
        });
      }
    });
  } catch (e) {
    // 如果在执行过程中发生错误，将错误信息打印到控制台
    console.log(e);
  }
};

export const highLightArea = (map, list) => {
  // 创建一个GeoJSON对象，用于表示多边形区域
  const geojson = {
    type: "Feature",
    geometry: {
      type: "MultiPolygon",
      coordinates: list, // list包含多边形的坐标
    },
  };

  // 获取地图图层数组中groupName为'baseLayer'的图层，并选择最后一个图层
  const tileLayers = map.getLayers().getArray().filter(i => i.groupName && i.groupName === 'baseLayer');
  const tileLayer = tileLayers[tileLayers.length - 1]; // 选择最后一个基础图层

  // 将GeoJSON对象转换为OpenLayers中的Feature对象
  const feature = new CTMapOl.format.GeoJSON().readFeatures(geojson)[0];

  // 将Feature的几何体坐标从EPSG:4326转换为EPSG:3857
  feature.getGeometry().transform('EPSG:4326', 'EPSG:3857');

  // 创建一个Mask对象，用于高亮显示指定的Feature，设置填充颜色为半透明黑色
  const highlight = new Mask({
    feature: feature,
    fill: new CTMapOl.style.Fill({
      color: "rgba(0, 0, 0, 0.5)",
    }),
  });

  // 将高亮显示的Mask添加到基础图层中
  tileLayer.addFilter(highlight);

  // 再次读取几何体并转换坐标系，以确保坐标系一致
  const geom = new CTMapOl.format.GeoJSON().readGeometry(geojson.geometry).transform('EPSG:4326', 'EPSG:3857');

  // 调整地图视图以适应高亮区域的几何体，设置动画持续时间为1000毫秒
  map.getView().fit(geom, { duration: 1000 });

  // 返回高亮对象，以便可以在其他地方进行操作
  return highlight;
};

export const refreshHighLightArea = (map, highlight) => {
  // 获取地图上的所有图层，并过滤出组名为'baseLayer'的图层
  const tileLayers = map.getLayers().getArray().filter(i => i.groupName && i.groupName === 'baseLayer')
  // 获取组名为'baseLayer'的最后一个图层
  const tileLayer = tileLayers[tileLayers.length - 1]
  // 将高亮过滤器添加到该图层
  tileLayer.addFilter(highlight);
}

const imageMarker = (lnglat, option, attributes) => {
  // 从选项中提取图标路径
  let imgIcon = option.icon;

  // 将经纬度坐标转换为墨卡托坐标系
  const transferPoint = transformPointToMercator(lnglat);

  // 创建一个新的地图要素，几何图形为转换后的墨卡托坐标点
  let feature = new CTMapOl.Feature({
    geometry: new CTMapOl.geom.Point(transferPoint),
    attributes: attributes, // 设置要素的属性
  });

  // 为要素设置样式，主要设置图标的样式
  feature.setStyle(
    new CTMapOl.style.Style({
      image: new CTMapOl.style.Icon({
        // anchorYUnits: "pixels",
        // 图标锚点，默认在图标中心底部
        anchor: option.anchor || [0.5, 36],
        // 图标图片的路径
        src: imgIcon,
        // 图标缩放比例，默认为1（原始大小）
        scale: 5,
        // 图标相对于锚点的偏移量，默认为[1, 0]
        offset: option.offset || [0, 0],
        // 图标偏移量的原点，默认在图标顶部左侧
        // offsetOrigin: option.offsetOrigin || "top-left",
        // 图标的尺寸，默认为undefined（使用图片原始尺寸）
        size: option.size || undefined,
      }),
    }),
  );

  // 返回配置好的地图要素
  return feature;
};
const createMarks = (data = [], options = {}) => {
  const list = data.map((item) => // 遍历数据数组，为每个数据项创建标记
    imageMarker(
      // 根据选项的类型调整经纬度坐标
      item.lnglat,
      { icon: item.imgIcon }, // 设置标记的图标
      {
        ...item.detail, // 合并数据项的详细信息
        imgIcon: item.imgIcon, // 图标信息
        lnglat: item.lnglat, // 原始经纬度坐标
        text: item.text, // 标记的文本信息
        ...options, // 合并传入的选项
        popName: item.popName, // 弹出窗口的名称
      }
    )
  );
  return list; // 返回标记列表
};

export const markerClusterLayer = (data, map, options) => {
  const textFill = new CTMapOl.style.Fill({
    color: "#ffffff",
  });
  const textFillTerracotta = new CTMapOl.style.Fill({
    color: "#ffffff",
  });
  const textStroke = new CTMapOl.style.Stroke({
    color: "#ffffff",
    width: 1,
  });

  const styleCluster = (countStr, type) => {
    return new CTMapOl.style.Style({
      image: new CTMapOl.style.Icon({
        anchor: [0.5, 0.65],
        src: '', // zhuanghao,
      }),
      text: new CTMapOl.style.Text({
        textvalue: 'label,value',
        font: 'normal 13px 微软雅黑',
        scale: 1,
        textAlign: "center",
        text: '', // countStr,
        // offsetY: -4,
        // offsetX: 0,
        fill: textFill,
        // stroke: textStroke,
      }),
    });
  };

  const monitorIcon = (imgIcon, text) => {
    return new CTMapOl.style.Style({
      image: new CTMapOl.style.Icon({
        anchor: [0.5, 0.65],
        src: imgIcon
      }),
      text: new CTMapOl.style.Text({
        font: 'normal 13px 微软雅黑',
        scale: 1,
        textAlign: "center",
        text,
        offsetY: -4,
        offsetX: 7,
        fill: textFill,
        // stroke: textStroke,
      }),
    });
  };

  const clusterStyle = (feature) => {
    if (feature.get("features") && feature.get("features").length > 1) {
      return styleCluster(
        feature.get("features").length.toString(),
        feature.get("features")[0].values_.attributes.type
      );
    }

    if (feature.get("features") && feature.get("features").length == 1) {
      let sel = feature.get("features")[0].get("attributes");
      return sel && monitorIcon(sel.imgIcon, sel.text);
    }

    return monitorIcon();
  };

  const createClusterLayer = (pointsSourceObj) => {
    const vectorLayer = new CTMapOl.layer.Vector({
      source: new CTMapOl.source.Cluster({
        distance: 50, // 聚合距离，单位是像素，
        minDistance: 0,
        source: pointsSourceObj,
      }),
      style: clusterStyle,
      zIndex: 10,
    });

    return vectorLayer;
  };
  const getZoomClass = (mapObj) => {
    var view = mapObj.getView();
    if (view) {
      var currentZoomD = view.getZoom();
      var currentZoom = Math.round(currentZoomD);
      return currentZoom;
    }
    return undefined;
  };

  const pointsSource = new CTMapOl.source.Vector(); // 返回一个矢量数据源实例
  const fts = createMarks(data, options);
  pointsSource.addFeatures(fts);
  const pointsClusterLayer = createClusterLayer(pointsSource);
  pointsClusterLayer.setMinZoom(0);
  pointsClusterLayer.setMaxZoom(18);
  pointsClusterLayer.setVisible(true);

  map.addLayer(pointsClusterLayer);

  return pointsClusterLayer;
};  