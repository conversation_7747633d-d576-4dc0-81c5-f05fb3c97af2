/**
 * 描述组件的数据属性。
 * @property {Boolean} fullscreenFlag - 全屏状态的标志。初始为false。
 */
export default {
    data() {
        return {
            fullscreenFlag: false,
        };
    },

    /**
     * 定义组件的方法。
     */
    methods: {
        /**
         * 触发全屏模式。
         * @param {Object} event - 触发全屏事件的对象。
         * 此方法使用this.$fullscreen.enter方法将指定元素进入全屏模式。
         * 它不包裹元素，并在全屏模式改变时更新fullscreenFlag的值。
         */
        fullscreen(event) {
            // 使用this.$fullscreen.enter方法进入全屏模式
            this.$fullscreen.enter(event.target, {
                wrap: false, // 不包裹元素

                // 定义全屏状态改变时的回调函数
                callback: (f) => {
                    this.fullscreenFlag = f; // 更新全屏标志的状态
                },
            });
        },
    },
};
