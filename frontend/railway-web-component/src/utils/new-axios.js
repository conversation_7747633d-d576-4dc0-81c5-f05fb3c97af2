import axios from 'axios';

// 创建 axios 实例
const baseUrl = window.location.protocol + '//' + window.location.host;
axios.defaults.withCredentials = true;//让ajax携带cookie
const request = axios.create({
  baseURL: baseUrl,// 基础url。
  timeout: 60000,// 请求超时时间
  withCredentials: true
});

// request interceptor(请求拦截器)
request.interceptors.request.use(config => {
  //自动传cookie
  config.credentials = true;
  return config;
}, error => {
  let respError = {
    status: error.response.status,
    statusText: error.response.Unauthorized
  };
  return Promise.reject(respError);
});

// response interceptor（接收拦截器）
request.interceptors.response.use((response) => {
    const res = response;
    res.url = response.request.responseURL;
    res.time = response.headers.date;
    // 网关响应参数可能为msg或message
    const msg = res.data.msg || res.data.message;
    // 暂时 . 工具箱看这里获取视频设备code400
    if (res.data.code === 200 || res.data.code === 400) {
      return res.data;
    } else if (res.data.code === 401) { // 401:token失效
      console.error('token失效');
    } else {
      console.error('未知异常，请联系管理员', msg);
      return Promise.reject('error');
    }
  }, error => {
    let { message } = error;
    if (message === 'Network Error') {
      message = '系统接口请求异常';
    } else if (message.includes('timeout')) {
      message = '系统接口请求超时';
    } else if (message.includes('Request failed with status code')) {
      const code = message.substr(message.length - 3);
      if (code === '503') {
        message = '服务尚未注册成功,请确认已启动服务后稍等!';
      } else if (code === '401') { // 401:token失效
        message = '暂未登录或token已经过期，请重新联系管理员或重新登录!';
      } else {
        message = '系统接口' + message.substr(message.length - 3) + '异常';
      }
    } else {
      //Add the missing "else" clause
      message = '系统接口请求异常';
    }
    console.error('未知异常，请联系管理员', message);
    return Promise.reject(error);
  }
);

export default request;
