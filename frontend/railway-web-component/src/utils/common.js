/**
 * 从ct/iframe-connect-sdk模块导入iframeSDK和requestSDK。
 * @import { iframeSDK, requestSDK } from '@ct/iframe-connect-sdk';
 */
import { uuid } from '@/components/common/utils'; // 导入uuid函数
import request from '@/utils/new-axios'; // 导入axios请求工具
import { requestSDK } from '@ct/iframe-connect-sdk'; // 导入requestSDK
/**
 * 生成一个0到1之间的随机浮点数。
 * @returns {number} 随机浮点数。
 */
export function randomFloat() {
  const crypto = window.crypto || window.msCrypto; // 获取浏览器的加密对象
  const array = new Uint32Array(1); // 创建一个长度为1的32位无符号整数数组
  return crypto.getRandomValues(array)[0]; // 生成随机数并返回
}

/**
 * 从URL中获取指定参数的值。
 * @param {string} name - 参数名。
 * @param {string} [webUrl] - URL字符串，默认使用当前页面的URL。
 * @returns {string} 参数值，如果不存在则返回空字符串。
 */
/* 获取url地址 */
export const $getUrlParam = (name, webUrl) => {
  try {
    if (webUrl == null) {
      webUrl = window.location.href; // 如果未提供webUrl，则使用当前页面的URL
    }
    if (webUrl.indexOf('?') === -1) {
      return ''; // 如果URL中没有参数，返回空字符串
    }
    const params = webUrl.split('?')[1]; // 获取参数部分
    const strs = params.split('&'); // 分割参数
    const param = {};
    for (let i = 0; i < strs.length; i++) {
      param[strs[i].split('=')[0]] = strs[i].split('=')[1]; // 将参数存入对象
    }
    const resp = param[name] || ''; // 获取指定参数的值
    return decodeURI(resp); // 返回解码后的参数值
  } catch (e) {
    return ''; // 出现异常时返回空字符串
  }
};

/**
 * 处理iframe对话框的操作，包括添加、删除、移动和获取iframe信息。
 * @param {string} type - 操作类型：'add'、'del'、'move'、'get'。
 * @param {object} params - 操作参数。
 * @returns {Promise<object>} 对话框操作的结果。
 */
let iframeDialogMoving = false; // 标记iframe对话框是否正在移动
export async function iframeDialog(type, params) {
  const keyBefore = 'iframeRef'; // iframe引用的前缀
  const viewId = $getUrlParam('viewId'); // 获取视图ID
  params = params || {}; // 确保params为对象
  const iframeIds = []; // 存储iframe ID的数组
  paramDeal(params, keyBefore, type, iframeIds, viewId); // 处理参数
  params.iframeIds.forEach((key) => {
    iframeIds.push(keyBefore + key + viewId); // 生成完整的iframe ID
  });
  const windowInfo = await requestSDK('getWindowInfo', {
    iframeIds: iframeIds,
    only: new Date().getTime() + '' + randomFloat(), // 唯一标识
  });
  const iframeInfo = windowInfo.data.iframeInfo || {}; // 获取iframe信息
  if (iframeInfo[keyBefore + params.key]) {
    iframeInfo[params.key] = iframeInfo[keyBefore + params.key]; // 更新iframe信息
  }
  params.iframeIds.forEach((key) => {
    if (iframeInfo[keyBefore + key + viewId]) {
      iframeInfo[key] = iframeInfo[keyBefore + key + viewId]; // 更新iframe信息
    }
  });
  if (type === 'get') {
    return new Promise((resolve) => {
      resolve(windowInfo.data); // 返回窗口信息
    });
  }
  const cw = windowInfo.data.width; // 容器宽度
  const ch = windowInfo.data.height; // 容器高度
  let percentW, percentH, percentX, percentY;
  let fitW = 0;
  if (type === 'add') {
    fitW = 1; // 添加操作时调整宽度
  }
  if (params.width) {
    const vw = params.width + fitW; // 弹框元素宽度
    percentW = (vw / cw) * 100 + '%'; // 计算宽度百分比
    if (type === 'add') {
      const vx = (cw - vw) / 2; // 计算left位置
      percentX = (vx / cw) * 100 + '%'; // 计算left百分比
    }
  }
  if (params.height) {
    const vh = params.height + fitW; // 弹框元素高度
    percentH = (vh / ch) * 100 + '%'; // 计算高度百分比
    if (type === 'add') {
      const vy = (ch - vh) / 2; // 计算top位置
      percentY = (vy / ch) * 100 + '%'; // 计算top百分比
    }
  }
  if (params.left != null) {
    percentX = (params.left / cw) * 100 + '%'; // 计算left百分比
  }
  if (params.top != null) {
    percentY = (params.top / ch) * 100 + '%'; // 计算top百分比
  }
  let vIframe;
  dealIframe(
    type,
    vIframe,
    iframeInfo,
    keyBefore,
    params,
    viewId,
    percentX,
    percentY,
    iframeDialogMoving,
    cw,
    ch
  );
  let returnData = dealResData(
    type,
    params,
    viewId,
    percentW, // 宽度百分比
    percentH, // 高度百分比
    percentX, // left百分比
    percentY
  );
  return new Promise((resolve) => {
    resolve(returnData); // 返回处理结果
  });
  // return _params;
}

/**
 * 处理iframe对话框的参数。
 * @param {object} params - 参数对象。
 * @param {string} keyBefore - 前缀。
 * @param {string} type - 操作类型。
 * @param {Array} iframeIds - iframe ID数组。
 * @param {string} viewId - 视图ID。
 */
function paramDeal(params, keyBefore, type, iframeIds, viewId) {
  params = params || {}; // 确保params为对象
  if (params.key) {
    let key = keyBefore + params.key; // 生成完整的key
    if (type === 'move') {
      key += viewId; // 移动操作时附加视图ID
    }
    iframeIds.push(key); // 添加到iframe ID数组
  }
  params.iframeIds = params.iframeIds || []; // 确保iframeIds为数组
}

/**
 * 处理iframe元素。
 * @param {string} type - 操作类型。
 * @param {HTMLElement} vIframe - iframe元素。
 * @param {object} iframeInfo - iframe信息。
 * @param {string} keyBefore - 前缀。
 * @param {object} params - 参数对象。
 * @param {string} viewId - 视图ID。
 * @param {string} percentX - left的百分比。
 * @param {string} percentY - top的百分比。
 * @param {boolean} iframeDialogMovingTemp - 是否正在移动对话框。
 * @param {number} cw - 容器宽度。
 * @param {number} ch - 容器高度。
 */
function dealIframe(
  type,
  vIframe,
  iframeInfo,
  keyBefore,
  params,
  viewId,
  percentX,
  percentY,
  iframeDialogMovingTemp,
  cw,
  ch
) {
  if (type === 'move') {
    vIframe = iframeInfo[keyBefore + params.key + viewId]; // 获取移动的iframe元素
  } else {
    vIframe = iframeInfo[keyBefore + params.key]; // 获取iframe元素
  }
  if (checkBoolean1(params, vIframe)) {
    percentX =
      ((cw - (params.width || vIframe.offsetWidth) - params.right) / cw) * 100 +
      '%'; // 计算left百分比
  }
  if (checkBoolean2(params, vIframe)) {
    percentY =
      ((ch - (params.height || vIframe.offsetHeight) - params.bottom) / ch) *
      100 +
      '%'; // 计算top百分比
  }
  if (commonBooleanJudge(params.move && vIframe, true, false)) {
    if (iframeDialogMovingTemp && params.fixed == null) {
      return false; // 如果正在移动且未固定，返回false
    }
    iframeDialogMovingTemp = true; // 标记正在移动
    setTimeout(() => {
      iframeDialogMovingTemp = false; // 80ms后重置移动标记
    }, 80);
    if (commonBooleanJudge(params.move.left, true, false)) {
      let x = vIframe.offsetLeft + params.move.left; // 计算新的left位置
      x = commonBooleanJudge(x < 0, 0, x); // 确保left不小于0
      x = commonBooleanJudge(
        x > cw - vIframe.offsetWidth,
        cw - vIframe.offsetWidth,
        x
      ); // 确保left不超过容器宽度
      percentX = (x / cw) * 100 + '%'; // 计算left百分比
    }
    if (params.move.top) {
      let y = vIframe.offsetTop + params.move.top; // 计算新的top位置
      y = commonBooleanJudge(y < 0, 0, y); // 确保top不小于0
      y = commonBooleanJudge(
        y > ch - vIframe.offsetHeight,
        ch - vIframe.offsetHeight,
        y
      ); // 确保top不超过容器高度
      percentY = (y / ch) * 100 + '%'; // 计算top百分比
    }
  }
}

/**
 * 检查是否满足布尔条件1。
 * @param {object} params - 参数对象。
 * @param {HTMLElement} vIframe - iframe元素。
 * @returns {boolean} 条件是否满足。
 */
function checkBoolean1(params, vIframe) {
  return params.right != null && (params.width || vIframe); // 检查条件
}
function checkBoolean2(params, vIframe) {
  return params.bottom != null && (params.height || vIframe); // 检查条件
}
function commonBooleanJudge(param, trueRes, falseRes) {
  return param ? trueRes : falseRes; // 根据条件返回结果
}
async function dealResData(
  type,
  params,
  viewId,
  percentW, // 宽度百分比
  percentH, // 高度百分比
  percentX, // left百分比
  percentY
) {
  let returnData = null;
  if (type === 'move') {
    params.key += viewId; // 移动操作时附加视图ID
  }
  const types = {
    add: 'addUrl',
    del: 'delUrl',
    move: 'moveUrl',
  };
  let _params = {
    iframeOperationId: types[type], // 操作类型
    key: params.key, // 唯一id
    viewId: viewId,
    percentW, // 宽度百分比
    percentH, // 高度百分比
    percentX, // left百分比
    percentY, // top百分比
    only: new Date().getTime() + '' + randomFloat(), // 唯一标识
  };
  if (type === 'add') {
    _params.iframeSrc = params.src; // 添加操作时设置iframe源
    _params.zIndex = 19; // 设置z-index
    await iframeSDK({
      iframeOperationId: 'delUrl',
      key: params.key, // 唯一id
    });
  }
  if (params.zIndex != null) {
    _params.zIndex = params.zIndex; // 设置z-index
  }
  // const returnData = await iframeSDK(_params);
  // console.log('-------returnData--------',returnData);
  if (type === 'move' || type === 'get') {
    _params.needBack = true; // 需要返回数据
    returnData = await iframeSDK({
      ..._params,
      only: new Date().getTime() + '' + randomFloat(), // 唯一标识
    });
  } else {
    returnData = await iframeSDK(_params); // 发起请求
  }
  return returnData; // 返回数据
}

/**
 * 视频插件的postMessage
 */
export function videoPostMessage(param) {
  const target = window.parent; // 获取父窗口
  target.postMessage(param, document.referrer); // 发送消息到父窗口
}
export const toPercentText = (num, total) => {
  if (!num || !total) {
    return '0%'; // 如果num或total为空，返回0%
  }
  let res = ((num / total) * 100).toFixed(1) + '%'; // 计算百分比
  res = res.replace(/0\.0/, '0'); // 去掉多余的0
  return res; // 返回百分比字符串
};
/**
 * 下划线字符串转驼峰字符串
 * @param {String} str 要转换的字符串
 * @return {String} 转换后的字符串
 */
export const toHump = (str) => {
  if (!str) {
    return str; // 如果字符串为空，直接返回
  }
  return str.replace(/\_(\w)/g, function (all, letter) {
    return letter.toUpperCase(); // 将下划线后的字母转换为大写
  });
};

//封装简写的vue请求
export class $v {
  /*
   * vue: 当前是以this.vue.$http或者this.vue.http方式请求(Vue、this)
   * url:请求链接
   * data:请求参数
   * success:请求成功回调
   * error:请求失败时是否报错(true时不报错),error三种形式:1.为function时调用 2.为true时不弹出提示错误，而是控制台输出  3.为false时弹出提示错误
   * loadParam对象: 属性loading,是否有加载动画,check是否校验,
   */
  static get(vue, url, params, success, error, loadParam = {}) {
    loadParam.connectMode = 'get'; // 设置请求模式为GET
    this.beforeConnect(vue, url, params, success, error, loadParam); // 进行请求前处理
  }
  static post(vue, url, params, success, error, loadParam = {}) {
    loadParam.connectMode = 'post'; // 设置请求模式为POST
    this.beforeConnect(vue, url, params, success, error, loadParam); // 进行请求前处理
  }
  static upload(vue, url, params, success, error, loadParam = {}) {
    loadParam.connectMode = 'post'; // 设置请求模式为POST
    loadParam.isLocation = true; // 设置为本地请求
    this.connect(vue, url, params, success, error, loadParam); // 发起请求
  }
  static beforeConnect(vue, url, params, success, error, loadParam) {
    loadParam.isLocation = vue.$env.VUE_APP_REQ_SDK === 'false'; // 判断是否使用本地请求
    this.connect(vue, url, params, success, error, loadParam); // 发起请求
  }
  static connect(vue, url, params, success, error, loadParam) {
    const connectMode = loadParam.connectMode; // 获取请求模式
    const onUploadProgress = loadParam.onUploadProgress; // 获取上传进度回调
    const _this = this;
    //获取请求axios的实例
    if ('post' === connectMode) {
      this.postHandle(_this, vue, {
        url,
        params,
        success,
        loadParam,
        onUploadProgress,
      });
      return;
    }
    if (loadParam.isLocation) {
      let p = '';
      if (params) {
        Object.keys(params).forEach(function (i) {
          p += i + '=' + params[i] + '&'; // 拼接参数
        });
        p = p.replace(/&$/, ''); // 去掉最后一个&
      }
      if (p) {
        url += '?' + p; // 拼接URL
      }
      request.get(url).then(function (d) {
        _this.judgmentReturn(vue, d, success); // 判断返回数据
      });
    } else {
      this.requestSDK(vue, url, params, 'get').then(function (d) {
        if (d.code === 200) {
          _this.judgmentReturn(vue, d, success); // 判断返回数据
        }
      });
    }
  }
  static postHandle(
    _this,
    vue,
    {
      url,
      params,
      success = () => {
        console.log('success');
      },
      loadParam = {},
      onUploadProgress,
    }
  ) {
    if (url.indexOf('?') > -1) {
      url += '&'; // 如果URL中已有参数，添加&
    } else {
      url += '?'; // 否则添加?
    }
    url += uuid(); // 添加唯一标识
    if (loadParam.isLocation) {
      request({
        method: 'post',
        url,
        data: params,
        onUploadProgress,
      }).then((d) => {
        _this.judgmentReturn(vue, d, success); // 判断返回数据
      });
    } else {
      /**
       * 使用请求SDK发起请求
       */
      this.requestSDK(vue, url, params, 'post').then((d) => {
        if (d.code === 200) {
          _this.judgmentReturn(vue, d, success); // 判断返回数据
        }
      });
    }
  }
  static async requestSDK(vue, url, params, method) {
    if (params.responseType) {
      const responseType = params.responseType; // 获取响应类型
      delete params.responseType; // 删除响应类型
      return await requestSDK(url, params, {}, method, responseType); // 发起请求
    }
    return await requestSDK(url, params, {}, method); // 发起请求
  }
  static judgmentReturn(vue, d, success) {
    if (success) {
      success(d); // 调用成功回调
    }
  }
  //判断字符是否为空
  static isEmpty(str) {
    return (
      typeof str === 'undefined' || str === null || str === '' || str === ' '
    ); // 判断字符串是否为空
  }
}
export function base64ToBlob(base64) {
  const str = atob(base64); // 解码base64字符串
  let n = str.length;
  const u8arr = new Uint8Array(n); // 创建Uint8Array
  while (n--) {
    u8arr[n] = str.charCodeAt(n); // 将字符串转换为字节
  }
  return new Blob([u8arr]); // 返回Blob对象
}

/**
 * 生成随机手机号
 * @returns
 */
export function generateRandomPhone() {
  // 中国大陆手机号有效号段（前三位）
  const validPrefixes = [
    '130',
    '131',
    '132',
    '133',
    '134',
    '135',
    '136',
    '137',
    '138',
    '139', // 中国联通
    '144',
    '145',
    '146',
    '147',
    '148',
    '149', // 物联网/数据卡
    '150',
    '151',
    '152',
    '153',
    '155',
    '156',
    '157',
    '158',
    '159', // 中国移动
    '165',
    '166',
    '167', // 虚拟运营商
    '170',
    '171',
    '172',
    '173',
    '174',
    '175',
    '176',
    '177',
    '178', // 各运营商
    '180',
    '181',
    '182',
    '183',
    '184',
    '185',
    '186',
    '187',
    '188',
    '189', // 中国电信
    '191',
    '192',
    '193',
    '195',
    '196',
    '198',
    '199', // 新号段
  ];

  // 随机选择号段
  const prefix =
    validPrefixes[Math.floor(randomFloat() * validPrefixes.length)];

  // 生成后8位随机数字
  const suffix = Array.from({ length: 8 }, () =>
    Math.floor(randomFloat() * 10)
  ).join('');

  return prefix + suffix;
}

/**
 * 设置地图瓦片图层
 */
export function setMapService(tileModes) {
  if (!tileModes?.length) return []
  const { MAP_CFG } = window.hjCfg
  // 解构出地图配置中的图层配置信息
  const {
    tiles: { wmtsTile },
  } = MAP_CFG
  // 拼接地图地址
  const addParamUrl = key => {
    const params = {
      ...wmtsTile.sourceOpt.params,
    }
    // 5-深色，6-浅色
    if (key === 5) {
      params.layer = params.LAYERSDARK
    } else {
      params.layer = params.LAYERS
    }
    delete params.LAYERSDARK
    delete params.LAYERS
    // 参数
    // new URLSearchParams(params).toString() 被转译
    const queryString = Object.keys(params).map(v => `${v}=${params[v]}`).join('&');
    return `${wmtsTile.sourceOpt.url}?${queryString}`
  }
  // 重组
  const _tileModes = tileModes.map(item =>
    [5, 6].includes(item.modeId)
      ? {
        ...item,
        layerUrls: [
          {
            url: addParamUrl(item.modeId),
          },
        ],
      }
      : item
  )
  return _tileModes
}
