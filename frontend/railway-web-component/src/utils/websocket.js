export function createWebSocket(options) {
    // 配置对象，包含WebSocket的URL、是否自动重连、重连间隔、最大重连次数、心跳间隔以及各种事件处理函数
    const config = {
      url: '', // WebSocket服务器的URL地址
      reconnect: true, // 是否在连接关闭后尝试重新连接
      reconnectInterval: 3000, // 重连的时间间隔（毫秒）
      maxReconnectAttempts: 5, // 最大重连尝试次数
      heartBeatInterval: 25000, // 发送心跳包的时间间隔（毫秒）
      // onMessage: () => {}, // 收到消息时的处理函数
      // onOpen: () => {}, // 连接打开时的处理函数
      // onClose: () => {}, // 连接关闭时的处理函数
      ...options // 使用扩展运算符合并用户提供的选项
    }

    let ws = null // WebSocket实例
    let reconnectAttempts = 0 // 当前已尝试重连的次数
    let reconnectTimer = null // 用于延迟重连的定时器
    let heartBeatTimer = null // 发送心跳包的定时器
    const messageQueue = [] // 消息队列，用于存储在WebSocket连接未建立时发送的消息

    // 连接到WebSocket服务器
    const connect = () => {
      clearTimeout(reconnectTimer) // 清除之前的重连定时器
      ws = new WebSocket(config.url) // 创建新的WebSocket连接

      ws.onopen = () => { // 连接成功时的处理函数
        reconnectAttempts = 0 // 重置重连尝试次数
        startHeartBeat() // 开始发送心跳包
        flushMessageQueue() // 发送消息队列中的消息
        config.onOpen?.() // 调用用户提供的onOpen处理函数
      }

      ws.onmessage = (event) => { // 收到消息时的处理函数
        const message = JSON.parse(event.data) // 解析收到的消息
        if(message.type === 'heartbeat') {
            return; // 忽略心跳包
        }
        config.onMessage?.(message) // 调用用户提供的onMessage处理函数
      }

      ws.onerror = (error) => { // 连接发生错误时的处理函数
        console.error('[WebSocket] Error:', error) // 打印错误信息
        handleReconnect() // 处理重连逻辑
      }

      ws.onclose = (event) => { // 连接关闭时的处理函数
        config.onClose?.(event) // 调用用户提供的onClose处理函数
        if (!event.wasClean) { // 如果连接不是正常关闭
          handleReconnect() // 处理重连逻辑
        }
      }
    }

    // 开始发送心跳包
    const startHeartBeat = () => {
      heartBeatTimer = setInterval(() => { // 设置定时器，定时发送心跳包
        if (ws.readyState === WebSocket.OPEN) { // 检查WebSocket连接是否处于打开状态
          ws.send(JSON.stringify({ type: 'heartbeat' })) // 发送心跳包
        }
      }, config.heartBeatInterval) // 心跳包的发送间隔
    }

    // 发送消息队列中的所有消息
    const flushMessageQueue = () => {
      while (messageQueue.length > 0) { // 当消息队列中有消息时
        const msg = messageQueue.shift() // 取出队列中的第一个消息
        ws.send(JSON.stringify(msg)) // 发送消息
      }
    }

    // 处理重连逻辑
    const handleReconnect = () => {
      clearInterval(heartBeatTimer) // 清除心跳包定时器
      if (config.reconnect && reconnectAttempts < config.maxReconnectAttempts) { // 如果开启了重连功能且未超过最大重连次数
        const delay = Math.min(
          1000 * Math.pow(2, reconnectAttempts), // 计算延迟时间，指数增长
          30000 // 最大延迟时间为30秒
        )
        reconnectTimer = setTimeout(() => { // 设置重连定时器
          reconnectAttempts++ // 增加重连尝试次数
          connect() // 尝试重新连接
        }, delay) // 延迟时间
      }
    }

    // 发送消息到WebSocket服务器
    const send = (data) => {
      if (ws && ws.readyState === WebSocket.OPEN) { // 如果WebSocket连接存在且处于打开状态
        ws.send(JSON.stringify(data)) // 发送消息
      } else { // 否则
        messageQueue.push(data) // 将消息存入消息队列
      }
    }

    // 关闭WebSocket连接
    const close = (code = 1000, reason) => {
      clearTimeout(reconnectTimer) // 清除重连定时器
      clearInterval(heartBeatTimer) // 清除心跳包定时器
      if (ws) { // 如果WebSocket连接存在
        ws.close(code, reason) // 关闭连接
      }
    }

    // 初始化连接
    connect()

    // 返回WebSocket实例的操作方法
    return {
      send, // 发送消息的方法
      close, // 关闭连接的方法
      reconnect: connect // 重连的方法，实际上是调用connect方法
    }
  }
