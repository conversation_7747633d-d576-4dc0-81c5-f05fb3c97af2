@import '~@/assets/styles/px-to-rem';

// *,
// ::after,
// ::before {
//   -webkit-box-sizing: content-box;
//   box-sizing: content-box;
// }
// // FIXME集成工程临时覆盖的el-message样式。这个样式应当在组件库处理，这里是因为通用样式发版较慢的临时处置方案。
// // 组件库处理后请删除这部分
// .el-message {
//     &.el-message--error,
//     &.el-message--info,
//     &.el-message--success,
//     &.el-message--warning {
//         padding: px-to-rem(9) px-to-rem(6) px-to-rem(9) px-to-rem(12) !important;
//     }
// }
.el-message,
[data-theme='theme-wiseblue'] .el-message {
    // 按照要求，设计师认为所有提示的图标和字色保持一致
    &.el-message--error,
    &.el-message--info,
    &.el-message--success,
    &.el-message--warning {
        .el-message__icon {
            color: #4F9FFF;
            font-size: px-to-rem(14);
        }
        .el-message__content {
            color: #E8F3FE;
        }
        padding: px-to-rem(9) px-to-rem(6) px-to-rem(9) px-to-rem(12) !important;
        background-color: #172537 !important;
        border-color: #172537 !important;
    }
}

// FIXME集成工程临时覆盖的el-checkbox样式。这个样式应当在组件库处理，这里是因为通用样式发版较慢的临时处置方案。
// 组件库处理后请删除这部分
.common-iw-s {
  &.el-date-picker,
  &.el-date-range-picker,
  .el-date-editor {
    .el-date-picker__header,
    .el-date-range-picker__header {
      font-size: px-to-rem(18);
    }
  }
}
.common-iw-s .el-tree .el-tree-node__expand-icon.el-icon-caret-right::before {
  font-size: px-to-rem(20);
}
.mainbody,
.allalarm-container,
.paranoma-container {
  .common-iw-s {

    .el-tree {
      .el-tree-node__expand-icon {
        margin-right: px-to-rem(6);
      }
    }
  
    .el-select-dropdown__item {
      height: px-to-rem(32);
    }
  
      .el-checkbox {
          .el-checkbox__input {
            .el-checkbox__inner {
              width: px-to-rem(16);
              height: px-to-rem(16);
              border-radius: px-to-rem(3);
            }
  
            &.is-indeterminate {
              .el-checkbox__inner {
                border: none;
                &::before {
                  position: initial;
                  background-color: transparent;
                  font-family: 'iconfont_p';
                  font-size: px-to-rem(16);
                  content: '\ed95';
                  border: none;
                  transform: none;
                  color: var(--iw-text-color);
                }
              }
            }
          }
      
          &.is-checked {
            .el-checkbox__input {
              .el-checkbox__inner {
                border: none;
                &::after {
                  top: 0;
                  left: 0;
                  font-family: 'iconfont_p';
                  font-size: px-to-rem(16);
                  content: '\ed96';
                  border: none;
                  transform: none;
                  color: var(--iw-text-color);
                }
              }
            }
          }
      }
  
  
    // // 测距侧面tooltip覆写
    //   &.small-box-startTip,&.small-box-measureTip {
    //   display: flex;
    //   align-items: center;
    //   padding: px-to-rem(6) px-to-rem(12);
    //   border-radius: px-to-rem(4);
    // }
  
    // &.small-box-measureTip {
    //   .iconfont {
    //     cursor: pointer;
    //     &.trash {
    //       margin-left: px-to-rem(6);
    //     }
    //   }
    // }
  }  
} 