<?xml version="1.0" encoding="UTF-8"?>
<svg width="29px" height="30px" viewBox="0 0 29 30" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <defs>
        <filter x="-47.1%" y="-44.4%" width="194.1%" height="188.9%" filterUnits="objectBoundingBox" id="filter-1">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.309803922   0 0 0 0 0.623529412   0 0 0 0 1  0 0 0 0.7 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="99.8535156%" id="linearGradient-2">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#BCDBFF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="99.8535156%" id="linearGradient-3">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#BCDBFF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="99.8535156%" id="linearGradient-4">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#BCDBFF" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="全景展示-大屏" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="1-4全景展示-选择某个区域" transform="translate(-1450.000000, -188.000000)" fill-rule="nonzero">
            <g id="热力图层" transform="translate(1444.000000, 183.000000)">
                <g id="icon_热力图_18_n" filter="url(#filter-1)" transform="translate(12.000000, 11.000000)">
                    <path d="M4.9077381,13.187234 C4.83184524,13.187234 4.75595238,13.2 4.68005952,13.212766 C4.50297619,12.4595745 3.81994048,11.9106383 2.99776786,11.9106383 C2.04910714,11.9106383 1.27752976,12.6638298 1.27752976,13.5957447 C1.27752976,14.5276596 2.04910714,15.2808511 2.99776786,15.2808511 C3.07366071,15.2808511 3.14955357,15.2680851 3.22544643,15.2553191 C3.40252976,16.0085106 4.08556548,16.5702128 4.9077381,16.5702128 C5.85639881,16.5702128 6.62797619,15.8170213 6.62797619,14.8851064 C6.62797619,13.9531915 5.85639881,13.187234 4.9077381,13.187234 Z M15.1659226,5.00425532 C15.1659226,3.14042553 13.6227679,1.6212766 11.7254464,1.6212766 C10.3087798,1.6212766 9.10714286,2.45106383 8.57589286,3.63829787 C8.19642857,3.43404255 7.77901786,3.30638298 7.3110119,3.30638298 C5.88169643,3.30638298 4.73065476,4.44255319 4.73065476,5.84680851 C4.73065476,7.25106383 5.86904762,8.37446809 7.2983631,8.37446809 C8.12053571,8.37446809 8.84151786,7.99148936 9.30952381,7.41702128 C9.92931548,8.01702128 10.7767857,8.37446809 11.7127976,8.37446809 C13.6227679,8.37446809 15.1659226,6.86808511 15.1659226,5.00425532 L15.1659226,5.00425532 Z" id="形状" fill="url(#linearGradient-2)"></path>
                    <path d="M12.0922619,14.8723404 C12.0922619,15.802999 12.8624387,16.5574468 13.8125,16.5574468 C14.7625613,16.5574468 15.5327381,15.802999 15.5327381,14.8723404 C15.5327381,13.9416819 14.7625613,13.187234 13.8125,13.187234 C12.8624387,13.187234 12.0922619,13.9416819 12.0922619,14.8723404 Z" id="路径" fill="url(#linearGradient-3)"></path>
                    <path d="M5.36309524,11.7702128 C4.79389881,11.0553191 3.92113095,10.6340426 2.99776786,10.6340426 C1.34077381,10.6340426 0,11.9617021 0,13.5829787 C0,14.8978723 0.872767857,16.0340426 2.13764881,16.4042553 C2.70684524,17.387234 3.74404762,18 4.9077381,18 C6.66592262,18 8.0952381,16.5957447 8.0952381,14.8723404 C8.08258929,13.3021277 6.91889881,11.987234 5.36309524,11.7702128 L5.36309524,11.7702128 Z M4.9077381,17.3617021 C3.94642857,17.3617021 3.0610119,16.8382979 2.63095238,15.9957447 L2.56770833,15.8680851 L2.42857143,15.8297872 C1.37872024,15.5744681 0.645089286,14.6553191 0.645089286,13.5957447 C0.645089286,12.3319149 1.70758929,11.2978723 2.99776786,11.2978723 C3.76934524,11.2978723 4.49032738,11.6680851 4.93303571,12.2808511 L5.02157738,12.3957447 L5.16071429,12.4085106 C6.47619048,12.5361702 7.45014881,13.5957447 7.45014881,14.8851064 C7.45014881,16.2382979 6.31175595,17.3617021 4.9077381,17.3617021 L4.9077381,17.3617021 Z M13.8125,11.7446809 C12.0543155,11.7446809 10.625,13.1489362 10.625,14.8723404 C10.625,16.5957447 12.0543155,18 13.8125,18 C15.5706845,18 17,16.5957447 17,14.8723404 C17,13.1489362 15.5706845,11.7446809 13.8125,11.7446809 Z M13.8125,17.3617021 C12.4084821,17.3617021 11.2700893,16.2510638 11.2700893,14.8723404 C11.2700893,13.493617 12.4084821,12.3829787 13.8125,12.3829787 C15.2165179,12.3829787 16.3549107,13.493617 16.3549107,14.8723404 C16.3549107,16.2510638 15.2165179,17.3617021 13.8125,17.3617021 Z M16.8102679,5.00425532 C16.8102679,2.24680851 14.5208333,0 11.7254464,0 C10.156994,0 8.66443452,0.714893617 7.703125,1.92765957 C7.5639881,1.91489362 7.4375,1.90212766 7.2983631,1.90212766 C5.08482143,1.90212766 3.28869048,3.66382979 3.28869048,5.83404255 C3.28869048,8.00425532 5.08482143,9.76595745 7.2983631,9.76595745 C7.95610119,9.76595745 8.58854167,9.61276596 9.1703869,9.31914894 C9.94196429,9.75319149 10.827381,9.99574468 11.7254464,9.99574468 C14.5334821,9.99574468 16.8102679,7.76170213 16.8102679,5.00425532 L16.8102679,5.00425532 Z M9.18303571,8.5787234 L9.01860119,8.66808511 C8.5,8.96170213 7.90550595,9.12765957 7.3110119,9.12765957 C5.4516369,9.12765957 3.94642857,7.64680851 3.94642857,5.83404255 C3.94642857,4.0212766 5.4516369,2.54042553 7.3110119,2.54042553 C7.47544643,2.54042553 7.63988095,2.55319149 7.80431548,2.5787234 L7.99404762,2.60425532 L8.1078869,2.45106383 C8.94270833,1.31489362 10.296131,0.638297872 11.7254464,0.638297872 C14.1793155,0.638297872 16.1778274,2.59148936 16.1778274,4.99148936 C16.1778274,7.39148936 14.1793155,9.35744681 11.7254464,9.35744681 C10.8779762,9.35744681 10.0558036,9.12765957 9.34747024,8.68085106 L9.18303571,8.5787234 L9.18303571,8.5787234 Z" id="形状" fill="url(#linearGradient-4)"></path>
                </g>
            </g>
        </g>
    </g>
</svg>
