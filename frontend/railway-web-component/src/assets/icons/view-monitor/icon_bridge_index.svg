<?xml version="1.0" encoding="UTF-8"?>
<svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>icon_桥梁_30</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="18.9631296%" y2="88.3962268%" id="linearGradient-1">
            <stop stop-color="#87CDFF" stop-opacity="0.1" offset="0%"></stop>
            <stop stop-color="#000000" stop-opacity="0.549455549" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="3.23029479%" y1="50%" x2="96.7782443%" y2="50%" id="linearGradient-2">
            <stop stop-color="#46F3E5" offset="0%"></stop>
            <stop stop-color="#00D4FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="3.23029479%" y1="50%" x2="96.7782443%" y2="50%" id="linearGradient-3">
            <stop stop-color="#46F3E5" offset="0%"></stop>
            <stop stop-color="#00D4FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="3.23029479%" y1="50%" x2="96.7782443%" y2="50%" id="linearGradient-4">
            <stop stop-color="#46F3E5" offset="0%"></stop>
            <stop stop-color="#00D4FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="6.54172289%" y1="21.875%" x2="100%" y2="31.0788222%" id="linearGradient-5">
            <stop stop-color="#F8F091" offset="0%"></stop>
            <stop stop-color="#FFC700" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="全景展示-大屏" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="1-4全景展示-选择某个区域" transform="translate(-753.000000, -1040.000000)">
            <g id="底部" transform="translate(0.000000, 1026.000000)">
                <g id="icon_桥梁_30" transform="translate(754.000000, 15.000000)">
                    <g id="编组-4">
                        <g id="fanhuishouye_icon">
                            <circle id="椭圆形" fill="url(#linearGradient-1)" cx="15" cy="15" r="15"></circle>
                            <path d="M3.50858461,5.35853741 C1.31884122,7.96570036 0,11.3288342 0,15 C0,23.2842712 6.71572875,30 15,30 C17.8716538,30 20.5548334,29.1930462 22.8351495,27.7935279" id="路径" stroke="url(#linearGradient-2)" stroke-width="0.75" stroke-linecap="round"></path>
                            <path d="M27.2352543,23.67955 C28.9763096,21.2296793 30,18.2343875 30,15 C30,6.71572875 23.2842712,0 15,0 C12.4837424,0 10.1121945,0.619576219 8.02959195,1.71449312" id="路径" stroke="url(#linearGradient-3)" stroke-width="0.75" stroke-linecap="round"></path>
                            <circle id="椭圆形" fill-opacity="0.1" fill="#003F8C" cx="15" cy="15" r="13.5"></circle>
                            <circle id="椭圆形" fill-opacity="0.588203057" fill="#000000" cx="15" cy="15" r="12"></circle>
                        </g>
                        <path d="M21.9999624,13.6368586 L21.9999624,19.0912391 C22.0043213,19.585772 21.6292391,19.9918334 21.1600564,20 L19.4710868,20 C19.4798813,17.3776144 17.4835465,15.2361508 14.9947238,15.1998548 C12.5072203,15.2375119 10.5113253,17.3780681 10.5205594,20 L8.85093759,20 C8.38131523,19.9918334 8.00579322,19.585772 8.01101495,19.0907854 L8.01101495,13.6373123 C8.00623296,13.1423257 8.38131523,12.7362642 8.85093759,12.7280976 L21.1600564,12.7280976 C21.6292391,12.7362642 22.0043213,13.1423257 21.9999624,13.6368586 Z M21.709708,10.9083072 L8.28017933,10.9083072 C8.20451741,10.9069842 8.13256003,10.8742772 8.08054586,10.8175673 C8.02781369,10.7602075 7.99893109,10.6838328 8.00003722,10.604782 L8.00003722,10.3035253 C7.99881557,10.2243182 8.0277064,10.1477589 8.08054586,10.0902863 C8.13263426,10.0337436 8.20458212,10.0012045 8.28017933,10 L21.7092683,10 C21.7847768,10.0014442 21.8565541,10.0341414 21.9084621,10.09074 C21.9612286,10.1474525 21.9898105,10.2241278 21.9889822,10.3035253 L21.9889822,10.6065968 C21.9918804,10.7695375 21.8667299,10.9043425 21.7088286,10.9083072 L21.709708,10.9083072 Z M15.4260904,12.7276439 L14.5862227,12.7276439 L14.5862227,10.9083072 L15.4260904,10.9083072 L15.4260904,12.7276439 Z M18.0842058,12.7276439 L17.2443381,12.7276439 L17.2443381,10.9083072 L18.0842058,10.9083072 L18.0842058,12.7276439 Z M20.7418814,12.7276439 L19.9020137,12.7276439 L19.9020137,10.9083072 L20.7418814,10.9083072 L20.7418814,12.7276439 Z M12.7684148,12.7276439 L11.9285471,12.7276439 L11.9285471,10.9083072 L12.7684148,10.9083072 L12.7684148,12.7276439 Z M10.1107392,12.7276439 L9.27087145,12.7276439 L9.27087145,10.9083072 L10.1107392,10.9083072 L10.1107392,12.7276439 Z" id="icon_桥梁_18_n" fill="url(#linearGradient-4)" fill-rule="nonzero"></path>
                    </g>
                    <path d="M6.53163749,3.25065556 L7.33094359,4.4496147 C7.44582576,4.62193795 7.39926056,4.85476396 7.2269373,4.96964613 C7.16533698,5.01071301 7.09295909,5.03262727 7.01892472,5.03262727 L5.42031254,5.03262727 C5.21320576,5.03262727 5.04531254,4.86473405 5.04531254,4.65762727 C5.04531254,4.5835929 5.0672268,4.51121502 5.10829368,4.4496147 L5.90759977,3.25065556 C6.02248195,3.0783323 6.25530795,3.0317671 6.42763121,3.14664927 C6.46882582,3.17411235 6.50417442,3.20946095 6.53163749,3.25065556 Z" id="三角形" fill="url(#linearGradient-5)" transform="translate(6.219619, 3.907627) rotate(139.000000) translate(-6.219619, -3.907627) "></path>
                    <path d="M25.1880702,24.7905634 L25.9873763,25.9895226 C26.1022585,26.1618458 26.0556933,26.3946718 25.88337,26.509554 C25.8217697,26.5506209 25.7493918,26.5725351 25.6753575,26.5725351 L24.0767453,26.5725351 C23.8696385,26.5725351 23.7017453,26.4046419 23.7017453,26.1975351 C23.7017453,26.1235008 23.7236595,26.0511229 23.7647264,25.9895226 L24.5640325,24.7905634 C24.6789147,24.6182402 24.9117407,24.571675 25.0840639,24.6865571 C25.1252585,24.7140202 25.1606072,24.7493688 25.1880702,24.7905634 Z" id="三角形" fill="url(#linearGradient-5)" transform="translate(24.876051, 25.447535) rotate(-41.000000) translate(-24.876051, -25.447535) "></path>
                </g>
            </g>
        </g>
    </g>
</svg>