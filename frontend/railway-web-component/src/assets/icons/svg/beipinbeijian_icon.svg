<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg">
    <title>beipin<PERSON>jian_icon</title>
    <g id="养护管理" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="beipinbeijian_icon">
            <g transform="translate(3.999600, 4.013400)" fill="currentColor">
                <polygon id="路径"
                         points="13.2626016 9.03626084 8.45639059 9.03626084 8.45639059 4.60832612 10.3735217 4.60832612 10.3735217 5.86049136 11.3463621 5.86049136 11.3463621 4.60832612 13.2626016 4.60832612"></polygon>
                <polygon id="路径"
                         points="7.46591414 9.03626084 2.65973883 9.03626084 2.65973883 4.60832612 4.57776158 4.60832612 4.57776158 5.86049136 5.54881867 5.86049136 5.54881867 4.60832612 7.46591414 4.60832612"></polygon>
                <polygon id="路径"
                         points="13.2626016 14.2225492 8.45639059 14.2225492 8.45639059 9.796469 10.3735217 9.796469 10.3735217 11.0486164 11.3463621 11.0486164 11.3463621 9.796469 13.2626016 9.796469"></polygon>
                <polygon id="路径"
                         points="7.46591414 14.2225492 2.65973883 14.2225492 2.65973883 9.796469 4.57776158 9.796469 4.57776158 11.0486164 5.54881867 11.0486164 5.54881867 9.796469 7.46591414 9.796469"></polygon>
                <polygon id="路径"
                         points="0 3.70105512 0 5.35277053 7.92741369 1.76131264 16 5.35277053 16 3.79173229 8.00047255 0"></polygon>
            </g>
        </g>
    </g>
</svg>
