<?xml version="1.0" encoding="UTF-8"?>
<svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>icon_间隔播放_16_n</title>
    <g id="广播历史" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="广播配置/3.3广播历史_详情" transform="translate(-610.000000, -1583.000000)" fill="#000000" fill-rule="nonzero">
            <g id="弹框备份-3" transform="translate(60.000000, 1356.000000)">
                <g id="编组-3备份" transform="translate(12.000000, 193.000000)">
                    <g id="icon_间隔播放_16_n" transform="translate(538.000000, 34.000000)">
                        <rect id="矩形" opacity="0" x="0" y="0" width="16" height="16"></rect>
                        <path d="M11.4522667,1.6048 C13.0441112,1.6048 14.3346388,2.89508878 14.3349337,4.48693333 L14.3349337,11.4522667 C14.3352164,12.2168842 14.0315987,12.9502662 13.4909324,13.4909324 C12.9502662,14.0315987 12.2168842,14.3352164 11.4522667,14.3349337 L4.48693333,14.3349337 C3.72240824,14.3350749 2.98916327,14.0313947 2.4486124,13.4907438 C1.90806154,12.9500929 1.60451699,12.2167917 1.6047996,11.4522667 L1.6047996,4.48693333 C1.60509445,2.89529703 2.89529703,1.60509445 4.48693333,1.6048 L11.4522667,1.6048 Z M5.33333333,5.19146667 L5.33333333,10.8085333 C5.33333333,11.6752 5.94986666,11.9888 6.70986667,11.5072 L10.8725333,8.87146667 C11.632,8.3904 11.6325333,7.6096 10.8725333,7.128 L6.70986667,4.4928 C5.94986667,4.01066667 5.33333333,4.32426667 5.33333333,5.19146667 Z M9.06666667,7.46666667 C9.38666667,7.46666667 9.56266667,7.61066667 9.59466667,7.89866667 L9.6,8 C9.6,8.35555555 9.42222222,8.53333333 9.06666667,8.53333333 L6.4,8.53333333 C6.08,8.53333333 5.904,8.38933333 5.87200001,8.10133333 L5.86666667,8 C5.86666667,7.64444445 6.04444445,7.46666667 6.4,7.46666667 L9.06666667,7.46666667 Z" id="形状"></path>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>