<?xml version="1.0" encoding="UTF-8"?>
<svg width="14px" height="16px" viewBox="0 0 14 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>icon_云广播_12_s</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#BCDBFF" offset="100%"></stop>
        </linearGradient>
        <path d="M15.2836925,6.95175795 L15.405472,7.0838479 L15.471962,7.16024706 C15.882874,7.64080498 16.2523585,8.24985639 16.5306714,8.94690202 C17.3381663,10.9651228 17.0774899,12.9582015 15.950053,13.3975017 L15.841056,13.4326896 C15.7171525,13.4683098 15.5882897,13.484387 15.456501,13.4819251 C15.2625972,13.4945074 15.0430829,13.495623 14.8236019,13.4929197 L14.0962755,13.4800083 L14.0962755,13.4800083 L13.8451953,13.4829847 C13.7813591,13.4887055 13.7124064,13.4796598 13.6395452,13.4572901 C13.0117643,13.4795646 12.4137773,13.5379956 11.8403401,13.6220101 C12.3991912,14.3383316 12.9112148,15.123483 13.1285527,15.7153488 L13.1808737,15.8706052 C13.2610898,16.0743013 13.3145823,16.2633523 13.3335659,16.430547 L13.3626153,16.7182699 L13.3794524,16.9660361 C13.4113175,17.6526128 13.2738936,17.8431541 12.9273893,17.9280907 L12.8040896,17.9530272 L12.6649749,17.9733545 L12.4661332,17.9935339 C11.9456624,18.0305059 11.5163914,17.920327 11.4905861,17.3646097 L11.4903106,17.2611488 C11.4983181,17.0341838 11.45931,16.6624961 11.3745964,16.2284298 L11.3379793,16.2513527 C11.3263804,16.0948489 11.2932454,15.8728904 11.2324837,15.6117673 C11.1081074,15.14294 10.9399427,14.6504731 10.7290151,14.2127468 C10.6691747,14.0961903 10.6049289,13.9789078 10.5353634,13.8631469 L10.1878547,13.9423984 C9.65909372,13.7014919 9.06736296,12.9687155 8.680747,12.0032608 C8.29552009,11.0378062 8.22097498,10.1043505 8.43951716,9.57225332 C9.45344168,9.00628484 10.5128517,8.29858563 11.550384,7.37795413 C11.5645415,7.36049854 11.5792601,7.34643444 11.5944886,7.33461102 L12.0112744,6.91812969 C12.1350575,6.79563895 12.2685799,6.66650433 12.4007022,6.54677711 L12.5455676,6.41775964 L12.5589449,6.40389812 C12.6634191,6.2932953 12.7864577,6.2017811 12.922349,6.13323576 L13.0270512,6.08619405 C13.70675,5.8216284 14.5469914,6.18176681 15.2836925,6.95175795 Z M7.92001644,9.85384426 C7.79222482,10.4951951 7.88482745,11.3363109 8.22375306,12.1829122 C8.56314167,13.0304278 9.07708623,13.7069774 9.61418146,14.0895937 L9.26414354,14.1846764 C8.6205553,14.4351826 7.72693997,13.7056061 7.26763097,12.5550145 C6.80739593,11.404423 6.95556012,10.2689166 7.59822233,10.0184104 L7.92001644,9.85384426 Z M13.2159605,6.55932168 C12.5751504,6.80845653 12.457082,8.77182193 13.0664073,10.3612565 C13.7025873,12.0192603 15.1119992,13.177623 15.7602176,12.9248312 C16.6408686,12.5819851 16.7844026,10.8782685 16.0820117,9.12152496 C15.3791578,7.36386722 14.0956854,6.21647557 13.2159605,6.55932168 Z M13.6007244,9.00084313 C14.1734565,9.00145205 14.6554266,9.42443967 14.7231669,9.98592349 C14.7909073,10.5474073 14.423057,11.070332 13.8664939,11.2037437 C13.641937,10.8999784 13.4595874,10.5678957 13.3243056,10.2163469 C13.1933626,9.86888664 13.1035589,9.50764136 13.056684,9.1398101 C13.2182756,9.05021299 13.4039438,9.00084313 13.6007244,9.00084313 Z" id="path-2"></path>
        <filter x="-30.0%" y="-25.0%" width="160.0%" height="150.0%" filterUnits="objectBoundingBox" id="filter-3">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.309803922   0 0 0 0 0.623529412   0 0 0 0 1  0 0 0 0.7 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="综合监测-大屏" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="icon_云广播_12_s" transform="translate(-5.000000, -4.000000)">
            <use fill="black" fill-opacity="1" filter="url(#filter-3)" xlink:href="#path-2"></use>
            <use fill="url(#linearGradient-1)" fill-rule="evenodd" xlink:href="#path-2"></use>
        </g>
    </g>
</svg>