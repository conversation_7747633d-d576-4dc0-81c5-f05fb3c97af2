<?xml version="1.0" encoding="UTF-8"?>
<svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>icon_摄像机_12_n</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#BCDBFF" offset="100%"></stop>
        </linearGradient>
        <path d="M25.2751553,26.328 L25.2751553,24.528 L24,24.2871429 L24,30 L25.2751553,29.5122857 L25.2751553,27.5528571 L27.299293,27.5528571 L28.1991002,26.0828571 L27.0739127,25.3482857 L26.5477398,26.328 L25.2751553,26.328 Z M36,24.528 L26.8493894,18 L25.8013283,18 L24.3762051,20.4497143 L24.3762051,21.2657143 L32.5473113,26.9811429 L33.2225952,26.8954286 L36,24.528 Z M24.3727773,21.756 L24.3727773,23.0571429 L32.6227237,28.6911429 L33.372563,28.6911429 L33.7479112,28.2857143 L34.1232593,27.0608571 C34.1232593,27.0608571 33.0734842,27.8777143 32.9980718,27.8777143 C32.6235807,27.5528571 24.3744912,21.756 24.3744912,21.756 L24.3727773,21.756 Z" id="path-2"></path>
        <filter x="-25.0%" y="-25.0%" width="150.0%" height="150.0%" filterUnits="objectBoundingBox" id="filter-3">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.309803922   0 0 0 0 0.623529412   0 0 0 0 1  0 0 0 0.7 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="综合监测-大屏" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="详情多个页签" transform="translate(-50.000000, -61.000000)" fill-rule="nonzero">
            <g id="icon_摄像机_12_n" transform="translate(28.000000, 45.000000)">
                <use fill="black" fill-opacity="1" filter="url(#filter-3)" xlink:href="#path-2"></use>
                <use fill="url(#linearGradient-1)" xlink:href="#path-2"></use>
            </g>
        </g>
    </g>
</svg>