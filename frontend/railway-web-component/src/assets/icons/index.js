/**
 * 引入并注册SVG图标组件。
 *
 * 此脚本负责将SVG图标组件导入并注册到Vue全局，使它们可以在应用程序中随时使用。
 * 它通过动态加载SVG图标，将它们作为Vue组件进行注册，以便在需要时可以方便地插入和使用。
 */
import Vue from 'vue';
import SvgIcon from './../../components/common/SvgIcon.vue';

// 注册SVG图标组件到全局
Vue.component("svg-icon", SvgIcon);

/**
 * 动态加载并注册所有SVG图标。
 *
 * @param {Function} requireContext - Webpack的require.context函数，用于动态加载指定目录下的所有模块。
 * @returns {Array} 返回一个包含所有加载模块的数组。
 *
 * 此函数通过调用require.context来动态加载指定目录下所有后缀为.svg的文件，并将它们注册为Vue组件。
 * 这样做的好处是可以在不预先知道所有图标名称的情况下，将大量SVG图标动态地引入到项目中。
 */
const requireAll = (requireContext) =>
    requireContext.keys().map(requireContext);

// 动态加载并注册SVG图标
const req = require.context("./svg", false, /\.svg$/);
const req2 = require.context("./maintenance", false, /\.svg$/);
const req3 = require.context("./igscreen", false, /\.svg$/);
const req4 = require.context('./view-monitor', false, /\.svg$/);

// 调用函数，加载并注册所有SVG图标
requireAll(req);
requireAll(req2);
requireAll(req3);
requireAll(req4);

