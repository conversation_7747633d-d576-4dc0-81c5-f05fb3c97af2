<?xml version="1.0" encoding="UTF-8"?>
<svg width="74px" height="84px" viewBox="0 0 74 84" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>icon_摄像机_60_s</title>
    <defs>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="0%" id="linearGradient-1">
            <stop stop-color="#058FC5" offset="0%"></stop>
            <stop stop-color="#02B7FF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="0.871394231%" id="linearGradient-2">
            <stop stop-color="#0CBAFF" offset="0%"></stop>
            <stop stop-color="#00B7FF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <filter x="-11.8%" y="-25.3%" width="123.5%" height="150.5%" filterUnits="objectBoundingBox" id="filter-3">
            <feGaussianBlur stdDeviation="2" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="0%" id="linearGradient-4">
            <stop stop-color="#058FC5" offset="0%"></stop>
            <stop stop-color="#02B7FF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="0.871394231%" id="linearGradient-5">
            <stop stop-color="#0CBAFF" offset="0%"></stop>
            <stop stop-color="#00B7FF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="92.9881701%" y1="100%" x2="7.0118299%" y2="0%" id="linearGradient-6">
            <stop stop-color="#00BAFF" offset="0%"></stop>
            <stop stop-color="#1B7CEC" offset="100%"></stop>
        </linearGradient>
        <path d="M13.4004142,31.208 L13.4004142,26.408 L10,25.7657143 L10,41 L13.4004142,39.6994286 L13.4004142,34.4742857 L18.7981147,34.4742857 L21.1976005,30.5542857 L18.1971006,28.5954286 L16.7939727,31.208 L13.4004142,31.208 Z M42,26.408 L17.5983718,9 L14.8035421,9 L11.0032136,15.5325714 L11.0032136,17.7085714 L32.7928301,32.9497143 L34.5935871,32.7211428 L42,26.408 Z M10.9940727,19.016 L10.9940727,22.4857143 L32.9939299,37.5097143 L34.9935014,37.5097143 L35.9944298,36.4285714 L36.9953581,33.1622857 C36.9953581,33.1622857 34.195958,35.3405714 33.9948582,35.3405714 C32.9962151,34.4742857 10.9986432,19.016 10.9986432,19.016 L10.9940727,19.016 Z" id="path-7"></path>
        <filter x="-1.6%" y="-1.6%" width="103.1%" height="106.3%" filterUnits="objectBoundingBox" id="filter-8">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.2 0" type="matrix" in="shadowOffsetOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="云广播绑定" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="资源管理/1.8云广播绑定_绑定" transform="translate(-1085.000000, -519.000000)">
            <g id="弹框" transform="translate(460.000000, 140.000000)">
                <g id="位图" transform="translate(334.000000, 159.000000)">
                    <g id="icon_摄像机_60_s" transform="translate(298.000000, 220.000000)">
                        <rect id="矩形" x="0" y="0" width="60" height="60"></rect>
                        <path d="M59.5031405,63.1430484 C59.5031405,70.7959102 46.1844335,77 29.7515702,77 C13.3187069,77 0,70.7959102 0,63.1430484 C0,55.4901866 13.3187069,49.2860968 29.7515702,49.2860968 C46.1844335,49.2860968 59.5031405,55.4901866 59.5031405,63.1430484 C59.5031405,63.1430484 59.5031405,63.1430484 59.5031405,63.1430484 Z" id="椭圆_7_拷贝" stroke="url(#linearGradient-2)" stroke-width="2" fill="url(#linearGradient-1)" opacity="0.5" filter="url(#filter-3)"></path>
                        <path d="M29.7515702,49 C40.6125511,49 49.4171423,53.1007685 49.4171423,58.1593075 C49.4171423,63.2178465 40.6125511,67.3186151 29.7515702,67.3186151 C18.8905894,67.3186151 10.0859982,63.2178465 10.0859982,58.1593075 C10.0859982,53.1007685 18.8905894,49 29.7515702,49 C29.7515702,49 29.7515702,49 29.7515702,49 Z" id="椭圆_7" stroke="url(#linearGradient-5)" stroke-width="2" fill="url(#linearGradient-4)"></path>
                        <g id="icon_广播_42_n">
                            <rect id="矩形" x="0" y="0" width="60" height="60"></rect>
                            <g id="图标" transform="translate(3.750000, 0.000000)">
                                <ellipse id="椭圆形" fill-opacity="0.25" fill="#9B9B9B" cx="26.25" cy="56.25" rx="9.375" ry="3.75"></ellipse>
                                <path d="M26.25,0.5 C33.3606662,0.5 39.7981662,3.38216692 44.4579996,8.04200038 C49.1178331,12.7018338 52,19.1393338 52,26.25 C52,32.656812 49.6602327,38.5171666 45.7884426,43.0230633 C41.8157977,47.6463324 36.2304986,50.844028 29.9049015,51.7426685 L29.9049015,51.7426685 L26.7112676,56.5317304 C26.5102116,56.6825612 26.3293659,56.7670823 26.1659963,56.776646 L26.1659963,56.776646 L22.5947527,51.7426193 C16.2692979,50.8439115 10.6841299,47.6462481 6.7115574,43.0230633 C2.83976731,38.5171666 0.5,32.656812 0.5,26.25 C0.5,19.1393338 3.38216692,12.7018338 8.04200038,8.04200038 C12.7018338,3.38216692 19.1393338,0.5 26.25,0.5 Z" id="形状结合" stroke="url(#linearGradient-6)" fill="#32C5FF"></path>
                                <g id="icon_摄像机_18_n" fill-rule="nonzero">
                                    <use fill="black" fill-opacity="1" filter="url(#filter-8)" xlink:href="#path-7"></use>
                                    <use fill="#FFFFFF" xlink:href="#path-7"></use>
                                </g>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>