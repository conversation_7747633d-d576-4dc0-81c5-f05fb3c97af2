<?xml version="1.0" encoding="UTF-8"?>
<svg width="42px" height="42px" viewBox="0 0 42 42" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>icon_摄像机在线_42_n</title>
    <defs>
        <linearGradient x1="92.9881701%" y1="100%" x2="7.0118299%" y2="0%" id="linearGradient-1">
            <stop stop-color="#00BAFF" offset="0%"></stop>
            <stop stop-color="#1B7CEC" offset="100%"></stop>
        </linearGradient>
        <path d="M8.92531064,22.656 L8.92531064,19.056 L6.375,18.5742857 L6.375,30 L8.92531064,29.0245714 L8.92531064,25.1057143 L12.973586,25.1057143 L14.7732004,22.1657143 L12.5228255,20.6965714 L11.4704795,22.656 L8.92531064,22.656 Z M30.375,19.056 L12.0737788,6 L9.97765658,6 L7.12741019,10.8994286 L7.12741019,12.5314286 L23.4696226,23.9622857 L24.8201903,23.7908571 L30.375,19.056 Z M7.12055453,13.512 L7.12055453,16.1142857 L23.6204474,27.3822857 L25.120126,27.3822857 L25.8708223,26.5714286 L26.6215186,24.1217143 C26.6215186,24.1217143 24.5219685,25.7554286 24.3711437,25.7554286 C23.6221613,25.1057143 7.12398237,13.512 7.12398237,13.512 L7.12055453,13.512 Z" id="path-2"></path>
        <filter x="-2.1%" y="-2.1%" width="104.2%" height="108.3%" filterUnits="objectBoundingBox" id="filter-3">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.2 0" type="matrix" in="shadowOffsetOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="云广播绑定" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="资源管理/1.2云广播绑定_绑定" transform="translate(-1124.000000, -455.000000)">
            <g id="弹框" transform="translate(460.000000, 140.000000)">
                <g id="位图" transform="translate(334.000000, 159.000000)">
                    <g id="icon_摄像机在线_42_n" transform="translate(330.000000, 156.000000)">
                        <rect id="矩形" x="0" y="0" width="42" height="42"></rect>
                        <g id="图标" transform="translate(2.625000, 0.000000)">
                            <ellipse id="椭圆形" fill-opacity="0.25" fill="#9B9B9B" cx="18.375" cy="39.375" rx="6.5625" ry="2.625"></ellipse>
                            <path d="M18.375,0.5 C23.311045,0.5 27.779795,2.50072752 31.0145337,5.73546629 C34.2492725,8.97020505 36.25,13.438955 36.25,18.375 C36.25,22.8223175 34.6259035,26.8903317 31.9383278,30.0181688 C29.1668829,33.243614 25.2647733,35.4696587 20.8465844,36.0805749 L20.8465844,36.0805749 L18.8362953,39.0942096 C18.6352282,39.2450534 18.4543734,39.3295818 18.2909963,39.339146 L18.2909963,39.339146 L15.9028664,36.0804989 C11.4849033,35.4694742 7.58300223,33.2434803 4.81167221,30.0181688 C2.12409653,26.8903317 0.5,22.8223175 0.5,18.375 C0.5,13.438955 2.50072752,8.97020505 5.73546629,5.73546629 C8.97020505,2.50072752 13.438955,0.5 18.375,0.5 Z" id="形状结合" stroke="url(#linearGradient-1)" fill="#32C5FF"></path>
                            <g id="icon_摄像机_18_n" fill-rule="nonzero">
                                <use fill="black" fill-opacity="1" filter="url(#filter-3)" xlink:href="#path-2"></use>
                                <use fill="#FFFFFF" xlink:href="#path-2"></use>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>