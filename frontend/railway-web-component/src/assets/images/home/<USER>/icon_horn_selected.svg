<?xml version="1.0" encoding="UTF-8"?>
<svg width="74px" height="84px" viewBox="0 0 74 84" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>icon_广播_60_s</title>
    <defs>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="0%" id="linearGradient-1">
            <stop stop-color="#058FC5" offset="0%"></stop>
            <stop stop-color="#02B7FF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="0.871394231%" id="linearGradient-2">
            <stop stop-color="#0CBAFF" offset="0%"></stop>
            <stop stop-color="#00B7FF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <filter x="-11.8%" y="-25.3%" width="123.5%" height="150.5%" filterUnits="objectBoundingBox" id="filter-3">
            <feGaussianBlur stdDeviation="2" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="0%" id="linearGradient-4">
            <stop stop-color="#058FC5" offset="0%"></stop>
            <stop stop-color="#02B7FF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="0.871394231%" id="linearGradient-5">
            <stop stop-color="#0CBAFF" offset="0%"></stop>
            <stop stop-color="#00B7FF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="92.9881701%" y1="100%" x2="7.0118299%" y2="0%" id="linearGradient-6">
            <stop stop-color="#00BAFF" offset="0%"></stop>
            <stop stop-color="#1B7CEC" offset="100%"></stop>
        </linearGradient>
        <path d="M24.8510774,2.88032011 L25.216416,3.28006602 L25.415886,3.511274 C26.648622,4.96559402 27.7570754,6.8087759 28.5920143,8.9182561 C31.014499,15.0260295 30.2324698,21.0577152 26.850159,22.3871762 L26.523168,22.4936658 C26.1514575,22.6014637 25.7648691,22.6501186 25.3695031,22.6426681 C24.7877917,22.6807462 24.1292488,22.6841224 23.4708056,22.6759412 L21.2888266,22.6368671 L21.2888266,22.6368671 L20.535586,22.6458746 C20.3440774,22.6631878 20.1372193,22.6358126 19.9186355,22.5681148 C18.0352929,22.6355246 16.2413319,22.8123552 14.5210203,23.0666097 C16.1975737,25.2344246 17.7336443,27.6105405 18.385658,29.4017134 L18.5426211,29.8715683 C18.7832695,30.4880171 18.943747,31.0601453 19.0006976,31.566129 L19.087846,32.4368695 L19.1383572,33.1866881 C19.2339525,35.2644861 18.8216809,35.8411243 17.782168,36.0981692 L17.4122688,36.1736348 L16.9949247,36.2351517 L16.3983997,36.296221 C14.8369872,36.40811 13.5491741,36.0746738 13.4717583,34.3928978 L13.4709319,34.0797925 C13.4949542,33.3929248 13.3779301,32.2680803 13.1237891,30.9544585 L13.0139379,31.0238306 C12.9791411,30.5502006 12.8797362,29.8784842 12.6974512,29.088243 C12.3243221,27.6694236 11.8198282,26.1790633 11.1870453,24.8543654 C11.007524,24.5016285 10.8147866,24.1466946 10.6060902,23.7963656 L9.56356413,24.0362058 C7.97728117,23.3071466 6.20208887,21.0895337 5.04224101,18.1677631 C3.88656028,15.2459924 3.66292493,12.4210607 4.31855149,10.8107666 C7.36032504,9.09796728 10.538555,6.95624599 13.651152,4.17012433 C13.6936246,4.1172982 13.7377802,4.07473582 13.7834657,4.03895441 L15.0338231,2.77855036 C15.4051726,2.40785473 15.8057398,2.01705258 16.2021067,1.65472021 L16.6367028,1.26427259 L16.6768347,1.22232325 C16.9902574,0.887604202 17.359373,0.610653327 17.767047,0.403213488 L18.0811535,0.260850411 C20.12025,-0.539808797 22.6409742,0.550083756 24.8510774,2.88032011 Z M2.76004933,11.6629497 C2.37667447,13.6038798 2.65448235,16.1493618 3.67125917,18.7114448 C4.689425,21.2762946 6.23125869,23.3237475 7.84254437,24.4816652 L6.79243061,24.7694153 C4.86166591,25.5275263 2.18081992,23.3195973 0.802892899,19.8375439 C-0.577812212,16.3554905 -0.133319644,12.9190898 1.794667,12.1609788 L2.76004933,11.6629497 Z M18.6478816,1.69268405 C16.7254511,2.44664477 16.3712461,8.38840847 18.1992218,13.1985394 C20.1077619,18.2161825 24.3359977,21.721754 26.2806529,20.956726 C28.9226057,19.9191654 29.3532079,14.7631809 27.2460352,9.44672029 C25.1374734,4.1274929 21.2870563,0.655123441 18.6478816,1.69268405 Z M19.8021733,9.08149895 C21.5203694,9.08334172 22.9662797,10.3634358 23.1695008,12.0626632 C23.3727219,13.7618905 22.2691711,15.3444259 20.5994818,15.7481717 C19.925811,14.8288821 19.3787623,13.8238949 18.9729168,12.7599972 C18.5800879,11.7084727 18.3106767,10.6152304 18.170052,9.50205688 C18.6548267,9.23090772 19.2118315,9.08149895 19.8021733,9.08149895 Z" id="path-7"></path>
        <filter x="-1.7%" y="-1.4%" width="103.3%" height="105.5%" filterUnits="objectBoundingBox" id="filter-8">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.2 0" type="matrix" in="shadowOffsetOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="云广播绑定" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="资源管理/1.8云广播绑定_绑定" transform="translate(-1109.000000, -436.000000)">
            <g id="弹框" transform="translate(460.000000, 140.000000)">
                <g id="位图" transform="translate(334.000000, 159.000000)">
                    <g id="icon_广播_60_s" transform="translate(322.000000, 137.000000)">
                        <rect id="矩形" x="0" y="0" width="60" height="60"></rect>
                        <path d="M59.5031405,63.1430484 C59.5031405,70.7959102 46.1844335,77 29.7515702,77 C13.3187069,77 0,70.7959102 0,63.1430484 C0,55.4901866 13.3187069,49.2860968 29.7515702,49.2860968 C46.1844335,49.2860968 59.5031405,55.4901866 59.5031405,63.1430484 C59.5031405,63.1430484 59.5031405,63.1430484 59.5031405,63.1430484 Z" id="椭圆_7_拷贝" stroke="url(#linearGradient-2)" stroke-width="2" fill="url(#linearGradient-1)" opacity="0.5" filter="url(#filter-3)"></path>
                        <path d="M29.7515702,49 C40.6125511,49 49.4171423,53.1007685 49.4171423,58.1593075 C49.4171423,63.2178465 40.6125511,67.3186151 29.7515702,67.3186151 C18.8905894,67.3186151 10.0859982,63.2178465 10.0859982,58.1593075 C10.0859982,53.1007685 18.8905894,49 29.7515702,49 C29.7515702,49 29.7515702,49 29.7515702,49 Z" id="椭圆_7" stroke="url(#linearGradient-5)" stroke-width="2" fill="url(#linearGradient-4)"></path>
                        <g id="icon_广播_42_n">
                            <rect id="矩形" x="0" y="0" width="60" height="60"></rect>
                            <g id="图标" transform="translate(3.750000, 0.000000)">
                                <ellipse id="椭圆形" fill-opacity="0.25" fill="#9B9B9B" cx="26.25" cy="56.25" rx="9.375" ry="3.75"></ellipse>
                                <path d="M26.25,0.5 C33.3606662,0.5 39.7981662,3.38216692 44.4579996,8.04200038 C49.1178331,12.7018338 52,19.1393338 52,26.25 C52,32.656812 49.6602327,38.5171666 45.7884426,43.0230633 C41.8157977,47.6463324 36.2304986,50.844028 29.9049015,51.7426685 L29.9049015,51.7426685 L26.7112676,56.5317304 C26.5102116,56.6825612 26.3293659,56.7670823 26.1659963,56.776646 L26.1659963,56.776646 L22.5947527,51.7426193 C16.2692979,50.8439115 10.6841299,47.6462481 6.7115574,43.0230633 C2.83976731,38.5171666 0.5,32.656812 0.5,26.25 C0.5,19.1393338 3.38216692,12.7018338 8.04200038,8.04200038 C12.7018338,3.38216692 19.1393338,0.5 26.25,0.5 Z" id="形状结合" stroke="url(#linearGradient-6)" fill="#32C5FF"></path>
                                <g id="形状" transform="translate(10.535714, 7.142857)">
                                    <use fill="black" fill-opacity="1" filter="url(#filter-8)" xlink:href="#path-7"></use>
                                    <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-7"></use>
                                </g>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>