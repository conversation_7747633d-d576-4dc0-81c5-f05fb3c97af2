* {
  margin: 0;
  padding: 0;
}

html,
body,
#app,
.wrapper {
  width: 100%;
  height: 100%;
  background: #fefefe;
  display: flex;
  flex-direction: row;
}

.main-left {
  width: 280px;
  background: #20222a;
  position: relative;
}

.main-left-collapse {
  width: 64px !important;
  position: relative;
}

.main-right {
  flex: 1;
  padding-left: 27px;
  position: relative;
  display: flex;
  flex-direction: column;
  background-color: #fff;
}

body {
  font-family: "PingFang SC", "Helvetica Neue", Helvetica, "microsoft yahei",
    arial, STHeiTi, sans-serif;
}

a {
  text-decoration: none;
}
/* 这里影响了公共图层无人机详情面板 */
/* .content-box {
  position: absolute;
  left: 0;
  right: 0;
  top: 98px;
  bottom: 0;
  padding-bottom: 30px;
  -webkit-transition: left 0.3s ease-in-out;
  transition: left 0.3s ease-in-out;
} */

.content {
  width: auto;
  height: 100%;
  padding: 10px;
  overflow-y: scroll;
  box-sizing: border-box;
}

/* .content-collapse {
} */

.container {
  padding: 30px;
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 5px;
}

.crumbs {
  margin: 10px 0;
}

.el-table thead {
  color: #606266;
}

.el-table th {
  background-color: #f5f7fa;
}

.pagination {
  margin: 20px 0;
  text-align: right;
}

.plugins-tips {
  padding: 20px 10px;
  margin-bottom: 20px;
}

.el-button + .el-tooltip {
  margin-left: 10px;
}

.el-table tr:hover {
  background: #f6faff;
}

.mgb20 {
  margin-bottom: 20px;
}

.move-enter-active,
.move-leave-active {
  transition: opacity 0.5s;
}

.move-enter,
.move-leave {
  opacity: 0;
}

/*BaseForm*/

.form-box {
  width: 600px;
}

.form-box .line {
  text-align: center;
}

.el-time-panel__content::after,
.el-time-panel__content::before {
  margin-top: -7px;
}

.el-time-spinner__wrapper
  .el-scrollbar__wrap:not(.el-scrollbar__wrap--hidden-default) {
  padding-bottom: 0;
}

/*Upload*/

.pure-button {
  width: 150px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  color: #fff;
  border-radius: 3px;
}

.g-core-image-corp-container .info-aside {
  height: 45px;
}

.el-upload--text {
  background-color: #fff;
  /* border: 1px dashed #d9d9d9; */
  border-radius: 6px;
  box-sizing: border-box;
  width: 360px;
  height: 180px;
  text-align: center;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.el-upload--text .el-icon-upload {
  font-size: 67px;
  color: #97a8be;
  margin: 40px 0 16px;
  line-height: 50px;
}

.el-upload--text {
  color: #97a8be;
  font-size: 14px;
  text-align: center;
}

.el-upload--text em {
  font-style: normal;
}

/*VueEditor*/

.ql-container {
  min-height: 400px;
}

.ql-snow .ql-tooltip {
  transform: translateX(117.5px) translateY(10px) !important;
}

.editor-btn {
  margin-top: 20px;
}

/*markdown*/

.v-note-wrapper .v-note-panel {
  min-height: 500px;
}

.el-menu-item {
  height: 64px;
  line-height: 64px;
  font-size: 14px;
  color: #303133;
  padding: 0 20px;
  list-style: none;
  cursor: pointer;
  position: relative;
  transition: border-color 0.3s, background-color 0.3s, color 0.3s;
  box-sizing: border-box;
  white-space: nowrap;
}

.el-menu-item.is-active {
  color: #ff6a6c;
  background: rgba(255, 255, 255, 0.1) !important;
  border-left: 4px solid #ff6a6c;
}

.el-menu-item.is-active svg {
  color: #ff6a6c;
  fill: transparent;
}

.el-menu-item svg {
  color: #fff;
  fill: transparent;
}

.el-menu {
  border-right: solid 1px transparent;
  list-style: none;
  position: relative;
  margin: 0;
  padding-left: 0;
  background-color: #fff;
}

@font-face {
  font-family: "Alibaba-PuHuiTi-H";
  src: url("../fonts/Alibaba-PuHuiTi-Heavy.ttf");
}

@font-face {
  font-family: "DINAlternate-Bold";
  src: url("../fonts/DIN Alternate Bold.ttf");
}

.msgbox-add-wrap {
  width: 100%;
}

.msgbox-add-wrap .msgbox-add-item {
  display: flex;
  margin-top: 8px;
}

.msgbox-add-wrap .msgbox-add-item > span {
  padding-top: 10px;
}

.msgbox-add-wrap .msgbox-add-item.item-required > span::before {
  content: "*";
  color: #f00;
}

.msgbox-add-wrap .msgbox-add-item input,
.msgbox-add-wrap .msgbox-add-item textarea {
  flex: 1;
  margin-left: 8px;
}

.el-button.el-button--text {
  border-color: transparent !important;
}

.el-button.el-button--primary {
  /* border-color: #ff6a6c !important;
  background-color: #ff6a6c !important; */
  color: #fff !important;
}

.el-button.el-button--primary:focus,
.el-button.el-button--primary:hover {
  color: #fff !important;
  /* border-color: #ff6a6db1 !important;
  background: #ff6a6db1 !important; */
}

/* .el-button.el-button--default:focus,
.el-button.el-button--default:hover {
  color: #f56c6c !important;
  border-color: #fbc4c4 !important;
} */

.el-button.el-button--text:focus,
.el-button.el-button--text:hover {
  color: #6eb0f6;
}

.el-button.el-button--primary:focus,
.el-button.el-button--primary:hover {
  /* border-color: #ff6a6c; */
  color: #fff;
}

/* 设置滚动条的样式 */
::-webkit-scrollbar {
  width: 4px;
  height: 8px;
}

/*滚动条样式*/

::-webkit-scrollbar-track {
  border-radius: 5px;
}

/*滚动条样式*/

::-webkit-scrollbar-thumb {
  border-radius: 5px;
  background: #d8d8d8;
  box-shadow: inset 0 0 6px #0000002e;
  -webkit-box-shadow: inset 0 0 6px #0000002e;
}

::-webkit-scrollbar-thumb:window-inactive {
  background: #d8d8d8;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #d8d8d8;
  border-radius: 5px;
  -webkit-border-radius: 5px;
}

::-webkit-scrollbar-corner {
  background-color: #d8d8d8;
}

.el-radio__input.is-checked .el-radio__inner {
  border-color: #ff6a6c !important;
  background: #ff6a6c !important;
}

.el-radio__input.is-checked + .el-radio__label {
  color: #ff6a6c !important;
}

/* 全局dialog样式 */
.el-dialog .el-dialog__header {
  border-bottom: 1px solid rgb(0, 0, 0, 0.06);
}

.el-dialog .el-dialog__header .el-dialog__title {
  font-size: 16px;
  color: #000000;
  font-weight: bold;
}

/* 全局popconfirm样式*/
.el-popconfirm .el-popconfirm__main {
  margin-bottom: 10px;
}

.el-popconfirm .el-popconfirm__main .el-popconfirm__icon {
  font-size: 18px;
}

.el-popconfirm .el-popconfirm__action {
  display: flex;
  flex-direction: row-reverse;
}

.el-popconfirm .el-popconfirm__action .el-button {
  border-color: rgba(0, 0, 0, 0.15) !important;
  min-width: 60px;
}

.el-popconfirm .el-popconfirm__action .el-button:first-child {
  margin-left: 10px;
}

.el-popconfirm .el-popconfirm__action .el-button:last-child {
  margin-left: 0;
}

/* 全局pagination样式 */
.el-pagination .el-pager li {
  color: #606266;
  font-weight: 400;
  line-height: 30px;
}

.el-pagination .el-pager li.active {
  background: #ff6a6c;
  border-radius: 1px;
  color: #fff;
}

/* 全局tab样式 */
.el-tabs .el-tabs__item {
  color: rgba(48, 49, 51, 0.65);
}

.el-tabs .el-tabs__item.is-active,
.el-tabs .el-tabs__item:hover {
  color: #ff6a6c;
}

/* .info-container-close {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
} */

.screen .el-picker-panel {
  border-image: linear-gradient(
      90deg,
      #3a9398 0%,
      #00bbf273 30%,
      #00bbf273 60%,
      #3a9398 99%
    )
    2 2 2 2;
  background-color: #000000d4;
  color: #fefefe;
}

.screen .el-picker-panel .el-picker-panel__icon-btn {
  color: #fff;
  border: 0;
  background: 0 0;
  cursor: pointer;
  outline: 0;
}

.screen .el-picker-panel__icon-btn.is-disabled {
  display: none;
}

.screen .el-picker-panel .el-date-range-picker__header {
  background: transparent;
  border-bottom: 1px solid;
  border-image: linear-gradient(
      90deg,
      #3a9398 0%,
      #00bbf273 30%,
      #00bbf273 60%,
      #3a9398 99%
    )
    2 2 2 2;
  padding: 0 10px;
}

.screen .el-picker-panel .el-date-range-picker__content {
  padding: 0;
}

.screen .el-picker-panel .el-date-table th {
  color: #fff;
  border-bottom: 0;
}

.screen .el-picker-panel .el-date-table td.prev-month {
  color: #6e6e6e;
}

.screen .el-picker-panel .el-date-range-picker__content.is-left {
  border-right: 1px solid #1384b4;
  padding: 0;
}

.screen
  .el-picker-panel
  .el-date-table.is-week-mode
  .el-date-table__row:hover
  div,
.el-picker-panel .el-date-table td.in-range div,
.el-picker-panel .el-date-table td.in-range div:hover {
  background-color: #00bbf273;
  color: #f9f5c0;
}

.screen .el-picker-panel .el-date-table td.start-date span {
  color: #f9f5c0;
  border: 1px solid #fef551;
  background-color: #203a4e;
}

.screen .el-picker-panel .el-date-table td.end-date span {
  color: #f9f5c0;
  border: 1px solid #fef551;
  background-color: #203a4e;
}

.screen .el-cascader-menu {
  color: #b3b3b3;
}

.screen .el-cascader__dropdown {
  background-color: #000000d4;
  border-bottom: 1px solid;
  border-image: linear-gradient(
      90deg,
      #3a9398 0%,
      #00bbf273 30%,
      #00bbf273 60%,
      #3a9398 99%
    )
    2 2 2 2;
}

.screen .el-popper .popper__arrow,
.el-popper .popper__arrow::after {
  border-bottom-color: #00bbf273;
}

.screen .el-popper[x-placement^="bottom"] .popper__arrow::after {
  border-bottom-color: #00bbf273;
}

.screen .el-cascader-node:not(.is-disabled):focus,
.el-cascader-node:not(.is-disabled):hover {
  background-color: #00bbf273;
  color: #fefefe;
}

.screen .el-autocomplete-suggestion {
  margin: 5px 0;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
  border-radius: 4px;
  border-image: linear-gradient(
      90deg,
      #3a9398 0%,
      #00bbf273 30%,
      #00bbf273 60%,
      #3a9398 99%
    )
    2 2 2 2;
  box-sizing: border-box;
  background-color: #000000d4;
}

.screen .el-autocomplete-suggestion li {
  color: #b3b3b3;
}

.screen .el-autocomplete-suggestion li.highlighted,
.el-autocomplete-suggestion li:hover {
  background-color: #00bbf273;
  color: #fefefe;
}

.screen .el-autocomplete-suggestion .popper__arrow {
  border-bottom-color: #00bbf273;
}

.screen .el-autocomplete-suggestion .popper__arrow::after {
  border-bottom-color: #00bbf273;
}
.screen_animal_locus .el-input__inner {
  background-color: #273547;
  border: 1px solid #273547;
  color: #ffffff;
}
.screen_animal_locus .el-input.is-disabled .el-input__inner {
  background-color: #273547;
  border: 1px solid #273547;
  color: #ffffff;
}
.screen_animal_locus .el-picker-panel__footer {
  background-color: #273547;
  border-top: 1px solid #999999;
}
.screen_animal_locus .el-date-range-picker__time-header {
  border-bottom: 1px solid #999999;
}

.screen_animal_locus .el-button--default {
  background: #409eff;
  border: 1px solid #409eff;
  color: #fff;
}
.screen_animal_locus .el-button--default:hover {
  background: #4691ff !important;
  border: 1px solid #409eff;
  color: #fff;
}
.screen_animal_locus .el-button.is-disabled.is-plain {
  background: #409eff;
  border: 1px solid #409eff;
  color: #c0c4c0;
}
.screen_animal_locus .el-button.is-disabled.is-plain:hover {
  background: #409eff !important;
  border: 1px solid #409eff;
  color: #c0c4c0 !important;
}

.screen_animal_locus .el-button.el-button--text {
  display: none;
}

/* 搜索分割线 */
.search-devider {
  width: 100%;
  height: 1px;
  background: #f0f0f0;
  margin: 8px 0;
}

.search-table.el-table .el-table__cell {
  font-size: 14px;
}

.search-table.el-table th.el-table__cell {
  font-weight: 600;
  color: #606266;
}

.search-table.el-table .el-button--small {
  font-size: 14px;
}

.search-table.el-table .el-button--small {
  padding-top: 6px;
  padding-bottom: 6px;
}

.search-table.el-table .el-checkbox__input.is-checked .el-checkbox__inner,
.search-table.el-table
  .el-checkbox__input.is-indeterminate
  .el-checkbox__inner {
  background-color: #ff6a6c;
  border-color: #ff6a6c;
}

.search-table.el-table .el-checkbox__input.is-focus .el-checkbox__inner {
  border-color: #ff6a6c;
}

.ctmap-ol-measure-tooltip {
  --font-size: 13px;
  /* --tooltip-bg:hsla(0,0%,9%,.8); */
  --tooltip-bg: #fff;
  background: var(--tooltip-bg);
  border-radius: 4px;
  color: #000;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  font-size: var(--font-size);
  padding: 0 3px;
  transform: translate(0%, 200%);
}

.ctmap-ol-measure-tooltip .__text {
  padding: 3px 6px 4px 8px;
}

.ctmap-ol-measure-tooltip .__actions {
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.ctmap-ol-measure-tooltip svg {
  fill: currentColor;
  -webkit-box-sizing: content-box;
  box-sizing: content-box;
  cursor: pointer;
  height: var(--font-size);
  margin: 0 3px;
  padding: 5px 3px;
  width: var(--font-size);
}

.ctmap-ol-measure-tooltip.--static:before {
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-bottom: 6px solid var(--tooltip-bg);
  top: -5px;
  content: "";
  left: 50%;
  margin-left: -6px;
  position: absolute;
}

/* 浮现提示 */
.c-tip {
  display: none;
  font-style: normal;
  font-weight: normal;
  border-radius: 4px;
  background: #0d1a26;
  color: #fff;
  z-index: 99999;
  padding: 4px 8px;
  pointer-events: none;
  position: fixed;
  top: 0;
  left: 0;
  max-width: 400px;
  font-size: 14px;
  word-break: break-all;
  line-height: normal;
}

.el-tooltip__popper {
  max-width: 450px;
  /* max-height: 99px; */
  /* background: #ffffff !important; */
  /* color: #606266 !important; */
  box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.2);
}

.tooltip-dark.is-dark {
  /* max-height: 99px; */
  background: #303133 !important;
  color: #fff !important;
}

.el-tooltip__popper[x-placement^="top"] .popper__arrow,
.el-tooltip__popper[x-placement^="top"] .popper__arrow::after {
  border-top-color: #fff;
}
.el-select-dropdown__item.selected.hover,
.el-select-dropdown__item.selected {
  color: #ff6a6c !important;
}

.el-table-filter__list-item:hover,
.el-table-filter__list-item.is-active {
  background-color: rgba(255, 106, 108, 0.1);
  color: #ff6a6c;
}

.el-table .ascending .sort-caret.ascending {
  border-bottom-color: #ff6a6c;
}
.el-table .descending .sort-caret.descending {
  border-top-color: #ff6a6c;
}
.el-table th.el-table__cell > .cell.highlight {
  color: unset;
}

.my-confirm-box-btn-order .el-message-box__btns {
  display: flex;
  justify-content: end;
  flex-direction: row-reverse;
}

.my-confirm-box-btn-order .el-message-box__btns button:nth-child(2) {
  margin-right: 10px;
  border-color: #ff6a6c;
  background-color: #ff6a6c !important;
  color: #fff !important;
}

.my-confirm-box-btn-order .el-message-box__btns button:hover {
  border-color: #fbc4c4;
  background-color: #fef0f0 !important;
  color: #ff6a6c !important;
}

.event_file_cascader {
  background: #172537 !important;
  border: none !important;
}
.event_file_cascader .el-cascader-menu {
  color: #ffffff;
}

.event_file_cascader .el-cascader-node:not(.is-disabled):focus,
.event_file_cascader .el-cascader-node:not(.is-disabled):hover {
  color: #4274ff !important;
  background-color: rgba(45, 86, 135, 0.83) !important;
}
.event_file_cascader .el-cascader-menu {
  border-right: solid 1px #35455a;
}
.event_file_cascader .el-radio__input.is-checked .el-radio__inner {
  border-color: #409eff !important;
  background: #409eff !important;
}
.event_file_cascader .el-checkbox__input.is-disabled .el-checkbox__inner {
  background-color: #999999;
}

.material_cascader .el-cascader-node:hover,
.material_cascader .el-cascader-node.in-active-path,
.material_cascader .el-cascader-node.is-active,
.material_cascader .el-cascader-node.is-selectable.in-checked-path {
  color: #ff6a6c !important;
}

.material_cascader .el-cascader-node:not(.is-disabled):focus,
.el-cascader-node:not(.is-disabled):hover {
  background: rgba(255, 106, 108, 0.1) !important;
}

.workbench-date-picker .el-date-table td.end-date span,
.workbench-date-picker .el-date-table td.start-date span {
  background-color: #ff6a6c;
  color: #ffffff !important;
}

.workbench-date-picker .el-date-table td.in-range div,
.workbench-date-picker.el-date-table.is-week-mode
  .el-date-table__row.current
  div,
.workbench-date-picker.el-date-table.is-week-mode
  .el-date-table__row:hover
  div {
  background-color: #f2f6fc;
  color: #666666;
}
.workbench-date-picker .el-date-table td.in-range div:hover {
  background-color: #f2f6fc;
  color: #ff6a6c;
}

.workbench-date-picker .el-date-table td.today span {
  color: #ff6a6c;
}

.el-date-editor .el-range__icon {
  color: #909399;
}

.camera_cascader .el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: #eb4b4b;
  border-color: #eb4b4b;
}

.camera_cascader .el-cascader-node:not(.is-disabled):focus,
.camera_cascader .el-cascader-node:not(.is-disabled):hover {
  background: rgba(255, 106, 108, 0.1) !important;
  color: #666666 !important;
}
