[class*=" el-icon-lx"],
[class^="el-icon-lx"] {
    font-family: lx-iconfont !important;
}


.el-icon-batch-query {
    mask: url('../images/home/<USER>') no-repeat center / cover;
    position: relative;
    top: -1px;
    transform: scale(1.5);
    background-color: #606266;
}

.el-button:hover .el-icon-batch-query {
    background-color: #fff;
}

.el-icon-batch-query::before {
    content: '提';
    font-size: 12px;
    visibility: hidden;
}

.icon_wurenjijichang_18{
  mask: url('../images/alarmEvent/alarm/icon_wurenjijichang_18.svg') no-repeat center / cover;
  /* transform: scale(0.6); */
  background-color: #ffffff;
}

.icon_wurenjijichang_18_s{
  mask: url('../images/alarmEvent/alarm/icon_wurenjijichang_18_s.svg') no-repeat center / cover;
  /* transform: scale(0.6); */
  background-color: #ffffff;
}

.guide-flight-footer {
  margin-top: .12rem;
    margin-bottom: .12rem;
    text-align: center;
    display: flex;
    justify-content: center;
}

.guide-flight-table .el-table .el-table__body-wrapper {
  overflow-y: auto !important;
}

.guide-flight-table .el-table .el-table__body-wrapper {
  border-right: none !important;
}