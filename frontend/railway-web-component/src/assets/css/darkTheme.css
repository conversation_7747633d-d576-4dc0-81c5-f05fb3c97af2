@charset "UTF-8";

.el-select-dropdown {
    border: solid 1px #388de0;
    background-color: #1b3651;
}

.el-select-dropdown.is-multiple .el-select-dropdown__item.selected {
    color: #1b95f8;
    background-color: #1b3651
}

.el-select-dropdown.is-multiple .el-select-dropdown__item.selected.hover {
    background-color: #01305f
}


.el-select-dropdown__empty {
    color: #999;
}


.el-select-dropdown__item {
    color: #f9f9f9;
}

.el-select-dropdown__item.is-disabled {
    color: #e4e2e2;
}

.el-select-dropdown__item.is-disabled:hover {
    background-color: #1b3651
}

.el-select-dropdown__item.hover,
.el-select-dropdown__item:hover {
    background-color: #01305f
}

.el-select-dropdown__item.selected {
    color: #1b95f8;
}

.el-time-spinner__wrapper.is-arrow .el-time-spinner__item:hover:not(.disabled):not(.active) {
    background: #1b3651;
    cursor: default
}

.el-time-panel {
    border: solid 1px #388de0;
    background-color: #1b3651;
    -webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
}


.el-time-spinner__item {
    color: #f9f9f9
}

.el-time-spinner__item:hover:not(.disabled):not(.active) {
    background: #01305f;
}

.el-time-spinner__item.active:not(.disabled) {
    color: #fff;
}

.el-time-panel__content::after,
.el-time-panel__content::before {
    content: "";
    top: 50%;
    position: absolute;
    margin-top: -15px;
    height: 32px;
    z-index: -1;
    left: 0;
    right: 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    padding-top: 6px;
    text-align: left;
    border-top: 1px solid #388de0;
    border-bottom: 1px solid #388de0
}


.el-time-panel__btn {
    background-color: transparent;
    color: #fff
}

.el-time-panel__btn.confirm {
    color: #1b95f8
}
