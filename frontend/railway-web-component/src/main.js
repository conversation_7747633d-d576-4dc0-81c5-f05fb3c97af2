/*
 * @Author: 米亚流年 <EMAIL>
 * @Date: 2024-01-12 13:10:39
 * @LastEditors: liu.yongli
 * @LastEditTime: 2025-04-23 10:39:42
 * @FilePath: /remote-component-template/src/main.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import Vue from 'vue';
import App from './App.vue';
import CTMapOl from '@ct/ct_map_ol';
import classNames from 'classnames';
import { setup } from "@ct/component-gallery-theme-chalk/js/index.js";
import '@ct/component-gallery-theme-chalk/css/index.css';
import router from './router';
import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';
import '@/assets/styles/common.scss';
import '@/assets/font/iconfont.css';
import { pxToRemMixin } from './common/pxToRem';
// 不用了
// import EasyPlayer from '#/EasyPlayer-component.min.js'
import ChinaTowerUI from '@ct/china-tower-tech-ui' // 挂载组件
import CtIcons from '@ct/icons-v2'
import iframeSDK, { mapSDK, postMsgUtil, requestSDK } from '@ct/iframe-connect-sdk'
import BaseVideoListener from '@ct/component-gallery-video-player-listener'
import '@ct/component-gallery-video-player-listener/common-base-component-video-player-listener.css'
import '@ct/ct_map_ol/index.css'
import VideoPlayer from 'vue-video-player'
// import './assets/css/theme-green/index.css'; // 浅绿色主题
import "./assets/css/icon.css";
import "./assets/icons";
import "./components/common/directives";
import * as echarts from "echarts";
import _ from "lodash";
import fullscreen from 'vue-fullscreen'
import './utils/drag-v2'
import importDirective from '@/components/common/resize/directive';
import './utils/drag'
import Message from '@/components/common/message/message.js'
import './components/common/c-tip.js';
import "./assets/iconfont/iconfont.css"; //字体图标库, 请放到最下面, 防止影响原页面样式, 如果可以请不要引入新的字体图标库
// 需要引入的
import store from "./store/index";
// import hls from 'videojs-contrib-hls'; // 后再查看是否需要
import { cacheAuthorization } from './api/service/common';

const bus = new Vue();
Vue.prototype.$EventBus = bus;

// 暴露全局引用。组件库里把Vue和地图工具类独立了出去，对应的集成工程需要将这两个引用暴露到window，不然集成组件无法正常工作。
window.Vue = Vue;
window['@ct/ct_map_ol'] = CTMapOl;
window.requestSDK = requestSDK;
window.iframeSDK = iframeSDK;

Vue.config.productionTip = false;
Vue.prototype.$classNames = classNames;
Vue.prototype.isCross = true;
Vue.prototype.$mapSDK = mapSDK;
Vue.prototype.$env = process.env;
Vue.prototype.$postMsgUtil = postMsgUtil;
Vue.prototype.$echarts = echarts;
Vue.prototype._ = _;
importDirective(Vue); // 引入resize指令
Vue.prototype.$NoticeMessage = Message
Vue.prototype.$requestSDK = requestSDK;
Vue.prototype.$store = store

Vue.use(fullscreen)
require('video.js/dist/video-js.css')
require('vue-video-player/src/custom-theme.css')
Vue.use(ElementUI, {
  size: "small",
});
Vue.use(ChinaTowerUI)
Vue.use(CtIcons)
Vue.use(BaseVideoListener)
Vue.mixin(pxToRemMixin);
localStorage.setItem('isCross', true);
// console.log(EasyPlayer)
// Vue.component('EasyPlayer', EasyPlayer)
Vue.use(VideoPlayer)

//地图包服务基础路径
const newUrl = window.location.origin + '/api'
localStorage.setItem('gisUrl', newUrl)

const vue = new Vue({
  router,
  store,
  beforeCreate() {
    Vue.prototype.$globalEventBus = this;
    // console.log('performance Vue beforeCreate', new Date().getTime())
    cacheAuthorization(process.env.VUE_APP_REQ_SDK, process.env.VUE_APP_REQ_PREFIX_BIZ);
  },
  render: h => h(App),
  mounted() {
    setup() //  增加动态colors能力，文档路径 Component-Gallery/docs/demo/vueDocs/change-colors.vue_doc.md
  }
}).$mount('#app');
// window._mainVue = vue

//获取水印文字
requestSDK('getUser').then(resp => {
  window.arWatermarkText = '中国铁塔'
  if (resp.userInfo.tenantId && !resp.towerAccount) {
    window.arWatermarkText = resp.userInfo.tenantInfo.tenantName
  }
});

export default vue;